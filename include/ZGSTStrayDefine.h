#ifndef ZGSTSTRAYDEFINE_H
#define ZGSTSTRAYDEFINE_H

constexpr const char* ST_YC_GDDY = "Ure"; // 轨地电压
constexpr const char* ST_YC_GJDY = "Ugj"; // 轨结电压
constexpr const char* ST_YC_JDDY = "Ujd"; // 结地电压
constexpr const char* ST_YC_JHDW = "Ua"; // 极化电位
constexpr const char* ST_YC_ZXADY = "Uzxa"; // 纵向A电压
constexpr const char* ST_YC_ZXBDY = "Uzxb"; // 纵向B电压
constexpr const char* ST_YC_CWJS = "Pos_CNT"; // 车位计数
constexpr const char* ST_YC_BTDW = "Uc1"; // 传感器回传本体电位
constexpr const char* ST_YC_ZXPY_SUM_M1 = "Ud_P_SUM"; // 正向极化偏移1分钟累加值
constexpr const char* ST_YC_ZXPY_COUNT_M1 = "Ud_P_CNT"; // 正向极化偏移1分钟累加计数
constexpr const char* ST_YC_ZXPY_MAX_M1 = "Ud_P_MAX"; // 正向极化偏移1分钟最大值
constexpr const char* ST_YC_FXPY_SUM_M1 = "Ud_N_SUM"; // 负向极化偏移1分钟累加值
constexpr const char* ST_YC_FXPY_COUNT_M1 = "Ud_N_CNT"; // 负向极化偏移1分钟累加计数
constexpr const char* ST_YC_FXPY_MAX_M1 = "Ud_N_MAX"; // 负向极化偏移1分钟最大值
constexpr const char* ST_YC_JHPY_COUNT_M1 = "Ud_CNT"; // 极化电位偏移1分钟统计计数
constexpr const char* ST_YC_ZXGD_SUM_M1 = "Ure_P_SUM"; // 正向轨地1分钟累加值
constexpr const char* ST_YC_ZXGD_COUNT_M1 = "Ure_P_CNT"; // 正向轨地1分钟累加计数
constexpr const char* ST_YC_ZXGD_MAX_M1 = "Ure_P_MAX"; // 正向轨地1分钟最大值
constexpr const char* ST_YC_FXGD_SUM_M1 = "Ure_N_SUM"; // 负向轨地1分钟累加值
constexpr const char* ST_YC_FXGD_COUNT_M1 = "Ure_N_CNT"; // 负向轨地1分钟累加计数
constexpr const char* ST_YC_FXGD_MAX_M1 = "Ure_N_MAX"; // 负向轨地1分钟最大值
constexpr const char* ST_YC_GDDY_COUNT_M1 = "Ure_CNT"; // 轨地电压1分钟统计计数

constexpr const char* ST_YX_ADGZ = "AD_Fault"; // AD故障
constexpr const char* ST_YX_CSBGZ = "CSB_Fault"; // 超声波故障
constexpr const char* ST_YX_CWXX = "TrainPos"; // 车位信息

constexpr const char* ST_RAIL_RESA = "Rra"; // 10米钢轨电阻A
constexpr const char* ST_RAIL_RESB = "Rrb"; // 10米钢轨电阻B

constexpr const char* ST_FIELD_STATE = "rtState"; // 二次设备通信状态

constexpr const char* CALC_YC_BTDW = "Uc"; // 本体电位
constexpr const char* CALC_YC_ZXADL = "Ia"; // 纵向A电流
constexpr const char* CALC_YC_ZXBDL = "Ib"; // 纵向B电流
constexpr const char* CALC_YC_JHPY = "Ud"; // 极化电位偏移值
constexpr const char* CALC_YC_ZXPY_SUM_D1 = "Ud_P_D1_SUM"; // 正向极化偏移日累加值
constexpr const char* CALC_YC_ZXPY_COUNT_D1 = "Ud_P_D1_CNT"; // 正向极化偏移日累加计数
constexpr const char* CALC_YC_ZXPY_AVG_D1 = "Ud_P_D1_AVG"; // 正向极化偏移日平均值
constexpr const char* CALC_YC_ZXPY_MAX_D1 = "Ud_P_D1_MAX"; // 正向极化偏移日最大值
constexpr const char* CALC_YC_ZXPY_AVG_H1 = "Ud_P_H1_AVG"; // 正向极化偏移1小时平均值
constexpr const char* CALC_YC_ZXPY_MAX_H1 = "Ud_P_H1_MAX"; // 正向极化偏移1小时最大值
constexpr const char* CALC_YC_ZXPY_SUM_H1_R = "Ud_P_H1_SUM"; // 正向极化偏移1小时累加值
constexpr const char* CALC_YC_ZXPY_COUNT_H1_R = "Ud_P_H1_CNT"; // 正向极化偏移1小时累加计数
constexpr const char* CALC_YC_ZXPY_AVG_H1_R = "Ud_P_H1_AVG_R"; // 正向极化偏移1小时实时平均值
constexpr const char* CALC_YC_ZXPY_MAX_H1_R = "Ud_P_H1_MAX_R"; // 正向极化偏移1小时实时最大值
constexpr const char* CALC_YC_FXPY_SUM_D1 = "Ud_N_D1_SUM"; // 负向极化偏移日累加值
constexpr const char* CALC_YC_FXPY_COUNT_D1 = "Ud_N_D1_CNT"; // 负向极化偏移日累加计数
constexpr const char* CALC_YC_FXPY_AVG_D1 = "Ud_N_D1_AVG"; // 负向极化偏移日平均值
constexpr const char* CALC_YC_FXPY_MAX_D1 = "Ud_N_D1_MAX"; // 负向极化偏移日最大值
constexpr const char* CALC_YC_FXPY_AVG_H1 = "Ud_N_H1_AVG"; // 负向极化偏移1小时平均值
constexpr const char* CALC_YC_FXPY_MAX_H1 = "Ud_N_H1_MAX"; // 负向极化偏移1小时最大值
constexpr const char* CALC_YC_FXPY_SUM_H1_R = "Ud_N_H1_SUM"; // 负向极化偏移1小时累加值
constexpr const char* CALC_YC_FXPY_COUNT_H1_R = "Ud_N_H1_CNT"; // 负向极化偏移1小时累加计数
constexpr const char* CALC_YC_FXPY_AVG_H1_R = "Ud_N_H1_AVG_R"; // 负向极化偏移1小时实时平均值
constexpr const char* CALC_YC_FXPY_MAX_H1_R = "Ud_N_H1_MAX_R"; // 负向极化偏移1小时实时最大值
constexpr const char* CALC_YC_ZXGD_SUM_D1 = "Ure_P_D1_SUM"; // 正向轨地日累加值
constexpr const char* CALC_YC_ZXGD_COUNT_D1 = "Ure_P_D1_CNT"; // 正向轨地日累加计数
constexpr const char* CALC_YC_ZXGD_AVG_D1 = "Ure_P_D1_AVG"; // 正向轨地日平均值
constexpr const char* CALC_YC_ZXGD_MAX_D1 = "Ure_P_D1_MAX"; // 正向轨地日最大值
constexpr const char* CALC_YC_ZXGD_AVG_H1 = "Ure_P_H1_AVG"; // 正向轨地1小时平均值
constexpr const char* CALC_YC_ZXGD_MAX_H1 = "Ure_P_H1_MAX"; // 正向轨地1小时最大值
constexpr const char* CALC_YC_ZXGD_SUM_H1_R = "Ure_P_H1_SUM"; // 正向轨地1小时累加值
constexpr const char* CALC_YC_ZXGD_COUNT_H1_R = "Ure_P_H1_CNT"; // 正向轨地1小时累加计数
constexpr const char* CALC_YC_ZXGD_AVG_H1_R = "Ure_P_H1_AVG_R"; // 正向轨地1小时实时平均值
constexpr const char* CALC_YC_ZXGD_MAX_H1_R = "Ure_P_H1_MAX_R"; // 正向轨地1小时实时最大值
constexpr const char* CALC_YC_FXGD_SUM_D1 = "Ure_N_D1_SUM"; // 负向轨地日累加值
constexpr const char* CALC_YC_FXGD_COUNT_D1 = "Ure_N_D1_CNT"; // 负向轨地日累加计数
constexpr const char* CALC_YC_FXGD_AVG_D1 = "Ure_N_D1_AVG"; // 负向轨地日平均值
constexpr const char* CALC_YC_FXGD_MAX_D1 = "Ure_N_D1_MAX"; // 负向轨地日最大值
constexpr const char* CALC_YC_FXGD_AVG_H1 = "Ure_N_H1_AVG"; // 负向轨地1小时平均值
constexpr const char* CALC_YC_FXGD_MAX_H1 = "Ure_N_H1_MAX"; // 负向轨地1小时最大值
constexpr const char* CALC_YC_FXGD_SUM_H1_R = "Ure_N_H1_SUM"; // 负向轨地1小时累加值
constexpr const char* CALC_YC_FXGD_COUNT_H1_R = "Ure_N_H1_CNT"; // 负向轨地1小时累加计数
constexpr const char* CALC_YC_FXGD_AVG_H1_R = "Ure_N_H1_AVG_R"; // 负向轨地1小时实时平均值
constexpr const char* CALC_YC_FXGD_MAX_H1_R = "Ure_N_H1_MAX_R"; // 负向轨地1小时实时最大值
constexpr const char* CALC_YC_ZXGJ_SUM_D1 = "Ugj_P_D1_SUM"; // 正向轨结日累加值
constexpr const char* CALC_YC_ZXGJ_COUNT_D1 = "Ugj_P_D1_CNT"; // 正向轨结日累加计数
constexpr const char* CALC_YC_ZXGJ_AVG_D1 = "Ugj_P_D1_AVG"; // 正向轨结日平均值
constexpr const char* CALC_YC_ZXGJ_MAX_D1 = "Ugj_P_D1_MAX"; // 正向轨结日最大值
constexpr const char* CALC_YC_FXGJ_SUM_D1 = "Ugj_N_D1_SUM"; // 负向轨结日累加值
constexpr const char* CALC_YC_FXGJ_COUNT_D1 = "Ugj_N_D1_CNT"; // 负向轨结日累加计数
constexpr const char* CALC_YC_FXGJ_AVG_D1 = "Ugj_N_D1_AVG"; // 负向轨结日平均值
constexpr const char* CALC_YC_FXGJ_MAX_D1 = "Ugj_N_D1_MAX"; // 负向轨结日最大值
constexpr const char* CALC_YC_ZXJD_SUM_D1 = "Ujd_P_D1_SUM"; // 正向结地日累加值
constexpr const char* CALC_YC_ZXJD_COUNT_D1 = "Ujd_P_D1_CNT"; // 正向结地日累加计数
constexpr const char* CALC_YC_ZXJD_AVG_D1 = "Ujd_P_D1_AVG"; // 正向结地日平均值
constexpr const char* CALC_YC_ZXJD_MAX_D1 = "Ujd_P_D1_MAX"; // 正向结地日最大值
constexpr const char* CALC_YC_FXJD_SUM_D1 = "Ujd_N_D1_SUM"; // 负向结地日累加值
constexpr const char* CALC_YC_FXJD_COUNT_D1 = "Ujd_N_D1_CNT"; // 负向结地日累加计数
constexpr const char* CALC_YC_FXJD_AVG_D1 = "Ujd_N_D1_AVG"; // 负向结地日平均值
constexpr const char* CALC_YC_FXJD_MAX_D1 = "Ujd_N_D1_MAX"; // 负向结地日最大值
constexpr const char* CALC_YC_ZXJD_PEAK_H1 = "Ujd_P_H1_PEAK"; // 正向结地小时峰值

constexpr const char* CALC_YX_JHPY_P_WARN = "Ud_P_Warn"; // 正向极化偏移告警
constexpr const char* CALC_YX_JHPY_N_WARN = "Ud_N_Warn"; // 负向极化偏移告警
constexpr const char* CALC_YX_COMM_STATE = "CommState"; // 传感器通信状态
constexpr const char* CALC_YX_UC_WARN = "UcWarn"; // 本体电位无效告警

constexpr const char* CALC_PARAM_JHPY_P_WARN = "Ud_P_WarnValue"; // 正向极化电位告警阈值
constexpr const char* CALC_PARAM_JHPY_N_WARN = "Ud_N_WarnValue"; // 负向极化电位告警阈值

constexpr const char* CALC_YK_START_MEAS = "StartMeas"; // 开始测量
constexpr const char* CALC_YK_STOP_MEAS = "StopMeas"; // 停止测量

constexpr const char* PARAM_BTDW_CALC_START = "UcStatStart";
constexpr const char* PARAM_BTDW_CALC_END = "UcStatEnd";
constexpr const char* PARAM_OPER_START = "OperationStart";
constexpr const char* PARAM_OPER_END = "OperationEnd";
constexpr const char* PARAM_UA_WARN_LEVEL = "UaWarn";
constexpr const char* PARAM_UC_UP_VALUE = "UcValueUp";
constexpr const char* PARAM_UC_DOWN_VALUE = "UcValueDown";

constexpr const char* YS_BTDW_CALC_START = "CMD_UcStatStart";
constexpr const char* YS_BTDW_CALC_END = "CMD_UcStatEnd";
constexpr const char* YS_OPER_START = "CMD_OperationStart";
constexpr const char* YS_OPER_END = "CMD_OperationEnd";
constexpr const char* YS_UA_WARN_LEVEL = "CMD_UaWarn";

// 排流柜

constexpr const char* PL_YX_QF = "QF"; // QF投入
constexpr const char* PL_YC_DLZ = "I"; // 排流支路电流值
constexpr const char* PL_YK_CLOSE = "CtrlClose"; // 排流支路控制合闸
constexpr const char* PL_YK_OPEN = "CtrlOpen"; // 排流支路控制分闸


constexpr const char* CALC_YC_PL_SUM_D1 = "QF_D1_SUM";
constexpr const char* CALC_YC_PL_MAX_D1 = "QF_D1_MAX";
constexpr const char* CALC_TEXT_PL_MAXTIME_D1 = "QF_D1_MaxTime";

// 杂散监测后台

constexpr const char* CALC_YC_GDDY_OFFSET = "UreOffset"; // 轨地电压偏移值
constexpr const char* CALC_YC_ZXA_OFFSET = "IzxaOffset"; // 纵向A电流偏移值
constexpr const char* CALC_YC_ZXB_OFFSET = "IzxbOffset"; // 纵向B电流偏移值
constexpr const char* CALC_YC_GDDY_ACTUAL = "UreActual"; // 轨地电压实际值
constexpr const char* CALC_YC_ZXA_ACTUAL = "IzxaActual"; // 纵向A电流实际值
constexpr const char* CALC_YC_ZXB_ACTUAL = "IzxbActual"; // 纵向B电流实际值
constexpr const char* CALC_YC_GDDZ = "Rgd"; // 轨地电阻值
constexpr const char* CALC_YC_DISTANCE = "Distance"; // 轨地电阻值

constexpr const char* PREFIX_UP_IN = "UpIn";
constexpr const char* PREFIX_UP_OUT = "UpOut";
constexpr const char* PREFIX_DOWN_IN = "DownIn";
constexpr const char* PREFIX_DOWN_OUT = "DownOut";

constexpr const char* CALC_YC_PL_WARN_SUM_D1 = "Ud_P_Warn_D1_SUM";
constexpr const char* CALC_YC_PL_WARN_SUM_D2 = "Ud_P_Warn_D2_SUM";
constexpr const char* CALC_YC_PL_WARN_SUM_D3 = "Ud_P_Warn_D3_SUM";
constexpr const char* CALC_YC_PL_WARN_SUM_D4 = "Ud_P_Warn_D4_SUM";
constexpr const char* CALC_YC_PL_WARN_SUM_D5 = "Ud_P_Warn_D5_SUM";
constexpr const char* CALC_YC_PL_WARN_SUM_D6 = "Ud_P_Warn_D6_SUM";
constexpr const char* CALC_YC_PL_WARN_SUM_D7 = "Ud_P_Warn_D7_SUM";
constexpr const char* CALC_YX_PL_SIGNAL = "PL_Signal";

constexpr const char* PARAM_UD_P_WARN_SUM = "Ud_P_Warn_SUM";
constexpr const char* PARAM_UD_P_WARN_DAY_SUM = "Ud_P_Warn_Day_SUM";
constexpr const char* PARAM_UD_P_WARN_OFF_SUM = "Ud_P_Warn_Off_SUM";
constexpr const char* PARAM_AUTO_DRAINAGE = "AutoDrainage";




#endif // ZGSTSTRAYDEFINE_H

#ifndef ZGSPUSERMANAGERERROR_H
#define ZGSPUSERMANAGERERROR_H

namespace ZGSPUserManager
{
    const int ZG_ERR_SERVER_INIT = 2010;    //服务初始化出错
    const int ZG_ERR_DB = 2020;             //数据库服务错误
    const int ZG_ERR_RT = 2030;             //实时库服务错误
    const int ZG_ERR_INST_STATUS = 2040;    //服务实例状态错误
    const int ZG_ERR_CLIENT = 2050;         //客户端节点错误
    const int ZG_ERR_CLIENT_NOT_EXISTS = 2060; //客户端不存在
    const int ZG_ERR_USER = 2070;           //用户错误
    const int ZG_ERR_VERIFY = 2080;         //验证错误
    const int ZG_ERR_INTERNAL = 2090;       //内部错误
}

#endif // ZGSPUSERMANAGERERROR_H

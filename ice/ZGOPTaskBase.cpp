//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPTaskBase.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGOPTaskBase.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPTaskBase_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPTaskBase",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGOPTaskBase_ops[] =
{
    "abolishTask",
    "checkState",
    "confirmTask",
    "deleteTask",
    "dispatchData",
    "exitApp",
    "getTaskList",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "pauseTask",
    "resumeDebug",
    "resumeTask",
    "retryTask",
    "startDebug",
    "startTask",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGOPTaskBase_deleteTask_name = "deleteTask";
const ::std::string iceC_ZG6000_ZGOPTaskBase_getTaskList_name = "getTaskList";
const ::std::string iceC_ZG6000_ZGOPTaskBase_startTask_name = "startTask";
const ::std::string iceC_ZG6000_ZGOPTaskBase_pauseTask_name = "pauseTask";
const ::std::string iceC_ZG6000_ZGOPTaskBase_resumeTask_name = "resumeTask";
const ::std::string iceC_ZG6000_ZGOPTaskBase_retryTask_name = "retryTask";
const ::std::string iceC_ZG6000_ZGOPTaskBase_abolishTask_name = "abolishTask";
const ::std::string iceC_ZG6000_ZGOPTaskBase_confirmTask_name = "confirmTask";

}

bool
ZG6000::ZGOPTaskBase::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPTaskBase_ids, iceC_ZG6000_ZGOPTaskBase_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGOPTaskBase::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGOPTaskBase_ids[0], &iceC_ZG6000_ZGOPTaskBase_ids[3]);
}

::std::string
ZG6000::ZGOPTaskBase::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPTaskBase::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGOPTaskBase";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_deleteTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_getTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_param;
    istr->readAll(iceP_param);
    inS.endReadParams();
    ListStringMap iceP_listTask;
    ErrorInfo iceP_e;
    bool ret = this->getTaskList(::std::move(iceP_param), iceP_listTask, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listTask, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_startTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->startTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_pauseTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->pauseTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_resumeTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->resumeTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_retryTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->retryTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_abolishTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->abolishTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_confirmTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPTaskBase_ops, iceC_ZG6000_ZGOPTaskBase_ops + 23, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPTaskBase_ops)
    {
        case 0:
        {
            return _iceD_abolishTask(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_confirmTask(in, current);
        }
        case 3:
        {
            return _iceD_deleteTask(in, current);
        }
        case 4:
        {
            return _iceD_dispatchData(in, current);
        }
        case 5:
        {
            return _iceD_exitApp(in, current);
        }
        case 6:
        {
            return _iceD_getTaskList(in, current);
        }
        case 7:
        {
            return _iceD_getVersion(in, current);
        }
        case 8:
        {
            return _iceD_heartDebug(in, current);
        }
        case 9:
        {
            return _iceD_ice_id(in, current);
        }
        case 10:
        {
            return _iceD_ice_ids(in, current);
        }
        case 11:
        {
            return _iceD_ice_isA(in, current);
        }
        case 12:
        {
            return _iceD_ice_ping(in, current);
        }
        case 13:
        {
            return _iceD_isDebugging(in, current);
        }
        case 14:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 15:
        {
            return _iceD_pauseTask(in, current);
        }
        case 16:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 17:
        {
            return _iceD_resumeTask(in, current);
        }
        case 18:
        {
            return _iceD_retryTask(in, current);
        }
        case 19:
        {
            return _iceD_startDebug(in, current);
        }
        case 20:
        {
            return _iceD_startTask(in, current);
        }
        case 21:
        {
            return _iceD_stopDebug(in, current);
        }
        case 22:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskBasePrx::_iceI_deleteTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::DeleteTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_deleteTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskBase_deleteTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskBase::DeleteTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskBasePrx::_iceI_getTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::GetTaskListResult>>& outAsync, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_getTaskList_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskBase_getTaskList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskBase::GetTaskListResult v;
            istr->readAll(v.listTask, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskBasePrx::_iceI_startTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::StartTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_startTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskBase_startTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskBase::StartTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskBasePrx::_iceI_pauseTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::PauseTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_pauseTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskBase_pauseTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskBase::PauseTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskBasePrx::_iceI_resumeTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::ResumeTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_resumeTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskBase_resumeTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskBase::ResumeTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskBasePrx::_iceI_retryTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::RetryTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_retryTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskBase_retryTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskBase::RetryTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskBasePrx::_iceI_abolishTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::AbolishTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_abolishTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskBase_abolishTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskBase::AbolishTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskBasePrx::_iceI_confirmTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::ConfirmTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_confirmTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskBase_confirmTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskBase::ConfirmTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGOPTaskBasePrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGOPTaskBasePrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGOPTaskBasePrx::ice_staticId()
{
    return ZGOPTaskBase::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPTaskBase_deleteTask_name = "deleteTask";

const ::std::string iceC_ZG6000_ZGOPTaskBase_getTaskList_name = "getTaskList";

const ::std::string iceC_ZG6000_ZGOPTaskBase_startTask_name = "startTask";

const ::std::string iceC_ZG6000_ZGOPTaskBase_pauseTask_name = "pauseTask";

const ::std::string iceC_ZG6000_ZGOPTaskBase_resumeTask_name = "resumeTask";

const ::std::string iceC_ZG6000_ZGOPTaskBase_retryTask_name = "retryTask";

const ::std::string iceC_ZG6000_ZGOPTaskBase_abolishTask_name = "abolishTask";

const ::std::string iceC_ZG6000_ZGOPTaskBase_confirmTask_name = "confirmTask";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGOPTaskBase* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGOPTaskBase>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGOPTaskBase;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskBase::_iceI_begin_deleteTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_deleteTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskBase_deleteTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskBase_deleteTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskBase_deleteTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskBase::end_deleteTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_deleteTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskBase::_iceI_end_deleteTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_deleteTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskBase::_iceI_begin_getTaskList(const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_getTaskList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskBase_getTaskList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskBase_getTaskList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskBase_getTaskList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskBase::end_getTaskList(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_getTaskList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTask);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskBase::_iceI_end_getTaskList(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_getTaskList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTask);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskBase::_iceI_begin_startTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_startTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskBase_startTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskBase_startTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskBase_startTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskBase::end_startTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_startTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskBase::_iceI_end_startTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_startTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskBase::_iceI_begin_pauseTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_pauseTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskBase_pauseTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskBase_pauseTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskBase_pauseTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskBase::end_pauseTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_pauseTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskBase::_iceI_end_pauseTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_pauseTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskBase::_iceI_begin_resumeTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_resumeTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskBase_resumeTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskBase_resumeTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskBase_resumeTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskBase::end_resumeTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_resumeTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskBase::_iceI_end_resumeTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_resumeTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskBase::_iceI_begin_retryTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_retryTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskBase_retryTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskBase_retryTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskBase_retryTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskBase::end_retryTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_retryTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskBase::_iceI_end_retryTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_retryTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskBase::_iceI_begin_abolishTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_abolishTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskBase_abolishTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskBase_abolishTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskBase_abolishTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskBase::end_abolishTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_abolishTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskBase::_iceI_end_abolishTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_abolishTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskBase::_iceI_begin_confirmTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskBase_confirmTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskBase_confirmTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskBase_confirmTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskBase_confirmTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskBase::end_confirmTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_confirmTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskBase::_iceI_end_confirmTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskBase_confirmTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGOPTaskBase::_newInstance() const
{
    return new ZGOPTaskBase;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGOPTaskBase::ice_staticId()
{
    return ::ZG6000::ZGOPTaskBase::ice_staticId();
}

ZG6000::ZGOPTaskBase::~ZGOPTaskBase()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGOPTaskBase* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPTaskBase_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPTaskBase",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGOPTaskBase::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPTaskBase_ids, iceC_ZG6000_ZGOPTaskBase_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGOPTaskBase::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGOPTaskBase_ids[0], &iceC_ZG6000_ZGOPTaskBase_ids[3]);
}

const ::std::string&
ZG6000::ZGOPTaskBase::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPTaskBase::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGOPTaskBase";
    return typeId;
#else
    return iceC_ZG6000_ZGOPTaskBase_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_deleteTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_getTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_param;
    istr->read(iceP_param);
    inS.endReadParams();
    ListStringMap iceP_listTask;
    ErrorInfo iceP_e;
    bool ret = this->getTaskList(iceP_param, iceP_listTask, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listTask);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_startTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->startTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_pauseTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->pauseTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_resumeTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->resumeTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_retryTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->retryTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_abolishTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->abolishTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceD_confirmTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPTaskBase_all[] =
{
    "abolishTask",
    "checkState",
    "confirmTask",
    "deleteTask",
    "dispatchData",
    "exitApp",
    "getTaskList",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "pauseTask",
    "resumeDebug",
    "resumeTask",
    "retryTask",
    "startDebug",
    "startTask",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskBase::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPTaskBase_all, iceC_ZG6000_ZGOPTaskBase_all + 23, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPTaskBase_all)
    {
        case 0:
        {
            return _iceD_abolishTask(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_confirmTask(in, current);
        }
        case 3:
        {
            return _iceD_deleteTask(in, current);
        }
        case 4:
        {
            return _iceD_dispatchData(in, current);
        }
        case 5:
        {
            return _iceD_exitApp(in, current);
        }
        case 6:
        {
            return _iceD_getTaskList(in, current);
        }
        case 7:
        {
            return _iceD_getVersion(in, current);
        }
        case 8:
        {
            return _iceD_heartDebug(in, current);
        }
        case 9:
        {
            return _iceD_ice_id(in, current);
        }
        case 10:
        {
            return _iceD_ice_ids(in, current);
        }
        case 11:
        {
            return _iceD_ice_isA(in, current);
        }
        case 12:
        {
            return _iceD_ice_ping(in, current);
        }
        case 13:
        {
            return _iceD_isDebugging(in, current);
        }
        case 14:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 15:
        {
            return _iceD_pauseTask(in, current);
        }
        case 16:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 17:
        {
            return _iceD_resumeTask(in, current);
        }
        case 18:
        {
            return _iceD_retryTask(in, current);
        }
        case 19:
        {
            return _iceD_startDebug(in, current);
        }
        case 20:
        {
            return _iceD_startTask(in, current);
        }
        case 21:
        {
            return _iceD_stopDebug(in, current);
        }
        case 22:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGOPTaskBase::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGOPTaskBase, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGOPTaskBase::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGOPTaskBase, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGOPTaskBasePtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGOPTaskBasePtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGOPTaskBase::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

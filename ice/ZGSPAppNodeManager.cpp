//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPAppNodeManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSPAppNodeManager.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPAppNodeManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPAppNodeManager",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSPAppNodeManager_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getAncestor",
    "getChild",
    "getDescendant",
    "getListYv",
    "getParent",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGSPAppNodeManager_getChild_name = "getChild";
const ::std::string iceC_ZG6000_ZGSPAppNodeManager_getParent_name = "getParent";
const ::std::string iceC_ZG6000_ZGSPAppNodeManager_getDescendant_name = "getDescendant";
const ::std::string iceC_ZG6000_ZGSPAppNodeManager_getAncestor_name = "getAncestor";
const ::std::string iceC_ZG6000_ZGSPAppNodeManager_getListYv_name = "getListYv";

}

bool
ZG6000::ZGSPAppNodeManager::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPAppNodeManager_ids, iceC_ZG6000_ZGSPAppNodeManager_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSPAppNodeManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSPAppNodeManager_ids[0], &iceC_ZG6000_ZGSPAppNodeManager_ids[3]);
}

::std::string
ZG6000::ZGSPAppNodeManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPAppNodeManager::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSPAppNodeManager";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceD_getChild(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    istr->readAll(iceP_appNodeID);
    inS.endReadParams();
    ListStringMap iceP_listChild;
    ErrorInfo iceP_e;
    bool ret = this->getChild(::std::move(iceP_appNodeID), iceP_listChild, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listChild, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceD_getParent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    istr->readAll(iceP_appNodeID);
    inS.endReadParams();
    ListStringMap iceP_listParent;
    ErrorInfo iceP_e;
    bool ret = this->getParent(::std::move(iceP_appNodeID), iceP_listParent, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listParent, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceD_getDescendant(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    istr->readAll(iceP_appNodeID);
    inS.endReadParams();
    ListStringMap iceP_listDescendant;
    ErrorInfo iceP_e;
    bool ret = this->getDescendant(::std::move(iceP_appNodeID), iceP_listDescendant, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listDescendant, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceD_getAncestor(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    istr->readAll(iceP_appNodeID);
    inS.endReadParams();
    ListStringMap iceP_listAncestor;
    ErrorInfo iceP_e;
    bool ret = this->getAncestor(::std::move(iceP_appNodeID), iceP_listAncestor, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listAncestor, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceD_getListYv(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    istr->readAll(iceP_appNodeID);
    inS.endReadParams();
    ListStringMap iceP_listYv;
    ErrorInfo iceP_e;
    bool ret = this->getListYv(::std::move(iceP_appNodeID), iceP_listYv, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listYv, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPAppNodeManager_ops, iceC_ZG6000_ZGSPAppNodeManager_ops + 20, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPAppNodeManager_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getAncestor(in, current);
        }
        case 4:
        {
            return _iceD_getChild(in, current);
        }
        case 5:
        {
            return _iceD_getDescendant(in, current);
        }
        case 6:
        {
            return _iceD_getListYv(in, current);
        }
        case 7:
        {
            return _iceD_getParent(in, current);
        }
        case 8:
        {
            return _iceD_getVersion(in, current);
        }
        case 9:
        {
            return _iceD_heartDebug(in, current);
        }
        case 10:
        {
            return _iceD_ice_id(in, current);
        }
        case 11:
        {
            return _iceD_ice_ids(in, current);
        }
        case 12:
        {
            return _iceD_ice_isA(in, current);
        }
        case 13:
        {
            return _iceD_ice_ping(in, current);
        }
        case 14:
        {
            return _iceD_isDebugging(in, current);
        }
        case 15:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 16:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 17:
        {
            return _iceD_startDebug(in, current);
        }
        case 18:
        {
            return _iceD_stopDebug(in, current);
        }
        case 19:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPAppNodeManagerPrx::_iceI_getChild(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPAppNodeManager::GetChildResult>>& outAsync, const ::std::string& iceP_appNodeID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPAppNodeManager_getChild_name);
    outAsync->invoke(iceC_ZG6000_ZGSPAppNodeManager_getChild_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_appNodeID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPAppNodeManager::GetChildResult v;
            istr->readAll(v.listChild, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPAppNodeManagerPrx::_iceI_getParent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPAppNodeManager::GetParentResult>>& outAsync, const ::std::string& iceP_appNodeID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPAppNodeManager_getParent_name);
    outAsync->invoke(iceC_ZG6000_ZGSPAppNodeManager_getParent_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_appNodeID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPAppNodeManager::GetParentResult v;
            istr->readAll(v.listParent, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPAppNodeManagerPrx::_iceI_getDescendant(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPAppNodeManager::GetDescendantResult>>& outAsync, const ::std::string& iceP_appNodeID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPAppNodeManager_getDescendant_name);
    outAsync->invoke(iceC_ZG6000_ZGSPAppNodeManager_getDescendant_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_appNodeID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPAppNodeManager::GetDescendantResult v;
            istr->readAll(v.listDescendant, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPAppNodeManagerPrx::_iceI_getAncestor(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPAppNodeManager::GetAncestorResult>>& outAsync, const ::std::string& iceP_appNodeID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPAppNodeManager_getAncestor_name);
    outAsync->invoke(iceC_ZG6000_ZGSPAppNodeManager_getAncestor_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_appNodeID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPAppNodeManager::GetAncestorResult v;
            istr->readAll(v.listAncestor, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPAppNodeManagerPrx::_iceI_getListYv(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPAppNodeManager::GetListYvResult>>& outAsync, const ::std::string& iceP_appNodeID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPAppNodeManager_getListYv_name);
    outAsync->invoke(iceC_ZG6000_ZGSPAppNodeManager_getListYv_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_appNodeID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPAppNodeManager::GetListYvResult v;
            istr->readAll(v.listYv, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSPAppNodeManagerPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSPAppNodeManagerPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSPAppNodeManagerPrx::ice_staticId()
{
    return ZGSPAppNodeManager::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPAppNodeManager_getChild_name = "getChild";

const ::std::string iceC_ZG6000_ZGSPAppNodeManager_getParent_name = "getParent";

const ::std::string iceC_ZG6000_ZGSPAppNodeManager_getDescendant_name = "getDescendant";

const ::std::string iceC_ZG6000_ZGSPAppNodeManager_getAncestor_name = "getAncestor";

const ::std::string iceC_ZG6000_ZGSPAppNodeManager_getListYv_name = "getListYv";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSPAppNodeManager* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSPAppNodeManager>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSPAppNodeManager;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPAppNodeManager::_iceI_begin_getChild(const ::std::string& iceP_appNodeID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPAppNodeManager_getChild_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPAppNodeManager_getChild_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPAppNodeManager_getChild_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_appNodeID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPAppNodeManager_getChild_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPAppNodeManager::end_getChild(::ZG6000::ListStringMap& iceP_listChild, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPAppNodeManager_getChild_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listChild);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPAppNodeManager::_iceI_end_getChild(::ZG6000::ListStringMap& iceP_listChild, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPAppNodeManager_getChild_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listChild);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPAppNodeManager::_iceI_begin_getParent(const ::std::string& iceP_appNodeID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPAppNodeManager_getParent_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPAppNodeManager_getParent_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPAppNodeManager_getParent_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_appNodeID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPAppNodeManager_getParent_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPAppNodeManager::end_getParent(::ZG6000::ListStringMap& iceP_listParent, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPAppNodeManager_getParent_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listParent);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPAppNodeManager::_iceI_end_getParent(::ZG6000::ListStringMap& iceP_listParent, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPAppNodeManager_getParent_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listParent);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPAppNodeManager::_iceI_begin_getDescendant(const ::std::string& iceP_appNodeID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPAppNodeManager_getDescendant_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPAppNodeManager_getDescendant_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPAppNodeManager_getDescendant_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_appNodeID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPAppNodeManager_getDescendant_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPAppNodeManager::end_getDescendant(::ZG6000::ListStringMap& iceP_listDescendant, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPAppNodeManager_getDescendant_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listDescendant);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPAppNodeManager::_iceI_end_getDescendant(::ZG6000::ListStringMap& iceP_listDescendant, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPAppNodeManager_getDescendant_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listDescendant);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPAppNodeManager::_iceI_begin_getAncestor(const ::std::string& iceP_appNodeID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPAppNodeManager_getAncestor_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPAppNodeManager_getAncestor_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPAppNodeManager_getAncestor_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_appNodeID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPAppNodeManager_getAncestor_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPAppNodeManager::end_getAncestor(::ZG6000::ListStringMap& iceP_listAncestor, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPAppNodeManager_getAncestor_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listAncestor);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPAppNodeManager::_iceI_end_getAncestor(::ZG6000::ListStringMap& iceP_listAncestor, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPAppNodeManager_getAncestor_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listAncestor);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPAppNodeManager::_iceI_begin_getListYv(const ::std::string& iceP_appNodeID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPAppNodeManager_getListYv_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPAppNodeManager_getListYv_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPAppNodeManager_getListYv_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_appNodeID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPAppNodeManager_getListYv_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPAppNodeManager::end_getListYv(::ZG6000::ListStringMap& iceP_listYv, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPAppNodeManager_getListYv_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listYv);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPAppNodeManager::_iceI_end_getListYv(::ZG6000::ListStringMap& iceP_listYv, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPAppNodeManager_getListYv_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listYv);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSPAppNodeManager::_newInstance() const
{
    return new ZGSPAppNodeManager;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSPAppNodeManager::ice_staticId()
{
    return ::ZG6000::ZGSPAppNodeManager::ice_staticId();
}

ZG6000::ZGSPAppNodeManager::~ZGSPAppNodeManager()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSPAppNodeManager* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPAppNodeManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPAppNodeManager",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSPAppNodeManager::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPAppNodeManager_ids, iceC_ZG6000_ZGSPAppNodeManager_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSPAppNodeManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSPAppNodeManager_ids[0], &iceC_ZG6000_ZGSPAppNodeManager_ids[3]);
}

const ::std::string&
ZG6000::ZGSPAppNodeManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPAppNodeManager::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSPAppNodeManager";
    return typeId;
#else
    return iceC_ZG6000_ZGSPAppNodeManager_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceD_getChild(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    istr->read(iceP_appNodeID);
    inS.endReadParams();
    ListStringMap iceP_listChild;
    ErrorInfo iceP_e;
    bool ret = this->getChild(iceP_appNodeID, iceP_listChild, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listChild);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceD_getParent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    istr->read(iceP_appNodeID);
    inS.endReadParams();
    ListStringMap iceP_listParent;
    ErrorInfo iceP_e;
    bool ret = this->getParent(iceP_appNodeID, iceP_listParent, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listParent);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceD_getDescendant(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    istr->read(iceP_appNodeID);
    inS.endReadParams();
    ListStringMap iceP_listDescendant;
    ErrorInfo iceP_e;
    bool ret = this->getDescendant(iceP_appNodeID, iceP_listDescendant, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listDescendant);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceD_getAncestor(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    istr->read(iceP_appNodeID);
    inS.endReadParams();
    ListStringMap iceP_listAncestor;
    ErrorInfo iceP_e;
    bool ret = this->getAncestor(iceP_appNodeID, iceP_listAncestor, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listAncestor);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceD_getListYv(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    istr->read(iceP_appNodeID);
    inS.endReadParams();
    ListStringMap iceP_listYv;
    ErrorInfo iceP_e;
    bool ret = this->getListYv(iceP_appNodeID, iceP_listYv, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listYv);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPAppNodeManager_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getAncestor",
    "getChild",
    "getDescendant",
    "getListYv",
    "getParent",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSPAppNodeManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPAppNodeManager_all, iceC_ZG6000_ZGSPAppNodeManager_all + 20, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPAppNodeManager_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getAncestor(in, current);
        }
        case 4:
        {
            return _iceD_getChild(in, current);
        }
        case 5:
        {
            return _iceD_getDescendant(in, current);
        }
        case 6:
        {
            return _iceD_getListYv(in, current);
        }
        case 7:
        {
            return _iceD_getParent(in, current);
        }
        case 8:
        {
            return _iceD_getVersion(in, current);
        }
        case 9:
        {
            return _iceD_heartDebug(in, current);
        }
        case 10:
        {
            return _iceD_ice_id(in, current);
        }
        case 11:
        {
            return _iceD_ice_ids(in, current);
        }
        case 12:
        {
            return _iceD_ice_isA(in, current);
        }
        case 13:
        {
            return _iceD_ice_ping(in, current);
        }
        case 14:
        {
            return _iceD_isDebugging(in, current);
        }
        case 15:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 16:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 17:
        {
            return _iceD_startDebug(in, current);
        }
        case 18:
        {
            return _iceD_stopDebug(in, current);
        }
        case 19:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSPAppNodeManager::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSPAppNodeManager, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSPAppNodeManager::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSPAppNodeManager, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSPAppNodeManagerPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSPAppNodeManagerPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSPAppNodeManager::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

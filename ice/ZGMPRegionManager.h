//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPRegionManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGMPRegionManager_h__
#define __ZGMPRegionManager_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGMPRegionManager;
class ZGMPRegionManagerPrx;

}

namespace ZG6000
{

class ZGMPRegionManager : public virtual ZGServerBase
{
public:

    using ProxyType = ZGMPRegionManagerPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getRegionList.
     */
    struct GetRegionListResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ListStringMap listRegion;
        ErrorInfo e;
    };

    /**
     * @param param 查询参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取区域列表
     */
    virtual bool getRegionList(StringMap param, ListStringMap& listRegion, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getRegionList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getRegionAccess.
     */
    struct GetRegionAccessResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ListStringMap ListAccess;
        ErrorInfo e;
    };

    /**
     * @param regionID 区域ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取区域门禁参数
     */
    virtual bool getRegionAccess(::std::string regionID, ListStringMap& ListAccess, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getRegionAccess(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getRegionPeople.
     */
    struct GetRegionPeopleResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 平台人员列表 */
        ListStringMap listPeople;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param regionID 区域ID
     * @param listPeople 平台人员列表
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取平台人员列表
     */
    virtual bool getRegionPeople(::std::string regionID, ListStringMap& listPeople, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getRegionPeople(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getRegionYv.
     */
    struct GetRegionYvResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 视频列表ID */
        ListStringMap listYv;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param regionID 区域ID
     * @param listYv 视频列表ID
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取区域视频列表
     */
    virtual bool getRegionYv(::std::string regionID, ListStringMap& listYv, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getRegionYv(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to clearRegionPeople.
     */
    struct ClearRegionPeopleResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param regionID 区域ID
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 清空区域人员
     */
    virtual bool clearRegionPeople(::std::string regionID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_clearRegionPeople(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to resetWarn.
     */
    struct ResetWarnResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool resetWarn(::std::string regionID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_resetWarn(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void setupAlarm(::std::string regionID, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setupAlarm(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void closeAlarm(::std::string regionID, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_closeAlarm(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGMPRegionManagerPrx : public virtual ::Ice::Proxy<ZGMPRegionManagerPrx, ZGServerBasePrx>
{
public:

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取区域列表
     */
    bool getRegionList(const StringMap& param, ListStringMap& listRegion, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPRegionManager::GetRegionListResult>(true, this, &ZGMPRegionManagerPrx::_iceI_getRegionList, param, context).get();
        listRegion = ::std::move(_result.listRegion);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   获取区域列表
     */
    template<template<typename> class P = ::std::promise>
    auto getRegionListAsync(const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPRegionManager::GetRegionListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPRegionManager::GetRegionListResult, P>(false, this, &ZGMPRegionManagerPrx::_iceI_getRegionList, param, context);
    }

    /**
     * @param param 查询参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   获取区域列表
     */
    ::std::function<void()>
    getRegionListAsync(const StringMap& param,
                       ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPRegionManager::GetRegionListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listRegion), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPRegionManager::GetRegionListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPRegionManagerPrx::_iceI_getRegionList, param, context);
    }

    /// \cond INTERNAL
    void _iceI_getRegionList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::GetRegionListResult>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取区域门禁参数
     */
    bool getRegionAccess(const ::std::string& regionID, ListStringMap& ListAccess, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPRegionManager::GetRegionAccessResult>(true, this, &ZGMPRegionManagerPrx::_iceI_getRegionAccess, regionID, context).get();
        ListAccess = ::std::move(_result.ListAccess);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   获取区域门禁参数
     */
    template<template<typename> class P = ::std::promise>
    auto getRegionAccessAsync(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPRegionManager::GetRegionAccessResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPRegionManager::GetRegionAccessResult, P>(false, this, &ZGMPRegionManagerPrx::_iceI_getRegionAccess, regionID, context);
    }

    /**
     * @param regionID 区域ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   获取区域门禁参数
     */
    ::std::function<void()>
    getRegionAccessAsync(const ::std::string& regionID,
                         ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPRegionManager::GetRegionAccessResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.ListAccess), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPRegionManager::GetRegionAccessResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPRegionManagerPrx::_iceI_getRegionAccess, regionID, context);
    }

    /// \cond INTERNAL
    void _iceI_getRegionAccess(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::GetRegionAccessResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param regionID 区域ID
     * @param listPeople 平台人员列表
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取平台人员列表
     */
    bool getRegionPeople(const ::std::string& regionID, ListStringMap& listPeople, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPRegionManager::GetRegionPeopleResult>(true, this, &ZGMPRegionManagerPrx::_iceI_getRegionPeople, regionID, context).get();
        listPeople = ::std::move(_result.listPeople);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 获取平台人员列表
     */
    template<template<typename> class P = ::std::promise>
    auto getRegionPeopleAsync(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPRegionManager::GetRegionPeopleResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPRegionManager::GetRegionPeopleResult, P>(false, this, &ZGMPRegionManagerPrx::_iceI_getRegionPeople, regionID, context);
    }

    /**
     * @param regionID 区域ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 获取平台人员列表
     */
    ::std::function<void()>
    getRegionPeopleAsync(const ::std::string& regionID,
                         ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPRegionManager::GetRegionPeopleResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listPeople), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPRegionManager::GetRegionPeopleResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPRegionManagerPrx::_iceI_getRegionPeople, regionID, context);
    }

    /// \cond INTERNAL
    void _iceI_getRegionPeople(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::GetRegionPeopleResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param regionID 区域ID
     * @param listYv 视频列表ID
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取区域视频列表
     */
    bool getRegionYv(const ::std::string& regionID, ListStringMap& listYv, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPRegionManager::GetRegionYvResult>(true, this, &ZGMPRegionManagerPrx::_iceI_getRegionYv, regionID, context).get();
        listYv = ::std::move(_result.listYv);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 获取区域视频列表
     */
    template<template<typename> class P = ::std::promise>
    auto getRegionYvAsync(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPRegionManager::GetRegionYvResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPRegionManager::GetRegionYvResult, P>(false, this, &ZGMPRegionManagerPrx::_iceI_getRegionYv, regionID, context);
    }

    /**
     * @param regionID 区域ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 获取区域视频列表
     */
    ::std::function<void()>
    getRegionYvAsync(const ::std::string& regionID,
                     ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPRegionManager::GetRegionYvResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listYv), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPRegionManager::GetRegionYvResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPRegionManagerPrx::_iceI_getRegionYv, regionID, context);
    }

    /// \cond INTERNAL
    void _iceI_getRegionYv(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::GetRegionYvResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param regionID 区域ID
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 清空区域人员
     */
    bool clearRegionPeople(const ::std::string& regionID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPRegionManager::ClearRegionPeopleResult>(true, this, &ZGMPRegionManagerPrx::_iceI_clearRegionPeople, regionID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 清空区域人员
     */
    template<template<typename> class P = ::std::promise>
    auto clearRegionPeopleAsync(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPRegionManager::ClearRegionPeopleResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPRegionManager::ClearRegionPeopleResult, P>(false, this, &ZGMPRegionManagerPrx::_iceI_clearRegionPeople, regionID, context);
    }

    /**
     * @param regionID 区域ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 清空区域人员
     */
    ::std::function<void()>
    clearRegionPeopleAsync(const ::std::string& regionID,
                           ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPRegionManager::ClearRegionPeopleResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPRegionManager::ClearRegionPeopleResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPRegionManagerPrx::_iceI_clearRegionPeople, regionID, context);
    }

    /// \cond INTERNAL
    void _iceI_clearRegionPeople(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::ClearRegionPeopleResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool resetWarn(const ::std::string& regionID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPRegionManager::ResetWarnResult>(true, this, &ZGMPRegionManagerPrx::_iceI_resetWarn, regionID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto resetWarnAsync(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPRegionManager::ResetWarnResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPRegionManager::ResetWarnResult, P>(false, this, &ZGMPRegionManagerPrx::_iceI_resetWarn, regionID, context);
    }

    ::std::function<void()>
    resetWarnAsync(const ::std::string& regionID,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPRegionManager::ResetWarnResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPRegionManager::ResetWarnResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPRegionManagerPrx::_iceI_resetWarn, regionID, context);
    }

    /// \cond INTERNAL
    void _iceI_resetWarn(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::ResetWarnResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    void setupAlarm(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGMPRegionManagerPrx::_iceI_setupAlarm, regionID, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto setupAlarmAsync(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGMPRegionManagerPrx::_iceI_setupAlarm, regionID, context);
    }

    ::std::function<void()>
    setupAlarmAsync(const ::std::string& regionID,
                    ::std::function<void()> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGMPRegionManagerPrx::_iceI_setupAlarm, regionID, context);
    }

    /// \cond INTERNAL
    void _iceI_setupAlarm(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    void closeAlarm(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGMPRegionManagerPrx::_iceI_closeAlarm, regionID, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto closeAlarmAsync(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGMPRegionManagerPrx::_iceI_closeAlarm, regionID, context);
    }

    ::std::function<void()>
    closeAlarmAsync(const ::std::string& regionID,
                    ::std::function<void()> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGMPRegionManagerPrx::_iceI_closeAlarm, regionID, context);
    }

    /// \cond INTERNAL
    void _iceI_closeAlarm(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGMPRegionManagerPrx() = default;
    friend ::std::shared_ptr<ZGMPRegionManagerPrx> IceInternal::createProxy<ZGMPRegionManagerPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGMPRegionManagerPtr = ::std::shared_ptr<ZGMPRegionManager>;
using ZGMPRegionManagerPrxPtr = ::std::shared_ptr<ZGMPRegionManagerPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGMPRegionManager;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGMPRegionManager>&);
::IceProxy::Ice::Object* upCast(ZGMPRegionManager*);
/// \endcond

}

}

namespace ZG6000
{

class ZGMPRegionManager;
/// \cond INTERNAL
::Ice::Object* upCast(ZGMPRegionManager*);
/// \endcond
typedef ::IceInternal::Handle< ZGMPRegionManager> ZGMPRegionManagerPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGMPRegionManager> ZGMPRegionManagerPrx;
typedef ZGMPRegionManagerPrx ZGMPRegionManagerPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGMPRegionManagerPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionList.
 */
class Callback_ZGMPRegionManager_getRegionList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPRegionManager_getRegionList_Base> Callback_ZGMPRegionManager_getRegionListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionAccess.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionAccess.
 */
class Callback_ZGMPRegionManager_getRegionAccess_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPRegionManager_getRegionAccess_Base> Callback_ZGMPRegionManager_getRegionAccessPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionPeople.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionPeople.
 */
class Callback_ZGMPRegionManager_getRegionPeople_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPRegionManager_getRegionPeople_Base> Callback_ZGMPRegionManager_getRegionPeoplePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionYv.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionYv.
 */
class Callback_ZGMPRegionManager_getRegionYv_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPRegionManager_getRegionYv_Base> Callback_ZGMPRegionManager_getRegionYvPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_clearRegionPeople.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_clearRegionPeople.
 */
class Callback_ZGMPRegionManager_clearRegionPeople_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPRegionManager_clearRegionPeople_Base> Callback_ZGMPRegionManager_clearRegionPeoplePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_resetWarn.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_resetWarn.
 */
class Callback_ZGMPRegionManager_resetWarn_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPRegionManager_resetWarn_Base> Callback_ZGMPRegionManager_resetWarnPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_setupAlarm.
 */
class Callback_ZGMPRegionManager_setupAlarm_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPRegionManager_setupAlarm_Base> Callback_ZGMPRegionManager_setupAlarmPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_closeAlarm.
 */
class Callback_ZGMPRegionManager_closeAlarm_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPRegionManager_closeAlarm_Base> Callback_ZGMPRegionManager_closeAlarmPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGMPRegionManager : public virtual ::Ice::Proxy<ZGMPRegionManager, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取区域列表
     */
    bool getRegionList(const ::ZG6000::StringMap& param, ::ZG6000::ListStringMap& listRegion, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getRegionList(listRegion, e, _iceI_begin_getRegionList(param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取区域列表
     */
    ::Ice::AsyncResultPtr begin_getRegionList(const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getRegionList(param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param param 查询参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取区域列表
     */
    ::Ice::AsyncResultPtr begin_getRegionList(const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionList(param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取区域列表
     */
    ::Ice::AsyncResultPtr begin_getRegionList(const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionList(param, context, cb, cookie);
    }

    /**
     * @param param 查询参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取区域列表
     */
    ::Ice::AsyncResultPtr begin_getRegionList(const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGMPRegionManager_getRegionListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionList(param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取区域列表
     */
    ::Ice::AsyncResultPtr begin_getRegionList(const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPRegionManager_getRegionListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionList(param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getRegionList.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getRegionList(::ZG6000::ListStringMap& listRegion, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getRegionList(::ZG6000::ListStringMap& iceP_listRegion, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getRegionList(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取区域门禁参数
     */
    bool getRegionAccess(const ::std::string& regionID, ::ZG6000::ListStringMap& ListAccess, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getRegionAccess(ListAccess, e, _iceI_begin_getRegionAccess(regionID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取区域门禁参数
     */
    ::Ice::AsyncResultPtr begin_getRegionAccess(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getRegionAccess(regionID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param regionID 区域ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取区域门禁参数
     */
    ::Ice::AsyncResultPtr begin_getRegionAccess(const ::std::string& regionID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionAccess(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取区域门禁参数
     */
    ::Ice::AsyncResultPtr begin_getRegionAccess(const ::std::string& regionID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionAccess(regionID, context, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取区域门禁参数
     */
    ::Ice::AsyncResultPtr begin_getRegionAccess(const ::std::string& regionID, const ::ZG6000::Callback_ZGMPRegionManager_getRegionAccessPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionAccess(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取区域门禁参数
     */
    ::Ice::AsyncResultPtr begin_getRegionAccess(const ::std::string& regionID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPRegionManager_getRegionAccessPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionAccess(regionID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getRegionAccess.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getRegionAccess(::ZG6000::ListStringMap& ListAccess, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getRegionAccess(::ZG6000::ListStringMap& iceP_ListAccess, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getRegionAccess(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param regionID 区域ID
     * @param listPeople 平台人员列表
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取平台人员列表
     */
    bool getRegionPeople(const ::std::string& regionID, ::ZG6000::ListStringMap& listPeople, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getRegionPeople(listPeople, e, _iceI_begin_getRegionPeople(regionID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取平台人员列表
     */
    ::Ice::AsyncResultPtr begin_getRegionPeople(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getRegionPeople(regionID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param regionID 区域ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取平台人员列表
     */
    ::Ice::AsyncResultPtr begin_getRegionPeople(const ::std::string& regionID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionPeople(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取平台人员列表
     */
    ::Ice::AsyncResultPtr begin_getRegionPeople(const ::std::string& regionID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionPeople(regionID, context, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取平台人员列表
     */
    ::Ice::AsyncResultPtr begin_getRegionPeople(const ::std::string& regionID, const ::ZG6000::Callback_ZGMPRegionManager_getRegionPeoplePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionPeople(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取平台人员列表
     */
    ::Ice::AsyncResultPtr begin_getRegionPeople(const ::std::string& regionID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPRegionManager_getRegionPeoplePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionPeople(regionID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getRegionPeople.
     * @param listPeople 平台人员列表
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getRegionPeople(::ZG6000::ListStringMap& listPeople, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getRegionPeople(::ZG6000::ListStringMap& iceP_listPeople, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getRegionPeople(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param regionID 区域ID
     * @param listYv 视频列表ID
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取区域视频列表
     */
    bool getRegionYv(const ::std::string& regionID, ::ZG6000::ListStringMap& listYv, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getRegionYv(listYv, e, _iceI_begin_getRegionYv(regionID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取区域视频列表
     */
    ::Ice::AsyncResultPtr begin_getRegionYv(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getRegionYv(regionID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param regionID 区域ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取区域视频列表
     */
    ::Ice::AsyncResultPtr begin_getRegionYv(const ::std::string& regionID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionYv(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取区域视频列表
     */
    ::Ice::AsyncResultPtr begin_getRegionYv(const ::std::string& regionID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionYv(regionID, context, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取区域视频列表
     */
    ::Ice::AsyncResultPtr begin_getRegionYv(const ::std::string& regionID, const ::ZG6000::Callback_ZGMPRegionManager_getRegionYvPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionYv(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取区域视频列表
     */
    ::Ice::AsyncResultPtr begin_getRegionYv(const ::std::string& regionID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPRegionManager_getRegionYvPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRegionYv(regionID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getRegionYv.
     * @param listYv 视频列表ID
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getRegionYv(::ZG6000::ListStringMap& listYv, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getRegionYv(::ZG6000::ListStringMap& iceP_listYv, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getRegionYv(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param regionID 区域ID
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 清空区域人员
     */
    bool clearRegionPeople(const ::std::string& regionID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_clearRegionPeople(e, _iceI_begin_clearRegionPeople(regionID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 清空区域人员
     */
    ::Ice::AsyncResultPtr begin_clearRegionPeople(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_clearRegionPeople(regionID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param regionID 区域ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 清空区域人员
     */
    ::Ice::AsyncResultPtr begin_clearRegionPeople(const ::std::string& regionID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearRegionPeople(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 清空区域人员
     */
    ::Ice::AsyncResultPtr begin_clearRegionPeople(const ::std::string& regionID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearRegionPeople(regionID, context, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 清空区域人员
     */
    ::Ice::AsyncResultPtr begin_clearRegionPeople(const ::std::string& regionID, const ::ZG6000::Callback_ZGMPRegionManager_clearRegionPeoplePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearRegionPeople(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param regionID 区域ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 清空区域人员
     */
    ::Ice::AsyncResultPtr begin_clearRegionPeople(const ::std::string& regionID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPRegionManager_clearRegionPeoplePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearRegionPeople(regionID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_clearRegionPeople.
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_clearRegionPeople(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_clearRegionPeople(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_clearRegionPeople(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool resetWarn(const ::std::string& regionID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_resetWarn(e, _iceI_begin_resetWarn(regionID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_resetWarn(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_resetWarn(regionID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_resetWarn(const ::std::string& regionID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetWarn(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resetWarn(const ::std::string& regionID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetWarn(regionID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resetWarn(const ::std::string& regionID, const ::ZG6000::Callback_ZGMPRegionManager_resetWarnPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetWarn(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resetWarn(const ::std::string& regionID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPRegionManager_resetWarnPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetWarn(regionID, context, cb, cookie);
    }

    bool end_resetWarn(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_resetWarn(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_resetWarn(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    void setupAlarm(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_setupAlarm(_iceI_begin_setupAlarm(regionID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setupAlarm(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setupAlarm(regionID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setupAlarm(const ::std::string& regionID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setupAlarm(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setupAlarm(const ::std::string& regionID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setupAlarm(regionID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setupAlarm(const ::std::string& regionID, const ::ZG6000::Callback_ZGMPRegionManager_setupAlarmPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setupAlarm(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setupAlarm(const ::std::string& regionID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPRegionManager_setupAlarmPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setupAlarm(regionID, context, cb, cookie);
    }

    void end_setupAlarm(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_setupAlarm(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    void closeAlarm(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_closeAlarm(_iceI_begin_closeAlarm(regionID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_closeAlarm(const ::std::string& regionID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_closeAlarm(regionID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_closeAlarm(const ::std::string& regionID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeAlarm(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_closeAlarm(const ::std::string& regionID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeAlarm(regionID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_closeAlarm(const ::std::string& regionID, const ::ZG6000::Callback_ZGMPRegionManager_closeAlarmPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeAlarm(regionID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_closeAlarm(const ::std::string& regionID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPRegionManager_closeAlarmPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeAlarm(regionID, context, cb, cookie);
    }

    void end_closeAlarm(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_closeAlarm(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGMPRegionManager : virtual public ZGServerBase
{
public:

    typedef ZGMPRegionManagerPrx ProxyType;
    typedef ZGMPRegionManagerPtr PointerType;

    virtual ~ZGMPRegionManager();

#ifdef ICE_CPP11_COMPILER
    ZGMPRegionManager() = default;
    ZGMPRegionManager(const ZGMPRegionManager&) = default;
    ZGMPRegionManager& operator=(const ZGMPRegionManager&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param param 查询参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取区域列表
     */
    virtual bool getRegionList(const StringMap& param, ListStringMap& listRegion, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getRegionList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param regionID 区域ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取区域门禁参数
     */
    virtual bool getRegionAccess(const ::std::string& regionID, ListStringMap& ListAccess, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getRegionAccess(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param regionID 区域ID
     * @param listPeople 平台人员列表
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取平台人员列表
     */
    virtual bool getRegionPeople(const ::std::string& regionID, ListStringMap& listPeople, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getRegionPeople(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param regionID 区域ID
     * @param listYv 视频列表ID
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取区域视频列表
     */
    virtual bool getRegionYv(const ::std::string& regionID, ListStringMap& listYv, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getRegionYv(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param regionID 区域ID
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 清空区域人员
     */
    virtual bool clearRegionPeople(const ::std::string& regionID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_clearRegionPeople(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool resetWarn(const ::std::string& regionID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_resetWarn(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void setupAlarm(const ::std::string& regionID, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setupAlarm(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void closeAlarm(const ::std::string& regionID, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_closeAlarm(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGMPRegionManager& lhs, const ZGMPRegionManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGMPRegionManager& lhs, const ZGMPRegionManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionList.
 */
template<class T>
class CallbackNC_ZGMPRegionManager_getRegionList : public Callback_ZGMPRegionManager_getRegionList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPRegionManager_getRegionList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listRegion;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRegionList(iceP_listRegion, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listRegion, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionList.
 */
template<class T> Callback_ZGMPRegionManager_getRegionListPtr
newCallback_ZGMPRegionManager_getRegionList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_getRegionList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionList.
 */
template<class T> Callback_ZGMPRegionManager_getRegionListPtr
newCallback_ZGMPRegionManager_getRegionList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_getRegionList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionList.
 */
template<class T, typename CT>
class Callback_ZGMPRegionManager_getRegionList : public Callback_ZGMPRegionManager_getRegionList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPRegionManager_getRegionList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listRegion;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRegionList(iceP_listRegion, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listRegion, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionList.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_getRegionListPtr
newCallback_ZGMPRegionManager_getRegionList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_getRegionList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionList.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_getRegionListPtr
newCallback_ZGMPRegionManager_getRegionList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_getRegionList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionAccess.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionAccess.
 */
template<class T>
class CallbackNC_ZGMPRegionManager_getRegionAccess : public Callback_ZGMPRegionManager_getRegionAccess_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPRegionManager_getRegionAccess(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_ListAccess;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRegionAccess(iceP_ListAccess, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_ListAccess, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionAccess.
 */
template<class T> Callback_ZGMPRegionManager_getRegionAccessPtr
newCallback_ZGMPRegionManager_getRegionAccess(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_getRegionAccess<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionAccess.
 */
template<class T> Callback_ZGMPRegionManager_getRegionAccessPtr
newCallback_ZGMPRegionManager_getRegionAccess(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_getRegionAccess<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionAccess.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionAccess.
 */
template<class T, typename CT>
class Callback_ZGMPRegionManager_getRegionAccess : public Callback_ZGMPRegionManager_getRegionAccess_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPRegionManager_getRegionAccess(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_ListAccess;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRegionAccess(iceP_ListAccess, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_ListAccess, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionAccess.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_getRegionAccessPtr
newCallback_ZGMPRegionManager_getRegionAccess(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_getRegionAccess<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionAccess.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_getRegionAccessPtr
newCallback_ZGMPRegionManager_getRegionAccess(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_getRegionAccess<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionPeople.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionPeople.
 */
template<class T>
class CallbackNC_ZGMPRegionManager_getRegionPeople : public Callback_ZGMPRegionManager_getRegionPeople_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPRegionManager_getRegionPeople(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listPeople;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRegionPeople(iceP_listPeople, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listPeople, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionPeople.
 */
template<class T> Callback_ZGMPRegionManager_getRegionPeoplePtr
newCallback_ZGMPRegionManager_getRegionPeople(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_getRegionPeople<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionPeople.
 */
template<class T> Callback_ZGMPRegionManager_getRegionPeoplePtr
newCallback_ZGMPRegionManager_getRegionPeople(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_getRegionPeople<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionPeople.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionPeople.
 */
template<class T, typename CT>
class Callback_ZGMPRegionManager_getRegionPeople : public Callback_ZGMPRegionManager_getRegionPeople_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPRegionManager_getRegionPeople(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listPeople;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRegionPeople(iceP_listPeople, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listPeople, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionPeople.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_getRegionPeoplePtr
newCallback_ZGMPRegionManager_getRegionPeople(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_getRegionPeople<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionPeople.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_getRegionPeoplePtr
newCallback_ZGMPRegionManager_getRegionPeople(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_getRegionPeople<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionYv.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionYv.
 */
template<class T>
class CallbackNC_ZGMPRegionManager_getRegionYv : public Callback_ZGMPRegionManager_getRegionYv_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPRegionManager_getRegionYv(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listYv;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRegionYv(iceP_listYv, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listYv, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionYv.
 */
template<class T> Callback_ZGMPRegionManager_getRegionYvPtr
newCallback_ZGMPRegionManager_getRegionYv(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_getRegionYv<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionYv.
 */
template<class T> Callback_ZGMPRegionManager_getRegionYvPtr
newCallback_ZGMPRegionManager_getRegionYv(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_getRegionYv<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionYv.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_getRegionYv.
 */
template<class T, typename CT>
class Callback_ZGMPRegionManager_getRegionYv : public Callback_ZGMPRegionManager_getRegionYv_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPRegionManager_getRegionYv(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listYv;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRegionYv(iceP_listYv, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listYv, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionYv.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_getRegionYvPtr
newCallback_ZGMPRegionManager_getRegionYv(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_getRegionYv<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_getRegionYv.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_getRegionYvPtr
newCallback_ZGMPRegionManager_getRegionYv(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_getRegionYv<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_clearRegionPeople.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_clearRegionPeople.
 */
template<class T>
class CallbackNC_ZGMPRegionManager_clearRegionPeople : public Callback_ZGMPRegionManager_clearRegionPeople_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPRegionManager_clearRegionPeople(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_clearRegionPeople(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_clearRegionPeople.
 */
template<class T> Callback_ZGMPRegionManager_clearRegionPeoplePtr
newCallback_ZGMPRegionManager_clearRegionPeople(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_clearRegionPeople<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_clearRegionPeople.
 */
template<class T> Callback_ZGMPRegionManager_clearRegionPeoplePtr
newCallback_ZGMPRegionManager_clearRegionPeople(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_clearRegionPeople<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_clearRegionPeople.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_clearRegionPeople.
 */
template<class T, typename CT>
class Callback_ZGMPRegionManager_clearRegionPeople : public Callback_ZGMPRegionManager_clearRegionPeople_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPRegionManager_clearRegionPeople(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_clearRegionPeople(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_clearRegionPeople.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_clearRegionPeoplePtr
newCallback_ZGMPRegionManager_clearRegionPeople(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_clearRegionPeople<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_clearRegionPeople.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_clearRegionPeoplePtr
newCallback_ZGMPRegionManager_clearRegionPeople(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_clearRegionPeople<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_resetWarn.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_resetWarn.
 */
template<class T>
class CallbackNC_ZGMPRegionManager_resetWarn : public Callback_ZGMPRegionManager_resetWarn_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPRegionManager_resetWarn(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_resetWarn(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_resetWarn.
 */
template<class T> Callback_ZGMPRegionManager_resetWarnPtr
newCallback_ZGMPRegionManager_resetWarn(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_resetWarn<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_resetWarn.
 */
template<class T> Callback_ZGMPRegionManager_resetWarnPtr
newCallback_ZGMPRegionManager_resetWarn(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_resetWarn<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_resetWarn.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_resetWarn.
 */
template<class T, typename CT>
class Callback_ZGMPRegionManager_resetWarn : public Callback_ZGMPRegionManager_resetWarn_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPRegionManager_resetWarn(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRegionManagerPrx proxy = ZGMPRegionManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_resetWarn(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_resetWarn.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_resetWarnPtr
newCallback_ZGMPRegionManager_resetWarn(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_resetWarn<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_resetWarn.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_resetWarnPtr
newCallback_ZGMPRegionManager_resetWarn(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_resetWarn<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_setupAlarm.
 */
template<class T>
class CallbackNC_ZGMPRegionManager_setupAlarm : public Callback_ZGMPRegionManager_setupAlarm_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGMPRegionManager_setupAlarm(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 */
template<class T> Callback_ZGMPRegionManager_setupAlarmPtr
newCallback_ZGMPRegionManager_setupAlarm(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_setupAlarm<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 */
template<class T> Callback_ZGMPRegionManager_setupAlarmPtr
newCallback_ZGMPRegionManager_setupAlarm(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_setupAlarm<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 */
template<class T> Callback_ZGMPRegionManager_setupAlarmPtr
newCallback_ZGMPRegionManager_setupAlarm(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_setupAlarm<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 */
template<class T> Callback_ZGMPRegionManager_setupAlarmPtr
newCallback_ZGMPRegionManager_setupAlarm(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_setupAlarm<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_setupAlarm.
 */
template<class T, typename CT>
class Callback_ZGMPRegionManager_setupAlarm : public Callback_ZGMPRegionManager_setupAlarm_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGMPRegionManager_setupAlarm(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_setupAlarmPtr
newCallback_ZGMPRegionManager_setupAlarm(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_setupAlarm<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_setupAlarmPtr
newCallback_ZGMPRegionManager_setupAlarm(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_setupAlarm<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_setupAlarmPtr
newCallback_ZGMPRegionManager_setupAlarm(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_setupAlarm<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_setupAlarm.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_setupAlarmPtr
newCallback_ZGMPRegionManager_setupAlarm(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_setupAlarm<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_closeAlarm.
 */
template<class T>
class CallbackNC_ZGMPRegionManager_closeAlarm : public Callback_ZGMPRegionManager_closeAlarm_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGMPRegionManager_closeAlarm(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 */
template<class T> Callback_ZGMPRegionManager_closeAlarmPtr
newCallback_ZGMPRegionManager_closeAlarm(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_closeAlarm<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 */
template<class T> Callback_ZGMPRegionManager_closeAlarmPtr
newCallback_ZGMPRegionManager_closeAlarm(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_closeAlarm<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 */
template<class T> Callback_ZGMPRegionManager_closeAlarmPtr
newCallback_ZGMPRegionManager_closeAlarm(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_closeAlarm<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 */
template<class T> Callback_ZGMPRegionManager_closeAlarmPtr
newCallback_ZGMPRegionManager_closeAlarm(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRegionManager_closeAlarm<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRegionManager_closeAlarm.
 */
template<class T, typename CT>
class Callback_ZGMPRegionManager_closeAlarm : public Callback_ZGMPRegionManager_closeAlarm_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGMPRegionManager_closeAlarm(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_closeAlarmPtr
newCallback_ZGMPRegionManager_closeAlarm(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_closeAlarm<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_closeAlarmPtr
newCallback_ZGMPRegionManager_closeAlarm(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_closeAlarm<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_closeAlarmPtr
newCallback_ZGMPRegionManager_closeAlarm(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_closeAlarm<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRegionManager::begin_closeAlarm.
 */
template<class T, typename CT> Callback_ZGMPRegionManager_closeAlarmPtr
newCallback_ZGMPRegionManager_closeAlarm(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRegionManager_closeAlarm<T, CT>(instance, 0, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

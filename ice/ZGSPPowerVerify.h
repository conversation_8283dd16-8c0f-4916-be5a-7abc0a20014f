//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPPowerVerify.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPPowerVerify_h__
#define __ZGSPPowerVerify_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPPowerVerify;
class ZGSPPowerVerifyPrx;

}

namespace ZG6000
{

class ZGSPPowerVerify : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPPowerVerifyPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to isUserHasPower.
     */
    struct IsUserHasPowerResult
    {
        bool returnValue;
        bool hasPower;
        ErrorInfo e;
    };

    virtual bool isUserHasPower(::std::string userID, ::std::string powerID, bool& hasPower, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_isUserHasPower(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to loginByPassword.
     */
    struct LoginByPasswordResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool loginByPassword(::std::string clientID, ::std::string userID, ::std::string password, int keepTime, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to loginByPasswordAndVerifyCode.
     */
    struct LoginByPasswordAndVerifyCodeResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool loginByPasswordAndVerifyCode(::std::string clientID, ::std::string userID, ::std::string password, ::std::string verifyCode, int keepTime, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByPasswordAndVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to loginByCard.
     */
    struct LoginByCardResult
    {
        bool returnValue;
        ::std::string realUserID;
        ErrorInfo e;
    };

    virtual bool loginByCard(::std::string clientID, ::std::string userID, ::std::string authModeID, ::std::string cardID, int keepTime, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to loginByAuthDev.
     */
    struct LoginByAuthDevResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool loginByAuthDev(::std::string clientID, ::std::string userID, ::std::string authModeID, int keepTime, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByAuthDev(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to logout.
     */
    struct LogoutResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool logout(::std::string clientID, ::std::string userID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_logout(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getAvaiableUser.
     */
    struct GetAvaiableUserResult
    {
        bool returnValue;
        ListStringMap lstUser;
        ErrorInfo e;
    };

    virtual bool getAvaiableUser(::std::string clientID, ::std::string appNodeID, ::std::string powerID, ListStringMap& lstUser, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getAvaiableUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByPassword.
     */
    struct VerifyByPasswordResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool verifyByPassword(::std::string clientID, ::std::string userID, ::std::string password, ::std::string appNodeID, ::std::string powerID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByPasswordAndVerifyCode.
     */
    struct VerifyByPasswordAndVerifyCodeResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool verifyByPasswordAndVerifyCode(::std::string clientID, ::std::string userID, ::std::string password, ::std::string verifyCode, ::std::string appNodeID, ::std::string powerID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByPasswordAndVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByCard.
     */
    struct VerifyByCardResult
    {
        bool returnValue;
        ::std::string realUserID;
        ErrorInfo e;
    };

    virtual bool verifyByCard(::std::string clientID, ::std::string userID, ::std::string authModeID, ::std::string cardID, ::std::string appNodeID, ::std::string powerID, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByAuthDev.
     */
    struct VerifyByAuthDevResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool verifyByAuthDev(::std::string clientID, ::std::string userID, ::std::string authModeID, ::std::string appNodeID, ::std::string powerID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByAuthDev(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to sendVerifyCode.
     */
    struct SendVerifyCodeResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool sendVerifyCode(::std::string userID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_sendVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPPowerVerifyPrx : public virtual ::Ice::Proxy<ZGSPPowerVerifyPrx, ZGServerBasePrx>
{
public:

    bool isUserHasPower(const ::std::string& userID, const ::std::string& powerID, bool& hasPower, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::IsUserHasPowerResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_isUserHasPower, userID, powerID, context).get();
        hasPower = _result.hasPower;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto isUserHasPowerAsync(const ::std::string& userID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::IsUserHasPowerResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::IsUserHasPowerResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_isUserHasPower, userID, powerID, context);
    }

    ::std::function<void()>
    isUserHasPowerAsync(const ::std::string& userID, const ::std::string& powerID,
                        ::std::function<void(bool, bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::IsUserHasPowerResult&& _result)
        {
            response(_result.returnValue, _result.hasPower, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::IsUserHasPowerResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_isUserHasPower, userID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_isUserHasPower(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::IsUserHasPowerResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, int keepTime, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::LoginByPasswordResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_loginByPassword, clientID, userID, password, keepTime, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto loginByPasswordAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::LoginByPasswordResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::LoginByPasswordResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_loginByPassword, clientID, userID, password, keepTime, context);
    }

    ::std::function<void()>
    loginByPasswordAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, int keepTime,
                         ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::LoginByPasswordResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::LoginByPasswordResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_loginByPassword, clientID, userID, password, keepTime, context);
    }

    /// \cond INTERNAL
    void _iceI_loginByPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::LoginByPasswordResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    bool loginByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, int keepTime, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::LoginByPasswordAndVerifyCodeResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_loginByPasswordAndVerifyCode, clientID, userID, password, verifyCode, keepTime, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto loginByPasswordAndVerifyCodeAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::LoginByPasswordAndVerifyCodeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::LoginByPasswordAndVerifyCodeResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_loginByPasswordAndVerifyCode, clientID, userID, password, verifyCode, keepTime, context);
    }

    ::std::function<void()>
    loginByPasswordAndVerifyCodeAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, int keepTime,
                                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                      ::std::function<void(bool)> sent = nullptr,
                                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::LoginByPasswordAndVerifyCodeResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::LoginByPasswordAndVerifyCodeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_loginByPasswordAndVerifyCode, clientID, userID, password, verifyCode, keepTime, context);
    }

    /// \cond INTERNAL
    void _iceI_loginByPasswordAndVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::LoginByPasswordAndVerifyCodeResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    bool loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, int keepTime, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::LoginByCardResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_loginByCard, clientID, userID, authModeID, cardID, keepTime, context).get();
        realUserID = ::std::move(_result.realUserID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto loginByCardAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::LoginByCardResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::LoginByCardResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_loginByCard, clientID, userID, authModeID, cardID, keepTime, context);
    }

    ::std::function<void()>
    loginByCardAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, int keepTime,
                     ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::LoginByCardResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.realUserID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::LoginByCardResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_loginByCard, clientID, userID, authModeID, cardID, keepTime, context);
    }

    /// \cond INTERNAL
    void _iceI_loginByCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::LoginByCardResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    bool loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, int keepTime, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::LoginByAuthDevResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_loginByAuthDev, clientID, userID, authModeID, keepTime, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto loginByAuthDevAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::LoginByAuthDevResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::LoginByAuthDevResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_loginByAuthDev, clientID, userID, authModeID, keepTime, context);
    }

    ::std::function<void()>
    loginByAuthDevAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, int keepTime,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::LoginByAuthDevResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::LoginByAuthDevResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_loginByAuthDev, clientID, userID, authModeID, keepTime, context);
    }

    /// \cond INTERNAL
    void _iceI_loginByAuthDev(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::LoginByAuthDevResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    bool logout(const ::std::string& clientID, const ::std::string& userID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::LogoutResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_logout, clientID, userID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto logoutAsync(const ::std::string& clientID, const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::LogoutResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::LogoutResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_logout, clientID, userID, context);
    }

    ::std::function<void()>
    logoutAsync(const ::std::string& clientID, const ::std::string& userID,
                ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                ::std::function<void(::std::exception_ptr)> ex = nullptr,
                ::std::function<void(bool)> sent = nullptr,
                const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::LogoutResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::LogoutResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_logout, clientID, userID, context);
    }

    /// \cond INTERNAL
    void _iceI_logout(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::LogoutResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, ListStringMap& lstUser, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::GetAvaiableUserResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_getAvaiableUser, clientID, appNodeID, powerID, context).get();
        lstUser = ::std::move(_result.lstUser);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getAvaiableUserAsync(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::GetAvaiableUserResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::GetAvaiableUserResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_getAvaiableUser, clientID, appNodeID, powerID, context);
    }

    ::std::function<void()>
    getAvaiableUserAsync(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID,
                         ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::GetAvaiableUserResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.lstUser), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::GetAvaiableUserResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_getAvaiableUser, clientID, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_getAvaiableUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::GetAvaiableUserResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::VerifyByPasswordResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_verifyByPassword, clientID, userID, password, appNodeID, powerID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByPasswordAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::VerifyByPasswordResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::VerifyByPasswordResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_verifyByPassword, clientID, userID, password, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByPasswordAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::VerifyByPasswordResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::VerifyByPasswordResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_verifyByPassword, clientID, userID, password, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::VerifyByPasswordResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::VerifyByPasswordAndVerifyCodeResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_verifyByPasswordAndVerifyCode, clientID, userID, password, verifyCode, appNodeID, powerID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByPasswordAndVerifyCodeAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::VerifyByPasswordAndVerifyCodeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::VerifyByPasswordAndVerifyCodeResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_verifyByPasswordAndVerifyCode, clientID, userID, password, verifyCode, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByPasswordAndVerifyCodeAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, const ::std::string& appNodeID, const ::std::string& powerID,
                                       ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                       ::std::function<void(bool)> sent = nullptr,
                                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::VerifyByPasswordAndVerifyCodeResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::VerifyByPasswordAndVerifyCodeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_verifyByPasswordAndVerifyCode, clientID, userID, password, verifyCode, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByPasswordAndVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::VerifyByPasswordAndVerifyCodeResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::VerifyByCardResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_verifyByCard, clientID, userID, authModeID, cardID, appNodeID, powerID, context).get();
        realUserID = ::std::move(_result.realUserID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByCardAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::VerifyByCardResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::VerifyByCardResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_verifyByCard, clientID, userID, authModeID, cardID, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByCardAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID,
                      ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::VerifyByCardResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.realUserID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::VerifyByCardResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_verifyByCard, clientID, userID, authModeID, cardID, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::VerifyByCardResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::VerifyByAuthDevResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_verifyByAuthDev, clientID, userID, authModeID, appNodeID, powerID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByAuthDevAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::VerifyByAuthDevResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::VerifyByAuthDevResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_verifyByAuthDev, clientID, userID, authModeID, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByAuthDevAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID,
                         ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::VerifyByAuthDevResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::VerifyByAuthDevResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_verifyByAuthDev, clientID, userID, authModeID, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByAuthDev(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::VerifyByAuthDevResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool sendVerifyCode(const ::std::string& userID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPPowerVerify::SendVerifyCodeResult>(true, this, &ZGSPPowerVerifyPrx::_iceI_sendVerifyCode, userID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto sendVerifyCodeAsync(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPPowerVerify::SendVerifyCodeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPPowerVerify::SendVerifyCodeResult, P>(false, this, &ZGSPPowerVerifyPrx::_iceI_sendVerifyCode, userID, context);
    }

    ::std::function<void()>
    sendVerifyCodeAsync(const ::std::string& userID,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPPowerVerify::SendVerifyCodeResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPPowerVerify::SendVerifyCodeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPPowerVerifyPrx::_iceI_sendVerifyCode, userID, context);
    }

    /// \cond INTERNAL
    void _iceI_sendVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::SendVerifyCodeResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPPowerVerifyPrx() = default;
    friend ::std::shared_ptr<ZGSPPowerVerifyPrx> IceInternal::createProxy<ZGSPPowerVerifyPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPPowerVerifyPtr = ::std::shared_ptr<ZGSPPowerVerify>;
using ZGSPPowerVerifyPrxPtr = ::std::shared_ptr<ZGSPPowerVerifyPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPPowerVerify;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPPowerVerify>&);
::IceProxy::Ice::Object* upCast(ZGSPPowerVerify*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPPowerVerify;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPPowerVerify*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPPowerVerify> ZGSPPowerVerifyPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPPowerVerify> ZGSPPowerVerifyPrx;
typedef ZGSPPowerVerifyPrx ZGSPPowerVerifyPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPPowerVerifyPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_isUserHasPower.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_isUserHasPower.
 */
class Callback_ZGSPPowerVerify_isUserHasPower_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_isUserHasPower_Base> Callback_ZGSPPowerVerify_isUserHasPowerPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByPassword.
 */
class Callback_ZGSPPowerVerify_loginByPassword_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_loginByPassword_Base> Callback_ZGSPPowerVerify_loginByPasswordPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPasswordAndVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByPasswordAndVerifyCode.
 */
class Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCode_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCode_Base> Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCodePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByCard.
 */
class Callback_ZGSPPowerVerify_loginByCard_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_loginByCard_Base> Callback_ZGSPPowerVerify_loginByCardPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByAuthDev.
 */
class Callback_ZGSPPowerVerify_loginByAuthDev_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_loginByAuthDev_Base> Callback_ZGSPPowerVerify_loginByAuthDevPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_logout.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_logout.
 */
class Callback_ZGSPPowerVerify_logout_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_logout_Base> Callback_ZGSPPowerVerify_logoutPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_getAvaiableUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_getAvaiableUser.
 */
class Callback_ZGSPPowerVerify_getAvaiableUser_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_getAvaiableUser_Base> Callback_ZGSPPowerVerify_getAvaiableUserPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByPassword.
 */
class Callback_ZGSPPowerVerify_verifyByPassword_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_verifyByPassword_Base> Callback_ZGSPPowerVerify_verifyByPasswordPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPasswordAndVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode.
 */
class Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_Base> Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCodePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByCard.
 */
class Callback_ZGSPPowerVerify_verifyByCard_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_verifyByCard_Base> Callback_ZGSPPowerVerify_verifyByCardPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByAuthDev.
 */
class Callback_ZGSPPowerVerify_verifyByAuthDev_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_verifyByAuthDev_Base> Callback_ZGSPPowerVerify_verifyByAuthDevPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_sendVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_sendVerifyCode.
 */
class Callback_ZGSPPowerVerify_sendVerifyCode_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPPowerVerify_sendVerifyCode_Base> Callback_ZGSPPowerVerify_sendVerifyCodePtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPPowerVerify : public virtual ::Ice::Proxy<ZGSPPowerVerify, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool isUserHasPower(const ::std::string& userID, const ::std::string& powerID, bool& hasPower, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_isUserHasPower(hasPower, e, _iceI_begin_isUserHasPower(userID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_isUserHasPower(const ::std::string& userID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_isUserHasPower(userID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_isUserHasPower(const ::std::string& userID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isUserHasPower(userID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isUserHasPower(const ::std::string& userID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isUserHasPower(userID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isUserHasPower(const ::std::string& userID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPPowerVerify_isUserHasPowerPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isUserHasPower(userID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isUserHasPower(const ::std::string& userID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_isUserHasPowerPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isUserHasPower(userID, powerID, context, cb, cookie);
    }

    bool end_isUserHasPower(bool& hasPower, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_isUserHasPower(bool& iceP_hasPower, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_isUserHasPower(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_loginByPassword(e, _iceI_begin_loginByPassword(clientID, userID, password, keepTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_loginByPassword(clientID, userID, password, keepTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPassword(clientID, userID, password, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPassword(clientID, userID, password, keepTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, const ::ZG6000::Callback_ZGSPPowerVerify_loginByPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPassword(clientID, userID, password, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_loginByPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPassword(clientID, userID, password, keepTime, context, cb, cookie);
    }

    bool end_loginByPassword(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_loginByPassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_loginByPassword(const ::std::string&, const ::std::string&, const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool loginByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, ::Ice::Int keepTime, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_loginByPasswordAndVerifyCode(e, _iceI_begin_loginByPasswordAndVerifyCode(clientID, userID, password, verifyCode, keepTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_loginByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, ::Ice::Int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_loginByPasswordAndVerifyCode(clientID, userID, password, verifyCode, keepTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_loginByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, ::Ice::Int keepTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPasswordAndVerifyCode(clientID, userID, password, verifyCode, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, ::Ice::Int keepTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPasswordAndVerifyCode(clientID, userID, password, verifyCode, keepTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, ::Ice::Int keepTime, const ::ZG6000::Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPasswordAndVerifyCode(clientID, userID, password, verifyCode, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, ::Ice::Int keepTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPasswordAndVerifyCode(clientID, userID, password, verifyCode, keepTime, context, cb, cookie);
    }

    bool end_loginByPasswordAndVerifyCode(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_loginByPasswordAndVerifyCode(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_loginByPasswordAndVerifyCode(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, ::std::string& realUserID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_loginByCard(realUserID, e, _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, const ::ZG6000::Callback_ZGSPPowerVerify_loginByCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_loginByCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, context, cb, cookie);
    }

    bool end_loginByCard(::std::string& realUserID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_loginByCard(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_loginByCard(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_loginByAuthDev(e, _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, const ::ZG6000::Callback_ZGSPPowerVerify_loginByAuthDevPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_loginByAuthDevPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, context, cb, cookie);
    }

    bool end_loginByAuthDev(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_loginByAuthDev(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_loginByAuthDev(const ::std::string&, const ::std::string&, const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool logout(const ::std::string& clientID, const ::std::string& userID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_logout(e, _iceI_begin_logout(clientID, userID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_logout(const ::std::string& clientID, const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_logout(clientID, userID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_logout(const ::std::string& clientID, const ::std::string& userID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_logout(clientID, userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_logout(const ::std::string& clientID, const ::std::string& userID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_logout(clientID, userID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_logout(const ::std::string& clientID, const ::std::string& userID, const ::ZG6000::Callback_ZGSPPowerVerify_logoutPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_logout(clientID, userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_logout(const ::std::string& clientID, const ::std::string& userID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_logoutPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_logout(clientID, userID, context, cb, cookie);
    }

    bool end_logout(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_logout(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_logout(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, ::ZG6000::ListStringMap& lstUser, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getAvaiableUser(lstUser, e, _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPPowerVerify_getAvaiableUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_getAvaiableUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, context, cb, cookie);
    }

    bool end_getAvaiableUser(::ZG6000::ListStringMap& lstUser, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getAvaiableUser(::ZG6000::ListStringMap& iceP_lstUser, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getAvaiableUser(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByPassword(e, _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPPowerVerify_verifyByPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_verifyByPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByPassword(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByPassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByPassword(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, const ::std::string& appNodeID, const ::std::string& powerID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByPasswordAndVerifyCode(e, _iceI_begin_verifyByPasswordAndVerifyCode(clientID, userID, password, verifyCode, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByPasswordAndVerifyCode(clientID, userID, password, verifyCode, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPasswordAndVerifyCode(clientID, userID, password, verifyCode, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPasswordAndVerifyCode(clientID, userID, password, verifyCode, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPasswordAndVerifyCode(clientID, userID, password, verifyCode, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPasswordAndVerifyCode(clientID, userID, password, verifyCode, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByPasswordAndVerifyCode(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByPasswordAndVerifyCode(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByPasswordAndVerifyCode(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, ::std::string& realUserID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByCard(realUserID, e, _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPPowerVerify_verifyByCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_verifyByCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByCard(::std::string& realUserID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByCard(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByCard(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByAuthDev(e, _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPPowerVerify_verifyByAuthDevPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_verifyByAuthDevPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByAuthDev(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByAuthDev(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByAuthDev(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool sendVerifyCode(const ::std::string& userID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_sendVerifyCode(e, _iceI_begin_sendVerifyCode(userID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_sendVerifyCode(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_sendVerifyCode(userID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_sendVerifyCode(const ::std::string& userID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendVerifyCode(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendVerifyCode(const ::std::string& userID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendVerifyCode(userID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendVerifyCode(const ::std::string& userID, const ::ZG6000::Callback_ZGSPPowerVerify_sendVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendVerifyCode(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendVerifyCode(const ::std::string& userID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPPowerVerify_sendVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendVerifyCode(userID, context, cb, cookie);
    }

    bool end_sendVerifyCode(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_sendVerifyCode(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_sendVerifyCode(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPPowerVerify : virtual public ZGServerBase
{
public:

    typedef ZGSPPowerVerifyPrx ProxyType;
    typedef ZGSPPowerVerifyPtr PointerType;

    virtual ~ZGSPPowerVerify();

#ifdef ICE_CPP11_COMPILER
    ZGSPPowerVerify() = default;
    ZGSPPowerVerify(const ZGSPPowerVerify&) = default;
    ZGSPPowerVerify& operator=(const ZGSPPowerVerify&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool isUserHasPower(const ::std::string& userID, const ::std::string& powerID, bool& hasPower, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_isUserHasPower(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool loginByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, ::Ice::Int keepTime, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByPasswordAndVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByAuthDev(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool logout(const ::std::string& clientID, const ::std::string& userID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_logout(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, ListStringMap& lstUser, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getAvaiableUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByPasswordAndVerifyCode(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& verifyCode, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByPasswordAndVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByAuthDev(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool sendVerifyCode(const ::std::string& userID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_sendVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPPowerVerify& lhs, const ZGSPPowerVerify& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPPowerVerify& lhs, const ZGSPPowerVerify& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_isUserHasPower.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_isUserHasPower.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_isUserHasPower : public Callback_ZGSPPowerVerify_isUserHasPower_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_isUserHasPower(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        bool iceP_hasPower;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isUserHasPower(iceP_hasPower, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_hasPower, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_isUserHasPower.
 */
template<class T> Callback_ZGSPPowerVerify_isUserHasPowerPtr
newCallback_ZGSPPowerVerify_isUserHasPower(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_isUserHasPower<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_isUserHasPower.
 */
template<class T> Callback_ZGSPPowerVerify_isUserHasPowerPtr
newCallback_ZGSPPowerVerify_isUserHasPower(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_isUserHasPower<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_isUserHasPower.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_isUserHasPower.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_isUserHasPower : public Callback_ZGSPPowerVerify_isUserHasPower_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_isUserHasPower(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        bool iceP_hasPower;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isUserHasPower(iceP_hasPower, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_hasPower, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_isUserHasPower.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_isUserHasPowerPtr
newCallback_ZGSPPowerVerify_isUserHasPower(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_isUserHasPower<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_isUserHasPower.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_isUserHasPowerPtr
newCallback_ZGSPPowerVerify_isUserHasPower(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_isUserHasPower<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByPassword.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_loginByPassword : public Callback_ZGSPPowerVerify_loginByPassword_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_loginByPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByPassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPassword.
 */
template<class T> Callback_ZGSPPowerVerify_loginByPasswordPtr
newCallback_ZGSPPowerVerify_loginByPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_loginByPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPassword.
 */
template<class T> Callback_ZGSPPowerVerify_loginByPasswordPtr
newCallback_ZGSPPowerVerify_loginByPassword(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_loginByPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByPassword.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_loginByPassword : public Callback_ZGSPPowerVerify_loginByPassword_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_loginByPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByPassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPassword.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_loginByPasswordPtr
newCallback_ZGSPPowerVerify_loginByPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_loginByPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPassword.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_loginByPasswordPtr
newCallback_ZGSPPowerVerify_loginByPassword(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_loginByPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPasswordAndVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByPasswordAndVerifyCode.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_loginByPasswordAndVerifyCode : public Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCode_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_loginByPasswordAndVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByPasswordAndVerifyCode(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPasswordAndVerifyCode.
 */
template<class T> Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCodePtr
newCallback_ZGSPPowerVerify_loginByPasswordAndVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_loginByPasswordAndVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPasswordAndVerifyCode.
 */
template<class T> Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCodePtr
newCallback_ZGSPPowerVerify_loginByPasswordAndVerifyCode(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_loginByPasswordAndVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPasswordAndVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByPasswordAndVerifyCode.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCode : public Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCode_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByPasswordAndVerifyCode(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPasswordAndVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCodePtr
newCallback_ZGSPPowerVerify_loginByPasswordAndVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByPasswordAndVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCodePtr
newCallback_ZGSPPowerVerify_loginByPasswordAndVerifyCode(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_loginByPasswordAndVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByCard.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_loginByCard : public Callback_ZGSPPowerVerify_loginByCard_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_loginByCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_realUserID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByCard(iceP_realUserID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_realUserID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByCard.
 */
template<class T> Callback_ZGSPPowerVerify_loginByCardPtr
newCallback_ZGSPPowerVerify_loginByCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_loginByCard<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByCard.
 */
template<class T> Callback_ZGSPPowerVerify_loginByCardPtr
newCallback_ZGSPPowerVerify_loginByCard(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_loginByCard<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByCard.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_loginByCard : public Callback_ZGSPPowerVerify_loginByCard_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_loginByCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_realUserID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByCard(iceP_realUserID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_realUserID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByCard.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_loginByCardPtr
newCallback_ZGSPPowerVerify_loginByCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_loginByCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByCard.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_loginByCardPtr
newCallback_ZGSPPowerVerify_loginByCard(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_loginByCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByAuthDev.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_loginByAuthDev : public Callback_ZGSPPowerVerify_loginByAuthDev_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_loginByAuthDev(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByAuthDev(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByAuthDev.
 */
template<class T> Callback_ZGSPPowerVerify_loginByAuthDevPtr
newCallback_ZGSPPowerVerify_loginByAuthDev(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_loginByAuthDev<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByAuthDev.
 */
template<class T> Callback_ZGSPPowerVerify_loginByAuthDevPtr
newCallback_ZGSPPowerVerify_loginByAuthDev(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_loginByAuthDev<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_loginByAuthDev.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_loginByAuthDev : public Callback_ZGSPPowerVerify_loginByAuthDev_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_loginByAuthDev(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByAuthDev(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByAuthDev.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_loginByAuthDevPtr
newCallback_ZGSPPowerVerify_loginByAuthDev(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_loginByAuthDev<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_loginByAuthDev.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_loginByAuthDevPtr
newCallback_ZGSPPowerVerify_loginByAuthDev(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_loginByAuthDev<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_logout.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_logout.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_logout : public Callback_ZGSPPowerVerify_logout_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_logout(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_logout(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_logout.
 */
template<class T> Callback_ZGSPPowerVerify_logoutPtr
newCallback_ZGSPPowerVerify_logout(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_logout<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_logout.
 */
template<class T> Callback_ZGSPPowerVerify_logoutPtr
newCallback_ZGSPPowerVerify_logout(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_logout<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_logout.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_logout.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_logout : public Callback_ZGSPPowerVerify_logout_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_logout(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_logout(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_logout.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_logoutPtr
newCallback_ZGSPPowerVerify_logout(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_logout<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_logout.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_logoutPtr
newCallback_ZGSPPowerVerify_logout(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_logout<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_getAvaiableUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_getAvaiableUser.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_getAvaiableUser : public Callback_ZGSPPowerVerify_getAvaiableUser_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_getAvaiableUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_lstUser;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getAvaiableUser(iceP_lstUser, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_lstUser, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_getAvaiableUser.
 */
template<class T> Callback_ZGSPPowerVerify_getAvaiableUserPtr
newCallback_ZGSPPowerVerify_getAvaiableUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_getAvaiableUser<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_getAvaiableUser.
 */
template<class T> Callback_ZGSPPowerVerify_getAvaiableUserPtr
newCallback_ZGSPPowerVerify_getAvaiableUser(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_getAvaiableUser<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_getAvaiableUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_getAvaiableUser.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_getAvaiableUser : public Callback_ZGSPPowerVerify_getAvaiableUser_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_getAvaiableUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_lstUser;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getAvaiableUser(iceP_lstUser, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_lstUser, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_getAvaiableUser.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_getAvaiableUserPtr
newCallback_ZGSPPowerVerify_getAvaiableUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_getAvaiableUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_getAvaiableUser.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_getAvaiableUserPtr
newCallback_ZGSPPowerVerify_getAvaiableUser(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_getAvaiableUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByPassword.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_verifyByPassword : public Callback_ZGSPPowerVerify_verifyByPassword_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_verifyByPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByPassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPassword.
 */
template<class T> Callback_ZGSPPowerVerify_verifyByPasswordPtr
newCallback_ZGSPPowerVerify_verifyByPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_verifyByPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPassword.
 */
template<class T> Callback_ZGSPPowerVerify_verifyByPasswordPtr
newCallback_ZGSPPowerVerify_verifyByPassword(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_verifyByPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByPassword.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_verifyByPassword : public Callback_ZGSPPowerVerify_verifyByPassword_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_verifyByPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByPassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPassword.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_verifyByPasswordPtr
newCallback_ZGSPPowerVerify_verifyByPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_verifyByPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPassword.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_verifyByPasswordPtr
newCallback_ZGSPPowerVerify_verifyByPassword(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_verifyByPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPasswordAndVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_verifyByPasswordAndVerifyCode : public Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_verifyByPasswordAndVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByPasswordAndVerifyCode(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPasswordAndVerifyCode.
 */
template<class T> Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCodePtr
newCallback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_verifyByPasswordAndVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPasswordAndVerifyCode.
 */
template<class T> Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCodePtr
newCallback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_verifyByPasswordAndVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPasswordAndVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode : public Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByPasswordAndVerifyCode(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPasswordAndVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCodePtr
newCallback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByPasswordAndVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCodePtr
newCallback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_verifyByPasswordAndVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByCard.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_verifyByCard : public Callback_ZGSPPowerVerify_verifyByCard_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_verifyByCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_realUserID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByCard(iceP_realUserID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_realUserID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByCard.
 */
template<class T> Callback_ZGSPPowerVerify_verifyByCardPtr
newCallback_ZGSPPowerVerify_verifyByCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_verifyByCard<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByCard.
 */
template<class T> Callback_ZGSPPowerVerify_verifyByCardPtr
newCallback_ZGSPPowerVerify_verifyByCard(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_verifyByCard<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByCard.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_verifyByCard : public Callback_ZGSPPowerVerify_verifyByCard_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_verifyByCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_realUserID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByCard(iceP_realUserID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_realUserID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByCard.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_verifyByCardPtr
newCallback_ZGSPPowerVerify_verifyByCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_verifyByCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByCard.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_verifyByCardPtr
newCallback_ZGSPPowerVerify_verifyByCard(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_verifyByCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByAuthDev.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_verifyByAuthDev : public Callback_ZGSPPowerVerify_verifyByAuthDev_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_verifyByAuthDev(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByAuthDev(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByAuthDev.
 */
template<class T> Callback_ZGSPPowerVerify_verifyByAuthDevPtr
newCallback_ZGSPPowerVerify_verifyByAuthDev(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_verifyByAuthDev<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByAuthDev.
 */
template<class T> Callback_ZGSPPowerVerify_verifyByAuthDevPtr
newCallback_ZGSPPowerVerify_verifyByAuthDev(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_verifyByAuthDev<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_verifyByAuthDev.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_verifyByAuthDev : public Callback_ZGSPPowerVerify_verifyByAuthDev_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_verifyByAuthDev(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByAuthDev(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByAuthDev.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_verifyByAuthDevPtr
newCallback_ZGSPPowerVerify_verifyByAuthDev(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_verifyByAuthDev<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_verifyByAuthDev.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_verifyByAuthDevPtr
newCallback_ZGSPPowerVerify_verifyByAuthDev(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_verifyByAuthDev<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_sendVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_sendVerifyCode.
 */
template<class T>
class CallbackNC_ZGSPPowerVerify_sendVerifyCode : public Callback_ZGSPPowerVerify_sendVerifyCode_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPPowerVerify_sendVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_sendVerifyCode(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_sendVerifyCode.
 */
template<class T> Callback_ZGSPPowerVerify_sendVerifyCodePtr
newCallback_ZGSPPowerVerify_sendVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_sendVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_sendVerifyCode.
 */
template<class T> Callback_ZGSPPowerVerify_sendVerifyCodePtr
newCallback_ZGSPPowerVerify_sendVerifyCode(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPPowerVerify_sendVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPPowerVerify::begin_sendVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPPowerVerify_sendVerifyCode.
 */
template<class T, typename CT>
class Callback_ZGSPPowerVerify_sendVerifyCode : public Callback_ZGSPPowerVerify_sendVerifyCode_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPPowerVerify_sendVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPPowerVerifyPrx proxy = ZGSPPowerVerifyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_sendVerifyCode(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_sendVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_sendVerifyCodePtr
newCallback_ZGSPPowerVerify_sendVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_sendVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPPowerVerify::begin_sendVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPPowerVerify_sendVerifyCodePtr
newCallback_ZGSPPowerVerify_sendVerifyCode(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPPowerVerify_sendVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

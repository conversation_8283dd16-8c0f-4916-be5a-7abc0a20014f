//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPEventProcess.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSPEventProcess.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPEventProcess_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPEventProcess",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSPEventProcess_ops[] =
{
    "checkState",
    "confirmDPEvent",
    "confirmEvents",
    "dispatchData",
    "exitApp",
    "getUnconfirmedEvent",
    "getUnconfirmedEventNum",
    "getUnconfirmedPosEvent",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "processEvent",
    "processZGDPEvents",
    "processZGMPDatasetEvents",
    "processZGMPEvents",
    "processZGOPEvents",
    "processZGSPEvents",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGSPEventProcess_processZGMPEvents_name = "processZGMPEvents";
const ::std::string iceC_ZG6000_ZGSPEventProcess_processZGMPDatasetEvents_name = "processZGMPDatasetEvents";
const ::std::string iceC_ZG6000_ZGSPEventProcess_processZGSPEvents_name = "processZGSPEvents";
const ::std::string iceC_ZG6000_ZGSPEventProcess_processZGDPEvents_name = "processZGDPEvents";
const ::std::string iceC_ZG6000_ZGSPEventProcess_processZGOPEvents_name = "processZGOPEvents";
const ::std::string iceC_ZG6000_ZGSPEventProcess_processEvent_name = "processEvent";
const ::std::string iceC_ZG6000_ZGSPEventProcess_confirmEvents_name = "confirmEvents";
const ::std::string iceC_ZG6000_ZGSPEventProcess_confirmDPEvent_name = "confirmDPEvent";
const ::std::string iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEvent_name = "getUnconfirmedEvent";
const ::std::string iceC_ZG6000_ZGSPEventProcess_getUnconfirmedPosEvent_name = "getUnconfirmedPosEvent";
const ::std::string iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEventNum_name = "getUnconfirmedEventNum";

}

bool
ZG6000::ZGSPEventProcess::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPEventProcess_ids, iceC_ZG6000_ZGSPEventProcess_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSPEventProcess::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSPEventProcess_ids[0], &iceC_ZG6000_ZGSPEventProcess_ids[3]);
}

::std::string
ZG6000::ZGSPEventProcess::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPEventProcess::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSPEventProcess";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processZGMPEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    ListStringList iceP_listListAppNodeID;
    StringList iceP_listIsPublishEvent;
    istr->readAll(iceP_listEvent, iceP_listListAppNodeID, iceP_listIsPublishEvent);
    inS.endReadParams();
    this->processZGMPEvents(::std::move(iceP_listEvent), ::std::move(iceP_listListAppNodeID), ::std::move(iceP_listIsPublishEvent), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processZGMPDatasetEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    StringList iceP_listAppNodeID;
    StringList iceP_listIsPublishEvent;
    istr->readAll(iceP_listEvent, iceP_listAppNodeID, iceP_listIsPublishEvent);
    inS.endReadParams();
    this->processZGMPDatasetEvents(::std::move(iceP_listEvent), ::std::move(iceP_listAppNodeID), ::std::move(iceP_listIsPublishEvent), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processZGSPEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    StringList iceP_listIsPublishEvent;
    istr->readAll(iceP_listEvent, iceP_listIsPublishEvent);
    inS.endReadParams();
    this->processZGSPEvents(::std::move(iceP_listEvent), ::std::move(iceP_listIsPublishEvent), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processZGDPEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    StringList iceP_listIsPublishEvent;
    istr->readAll(iceP_listEvent, iceP_listIsPublishEvent);
    inS.endReadParams();
    this->processZGDPEvents(::std::move(iceP_listEvent), ::std::move(iceP_listIsPublishEvent), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processZGOPEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    StringList iceP_listIsPublishEvent;
    istr->readAll(iceP_listEvent, iceP_listIsPublishEvent);
    inS.endReadParams();
    this->processZGOPEvents(::std::move(iceP_listEvent), ::std::move(iceP_listIsPublishEvent), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processEvent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_event;
    istr->readAll(iceP_event);
    inS.endReadParams();
    this->processEvent(::std::move(iceP_event), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_confirmEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    ::std::string iceP_userID;
    ::std::string iceP_userName;
    istr->readAll(iceP_listEvent, iceP_userID, iceP_userName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmEvents(::std::move(iceP_listEvent), ::std::move(iceP_userID), ::std::move(iceP_userName), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_confirmDPEvent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    ::std::string iceP_userID;
    ::std::string iceP_userName;
    istr->readAll(iceP_listEvent, iceP_userID, iceP_userName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmDPEvent(::std::move(iceP_listEvent), ::std::move(iceP_userID), ::std::move(iceP_userName), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_getUnconfirmedEvent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    ::std::string iceP_subsystemID;
    istr->readAll(iceP_appNodeID, iceP_subsystemID);
    inS.endReadParams();
    ListStringMap iceP_listEvent;
    ErrorInfo iceP_e;
    bool ret = this->getUnconfirmedEvent(::std::move(iceP_appNodeID), ::std::move(iceP_subsystemID), iceP_listEvent, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listEvent, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_getUnconfirmedPosEvent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    ::std::string iceP_subsystemID;
    ::std::string iceP_position;
    istr->readAll(iceP_appNodeID, iceP_subsystemID, iceP_position);
    inS.endReadParams();
    ListStringMap iceP_listEvent;
    ErrorInfo iceP_e;
    bool ret = this->getUnconfirmedPosEvent(::std::move(iceP_appNodeID), ::std::move(iceP_subsystemID), ::std::move(iceP_position), iceP_listEvent, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listEvent, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_getUnconfirmedEventNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    StringMap iceP_appNodeEventNum;
    ErrorInfo iceP_e;
    bool ret = this->getUnconfirmedEventNum(iceP_appNodeEventNum, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_appNodeEventNum, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPEventProcess_ops, iceC_ZG6000_ZGSPEventProcess_ops + 26, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPEventProcess_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_confirmDPEvent(in, current);
        }
        case 2:
        {
            return _iceD_confirmEvents(in, current);
        }
        case 3:
        {
            return _iceD_dispatchData(in, current);
        }
        case 4:
        {
            return _iceD_exitApp(in, current);
        }
        case 5:
        {
            return _iceD_getUnconfirmedEvent(in, current);
        }
        case 6:
        {
            return _iceD_getUnconfirmedEventNum(in, current);
        }
        case 7:
        {
            return _iceD_getUnconfirmedPosEvent(in, current);
        }
        case 8:
        {
            return _iceD_getVersion(in, current);
        }
        case 9:
        {
            return _iceD_heartDebug(in, current);
        }
        case 10:
        {
            return _iceD_ice_id(in, current);
        }
        case 11:
        {
            return _iceD_ice_ids(in, current);
        }
        case 12:
        {
            return _iceD_ice_isA(in, current);
        }
        case 13:
        {
            return _iceD_ice_ping(in, current);
        }
        case 14:
        {
            return _iceD_isDebugging(in, current);
        }
        case 15:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 16:
        {
            return _iceD_processEvent(in, current);
        }
        case 17:
        {
            return _iceD_processZGDPEvents(in, current);
        }
        case 18:
        {
            return _iceD_processZGMPDatasetEvents(in, current);
        }
        case 19:
        {
            return _iceD_processZGMPEvents(in, current);
        }
        case 20:
        {
            return _iceD_processZGOPEvents(in, current);
        }
        case 21:
        {
            return _iceD_processZGSPEvents(in, current);
        }
        case 22:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 23:
        {
            return _iceD_startDebug(in, current);
        }
        case 24:
        {
            return _iceD_stopDebug(in, current);
        }
        case 25:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_processZGMPEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ListStringMap& iceP_listEvent, const ListStringList& iceP_listListAppNodeID, const StringList& iceP_listIsPublishEvent, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_processZGMPEvents_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listEvent, iceP_listListAppNodeID, iceP_listIsPublishEvent);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_processZGMPDatasetEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ListStringMap& iceP_listEvent, const StringList& iceP_listAppNodeID, const StringList& iceP_listIsPublishEvent, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_processZGMPDatasetEvents_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listEvent, iceP_listAppNodeID, iceP_listIsPublishEvent);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_processZGSPEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ListStringMap& iceP_listEvent, const StringList& iceP_listIsPublishEvent, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_processZGSPEvents_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listEvent, iceP_listIsPublishEvent);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_processZGDPEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ListStringMap& iceP_listEvent, const StringList& iceP_listIsPublishEvent, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_processZGDPEvents_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listEvent, iceP_listIsPublishEvent);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_processZGOPEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ListStringMap& iceP_listEvent, const StringList& iceP_listIsPublishEvent, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_processZGOPEvents_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listEvent, iceP_listIsPublishEvent);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_processEvent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const StringMap& iceP_event, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_processEvent_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_event);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_confirmEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPEventProcess::ConfirmEventsResult>>& outAsync, const ListStringMap& iceP_listEvent, const ::std::string& iceP_userID, const ::std::string& iceP_userName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPEventProcess_confirmEvents_name);
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_confirmEvents_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listEvent, iceP_userID, iceP_userName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPEventProcess::ConfirmEventsResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_confirmDPEvent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPEventProcess::ConfirmDPEventResult>>& outAsync, const ListStringMap& iceP_listEvent, const ::std::string& iceP_userID, const ::std::string& iceP_userName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPEventProcess_confirmDPEvent_name);
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_confirmDPEvent_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listEvent, iceP_userID, iceP_userName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPEventProcess::ConfirmDPEventResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_getUnconfirmedEvent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPEventProcess::GetUnconfirmedEventResult>>& outAsync, const ::std::string& iceP_appNodeID, const ::std::string& iceP_subsystemID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEvent_name);
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEvent_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_appNodeID, iceP_subsystemID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPEventProcess::GetUnconfirmedEventResult v;
            istr->readAll(v.listEvent, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_getUnconfirmedPosEvent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPEventProcess::GetUnconfirmedPosEventResult>>& outAsync, const ::std::string& iceP_appNodeID, const ::std::string& iceP_subsystemID, const ::std::string& iceP_position, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedPosEvent_name);
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedPosEvent_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_appNodeID, iceP_subsystemID, iceP_position);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPEventProcess::GetUnconfirmedPosEventResult v;
            istr->readAll(v.listEvent, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPEventProcessPrx::_iceI_getUnconfirmedEventNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPEventProcess::GetUnconfirmedEventNumResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEventNum_name);
    outAsync->invoke(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEventNum_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPEventProcess::GetUnconfirmedEventNumResult v;
            istr->readAll(v.appNodeEventNum, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSPEventProcessPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSPEventProcessPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSPEventProcessPrx::ice_staticId()
{
    return ZGSPEventProcess::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPEventProcess_processZGMPEvents_name = "processZGMPEvents";

const ::std::string iceC_ZG6000_ZGSPEventProcess_processZGMPDatasetEvents_name = "processZGMPDatasetEvents";

const ::std::string iceC_ZG6000_ZGSPEventProcess_processZGSPEvents_name = "processZGSPEvents";

const ::std::string iceC_ZG6000_ZGSPEventProcess_processZGDPEvents_name = "processZGDPEvents";

const ::std::string iceC_ZG6000_ZGSPEventProcess_processZGOPEvents_name = "processZGOPEvents";

const ::std::string iceC_ZG6000_ZGSPEventProcess_processEvent_name = "processEvent";

const ::std::string iceC_ZG6000_ZGSPEventProcess_confirmEvents_name = "confirmEvents";

const ::std::string iceC_ZG6000_ZGSPEventProcess_confirmDPEvent_name = "confirmDPEvent";

const ::std::string iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEvent_name = "getUnconfirmedEvent";

const ::std::string iceC_ZG6000_ZGSPEventProcess_getUnconfirmedPosEvent_name = "getUnconfirmedPosEvent";

const ::std::string iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEventNum_name = "getUnconfirmedEventNum";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSPEventProcess* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSPEventProcess>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSPEventProcess;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_processZGMPEvents(const ::ZG6000::ListStringMap& iceP_listEvent, const ::ZG6000::ListStringList& iceP_listListAppNodeID, const ::ZG6000::StringList& iceP_listIsPublishEvent, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_processZGMPEvents_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_processZGMPEvents_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listEvent);
        ostr->write(iceP_listListAppNodeID);
        ostr->write(iceP_listIsPublishEvent);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_processZGMPEvents_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPEventProcess::end_processZGMPEvents(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPEventProcess_processZGMPEvents_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_processZGMPDatasetEvents(const ::ZG6000::ListStringMap& iceP_listEvent, const ::ZG6000::StringList& iceP_listAppNodeID, const ::ZG6000::StringList& iceP_listIsPublishEvent, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_processZGMPDatasetEvents_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_processZGMPDatasetEvents_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listEvent);
        ostr->write(iceP_listAppNodeID);
        ostr->write(iceP_listIsPublishEvent);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_processZGMPDatasetEvents_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPEventProcess::end_processZGMPDatasetEvents(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPEventProcess_processZGMPDatasetEvents_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_processZGSPEvents(const ::ZG6000::ListStringMap& iceP_listEvent, const ::ZG6000::StringList& iceP_listIsPublishEvent, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_processZGSPEvents_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_processZGSPEvents_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listEvent);
        ostr->write(iceP_listIsPublishEvent);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_processZGSPEvents_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPEventProcess::end_processZGSPEvents(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPEventProcess_processZGSPEvents_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_processZGDPEvents(const ::ZG6000::ListStringMap& iceP_listEvent, const ::ZG6000::StringList& iceP_listIsPublishEvent, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_processZGDPEvents_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_processZGDPEvents_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listEvent);
        ostr->write(iceP_listIsPublishEvent);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_processZGDPEvents_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPEventProcess::end_processZGDPEvents(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPEventProcess_processZGDPEvents_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_processZGOPEvents(const ::ZG6000::ListStringMap& iceP_listEvent, const ::ZG6000::StringList& iceP_listIsPublishEvent, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_processZGOPEvents_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_processZGOPEvents_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listEvent);
        ostr->write(iceP_listIsPublishEvent);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_processZGOPEvents_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPEventProcess::end_processZGOPEvents(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPEventProcess_processZGOPEvents_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_processEvent(const ::ZG6000::StringMap& iceP_event, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_processEvent_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_processEvent_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_event);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_processEvent_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPEventProcess::end_processEvent(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPEventProcess_processEvent_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_confirmEvents(const ::ZG6000::ListStringMap& iceP_listEvent, const ::std::string& iceP_userID, const ::std::string& iceP_userName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPEventProcess_confirmEvents_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_confirmEvents_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_confirmEvents_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listEvent);
        ostr->write(iceP_userID);
        ostr->write(iceP_userName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_confirmEvents_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPEventProcess::end_confirmEvents(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPEventProcess_confirmEvents_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPEventProcess::_iceI_end_confirmEvents(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPEventProcess_confirmEvents_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_confirmDPEvent(const ::ZG6000::ListStringMap& iceP_listEvent, const ::std::string& iceP_userID, const ::std::string& iceP_userName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPEventProcess_confirmDPEvent_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_confirmDPEvent_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_confirmDPEvent_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listEvent);
        ostr->write(iceP_userID);
        ostr->write(iceP_userName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_confirmDPEvent_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPEventProcess::end_confirmDPEvent(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPEventProcess_confirmDPEvent_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPEventProcess::_iceI_end_confirmDPEvent(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPEventProcess_confirmDPEvent_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_getUnconfirmedEvent(const ::std::string& iceP_appNodeID, const ::std::string& iceP_subsystemID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEvent_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEvent_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEvent_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_subsystemID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEvent_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPEventProcess::end_getUnconfirmedEvent(::ZG6000::ListStringMap& iceP_listEvent, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEvent_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listEvent);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPEventProcess::_iceI_end_getUnconfirmedEvent(::ZG6000::ListStringMap& iceP_listEvent, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEvent_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listEvent);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_getUnconfirmedPosEvent(const ::std::string& iceP_appNodeID, const ::std::string& iceP_subsystemID, const ::std::string& iceP_position, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedPosEvent_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_getUnconfirmedPosEvent_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedPosEvent_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_subsystemID);
        ostr->write(iceP_position);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedPosEvent_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPEventProcess::end_getUnconfirmedPosEvent(::ZG6000::ListStringMap& iceP_listEvent, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPEventProcess_getUnconfirmedPosEvent_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listEvent);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPEventProcess::_iceI_end_getUnconfirmedPosEvent(::ZG6000::ListStringMap& iceP_listEvent, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPEventProcess_getUnconfirmedPosEvent_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listEvent);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPEventProcess::_iceI_begin_getUnconfirmedEventNum(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEventNum_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEventNum_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEventNum_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEventNum_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPEventProcess::end_getUnconfirmedEventNum(::ZG6000::StringMap& iceP_appNodeEventNum, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEventNum_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_appNodeEventNum);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPEventProcess::_iceI_end_getUnconfirmedEventNum(::ZG6000::StringMap& iceP_appNodeEventNum, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPEventProcess_getUnconfirmedEventNum_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_appNodeEventNum);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSPEventProcess::_newInstance() const
{
    return new ZGSPEventProcess;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSPEventProcess::ice_staticId()
{
    return ::ZG6000::ZGSPEventProcess::ice_staticId();
}

ZG6000::ZGSPEventProcess::~ZGSPEventProcess()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSPEventProcess* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPEventProcess_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPEventProcess",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSPEventProcess::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPEventProcess_ids, iceC_ZG6000_ZGSPEventProcess_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSPEventProcess::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSPEventProcess_ids[0], &iceC_ZG6000_ZGSPEventProcess_ids[3]);
}

const ::std::string&
ZG6000::ZGSPEventProcess::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPEventProcess::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSPEventProcess";
    return typeId;
#else
    return iceC_ZG6000_ZGSPEventProcess_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processZGMPEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    ListStringList iceP_listListAppNodeID;
    StringList iceP_listIsPublishEvent;
    istr->read(iceP_listEvent);
    istr->read(iceP_listListAppNodeID);
    istr->read(iceP_listIsPublishEvent);
    inS.endReadParams();
    this->processZGMPEvents(iceP_listEvent, iceP_listListAppNodeID, iceP_listIsPublishEvent, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processZGMPDatasetEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    StringList iceP_listAppNodeID;
    StringList iceP_listIsPublishEvent;
    istr->read(iceP_listEvent);
    istr->read(iceP_listAppNodeID);
    istr->read(iceP_listIsPublishEvent);
    inS.endReadParams();
    this->processZGMPDatasetEvents(iceP_listEvent, iceP_listAppNodeID, iceP_listIsPublishEvent, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processZGSPEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    StringList iceP_listIsPublishEvent;
    istr->read(iceP_listEvent);
    istr->read(iceP_listIsPublishEvent);
    inS.endReadParams();
    this->processZGSPEvents(iceP_listEvent, iceP_listIsPublishEvent, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processZGDPEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    StringList iceP_listIsPublishEvent;
    istr->read(iceP_listEvent);
    istr->read(iceP_listIsPublishEvent);
    inS.endReadParams();
    this->processZGDPEvents(iceP_listEvent, iceP_listIsPublishEvent, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processZGOPEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    StringList iceP_listIsPublishEvent;
    istr->read(iceP_listEvent);
    istr->read(iceP_listIsPublishEvent);
    inS.endReadParams();
    this->processZGOPEvents(iceP_listEvent, iceP_listIsPublishEvent, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_processEvent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_event;
    istr->read(iceP_event);
    inS.endReadParams();
    this->processEvent(iceP_event, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_confirmEvents(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    ::std::string iceP_userID;
    ::std::string iceP_userName;
    istr->read(iceP_listEvent);
    istr->read(iceP_userID);
    istr->read(iceP_userName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmEvents(iceP_listEvent, iceP_userID, iceP_userName, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_confirmDPEvent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listEvent;
    ::std::string iceP_userID;
    ::std::string iceP_userName;
    istr->read(iceP_listEvent);
    istr->read(iceP_userID);
    istr->read(iceP_userName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmDPEvent(iceP_listEvent, iceP_userID, iceP_userName, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_getUnconfirmedEvent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    ::std::string iceP_subsystemID;
    istr->read(iceP_appNodeID);
    istr->read(iceP_subsystemID);
    inS.endReadParams();
    ListStringMap iceP_listEvent;
    ErrorInfo iceP_e;
    bool ret = this->getUnconfirmedEvent(iceP_appNodeID, iceP_subsystemID, iceP_listEvent, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listEvent);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_getUnconfirmedPosEvent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    ::std::string iceP_subsystemID;
    ::std::string iceP_position;
    istr->read(iceP_appNodeID);
    istr->read(iceP_subsystemID);
    istr->read(iceP_position);
    inS.endReadParams();
    ListStringMap iceP_listEvent;
    ErrorInfo iceP_e;
    bool ret = this->getUnconfirmedPosEvent(iceP_appNodeID, iceP_subsystemID, iceP_position, iceP_listEvent, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listEvent);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceD_getUnconfirmedEventNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    StringMap iceP_appNodeEventNum;
    ErrorInfo iceP_e;
    bool ret = this->getUnconfirmedEventNum(iceP_appNodeEventNum, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_appNodeEventNum);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPEventProcess_all[] =
{
    "checkState",
    "confirmDPEvent",
    "confirmEvents",
    "dispatchData",
    "exitApp",
    "getUnconfirmedEvent",
    "getUnconfirmedEventNum",
    "getUnconfirmedPosEvent",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "processEvent",
    "processZGDPEvents",
    "processZGMPDatasetEvents",
    "processZGMPEvents",
    "processZGOPEvents",
    "processZGSPEvents",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSPEventProcess::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPEventProcess_all, iceC_ZG6000_ZGSPEventProcess_all + 26, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPEventProcess_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_confirmDPEvent(in, current);
        }
        case 2:
        {
            return _iceD_confirmEvents(in, current);
        }
        case 3:
        {
            return _iceD_dispatchData(in, current);
        }
        case 4:
        {
            return _iceD_exitApp(in, current);
        }
        case 5:
        {
            return _iceD_getUnconfirmedEvent(in, current);
        }
        case 6:
        {
            return _iceD_getUnconfirmedEventNum(in, current);
        }
        case 7:
        {
            return _iceD_getUnconfirmedPosEvent(in, current);
        }
        case 8:
        {
            return _iceD_getVersion(in, current);
        }
        case 9:
        {
            return _iceD_heartDebug(in, current);
        }
        case 10:
        {
            return _iceD_ice_id(in, current);
        }
        case 11:
        {
            return _iceD_ice_ids(in, current);
        }
        case 12:
        {
            return _iceD_ice_isA(in, current);
        }
        case 13:
        {
            return _iceD_ice_ping(in, current);
        }
        case 14:
        {
            return _iceD_isDebugging(in, current);
        }
        case 15:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 16:
        {
            return _iceD_processEvent(in, current);
        }
        case 17:
        {
            return _iceD_processZGDPEvents(in, current);
        }
        case 18:
        {
            return _iceD_processZGMPDatasetEvents(in, current);
        }
        case 19:
        {
            return _iceD_processZGMPEvents(in, current);
        }
        case 20:
        {
            return _iceD_processZGOPEvents(in, current);
        }
        case 21:
        {
            return _iceD_processZGSPEvents(in, current);
        }
        case 22:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 23:
        {
            return _iceD_startDebug(in, current);
        }
        case 24:
        {
            return _iceD_stopDebug(in, current);
        }
        case 25:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSPEventProcess::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSPEventProcess, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSPEventProcess::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSPEventProcess, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSPEventProcessPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSPEventProcessPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSPEventProcess::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPRTData.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSPRTData.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPRTData_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPRTData",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSPRTData_ops[] =
{
    "checkState",
    "deleteDataByField",
    "deleteDataByFields",
    "deleteDataByID",
    "deleteDataByIDOneway",
    "dispatchData",
    "exitApp",
    "getDataByFieldToJson",
    "getDataByFieldToValue",
    "getDataByFieldsToJson",
    "getDataByFieldsToList",
    "getDataByFieldsToMap",
    "getDataByIDToJson",
    "getDataByIDToMap",
    "getDataByKeyToJson",
    "getDataByKeyToValue",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "insertDataByFieldFromValue",
    "insertDataByFieldFromValueOneway",
    "insertDataByIDFromMap",
    "insertDataByIDFromMapOneway",
    "isDebugging",
    "mdeleteDataByField",
    "mdeleteDataByFields",
    "mdeleteDataByID",
    "mdeleteDataByIDOneway",
    "mgetDataByFieldToJson",
    "mgetDataByFieldToList",
    "mgetDataByFieldToMap",
    "mgetDataByFieldsToJson",
    "mgetDataByFieldsToListList",
    "mgetDataByFieldsToListMap",
    "mgetDataByFieldsToMapMap",
    "mgetDataByIDToJson",
    "mgetDataByIDToListList",
    "mgetDataByIDToListMap",
    "mgetDataByIDToMapMap",
    "mgetDataByKeyToJson",
    "mgetDataByKeyToList",
    "mgetDataByKeyToMap",
    "minsertDataByFieldFromList",
    "minsertDataByFieldFromListOneway",
    "minsertDataByFieldsFromListMap",
    "minsertDataByFieldsFromListMapOneway",
    "msetDataByKeyFromList",
    "msetDataByKeyFromListOneway",
    "msetDataByKeyFromMap",
    "msetDataByKeyFromMapOneway",
    "mupdateDataByFieldFromList",
    "mupdateDataByFieldFromListOneway",
    "mupdateDataByFieldFromValue",
    "mupdateDataByFieldFromValueOneway",
    "mupdateDataByFieldsFromListMap",
    "mupdateDataByFieldsFromListMapOneway",
    "mupdateDataByFieldsFromMap",
    "mupdateDataByFieldsFromMapOneway",
    "mupdateDataID",
    "mupdateDataIDOneway",
    "mupdateValueByFieldsFromListList",
    "mupdateValueByFieldsFromListListOneway",
    "pauseDebug",
    "resumeDebug",
    "setDataByKeyFromValue",
    "setDataByKeyFromValueOneway",
    "startDebug",
    "stopDebug",
    "test",
    "updateDataByFieldFromValue",
    "updateDataByFieldFromValueOneway",
    "updateDataByIDFromMap",
    "updateDataByIDFromMapOneway",
    "updateDataID",
    "updateDataIDOneway"
};
const ::std::string iceC_ZG6000_ZGSPRTData_getDataByKeyToValue_name = "getDataByKeyToValue";
const ::std::string iceC_ZG6000_ZGSPRTData_getDataByKeyToJson_name = "getDataByKeyToJson";
const ::std::string iceC_ZG6000_ZGSPRTData_setDataByKeyFromValue_name = "setDataByKeyFromValue";
const ::std::string iceC_ZG6000_ZGSPRTData_setDataByKeyFromValueOneway_name = "setDataByKeyFromValueOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByKeyToList_name = "mgetDataByKeyToList";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByKeyToMap_name = "mgetDataByKeyToMap";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByKeyToJson_name = "mgetDataByKeyToJson";
const ::std::string iceC_ZG6000_ZGSPRTData_msetDataByKeyFromList_name = "msetDataByKeyFromList";
const ::std::string iceC_ZG6000_ZGSPRTData_msetDataByKeyFromListOneway_name = "msetDataByKeyFromListOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMap_name = "msetDataByKeyFromMap";
const ::std::string iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMapOneway_name = "msetDataByKeyFromMapOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_getDataByIDToMap_name = "getDataByIDToMap";
const ::std::string iceC_ZG6000_ZGSPRTData_getDataByIDToJson_name = "getDataByIDToJson";
const ::std::string iceC_ZG6000_ZGSPRTData_getDataByFieldToValue_name = "getDataByFieldToValue";
const ::std::string iceC_ZG6000_ZGSPRTData_getDataByFieldToJson_name = "getDataByFieldToJson";
const ::std::string iceC_ZG6000_ZGSPRTData_getDataByFieldsToList_name = "getDataByFieldsToList";
const ::std::string iceC_ZG6000_ZGSPRTData_getDataByFieldsToMap_name = "getDataByFieldsToMap";
const ::std::string iceC_ZG6000_ZGSPRTData_getDataByFieldsToJson_name = "getDataByFieldsToJson";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByIDToListList_name = "mgetDataByIDToListList";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByIDToJson_name = "mgetDataByIDToJson";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByIDToListMap_name = "mgetDataByIDToListMap";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByIDToMapMap_name = "mgetDataByIDToMapMap";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldToList_name = "mgetDataByFieldToList";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldToMap_name = "mgetDataByFieldToMap";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldToJson_name = "mgetDataByFieldToJson";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListList_name = "mgetDataByFieldsToListList";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToJson_name = "mgetDataByFieldsToJson";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListMap_name = "mgetDataByFieldsToListMap";
const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToMapMap_name = "mgetDataByFieldsToMapMap";
const ::std::string iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValue_name = "updateDataByFieldFromValue";
const ::std::string iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValueOneway_name = "updateDataByFieldFromValueOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_updateDataByIDFromMap_name = "updateDataByIDFromMap";
const ::std::string iceC_ZG6000_ZGSPRTData_updateDataByIDFromMapOneway_name = "updateDataByIDFromMapOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromList_name = "mupdateDataByFieldFromList";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromListOneway_name = "mupdateDataByFieldFromListOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValue_name = "mupdateDataByFieldFromValue";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValueOneway_name = "mupdateDataByFieldFromValueOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListList_name = "mupdateValueByFieldsFromListList";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListListOneway_name = "mupdateValueByFieldsFromListListOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMap_name = "mupdateDataByFieldsFromListMap";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMapOneway_name = "mupdateDataByFieldsFromListMapOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMap_name = "mupdateDataByFieldsFromMap";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMapOneway_name = "mupdateDataByFieldsFromMapOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_updateDataID_name = "updateDataID";
const ::std::string iceC_ZG6000_ZGSPRTData_updateDataIDOneway_name = "updateDataIDOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataID_name = "mupdateDataID";
const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataIDOneway_name = "mupdateDataIDOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValue_name = "insertDataByFieldFromValue";
const ::std::string iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValueOneway_name = "insertDataByFieldFromValueOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_insertDataByIDFromMap_name = "insertDataByIDFromMap";
const ::std::string iceC_ZG6000_ZGSPRTData_insertDataByIDFromMapOneway_name = "insertDataByIDFromMapOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromList_name = "minsertDataByFieldFromList";
const ::std::string iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromListOneway_name = "minsertDataByFieldFromListOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMap_name = "minsertDataByFieldsFromListMap";
const ::std::string iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMapOneway_name = "minsertDataByFieldsFromListMapOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_deleteDataByID_name = "deleteDataByID";
const ::std::string iceC_ZG6000_ZGSPRTData_deleteDataByIDOneway_name = "deleteDataByIDOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_mdeleteDataByID_name = "mdeleteDataByID";
const ::std::string iceC_ZG6000_ZGSPRTData_mdeleteDataByIDOneway_name = "mdeleteDataByIDOneway";
const ::std::string iceC_ZG6000_ZGSPRTData_deleteDataByField_name = "deleteDataByField";
const ::std::string iceC_ZG6000_ZGSPRTData_deleteDataByFields_name = "deleteDataByFields";
const ::std::string iceC_ZG6000_ZGSPRTData_mdeleteDataByField_name = "mdeleteDataByField";
const ::std::string iceC_ZG6000_ZGSPRTData_mdeleteDataByFields_name = "mdeleteDataByFields";

}

bool
ZG6000::ZGSPRTData::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPRTData_ids, iceC_ZG6000_ZGSPRTData_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSPRTData::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSPRTData_ids[0], &iceC_ZG6000_ZGSPRTData_ids[3]);
}

::std::string
ZG6000::ZGSPRTData::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPRTData::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSPRTData";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByKeyToValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_key;
    istr->readAll(iceP_key);
    inS.endReadParams();
    ::std::string iceP_value;
    ErrorInfo iceP_e;
    bool ret = this->getDataByKeyToValue(::std::move(iceP_key), iceP_value, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_value, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByKeyToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_key;
    istr->readAll(iceP_key);
    inS.endReadParams();
    ::std::string iceP_jsonValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByKeyToJson(::std::move(iceP_key), iceP_jsonValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_jsonValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_setDataByKeyFromValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_key;
    ::std::string iceP_value;
    istr->readAll(iceP_key, iceP_value);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setDataByKeyFromValue(::std::move(iceP_key), ::std::move(iceP_value), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_setDataByKeyFromValueOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_key;
    ::std::string iceP_value;
    istr->readAll(iceP_key, iceP_value);
    inS.endReadParams();
    this->setDataByKeyFromValueOneway(::std::move(iceP_key), ::std::move(iceP_value), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByKeyToList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listKey;
    istr->readAll(iceP_listKey);
    inS.endReadParams();
    StringList iceP_listValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByKeyToList(::std::move(iceP_listKey), iceP_listValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByKeyToMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listKey;
    istr->readAll(iceP_listKey);
    inS.endReadParams();
    StringMap iceP_mapValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByKeyToMap(::std::move(iceP_listKey), iceP_mapValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_mapValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByKeyToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listKey;
    istr->readAll(iceP_listKey);
    inS.endReadParams();
    ::std::string iceP_jsonValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByKeyToJson(::std::move(iceP_listKey), iceP_jsonValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_jsonValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_msetDataByKeyFromList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listKey;
    StringList iceP_listValue;
    istr->readAll(iceP_listKey, iceP_listValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->msetDataByKeyFromList(::std::move(iceP_listKey), ::std::move(iceP_listValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_msetDataByKeyFromListOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listKey;
    StringList iceP_listValue;
    istr->readAll(iceP_listKey, iceP_listValue);
    inS.endReadParams();
    this->msetDataByKeyFromListOneway(::std::move(iceP_listKey), ::std::move(iceP_listValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_msetDataByKeyFromMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_keyValue;
    istr->readAll(iceP_keyValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->msetDataByKeyFromMap(::std::move(iceP_keyValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_msetDataByKeyFromMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_keyValue;
    istr->readAll(iceP_keyValue);
    inS.endReadParams();
    this->msetDataByKeyFromMapOneway(::std::move(iceP_keyValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByIDToMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    istr->readAll(iceP_tableName, iceP_id);
    inS.endReadParams();
    StringMap iceP_mapValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByIDToMap(::std::move(iceP_tableName), ::std::move(iceP_id), iceP_mapValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_mapValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByIDToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    istr->readAll(iceP_tableName, iceP_id);
    inS.endReadParams();
    ::std::string iceP_jsonVaule;
    ErrorInfo iceP_e;
    bool ret = this->getDataByIDToJson(::std::move(iceP_tableName), ::std::move(iceP_id), iceP_jsonVaule, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_jsonVaule, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByFieldToValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    istr->readAll(iceP_tableName, iceP_id, iceP_fieldName);
    inS.endReadParams();
    ::std::string iceP_fieldValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByFieldToValue(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_fieldName), iceP_fieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_fieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByFieldToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    istr->readAll(iceP_tableName, iceP_id, iceP_fieldName);
    inS.endReadParams();
    ::std::string iceP_jsonFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByFieldToJson(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_fieldName), iceP_jsonFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_jsonFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByFieldsToList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringList iceP_listFieldName;
    istr->readAll(iceP_tableName, iceP_id, iceP_listFieldName);
    inS.endReadParams();
    StringList iceP_listFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByFieldsToList(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_listFieldName), iceP_listFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByFieldsToMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringList iceP_listFieldName;
    istr->readAll(iceP_tableName, iceP_id, iceP_listFieldName);
    inS.endReadParams();
    StringMap iceP_mapFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByFieldsToMap(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_listFieldName), iceP_mapFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_mapFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByFieldsToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringList iceP_listFieldName;
    istr->readAll(iceP_tableName, iceP_id, iceP_listFieldName);
    inS.endReadParams();
    ::std::string iceP_jsonFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByFieldsToJson(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_listFieldName), iceP_jsonFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_jsonFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByIDToListList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->readAll(iceP_tableName, iceP_listID);
    inS.endReadParams();
    ListStringList iceP_listFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByIDToListList(::std::move(iceP_tableName), ::std::move(iceP_listID), iceP_listFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByIDToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->readAll(iceP_tableName, iceP_listID);
    inS.endReadParams();
    ::std::string iceP_jsonFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByIDToJson(::std::move(iceP_tableName), ::std::move(iceP_listID), iceP_jsonFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_jsonFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByIDToListMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->readAll(iceP_tableName, iceP_listID);
    inS.endReadParams();
    ListStringMap iceP_listFieldMapValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByIDToListMap(::std::move(iceP_tableName), ::std::move(iceP_listID), iceP_listFieldMapValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listFieldMapValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByIDToMapMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->readAll(iceP_tableName, iceP_listID);
    inS.endReadParams();
    MapStringMap iceP_mapFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByIDToMapMap(::std::move(iceP_tableName), ::std::move(iceP_listID), iceP_mapFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_mapFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldToList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldName);
    inS.endReadParams();
    StringList iceP_listFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldToList(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldName), iceP_listFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldToMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldName);
    inS.endReadParams();
    StringMap iceP_mapValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldToMap(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldName), iceP_mapValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_mapValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldName);
    inS.endReadParams();
    ::std::string iceP_jsonFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldToJson(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldName), iceP_jsonFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_jsonFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldsToListList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldName);
    inS.endReadParams();
    ListStringList iceP_listFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldsToListList(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldName), iceP_listFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldsToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldName);
    inS.endReadParams();
    ::std::string iceP_jsonFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldsToJson(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldName), iceP_jsonFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_jsonFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldsToListMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldName);
    inS.endReadParams();
    ListStringMap iceP_listFieldMapValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldsToListMap(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldName), iceP_listFieldMapValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listFieldMapValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldsToMapMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Idempotent, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldName);
    inS.endReadParams();
    MapStringMap iceP_mapFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldsToMapMap(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldName), iceP_mapFieldValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_mapFieldValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataByFieldFromValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->readAll(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateDataByFieldFromValue(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_fieldName), ::std::move(iceP_fieldValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataByFieldFromValueOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->readAll(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue);
    inS.endReadParams();
    this->updateDataByFieldFromValueOneway(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_fieldName), ::std::move(iceP_fieldValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataByIDFromMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringMap iceP_mapValue;
    istr->readAll(iceP_tableName, iceP_id, iceP_mapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateDataByIDFromMap(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_mapValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataByIDFromMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringMap iceP_mapValue;
    istr->readAll(iceP_tableName, iceP_id, iceP_mapValue);
    inS.endReadParams();
    this->updateDataByIDFromMapOneway(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_mapValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldFromList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    StringList iceP_listFieldValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateDataByFieldFromList(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldName), ::std::move(iceP_listFieldValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldFromListOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    StringList iceP_listFieldValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue);
    inS.endReadParams();
    this->mupdateDataByFieldFromListOneway(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldName), ::std::move(iceP_listFieldValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldFromValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_fieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateDataByFieldFromValue(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldName), ::std::move(iceP_fieldValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldFromValueOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_fieldValue);
    inS.endReadParams();
    this->mupdateDataByFieldFromValueOneway(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldName), ::std::move(iceP_fieldValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateValueByFieldsFromListList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    ListStringList iceP_listFieldValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_listFieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateValueByFieldsFromListList(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldName), ::std::move(iceP_listFieldValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateValueByFieldsFromListListOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    ListStringList iceP_listFieldValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_listFieldValue);
    inS.endReadParams();
    this->mupdateValueByFieldsFromListListOneway(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldName), ::std::move(iceP_listFieldValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldsFromListMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ListStringMap iceP_listFieldMapValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldMapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateDataByFieldsFromListMap(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldMapValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldsFromListMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ListStringMap iceP_listFieldMapValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldMapValue);
    inS.endReadParams();
    this->mupdateDataByFieldsFromListMapOneway(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldMapValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldsFromMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringMap iceP_fieldMapValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldMapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateDataByFieldsFromMap(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldMapValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldsFromMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringMap iceP_fieldMapValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldMapValue);
    inS.endReadParams();
    this->mupdateDataByFieldsFromMapOneway(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldMapValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_oldID;
    ::std::string iceP_newID;
    istr->readAll(iceP_tableName, iceP_oldID, iceP_newID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateDataID(::std::move(iceP_tableName), ::std::move(iceP_oldID), ::std::move(iceP_newID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataIDOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_oldID;
    ::std::string iceP_newID;
    istr->readAll(iceP_tableName, iceP_oldID, iceP_newID);
    inS.endReadParams();
    this->updateDataIDOneway(::std::move(iceP_tableName), ::std::move(iceP_oldID), ::std::move(iceP_newID), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listOldID;
    StringList iceP_listNewID;
    istr->readAll(iceP_tableName, iceP_listOldID, iceP_listNewID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateDataID(::std::move(iceP_tableName), ::std::move(iceP_listOldID), ::std::move(iceP_listNewID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataIDOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listOldID;
    StringList iceP_listNewID;
    istr->readAll(iceP_tableName, iceP_listOldID, iceP_listNewID);
    inS.endReadParams();
    this->mupdateDataIDOneway(::std::move(iceP_tableName), ::std::move(iceP_listOldID), ::std::move(iceP_listNewID), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_insertDataByFieldFromValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->readAll(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->insertDataByFieldFromValue(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_fieldName), ::std::move(iceP_fieldValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_insertDataByFieldFromValueOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->readAll(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue);
    inS.endReadParams();
    this->insertDataByFieldFromValueOneway(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_fieldName), ::std::move(iceP_fieldValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_insertDataByIDFromMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringMap iceP_mapValue;
    istr->readAll(iceP_tableName, iceP_id, iceP_mapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->insertDataByIDFromMap(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_mapValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_insertDataByIDFromMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringMap iceP_mapValue;
    istr->readAll(iceP_tableName, iceP_id, iceP_mapValue);
    inS.endReadParams();
    this->insertDataByIDFromMapOneway(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_mapValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_minsertDataByFieldFromList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    StringList iceP_listFieldValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->minsertDataByFieldFromList(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldName), ::std::move(iceP_listFieldValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_minsertDataByFieldFromListOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    StringList iceP_listFieldValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue);
    inS.endReadParams();
    this->minsertDataByFieldFromListOneway(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldName), ::std::move(iceP_listFieldValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_minsertDataByFieldsFromListMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ListStringMap iceP_listFieldMapValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldMapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->minsertDataByFieldsFromListMap(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldMapValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_minsertDataByFieldsFromListMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ListStringMap iceP_listFieldMapValue;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldMapValue);
    inS.endReadParams();
    this->minsertDataByFieldsFromListMapOneway(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldMapValue), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_deleteDataByID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    istr->readAll(iceP_tableName, iceP_id);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteDataByID(::std::move(iceP_tableName), ::std::move(iceP_id), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_deleteDataByIDOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    istr->readAll(iceP_tableName, iceP_id);
    inS.endReadParams();
    this->deleteDataByIDOneway(::std::move(iceP_tableName), ::std::move(iceP_id), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mdeleteDataByID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->readAll(iceP_tableName, iceP_listID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mdeleteDataByID(::std::move(iceP_tableName), ::std::move(iceP_listID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mdeleteDataByIDOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->readAll(iceP_tableName, iceP_listID);
    inS.endReadParams();
    this->mdeleteDataByIDOneway(::std::move(iceP_tableName), ::std::move(iceP_listID), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_deleteDataByField(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    istr->readAll(iceP_tableName, iceP_id, iceP_fieldName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteDataByField(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_fieldName), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_deleteDataByFields(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringList iceP_listFieldName;
    istr->readAll(iceP_tableName, iceP_id, iceP_listFieldName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteDataByFields(::std::move(iceP_tableName), ::std::move(iceP_id), ::std::move(iceP_listFieldName), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mdeleteDataByField(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    istr->readAll(iceP_tableName, iceP_listID, iceP_fieldName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mdeleteDataByField(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_fieldName), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mdeleteDataByFields(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    istr->readAll(iceP_tableName, iceP_listID, iceP_listFieldName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mdeleteDataByFields(::std::move(iceP_tableName), ::std::move(iceP_listID), ::std::move(iceP_listFieldName), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPRTData_ops, iceC_ZG6000_ZGSPRTData_ops + 78, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPRTData_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_deleteDataByField(in, current);
        }
        case 2:
        {
            return _iceD_deleteDataByFields(in, current);
        }
        case 3:
        {
            return _iceD_deleteDataByID(in, current);
        }
        case 4:
        {
            return _iceD_deleteDataByIDOneway(in, current);
        }
        case 5:
        {
            return _iceD_dispatchData(in, current);
        }
        case 6:
        {
            return _iceD_exitApp(in, current);
        }
        case 7:
        {
            return _iceD_getDataByFieldToJson(in, current);
        }
        case 8:
        {
            return _iceD_getDataByFieldToValue(in, current);
        }
        case 9:
        {
            return _iceD_getDataByFieldsToJson(in, current);
        }
        case 10:
        {
            return _iceD_getDataByFieldsToList(in, current);
        }
        case 11:
        {
            return _iceD_getDataByFieldsToMap(in, current);
        }
        case 12:
        {
            return _iceD_getDataByIDToJson(in, current);
        }
        case 13:
        {
            return _iceD_getDataByIDToMap(in, current);
        }
        case 14:
        {
            return _iceD_getDataByKeyToJson(in, current);
        }
        case 15:
        {
            return _iceD_getDataByKeyToValue(in, current);
        }
        case 16:
        {
            return _iceD_getVersion(in, current);
        }
        case 17:
        {
            return _iceD_heartDebug(in, current);
        }
        case 18:
        {
            return _iceD_ice_id(in, current);
        }
        case 19:
        {
            return _iceD_ice_ids(in, current);
        }
        case 20:
        {
            return _iceD_ice_isA(in, current);
        }
        case 21:
        {
            return _iceD_ice_ping(in, current);
        }
        case 22:
        {
            return _iceD_insertDataByFieldFromValue(in, current);
        }
        case 23:
        {
            return _iceD_insertDataByFieldFromValueOneway(in, current);
        }
        case 24:
        {
            return _iceD_insertDataByIDFromMap(in, current);
        }
        case 25:
        {
            return _iceD_insertDataByIDFromMapOneway(in, current);
        }
        case 26:
        {
            return _iceD_isDebugging(in, current);
        }
        case 27:
        {
            return _iceD_mdeleteDataByField(in, current);
        }
        case 28:
        {
            return _iceD_mdeleteDataByFields(in, current);
        }
        case 29:
        {
            return _iceD_mdeleteDataByID(in, current);
        }
        case 30:
        {
            return _iceD_mdeleteDataByIDOneway(in, current);
        }
        case 31:
        {
            return _iceD_mgetDataByFieldToJson(in, current);
        }
        case 32:
        {
            return _iceD_mgetDataByFieldToList(in, current);
        }
        case 33:
        {
            return _iceD_mgetDataByFieldToMap(in, current);
        }
        case 34:
        {
            return _iceD_mgetDataByFieldsToJson(in, current);
        }
        case 35:
        {
            return _iceD_mgetDataByFieldsToListList(in, current);
        }
        case 36:
        {
            return _iceD_mgetDataByFieldsToListMap(in, current);
        }
        case 37:
        {
            return _iceD_mgetDataByFieldsToMapMap(in, current);
        }
        case 38:
        {
            return _iceD_mgetDataByIDToJson(in, current);
        }
        case 39:
        {
            return _iceD_mgetDataByIDToListList(in, current);
        }
        case 40:
        {
            return _iceD_mgetDataByIDToListMap(in, current);
        }
        case 41:
        {
            return _iceD_mgetDataByIDToMapMap(in, current);
        }
        case 42:
        {
            return _iceD_mgetDataByKeyToJson(in, current);
        }
        case 43:
        {
            return _iceD_mgetDataByKeyToList(in, current);
        }
        case 44:
        {
            return _iceD_mgetDataByKeyToMap(in, current);
        }
        case 45:
        {
            return _iceD_minsertDataByFieldFromList(in, current);
        }
        case 46:
        {
            return _iceD_minsertDataByFieldFromListOneway(in, current);
        }
        case 47:
        {
            return _iceD_minsertDataByFieldsFromListMap(in, current);
        }
        case 48:
        {
            return _iceD_minsertDataByFieldsFromListMapOneway(in, current);
        }
        case 49:
        {
            return _iceD_msetDataByKeyFromList(in, current);
        }
        case 50:
        {
            return _iceD_msetDataByKeyFromListOneway(in, current);
        }
        case 51:
        {
            return _iceD_msetDataByKeyFromMap(in, current);
        }
        case 52:
        {
            return _iceD_msetDataByKeyFromMapOneway(in, current);
        }
        case 53:
        {
            return _iceD_mupdateDataByFieldFromList(in, current);
        }
        case 54:
        {
            return _iceD_mupdateDataByFieldFromListOneway(in, current);
        }
        case 55:
        {
            return _iceD_mupdateDataByFieldFromValue(in, current);
        }
        case 56:
        {
            return _iceD_mupdateDataByFieldFromValueOneway(in, current);
        }
        case 57:
        {
            return _iceD_mupdateDataByFieldsFromListMap(in, current);
        }
        case 58:
        {
            return _iceD_mupdateDataByFieldsFromListMapOneway(in, current);
        }
        case 59:
        {
            return _iceD_mupdateDataByFieldsFromMap(in, current);
        }
        case 60:
        {
            return _iceD_mupdateDataByFieldsFromMapOneway(in, current);
        }
        case 61:
        {
            return _iceD_mupdateDataID(in, current);
        }
        case 62:
        {
            return _iceD_mupdateDataIDOneway(in, current);
        }
        case 63:
        {
            return _iceD_mupdateValueByFieldsFromListList(in, current);
        }
        case 64:
        {
            return _iceD_mupdateValueByFieldsFromListListOneway(in, current);
        }
        case 65:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 66:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 67:
        {
            return _iceD_setDataByKeyFromValue(in, current);
        }
        case 68:
        {
            return _iceD_setDataByKeyFromValueOneway(in, current);
        }
        case 69:
        {
            return _iceD_startDebug(in, current);
        }
        case 70:
        {
            return _iceD_stopDebug(in, current);
        }
        case 71:
        {
            return _iceD_test(in, current);
        }
        case 72:
        {
            return _iceD_updateDataByFieldFromValue(in, current);
        }
        case 73:
        {
            return _iceD_updateDataByFieldFromValueOneway(in, current);
        }
        case 74:
        {
            return _iceD_updateDataByIDFromMap(in, current);
        }
        case 75:
        {
            return _iceD_updateDataByIDFromMapOneway(in, current);
        }
        case 76:
        {
            return _iceD_updateDataID(in, current);
        }
        case 77:
        {
            return _iceD_updateDataIDOneway(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_getDataByKeyToValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::GetDataByKeyToValueResult>>& outAsync, const ::std::string& iceP_key, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByKeyToValue_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_getDataByKeyToValue_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_key);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::GetDataByKeyToValueResult v;
            istr->readAll(v.value, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_getDataByKeyToJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::GetDataByKeyToJsonResult>>& outAsync, const ::std::string& iceP_key, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByKeyToJson_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_getDataByKeyToJson_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_key);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::GetDataByKeyToJsonResult v;
            istr->readAll(v.jsonValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_setDataByKeyFromValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::SetDataByKeyFromValueResult>>& outAsync, const ::std::string& iceP_key, const ::std::string& iceP_value, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_setDataByKeyFromValue_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_setDataByKeyFromValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_key, iceP_value);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::SetDataByKeyFromValueResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_setDataByKeyFromValueOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_key, const ::std::string& iceP_value, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_setDataByKeyFromValueOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_key, iceP_value);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByKeyToList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByKeyToListResult>>& outAsync, const StringList& iceP_listKey, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToList_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listKey);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByKeyToListResult v;
            istr->readAll(v.listValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByKeyToMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByKeyToMapResult>>& outAsync, const StringList& iceP_listKey, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToMap_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listKey);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByKeyToMapResult v;
            istr->readAll(v.mapValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByKeyToJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByKeyToJsonResult>>& outAsync, const StringList& iceP_listKey, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToJson_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToJson_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listKey);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByKeyToJsonResult v;
            istr->readAll(v.jsonValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_msetDataByKeyFromList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MsetDataByKeyFromListResult>>& outAsync, const StringList& iceP_listKey, const StringList& iceP_listValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listKey, iceP_listValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MsetDataByKeyFromListResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_msetDataByKeyFromListOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const StringList& iceP_listKey, const StringList& iceP_listValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromListOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listKey, iceP_listValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_msetDataByKeyFromMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MsetDataByKeyFromMapResult>>& outAsync, const StringMap& iceP_keyValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMap_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_keyValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MsetDataByKeyFromMapResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_msetDataByKeyFromMapOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const StringMap& iceP_keyValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMapOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_keyValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_getDataByIDToMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::GetDataByIDToMapResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByIDToMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_getDataByIDToMap_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::GetDataByIDToMapResult v;
            istr->readAll(v.mapValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_getDataByIDToJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::GetDataByIDToJsonResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByIDToJson_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_getDataByIDToJson_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::GetDataByIDToJsonResult v;
            istr->readAll(v.jsonVaule, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_getDataByFieldToValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::GetDataByFieldToValueResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByFieldToValue_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_getDataByFieldToValue_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_fieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::GetDataByFieldToValueResult v;
            istr->readAll(v.fieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_getDataByFieldToJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::GetDataByFieldToJsonResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByFieldToJson_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_getDataByFieldToJson_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_fieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::GetDataByFieldToJsonResult v;
            istr->readAll(v.jsonFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_getDataByFieldsToList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::GetDataByFieldsToListResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const StringList& iceP_listFieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByFieldsToList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_getDataByFieldsToList_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_listFieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::GetDataByFieldsToListResult v;
            istr->readAll(v.listFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_getDataByFieldsToMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::GetDataByFieldsToMapResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const StringList& iceP_listFieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByFieldsToMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_getDataByFieldsToMap_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_listFieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::GetDataByFieldsToMapResult v;
            istr->readAll(v.mapFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_getDataByFieldsToJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::GetDataByFieldsToJsonResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const StringList& iceP_listFieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByFieldsToJson_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_getDataByFieldsToJson_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_listFieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::GetDataByFieldsToJsonResult v;
            istr->readAll(v.jsonFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByIDToListList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByIDToListListResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByIDToListList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByIDToListList_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByIDToListListResult v;
            istr->readAll(v.listFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByIDToJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByIDToJsonResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByIDToJson_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByIDToJson_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByIDToJsonResult v;
            istr->readAll(v.jsonFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByIDToListMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByIDToListMapResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByIDToListMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByIDToListMap_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByIDToListMapResult v;
            istr->readAll(v.listFieldMapValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByIDToMapMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByIDToMapMapResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByIDToMapMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByIDToMapMap_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByIDToMapMapResult v;
            istr->readAll(v.mapFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByFieldToList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByFieldToListResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToList_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByFieldToListResult v;
            istr->readAll(v.listFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByFieldToMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByFieldToMapResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToMap_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByFieldToMapResult v;
            istr->readAll(v.mapValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByFieldToJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByFieldToJsonResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToJson_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToJson_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByFieldToJsonResult v;
            istr->readAll(v.jsonFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByFieldsToListList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByFieldsToListListResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const StringList& iceP_listFieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListList_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByFieldsToListListResult v;
            istr->readAll(v.listFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByFieldsToJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByFieldsToJsonResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const StringList& iceP_listFieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToJson_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToJson_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByFieldsToJsonResult v;
            istr->readAll(v.jsonFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByFieldsToListMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByFieldsToListMapResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const StringList& iceP_listFieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListMap_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByFieldsToListMapResult v;
            istr->readAll(v.listFieldMapValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mgetDataByFieldsToMapMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MgetDataByFieldsToMapMapResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const StringList& iceP_listFieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToMapMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToMapMap_name, ::Ice::OperationMode::Idempotent, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MgetDataByFieldsToMapMapResult v;
            istr->readAll(v.mapFieldValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_updateDataByFieldFromValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::UpdateDataByFieldFromValueResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValue_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::UpdateDataByFieldFromValueResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_updateDataByFieldFromValueOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValueOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_updateDataByIDFromMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::UpdateDataByIDFromMapResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const StringMap& iceP_mapValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_updateDataByIDFromMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_updateDataByIDFromMap_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_mapValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::UpdateDataByIDFromMapResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_updateDataByIDFromMapOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const StringMap& iceP_mapValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_updateDataByIDFromMapOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_mapValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateDataByFieldFromList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MupdateDataByFieldFromListResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::std::string& iceP_fieldName, const StringList& iceP_listFieldValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MupdateDataByFieldFromListResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateDataByFieldFromListOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::std::string& iceP_fieldName, const StringList& iceP_listFieldValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromListOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateDataByFieldFromValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MupdateDataByFieldFromValueResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValue_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_fieldValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MupdateDataByFieldFromValueResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateDataByFieldFromValueOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValueOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_fieldValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateValueByFieldsFromListList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MupdateValueByFieldsFromListListResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const StringList& iceP_listFieldName, const ListStringList& iceP_listFieldValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_listFieldValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MupdateValueByFieldsFromListListResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateValueByFieldsFromListListOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const StringList& iceP_listFieldName, const ListStringList& iceP_listFieldValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListListOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_listFieldValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateDataByFieldsFromListMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MupdateDataByFieldsFromListMapResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ListStringMap& iceP_listFieldMapValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMap_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldMapValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MupdateDataByFieldsFromListMapResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateDataByFieldsFromListMapOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ListStringMap& iceP_listFieldMapValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMapOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldMapValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateDataByFieldsFromMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MupdateDataByFieldsFromMapResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const StringMap& iceP_fieldMapValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMap_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldMapValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MupdateDataByFieldsFromMapResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateDataByFieldsFromMapOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const StringMap& iceP_fieldMapValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMapOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldMapValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_updateDataID(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::UpdateDataIDResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_oldID, const ::std::string& iceP_newID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_updateDataID_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_updateDataID_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_oldID, iceP_newID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::UpdateDataIDResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_updateDataIDOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_oldID, const ::std::string& iceP_newID, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_updateDataIDOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_oldID, iceP_newID);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateDataID(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MupdateDataIDResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listOldID, const StringList& iceP_listNewID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateDataID_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataID_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listOldID, iceP_listNewID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MupdateDataIDResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mupdateDataIDOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listOldID, const StringList& iceP_listNewID, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataIDOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listOldID, iceP_listNewID);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_insertDataByFieldFromValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::InsertDataByFieldFromValueResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValue_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::InsertDataByFieldFromValueResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_insertDataByFieldFromValueOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValueOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_insertDataByIDFromMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::InsertDataByIDFromMapResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const StringMap& iceP_mapValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_insertDataByIDFromMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_insertDataByIDFromMap_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_mapValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::InsertDataByIDFromMapResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_insertDataByIDFromMapOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const StringMap& iceP_mapValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_insertDataByIDFromMapOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_mapValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_minsertDataByFieldFromList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MinsertDataByFieldFromListResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::std::string& iceP_fieldName, const StringList& iceP_listFieldValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MinsertDataByFieldFromListResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_minsertDataByFieldFromListOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::std::string& iceP_fieldName, const StringList& iceP_listFieldValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromListOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_minsertDataByFieldsFromListMap(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MinsertDataByFieldsFromListMapResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ListStringMap& iceP_listFieldMapValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMap_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMap_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldMapValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MinsertDataByFieldsFromListMapResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_minsertDataByFieldsFromListMapOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ListStringMap& iceP_listFieldMapValue, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMapOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldMapValue);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_deleteDataByID(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::DeleteDataByIDResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_deleteDataByID_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_deleteDataByID_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::DeleteDataByIDResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_deleteDataByIDOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_deleteDataByIDOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mdeleteDataByID(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MdeleteDataByIDResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mdeleteDataByID_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mdeleteDataByID_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MdeleteDataByIDResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mdeleteDataByIDOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mdeleteDataByIDOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_deleteDataByField(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::DeleteDataByFieldResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_deleteDataByField_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_deleteDataByField_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_fieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::DeleteDataByFieldResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_deleteDataByFields(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::DeleteDataByFieldsResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_id, const StringList& iceP_listFieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_deleteDataByFields_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_deleteDataByFields_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_id, iceP_listFieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::DeleteDataByFieldsResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mdeleteDataByField(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MdeleteDataByFieldResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mdeleteDataByField_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mdeleteDataByField_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_fieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MdeleteDataByFieldResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPRTDataPrx::_iceI_mdeleteDataByFields(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRTData::MdeleteDataByFieldsResult>>& outAsync, const ::std::string& iceP_tableName, const StringList& iceP_listID, const StringList& iceP_listFieldName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mdeleteDataByFields_name);
    outAsync->invoke(iceC_ZG6000_ZGSPRTData_mdeleteDataByFields_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_listID, iceP_listFieldName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPRTData::MdeleteDataByFieldsResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSPRTDataPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSPRTDataPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSPRTDataPrx::ice_staticId()
{
    return ZGSPRTData::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPRTData_getDataByKeyToValue_name = "getDataByKeyToValue";

const ::std::string iceC_ZG6000_ZGSPRTData_getDataByKeyToJson_name = "getDataByKeyToJson";

const ::std::string iceC_ZG6000_ZGSPRTData_setDataByKeyFromValue_name = "setDataByKeyFromValue";

const ::std::string iceC_ZG6000_ZGSPRTData_setDataByKeyFromValueOneway_name = "setDataByKeyFromValueOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByKeyToList_name = "mgetDataByKeyToList";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByKeyToMap_name = "mgetDataByKeyToMap";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByKeyToJson_name = "mgetDataByKeyToJson";

const ::std::string iceC_ZG6000_ZGSPRTData_msetDataByKeyFromList_name = "msetDataByKeyFromList";

const ::std::string iceC_ZG6000_ZGSPRTData_msetDataByKeyFromListOneway_name = "msetDataByKeyFromListOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMap_name = "msetDataByKeyFromMap";

const ::std::string iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMapOneway_name = "msetDataByKeyFromMapOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_getDataByIDToMap_name = "getDataByIDToMap";

const ::std::string iceC_ZG6000_ZGSPRTData_getDataByIDToJson_name = "getDataByIDToJson";

const ::std::string iceC_ZG6000_ZGSPRTData_getDataByFieldToValue_name = "getDataByFieldToValue";

const ::std::string iceC_ZG6000_ZGSPRTData_getDataByFieldToJson_name = "getDataByFieldToJson";

const ::std::string iceC_ZG6000_ZGSPRTData_getDataByFieldsToList_name = "getDataByFieldsToList";

const ::std::string iceC_ZG6000_ZGSPRTData_getDataByFieldsToMap_name = "getDataByFieldsToMap";

const ::std::string iceC_ZG6000_ZGSPRTData_getDataByFieldsToJson_name = "getDataByFieldsToJson";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByIDToListList_name = "mgetDataByIDToListList";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByIDToJson_name = "mgetDataByIDToJson";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByIDToListMap_name = "mgetDataByIDToListMap";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByIDToMapMap_name = "mgetDataByIDToMapMap";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldToList_name = "mgetDataByFieldToList";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldToMap_name = "mgetDataByFieldToMap";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldToJson_name = "mgetDataByFieldToJson";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListList_name = "mgetDataByFieldsToListList";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToJson_name = "mgetDataByFieldsToJson";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListMap_name = "mgetDataByFieldsToListMap";

const ::std::string iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToMapMap_name = "mgetDataByFieldsToMapMap";

const ::std::string iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValue_name = "updateDataByFieldFromValue";

const ::std::string iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValueOneway_name = "updateDataByFieldFromValueOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_updateDataByIDFromMap_name = "updateDataByIDFromMap";

const ::std::string iceC_ZG6000_ZGSPRTData_updateDataByIDFromMapOneway_name = "updateDataByIDFromMapOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromList_name = "mupdateDataByFieldFromList";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromListOneway_name = "mupdateDataByFieldFromListOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValue_name = "mupdateDataByFieldFromValue";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValueOneway_name = "mupdateDataByFieldFromValueOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListList_name = "mupdateValueByFieldsFromListList";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListListOneway_name = "mupdateValueByFieldsFromListListOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMap_name = "mupdateDataByFieldsFromListMap";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMapOneway_name = "mupdateDataByFieldsFromListMapOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMap_name = "mupdateDataByFieldsFromMap";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMapOneway_name = "mupdateDataByFieldsFromMapOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_updateDataID_name = "updateDataID";

const ::std::string iceC_ZG6000_ZGSPRTData_updateDataIDOneway_name = "updateDataIDOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataID_name = "mupdateDataID";

const ::std::string iceC_ZG6000_ZGSPRTData_mupdateDataIDOneway_name = "mupdateDataIDOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValue_name = "insertDataByFieldFromValue";

const ::std::string iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValueOneway_name = "insertDataByFieldFromValueOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_insertDataByIDFromMap_name = "insertDataByIDFromMap";

const ::std::string iceC_ZG6000_ZGSPRTData_insertDataByIDFromMapOneway_name = "insertDataByIDFromMapOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromList_name = "minsertDataByFieldFromList";

const ::std::string iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromListOneway_name = "minsertDataByFieldFromListOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMap_name = "minsertDataByFieldsFromListMap";

const ::std::string iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMapOneway_name = "minsertDataByFieldsFromListMapOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_deleteDataByID_name = "deleteDataByID";

const ::std::string iceC_ZG6000_ZGSPRTData_deleteDataByIDOneway_name = "deleteDataByIDOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_mdeleteDataByID_name = "mdeleteDataByID";

const ::std::string iceC_ZG6000_ZGSPRTData_mdeleteDataByIDOneway_name = "mdeleteDataByIDOneway";

const ::std::string iceC_ZG6000_ZGSPRTData_deleteDataByField_name = "deleteDataByField";

const ::std::string iceC_ZG6000_ZGSPRTData_deleteDataByFields_name = "deleteDataByFields";

const ::std::string iceC_ZG6000_ZGSPRTData_mdeleteDataByField_name = "mdeleteDataByField";

const ::std::string iceC_ZG6000_ZGSPRTData_mdeleteDataByFields_name = "mdeleteDataByFields";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSPRTData* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSPRTData>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSPRTData;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_getDataByKeyToValue(const ::std::string& iceP_key, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByKeyToValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_getDataByKeyToValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_getDataByKeyToValue_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_key);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_getDataByKeyToValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_getDataByKeyToValue(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByKeyToValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_value);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_getDataByKeyToValue(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByKeyToValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_value);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_getDataByKeyToJson(const ::std::string& iceP_key, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByKeyToJson_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_getDataByKeyToJson_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_getDataByKeyToJson_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_key);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_getDataByKeyToJson_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_getDataByKeyToJson(::std::string& iceP_jsonValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByKeyToJson_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_getDataByKeyToJson(::std::string& iceP_jsonValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByKeyToJson_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_setDataByKeyFromValue(const ::std::string& iceP_key, const ::std::string& iceP_value, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_setDataByKeyFromValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_setDataByKeyFromValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_setDataByKeyFromValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_key);
        ostr->write(iceP_value);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_setDataByKeyFromValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_setDataByKeyFromValue(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_setDataByKeyFromValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_setDataByKeyFromValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_setDataByKeyFromValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_setDataByKeyFromValueOneway(const ::std::string& iceP_key, const ::std::string& iceP_value, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_setDataByKeyFromValueOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_setDataByKeyFromValueOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_key);
        ostr->write(iceP_value);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_setDataByKeyFromValueOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_setDataByKeyFromValueOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_setDataByKeyFromValueOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByKeyToList(const ::ZG6000::StringList& iceP_listKey, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByKeyToList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToList_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listKey);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByKeyToList(::ZG6000::StringList& iceP_listValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByKeyToList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByKeyToList(::ZG6000::StringList& iceP_listValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByKeyToList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByKeyToMap(const ::ZG6000::StringList& iceP_listKey, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByKeyToMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToMap_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listKey);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByKeyToMap(::ZG6000::StringMap& iceP_mapValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByKeyToMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByKeyToMap(::ZG6000::StringMap& iceP_mapValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByKeyToMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByKeyToJson(const ::ZG6000::StringList& iceP_listKey, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToJson_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByKeyToJson_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToJson_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listKey);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByKeyToJson_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByKeyToJson(::std::string& iceP_jsonValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByKeyToJson_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByKeyToJson(::std::string& iceP_jsonValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByKeyToJson_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_msetDataByKeyFromList(const ::ZG6000::StringList& iceP_listKey, const ::ZG6000::StringList& iceP_listValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_msetDataByKeyFromList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listKey);
        ostr->write(iceP_listValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_msetDataByKeyFromList(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_msetDataByKeyFromList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_msetDataByKeyFromList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_msetDataByKeyFromList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_msetDataByKeyFromListOneway(const ::ZG6000::StringList& iceP_listKey, const ::ZG6000::StringList& iceP_listValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_msetDataByKeyFromListOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromListOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listKey);
        ostr->write(iceP_listValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromListOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_msetDataByKeyFromListOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_msetDataByKeyFromListOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_msetDataByKeyFromMap(const ::ZG6000::StringMap& iceP_keyValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMap_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_keyValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_msetDataByKeyFromMap(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_msetDataByKeyFromMap(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_msetDataByKeyFromMapOneway(const ::ZG6000::StringMap& iceP_keyValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMapOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMapOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_keyValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMapOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_msetDataByKeyFromMapOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_msetDataByKeyFromMapOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_getDataByIDToMap(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByIDToMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_getDataByIDToMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_getDataByIDToMap_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_getDataByIDToMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_getDataByIDToMap(::ZG6000::StringMap& iceP_mapValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByIDToMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_getDataByIDToMap(::ZG6000::StringMap& iceP_mapValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByIDToMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_getDataByIDToJson(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByIDToJson_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_getDataByIDToJson_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_getDataByIDToJson_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_getDataByIDToJson_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_getDataByIDToJson(::std::string& iceP_jsonVaule, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByIDToJson_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonVaule);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_getDataByIDToJson(::std::string& iceP_jsonVaule, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByIDToJson_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonVaule);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_getDataByFieldToValue(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByFieldToValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_getDataByFieldToValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_getDataByFieldToValue_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_fieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_getDataByFieldToValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_getDataByFieldToValue(::std::string& iceP_fieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByFieldToValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_fieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_getDataByFieldToValue(::std::string& iceP_fieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByFieldToValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_fieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_getDataByFieldToJson(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByFieldToJson_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_getDataByFieldToJson_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_getDataByFieldToJson_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_fieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_getDataByFieldToJson_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_getDataByFieldToJson(::std::string& iceP_jsonFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByFieldToJson_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_getDataByFieldToJson(::std::string& iceP_jsonFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByFieldToJson_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_getDataByFieldsToList(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::ZG6000::StringList& iceP_listFieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByFieldsToList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_getDataByFieldsToList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_getDataByFieldsToList_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_listFieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_getDataByFieldsToList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_getDataByFieldsToList(::ZG6000::StringList& iceP_listFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByFieldsToList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_getDataByFieldsToList(::ZG6000::StringList& iceP_listFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByFieldsToList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_getDataByFieldsToMap(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::ZG6000::StringList& iceP_listFieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByFieldsToMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_getDataByFieldsToMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_getDataByFieldsToMap_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_listFieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_getDataByFieldsToMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_getDataByFieldsToMap(::ZG6000::StringMap& iceP_mapFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByFieldsToMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_getDataByFieldsToMap(::ZG6000::StringMap& iceP_mapFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByFieldsToMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_getDataByFieldsToJson(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::ZG6000::StringList& iceP_listFieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_getDataByFieldsToJson_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_getDataByFieldsToJson_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_getDataByFieldsToJson_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_listFieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_getDataByFieldsToJson_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_getDataByFieldsToJson(::std::string& iceP_jsonFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByFieldsToJson_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_getDataByFieldsToJson(::std::string& iceP_jsonFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_getDataByFieldsToJson_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByIDToListList(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByIDToListList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToListList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByIDToListList_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByIDToListList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByIDToListList(::ZG6000::ListStringList& iceP_listFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToListList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByIDToListList(::ZG6000::ListStringList& iceP_listFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToListList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByIDToJson(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByIDToJson_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToJson_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByIDToJson_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByIDToJson_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByIDToJson(::std::string& iceP_jsonFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToJson_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByIDToJson(::std::string& iceP_jsonFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToJson_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByIDToListMap(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByIDToListMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToListMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByIDToListMap_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByIDToListMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByIDToListMap(::ZG6000::ListStringMap& iceP_listFieldMapValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToListMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldMapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByIDToListMap(::ZG6000::ListStringMap& iceP_listFieldMapValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToListMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldMapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByIDToMapMap(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByIDToMapMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToMapMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByIDToMapMap_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByIDToMapMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByIDToMapMap(::ZG6000::MapStringMap& iceP_mapFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToMapMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByIDToMapMap(::ZG6000::MapStringMap& iceP_mapFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByIDToMapMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByFieldToList(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldToList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToList_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByFieldToList(::ZG6000::StringList& iceP_listFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldToList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByFieldToList(::ZG6000::StringList& iceP_listFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldToList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByFieldToMap(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldToMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToMap_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByFieldToMap(::ZG6000::StringMap& iceP_mapValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldToMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByFieldToMap(::ZG6000::StringMap& iceP_mapValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldToMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByFieldToJson(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToJson_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldToJson_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToJson_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldToJson_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByFieldToJson(::std::string& iceP_jsonFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldToJson_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByFieldToJson(::std::string& iceP_jsonFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldToJson_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByFieldsToListList(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::StringList& iceP_listFieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListList_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByFieldsToListList(::ZG6000::ListStringList& iceP_listFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByFieldsToListList(::ZG6000::ListStringList& iceP_listFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByFieldsToJson(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::StringList& iceP_listFieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToJson_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToJson_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToJson_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToJson_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByFieldsToJson(::std::string& iceP_jsonFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToJson_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByFieldsToJson(::std::string& iceP_jsonFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToJson_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_jsonFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByFieldsToListMap(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::StringList& iceP_listFieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListMap_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByFieldsToListMap(::ZG6000::ListStringMap& iceP_listFieldMapValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldMapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByFieldsToListMap(::ZG6000::ListStringMap& iceP_listFieldMapValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToListMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFieldMapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mgetDataByFieldsToMapMap(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::StringList& iceP_listFieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToMapMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToMapMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToMapMap_name, ::Ice::Idempotent, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToMapMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mgetDataByFieldsToMapMap(::ZG6000::MapStringMap& iceP_mapFieldValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToMapMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mgetDataByFieldsToMapMap(::ZG6000::MapStringMap& iceP_mapFieldValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mgetDataByFieldsToMapMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapFieldValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_updateDataByFieldFromValue(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_fieldName);
        ostr->write(iceP_fieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_updateDataByFieldFromValue(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_updateDataByFieldFromValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_updateDataByFieldFromValueOneway(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValueOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValueOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_fieldName);
        ostr->write(iceP_fieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValueOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_updateDataByFieldFromValueOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_updateDataByFieldFromValueOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_updateDataByIDFromMap(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::ZG6000::StringMap& iceP_mapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_updateDataByIDFromMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_updateDataByIDFromMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_updateDataByIDFromMap_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_mapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_updateDataByIDFromMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_updateDataByIDFromMap(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_updateDataByIDFromMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_updateDataByIDFromMap(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_updateDataByIDFromMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_updateDataByIDFromMapOneway(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::ZG6000::StringMap& iceP_mapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_updateDataByIDFromMapOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_updateDataByIDFromMapOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_mapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_updateDataByIDFromMapOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_updateDataByIDFromMapOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_updateDataByIDFromMapOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateDataByFieldFromList(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::ZG6000::StringList& iceP_listFieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldName);
        ostr->write(iceP_listFieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mupdateDataByFieldFromList(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mupdateDataByFieldFromList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateDataByFieldFromListOneway(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::ZG6000::StringList& iceP_listFieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromListOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromListOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldName);
        ostr->write(iceP_listFieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromListOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_mupdateDataByFieldFromListOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromListOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateDataByFieldFromValue(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldName);
        ostr->write(iceP_fieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mupdateDataByFieldFromValue(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mupdateDataByFieldFromValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateDataByFieldFromValueOneway(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValueOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValueOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldName);
        ostr->write(iceP_fieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValueOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_mupdateDataByFieldFromValueOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldFromValueOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateValueByFieldsFromListList(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::StringList& iceP_listFieldName, const ::ZG6000::ListStringList& iceP_listFieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldName);
        ostr->write(iceP_listFieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mupdateValueByFieldsFromListList(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mupdateValueByFieldsFromListList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateValueByFieldsFromListListOneway(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::StringList& iceP_listFieldName, const ::ZG6000::ListStringList& iceP_listFieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListListOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListListOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldName);
        ostr->write(iceP_listFieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListListOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_mupdateValueByFieldsFromListListOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_mupdateValueByFieldsFromListListOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateDataByFieldsFromListMap(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::ListStringMap& iceP_listFieldMapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMap_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldMapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mupdateDataByFieldsFromListMap(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mupdateDataByFieldsFromListMap(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateDataByFieldsFromListMapOneway(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::ListStringMap& iceP_listFieldMapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMapOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMapOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldMapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMapOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_mupdateDataByFieldsFromListMapOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromListMapOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateDataByFieldsFromMap(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::StringMap& iceP_fieldMapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMap_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldMapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mupdateDataByFieldsFromMap(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mupdateDataByFieldsFromMap(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateDataByFieldsFromMapOneway(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::StringMap& iceP_fieldMapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMapOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMapOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldMapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMapOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_mupdateDataByFieldsFromMapOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_mupdateDataByFieldsFromMapOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_updateDataID(const ::std::string& iceP_tableName, const ::std::string& iceP_oldID, const ::std::string& iceP_newID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_updateDataID_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_updateDataID_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_updateDataID_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_oldID);
        ostr->write(iceP_newID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_updateDataID_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_updateDataID(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_updateDataID_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_updateDataID(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_updateDataID_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_updateDataIDOneway(const ::std::string& iceP_tableName, const ::std::string& iceP_oldID, const ::std::string& iceP_newID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_updateDataIDOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_updateDataIDOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_oldID);
        ostr->write(iceP_newID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_updateDataIDOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_updateDataIDOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_updateDataIDOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateDataID(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listOldID, const ::ZG6000::StringList& iceP_listNewID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mupdateDataID_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateDataID_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateDataID_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listOldID);
        ostr->write(iceP_listNewID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataID_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mupdateDataID(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateDataID_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mupdateDataID(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mupdateDataID_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mupdateDataIDOneway(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listOldID, const ::ZG6000::StringList& iceP_listNewID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mupdateDataIDOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mupdateDataIDOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listOldID);
        ostr->write(iceP_listNewID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mupdateDataIDOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_mupdateDataIDOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_mupdateDataIDOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_insertDataByFieldFromValue(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_fieldName);
        ostr->write(iceP_fieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_insertDataByFieldFromValue(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_insertDataByFieldFromValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_insertDataByFieldFromValueOneway(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::std::string& iceP_fieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValueOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValueOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_fieldName);
        ostr->write(iceP_fieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValueOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_insertDataByFieldFromValueOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_insertDataByFieldFromValueOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_insertDataByIDFromMap(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::ZG6000::StringMap& iceP_mapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_insertDataByIDFromMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_insertDataByIDFromMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_insertDataByIDFromMap_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_mapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_insertDataByIDFromMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_insertDataByIDFromMap(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_insertDataByIDFromMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_insertDataByIDFromMap(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_insertDataByIDFromMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_insertDataByIDFromMapOneway(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::ZG6000::StringMap& iceP_mapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_insertDataByIDFromMapOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_insertDataByIDFromMapOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_mapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_insertDataByIDFromMapOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_insertDataByIDFromMapOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_insertDataByIDFromMapOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_minsertDataByFieldFromList(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::ZG6000::StringList& iceP_listFieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldName);
        ostr->write(iceP_listFieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_minsertDataByFieldFromList(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_minsertDataByFieldFromList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_minsertDataByFieldFromListOneway(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::ZG6000::StringList& iceP_listFieldValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromListOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromListOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldName);
        ostr->write(iceP_listFieldValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromListOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_minsertDataByFieldFromListOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_minsertDataByFieldFromListOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_minsertDataByFieldsFromListMap(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::ListStringMap& iceP_listFieldMapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMap_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMap_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMap_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldMapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMap_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_minsertDataByFieldsFromListMap(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMap_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_minsertDataByFieldsFromListMap(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMap_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_minsertDataByFieldsFromListMapOneway(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::ListStringMap& iceP_listFieldMapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMapOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMapOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldMapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMapOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_minsertDataByFieldsFromListMapOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_minsertDataByFieldsFromListMapOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_deleteDataByID(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_deleteDataByID_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_deleteDataByID_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_deleteDataByID_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_deleteDataByID_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_deleteDataByID(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_deleteDataByID_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_deleteDataByID(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_deleteDataByID_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_deleteDataByIDOneway(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_deleteDataByIDOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_deleteDataByIDOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_deleteDataByIDOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_deleteDataByIDOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_deleteDataByIDOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mdeleteDataByID(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mdeleteDataByID_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mdeleteDataByID_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mdeleteDataByID_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mdeleteDataByID_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mdeleteDataByID(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mdeleteDataByID_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mdeleteDataByID(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mdeleteDataByID_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mdeleteDataByIDOneway(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mdeleteDataByIDOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mdeleteDataByIDOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mdeleteDataByIDOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPRTData::end_mdeleteDataByIDOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPRTData_mdeleteDataByIDOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_deleteDataByField(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::std::string& iceP_fieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_deleteDataByField_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_deleteDataByField_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_deleteDataByField_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_fieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_deleteDataByField_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_deleteDataByField(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_deleteDataByField_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_deleteDataByField(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_deleteDataByField_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_deleteDataByFields(const ::std::string& iceP_tableName, const ::std::string& iceP_id, const ::ZG6000::StringList& iceP_listFieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_deleteDataByFields_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_deleteDataByFields_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_deleteDataByFields_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_id);
        ostr->write(iceP_listFieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_deleteDataByFields_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_deleteDataByFields(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_deleteDataByFields_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_deleteDataByFields(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_deleteDataByFields_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mdeleteDataByField(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::std::string& iceP_fieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mdeleteDataByField_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mdeleteDataByField_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mdeleteDataByField_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_fieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mdeleteDataByField_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mdeleteDataByField(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mdeleteDataByField_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mdeleteDataByField(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mdeleteDataByField_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPRTData::_iceI_begin_mdeleteDataByFields(const ::std::string& iceP_tableName, const ::ZG6000::StringList& iceP_listID, const ::ZG6000::StringList& iceP_listFieldName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPRTData_mdeleteDataByFields_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPRTData_mdeleteDataByFields_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPRTData_mdeleteDataByFields_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_listID);
        ostr->write(iceP_listFieldName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPRTData_mdeleteDataByFields_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPRTData::end_mdeleteDataByFields(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mdeleteDataByFields_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPRTData::_iceI_end_mdeleteDataByFields(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPRTData_mdeleteDataByFields_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSPRTData::_newInstance() const
{
    return new ZGSPRTData;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSPRTData::ice_staticId()
{
    return ::ZG6000::ZGSPRTData::ice_staticId();
}

ZG6000::ZGSPRTData::~ZGSPRTData()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSPRTData* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPRTData_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPRTData",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSPRTData::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPRTData_ids, iceC_ZG6000_ZGSPRTData_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSPRTData::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSPRTData_ids[0], &iceC_ZG6000_ZGSPRTData_ids[3]);
}

const ::std::string&
ZG6000::ZGSPRTData::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPRTData::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSPRTData";
    return typeId;
#else
    return iceC_ZG6000_ZGSPRTData_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByKeyToValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_key;
    istr->read(iceP_key);
    inS.endReadParams();
    ::std::string iceP_value;
    ErrorInfo iceP_e;
    bool ret = this->getDataByKeyToValue(iceP_key, iceP_value, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_value);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByKeyToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_key;
    istr->read(iceP_key);
    inS.endReadParams();
    ::std::string iceP_jsonValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByKeyToJson(iceP_key, iceP_jsonValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_jsonValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_setDataByKeyFromValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_key;
    ::std::string iceP_value;
    istr->read(iceP_key);
    istr->read(iceP_value);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setDataByKeyFromValue(iceP_key, iceP_value, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_setDataByKeyFromValueOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_key;
    ::std::string iceP_value;
    istr->read(iceP_key);
    istr->read(iceP_value);
    inS.endReadParams();
    this->setDataByKeyFromValueOneway(iceP_key, iceP_value, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByKeyToList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listKey;
    istr->read(iceP_listKey);
    inS.endReadParams();
    StringList iceP_listValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByKeyToList(iceP_listKey, iceP_listValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByKeyToMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listKey;
    istr->read(iceP_listKey);
    inS.endReadParams();
    StringMap iceP_mapValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByKeyToMap(iceP_listKey, iceP_mapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_mapValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByKeyToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listKey;
    istr->read(iceP_listKey);
    inS.endReadParams();
    ::std::string iceP_jsonValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByKeyToJson(iceP_listKey, iceP_jsonValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_jsonValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_msetDataByKeyFromList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listKey;
    StringList iceP_listValue;
    istr->read(iceP_listKey);
    istr->read(iceP_listValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->msetDataByKeyFromList(iceP_listKey, iceP_listValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_msetDataByKeyFromListOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listKey;
    StringList iceP_listValue;
    istr->read(iceP_listKey);
    istr->read(iceP_listValue);
    inS.endReadParams();
    this->msetDataByKeyFromListOneway(iceP_listKey, iceP_listValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_msetDataByKeyFromMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_keyValue;
    istr->read(iceP_keyValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->msetDataByKeyFromMap(iceP_keyValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_msetDataByKeyFromMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_keyValue;
    istr->read(iceP_keyValue);
    inS.endReadParams();
    this->msetDataByKeyFromMapOneway(iceP_keyValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByIDToMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    inS.endReadParams();
    StringMap iceP_mapValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByIDToMap(iceP_tableName, iceP_id, iceP_mapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_mapValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByIDToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    inS.endReadParams();
    ::std::string iceP_jsonVaule;
    ErrorInfo iceP_e;
    bool ret = this->getDataByIDToJson(iceP_tableName, iceP_id, iceP_jsonVaule, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_jsonVaule);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByFieldToValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_fieldName);
    inS.endReadParams();
    ::std::string iceP_fieldValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByFieldToValue(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_fieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByFieldToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_fieldName);
    inS.endReadParams();
    ::std::string iceP_jsonFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByFieldToJson(iceP_tableName, iceP_id, iceP_fieldName, iceP_jsonFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_jsonFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByFieldsToList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringList iceP_listFieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_listFieldName);
    inS.endReadParams();
    StringList iceP_listFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByFieldsToList(iceP_tableName, iceP_id, iceP_listFieldName, iceP_listFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByFieldsToMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringList iceP_listFieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_listFieldName);
    inS.endReadParams();
    StringMap iceP_mapFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByFieldsToMap(iceP_tableName, iceP_id, iceP_listFieldName, iceP_mapFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_mapFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_getDataByFieldsToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringList iceP_listFieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_listFieldName);
    inS.endReadParams();
    ::std::string iceP_jsonFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->getDataByFieldsToJson(iceP_tableName, iceP_id, iceP_listFieldName, iceP_jsonFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_jsonFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByIDToListList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    inS.endReadParams();
    ListStringList iceP_listFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByIDToListList(iceP_tableName, iceP_listID, iceP_listFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByIDToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    inS.endReadParams();
    ::std::string iceP_jsonFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByIDToJson(iceP_tableName, iceP_listID, iceP_jsonFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_jsonFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByIDToListMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    inS.endReadParams();
    ListStringMap iceP_listFieldMapValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByIDToListMap(iceP_tableName, iceP_listID, iceP_listFieldMapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listFieldMapValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByIDToMapMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    inS.endReadParams();
    MapStringMap iceP_mapFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByIDToMapMap(iceP_tableName, iceP_listID, iceP_mapFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_mapFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldToList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldName);
    inS.endReadParams();
    StringList iceP_listFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldToList(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldToMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldName);
    inS.endReadParams();
    StringMap iceP_mapValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldToMap(iceP_tableName, iceP_listID, iceP_fieldName, iceP_mapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_mapValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldName);
    inS.endReadParams();
    ::std::string iceP_jsonFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldToJson(iceP_tableName, iceP_listID, iceP_fieldName, iceP_jsonFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_jsonFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldsToListList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldName);
    inS.endReadParams();
    ListStringList iceP_listFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldsToListList(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_listFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldsToJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldName);
    inS.endReadParams();
    ::std::string iceP_jsonFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldsToJson(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_jsonFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_jsonFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldsToListMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldName);
    inS.endReadParams();
    ListStringMap iceP_listFieldMapValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldsToListMap(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_listFieldMapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listFieldMapValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mgetDataByFieldsToMapMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Idempotent, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldName);
    inS.endReadParams();
    MapStringMap iceP_mapFieldValue;
    ErrorInfo iceP_e;
    bool ret = this->mgetDataByFieldsToMapMap(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_mapFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_mapFieldValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataByFieldFromValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_fieldName);
    istr->read(iceP_fieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateDataByFieldFromValue(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataByFieldFromValueOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_fieldName);
    istr->read(iceP_fieldValue);
    inS.endReadParams();
    this->updateDataByFieldFromValueOneway(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataByIDFromMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringMap iceP_mapValue;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_mapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateDataByIDFromMap(iceP_tableName, iceP_id, iceP_mapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataByIDFromMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringMap iceP_mapValue;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_mapValue);
    inS.endReadParams();
    this->updateDataByIDFromMapOneway(iceP_tableName, iceP_id, iceP_mapValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldFromList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    StringList iceP_listFieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldName);
    istr->read(iceP_listFieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateDataByFieldFromList(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldFromListOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    StringList iceP_listFieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldName);
    istr->read(iceP_listFieldValue);
    inS.endReadParams();
    this->mupdateDataByFieldFromListOneway(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldFromValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldName);
    istr->read(iceP_fieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateDataByFieldFromValue(iceP_tableName, iceP_listID, iceP_fieldName, iceP_fieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldFromValueOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldName);
    istr->read(iceP_fieldValue);
    inS.endReadParams();
    this->mupdateDataByFieldFromValueOneway(iceP_tableName, iceP_listID, iceP_fieldName, iceP_fieldValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateValueByFieldsFromListList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    ListStringList iceP_listFieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldName);
    istr->read(iceP_listFieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateValueByFieldsFromListList(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_listFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateValueByFieldsFromListListOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    ListStringList iceP_listFieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldName);
    istr->read(iceP_listFieldValue);
    inS.endReadParams();
    this->mupdateValueByFieldsFromListListOneway(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_listFieldValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldsFromListMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ListStringMap iceP_listFieldMapValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldMapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateDataByFieldsFromListMap(iceP_tableName, iceP_listID, iceP_listFieldMapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldsFromListMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ListStringMap iceP_listFieldMapValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldMapValue);
    inS.endReadParams();
    this->mupdateDataByFieldsFromListMapOneway(iceP_tableName, iceP_listID, iceP_listFieldMapValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldsFromMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringMap iceP_fieldMapValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldMapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateDataByFieldsFromMap(iceP_tableName, iceP_listID, iceP_fieldMapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataByFieldsFromMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringMap iceP_fieldMapValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldMapValue);
    inS.endReadParams();
    this->mupdateDataByFieldsFromMapOneway(iceP_tableName, iceP_listID, iceP_fieldMapValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_oldID;
    ::std::string iceP_newID;
    istr->read(iceP_tableName);
    istr->read(iceP_oldID);
    istr->read(iceP_newID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateDataID(iceP_tableName, iceP_oldID, iceP_newID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_updateDataIDOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_oldID;
    ::std::string iceP_newID;
    istr->read(iceP_tableName);
    istr->read(iceP_oldID);
    istr->read(iceP_newID);
    inS.endReadParams();
    this->updateDataIDOneway(iceP_tableName, iceP_oldID, iceP_newID, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listOldID;
    StringList iceP_listNewID;
    istr->read(iceP_tableName);
    istr->read(iceP_listOldID);
    istr->read(iceP_listNewID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateDataID(iceP_tableName, iceP_listOldID, iceP_listNewID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mupdateDataIDOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listOldID;
    StringList iceP_listNewID;
    istr->read(iceP_tableName);
    istr->read(iceP_listOldID);
    istr->read(iceP_listNewID);
    inS.endReadParams();
    this->mupdateDataIDOneway(iceP_tableName, iceP_listOldID, iceP_listNewID, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_insertDataByFieldFromValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_fieldName);
    istr->read(iceP_fieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->insertDataByFieldFromValue(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_insertDataByFieldFromValueOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    ::std::string iceP_fieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_fieldName);
    istr->read(iceP_fieldValue);
    inS.endReadParams();
    this->insertDataByFieldFromValueOneway(iceP_tableName, iceP_id, iceP_fieldName, iceP_fieldValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_insertDataByIDFromMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringMap iceP_mapValue;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_mapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->insertDataByIDFromMap(iceP_tableName, iceP_id, iceP_mapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_insertDataByIDFromMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringMap iceP_mapValue;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_mapValue);
    inS.endReadParams();
    this->insertDataByIDFromMapOneway(iceP_tableName, iceP_id, iceP_mapValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_minsertDataByFieldFromList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    StringList iceP_listFieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldName);
    istr->read(iceP_listFieldValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->minsertDataByFieldFromList(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_minsertDataByFieldFromListOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    StringList iceP_listFieldValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldName);
    istr->read(iceP_listFieldValue);
    inS.endReadParams();
    this->minsertDataByFieldFromListOneway(iceP_tableName, iceP_listID, iceP_fieldName, iceP_listFieldValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_minsertDataByFieldsFromListMap(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ListStringMap iceP_listFieldMapValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldMapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->minsertDataByFieldsFromListMap(iceP_tableName, iceP_listID, iceP_listFieldMapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_minsertDataByFieldsFromListMapOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ListStringMap iceP_listFieldMapValue;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldMapValue);
    inS.endReadParams();
    this->minsertDataByFieldsFromListMapOneway(iceP_tableName, iceP_listID, iceP_listFieldMapValue, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_deleteDataByID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteDataByID(iceP_tableName, iceP_id, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_deleteDataByIDOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    inS.endReadParams();
    this->deleteDataByIDOneway(iceP_tableName, iceP_id, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mdeleteDataByID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mdeleteDataByID(iceP_tableName, iceP_listID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mdeleteDataByIDOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    inS.endReadParams();
    this->mdeleteDataByIDOneway(iceP_tableName, iceP_listID, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_deleteDataByField(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    ::std::string iceP_fieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_fieldName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteDataByField(iceP_tableName, iceP_id, iceP_fieldName, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_deleteDataByFields(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_id;
    StringList iceP_listFieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_id);
    istr->read(iceP_listFieldName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteDataByFields(iceP_tableName, iceP_id, iceP_listFieldName, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mdeleteDataByField(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    ::std::string iceP_fieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_fieldName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mdeleteDataByField(iceP_tableName, iceP_listID, iceP_fieldName, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceD_mdeleteDataByFields(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    StringList iceP_listID;
    StringList iceP_listFieldName;
    istr->read(iceP_tableName);
    istr->read(iceP_listID);
    istr->read(iceP_listFieldName);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mdeleteDataByFields(iceP_tableName, iceP_listID, iceP_listFieldName, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPRTData_all[] =
{
    "checkState",
    "deleteDataByField",
    "deleteDataByFields",
    "deleteDataByID",
    "deleteDataByIDOneway",
    "dispatchData",
    "exitApp",
    "getDataByFieldToJson",
    "getDataByFieldToValue",
    "getDataByFieldsToJson",
    "getDataByFieldsToList",
    "getDataByFieldsToMap",
    "getDataByIDToJson",
    "getDataByIDToMap",
    "getDataByKeyToJson",
    "getDataByKeyToValue",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "insertDataByFieldFromValue",
    "insertDataByFieldFromValueOneway",
    "insertDataByIDFromMap",
    "insertDataByIDFromMapOneway",
    "isDebugging",
    "mdeleteDataByField",
    "mdeleteDataByFields",
    "mdeleteDataByID",
    "mdeleteDataByIDOneway",
    "mgetDataByFieldToJson",
    "mgetDataByFieldToList",
    "mgetDataByFieldToMap",
    "mgetDataByFieldsToJson",
    "mgetDataByFieldsToListList",
    "mgetDataByFieldsToListMap",
    "mgetDataByFieldsToMapMap",
    "mgetDataByIDToJson",
    "mgetDataByIDToListList",
    "mgetDataByIDToListMap",
    "mgetDataByIDToMapMap",
    "mgetDataByKeyToJson",
    "mgetDataByKeyToList",
    "mgetDataByKeyToMap",
    "minsertDataByFieldFromList",
    "minsertDataByFieldFromListOneway",
    "minsertDataByFieldsFromListMap",
    "minsertDataByFieldsFromListMapOneway",
    "msetDataByKeyFromList",
    "msetDataByKeyFromListOneway",
    "msetDataByKeyFromMap",
    "msetDataByKeyFromMapOneway",
    "mupdateDataByFieldFromList",
    "mupdateDataByFieldFromListOneway",
    "mupdateDataByFieldFromValue",
    "mupdateDataByFieldFromValueOneway",
    "mupdateDataByFieldsFromListMap",
    "mupdateDataByFieldsFromListMapOneway",
    "mupdateDataByFieldsFromMap",
    "mupdateDataByFieldsFromMapOneway",
    "mupdateDataID",
    "mupdateDataIDOneway",
    "mupdateValueByFieldsFromListList",
    "mupdateValueByFieldsFromListListOneway",
    "pauseDebug",
    "resumeDebug",
    "setDataByKeyFromValue",
    "setDataByKeyFromValueOneway",
    "startDebug",
    "stopDebug",
    "test",
    "updateDataByFieldFromValue",
    "updateDataByFieldFromValueOneway",
    "updateDataByIDFromMap",
    "updateDataByIDFromMapOneway",
    "updateDataID",
    "updateDataIDOneway"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSPRTData::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPRTData_all, iceC_ZG6000_ZGSPRTData_all + 78, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPRTData_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_deleteDataByField(in, current);
        }
        case 2:
        {
            return _iceD_deleteDataByFields(in, current);
        }
        case 3:
        {
            return _iceD_deleteDataByID(in, current);
        }
        case 4:
        {
            return _iceD_deleteDataByIDOneway(in, current);
        }
        case 5:
        {
            return _iceD_dispatchData(in, current);
        }
        case 6:
        {
            return _iceD_exitApp(in, current);
        }
        case 7:
        {
            return _iceD_getDataByFieldToJson(in, current);
        }
        case 8:
        {
            return _iceD_getDataByFieldToValue(in, current);
        }
        case 9:
        {
            return _iceD_getDataByFieldsToJson(in, current);
        }
        case 10:
        {
            return _iceD_getDataByFieldsToList(in, current);
        }
        case 11:
        {
            return _iceD_getDataByFieldsToMap(in, current);
        }
        case 12:
        {
            return _iceD_getDataByIDToJson(in, current);
        }
        case 13:
        {
            return _iceD_getDataByIDToMap(in, current);
        }
        case 14:
        {
            return _iceD_getDataByKeyToJson(in, current);
        }
        case 15:
        {
            return _iceD_getDataByKeyToValue(in, current);
        }
        case 16:
        {
            return _iceD_getVersion(in, current);
        }
        case 17:
        {
            return _iceD_heartDebug(in, current);
        }
        case 18:
        {
            return _iceD_ice_id(in, current);
        }
        case 19:
        {
            return _iceD_ice_ids(in, current);
        }
        case 20:
        {
            return _iceD_ice_isA(in, current);
        }
        case 21:
        {
            return _iceD_ice_ping(in, current);
        }
        case 22:
        {
            return _iceD_insertDataByFieldFromValue(in, current);
        }
        case 23:
        {
            return _iceD_insertDataByFieldFromValueOneway(in, current);
        }
        case 24:
        {
            return _iceD_insertDataByIDFromMap(in, current);
        }
        case 25:
        {
            return _iceD_insertDataByIDFromMapOneway(in, current);
        }
        case 26:
        {
            return _iceD_isDebugging(in, current);
        }
        case 27:
        {
            return _iceD_mdeleteDataByField(in, current);
        }
        case 28:
        {
            return _iceD_mdeleteDataByFields(in, current);
        }
        case 29:
        {
            return _iceD_mdeleteDataByID(in, current);
        }
        case 30:
        {
            return _iceD_mdeleteDataByIDOneway(in, current);
        }
        case 31:
        {
            return _iceD_mgetDataByFieldToJson(in, current);
        }
        case 32:
        {
            return _iceD_mgetDataByFieldToList(in, current);
        }
        case 33:
        {
            return _iceD_mgetDataByFieldToMap(in, current);
        }
        case 34:
        {
            return _iceD_mgetDataByFieldsToJson(in, current);
        }
        case 35:
        {
            return _iceD_mgetDataByFieldsToListList(in, current);
        }
        case 36:
        {
            return _iceD_mgetDataByFieldsToListMap(in, current);
        }
        case 37:
        {
            return _iceD_mgetDataByFieldsToMapMap(in, current);
        }
        case 38:
        {
            return _iceD_mgetDataByIDToJson(in, current);
        }
        case 39:
        {
            return _iceD_mgetDataByIDToListList(in, current);
        }
        case 40:
        {
            return _iceD_mgetDataByIDToListMap(in, current);
        }
        case 41:
        {
            return _iceD_mgetDataByIDToMapMap(in, current);
        }
        case 42:
        {
            return _iceD_mgetDataByKeyToJson(in, current);
        }
        case 43:
        {
            return _iceD_mgetDataByKeyToList(in, current);
        }
        case 44:
        {
            return _iceD_mgetDataByKeyToMap(in, current);
        }
        case 45:
        {
            return _iceD_minsertDataByFieldFromList(in, current);
        }
        case 46:
        {
            return _iceD_minsertDataByFieldFromListOneway(in, current);
        }
        case 47:
        {
            return _iceD_minsertDataByFieldsFromListMap(in, current);
        }
        case 48:
        {
            return _iceD_minsertDataByFieldsFromListMapOneway(in, current);
        }
        case 49:
        {
            return _iceD_msetDataByKeyFromList(in, current);
        }
        case 50:
        {
            return _iceD_msetDataByKeyFromListOneway(in, current);
        }
        case 51:
        {
            return _iceD_msetDataByKeyFromMap(in, current);
        }
        case 52:
        {
            return _iceD_msetDataByKeyFromMapOneway(in, current);
        }
        case 53:
        {
            return _iceD_mupdateDataByFieldFromList(in, current);
        }
        case 54:
        {
            return _iceD_mupdateDataByFieldFromListOneway(in, current);
        }
        case 55:
        {
            return _iceD_mupdateDataByFieldFromValue(in, current);
        }
        case 56:
        {
            return _iceD_mupdateDataByFieldFromValueOneway(in, current);
        }
        case 57:
        {
            return _iceD_mupdateDataByFieldsFromListMap(in, current);
        }
        case 58:
        {
            return _iceD_mupdateDataByFieldsFromListMapOneway(in, current);
        }
        case 59:
        {
            return _iceD_mupdateDataByFieldsFromMap(in, current);
        }
        case 60:
        {
            return _iceD_mupdateDataByFieldsFromMapOneway(in, current);
        }
        case 61:
        {
            return _iceD_mupdateDataID(in, current);
        }
        case 62:
        {
            return _iceD_mupdateDataIDOneway(in, current);
        }
        case 63:
        {
            return _iceD_mupdateValueByFieldsFromListList(in, current);
        }
        case 64:
        {
            return _iceD_mupdateValueByFieldsFromListListOneway(in, current);
        }
        case 65:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 66:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 67:
        {
            return _iceD_setDataByKeyFromValue(in, current);
        }
        case 68:
        {
            return _iceD_setDataByKeyFromValueOneway(in, current);
        }
        case 69:
        {
            return _iceD_startDebug(in, current);
        }
        case 70:
        {
            return _iceD_stopDebug(in, current);
        }
        case 71:
        {
            return _iceD_test(in, current);
        }
        case 72:
        {
            return _iceD_updateDataByFieldFromValue(in, current);
        }
        case 73:
        {
            return _iceD_updateDataByFieldFromValueOneway(in, current);
        }
        case 74:
        {
            return _iceD_updateDataByIDFromMap(in, current);
        }
        case 75:
        {
            return _iceD_updateDataByIDFromMapOneway(in, current);
        }
        case 76:
        {
            return _iceD_updateDataID(in, current);
        }
        case 77:
        {
            return _iceD_updateDataIDOneway(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSPRTData::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSPRTData, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSPRTData::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSPRTData, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSPRTDataPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSPRTDataPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSPRTData::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

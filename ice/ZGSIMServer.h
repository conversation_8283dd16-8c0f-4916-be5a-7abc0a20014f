//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSIMServer.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSIMServer_h__
#define __ZGSIMServer_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSIMServer;
class ZGSIMServerPrx;

}

namespace ZG6000
{

class ZGSIMServer : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSIMServerPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getServerState.
     */
    struct GetServerStateResult
    {
        bool returnValue;
        StringMap mapValue;
        ErrorInfo e;
    };

    virtual bool getServerState(StringMap& mapValue, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getServerState(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setServerAction.
     */
    struct SetServerActionResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setServerAction(StringMap mapValue, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setServerAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDeviceList.
     */
    struct GetDeviceListResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getDeviceList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setDeviceAction.
     */
    struct SetDeviceActionResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setDeviceAction(ListStringMap listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setDeviceAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPrimaryList.
     */
    struct GetPrimaryListResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getPrimaryList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPrimaryList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPortList.
     */
    struct GetPortListResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getPortList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPortList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDatasetList.
     */
    struct GetDatasetListResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getDatasetList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDatasetList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getUserGroupList.
     */
    struct GetUserGroupListResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getUserGroupList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserGroupList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setPrimaryAction.
     */
    struct SetPrimaryActionResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setPrimaryAction(ListStringMap listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setPrimaryAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setPortAction.
     */
    struct SetPortActionResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setPortAction(ListStringMap listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setPortAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setDatasetAction.
     */
    struct SetDatasetActionResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setDatasetAction(ListStringMap listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setDatasetAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setUserGroupAction.
     */
    struct SetUserGroupActionResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setUserGroupAction(ListStringMap listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setUserGroupAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getSignalValue.
     */
    struct GetSignalValueResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getSignalValue(int dataType, StringList listDataID, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getSignalValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setSignalValue.
     */
    struct SetSignalValueResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setSignalValue(int dataType, ListStringMap listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setSignalValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDeviceData.
     */
    struct GetDeviceDataResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getDeviceData(int dataType, ::std::string deviceID, int page, int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPrimaryData.
     */
    struct GetPrimaryDataResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getPrimaryData(int dataType, ::std::string primaryID, int page, int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPrimaryData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPortData.
     */
    struct GetPortDataResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getPortData(int dataType, ::std::string portID, int page, int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPortData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDatasetData.
     */
    struct GetDatasetDataResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getDatasetData(int dataType, ::std::string datasetID, int page, int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDatasetData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getUserGroupData.
     */
    struct GetUserGroupDataResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getUserGroupData(int dataType, ::std::string userGroupID, int page, int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserGroupData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDeviceDataNum.
     */
    struct GetDeviceDataNumResult
    {
        bool returnValue;
        int num;
        ErrorInfo e;
    };

    virtual bool getDeviceDataNum(int dataType, ::std::string deviceID, int& num, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceDataNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPrimaryDataNum.
     */
    struct GetPrimaryDataNumResult
    {
        bool returnValue;
        int num;
        ErrorInfo e;
    };

    virtual bool getPrimaryDataNum(int dataType, ::std::string primaryID, int& num, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPrimaryDataNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPortDataNum.
     */
    struct GetPortDataNumResult
    {
        bool returnValue;
        int num;
        ErrorInfo e;
    };

    virtual bool getPortDataNum(int dataType, ::std::string portID, int& num, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPortDataNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDatasetDataNum.
     */
    struct GetDatasetDataNumResult
    {
        bool returnValue;
        int num;
        ErrorInfo e;
    };

    virtual bool getDatasetDataNum(int dataType, ::std::string datasetID, int& num, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDatasetDataNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getUserGroupDataNum.
     */
    struct GetUserGroupDataNumResult
    {
        bool returnValue;
        int num;
        ErrorInfo e;
    };

    virtual bool getUserGroupDataNum(int dataType, ::std::string userGroupID, int& num, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserGroupDataNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getNetInterface.
     */
    struct GetNetInterfaceResult
    {
        bool returnValue;
        StringList listName;
        ErrorInfo e;
    };

    virtual bool getNetInterface(StringList& listName, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getNetInterface(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getNetworkAddr.
     */
    struct GetNetworkAddrResult
    {
        bool returnValue;
        ListStringMap listIPv4;
        ErrorInfo e;
    };

    virtual bool getNetworkAddr(::std::string name, ListStringMap& listIPv4, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getNetworkAddr(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to addNetworkAddr.
     */
    struct AddNetworkAddrResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool addNetworkAddr(::std::string name, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_addNetworkAddr(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to removeNetworkAddr.
     */
    struct RemoveNetworkAddrResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool removeNetworkAddr(::std::string name, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_removeNetworkAddr(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to addNetworkAddrEx.
     */
    struct AddNetworkAddrExResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool addNetworkAddrEx(::std::string name, ListStringMap listIPv4, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_addNetworkAddrEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to removeNetworkAddrEx.
     */
    struct RemoveNetworkAddrExResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool removeNetworkAddrEx(::std::string name, ListStringMap listIPv4, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_removeNetworkAddrEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to bindNetInterface.
     */
    struct BindNetInterfaceResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool bindNetInterface(::std::string name, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_bindNetInterface(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getTypeList.
     */
    struct GetTypeListResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getTypeList(int type, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTypeList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDeviceRun.
     */
    struct GetDeviceRunResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getDeviceRun(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceRun(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setDeviceRun.
     */
    struct SetDeviceRunResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setDeviceRun(ListStringMap listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setDeviceRun(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to addTaskList.
     */
    struct AddTaskListResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool addTaskList(int type, StringList listID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_addTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deleteTaskList.
     */
    struct DeleteTaskListResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool deleteTaskList(int type, StringList listID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to clearTaskList.
     */
    struct ClearTaskListResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool clearTaskList(int type, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_clearTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getTaskList.
     */
    struct GetTaskListResult
    {
        bool returnValue;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool getTaskList(int type, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setTaskTest.
     */
    struct SetTaskTestResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setTaskTest(int type, int testAction, ::std::string option, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setTaskTest(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getTaskTest.
     */
    struct GetTaskTestResult
    {
        bool returnValue;
        int testState;
        ErrorInfo e;
    };

    virtual bool getTaskTest(int type, int& testState, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskTest(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSIMServerPrx : public virtual ::Ice::Proxy<ZGSIMServerPrx, ZGServerBasePrx>
{
public:

    bool getServerState(StringMap& mapValue, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetServerStateResult>(true, this, &ZGSIMServerPrx::_iceI_getServerState, context).get();
        mapValue = ::std::move(_result.mapValue);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getServerStateAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetServerStateResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetServerStateResult, P>(false, this, &ZGSIMServerPrx::_iceI_getServerState, context);
    }

    ::std::function<void()>
    getServerStateAsync(::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetServerStateResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.mapValue), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetServerStateResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getServerState, context);
    }

    /// \cond INTERNAL
    void _iceI_getServerState(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetServerStateResult>>&, const ::Ice::Context&);
    /// \endcond

    bool setServerAction(const StringMap& mapValue, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::SetServerActionResult>(true, this, &ZGSIMServerPrx::_iceI_setServerAction, mapValue, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setServerActionAsync(const StringMap& mapValue, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::SetServerActionResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::SetServerActionResult, P>(false, this, &ZGSIMServerPrx::_iceI_setServerAction, mapValue, context);
    }

    ::std::function<void()>
    setServerActionAsync(const StringMap& mapValue,
                         ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::SetServerActionResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::SetServerActionResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_setServerAction, mapValue, context);
    }

    /// \cond INTERNAL
    void _iceI_setServerAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetServerActionResult>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool getDeviceList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetDeviceListResult>(true, this, &ZGSIMServerPrx::_iceI_getDeviceList, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDeviceListAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetDeviceListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetDeviceListResult, P>(false, this, &ZGSIMServerPrx::_iceI_getDeviceList, context);
    }

    ::std::function<void()>
    getDeviceListAsync(::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetDeviceListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetDeviceListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getDeviceList, context);
    }

    /// \cond INTERNAL
    void _iceI_getDeviceList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDeviceListResult>>&, const ::Ice::Context&);
    /// \endcond

    bool setDeviceAction(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::SetDeviceActionResult>(true, this, &ZGSIMServerPrx::_iceI_setDeviceAction, listItem, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setDeviceActionAsync(const ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::SetDeviceActionResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::SetDeviceActionResult, P>(false, this, &ZGSIMServerPrx::_iceI_setDeviceAction, listItem, context);
    }

    ::std::function<void()>
    setDeviceActionAsync(const ListStringMap& listItem,
                         ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::SetDeviceActionResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::SetDeviceActionResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_setDeviceAction, listItem, context);
    }

    /// \cond INTERNAL
    void _iceI_setDeviceAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetDeviceActionResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool getPrimaryList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetPrimaryListResult>(true, this, &ZGSIMServerPrx::_iceI_getPrimaryList, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPrimaryListAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetPrimaryListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetPrimaryListResult, P>(false, this, &ZGSIMServerPrx::_iceI_getPrimaryList, context);
    }

    ::std::function<void()>
    getPrimaryListAsync(::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetPrimaryListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetPrimaryListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getPrimaryList, context);
    }

    /// \cond INTERNAL
    void _iceI_getPrimaryList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPrimaryListResult>>&, const ::Ice::Context&);
    /// \endcond

    bool getPortList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetPortListResult>(true, this, &ZGSIMServerPrx::_iceI_getPortList, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPortListAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetPortListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetPortListResult, P>(false, this, &ZGSIMServerPrx::_iceI_getPortList, context);
    }

    ::std::function<void()>
    getPortListAsync(::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetPortListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetPortListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getPortList, context);
    }

    /// \cond INTERNAL
    void _iceI_getPortList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPortListResult>>&, const ::Ice::Context&);
    /// \endcond

    bool getDatasetList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetDatasetListResult>(true, this, &ZGSIMServerPrx::_iceI_getDatasetList, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDatasetListAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetDatasetListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetDatasetListResult, P>(false, this, &ZGSIMServerPrx::_iceI_getDatasetList, context);
    }

    ::std::function<void()>
    getDatasetListAsync(::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetDatasetListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetDatasetListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getDatasetList, context);
    }

    /// \cond INTERNAL
    void _iceI_getDatasetList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDatasetListResult>>&, const ::Ice::Context&);
    /// \endcond

    bool getUserGroupList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetUserGroupListResult>(true, this, &ZGSIMServerPrx::_iceI_getUserGroupList, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getUserGroupListAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetUserGroupListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetUserGroupListResult, P>(false, this, &ZGSIMServerPrx::_iceI_getUserGroupList, context);
    }

    ::std::function<void()>
    getUserGroupListAsync(::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetUserGroupListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetUserGroupListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getUserGroupList, context);
    }

    /// \cond INTERNAL
    void _iceI_getUserGroupList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetUserGroupListResult>>&, const ::Ice::Context&);
    /// \endcond

    bool setPrimaryAction(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::SetPrimaryActionResult>(true, this, &ZGSIMServerPrx::_iceI_setPrimaryAction, listItem, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setPrimaryActionAsync(const ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::SetPrimaryActionResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::SetPrimaryActionResult, P>(false, this, &ZGSIMServerPrx::_iceI_setPrimaryAction, listItem, context);
    }

    ::std::function<void()>
    setPrimaryActionAsync(const ListStringMap& listItem,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::SetPrimaryActionResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::SetPrimaryActionResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_setPrimaryAction, listItem, context);
    }

    /// \cond INTERNAL
    void _iceI_setPrimaryAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetPrimaryActionResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool setPortAction(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::SetPortActionResult>(true, this, &ZGSIMServerPrx::_iceI_setPortAction, listItem, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setPortActionAsync(const ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::SetPortActionResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::SetPortActionResult, P>(false, this, &ZGSIMServerPrx::_iceI_setPortAction, listItem, context);
    }

    ::std::function<void()>
    setPortActionAsync(const ListStringMap& listItem,
                       ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::SetPortActionResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::SetPortActionResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_setPortAction, listItem, context);
    }

    /// \cond INTERNAL
    void _iceI_setPortAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetPortActionResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool setDatasetAction(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::SetDatasetActionResult>(true, this, &ZGSIMServerPrx::_iceI_setDatasetAction, listItem, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setDatasetActionAsync(const ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::SetDatasetActionResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::SetDatasetActionResult, P>(false, this, &ZGSIMServerPrx::_iceI_setDatasetAction, listItem, context);
    }

    ::std::function<void()>
    setDatasetActionAsync(const ListStringMap& listItem,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::SetDatasetActionResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::SetDatasetActionResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_setDatasetAction, listItem, context);
    }

    /// \cond INTERNAL
    void _iceI_setDatasetAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetDatasetActionResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool setUserGroupAction(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::SetUserGroupActionResult>(true, this, &ZGSIMServerPrx::_iceI_setUserGroupAction, listItem, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setUserGroupActionAsync(const ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::SetUserGroupActionResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::SetUserGroupActionResult, P>(false, this, &ZGSIMServerPrx::_iceI_setUserGroupAction, listItem, context);
    }

    ::std::function<void()>
    setUserGroupActionAsync(const ListStringMap& listItem,
                            ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::SetUserGroupActionResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::SetUserGroupActionResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_setUserGroupAction, listItem, context);
    }

    /// \cond INTERNAL
    void _iceI_setUserGroupAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetUserGroupActionResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool getSignalValue(int dataType, const StringList& listDataID, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetSignalValueResult>(true, this, &ZGSIMServerPrx::_iceI_getSignalValue, dataType, listDataID, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getSignalValueAsync(int dataType, const StringList& listDataID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetSignalValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetSignalValueResult, P>(false, this, &ZGSIMServerPrx::_iceI_getSignalValue, dataType, listDataID, context);
    }

    ::std::function<void()>
    getSignalValueAsync(int dataType, const StringList& listDataID,
                        ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetSignalValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetSignalValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getSignalValue, dataType, listDataID, context);
    }

    /// \cond INTERNAL
    void _iceI_getSignalValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetSignalValueResult>>&, int, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool setSignalValue(int dataType, const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::SetSignalValueResult>(true, this, &ZGSIMServerPrx::_iceI_setSignalValue, dataType, listItem, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setSignalValueAsync(int dataType, const ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::SetSignalValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::SetSignalValueResult, P>(false, this, &ZGSIMServerPrx::_iceI_setSignalValue, dataType, listItem, context);
    }

    ::std::function<void()>
    setSignalValueAsync(int dataType, const ListStringMap& listItem,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::SetSignalValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::SetSignalValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_setSignalValue, dataType, listItem, context);
    }

    /// \cond INTERNAL
    void _iceI_setSignalValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetSignalValueResult>>&, int, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool getDeviceData(int dataType, const ::std::string& deviceID, int page, int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetDeviceDataResult>(true, this, &ZGSIMServerPrx::_iceI_getDeviceData, dataType, deviceID, page, limit, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDeviceDataAsync(int dataType, const ::std::string& deviceID, int page, int limit, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetDeviceDataResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetDeviceDataResult, P>(false, this, &ZGSIMServerPrx::_iceI_getDeviceData, dataType, deviceID, page, limit, context);
    }

    ::std::function<void()>
    getDeviceDataAsync(int dataType, const ::std::string& deviceID, int page, int limit,
                       ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetDeviceDataResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetDeviceDataResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getDeviceData, dataType, deviceID, page, limit, context);
    }

    /// \cond INTERNAL
    void _iceI_getDeviceData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDeviceDataResult>>&, int, const ::std::string&, int, int, const ::Ice::Context&);
    /// \endcond

    bool getPrimaryData(int dataType, const ::std::string& primaryID, int page, int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetPrimaryDataResult>(true, this, &ZGSIMServerPrx::_iceI_getPrimaryData, dataType, primaryID, page, limit, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPrimaryDataAsync(int dataType, const ::std::string& primaryID, int page, int limit, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetPrimaryDataResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetPrimaryDataResult, P>(false, this, &ZGSIMServerPrx::_iceI_getPrimaryData, dataType, primaryID, page, limit, context);
    }

    ::std::function<void()>
    getPrimaryDataAsync(int dataType, const ::std::string& primaryID, int page, int limit,
                        ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetPrimaryDataResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetPrimaryDataResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getPrimaryData, dataType, primaryID, page, limit, context);
    }

    /// \cond INTERNAL
    void _iceI_getPrimaryData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPrimaryDataResult>>&, int, const ::std::string&, int, int, const ::Ice::Context&);
    /// \endcond

    bool getPortData(int dataType, const ::std::string& portID, int page, int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetPortDataResult>(true, this, &ZGSIMServerPrx::_iceI_getPortData, dataType, portID, page, limit, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPortDataAsync(int dataType, const ::std::string& portID, int page, int limit, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetPortDataResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetPortDataResult, P>(false, this, &ZGSIMServerPrx::_iceI_getPortData, dataType, portID, page, limit, context);
    }

    ::std::function<void()>
    getPortDataAsync(int dataType, const ::std::string& portID, int page, int limit,
                     ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetPortDataResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetPortDataResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getPortData, dataType, portID, page, limit, context);
    }

    /// \cond INTERNAL
    void _iceI_getPortData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPortDataResult>>&, int, const ::std::string&, int, int, const ::Ice::Context&);
    /// \endcond

    bool getDatasetData(int dataType, const ::std::string& datasetID, int page, int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetDatasetDataResult>(true, this, &ZGSIMServerPrx::_iceI_getDatasetData, dataType, datasetID, page, limit, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDatasetDataAsync(int dataType, const ::std::string& datasetID, int page, int limit, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetDatasetDataResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetDatasetDataResult, P>(false, this, &ZGSIMServerPrx::_iceI_getDatasetData, dataType, datasetID, page, limit, context);
    }

    ::std::function<void()>
    getDatasetDataAsync(int dataType, const ::std::string& datasetID, int page, int limit,
                        ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetDatasetDataResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetDatasetDataResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getDatasetData, dataType, datasetID, page, limit, context);
    }

    /// \cond INTERNAL
    void _iceI_getDatasetData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDatasetDataResult>>&, int, const ::std::string&, int, int, const ::Ice::Context&);
    /// \endcond

    bool getUserGroupData(int dataType, const ::std::string& userGroupID, int page, int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetUserGroupDataResult>(true, this, &ZGSIMServerPrx::_iceI_getUserGroupData, dataType, userGroupID, page, limit, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getUserGroupDataAsync(int dataType, const ::std::string& userGroupID, int page, int limit, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetUserGroupDataResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetUserGroupDataResult, P>(false, this, &ZGSIMServerPrx::_iceI_getUserGroupData, dataType, userGroupID, page, limit, context);
    }

    ::std::function<void()>
    getUserGroupDataAsync(int dataType, const ::std::string& userGroupID, int page, int limit,
                          ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetUserGroupDataResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetUserGroupDataResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getUserGroupData, dataType, userGroupID, page, limit, context);
    }

    /// \cond INTERNAL
    void _iceI_getUserGroupData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetUserGroupDataResult>>&, int, const ::std::string&, int, int, const ::Ice::Context&);
    /// \endcond

    bool getDeviceDataNum(int dataType, const ::std::string& deviceID, int& num, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetDeviceDataNumResult>(true, this, &ZGSIMServerPrx::_iceI_getDeviceDataNum, dataType, deviceID, context).get();
        num = _result.num;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDeviceDataNumAsync(int dataType, const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetDeviceDataNumResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetDeviceDataNumResult, P>(false, this, &ZGSIMServerPrx::_iceI_getDeviceDataNum, dataType, deviceID, context);
    }

    ::std::function<void()>
    getDeviceDataNumAsync(int dataType, const ::std::string& deviceID,
                          ::std::function<void(bool, int, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetDeviceDataNumResult&& _result)
        {
            response(_result.returnValue, _result.num, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetDeviceDataNumResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getDeviceDataNum, dataType, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getDeviceDataNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDeviceDataNumResult>>&, int, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getPrimaryDataNum(int dataType, const ::std::string& primaryID, int& num, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetPrimaryDataNumResult>(true, this, &ZGSIMServerPrx::_iceI_getPrimaryDataNum, dataType, primaryID, context).get();
        num = _result.num;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPrimaryDataNumAsync(int dataType, const ::std::string& primaryID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetPrimaryDataNumResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetPrimaryDataNumResult, P>(false, this, &ZGSIMServerPrx::_iceI_getPrimaryDataNum, dataType, primaryID, context);
    }

    ::std::function<void()>
    getPrimaryDataNumAsync(int dataType, const ::std::string& primaryID,
                           ::std::function<void(bool, int, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetPrimaryDataNumResult&& _result)
        {
            response(_result.returnValue, _result.num, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetPrimaryDataNumResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getPrimaryDataNum, dataType, primaryID, context);
    }

    /// \cond INTERNAL
    void _iceI_getPrimaryDataNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPrimaryDataNumResult>>&, int, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getPortDataNum(int dataType, const ::std::string& portID, int& num, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetPortDataNumResult>(true, this, &ZGSIMServerPrx::_iceI_getPortDataNum, dataType, portID, context).get();
        num = _result.num;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPortDataNumAsync(int dataType, const ::std::string& portID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetPortDataNumResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetPortDataNumResult, P>(false, this, &ZGSIMServerPrx::_iceI_getPortDataNum, dataType, portID, context);
    }

    ::std::function<void()>
    getPortDataNumAsync(int dataType, const ::std::string& portID,
                        ::std::function<void(bool, int, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetPortDataNumResult&& _result)
        {
            response(_result.returnValue, _result.num, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetPortDataNumResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getPortDataNum, dataType, portID, context);
    }

    /// \cond INTERNAL
    void _iceI_getPortDataNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPortDataNumResult>>&, int, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getDatasetDataNum(int dataType, const ::std::string& datasetID, int& num, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetDatasetDataNumResult>(true, this, &ZGSIMServerPrx::_iceI_getDatasetDataNum, dataType, datasetID, context).get();
        num = _result.num;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDatasetDataNumAsync(int dataType, const ::std::string& datasetID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetDatasetDataNumResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetDatasetDataNumResult, P>(false, this, &ZGSIMServerPrx::_iceI_getDatasetDataNum, dataType, datasetID, context);
    }

    ::std::function<void()>
    getDatasetDataNumAsync(int dataType, const ::std::string& datasetID,
                           ::std::function<void(bool, int, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetDatasetDataNumResult&& _result)
        {
            response(_result.returnValue, _result.num, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetDatasetDataNumResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getDatasetDataNum, dataType, datasetID, context);
    }

    /// \cond INTERNAL
    void _iceI_getDatasetDataNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDatasetDataNumResult>>&, int, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getUserGroupDataNum(int dataType, const ::std::string& userGroupID, int& num, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetUserGroupDataNumResult>(true, this, &ZGSIMServerPrx::_iceI_getUserGroupDataNum, dataType, userGroupID, context).get();
        num = _result.num;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getUserGroupDataNumAsync(int dataType, const ::std::string& userGroupID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetUserGroupDataNumResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetUserGroupDataNumResult, P>(false, this, &ZGSIMServerPrx::_iceI_getUserGroupDataNum, dataType, userGroupID, context);
    }

    ::std::function<void()>
    getUserGroupDataNumAsync(int dataType, const ::std::string& userGroupID,
                             ::std::function<void(bool, int, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetUserGroupDataNumResult&& _result)
        {
            response(_result.returnValue, _result.num, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetUserGroupDataNumResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getUserGroupDataNum, dataType, userGroupID, context);
    }

    /// \cond INTERNAL
    void _iceI_getUserGroupDataNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetUserGroupDataNumResult>>&, int, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getNetInterface(StringList& listName, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetNetInterfaceResult>(true, this, &ZGSIMServerPrx::_iceI_getNetInterface, context).get();
        listName = ::std::move(_result.listName);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getNetInterfaceAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetNetInterfaceResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetNetInterfaceResult, P>(false, this, &ZGSIMServerPrx::_iceI_getNetInterface, context);
    }

    ::std::function<void()>
    getNetInterfaceAsync(::std::function<void(bool, ::ZG6000::StringList, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetNetInterfaceResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listName), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetNetInterfaceResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getNetInterface, context);
    }

    /// \cond INTERNAL
    void _iceI_getNetInterface(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetNetInterfaceResult>>&, const ::Ice::Context&);
    /// \endcond

    bool getNetworkAddr(const ::std::string& name, ListStringMap& listIPv4, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetNetworkAddrResult>(true, this, &ZGSIMServerPrx::_iceI_getNetworkAddr, name, context).get();
        listIPv4 = ::std::move(_result.listIPv4);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getNetworkAddrAsync(const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetNetworkAddrResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetNetworkAddrResult, P>(false, this, &ZGSIMServerPrx::_iceI_getNetworkAddr, name, context);
    }

    ::std::function<void()>
    getNetworkAddrAsync(const ::std::string& name,
                        ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetNetworkAddrResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listIPv4), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetNetworkAddrResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getNetworkAddr, name, context);
    }

    /// \cond INTERNAL
    void _iceI_getNetworkAddr(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetNetworkAddrResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool addNetworkAddr(const ::std::string& name, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::AddNetworkAddrResult>(true, this, &ZGSIMServerPrx::_iceI_addNetworkAddr, name, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto addNetworkAddrAsync(const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::AddNetworkAddrResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::AddNetworkAddrResult, P>(false, this, &ZGSIMServerPrx::_iceI_addNetworkAddr, name, context);
    }

    ::std::function<void()>
    addNetworkAddrAsync(const ::std::string& name,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::AddNetworkAddrResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::AddNetworkAddrResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_addNetworkAddr, name, context);
    }

    /// \cond INTERNAL
    void _iceI_addNetworkAddr(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::AddNetworkAddrResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool removeNetworkAddr(const ::std::string& name, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::RemoveNetworkAddrResult>(true, this, &ZGSIMServerPrx::_iceI_removeNetworkAddr, name, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto removeNetworkAddrAsync(const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::RemoveNetworkAddrResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::RemoveNetworkAddrResult, P>(false, this, &ZGSIMServerPrx::_iceI_removeNetworkAddr, name, context);
    }

    ::std::function<void()>
    removeNetworkAddrAsync(const ::std::string& name,
                           ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::RemoveNetworkAddrResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::RemoveNetworkAddrResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_removeNetworkAddr, name, context);
    }

    /// \cond INTERNAL
    void _iceI_removeNetworkAddr(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::RemoveNetworkAddrResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool addNetworkAddrEx(const ::std::string& name, const ListStringMap& listIPv4, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::AddNetworkAddrExResult>(true, this, &ZGSIMServerPrx::_iceI_addNetworkAddrEx, name, listIPv4, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto addNetworkAddrExAsync(const ::std::string& name, const ListStringMap& listIPv4, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::AddNetworkAddrExResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::AddNetworkAddrExResult, P>(false, this, &ZGSIMServerPrx::_iceI_addNetworkAddrEx, name, listIPv4, context);
    }

    ::std::function<void()>
    addNetworkAddrExAsync(const ::std::string& name, const ListStringMap& listIPv4,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::AddNetworkAddrExResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::AddNetworkAddrExResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_addNetworkAddrEx, name, listIPv4, context);
    }

    /// \cond INTERNAL
    void _iceI_addNetworkAddrEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::AddNetworkAddrExResult>>&, const ::std::string&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool removeNetworkAddrEx(const ::std::string& name, const ListStringMap& listIPv4, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::RemoveNetworkAddrExResult>(true, this, &ZGSIMServerPrx::_iceI_removeNetworkAddrEx, name, listIPv4, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto removeNetworkAddrExAsync(const ::std::string& name, const ListStringMap& listIPv4, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::RemoveNetworkAddrExResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::RemoveNetworkAddrExResult, P>(false, this, &ZGSIMServerPrx::_iceI_removeNetworkAddrEx, name, listIPv4, context);
    }

    ::std::function<void()>
    removeNetworkAddrExAsync(const ::std::string& name, const ListStringMap& listIPv4,
                             ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::RemoveNetworkAddrExResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::RemoveNetworkAddrExResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_removeNetworkAddrEx, name, listIPv4, context);
    }

    /// \cond INTERNAL
    void _iceI_removeNetworkAddrEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::RemoveNetworkAddrExResult>>&, const ::std::string&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool bindNetInterface(const ::std::string& name, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::BindNetInterfaceResult>(true, this, &ZGSIMServerPrx::_iceI_bindNetInterface, name, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto bindNetInterfaceAsync(const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::BindNetInterfaceResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::BindNetInterfaceResult, P>(false, this, &ZGSIMServerPrx::_iceI_bindNetInterface, name, context);
    }

    ::std::function<void()>
    bindNetInterfaceAsync(const ::std::string& name,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::BindNetInterfaceResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::BindNetInterfaceResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_bindNetInterface, name, context);
    }

    /// \cond INTERNAL
    void _iceI_bindNetInterface(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::BindNetInterfaceResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getTypeList(int type, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetTypeListResult>(true, this, &ZGSIMServerPrx::_iceI_getTypeList, type, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTypeListAsync(int type, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetTypeListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetTypeListResult, P>(false, this, &ZGSIMServerPrx::_iceI_getTypeList, type, context);
    }

    ::std::function<void()>
    getTypeListAsync(int type,
                     ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetTypeListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetTypeListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getTypeList, type, context);
    }

    /// \cond INTERNAL
    void _iceI_getTypeList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetTypeListResult>>&, int, const ::Ice::Context&);
    /// \endcond

    bool getDeviceRun(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetDeviceRunResult>(true, this, &ZGSIMServerPrx::_iceI_getDeviceRun, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDeviceRunAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetDeviceRunResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetDeviceRunResult, P>(false, this, &ZGSIMServerPrx::_iceI_getDeviceRun, context);
    }

    ::std::function<void()>
    getDeviceRunAsync(::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetDeviceRunResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetDeviceRunResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getDeviceRun, context);
    }

    /// \cond INTERNAL
    void _iceI_getDeviceRun(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDeviceRunResult>>&, const ::Ice::Context&);
    /// \endcond

    bool setDeviceRun(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::SetDeviceRunResult>(true, this, &ZGSIMServerPrx::_iceI_setDeviceRun, listItem, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setDeviceRunAsync(const ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::SetDeviceRunResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::SetDeviceRunResult, P>(false, this, &ZGSIMServerPrx::_iceI_setDeviceRun, listItem, context);
    }

    ::std::function<void()>
    setDeviceRunAsync(const ListStringMap& listItem,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::SetDeviceRunResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::SetDeviceRunResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_setDeviceRun, listItem, context);
    }

    /// \cond INTERNAL
    void _iceI_setDeviceRun(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetDeviceRunResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool addTaskList(int type, const StringList& listID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::AddTaskListResult>(true, this, &ZGSIMServerPrx::_iceI_addTaskList, type, listID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto addTaskListAsync(int type, const StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::AddTaskListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::AddTaskListResult, P>(false, this, &ZGSIMServerPrx::_iceI_addTaskList, type, listID, context);
    }

    ::std::function<void()>
    addTaskListAsync(int type, const StringList& listID,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::AddTaskListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::AddTaskListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_addTaskList, type, listID, context);
    }

    /// \cond INTERNAL
    void _iceI_addTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::AddTaskListResult>>&, int, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool deleteTaskList(int type, const StringList& listID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::DeleteTaskListResult>(true, this, &ZGSIMServerPrx::_iceI_deleteTaskList, type, listID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto deleteTaskListAsync(int type, const StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::DeleteTaskListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::DeleteTaskListResult, P>(false, this, &ZGSIMServerPrx::_iceI_deleteTaskList, type, listID, context);
    }

    ::std::function<void()>
    deleteTaskListAsync(int type, const StringList& listID,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::DeleteTaskListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::DeleteTaskListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_deleteTaskList, type, listID, context);
    }

    /// \cond INTERNAL
    void _iceI_deleteTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::DeleteTaskListResult>>&, int, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool clearTaskList(int type, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::ClearTaskListResult>(true, this, &ZGSIMServerPrx::_iceI_clearTaskList, type, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto clearTaskListAsync(int type, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::ClearTaskListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::ClearTaskListResult, P>(false, this, &ZGSIMServerPrx::_iceI_clearTaskList, type, context);
    }

    ::std::function<void()>
    clearTaskListAsync(int type,
                       ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::ClearTaskListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::ClearTaskListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_clearTaskList, type, context);
    }

    /// \cond INTERNAL
    void _iceI_clearTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::ClearTaskListResult>>&, int, const ::Ice::Context&);
    /// \endcond

    bool getTaskList(int type, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetTaskListResult>(true, this, &ZGSIMServerPrx::_iceI_getTaskList, type, context).get();
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTaskListAsync(int type, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetTaskListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetTaskListResult, P>(false, this, &ZGSIMServerPrx::_iceI_getTaskList, type, context);
    }

    ::std::function<void()>
    getTaskListAsync(int type,
                     ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetTaskListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetTaskListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getTaskList, type, context);
    }

    /// \cond INTERNAL
    void _iceI_getTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetTaskListResult>>&, int, const ::Ice::Context&);
    /// \endcond

    bool setTaskTest(int type, int testAction, const ::std::string& option, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::SetTaskTestResult>(true, this, &ZGSIMServerPrx::_iceI_setTaskTest, type, testAction, option, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setTaskTestAsync(int type, int testAction, const ::std::string& option, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::SetTaskTestResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::SetTaskTestResult, P>(false, this, &ZGSIMServerPrx::_iceI_setTaskTest, type, testAction, option, context);
    }

    ::std::function<void()>
    setTaskTestAsync(int type, int testAction, const ::std::string& option,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::SetTaskTestResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::SetTaskTestResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_setTaskTest, type, testAction, option, context);
    }

    /// \cond INTERNAL
    void _iceI_setTaskTest(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetTaskTestResult>>&, int, int, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getTaskTest(int type, int& testState, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSIMServer::GetTaskTestResult>(true, this, &ZGSIMServerPrx::_iceI_getTaskTest, type, context).get();
        testState = _result.testState;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTaskTestAsync(int type, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSIMServer::GetTaskTestResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSIMServer::GetTaskTestResult, P>(false, this, &ZGSIMServerPrx::_iceI_getTaskTest, type, context);
    }

    ::std::function<void()>
    getTaskTestAsync(int type,
                     ::std::function<void(bool, int, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSIMServer::GetTaskTestResult&& _result)
        {
            response(_result.returnValue, _result.testState, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSIMServer::GetTaskTestResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSIMServerPrx::_iceI_getTaskTest, type, context);
    }

    /// \cond INTERNAL
    void _iceI_getTaskTest(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetTaskTestResult>>&, int, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSIMServerPrx() = default;
    friend ::std::shared_ptr<ZGSIMServerPrx> IceInternal::createProxy<ZGSIMServerPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSIMServerPtr = ::std::shared_ptr<ZGSIMServer>;
using ZGSIMServerPrxPtr = ::std::shared_ptr<ZGSIMServerPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSIMServer;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSIMServer>&);
::IceProxy::Ice::Object* upCast(ZGSIMServer*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSIMServer;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSIMServer*);
/// \endcond
typedef ::IceInternal::Handle< ZGSIMServer> ZGSIMServerPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSIMServer> ZGSIMServerPrx;
typedef ZGSIMServerPrx ZGSIMServerPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSIMServerPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getServerState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getServerState.
 */
class Callback_ZGSIMServer_getServerState_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getServerState_Base> Callback_ZGSIMServer_getServerStatePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setServerAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setServerAction.
 */
class Callback_ZGSIMServer_setServerAction_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_setServerAction_Base> Callback_ZGSIMServer_setServerActionPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceList.
 */
class Callback_ZGSIMServer_getDeviceList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getDeviceList_Base> Callback_ZGSIMServer_getDeviceListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setDeviceAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setDeviceAction.
 */
class Callback_ZGSIMServer_setDeviceAction_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_setDeviceAction_Base> Callback_ZGSIMServer_setDeviceActionPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPrimaryList.
 */
class Callback_ZGSIMServer_getPrimaryList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getPrimaryList_Base> Callback_ZGSIMServer_getPrimaryListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPortList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPortList.
 */
class Callback_ZGSIMServer_getPortList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getPortList_Base> Callback_ZGSIMServer_getPortListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDatasetList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDatasetList.
 */
class Callback_ZGSIMServer_getDatasetList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getDatasetList_Base> Callback_ZGSIMServer_getDatasetListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getUserGroupList.
 */
class Callback_ZGSIMServer_getUserGroupList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getUserGroupList_Base> Callback_ZGSIMServer_getUserGroupListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setPrimaryAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setPrimaryAction.
 */
class Callback_ZGSIMServer_setPrimaryAction_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_setPrimaryAction_Base> Callback_ZGSIMServer_setPrimaryActionPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setPortAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setPortAction.
 */
class Callback_ZGSIMServer_setPortAction_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_setPortAction_Base> Callback_ZGSIMServer_setPortActionPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setDatasetAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setDatasetAction.
 */
class Callback_ZGSIMServer_setDatasetAction_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_setDatasetAction_Base> Callback_ZGSIMServer_setDatasetActionPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setUserGroupAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setUserGroupAction.
 */
class Callback_ZGSIMServer_setUserGroupAction_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_setUserGroupAction_Base> Callback_ZGSIMServer_setUserGroupActionPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getSignalValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getSignalValue.
 */
class Callback_ZGSIMServer_getSignalValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getSignalValue_Base> Callback_ZGSIMServer_getSignalValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setSignalValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setSignalValue.
 */
class Callback_ZGSIMServer_setSignalValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_setSignalValue_Base> Callback_ZGSIMServer_setSignalValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceData.
 */
class Callback_ZGSIMServer_getDeviceData_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getDeviceData_Base> Callback_ZGSIMServer_getDeviceDataPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPrimaryData.
 */
class Callback_ZGSIMServer_getPrimaryData_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getPrimaryData_Base> Callback_ZGSIMServer_getPrimaryDataPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPortData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPortData.
 */
class Callback_ZGSIMServer_getPortData_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getPortData_Base> Callback_ZGSIMServer_getPortDataPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDatasetData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDatasetData.
 */
class Callback_ZGSIMServer_getDatasetData_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getDatasetData_Base> Callback_ZGSIMServer_getDatasetDataPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getUserGroupData.
 */
class Callback_ZGSIMServer_getUserGroupData_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getUserGroupData_Base> Callback_ZGSIMServer_getUserGroupDataPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceDataNum.
 */
class Callback_ZGSIMServer_getDeviceDataNum_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getDeviceDataNum_Base> Callback_ZGSIMServer_getDeviceDataNumPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPrimaryDataNum.
 */
class Callback_ZGSIMServer_getPrimaryDataNum_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getPrimaryDataNum_Base> Callback_ZGSIMServer_getPrimaryDataNumPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPortDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPortDataNum.
 */
class Callback_ZGSIMServer_getPortDataNum_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getPortDataNum_Base> Callback_ZGSIMServer_getPortDataNumPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDatasetDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDatasetDataNum.
 */
class Callback_ZGSIMServer_getDatasetDataNum_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getDatasetDataNum_Base> Callback_ZGSIMServer_getDatasetDataNumPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getUserGroupDataNum.
 */
class Callback_ZGSIMServer_getUserGroupDataNum_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getUserGroupDataNum_Base> Callback_ZGSIMServer_getUserGroupDataNumPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getNetInterface.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getNetInterface.
 */
class Callback_ZGSIMServer_getNetInterface_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getNetInterface_Base> Callback_ZGSIMServer_getNetInterfacePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getNetworkAddr.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getNetworkAddr.
 */
class Callback_ZGSIMServer_getNetworkAddr_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getNetworkAddr_Base> Callback_ZGSIMServer_getNetworkAddrPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddr.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_addNetworkAddr.
 */
class Callback_ZGSIMServer_addNetworkAddr_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_addNetworkAddr_Base> Callback_ZGSIMServer_addNetworkAddrPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddr.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_removeNetworkAddr.
 */
class Callback_ZGSIMServer_removeNetworkAddr_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_removeNetworkAddr_Base> Callback_ZGSIMServer_removeNetworkAddrPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddrEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_addNetworkAddrEx.
 */
class Callback_ZGSIMServer_addNetworkAddrEx_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_addNetworkAddrEx_Base> Callback_ZGSIMServer_addNetworkAddrExPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddrEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_removeNetworkAddrEx.
 */
class Callback_ZGSIMServer_removeNetworkAddrEx_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_removeNetworkAddrEx_Base> Callback_ZGSIMServer_removeNetworkAddrExPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_bindNetInterface.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_bindNetInterface.
 */
class Callback_ZGSIMServer_bindNetInterface_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_bindNetInterface_Base> Callback_ZGSIMServer_bindNetInterfacePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getTypeList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getTypeList.
 */
class Callback_ZGSIMServer_getTypeList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getTypeList_Base> Callback_ZGSIMServer_getTypeListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceRun.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceRun.
 */
class Callback_ZGSIMServer_getDeviceRun_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getDeviceRun_Base> Callback_ZGSIMServer_getDeviceRunPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setDeviceRun.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setDeviceRun.
 */
class Callback_ZGSIMServer_setDeviceRun_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_setDeviceRun_Base> Callback_ZGSIMServer_setDeviceRunPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_addTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_addTaskList.
 */
class Callback_ZGSIMServer_addTaskList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_addTaskList_Base> Callback_ZGSIMServer_addTaskListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_deleteTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_deleteTaskList.
 */
class Callback_ZGSIMServer_deleteTaskList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_deleteTaskList_Base> Callback_ZGSIMServer_deleteTaskListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_clearTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_clearTaskList.
 */
class Callback_ZGSIMServer_clearTaskList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_clearTaskList_Base> Callback_ZGSIMServer_clearTaskListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getTaskList.
 */
class Callback_ZGSIMServer_getTaskList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getTaskList_Base> Callback_ZGSIMServer_getTaskListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setTaskTest.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setTaskTest.
 */
class Callback_ZGSIMServer_setTaskTest_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_setTaskTest_Base> Callback_ZGSIMServer_setTaskTestPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getTaskTest.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getTaskTest.
 */
class Callback_ZGSIMServer_getTaskTest_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSIMServer_getTaskTest_Base> Callback_ZGSIMServer_getTaskTestPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSIMServer : public virtual ::Ice::Proxy<ZGSIMServer, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool getServerState(::ZG6000::StringMap& mapValue, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getServerState(mapValue, e, _iceI_begin_getServerState(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getServerState(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getServerState(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getServerState(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getServerState(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getServerState(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getServerState(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getServerState(const ::ZG6000::Callback_ZGSIMServer_getServerStatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getServerState(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getServerState(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getServerStatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getServerState(context, cb, cookie);
    }

    bool end_getServerState(::ZG6000::StringMap& mapValue, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getServerState(::ZG6000::StringMap& iceP_mapValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getServerState(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setServerAction(const ::ZG6000::StringMap& mapValue, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setServerAction(e, _iceI_begin_setServerAction(mapValue, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setServerAction(const ::ZG6000::StringMap& mapValue, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setServerAction(mapValue, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setServerAction(const ::ZG6000::StringMap& mapValue, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setServerAction(mapValue, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setServerAction(const ::ZG6000::StringMap& mapValue, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setServerAction(mapValue, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setServerAction(const ::ZG6000::StringMap& mapValue, const ::ZG6000::Callback_ZGSIMServer_setServerActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setServerAction(mapValue, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setServerAction(const ::ZG6000::StringMap& mapValue, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_setServerActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setServerAction(mapValue, context, cb, cookie);
    }

    bool end_setServerAction(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setServerAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setServerAction(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDeviceList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDeviceList(listItem, e, _iceI_begin_getDeviceList(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDeviceList(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDeviceList(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDeviceList(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceList(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceList(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceList(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceList(const ::ZG6000::Callback_ZGSIMServer_getDeviceListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceList(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceList(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getDeviceListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceList(context, cb, cookie);
    }

    bool end_getDeviceList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDeviceList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDeviceList(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setDeviceAction(const ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setDeviceAction(e, _iceI_begin_setDeviceAction(listItem, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setDeviceAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setDeviceAction(listItem, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setDeviceAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDeviceAction(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setDeviceAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDeviceAction(listItem, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setDeviceAction(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::Callback_ZGSIMServer_setDeviceActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDeviceAction(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setDeviceAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_setDeviceActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDeviceAction(listItem, context, cb, cookie);
    }

    bool end_setDeviceAction(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setDeviceAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setDeviceAction(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPrimaryList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPrimaryList(listItem, e, _iceI_begin_getPrimaryList(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPrimaryList(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPrimaryList(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryList(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryList(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryList(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryList(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryList(const ::ZG6000::Callback_ZGSIMServer_getPrimaryListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryList(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryList(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getPrimaryListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryList(context, cb, cookie);
    }

    bool end_getPrimaryList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPrimaryList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPrimaryList(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPortList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPortList(listItem, e, _iceI_begin_getPortList(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPortList(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPortList(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPortList(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortList(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPortList(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortList(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPortList(const ::ZG6000::Callback_ZGSIMServer_getPortListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortList(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPortList(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getPortListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortList(context, cb, cookie);
    }

    bool end_getPortList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPortList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPortList(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDatasetList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDatasetList(listItem, e, _iceI_begin_getDatasetList(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDatasetList(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDatasetList(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDatasetList(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetList(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDatasetList(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetList(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDatasetList(const ::ZG6000::Callback_ZGSIMServer_getDatasetListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetList(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDatasetList(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getDatasetListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetList(context, cb, cookie);
    }

    bool end_getDatasetList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDatasetList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDatasetList(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getUserGroupList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getUserGroupList(listItem, e, _iceI_begin_getUserGroupList(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getUserGroupList(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getUserGroupList(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupList(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupList(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupList(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupList(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupList(const ::ZG6000::Callback_ZGSIMServer_getUserGroupListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupList(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupList(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getUserGroupListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupList(context, cb, cookie);
    }

    bool end_getUserGroupList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getUserGroupList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getUserGroupList(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setPrimaryAction(const ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setPrimaryAction(e, _iceI_begin_setPrimaryAction(listItem, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setPrimaryAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setPrimaryAction(listItem, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setPrimaryAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setPrimaryAction(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setPrimaryAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setPrimaryAction(listItem, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setPrimaryAction(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::Callback_ZGSIMServer_setPrimaryActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setPrimaryAction(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setPrimaryAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_setPrimaryActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setPrimaryAction(listItem, context, cb, cookie);
    }

    bool end_setPrimaryAction(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setPrimaryAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setPrimaryAction(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setPortAction(const ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setPortAction(e, _iceI_begin_setPortAction(listItem, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setPortAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setPortAction(listItem, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setPortAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setPortAction(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setPortAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setPortAction(listItem, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setPortAction(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::Callback_ZGSIMServer_setPortActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setPortAction(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setPortAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_setPortActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setPortAction(listItem, context, cb, cookie);
    }

    bool end_setPortAction(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setPortAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setPortAction(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setDatasetAction(const ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setDatasetAction(e, _iceI_begin_setDatasetAction(listItem, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setDatasetAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setDatasetAction(listItem, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setDatasetAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDatasetAction(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setDatasetAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDatasetAction(listItem, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setDatasetAction(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::Callback_ZGSIMServer_setDatasetActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDatasetAction(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setDatasetAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_setDatasetActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDatasetAction(listItem, context, cb, cookie);
    }

    bool end_setDatasetAction(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setDatasetAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setDatasetAction(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setUserGroupAction(const ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setUserGroupAction(e, _iceI_begin_setUserGroupAction(listItem, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setUserGroupAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setUserGroupAction(listItem, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setUserGroupAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setUserGroupAction(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setUserGroupAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setUserGroupAction(listItem, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setUserGroupAction(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::Callback_ZGSIMServer_setUserGroupActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setUserGroupAction(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setUserGroupAction(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_setUserGroupActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setUserGroupAction(listItem, context, cb, cookie);
    }

    bool end_setUserGroupAction(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setUserGroupAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setUserGroupAction(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getSignalValue(::Ice::Int dataType, const ::ZG6000::StringList& listDataID, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getSignalValue(listItem, e, _iceI_begin_getSignalValue(dataType, listDataID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getSignalValue(::Ice::Int dataType, const ::ZG6000::StringList& listDataID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getSignalValue(dataType, listDataID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getSignalValue(::Ice::Int dataType, const ::ZG6000::StringList& listDataID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSignalValue(dataType, listDataID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getSignalValue(::Ice::Int dataType, const ::ZG6000::StringList& listDataID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSignalValue(dataType, listDataID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getSignalValue(::Ice::Int dataType, const ::ZG6000::StringList& listDataID, const ::ZG6000::Callback_ZGSIMServer_getSignalValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSignalValue(dataType, listDataID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getSignalValue(::Ice::Int dataType, const ::ZG6000::StringList& listDataID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getSignalValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSignalValue(dataType, listDataID, context, cb, cookie);
    }

    bool end_getSignalValue(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getSignalValue(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getSignalValue(::Ice::Int, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setSignalValue(::Ice::Int dataType, const ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setSignalValue(e, _iceI_begin_setSignalValue(dataType, listItem, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setSignalValue(::Ice::Int dataType, const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setSignalValue(dataType, listItem, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setSignalValue(::Ice::Int dataType, const ::ZG6000::ListStringMap& listItem, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setSignalValue(dataType, listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setSignalValue(::Ice::Int dataType, const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setSignalValue(dataType, listItem, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setSignalValue(::Ice::Int dataType, const ::ZG6000::ListStringMap& listItem, const ::ZG6000::Callback_ZGSIMServer_setSignalValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setSignalValue(dataType, listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setSignalValue(::Ice::Int dataType, const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_setSignalValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setSignalValue(dataType, listItem, context, cb, cookie);
    }

    bool end_setSignalValue(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setSignalValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setSignalValue(::Ice::Int, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDeviceData(::Ice::Int dataType, const ::std::string& deviceID, ::Ice::Int page, ::Ice::Int limit, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDeviceData(listItem, e, _iceI_begin_getDeviceData(dataType, deviceID, page, limit, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDeviceData(::Ice::Int dataType, const ::std::string& deviceID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDeviceData(dataType, deviceID, page, limit, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDeviceData(::Ice::Int dataType, const ::std::string& deviceID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceData(dataType, deviceID, page, limit, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceData(::Ice::Int dataType, const ::std::string& deviceID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceData(dataType, deviceID, page, limit, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceData(::Ice::Int dataType, const ::std::string& deviceID, ::Ice::Int page, ::Ice::Int limit, const ::ZG6000::Callback_ZGSIMServer_getDeviceDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceData(dataType, deviceID, page, limit, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceData(::Ice::Int dataType, const ::std::string& deviceID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getDeviceDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceData(dataType, deviceID, page, limit, context, cb, cookie);
    }

    bool end_getDeviceData(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDeviceData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDeviceData(::Ice::Int, const ::std::string&, ::Ice::Int, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPrimaryData(::Ice::Int dataType, const ::std::string& primaryID, ::Ice::Int page, ::Ice::Int limit, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPrimaryData(listItem, e, _iceI_begin_getPrimaryData(dataType, primaryID, page, limit, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPrimaryData(::Ice::Int dataType, const ::std::string& primaryID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPrimaryData(dataType, primaryID, page, limit, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryData(::Ice::Int dataType, const ::std::string& primaryID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryData(dataType, primaryID, page, limit, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryData(::Ice::Int dataType, const ::std::string& primaryID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryData(dataType, primaryID, page, limit, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryData(::Ice::Int dataType, const ::std::string& primaryID, ::Ice::Int page, ::Ice::Int limit, const ::ZG6000::Callback_ZGSIMServer_getPrimaryDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryData(dataType, primaryID, page, limit, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryData(::Ice::Int dataType, const ::std::string& primaryID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getPrimaryDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryData(dataType, primaryID, page, limit, context, cb, cookie);
    }

    bool end_getPrimaryData(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPrimaryData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPrimaryData(::Ice::Int, const ::std::string&, ::Ice::Int, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPortData(::Ice::Int dataType, const ::std::string& portID, ::Ice::Int page, ::Ice::Int limit, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPortData(listItem, e, _iceI_begin_getPortData(dataType, portID, page, limit, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPortData(::Ice::Int dataType, const ::std::string& portID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPortData(dataType, portID, page, limit, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPortData(::Ice::Int dataType, const ::std::string& portID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortData(dataType, portID, page, limit, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPortData(::Ice::Int dataType, const ::std::string& portID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortData(dataType, portID, page, limit, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPortData(::Ice::Int dataType, const ::std::string& portID, ::Ice::Int page, ::Ice::Int limit, const ::ZG6000::Callback_ZGSIMServer_getPortDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortData(dataType, portID, page, limit, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPortData(::Ice::Int dataType, const ::std::string& portID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getPortDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortData(dataType, portID, page, limit, context, cb, cookie);
    }

    bool end_getPortData(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPortData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPortData(::Ice::Int, const ::std::string&, ::Ice::Int, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDatasetData(::Ice::Int dataType, const ::std::string& datasetID, ::Ice::Int page, ::Ice::Int limit, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDatasetData(listItem, e, _iceI_begin_getDatasetData(dataType, datasetID, page, limit, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDatasetData(::Ice::Int dataType, const ::std::string& datasetID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDatasetData(dataType, datasetID, page, limit, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDatasetData(::Ice::Int dataType, const ::std::string& datasetID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetData(dataType, datasetID, page, limit, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDatasetData(::Ice::Int dataType, const ::std::string& datasetID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetData(dataType, datasetID, page, limit, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDatasetData(::Ice::Int dataType, const ::std::string& datasetID, ::Ice::Int page, ::Ice::Int limit, const ::ZG6000::Callback_ZGSIMServer_getDatasetDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetData(dataType, datasetID, page, limit, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDatasetData(::Ice::Int dataType, const ::std::string& datasetID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getDatasetDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetData(dataType, datasetID, page, limit, context, cb, cookie);
    }

    bool end_getDatasetData(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDatasetData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDatasetData(::Ice::Int, const ::std::string&, ::Ice::Int, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getUserGroupData(::Ice::Int dataType, const ::std::string& userGroupID, ::Ice::Int page, ::Ice::Int limit, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getUserGroupData(listItem, e, _iceI_begin_getUserGroupData(dataType, userGroupID, page, limit, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getUserGroupData(::Ice::Int dataType, const ::std::string& userGroupID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getUserGroupData(dataType, userGroupID, page, limit, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupData(::Ice::Int dataType, const ::std::string& userGroupID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupData(dataType, userGroupID, page, limit, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupData(::Ice::Int dataType, const ::std::string& userGroupID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupData(dataType, userGroupID, page, limit, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupData(::Ice::Int dataType, const ::std::string& userGroupID, ::Ice::Int page, ::Ice::Int limit, const ::ZG6000::Callback_ZGSIMServer_getUserGroupDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupData(dataType, userGroupID, page, limit, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupData(::Ice::Int dataType, const ::std::string& userGroupID, ::Ice::Int page, ::Ice::Int limit, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getUserGroupDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupData(dataType, userGroupID, page, limit, context, cb, cookie);
    }

    bool end_getUserGroupData(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getUserGroupData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getUserGroupData(::Ice::Int, const ::std::string&, ::Ice::Int, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDeviceDataNum(::Ice::Int dataType, const ::std::string& deviceID, ::Ice::Int& num, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDeviceDataNum(num, e, _iceI_begin_getDeviceDataNum(dataType, deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDeviceDataNum(::Ice::Int dataType, const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDeviceDataNum(dataType, deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDeviceDataNum(::Ice::Int dataType, const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceDataNum(dataType, deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceDataNum(::Ice::Int dataType, const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceDataNum(dataType, deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceDataNum(::Ice::Int dataType, const ::std::string& deviceID, const ::ZG6000::Callback_ZGSIMServer_getDeviceDataNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceDataNum(dataType, deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceDataNum(::Ice::Int dataType, const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getDeviceDataNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceDataNum(dataType, deviceID, context, cb, cookie);
    }

    bool end_getDeviceDataNum(::Ice::Int& num, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDeviceDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDeviceDataNum(::Ice::Int, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPrimaryDataNum(::Ice::Int dataType, const ::std::string& primaryID, ::Ice::Int& num, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPrimaryDataNum(num, e, _iceI_begin_getPrimaryDataNum(dataType, primaryID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPrimaryDataNum(::Ice::Int dataType, const ::std::string& primaryID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPrimaryDataNum(dataType, primaryID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryDataNum(::Ice::Int dataType, const ::std::string& primaryID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryDataNum(dataType, primaryID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryDataNum(::Ice::Int dataType, const ::std::string& primaryID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryDataNum(dataType, primaryID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryDataNum(::Ice::Int dataType, const ::std::string& primaryID, const ::ZG6000::Callback_ZGSIMServer_getPrimaryDataNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryDataNum(dataType, primaryID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPrimaryDataNum(::Ice::Int dataType, const ::std::string& primaryID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getPrimaryDataNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPrimaryDataNum(dataType, primaryID, context, cb, cookie);
    }

    bool end_getPrimaryDataNum(::Ice::Int& num, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPrimaryDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPrimaryDataNum(::Ice::Int, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPortDataNum(::Ice::Int dataType, const ::std::string& portID, ::Ice::Int& num, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPortDataNum(num, e, _iceI_begin_getPortDataNum(dataType, portID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPortDataNum(::Ice::Int dataType, const ::std::string& portID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPortDataNum(dataType, portID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPortDataNum(::Ice::Int dataType, const ::std::string& portID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortDataNum(dataType, portID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPortDataNum(::Ice::Int dataType, const ::std::string& portID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortDataNum(dataType, portID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPortDataNum(::Ice::Int dataType, const ::std::string& portID, const ::ZG6000::Callback_ZGSIMServer_getPortDataNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortDataNum(dataType, portID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPortDataNum(::Ice::Int dataType, const ::std::string& portID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getPortDataNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPortDataNum(dataType, portID, context, cb, cookie);
    }

    bool end_getPortDataNum(::Ice::Int& num, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPortDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPortDataNum(::Ice::Int, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDatasetDataNum(::Ice::Int dataType, const ::std::string& datasetID, ::Ice::Int& num, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDatasetDataNum(num, e, _iceI_begin_getDatasetDataNum(dataType, datasetID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDatasetDataNum(::Ice::Int dataType, const ::std::string& datasetID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDatasetDataNum(dataType, datasetID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDatasetDataNum(::Ice::Int dataType, const ::std::string& datasetID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetDataNum(dataType, datasetID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDatasetDataNum(::Ice::Int dataType, const ::std::string& datasetID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetDataNum(dataType, datasetID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDatasetDataNum(::Ice::Int dataType, const ::std::string& datasetID, const ::ZG6000::Callback_ZGSIMServer_getDatasetDataNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetDataNum(dataType, datasetID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDatasetDataNum(::Ice::Int dataType, const ::std::string& datasetID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getDatasetDataNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDatasetDataNum(dataType, datasetID, context, cb, cookie);
    }

    bool end_getDatasetDataNum(::Ice::Int& num, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDatasetDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDatasetDataNum(::Ice::Int, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getUserGroupDataNum(::Ice::Int dataType, const ::std::string& userGroupID, ::Ice::Int& num, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getUserGroupDataNum(num, e, _iceI_begin_getUserGroupDataNum(dataType, userGroupID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getUserGroupDataNum(::Ice::Int dataType, const ::std::string& userGroupID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getUserGroupDataNum(dataType, userGroupID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupDataNum(::Ice::Int dataType, const ::std::string& userGroupID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupDataNum(dataType, userGroupID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupDataNum(::Ice::Int dataType, const ::std::string& userGroupID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupDataNum(dataType, userGroupID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupDataNum(::Ice::Int dataType, const ::std::string& userGroupID, const ::ZG6000::Callback_ZGSIMServer_getUserGroupDataNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupDataNum(dataType, userGroupID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserGroupDataNum(::Ice::Int dataType, const ::std::string& userGroupID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getUserGroupDataNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserGroupDataNum(dataType, userGroupID, context, cb, cookie);
    }

    bool end_getUserGroupDataNum(::Ice::Int& num, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getUserGroupDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getUserGroupDataNum(::Ice::Int, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getNetInterface(::ZG6000::StringList& listName, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getNetInterface(listName, e, _iceI_begin_getNetInterface(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getNetInterface(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getNetInterface(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getNetInterface(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getNetInterface(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getNetInterface(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getNetInterface(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getNetInterface(const ::ZG6000::Callback_ZGSIMServer_getNetInterfacePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getNetInterface(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getNetInterface(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getNetInterfacePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getNetInterface(context, cb, cookie);
    }

    bool end_getNetInterface(::ZG6000::StringList& listName, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getNetInterface(::ZG6000::StringList& iceP_listName, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getNetInterface(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getNetworkAddr(const ::std::string& name, ::ZG6000::ListStringMap& listIPv4, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getNetworkAddr(listIPv4, e, _iceI_begin_getNetworkAddr(name, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getNetworkAddr(const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getNetworkAddr(name, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getNetworkAddr(const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getNetworkAddr(name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getNetworkAddr(const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getNetworkAddr(name, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getNetworkAddr(const ::std::string& name, const ::ZG6000::Callback_ZGSIMServer_getNetworkAddrPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getNetworkAddr(name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getNetworkAddr(const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getNetworkAddrPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getNetworkAddr(name, context, cb, cookie);
    }

    bool end_getNetworkAddr(::ZG6000::ListStringMap& listIPv4, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getNetworkAddr(::ZG6000::ListStringMap& iceP_listIPv4, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getNetworkAddr(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool addNetworkAddr(const ::std::string& name, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_addNetworkAddr(e, _iceI_begin_addNetworkAddr(name, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_addNetworkAddr(const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_addNetworkAddr(name, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_addNetworkAddr(const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addNetworkAddr(name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addNetworkAddr(const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addNetworkAddr(name, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addNetworkAddr(const ::std::string& name, const ::ZG6000::Callback_ZGSIMServer_addNetworkAddrPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addNetworkAddr(name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addNetworkAddr(const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_addNetworkAddrPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addNetworkAddr(name, context, cb, cookie);
    }

    bool end_addNetworkAddr(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_addNetworkAddr(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_addNetworkAddr(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool removeNetworkAddr(const ::std::string& name, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_removeNetworkAddr(e, _iceI_begin_removeNetworkAddr(name, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_removeNetworkAddr(const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_removeNetworkAddr(name, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_removeNetworkAddr(const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeNetworkAddr(name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_removeNetworkAddr(const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeNetworkAddr(name, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_removeNetworkAddr(const ::std::string& name, const ::ZG6000::Callback_ZGSIMServer_removeNetworkAddrPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeNetworkAddr(name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_removeNetworkAddr(const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_removeNetworkAddrPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeNetworkAddr(name, context, cb, cookie);
    }

    bool end_removeNetworkAddr(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_removeNetworkAddr(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_removeNetworkAddr(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool addNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_addNetworkAddrEx(e, _iceI_begin_addNetworkAddrEx(name, listIPv4, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_addNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_addNetworkAddrEx(name, listIPv4, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_addNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addNetworkAddrEx(name, listIPv4, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addNetworkAddrEx(name, listIPv4, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, const ::ZG6000::Callback_ZGSIMServer_addNetworkAddrExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addNetworkAddrEx(name, listIPv4, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_addNetworkAddrExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addNetworkAddrEx(name, listIPv4, context, cb, cookie);
    }

    bool end_addNetworkAddrEx(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_addNetworkAddrEx(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_addNetworkAddrEx(const ::std::string&, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool removeNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_removeNetworkAddrEx(e, _iceI_begin_removeNetworkAddrEx(name, listIPv4, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_removeNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_removeNetworkAddrEx(name, listIPv4, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_removeNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeNetworkAddrEx(name, listIPv4, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_removeNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeNetworkAddrEx(name, listIPv4, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_removeNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, const ::ZG6000::Callback_ZGSIMServer_removeNetworkAddrExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeNetworkAddrEx(name, listIPv4, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_removeNetworkAddrEx(const ::std::string& name, const ::ZG6000::ListStringMap& listIPv4, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_removeNetworkAddrExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeNetworkAddrEx(name, listIPv4, context, cb, cookie);
    }

    bool end_removeNetworkAddrEx(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_removeNetworkAddrEx(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_removeNetworkAddrEx(const ::std::string&, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool bindNetInterface(const ::std::string& name, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_bindNetInterface(e, _iceI_begin_bindNetInterface(name, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_bindNetInterface(const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_bindNetInterface(name, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_bindNetInterface(const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_bindNetInterface(name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_bindNetInterface(const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_bindNetInterface(name, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_bindNetInterface(const ::std::string& name, const ::ZG6000::Callback_ZGSIMServer_bindNetInterfacePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_bindNetInterface(name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_bindNetInterface(const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_bindNetInterfacePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_bindNetInterface(name, context, cb, cookie);
    }

    bool end_bindNetInterface(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_bindNetInterface(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_bindNetInterface(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getTypeList(::Ice::Int type, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTypeList(listItem, e, _iceI_begin_getTypeList(type, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTypeList(::Ice::Int type, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTypeList(type, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTypeList(::Ice::Int type, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTypeList(type, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTypeList(::Ice::Int type, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTypeList(type, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTypeList(::Ice::Int type, const ::ZG6000::Callback_ZGSIMServer_getTypeListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTypeList(type, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTypeList(::Ice::Int type, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getTypeListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTypeList(type, context, cb, cookie);
    }

    bool end_getTypeList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTypeList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTypeList(::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDeviceRun(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDeviceRun(listItem, e, _iceI_begin_getDeviceRun(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDeviceRun(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDeviceRun(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDeviceRun(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceRun(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceRun(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceRun(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceRun(const ::ZG6000::Callback_ZGSIMServer_getDeviceRunPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceRun(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceRun(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getDeviceRunPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceRun(context, cb, cookie);
    }

    bool end_getDeviceRun(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDeviceRun(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDeviceRun(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setDeviceRun(const ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setDeviceRun(e, _iceI_begin_setDeviceRun(listItem, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setDeviceRun(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setDeviceRun(listItem, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setDeviceRun(const ::ZG6000::ListStringMap& listItem, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDeviceRun(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setDeviceRun(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDeviceRun(listItem, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setDeviceRun(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::Callback_ZGSIMServer_setDeviceRunPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDeviceRun(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setDeviceRun(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_setDeviceRunPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setDeviceRun(listItem, context, cb, cookie);
    }

    bool end_setDeviceRun(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setDeviceRun(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setDeviceRun(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool addTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_addTaskList(e, _iceI_begin_addTaskList(type, listID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_addTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_addTaskList(type, listID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_addTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addTaskList(type, listID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addTaskList(type, listID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, const ::ZG6000::Callback_ZGSIMServer_addTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addTaskList(type, listID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_addTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addTaskList(type, listID, context, cb, cookie);
    }

    bool end_addTaskList(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_addTaskList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_addTaskList(::Ice::Int, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool deleteTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deleteTaskList(e, _iceI_begin_deleteTaskList(type, listID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_deleteTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deleteTaskList(type, listID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_deleteTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTaskList(type, listID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTaskList(type, listID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, const ::ZG6000::Callback_ZGSIMServer_deleteTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTaskList(type, listID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteTaskList(::Ice::Int type, const ::ZG6000::StringList& listID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_deleteTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTaskList(type, listID, context, cb, cookie);
    }

    bool end_deleteTaskList(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deleteTaskList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deleteTaskList(::Ice::Int, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool clearTaskList(::Ice::Int type, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_clearTaskList(e, _iceI_begin_clearTaskList(type, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_clearTaskList(::Ice::Int type, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_clearTaskList(type, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_clearTaskList(::Ice::Int type, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearTaskList(type, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_clearTaskList(::Ice::Int type, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearTaskList(type, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_clearTaskList(::Ice::Int type, const ::ZG6000::Callback_ZGSIMServer_clearTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearTaskList(type, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_clearTaskList(::Ice::Int type, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_clearTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearTaskList(type, context, cb, cookie);
    }

    bool end_clearTaskList(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_clearTaskList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_clearTaskList(::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getTaskList(::Ice::Int type, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTaskList(listItem, e, _iceI_begin_getTaskList(type, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTaskList(::Ice::Int type, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTaskList(type, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTaskList(::Ice::Int type, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(type, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskList(::Ice::Int type, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(type, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskList(::Ice::Int type, const ::ZG6000::Callback_ZGSIMServer_getTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(type, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskList(::Ice::Int type, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(type, context, cb, cookie);
    }

    bool end_getTaskList(::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTaskList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTaskList(::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setTaskTest(::Ice::Int type, ::Ice::Int testAction, const ::std::string& option, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setTaskTest(e, _iceI_begin_setTaskTest(type, testAction, option, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setTaskTest(::Ice::Int type, ::Ice::Int testAction, const ::std::string& option, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setTaskTest(type, testAction, option, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setTaskTest(::Ice::Int type, ::Ice::Int testAction, const ::std::string& option, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setTaskTest(type, testAction, option, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setTaskTest(::Ice::Int type, ::Ice::Int testAction, const ::std::string& option, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setTaskTest(type, testAction, option, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setTaskTest(::Ice::Int type, ::Ice::Int testAction, const ::std::string& option, const ::ZG6000::Callback_ZGSIMServer_setTaskTestPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setTaskTest(type, testAction, option, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setTaskTest(::Ice::Int type, ::Ice::Int testAction, const ::std::string& option, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_setTaskTestPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setTaskTest(type, testAction, option, context, cb, cookie);
    }

    bool end_setTaskTest(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setTaskTest(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setTaskTest(::Ice::Int, ::Ice::Int, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getTaskTest(::Ice::Int type, ::Ice::Int& testState, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTaskTest(testState, e, _iceI_begin_getTaskTest(type, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTaskTest(::Ice::Int type, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTaskTest(type, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTaskTest(::Ice::Int type, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskTest(type, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskTest(::Ice::Int type, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskTest(type, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskTest(::Ice::Int type, const ::ZG6000::Callback_ZGSIMServer_getTaskTestPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskTest(type, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskTest(::Ice::Int type, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSIMServer_getTaskTestPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskTest(type, context, cb, cookie);
    }

    bool end_getTaskTest(::Ice::Int& testState, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTaskTest(::Ice::Int& iceP_testState, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTaskTest(::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSIMServer : virtual public ZGServerBase
{
public:

    typedef ZGSIMServerPrx ProxyType;
    typedef ZGSIMServerPtr PointerType;

    virtual ~ZGSIMServer();

#ifdef ICE_CPP11_COMPILER
    ZGSIMServer() = default;
    ZGSIMServer(const ZGSIMServer&) = default;
    ZGSIMServer& operator=(const ZGSIMServer&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool getServerState(StringMap& mapValue, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getServerState(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setServerAction(const StringMap& mapValue, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setServerAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDeviceList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setDeviceAction(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setDeviceAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPrimaryList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPrimaryList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPortList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPortList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDatasetList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDatasetList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getUserGroupList(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserGroupList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setPrimaryAction(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setPrimaryAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setPortAction(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setPortAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setDatasetAction(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setDatasetAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setUserGroupAction(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setUserGroupAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getSignalValue(::Ice::Int dataType, const StringList& listDataID, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getSignalValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setSignalValue(::Ice::Int dataType, const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setSignalValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDeviceData(::Ice::Int dataType, const ::std::string& deviceID, ::Ice::Int page, ::Ice::Int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPrimaryData(::Ice::Int dataType, const ::std::string& primaryID, ::Ice::Int page, ::Ice::Int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPrimaryData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPortData(::Ice::Int dataType, const ::std::string& portID, ::Ice::Int page, ::Ice::Int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPortData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDatasetData(::Ice::Int dataType, const ::std::string& datasetID, ::Ice::Int page, ::Ice::Int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDatasetData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getUserGroupData(::Ice::Int dataType, const ::std::string& userGroupID, ::Ice::Int page, ::Ice::Int limit, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserGroupData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDeviceDataNum(::Ice::Int dataType, const ::std::string& deviceID, ::Ice::Int& num, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceDataNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPrimaryDataNum(::Ice::Int dataType, const ::std::string& primaryID, ::Ice::Int& num, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPrimaryDataNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPortDataNum(::Ice::Int dataType, const ::std::string& portID, ::Ice::Int& num, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPortDataNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDatasetDataNum(::Ice::Int dataType, const ::std::string& datasetID, ::Ice::Int& num, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDatasetDataNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getUserGroupDataNum(::Ice::Int dataType, const ::std::string& userGroupID, ::Ice::Int& num, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserGroupDataNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getNetInterface(StringList& listName, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getNetInterface(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getNetworkAddr(const ::std::string& name, ListStringMap& listIPv4, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getNetworkAddr(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool addNetworkAddr(const ::std::string& name, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_addNetworkAddr(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool removeNetworkAddr(const ::std::string& name, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_removeNetworkAddr(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool addNetworkAddrEx(const ::std::string& name, const ListStringMap& listIPv4, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_addNetworkAddrEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool removeNetworkAddrEx(const ::std::string& name, const ListStringMap& listIPv4, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_removeNetworkAddrEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool bindNetInterface(const ::std::string& name, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_bindNetInterface(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getTypeList(::Ice::Int type, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTypeList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDeviceRun(ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceRun(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setDeviceRun(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setDeviceRun(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool addTaskList(::Ice::Int type, const StringList& listID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_addTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool deleteTaskList(::Ice::Int type, const StringList& listID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool clearTaskList(::Ice::Int type, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_clearTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getTaskList(::Ice::Int type, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setTaskTest(::Ice::Int type, ::Ice::Int testAction, const ::std::string& option, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setTaskTest(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getTaskTest(::Ice::Int type, ::Ice::Int& testState, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskTest(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSIMServer& lhs, const ZGSIMServer& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSIMServer& lhs, const ZGSIMServer& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getServerState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getServerState.
 */
template<class T>
class CallbackNC_ZGSIMServer_getServerState : public Callback_ZGSIMServer_getServerState_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getServerState(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        StringMap iceP_mapValue;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getServerState(iceP_mapValue, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_mapValue, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getServerState.
 */
template<class T> Callback_ZGSIMServer_getServerStatePtr
newCallback_ZGSIMServer_getServerState(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getServerState<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getServerState.
 */
template<class T> Callback_ZGSIMServer_getServerStatePtr
newCallback_ZGSIMServer_getServerState(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getServerState<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getServerState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getServerState.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getServerState : public Callback_ZGSIMServer_getServerState_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getServerState(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        StringMap iceP_mapValue;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getServerState(iceP_mapValue, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_mapValue, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getServerState.
 */
template<class T, typename CT> Callback_ZGSIMServer_getServerStatePtr
newCallback_ZGSIMServer_getServerState(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getServerState<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getServerState.
 */
template<class T, typename CT> Callback_ZGSIMServer_getServerStatePtr
newCallback_ZGSIMServer_getServerState(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getServerState<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setServerAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setServerAction.
 */
template<class T>
class CallbackNC_ZGSIMServer_setServerAction : public Callback_ZGSIMServer_setServerAction_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_setServerAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setServerAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setServerAction.
 */
template<class T> Callback_ZGSIMServer_setServerActionPtr
newCallback_ZGSIMServer_setServerAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setServerAction<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setServerAction.
 */
template<class T> Callback_ZGSIMServer_setServerActionPtr
newCallback_ZGSIMServer_setServerAction(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setServerAction<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setServerAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setServerAction.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_setServerAction : public Callback_ZGSIMServer_setServerAction_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_setServerAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setServerAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setServerAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setServerActionPtr
newCallback_ZGSIMServer_setServerAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setServerAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setServerAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setServerActionPtr
newCallback_ZGSIMServer_setServerAction(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setServerAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceList.
 */
template<class T>
class CallbackNC_ZGSIMServer_getDeviceList : public Callback_ZGSIMServer_getDeviceList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getDeviceList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceList.
 */
template<class T> Callback_ZGSIMServer_getDeviceListPtr
newCallback_ZGSIMServer_getDeviceList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDeviceList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceList.
 */
template<class T> Callback_ZGSIMServer_getDeviceListPtr
newCallback_ZGSIMServer_getDeviceList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDeviceList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceList.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getDeviceList : public Callback_ZGSIMServer_getDeviceList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getDeviceList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDeviceListPtr
newCallback_ZGSIMServer_getDeviceList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDeviceList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDeviceListPtr
newCallback_ZGSIMServer_getDeviceList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDeviceList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setDeviceAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setDeviceAction.
 */
template<class T>
class CallbackNC_ZGSIMServer_setDeviceAction : public Callback_ZGSIMServer_setDeviceAction_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_setDeviceAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setDeviceAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDeviceAction.
 */
template<class T> Callback_ZGSIMServer_setDeviceActionPtr
newCallback_ZGSIMServer_setDeviceAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setDeviceAction<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDeviceAction.
 */
template<class T> Callback_ZGSIMServer_setDeviceActionPtr
newCallback_ZGSIMServer_setDeviceAction(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setDeviceAction<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setDeviceAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setDeviceAction.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_setDeviceAction : public Callback_ZGSIMServer_setDeviceAction_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_setDeviceAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setDeviceAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDeviceAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setDeviceActionPtr
newCallback_ZGSIMServer_setDeviceAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setDeviceAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDeviceAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setDeviceActionPtr
newCallback_ZGSIMServer_setDeviceAction(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setDeviceAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPrimaryList.
 */
template<class T>
class CallbackNC_ZGSIMServer_getPrimaryList : public Callback_ZGSIMServer_getPrimaryList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getPrimaryList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPrimaryList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryList.
 */
template<class T> Callback_ZGSIMServer_getPrimaryListPtr
newCallback_ZGSIMServer_getPrimaryList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPrimaryList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryList.
 */
template<class T> Callback_ZGSIMServer_getPrimaryListPtr
newCallback_ZGSIMServer_getPrimaryList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPrimaryList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPrimaryList.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getPrimaryList : public Callback_ZGSIMServer_getPrimaryList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getPrimaryList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPrimaryList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPrimaryListPtr
newCallback_ZGSIMServer_getPrimaryList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPrimaryList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPrimaryListPtr
newCallback_ZGSIMServer_getPrimaryList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPrimaryList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPortList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPortList.
 */
template<class T>
class CallbackNC_ZGSIMServer_getPortList : public Callback_ZGSIMServer_getPortList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getPortList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPortList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortList.
 */
template<class T> Callback_ZGSIMServer_getPortListPtr
newCallback_ZGSIMServer_getPortList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPortList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortList.
 */
template<class T> Callback_ZGSIMServer_getPortListPtr
newCallback_ZGSIMServer_getPortList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPortList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPortList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPortList.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getPortList : public Callback_ZGSIMServer_getPortList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getPortList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPortList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPortListPtr
newCallback_ZGSIMServer_getPortList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPortList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPortListPtr
newCallback_ZGSIMServer_getPortList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPortList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDatasetList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDatasetList.
 */
template<class T>
class CallbackNC_ZGSIMServer_getDatasetList : public Callback_ZGSIMServer_getDatasetList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getDatasetList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDatasetList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetList.
 */
template<class T> Callback_ZGSIMServer_getDatasetListPtr
newCallback_ZGSIMServer_getDatasetList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDatasetList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetList.
 */
template<class T> Callback_ZGSIMServer_getDatasetListPtr
newCallback_ZGSIMServer_getDatasetList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDatasetList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDatasetList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDatasetList.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getDatasetList : public Callback_ZGSIMServer_getDatasetList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getDatasetList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDatasetList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDatasetListPtr
newCallback_ZGSIMServer_getDatasetList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDatasetList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDatasetListPtr
newCallback_ZGSIMServer_getDatasetList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDatasetList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getUserGroupList.
 */
template<class T>
class CallbackNC_ZGSIMServer_getUserGroupList : public Callback_ZGSIMServer_getUserGroupList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getUserGroupList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserGroupList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupList.
 */
template<class T> Callback_ZGSIMServer_getUserGroupListPtr
newCallback_ZGSIMServer_getUserGroupList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getUserGroupList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupList.
 */
template<class T> Callback_ZGSIMServer_getUserGroupListPtr
newCallback_ZGSIMServer_getUserGroupList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getUserGroupList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getUserGroupList.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getUserGroupList : public Callback_ZGSIMServer_getUserGroupList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getUserGroupList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserGroupList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getUserGroupListPtr
newCallback_ZGSIMServer_getUserGroupList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getUserGroupList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getUserGroupListPtr
newCallback_ZGSIMServer_getUserGroupList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getUserGroupList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setPrimaryAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setPrimaryAction.
 */
template<class T>
class CallbackNC_ZGSIMServer_setPrimaryAction : public Callback_ZGSIMServer_setPrimaryAction_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_setPrimaryAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setPrimaryAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setPrimaryAction.
 */
template<class T> Callback_ZGSIMServer_setPrimaryActionPtr
newCallback_ZGSIMServer_setPrimaryAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setPrimaryAction<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setPrimaryAction.
 */
template<class T> Callback_ZGSIMServer_setPrimaryActionPtr
newCallback_ZGSIMServer_setPrimaryAction(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setPrimaryAction<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setPrimaryAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setPrimaryAction.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_setPrimaryAction : public Callback_ZGSIMServer_setPrimaryAction_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_setPrimaryAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setPrimaryAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setPrimaryAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setPrimaryActionPtr
newCallback_ZGSIMServer_setPrimaryAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setPrimaryAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setPrimaryAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setPrimaryActionPtr
newCallback_ZGSIMServer_setPrimaryAction(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setPrimaryAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setPortAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setPortAction.
 */
template<class T>
class CallbackNC_ZGSIMServer_setPortAction : public Callback_ZGSIMServer_setPortAction_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_setPortAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setPortAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setPortAction.
 */
template<class T> Callback_ZGSIMServer_setPortActionPtr
newCallback_ZGSIMServer_setPortAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setPortAction<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setPortAction.
 */
template<class T> Callback_ZGSIMServer_setPortActionPtr
newCallback_ZGSIMServer_setPortAction(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setPortAction<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setPortAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setPortAction.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_setPortAction : public Callback_ZGSIMServer_setPortAction_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_setPortAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setPortAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setPortAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setPortActionPtr
newCallback_ZGSIMServer_setPortAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setPortAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setPortAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setPortActionPtr
newCallback_ZGSIMServer_setPortAction(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setPortAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setDatasetAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setDatasetAction.
 */
template<class T>
class CallbackNC_ZGSIMServer_setDatasetAction : public Callback_ZGSIMServer_setDatasetAction_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_setDatasetAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setDatasetAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDatasetAction.
 */
template<class T> Callback_ZGSIMServer_setDatasetActionPtr
newCallback_ZGSIMServer_setDatasetAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setDatasetAction<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDatasetAction.
 */
template<class T> Callback_ZGSIMServer_setDatasetActionPtr
newCallback_ZGSIMServer_setDatasetAction(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setDatasetAction<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setDatasetAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setDatasetAction.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_setDatasetAction : public Callback_ZGSIMServer_setDatasetAction_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_setDatasetAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setDatasetAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDatasetAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setDatasetActionPtr
newCallback_ZGSIMServer_setDatasetAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setDatasetAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDatasetAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setDatasetActionPtr
newCallback_ZGSIMServer_setDatasetAction(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setDatasetAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setUserGroupAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setUserGroupAction.
 */
template<class T>
class CallbackNC_ZGSIMServer_setUserGroupAction : public Callback_ZGSIMServer_setUserGroupAction_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_setUserGroupAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setUserGroupAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setUserGroupAction.
 */
template<class T> Callback_ZGSIMServer_setUserGroupActionPtr
newCallback_ZGSIMServer_setUserGroupAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setUserGroupAction<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setUserGroupAction.
 */
template<class T> Callback_ZGSIMServer_setUserGroupActionPtr
newCallback_ZGSIMServer_setUserGroupAction(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setUserGroupAction<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setUserGroupAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setUserGroupAction.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_setUserGroupAction : public Callback_ZGSIMServer_setUserGroupAction_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_setUserGroupAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setUserGroupAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setUserGroupAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setUserGroupActionPtr
newCallback_ZGSIMServer_setUserGroupAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setUserGroupAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setUserGroupAction.
 */
template<class T, typename CT> Callback_ZGSIMServer_setUserGroupActionPtr
newCallback_ZGSIMServer_setUserGroupAction(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setUserGroupAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getSignalValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getSignalValue.
 */
template<class T>
class CallbackNC_ZGSIMServer_getSignalValue : public Callback_ZGSIMServer_getSignalValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getSignalValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getSignalValue(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getSignalValue.
 */
template<class T> Callback_ZGSIMServer_getSignalValuePtr
newCallback_ZGSIMServer_getSignalValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getSignalValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getSignalValue.
 */
template<class T> Callback_ZGSIMServer_getSignalValuePtr
newCallback_ZGSIMServer_getSignalValue(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getSignalValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getSignalValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getSignalValue.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getSignalValue : public Callback_ZGSIMServer_getSignalValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getSignalValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getSignalValue(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getSignalValue.
 */
template<class T, typename CT> Callback_ZGSIMServer_getSignalValuePtr
newCallback_ZGSIMServer_getSignalValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getSignalValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getSignalValue.
 */
template<class T, typename CT> Callback_ZGSIMServer_getSignalValuePtr
newCallback_ZGSIMServer_getSignalValue(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getSignalValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setSignalValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setSignalValue.
 */
template<class T>
class CallbackNC_ZGSIMServer_setSignalValue : public Callback_ZGSIMServer_setSignalValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_setSignalValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setSignalValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setSignalValue.
 */
template<class T> Callback_ZGSIMServer_setSignalValuePtr
newCallback_ZGSIMServer_setSignalValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setSignalValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setSignalValue.
 */
template<class T> Callback_ZGSIMServer_setSignalValuePtr
newCallback_ZGSIMServer_setSignalValue(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setSignalValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setSignalValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setSignalValue.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_setSignalValue : public Callback_ZGSIMServer_setSignalValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_setSignalValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setSignalValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setSignalValue.
 */
template<class T, typename CT> Callback_ZGSIMServer_setSignalValuePtr
newCallback_ZGSIMServer_setSignalValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setSignalValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setSignalValue.
 */
template<class T, typename CT> Callback_ZGSIMServer_setSignalValuePtr
newCallback_ZGSIMServer_setSignalValue(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setSignalValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceData.
 */
template<class T>
class CallbackNC_ZGSIMServer_getDeviceData : public Callback_ZGSIMServer_getDeviceData_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getDeviceData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceData(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceData.
 */
template<class T> Callback_ZGSIMServer_getDeviceDataPtr
newCallback_ZGSIMServer_getDeviceData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDeviceData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceData.
 */
template<class T> Callback_ZGSIMServer_getDeviceDataPtr
newCallback_ZGSIMServer_getDeviceData(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDeviceData<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceData.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getDeviceData : public Callback_ZGSIMServer_getDeviceData_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getDeviceData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceData(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceData.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDeviceDataPtr
newCallback_ZGSIMServer_getDeviceData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDeviceData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceData.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDeviceDataPtr
newCallback_ZGSIMServer_getDeviceData(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDeviceData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPrimaryData.
 */
template<class T>
class CallbackNC_ZGSIMServer_getPrimaryData : public Callback_ZGSIMServer_getPrimaryData_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getPrimaryData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPrimaryData(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryData.
 */
template<class T> Callback_ZGSIMServer_getPrimaryDataPtr
newCallback_ZGSIMServer_getPrimaryData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPrimaryData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryData.
 */
template<class T> Callback_ZGSIMServer_getPrimaryDataPtr
newCallback_ZGSIMServer_getPrimaryData(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPrimaryData<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPrimaryData.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getPrimaryData : public Callback_ZGSIMServer_getPrimaryData_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getPrimaryData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPrimaryData(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryData.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPrimaryDataPtr
newCallback_ZGSIMServer_getPrimaryData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPrimaryData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryData.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPrimaryDataPtr
newCallback_ZGSIMServer_getPrimaryData(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPrimaryData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPortData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPortData.
 */
template<class T>
class CallbackNC_ZGSIMServer_getPortData : public Callback_ZGSIMServer_getPortData_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getPortData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPortData(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortData.
 */
template<class T> Callback_ZGSIMServer_getPortDataPtr
newCallback_ZGSIMServer_getPortData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPortData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortData.
 */
template<class T> Callback_ZGSIMServer_getPortDataPtr
newCallback_ZGSIMServer_getPortData(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPortData<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPortData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPortData.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getPortData : public Callback_ZGSIMServer_getPortData_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getPortData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPortData(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortData.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPortDataPtr
newCallback_ZGSIMServer_getPortData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPortData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortData.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPortDataPtr
newCallback_ZGSIMServer_getPortData(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPortData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDatasetData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDatasetData.
 */
template<class T>
class CallbackNC_ZGSIMServer_getDatasetData : public Callback_ZGSIMServer_getDatasetData_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getDatasetData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDatasetData(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetData.
 */
template<class T> Callback_ZGSIMServer_getDatasetDataPtr
newCallback_ZGSIMServer_getDatasetData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDatasetData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetData.
 */
template<class T> Callback_ZGSIMServer_getDatasetDataPtr
newCallback_ZGSIMServer_getDatasetData(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDatasetData<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDatasetData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDatasetData.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getDatasetData : public Callback_ZGSIMServer_getDatasetData_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getDatasetData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDatasetData(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetData.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDatasetDataPtr
newCallback_ZGSIMServer_getDatasetData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDatasetData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetData.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDatasetDataPtr
newCallback_ZGSIMServer_getDatasetData(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDatasetData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getUserGroupData.
 */
template<class T>
class CallbackNC_ZGSIMServer_getUserGroupData : public Callback_ZGSIMServer_getUserGroupData_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getUserGroupData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserGroupData(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupData.
 */
template<class T> Callback_ZGSIMServer_getUserGroupDataPtr
newCallback_ZGSIMServer_getUserGroupData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getUserGroupData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupData.
 */
template<class T> Callback_ZGSIMServer_getUserGroupDataPtr
newCallback_ZGSIMServer_getUserGroupData(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getUserGroupData<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getUserGroupData.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getUserGroupData : public Callback_ZGSIMServer_getUserGroupData_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getUserGroupData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserGroupData(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupData.
 */
template<class T, typename CT> Callback_ZGSIMServer_getUserGroupDataPtr
newCallback_ZGSIMServer_getUserGroupData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getUserGroupData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupData.
 */
template<class T, typename CT> Callback_ZGSIMServer_getUserGroupDataPtr
newCallback_ZGSIMServer_getUserGroupData(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getUserGroupData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceDataNum.
 */
template<class T>
class CallbackNC_ZGSIMServer_getDeviceDataNum : public Callback_ZGSIMServer_getDeviceDataNum_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getDeviceDataNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_num;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceDataNum(iceP_num, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_num, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceDataNum.
 */
template<class T> Callback_ZGSIMServer_getDeviceDataNumPtr
newCallback_ZGSIMServer_getDeviceDataNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDeviceDataNum<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceDataNum.
 */
template<class T> Callback_ZGSIMServer_getDeviceDataNumPtr
newCallback_ZGSIMServer_getDeviceDataNum(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDeviceDataNum<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceDataNum.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getDeviceDataNum : public Callback_ZGSIMServer_getDeviceDataNum_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getDeviceDataNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_num;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceDataNum(iceP_num, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_num, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceDataNum.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDeviceDataNumPtr
newCallback_ZGSIMServer_getDeviceDataNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDeviceDataNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceDataNum.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDeviceDataNumPtr
newCallback_ZGSIMServer_getDeviceDataNum(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDeviceDataNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPrimaryDataNum.
 */
template<class T>
class CallbackNC_ZGSIMServer_getPrimaryDataNum : public Callback_ZGSIMServer_getPrimaryDataNum_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getPrimaryDataNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_num;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPrimaryDataNum(iceP_num, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_num, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryDataNum.
 */
template<class T> Callback_ZGSIMServer_getPrimaryDataNumPtr
newCallback_ZGSIMServer_getPrimaryDataNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPrimaryDataNum<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryDataNum.
 */
template<class T> Callback_ZGSIMServer_getPrimaryDataNumPtr
newCallback_ZGSIMServer_getPrimaryDataNum(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPrimaryDataNum<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPrimaryDataNum.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getPrimaryDataNum : public Callback_ZGSIMServer_getPrimaryDataNum_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getPrimaryDataNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_num;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPrimaryDataNum(iceP_num, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_num, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryDataNum.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPrimaryDataNumPtr
newCallback_ZGSIMServer_getPrimaryDataNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPrimaryDataNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPrimaryDataNum.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPrimaryDataNumPtr
newCallback_ZGSIMServer_getPrimaryDataNum(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPrimaryDataNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPortDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPortDataNum.
 */
template<class T>
class CallbackNC_ZGSIMServer_getPortDataNum : public Callback_ZGSIMServer_getPortDataNum_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getPortDataNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_num;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPortDataNum(iceP_num, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_num, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortDataNum.
 */
template<class T> Callback_ZGSIMServer_getPortDataNumPtr
newCallback_ZGSIMServer_getPortDataNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPortDataNum<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortDataNum.
 */
template<class T> Callback_ZGSIMServer_getPortDataNumPtr
newCallback_ZGSIMServer_getPortDataNum(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getPortDataNum<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getPortDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getPortDataNum.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getPortDataNum : public Callback_ZGSIMServer_getPortDataNum_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getPortDataNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_num;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPortDataNum(iceP_num, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_num, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortDataNum.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPortDataNumPtr
newCallback_ZGSIMServer_getPortDataNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPortDataNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getPortDataNum.
 */
template<class T, typename CT> Callback_ZGSIMServer_getPortDataNumPtr
newCallback_ZGSIMServer_getPortDataNum(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getPortDataNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDatasetDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDatasetDataNum.
 */
template<class T>
class CallbackNC_ZGSIMServer_getDatasetDataNum : public Callback_ZGSIMServer_getDatasetDataNum_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getDatasetDataNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_num;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDatasetDataNum(iceP_num, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_num, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetDataNum.
 */
template<class T> Callback_ZGSIMServer_getDatasetDataNumPtr
newCallback_ZGSIMServer_getDatasetDataNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDatasetDataNum<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetDataNum.
 */
template<class T> Callback_ZGSIMServer_getDatasetDataNumPtr
newCallback_ZGSIMServer_getDatasetDataNum(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDatasetDataNum<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDatasetDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDatasetDataNum.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getDatasetDataNum : public Callback_ZGSIMServer_getDatasetDataNum_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getDatasetDataNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_num;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDatasetDataNum(iceP_num, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_num, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetDataNum.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDatasetDataNumPtr
newCallback_ZGSIMServer_getDatasetDataNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDatasetDataNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDatasetDataNum.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDatasetDataNumPtr
newCallback_ZGSIMServer_getDatasetDataNum(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDatasetDataNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getUserGroupDataNum.
 */
template<class T>
class CallbackNC_ZGSIMServer_getUserGroupDataNum : public Callback_ZGSIMServer_getUserGroupDataNum_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getUserGroupDataNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_num;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserGroupDataNum(iceP_num, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_num, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupDataNum.
 */
template<class T> Callback_ZGSIMServer_getUserGroupDataNumPtr
newCallback_ZGSIMServer_getUserGroupDataNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getUserGroupDataNum<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupDataNum.
 */
template<class T> Callback_ZGSIMServer_getUserGroupDataNumPtr
newCallback_ZGSIMServer_getUserGroupDataNum(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getUserGroupDataNum<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupDataNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getUserGroupDataNum.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getUserGroupDataNum : public Callback_ZGSIMServer_getUserGroupDataNum_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getUserGroupDataNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_num;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserGroupDataNum(iceP_num, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_num, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupDataNum.
 */
template<class T, typename CT> Callback_ZGSIMServer_getUserGroupDataNumPtr
newCallback_ZGSIMServer_getUserGroupDataNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getUserGroupDataNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getUserGroupDataNum.
 */
template<class T, typename CT> Callback_ZGSIMServer_getUserGroupDataNumPtr
newCallback_ZGSIMServer_getUserGroupDataNum(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getUserGroupDataNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getNetInterface.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getNetInterface.
 */
template<class T>
class CallbackNC_ZGSIMServer_getNetInterface : public Callback_ZGSIMServer_getNetInterface_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringList&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getNetInterface(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        StringList iceP_listName;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getNetInterface(iceP_listName, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listName, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getNetInterface.
 */
template<class T> Callback_ZGSIMServer_getNetInterfacePtr
newCallback_ZGSIMServer_getNetInterface(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getNetInterface<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getNetInterface.
 */
template<class T> Callback_ZGSIMServer_getNetInterfacePtr
newCallback_ZGSIMServer_getNetInterface(T* instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getNetInterface<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getNetInterface.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getNetInterface.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getNetInterface : public Callback_ZGSIMServer_getNetInterface_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringList&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getNetInterface(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        StringList iceP_listName;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getNetInterface(iceP_listName, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listName, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getNetInterface.
 */
template<class T, typename CT> Callback_ZGSIMServer_getNetInterfacePtr
newCallback_ZGSIMServer_getNetInterface(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getNetInterface<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getNetInterface.
 */
template<class T, typename CT> Callback_ZGSIMServer_getNetInterfacePtr
newCallback_ZGSIMServer_getNetInterface(T* instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getNetInterface<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getNetworkAddr.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getNetworkAddr.
 */
template<class T>
class CallbackNC_ZGSIMServer_getNetworkAddr : public Callback_ZGSIMServer_getNetworkAddr_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getNetworkAddr(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listIPv4;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getNetworkAddr(iceP_listIPv4, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listIPv4, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getNetworkAddr.
 */
template<class T> Callback_ZGSIMServer_getNetworkAddrPtr
newCallback_ZGSIMServer_getNetworkAddr(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getNetworkAddr<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getNetworkAddr.
 */
template<class T> Callback_ZGSIMServer_getNetworkAddrPtr
newCallback_ZGSIMServer_getNetworkAddr(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getNetworkAddr<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getNetworkAddr.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getNetworkAddr.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getNetworkAddr : public Callback_ZGSIMServer_getNetworkAddr_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getNetworkAddr(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listIPv4;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getNetworkAddr(iceP_listIPv4, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listIPv4, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getNetworkAddr.
 */
template<class T, typename CT> Callback_ZGSIMServer_getNetworkAddrPtr
newCallback_ZGSIMServer_getNetworkAddr(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getNetworkAddr<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getNetworkAddr.
 */
template<class T, typename CT> Callback_ZGSIMServer_getNetworkAddrPtr
newCallback_ZGSIMServer_getNetworkAddr(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getNetworkAddr<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddr.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_addNetworkAddr.
 */
template<class T>
class CallbackNC_ZGSIMServer_addNetworkAddr : public Callback_ZGSIMServer_addNetworkAddr_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_addNetworkAddr(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addNetworkAddr(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddr.
 */
template<class T> Callback_ZGSIMServer_addNetworkAddrPtr
newCallback_ZGSIMServer_addNetworkAddr(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_addNetworkAddr<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddr.
 */
template<class T> Callback_ZGSIMServer_addNetworkAddrPtr
newCallback_ZGSIMServer_addNetworkAddr(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_addNetworkAddr<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddr.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_addNetworkAddr.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_addNetworkAddr : public Callback_ZGSIMServer_addNetworkAddr_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_addNetworkAddr(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addNetworkAddr(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddr.
 */
template<class T, typename CT> Callback_ZGSIMServer_addNetworkAddrPtr
newCallback_ZGSIMServer_addNetworkAddr(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_addNetworkAddr<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddr.
 */
template<class T, typename CT> Callback_ZGSIMServer_addNetworkAddrPtr
newCallback_ZGSIMServer_addNetworkAddr(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_addNetworkAddr<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddr.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_removeNetworkAddr.
 */
template<class T>
class CallbackNC_ZGSIMServer_removeNetworkAddr : public Callback_ZGSIMServer_removeNetworkAddr_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_removeNetworkAddr(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_removeNetworkAddr(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddr.
 */
template<class T> Callback_ZGSIMServer_removeNetworkAddrPtr
newCallback_ZGSIMServer_removeNetworkAddr(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_removeNetworkAddr<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddr.
 */
template<class T> Callback_ZGSIMServer_removeNetworkAddrPtr
newCallback_ZGSIMServer_removeNetworkAddr(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_removeNetworkAddr<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddr.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_removeNetworkAddr.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_removeNetworkAddr : public Callback_ZGSIMServer_removeNetworkAddr_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_removeNetworkAddr(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_removeNetworkAddr(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddr.
 */
template<class T, typename CT> Callback_ZGSIMServer_removeNetworkAddrPtr
newCallback_ZGSIMServer_removeNetworkAddr(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_removeNetworkAddr<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddr.
 */
template<class T, typename CT> Callback_ZGSIMServer_removeNetworkAddrPtr
newCallback_ZGSIMServer_removeNetworkAddr(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_removeNetworkAddr<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddrEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_addNetworkAddrEx.
 */
template<class T>
class CallbackNC_ZGSIMServer_addNetworkAddrEx : public Callback_ZGSIMServer_addNetworkAddrEx_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_addNetworkAddrEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addNetworkAddrEx(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddrEx.
 */
template<class T> Callback_ZGSIMServer_addNetworkAddrExPtr
newCallback_ZGSIMServer_addNetworkAddrEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_addNetworkAddrEx<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddrEx.
 */
template<class T> Callback_ZGSIMServer_addNetworkAddrExPtr
newCallback_ZGSIMServer_addNetworkAddrEx(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_addNetworkAddrEx<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddrEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_addNetworkAddrEx.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_addNetworkAddrEx : public Callback_ZGSIMServer_addNetworkAddrEx_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_addNetworkAddrEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addNetworkAddrEx(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddrEx.
 */
template<class T, typename CT> Callback_ZGSIMServer_addNetworkAddrExPtr
newCallback_ZGSIMServer_addNetworkAddrEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_addNetworkAddrEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addNetworkAddrEx.
 */
template<class T, typename CT> Callback_ZGSIMServer_addNetworkAddrExPtr
newCallback_ZGSIMServer_addNetworkAddrEx(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_addNetworkAddrEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddrEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_removeNetworkAddrEx.
 */
template<class T>
class CallbackNC_ZGSIMServer_removeNetworkAddrEx : public Callback_ZGSIMServer_removeNetworkAddrEx_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_removeNetworkAddrEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_removeNetworkAddrEx(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddrEx.
 */
template<class T> Callback_ZGSIMServer_removeNetworkAddrExPtr
newCallback_ZGSIMServer_removeNetworkAddrEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_removeNetworkAddrEx<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddrEx.
 */
template<class T> Callback_ZGSIMServer_removeNetworkAddrExPtr
newCallback_ZGSIMServer_removeNetworkAddrEx(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_removeNetworkAddrEx<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddrEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_removeNetworkAddrEx.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_removeNetworkAddrEx : public Callback_ZGSIMServer_removeNetworkAddrEx_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_removeNetworkAddrEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_removeNetworkAddrEx(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddrEx.
 */
template<class T, typename CT> Callback_ZGSIMServer_removeNetworkAddrExPtr
newCallback_ZGSIMServer_removeNetworkAddrEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_removeNetworkAddrEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_removeNetworkAddrEx.
 */
template<class T, typename CT> Callback_ZGSIMServer_removeNetworkAddrExPtr
newCallback_ZGSIMServer_removeNetworkAddrEx(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_removeNetworkAddrEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_bindNetInterface.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_bindNetInterface.
 */
template<class T>
class CallbackNC_ZGSIMServer_bindNetInterface : public Callback_ZGSIMServer_bindNetInterface_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_bindNetInterface(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_bindNetInterface(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_bindNetInterface.
 */
template<class T> Callback_ZGSIMServer_bindNetInterfacePtr
newCallback_ZGSIMServer_bindNetInterface(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_bindNetInterface<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_bindNetInterface.
 */
template<class T> Callback_ZGSIMServer_bindNetInterfacePtr
newCallback_ZGSIMServer_bindNetInterface(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_bindNetInterface<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_bindNetInterface.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_bindNetInterface.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_bindNetInterface : public Callback_ZGSIMServer_bindNetInterface_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_bindNetInterface(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_bindNetInterface(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_bindNetInterface.
 */
template<class T, typename CT> Callback_ZGSIMServer_bindNetInterfacePtr
newCallback_ZGSIMServer_bindNetInterface(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_bindNetInterface<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_bindNetInterface.
 */
template<class T, typename CT> Callback_ZGSIMServer_bindNetInterfacePtr
newCallback_ZGSIMServer_bindNetInterface(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_bindNetInterface<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getTypeList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getTypeList.
 */
template<class T>
class CallbackNC_ZGSIMServer_getTypeList : public Callback_ZGSIMServer_getTypeList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getTypeList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTypeList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTypeList.
 */
template<class T> Callback_ZGSIMServer_getTypeListPtr
newCallback_ZGSIMServer_getTypeList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getTypeList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTypeList.
 */
template<class T> Callback_ZGSIMServer_getTypeListPtr
newCallback_ZGSIMServer_getTypeList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getTypeList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getTypeList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getTypeList.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getTypeList : public Callback_ZGSIMServer_getTypeList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getTypeList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTypeList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTypeList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getTypeListPtr
newCallback_ZGSIMServer_getTypeList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getTypeList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTypeList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getTypeListPtr
newCallback_ZGSIMServer_getTypeList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getTypeList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceRun.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceRun.
 */
template<class T>
class CallbackNC_ZGSIMServer_getDeviceRun : public Callback_ZGSIMServer_getDeviceRun_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getDeviceRun(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceRun(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceRun.
 */
template<class T> Callback_ZGSIMServer_getDeviceRunPtr
newCallback_ZGSIMServer_getDeviceRun(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDeviceRun<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceRun.
 */
template<class T> Callback_ZGSIMServer_getDeviceRunPtr
newCallback_ZGSIMServer_getDeviceRun(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getDeviceRun<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getDeviceRun.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getDeviceRun.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getDeviceRun : public Callback_ZGSIMServer_getDeviceRun_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getDeviceRun(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceRun(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceRun.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDeviceRunPtr
newCallback_ZGSIMServer_getDeviceRun(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDeviceRun<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getDeviceRun.
 */
template<class T, typename CT> Callback_ZGSIMServer_getDeviceRunPtr
newCallback_ZGSIMServer_getDeviceRun(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getDeviceRun<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setDeviceRun.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setDeviceRun.
 */
template<class T>
class CallbackNC_ZGSIMServer_setDeviceRun : public Callback_ZGSIMServer_setDeviceRun_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_setDeviceRun(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setDeviceRun(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDeviceRun.
 */
template<class T> Callback_ZGSIMServer_setDeviceRunPtr
newCallback_ZGSIMServer_setDeviceRun(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setDeviceRun<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDeviceRun.
 */
template<class T> Callback_ZGSIMServer_setDeviceRunPtr
newCallback_ZGSIMServer_setDeviceRun(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setDeviceRun<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setDeviceRun.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setDeviceRun.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_setDeviceRun : public Callback_ZGSIMServer_setDeviceRun_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_setDeviceRun(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setDeviceRun(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDeviceRun.
 */
template<class T, typename CT> Callback_ZGSIMServer_setDeviceRunPtr
newCallback_ZGSIMServer_setDeviceRun(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setDeviceRun<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setDeviceRun.
 */
template<class T, typename CT> Callback_ZGSIMServer_setDeviceRunPtr
newCallback_ZGSIMServer_setDeviceRun(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setDeviceRun<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_addTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_addTaskList.
 */
template<class T>
class CallbackNC_ZGSIMServer_addTaskList : public Callback_ZGSIMServer_addTaskList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_addTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addTaskList(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addTaskList.
 */
template<class T> Callback_ZGSIMServer_addTaskListPtr
newCallback_ZGSIMServer_addTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_addTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addTaskList.
 */
template<class T> Callback_ZGSIMServer_addTaskListPtr
newCallback_ZGSIMServer_addTaskList(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_addTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_addTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_addTaskList.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_addTaskList : public Callback_ZGSIMServer_addTaskList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_addTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addTaskList(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addTaskList.
 */
template<class T, typename CT> Callback_ZGSIMServer_addTaskListPtr
newCallback_ZGSIMServer_addTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_addTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_addTaskList.
 */
template<class T, typename CT> Callback_ZGSIMServer_addTaskListPtr
newCallback_ZGSIMServer_addTaskList(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_addTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_deleteTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_deleteTaskList.
 */
template<class T>
class CallbackNC_ZGSIMServer_deleteTaskList : public Callback_ZGSIMServer_deleteTaskList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_deleteTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteTaskList(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_deleteTaskList.
 */
template<class T> Callback_ZGSIMServer_deleteTaskListPtr
newCallback_ZGSIMServer_deleteTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_deleteTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_deleteTaskList.
 */
template<class T> Callback_ZGSIMServer_deleteTaskListPtr
newCallback_ZGSIMServer_deleteTaskList(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_deleteTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_deleteTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_deleteTaskList.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_deleteTaskList : public Callback_ZGSIMServer_deleteTaskList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_deleteTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteTaskList(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_deleteTaskList.
 */
template<class T, typename CT> Callback_ZGSIMServer_deleteTaskListPtr
newCallback_ZGSIMServer_deleteTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_deleteTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_deleteTaskList.
 */
template<class T, typename CT> Callback_ZGSIMServer_deleteTaskListPtr
newCallback_ZGSIMServer_deleteTaskList(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_deleteTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_clearTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_clearTaskList.
 */
template<class T>
class CallbackNC_ZGSIMServer_clearTaskList : public Callback_ZGSIMServer_clearTaskList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_clearTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_clearTaskList(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_clearTaskList.
 */
template<class T> Callback_ZGSIMServer_clearTaskListPtr
newCallback_ZGSIMServer_clearTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_clearTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_clearTaskList.
 */
template<class T> Callback_ZGSIMServer_clearTaskListPtr
newCallback_ZGSIMServer_clearTaskList(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_clearTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_clearTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_clearTaskList.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_clearTaskList : public Callback_ZGSIMServer_clearTaskList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_clearTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_clearTaskList(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_clearTaskList.
 */
template<class T, typename CT> Callback_ZGSIMServer_clearTaskListPtr
newCallback_ZGSIMServer_clearTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_clearTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_clearTaskList.
 */
template<class T, typename CT> Callback_ZGSIMServer_clearTaskListPtr
newCallback_ZGSIMServer_clearTaskList(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_clearTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getTaskList.
 */
template<class T>
class CallbackNC_ZGSIMServer_getTaskList : public Callback_ZGSIMServer_getTaskList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTaskList.
 */
template<class T> Callback_ZGSIMServer_getTaskListPtr
newCallback_ZGSIMServer_getTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTaskList.
 */
template<class T> Callback_ZGSIMServer_getTaskListPtr
newCallback_ZGSIMServer_getTaskList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getTaskList.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getTaskList : public Callback_ZGSIMServer_getTaskList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskList(iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTaskList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getTaskListPtr
newCallback_ZGSIMServer_getTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTaskList.
 */
template<class T, typename CT> Callback_ZGSIMServer_getTaskListPtr
newCallback_ZGSIMServer_getTaskList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setTaskTest.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setTaskTest.
 */
template<class T>
class CallbackNC_ZGSIMServer_setTaskTest : public Callback_ZGSIMServer_setTaskTest_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSIMServer_setTaskTest(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setTaskTest(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setTaskTest.
 */
template<class T> Callback_ZGSIMServer_setTaskTestPtr
newCallback_ZGSIMServer_setTaskTest(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setTaskTest<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setTaskTest.
 */
template<class T> Callback_ZGSIMServer_setTaskTestPtr
newCallback_ZGSIMServer_setTaskTest(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_setTaskTest<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_setTaskTest.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_setTaskTest.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_setTaskTest : public Callback_ZGSIMServer_setTaskTest_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_setTaskTest(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setTaskTest(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setTaskTest.
 */
template<class T, typename CT> Callback_ZGSIMServer_setTaskTestPtr
newCallback_ZGSIMServer_setTaskTest(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setTaskTest<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_setTaskTest.
 */
template<class T, typename CT> Callback_ZGSIMServer_setTaskTestPtr
newCallback_ZGSIMServer_setTaskTest(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_setTaskTest<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getTaskTest.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getTaskTest.
 */
template<class T>
class CallbackNC_ZGSIMServer_getTaskTest : public Callback_ZGSIMServer_getTaskTest_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&);

    CallbackNC_ZGSIMServer_getTaskTest(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_testState;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskTest(iceP_testState, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_testState, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTaskTest.
 */
template<class T> Callback_ZGSIMServer_getTaskTestPtr
newCallback_ZGSIMServer_getTaskTest(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getTaskTest<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTaskTest.
 */
template<class T> Callback_ZGSIMServer_getTaskTestPtr
newCallback_ZGSIMServer_getTaskTest(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSIMServer_getTaskTest<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSIMServer::begin_getTaskTest.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSIMServer_getTaskTest.
 */
template<class T, typename CT>
class Callback_ZGSIMServer_getTaskTest : public Callback_ZGSIMServer_getTaskTest_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&, const CT&);

    Callback_ZGSIMServer_getTaskTest(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSIMServerPrx proxy = ZGSIMServerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_testState;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskTest(iceP_testState, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_testState, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTaskTest.
 */
template<class T, typename CT> Callback_ZGSIMServer_getTaskTestPtr
newCallback_ZGSIMServer_getTaskTest(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getTaskTest<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSIMServer::begin_getTaskTest.
 */
template<class T, typename CT> Callback_ZGSIMServer_getTaskTestPtr
newCallback_ZGSIMServer_getTaskTest(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSIMServer_getTaskTest<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

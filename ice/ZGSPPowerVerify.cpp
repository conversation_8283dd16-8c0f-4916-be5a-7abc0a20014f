//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPPowerVerify.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSPPowerVerify.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPPowerVerify_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPPowerVerify",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSPPowerVerify_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getAvaiableUser",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "isUserHasPower",
    "loginByAuthDev",
    "loginByCard",
    "loginByPassword",
    "loginByPasswordAndVerifyCode",
    "logout",
    "pauseDebug",
    "resumeDebug",
    "sendVerifyCode",
    "startDebug",
    "stopDebug",
    "test",
    "verifyByAuthDev",
    "verifyByCard",
    "verifyByPassword",
    "verifyByPasswordAndVerifyCode"
};
const ::std::string iceC_ZG6000_ZGSPPowerVerify_isUserHasPower_name = "isUserHasPower";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_loginByPassword_name = "loginByPassword";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_loginByPasswordAndVerifyCode_name = "loginByPasswordAndVerifyCode";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_loginByCard_name = "loginByCard";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_loginByAuthDev_name = "loginByAuthDev";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_logout_name = "logout";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_getAvaiableUser_name = "getAvaiableUser";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_verifyByPassword_name = "verifyByPassword";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_name = "verifyByPasswordAndVerifyCode";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_verifyByCard_name = "verifyByCard";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_verifyByAuthDev_name = "verifyByAuthDev";
const ::std::string iceC_ZG6000_ZGSPPowerVerify_sendVerifyCode_name = "sendVerifyCode";

}

bool
ZG6000::ZGSPPowerVerify::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPPowerVerify_ids, iceC_ZG6000_ZGSPPowerVerify_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSPPowerVerify::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSPPowerVerify_ids[0], &iceC_ZG6000_ZGSPPowerVerify_ids[3]);
}

::std::string
ZG6000::ZGSPPowerVerify::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPPowerVerify::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSPPowerVerify";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_isUserHasPower(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_userID, iceP_powerID);
    inS.endReadParams();
    bool iceP_hasPower;
    ErrorInfo iceP_e;
    bool ret = this->isUserHasPower(::std::move(iceP_userID), ::std::move(iceP_powerID), iceP_hasPower, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_hasPower, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_loginByPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    int iceP_keepTime;
    istr->readAll(iceP_clientID, iceP_userID, iceP_password, iceP_keepTime);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->loginByPassword(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_password), iceP_keepTime, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_loginByPasswordAndVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::std::string iceP_verifyCode;
    int iceP_keepTime;
    istr->readAll(iceP_clientID, iceP_userID, iceP_password, iceP_verifyCode, iceP_keepTime);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->loginByPasswordAndVerifyCode(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_password), ::std::move(iceP_verifyCode), iceP_keepTime, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_loginByCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_cardID;
    int iceP_keepTime;
    istr->readAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_keepTime);
    inS.endReadParams();
    ::std::string iceP_realUserID;
    ErrorInfo iceP_e;
    bool ret = this->loginByCard(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_authModeID), ::std::move(iceP_cardID), iceP_keepTime, iceP_realUserID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_realUserID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_loginByAuthDev(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    int iceP_keepTime;
    istr->readAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_keepTime);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->loginByAuthDev(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_authModeID), iceP_keepTime, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_logout(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    istr->readAll(iceP_clientID, iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->logout(::std::move(iceP_clientID), ::std::move(iceP_userID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_getAvaiableUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_clientID, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ListStringMap iceP_lstUser;
    ErrorInfo iceP_e;
    bool ret = this->getAvaiableUser(::std::move(iceP_clientID), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_lstUser, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_lstUser, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_verifyByPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_clientID, iceP_userID, iceP_password, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByPassword(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_password), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_verifyByPasswordAndVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::std::string iceP_verifyCode;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_clientID, iceP_userID, iceP_password, iceP_verifyCode, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByPasswordAndVerifyCode(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_password), ::std::move(iceP_verifyCode), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_verifyByCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_cardID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ::std::string iceP_realUserID;
    ErrorInfo iceP_e;
    bool ret = this->verifyByCard(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_authModeID), ::std::move(iceP_cardID), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_realUserID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_realUserID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_verifyByAuthDev(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByAuthDev(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_authModeID), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_sendVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->readAll(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->sendVerifyCode(::std::move(iceP_userID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPPowerVerify_ops, iceC_ZG6000_ZGSPPowerVerify_ops + 27, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPPowerVerify_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getAvaiableUser(in, current);
        }
        case 4:
        {
            return _iceD_getVersion(in, current);
        }
        case 5:
        {
            return _iceD_heartDebug(in, current);
        }
        case 6:
        {
            return _iceD_ice_id(in, current);
        }
        case 7:
        {
            return _iceD_ice_ids(in, current);
        }
        case 8:
        {
            return _iceD_ice_isA(in, current);
        }
        case 9:
        {
            return _iceD_ice_ping(in, current);
        }
        case 10:
        {
            return _iceD_isDebugging(in, current);
        }
        case 11:
        {
            return _iceD_isUserHasPower(in, current);
        }
        case 12:
        {
            return _iceD_loginByAuthDev(in, current);
        }
        case 13:
        {
            return _iceD_loginByCard(in, current);
        }
        case 14:
        {
            return _iceD_loginByPassword(in, current);
        }
        case 15:
        {
            return _iceD_loginByPasswordAndVerifyCode(in, current);
        }
        case 16:
        {
            return _iceD_logout(in, current);
        }
        case 17:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 18:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 19:
        {
            return _iceD_sendVerifyCode(in, current);
        }
        case 20:
        {
            return _iceD_startDebug(in, current);
        }
        case 21:
        {
            return _iceD_stopDebug(in, current);
        }
        case 22:
        {
            return _iceD_test(in, current);
        }
        case 23:
        {
            return _iceD_verifyByAuthDev(in, current);
        }
        case 24:
        {
            return _iceD_verifyByCard(in, current);
        }
        case 25:
        {
            return _iceD_verifyByPassword(in, current);
        }
        case 26:
        {
            return _iceD_verifyByPasswordAndVerifyCode(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_isUserHasPower(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::IsUserHasPowerResult>>& outAsync, const ::std::string& iceP_userID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_isUserHasPower_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_isUserHasPower_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::IsUserHasPowerResult v;
            istr->readAll(v.hasPower, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_loginByPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::LoginByPasswordResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, int iceP_keepTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_loginByPassword_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_loginByPassword_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_password, iceP_keepTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::LoginByPasswordResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_loginByPasswordAndVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::LoginByPasswordAndVerifyCodeResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, const ::std::string& iceP_verifyCode, int iceP_keepTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_loginByPasswordAndVerifyCode_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_loginByPasswordAndVerifyCode_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_password, iceP_verifyCode, iceP_keepTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::LoginByPasswordAndVerifyCodeResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_loginByCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::LoginByCardResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_cardID, int iceP_keepTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_loginByCard_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_loginByCard_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_keepTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::LoginByCardResult v;
            istr->readAll(v.realUserID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_loginByAuthDev(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::LoginByAuthDevResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, int iceP_keepTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_loginByAuthDev_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_loginByAuthDev_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_keepTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::LoginByAuthDevResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_logout(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::LogoutResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_logout_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_logout_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::LogoutResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_getAvaiableUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::GetAvaiableUserResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_getAvaiableUser_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_getAvaiableUser_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::GetAvaiableUserResult v;
            istr->readAll(v.lstUser, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_verifyByPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::VerifyByPasswordResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_verifyByPassword_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_verifyByPassword_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_password, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::VerifyByPasswordResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_verifyByPasswordAndVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::VerifyByPasswordAndVerifyCodeResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, const ::std::string& iceP_verifyCode, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_password, iceP_verifyCode, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::VerifyByPasswordAndVerifyCodeResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_verifyByCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::VerifyByCardResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_cardID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_verifyByCard_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_verifyByCard_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::VerifyByCardResult v;
            istr->readAll(v.realUserID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_verifyByAuthDev(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::VerifyByAuthDevResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_verifyByAuthDev_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_verifyByAuthDev_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::VerifyByAuthDevResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPPowerVerifyPrx::_iceI_sendVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPPowerVerify::SendVerifyCodeResult>>& outAsync, const ::std::string& iceP_userID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_sendVerifyCode_name);
    outAsync->invoke(iceC_ZG6000_ZGSPPowerVerify_sendVerifyCode_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPPowerVerify::SendVerifyCodeResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSPPowerVerifyPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSPPowerVerifyPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSPPowerVerifyPrx::ice_staticId()
{
    return ZGSPPowerVerify::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPPowerVerify_isUserHasPower_name = "isUserHasPower";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_loginByPassword_name = "loginByPassword";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_loginByPasswordAndVerifyCode_name = "loginByPasswordAndVerifyCode";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_loginByCard_name = "loginByCard";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_loginByAuthDev_name = "loginByAuthDev";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_logout_name = "logout";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_getAvaiableUser_name = "getAvaiableUser";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_verifyByPassword_name = "verifyByPassword";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_name = "verifyByPasswordAndVerifyCode";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_verifyByCard_name = "verifyByCard";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_verifyByAuthDev_name = "verifyByAuthDev";

const ::std::string iceC_ZG6000_ZGSPPowerVerify_sendVerifyCode_name = "sendVerifyCode";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSPPowerVerify* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSPPowerVerify>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSPPowerVerify;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_isUserHasPower(const ::std::string& iceP_userID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_isUserHasPower_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_isUserHasPower_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_isUserHasPower_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_isUserHasPower_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_isUserHasPower(bool& iceP_hasPower, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_isUserHasPower_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_hasPower);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_isUserHasPower(bool& iceP_hasPower, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_isUserHasPower_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_hasPower);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_loginByPassword(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, ::Ice::Int iceP_keepTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_loginByPassword_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_loginByPassword_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_loginByPassword_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_password);
        ostr->write(iceP_keepTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_loginByPassword_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_loginByPassword(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_loginByPassword_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_loginByPassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_loginByPassword_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_loginByPasswordAndVerifyCode(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, const ::std::string& iceP_verifyCode, ::Ice::Int iceP_keepTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_loginByPasswordAndVerifyCode_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_loginByPasswordAndVerifyCode_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_loginByPasswordAndVerifyCode_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_password);
        ostr->write(iceP_verifyCode);
        ostr->write(iceP_keepTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_loginByPasswordAndVerifyCode_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_loginByPasswordAndVerifyCode(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_loginByPasswordAndVerifyCode_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_loginByPasswordAndVerifyCode(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_loginByPasswordAndVerifyCode_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_loginByCard(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_cardID, ::Ice::Int iceP_keepTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_loginByCard_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_loginByCard_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_loginByCard_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_authModeID);
        ostr->write(iceP_cardID);
        ostr->write(iceP_keepTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_loginByCard_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_loginByCard(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_loginByCard_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_realUserID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_loginByCard(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_loginByCard_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_realUserID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_loginByAuthDev(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, ::Ice::Int iceP_keepTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_loginByAuthDev_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_loginByAuthDev_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_loginByAuthDev_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_authModeID);
        ostr->write(iceP_keepTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_loginByAuthDev_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_loginByAuthDev(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_loginByAuthDev_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_loginByAuthDev(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_loginByAuthDev_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_logout(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_logout_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_logout_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_logout_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_logout_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_logout(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_logout_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_logout(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_logout_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_getAvaiableUser(const ::std::string& iceP_clientID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_getAvaiableUser_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_getAvaiableUser_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_getAvaiableUser_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_getAvaiableUser_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_getAvaiableUser(::ZG6000::ListStringMap& iceP_lstUser, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_getAvaiableUser_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_lstUser);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_getAvaiableUser(::ZG6000::ListStringMap& iceP_lstUser, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_getAvaiableUser_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_lstUser);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_verifyByPassword(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_verifyByPassword_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_verifyByPassword_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_verifyByPassword_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_password);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_verifyByPassword_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_verifyByPassword(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_verifyByPassword_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_verifyByPassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_verifyByPassword_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_verifyByPasswordAndVerifyCode(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, const ::std::string& iceP_verifyCode, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_password);
        ostr->write(iceP_verifyCode);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_verifyByPasswordAndVerifyCode(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_verifyByPasswordAndVerifyCode(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_verifyByPasswordAndVerifyCode_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_verifyByCard(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_cardID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_verifyByCard_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_verifyByCard_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_verifyByCard_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_authModeID);
        ostr->write(iceP_cardID);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_verifyByCard_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_verifyByCard(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_verifyByCard_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_realUserID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_verifyByCard(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_verifyByCard_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_realUserID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_verifyByAuthDev(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_verifyByAuthDev_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_verifyByAuthDev_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_verifyByAuthDev_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_authModeID);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_verifyByAuthDev_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_verifyByAuthDev(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_verifyByAuthDev_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_verifyByAuthDev(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_verifyByAuthDev_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPPowerVerify::_iceI_begin_sendVerifyCode(const ::std::string& iceP_userID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPPowerVerify_sendVerifyCode_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPPowerVerify_sendVerifyCode_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPPowerVerify_sendVerifyCode_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPPowerVerify_sendVerifyCode_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPPowerVerify::end_sendVerifyCode(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_sendVerifyCode_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPPowerVerify::_iceI_end_sendVerifyCode(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPPowerVerify_sendVerifyCode_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSPPowerVerify::_newInstance() const
{
    return new ZGSPPowerVerify;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSPPowerVerify::ice_staticId()
{
    return ::ZG6000::ZGSPPowerVerify::ice_staticId();
}

ZG6000::ZGSPPowerVerify::~ZGSPPowerVerify()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSPPowerVerify* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPPowerVerify_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPPowerVerify",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSPPowerVerify::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPPowerVerify_ids, iceC_ZG6000_ZGSPPowerVerify_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSPPowerVerify::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSPPowerVerify_ids[0], &iceC_ZG6000_ZGSPPowerVerify_ids[3]);
}

const ::std::string&
ZG6000::ZGSPPowerVerify::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPPowerVerify::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSPPowerVerify";
    return typeId;
#else
    return iceC_ZG6000_ZGSPPowerVerify_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_isUserHasPower(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_powerID;
    istr->read(iceP_userID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    bool iceP_hasPower;
    ErrorInfo iceP_e;
    bool ret = this->isUserHasPower(iceP_userID, iceP_powerID, iceP_hasPower, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_hasPower);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_loginByPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::Ice::Int iceP_keepTime;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_password);
    istr->read(iceP_keepTime);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->loginByPassword(iceP_clientID, iceP_userID, iceP_password, iceP_keepTime, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_loginByPasswordAndVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::std::string iceP_verifyCode;
    ::Ice::Int iceP_keepTime;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_password);
    istr->read(iceP_verifyCode);
    istr->read(iceP_keepTime);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->loginByPasswordAndVerifyCode(iceP_clientID, iceP_userID, iceP_password, iceP_verifyCode, iceP_keepTime, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_loginByCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_cardID;
    ::Ice::Int iceP_keepTime;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_authModeID);
    istr->read(iceP_cardID);
    istr->read(iceP_keepTime);
    inS.endReadParams();
    ::std::string iceP_realUserID;
    ErrorInfo iceP_e;
    bool ret = this->loginByCard(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_keepTime, iceP_realUserID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_realUserID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_loginByAuthDev(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::Ice::Int iceP_keepTime;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_authModeID);
    istr->read(iceP_keepTime);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->loginByAuthDev(iceP_clientID, iceP_userID, iceP_authModeID, iceP_keepTime, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_logout(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->logout(iceP_clientID, iceP_userID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_getAvaiableUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_clientID);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ListStringMap iceP_lstUser;
    ErrorInfo iceP_e;
    bool ret = this->getAvaiableUser(iceP_clientID, iceP_appNodeID, iceP_powerID, iceP_lstUser, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_lstUser);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_verifyByPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_password);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByPassword(iceP_clientID, iceP_userID, iceP_password, iceP_appNodeID, iceP_powerID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_verifyByPasswordAndVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::std::string iceP_verifyCode;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_password);
    istr->read(iceP_verifyCode);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByPasswordAndVerifyCode(iceP_clientID, iceP_userID, iceP_password, iceP_verifyCode, iceP_appNodeID, iceP_powerID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_verifyByCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_cardID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_authModeID);
    istr->read(iceP_cardID);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ::std::string iceP_realUserID;
    ErrorInfo iceP_e;
    bool ret = this->verifyByCard(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_appNodeID, iceP_powerID, iceP_realUserID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_realUserID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_verifyByAuthDev(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_authModeID);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByAuthDev(iceP_clientID, iceP_userID, iceP_authModeID, iceP_appNodeID, iceP_powerID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceD_sendVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->read(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->sendVerifyCode(iceP_userID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPPowerVerify_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getAvaiableUser",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "isUserHasPower",
    "loginByAuthDev",
    "loginByCard",
    "loginByPassword",
    "loginByPasswordAndVerifyCode",
    "logout",
    "pauseDebug",
    "resumeDebug",
    "sendVerifyCode",
    "startDebug",
    "stopDebug",
    "test",
    "verifyByAuthDev",
    "verifyByCard",
    "verifyByPassword",
    "verifyByPasswordAndVerifyCode"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSPPowerVerify::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPPowerVerify_all, iceC_ZG6000_ZGSPPowerVerify_all + 27, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPPowerVerify_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getAvaiableUser(in, current);
        }
        case 4:
        {
            return _iceD_getVersion(in, current);
        }
        case 5:
        {
            return _iceD_heartDebug(in, current);
        }
        case 6:
        {
            return _iceD_ice_id(in, current);
        }
        case 7:
        {
            return _iceD_ice_ids(in, current);
        }
        case 8:
        {
            return _iceD_ice_isA(in, current);
        }
        case 9:
        {
            return _iceD_ice_ping(in, current);
        }
        case 10:
        {
            return _iceD_isDebugging(in, current);
        }
        case 11:
        {
            return _iceD_isUserHasPower(in, current);
        }
        case 12:
        {
            return _iceD_loginByAuthDev(in, current);
        }
        case 13:
        {
            return _iceD_loginByCard(in, current);
        }
        case 14:
        {
            return _iceD_loginByPassword(in, current);
        }
        case 15:
        {
            return _iceD_loginByPasswordAndVerifyCode(in, current);
        }
        case 16:
        {
            return _iceD_logout(in, current);
        }
        case 17:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 18:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 19:
        {
            return _iceD_sendVerifyCode(in, current);
        }
        case 20:
        {
            return _iceD_startDebug(in, current);
        }
        case 21:
        {
            return _iceD_stopDebug(in, current);
        }
        case 22:
        {
            return _iceD_test(in, current);
        }
        case 23:
        {
            return _iceD_verifyByAuthDev(in, current);
        }
        case 24:
        {
            return _iceD_verifyByCard(in, current);
        }
        case 25:
        {
            return _iceD_verifyByPassword(in, current);
        }
        case 26:
        {
            return _iceD_verifyByPasswordAndVerifyCode(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSPPowerVerify::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSPPowerVerify, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSPPowerVerify::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSPPowerVerify, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSPPowerVerifyPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSPPowerVerifyPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSPPowerVerify::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

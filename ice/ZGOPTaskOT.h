//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPTaskOT.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGOPTaskOT_h__
#define __ZGOPTaskOT_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGOPTaskBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGOPTaskOT;
class ZGOPTaskOTPrx;

}

namespace ZG6000
{

class ZGOPTaskOT : public virtual ZGOPTaskBase
{
public:

    using ProxyType = ZGOPTaskOTPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getOT.
     */
    struct GetOTResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        StringMap head;
        ListStringMap items;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取操作票信息
     */
    virtual bool getOT(::std::string otID, StringMap& head, ListStringMap& items, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getOT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to createOT.
     */
    struct CreateOTResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ::std::string otID;
        ErrorInfo e;
    };

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建操作票
     */
    virtual bool createOT(::std::string taskTypeID, StringMap param, ::std::string& otID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createOT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to editOT.
     */
    struct EditOTResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	编辑操作票
     */
    virtual bool editOT(::std::string otID, StringMap head, ListStringMap items, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_editOT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to convertOT.
     */
    struct ConvertOTResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	转换操作票类型
     */
    virtual bool convertOT(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_convertOT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to skipItem.
     */
    struct SkipItemResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	跳过当前步骤
     */
    virtual bool skipItem(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_skipItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to startPreview.
     */
    struct StartPreviewResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	开始预演
     */
    virtual bool startPreview(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_startPreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to stopPreview.
     */
    struct StopPreviewResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	停止预演
     */
    virtual bool stopPreview(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_stopPreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to pausePreview.
     */
    struct PausePreviewResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	暂停预演
     */
    virtual bool pausePreview(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_pausePreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to resumePreview.
     */
    struct ResumePreviewResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	继续预演
     */
    virtual bool resumePreview(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_resumePreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to retryPreview.
     */
    struct RetryPreviewResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	重试预演步骤
     */
    virtual bool retryPreview(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_retryPreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to confirmPreview.
     */
    struct ConfirmPreviewResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	确认预演
     */
    virtual bool confirmPreview(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmPreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDeviceTerm.
     */
    struct GetDeviceTermResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ListStringMap terms;
        ErrorInfo e;
    };

    /**
     * @param param 相关参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取设备操作术语
     */
    virtual bool getDeviceTerm(::std::string deviceID, StringMap param, ListStringMap& terms, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceTerm(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getCommonTerm.
     */
    struct GetCommonTermResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ListStringMap terms;
        ErrorInfo e;
    };

    /**
     * @param param 相关参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取公共操作术语
     */
    virtual bool getCommonTerm(StringMap param, ListStringMap& terms, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getCommonTerm(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to createOtItem.
     */
    struct CreateOtItemResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ListStringMap items;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建操作票项
     */
    virtual bool createOtItem(::std::string otID, StringMap param, ListStringMap& items, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createOtItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deleteOtItem.
     */
    struct DeleteOtItemResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	删除操作票项
     */
    virtual bool deleteOtItem(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteOtItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setOtSimulateValue.
     */
    struct SetOtSimulateValueResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	设置操作票模拟值
     */
    virtual bool setOtSimulateValue(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setOtSimulateValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to clearOtSimulateValue.
     */
    struct ClearOtSimulateValueResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	清除操作票模拟值
     */
    virtual bool clearOtSimulateValue(::std::string otID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_clearOtSimulateValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to downloadTask.
     */
    struct DownloadTaskResult
    {
        bool returnValue;
        ListStringMap listTask;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool downloadTask(::std::string clientID, StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_downloadTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateTask.
     */
    struct UpdateTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateTask(ListStringMap listTask, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateItem.
     */
    struct UpdateItemResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateItem(ListStringMap listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGOPTaskOTPrx : public virtual ::Ice::Proxy<ZGOPTaskOTPrx, ZGOPTaskBasePrx>
{
public:

    /**
     * @param otID 操作票ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取操作票信息
     */
    bool getOT(const ::std::string& otID, StringMap& head, ListStringMap& items, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::GetOTResult>(true, this, &ZGOPTaskOTPrx::_iceI_getOT, otID, context).get();
        head = ::std::move(_result.head);
        items = ::std::move(_result.items);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	获取操作票信息
     */
    template<template<typename> class P = ::std::promise>
    auto getOTAsync(const ::std::string& otID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::GetOTResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::GetOTResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_getOT, otID, context);
    }

    /**
     * @param otID 操作票ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	获取操作票信息
     */
    ::std::function<void()>
    getOTAsync(const ::std::string& otID,
               ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
               ::std::function<void(::std::exception_ptr)> ex = nullptr,
               ::std::function<void(bool)> sent = nullptr,
               const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::GetOTResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.head), ::std::move(_result.items), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::GetOTResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_getOT, otID, context);
    }

    /// \cond INTERNAL
    void _iceI_getOT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::GetOTResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建操作票
     */
    bool createOT(const ::std::string& taskTypeID, const StringMap& param, ::std::string& otID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::CreateOTResult>(true, this, &ZGOPTaskOTPrx::_iceI_createOT, taskTypeID, param, context).get();
        otID = ::std::move(_result.otID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	创建操作票
     */
    template<template<typename> class P = ::std::promise>
    auto createOTAsync(const ::std::string& taskTypeID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::CreateOTResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::CreateOTResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_createOT, taskTypeID, param, context);
    }

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	创建操作票
     */
    ::std::function<void()>
    createOTAsync(const ::std::string& taskTypeID, const StringMap& param,
                  ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::CreateOTResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.otID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::CreateOTResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_createOT, taskTypeID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_createOT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::CreateOTResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	编辑操作票
     */
    bool editOT(const ::std::string& otID, const StringMap& head, const ListStringMap& items, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::EditOTResult>(true, this, &ZGOPTaskOTPrx::_iceI_editOT, otID, head, items, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	编辑操作票
     */
    template<template<typename> class P = ::std::promise>
    auto editOTAsync(const ::std::string& otID, const StringMap& head, const ListStringMap& items, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::EditOTResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::EditOTResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_editOT, otID, head, items, context);
    }

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	编辑操作票
     */
    ::std::function<void()>
    editOTAsync(const ::std::string& otID, const StringMap& head, const ListStringMap& items,
                ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                ::std::function<void(::std::exception_ptr)> ex = nullptr,
                ::std::function<void(bool)> sent = nullptr,
                const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::EditOTResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::EditOTResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_editOT, otID, head, items, context);
    }

    /// \cond INTERNAL
    void _iceI_editOT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::EditOTResult>>&, const ::std::string&, const StringMap&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	转换操作票类型
     */
    bool convertOT(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::ConvertOTResult>(true, this, &ZGOPTaskOTPrx::_iceI_convertOT, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	转换操作票类型
     */
    template<template<typename> class P = ::std::promise>
    auto convertOTAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::ConvertOTResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::ConvertOTResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_convertOT, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	转换操作票类型
     */
    ::std::function<void()>
    convertOTAsync(const ::std::string& otID, const StringMap& param,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::ConvertOTResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::ConvertOTResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_convertOT, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_convertOT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::ConvertOTResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	跳过当前步骤
     */
    bool skipItem(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::SkipItemResult>(true, this, &ZGOPTaskOTPrx::_iceI_skipItem, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	跳过当前步骤
     */
    template<template<typename> class P = ::std::promise>
    auto skipItemAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::SkipItemResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::SkipItemResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_skipItem, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	跳过当前步骤
     */
    ::std::function<void()>
    skipItemAsync(const ::std::string& otID, const StringMap& param,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::SkipItemResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::SkipItemResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_skipItem, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_skipItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::SkipItemResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	开始预演
     */
    bool startPreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::StartPreviewResult>(true, this, &ZGOPTaskOTPrx::_iceI_startPreview, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	开始预演
     */
    template<template<typename> class P = ::std::promise>
    auto startPreviewAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::StartPreviewResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::StartPreviewResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_startPreview, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	开始预演
     */
    ::std::function<void()>
    startPreviewAsync(const ::std::string& otID, const StringMap& param,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::StartPreviewResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::StartPreviewResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_startPreview, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_startPreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::StartPreviewResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	停止预演
     */
    bool stopPreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::StopPreviewResult>(true, this, &ZGOPTaskOTPrx::_iceI_stopPreview, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	停止预演
     */
    template<template<typename> class P = ::std::promise>
    auto stopPreviewAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::StopPreviewResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::StopPreviewResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_stopPreview, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	停止预演
     */
    ::std::function<void()>
    stopPreviewAsync(const ::std::string& otID, const StringMap& param,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::StopPreviewResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::StopPreviewResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_stopPreview, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_stopPreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::StopPreviewResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	暂停预演
     */
    bool pausePreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::PausePreviewResult>(true, this, &ZGOPTaskOTPrx::_iceI_pausePreview, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	暂停预演
     */
    template<template<typename> class P = ::std::promise>
    auto pausePreviewAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::PausePreviewResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::PausePreviewResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_pausePreview, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	暂停预演
     */
    ::std::function<void()>
    pausePreviewAsync(const ::std::string& otID, const StringMap& param,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::PausePreviewResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::PausePreviewResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_pausePreview, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_pausePreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::PausePreviewResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	继续预演
     */
    bool resumePreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::ResumePreviewResult>(true, this, &ZGOPTaskOTPrx::_iceI_resumePreview, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	继续预演
     */
    template<template<typename> class P = ::std::promise>
    auto resumePreviewAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::ResumePreviewResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::ResumePreviewResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_resumePreview, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	继续预演
     */
    ::std::function<void()>
    resumePreviewAsync(const ::std::string& otID, const StringMap& param,
                       ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::ResumePreviewResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::ResumePreviewResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_resumePreview, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_resumePreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::ResumePreviewResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	重试预演步骤
     */
    bool retryPreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::RetryPreviewResult>(true, this, &ZGOPTaskOTPrx::_iceI_retryPreview, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	重试预演步骤
     */
    template<template<typename> class P = ::std::promise>
    auto retryPreviewAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::RetryPreviewResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::RetryPreviewResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_retryPreview, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	重试预演步骤
     */
    ::std::function<void()>
    retryPreviewAsync(const ::std::string& otID, const StringMap& param,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::RetryPreviewResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::RetryPreviewResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_retryPreview, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_retryPreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::RetryPreviewResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	确认预演
     */
    bool confirmPreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::ConfirmPreviewResult>(true, this, &ZGOPTaskOTPrx::_iceI_confirmPreview, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	确认预演
     */
    template<template<typename> class P = ::std::promise>
    auto confirmPreviewAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::ConfirmPreviewResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::ConfirmPreviewResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_confirmPreview, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	确认预演
     */
    ::std::function<void()>
    confirmPreviewAsync(const ::std::string& otID, const StringMap& param,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::ConfirmPreviewResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::ConfirmPreviewResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_confirmPreview, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_confirmPreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::ConfirmPreviewResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取设备操作术语
     */
    bool getDeviceTerm(const ::std::string& deviceID, const StringMap& param, ListStringMap& terms, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::GetDeviceTermResult>(true, this, &ZGOPTaskOTPrx::_iceI_getDeviceTerm, deviceID, param, context).get();
        terms = ::std::move(_result.terms);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	获取设备操作术语
     */
    template<template<typename> class P = ::std::promise>
    auto getDeviceTermAsync(const ::std::string& deviceID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::GetDeviceTermResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::GetDeviceTermResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_getDeviceTerm, deviceID, param, context);
    }

    /**
     * @param param 相关参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	获取设备操作术语
     */
    ::std::function<void()>
    getDeviceTermAsync(const ::std::string& deviceID, const StringMap& param,
                       ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::GetDeviceTermResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.terms), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::GetDeviceTermResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_getDeviceTerm, deviceID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_getDeviceTerm(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::GetDeviceTermResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取公共操作术语
     */
    bool getCommonTerm(const StringMap& param, ListStringMap& terms, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::GetCommonTermResult>(true, this, &ZGOPTaskOTPrx::_iceI_getCommonTerm, param, context).get();
        terms = ::std::move(_result.terms);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	获取公共操作术语
     */
    template<template<typename> class P = ::std::promise>
    auto getCommonTermAsync(const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::GetCommonTermResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::GetCommonTermResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_getCommonTerm, param, context);
    }

    /**
     * @param param 相关参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	获取公共操作术语
     */
    ::std::function<void()>
    getCommonTermAsync(const StringMap& param,
                       ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::GetCommonTermResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.terms), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::GetCommonTermResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_getCommonTerm, param, context);
    }

    /// \cond INTERNAL
    void _iceI_getCommonTerm(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::GetCommonTermResult>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建操作票项
     */
    bool createOtItem(const ::std::string& otID, const StringMap& param, ListStringMap& items, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::CreateOtItemResult>(true, this, &ZGOPTaskOTPrx::_iceI_createOtItem, otID, param, context).get();
        items = ::std::move(_result.items);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	创建操作票项
     */
    template<template<typename> class P = ::std::promise>
    auto createOtItemAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::CreateOtItemResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::CreateOtItemResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_createOtItem, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	创建操作票项
     */
    ::std::function<void()>
    createOtItemAsync(const ::std::string& otID, const StringMap& param,
                      ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::CreateOtItemResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.items), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::CreateOtItemResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_createOtItem, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_createOtItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::CreateOtItemResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	删除操作票项
     */
    bool deleteOtItem(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::DeleteOtItemResult>(true, this, &ZGOPTaskOTPrx::_iceI_deleteOtItem, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	删除操作票项
     */
    template<template<typename> class P = ::std::promise>
    auto deleteOtItemAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::DeleteOtItemResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::DeleteOtItemResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_deleteOtItem, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	删除操作票项
     */
    ::std::function<void()>
    deleteOtItemAsync(const ::std::string& otID, const StringMap& param,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::DeleteOtItemResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::DeleteOtItemResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_deleteOtItem, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_deleteOtItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::DeleteOtItemResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	设置操作票模拟值
     */
    bool setOtSimulateValue(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::SetOtSimulateValueResult>(true, this, &ZGOPTaskOTPrx::_iceI_setOtSimulateValue, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	设置操作票模拟值
     */
    template<template<typename> class P = ::std::promise>
    auto setOtSimulateValueAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::SetOtSimulateValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::SetOtSimulateValueResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_setOtSimulateValue, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	设置操作票模拟值
     */
    ::std::function<void()>
    setOtSimulateValueAsync(const ::std::string& otID, const StringMap& param,
                            ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::SetOtSimulateValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::SetOtSimulateValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_setOtSimulateValue, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_setOtSimulateValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::SetOtSimulateValueResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	清除操作票模拟值
     */
    bool clearOtSimulateValue(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::ClearOtSimulateValueResult>(true, this, &ZGOPTaskOTPrx::_iceI_clearOtSimulateValue, otID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	清除操作票模拟值
     */
    template<template<typename> class P = ::std::promise>
    auto clearOtSimulateValueAsync(const ::std::string& otID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::ClearOtSimulateValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::ClearOtSimulateValueResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_clearOtSimulateValue, otID, param, context);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	清除操作票模拟值
     */
    ::std::function<void()>
    clearOtSimulateValueAsync(const ::std::string& otID, const StringMap& param,
                              ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                              ::std::function<void(::std::exception_ptr)> ex = nullptr,
                              ::std::function<void(bool)> sent = nullptr,
                              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::ClearOtSimulateValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::ClearOtSimulateValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_clearOtSimulateValue, otID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_clearOtSimulateValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::ClearOtSimulateValueResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool downloadTask(const ::std::string& clientID, const StringList& listTaskID, ListStringMap& listTask, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::DownloadTaskResult>(true, this, &ZGOPTaskOTPrx::_iceI_downloadTask, clientID, listTaskID, context).get();
        listTask = ::std::move(_result.listTask);
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto downloadTaskAsync(const ::std::string& clientID, const StringList& listTaskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::DownloadTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::DownloadTaskResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_downloadTask, clientID, listTaskID, context);
    }

    ::std::function<void()>
    downloadTaskAsync(const ::std::string& clientID, const StringList& listTaskID,
                      ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::DownloadTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listTask), ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::DownloadTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_downloadTask, clientID, listTaskID, context);
    }

    /// \cond INTERNAL
    void _iceI_downloadTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::DownloadTaskResult>>&, const ::std::string&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool updateTask(const ListStringMap& listTask, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::UpdateTaskResult>(true, this, &ZGOPTaskOTPrx::_iceI_updateTask, listTask, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateTaskAsync(const ListStringMap& listTask, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::UpdateTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::UpdateTaskResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_updateTask, listTask, context);
    }

    ::std::function<void()>
    updateTaskAsync(const ListStringMap& listTask,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::UpdateTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::UpdateTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_updateTask, listTask, context);
    }

    /// \cond INTERNAL
    void _iceI_updateTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::UpdateTaskResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool updateItem(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOT::UpdateItemResult>(true, this, &ZGOPTaskOTPrx::_iceI_updateItem, listItem, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateItemAsync(const ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOT::UpdateItemResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOT::UpdateItemResult, P>(false, this, &ZGOPTaskOTPrx::_iceI_updateItem, listItem, context);
    }

    ::std::function<void()>
    updateItemAsync(const ListStringMap& listItem,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOT::UpdateItemResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOT::UpdateItemResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOTPrx::_iceI_updateItem, listItem, context);
    }

    /// \cond INTERNAL
    void _iceI_updateItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::UpdateItemResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGOPTaskOTPrx() = default;
    friend ::std::shared_ptr<ZGOPTaskOTPrx> IceInternal::createProxy<ZGOPTaskOTPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGOPTaskOTPtr = ::std::shared_ptr<ZGOPTaskOT>;
using ZGOPTaskOTPrxPtr = ::std::shared_ptr<ZGOPTaskOTPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGOPTaskOT;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGOPTaskOT>&);
::IceProxy::Ice::Object* upCast(ZGOPTaskOT*);
/// \endcond

}

}

namespace ZG6000
{

class ZGOPTaskOT;
/// \cond INTERNAL
::Ice::Object* upCast(ZGOPTaskOT*);
/// \endcond
typedef ::IceInternal::Handle< ZGOPTaskOT> ZGOPTaskOTPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGOPTaskOT> ZGOPTaskOTPrx;
typedef ZGOPTaskOTPrx ZGOPTaskOTPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGOPTaskOTPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_getOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_getOT.
 */
class Callback_ZGOPTaskOT_getOT_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_getOT_Base> Callback_ZGOPTaskOT_getOTPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_createOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_createOT.
 */
class Callback_ZGOPTaskOT_createOT_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_createOT_Base> Callback_ZGOPTaskOT_createOTPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_editOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_editOT.
 */
class Callback_ZGOPTaskOT_editOT_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_editOT_Base> Callback_ZGOPTaskOT_editOTPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_convertOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_convertOT.
 */
class Callback_ZGOPTaskOT_convertOT_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_convertOT_Base> Callback_ZGOPTaskOT_convertOTPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_skipItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_skipItem.
 */
class Callback_ZGOPTaskOT_skipItem_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_skipItem_Base> Callback_ZGOPTaskOT_skipItemPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_startPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_startPreview.
 */
class Callback_ZGOPTaskOT_startPreview_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_startPreview_Base> Callback_ZGOPTaskOT_startPreviewPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_stopPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_stopPreview.
 */
class Callback_ZGOPTaskOT_stopPreview_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_stopPreview_Base> Callback_ZGOPTaskOT_stopPreviewPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_pausePreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_pausePreview.
 */
class Callback_ZGOPTaskOT_pausePreview_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_pausePreview_Base> Callback_ZGOPTaskOT_pausePreviewPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_resumePreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_resumePreview.
 */
class Callback_ZGOPTaskOT_resumePreview_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_resumePreview_Base> Callback_ZGOPTaskOT_resumePreviewPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_retryPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_retryPreview.
 */
class Callback_ZGOPTaskOT_retryPreview_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_retryPreview_Base> Callback_ZGOPTaskOT_retryPreviewPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_confirmPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_confirmPreview.
 */
class Callback_ZGOPTaskOT_confirmPreview_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_confirmPreview_Base> Callback_ZGOPTaskOT_confirmPreviewPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_getDeviceTerm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_getDeviceTerm.
 */
class Callback_ZGOPTaskOT_getDeviceTerm_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_getDeviceTerm_Base> Callback_ZGOPTaskOT_getDeviceTermPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_getCommonTerm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_getCommonTerm.
 */
class Callback_ZGOPTaskOT_getCommonTerm_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_getCommonTerm_Base> Callback_ZGOPTaskOT_getCommonTermPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_createOtItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_createOtItem.
 */
class Callback_ZGOPTaskOT_createOtItem_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_createOtItem_Base> Callback_ZGOPTaskOT_createOtItemPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_deleteOtItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_deleteOtItem.
 */
class Callback_ZGOPTaskOT_deleteOtItem_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_deleteOtItem_Base> Callback_ZGOPTaskOT_deleteOtItemPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_setOtSimulateValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_setOtSimulateValue.
 */
class Callback_ZGOPTaskOT_setOtSimulateValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_setOtSimulateValue_Base> Callback_ZGOPTaskOT_setOtSimulateValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_clearOtSimulateValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_clearOtSimulateValue.
 */
class Callback_ZGOPTaskOT_clearOtSimulateValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_clearOtSimulateValue_Base> Callback_ZGOPTaskOT_clearOtSimulateValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_downloadTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_downloadTask.
 */
class Callback_ZGOPTaskOT_downloadTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_downloadTask_Base> Callback_ZGOPTaskOT_downloadTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_updateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_updateTask.
 */
class Callback_ZGOPTaskOT_updateTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_updateTask_Base> Callback_ZGOPTaskOT_updateTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_updateItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_updateItem.
 */
class Callback_ZGOPTaskOT_updateItem_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOT_updateItem_Base> Callback_ZGOPTaskOT_updateItemPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGOPTaskOT : public virtual ::Ice::Proxy<ZGOPTaskOT, ::IceProxy::ZG6000::ZGOPTaskBase>
{
public:

    /**
     * @param otID 操作票ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取操作票信息
     */
    bool getOT(const ::std::string& otID, ::ZG6000::StringMap& head, ::ZG6000::ListStringMap& items, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getOT(head, items, e, _iceI_begin_getOT(otID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取操作票信息
     */
    ::Ice::AsyncResultPtr begin_getOT(const ::std::string& otID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getOT(otID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取操作票信息
     */
    ::Ice::AsyncResultPtr begin_getOT(const ::std::string& otID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getOT(otID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取操作票信息
     */
    ::Ice::AsyncResultPtr begin_getOT(const ::std::string& otID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getOT(otID, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取操作票信息
     */
    ::Ice::AsyncResultPtr begin_getOT(const ::std::string& otID, const ::ZG6000::Callback_ZGOPTaskOT_getOTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getOT(otID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取操作票信息
     */
    ::Ice::AsyncResultPtr begin_getOT(const ::std::string& otID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_getOTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getOT(otID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getOT.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getOT(::ZG6000::StringMap& head, ::ZG6000::ListStringMap& items, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getOT(::ZG6000::StringMap& iceP_head, ::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getOT(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建操作票
     */
    bool createOT(const ::std::string& taskTypeID, const ::ZG6000::StringMap& param, ::std::string& otID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createOT(otID, e, _iceI_begin_createOT(taskTypeID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建操作票
     */
    ::Ice::AsyncResultPtr begin_createOT(const ::std::string& taskTypeID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createOT(taskTypeID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建操作票
     */
    ::Ice::AsyncResultPtr begin_createOT(const ::std::string& taskTypeID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createOT(taskTypeID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建操作票
     */
    ::Ice::AsyncResultPtr begin_createOT(const ::std::string& taskTypeID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createOT(taskTypeID, param, context, cb, cookie);
    }

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建操作票
     */
    ::Ice::AsyncResultPtr begin_createOT(const ::std::string& taskTypeID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_createOTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createOT(taskTypeID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建操作票
     */
    ::Ice::AsyncResultPtr begin_createOT(const ::std::string& taskTypeID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_createOTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createOT(taskTypeID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_createOT.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_createOT(::std::string& otID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createOT(::std::string& iceP_otID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createOT(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	编辑操作票
     */
    bool editOT(const ::std::string& otID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& items, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_editOT(e, _iceI_begin_editOT(otID, head, items, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	编辑操作票
     */
    ::Ice::AsyncResultPtr begin_editOT(const ::std::string& otID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& items, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_editOT(otID, head, items, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	编辑操作票
     */
    ::Ice::AsyncResultPtr begin_editOT(const ::std::string& otID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& items, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editOT(otID, head, items, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	编辑操作票
     */
    ::Ice::AsyncResultPtr begin_editOT(const ::std::string& otID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& items, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editOT(otID, head, items, context, cb, cookie);
    }

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	编辑操作票
     */
    ::Ice::AsyncResultPtr begin_editOT(const ::std::string& otID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& items, const ::ZG6000::Callback_ZGOPTaskOT_editOTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editOT(otID, head, items, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	编辑操作票
     */
    ::Ice::AsyncResultPtr begin_editOT(const ::std::string& otID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& items, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_editOTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editOT(otID, head, items, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_editOT.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_editOT(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_editOT(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_editOT(const ::std::string&, const ::ZG6000::StringMap&, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	转换操作票类型
     */
    bool convertOT(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_convertOT(e, _iceI_begin_convertOT(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转换操作票类型
     */
    ::Ice::AsyncResultPtr begin_convertOT(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_convertOT(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转换操作票类型
     */
    ::Ice::AsyncResultPtr begin_convertOT(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertOT(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转换操作票类型
     */
    ::Ice::AsyncResultPtr begin_convertOT(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertOT(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转换操作票类型
     */
    ::Ice::AsyncResultPtr begin_convertOT(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_convertOTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertOT(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转换操作票类型
     */
    ::Ice::AsyncResultPtr begin_convertOT(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_convertOTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertOT(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_convertOT.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_convertOT(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_convertOT(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_convertOT(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	跳过当前步骤
     */
    bool skipItem(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_skipItem(e, _iceI_begin_skipItem(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	跳过当前步骤
     */
    ::Ice::AsyncResultPtr begin_skipItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_skipItem(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	跳过当前步骤
     */
    ::Ice::AsyncResultPtr begin_skipItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_skipItem(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	跳过当前步骤
     */
    ::Ice::AsyncResultPtr begin_skipItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_skipItem(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	跳过当前步骤
     */
    ::Ice::AsyncResultPtr begin_skipItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_skipItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_skipItem(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	跳过当前步骤
     */
    ::Ice::AsyncResultPtr begin_skipItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_skipItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_skipItem(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_skipItem.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_skipItem(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_skipItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_skipItem(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	开始预演
     */
    bool startPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_startPreview(e, _iceI_begin_startPreview(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	开始预演
     */
    ::Ice::AsyncResultPtr begin_startPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_startPreview(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	开始预演
     */
    ::Ice::AsyncResultPtr begin_startPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startPreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	开始预演
     */
    ::Ice::AsyncResultPtr begin_startPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startPreview(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	开始预演
     */
    ::Ice::AsyncResultPtr begin_startPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_startPreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startPreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	开始预演
     */
    ::Ice::AsyncResultPtr begin_startPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_startPreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startPreview(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_startPreview.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_startPreview(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_startPreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_startPreview(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	停止预演
     */
    bool stopPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_stopPreview(e, _iceI_begin_stopPreview(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	停止预演
     */
    ::Ice::AsyncResultPtr begin_stopPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_stopPreview(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	停止预演
     */
    ::Ice::AsyncResultPtr begin_stopPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopPreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	停止预演
     */
    ::Ice::AsyncResultPtr begin_stopPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopPreview(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	停止预演
     */
    ::Ice::AsyncResultPtr begin_stopPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_stopPreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopPreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	停止预演
     */
    ::Ice::AsyncResultPtr begin_stopPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_stopPreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopPreview(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_stopPreview.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_stopPreview(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_stopPreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_stopPreview(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	暂停预演
     */
    bool pausePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_pausePreview(e, _iceI_begin_pausePreview(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	暂停预演
     */
    ::Ice::AsyncResultPtr begin_pausePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_pausePreview(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	暂停预演
     */
    ::Ice::AsyncResultPtr begin_pausePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pausePreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	暂停预演
     */
    ::Ice::AsyncResultPtr begin_pausePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pausePreview(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	暂停预演
     */
    ::Ice::AsyncResultPtr begin_pausePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_pausePreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pausePreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	暂停预演
     */
    ::Ice::AsyncResultPtr begin_pausePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_pausePreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pausePreview(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_pausePreview.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_pausePreview(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_pausePreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_pausePreview(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	继续预演
     */
    bool resumePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_resumePreview(e, _iceI_begin_resumePreview(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	继续预演
     */
    ::Ice::AsyncResultPtr begin_resumePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_resumePreview(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	继续预演
     */
    ::Ice::AsyncResultPtr begin_resumePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumePreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	继续预演
     */
    ::Ice::AsyncResultPtr begin_resumePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumePreview(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	继续预演
     */
    ::Ice::AsyncResultPtr begin_resumePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_resumePreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumePreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	继续预演
     */
    ::Ice::AsyncResultPtr begin_resumePreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_resumePreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumePreview(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_resumePreview.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_resumePreview(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_resumePreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_resumePreview(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	重试预演步骤
     */
    bool retryPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_retryPreview(e, _iceI_begin_retryPreview(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	重试预演步骤
     */
    ::Ice::AsyncResultPtr begin_retryPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_retryPreview(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	重试预演步骤
     */
    ::Ice::AsyncResultPtr begin_retryPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_retryPreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	重试预演步骤
     */
    ::Ice::AsyncResultPtr begin_retryPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_retryPreview(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	重试预演步骤
     */
    ::Ice::AsyncResultPtr begin_retryPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_retryPreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_retryPreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	重试预演步骤
     */
    ::Ice::AsyncResultPtr begin_retryPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_retryPreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_retryPreview(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_retryPreview.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_retryPreview(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_retryPreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_retryPreview(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	确认预演
     */
    bool confirmPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_confirmPreview(e, _iceI_begin_confirmPreview(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	确认预演
     */
    ::Ice::AsyncResultPtr begin_confirmPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_confirmPreview(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	确认预演
     */
    ::Ice::AsyncResultPtr begin_confirmPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmPreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	确认预演
     */
    ::Ice::AsyncResultPtr begin_confirmPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmPreview(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	确认预演
     */
    ::Ice::AsyncResultPtr begin_confirmPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_confirmPreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmPreview(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	确认预演
     */
    ::Ice::AsyncResultPtr begin_confirmPreview(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_confirmPreviewPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmPreview(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_confirmPreview.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_confirmPreview(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_confirmPreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_confirmPreview(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取设备操作术语
     */
    bool getDeviceTerm(const ::std::string& deviceID, const ::ZG6000::StringMap& param, ::ZG6000::ListStringMap& terms, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDeviceTerm(terms, e, _iceI_begin_getDeviceTerm(deviceID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取设备操作术语
     */
    ::Ice::AsyncResultPtr begin_getDeviceTerm(const ::std::string& deviceID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDeviceTerm(deviceID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param param 相关参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取设备操作术语
     */
    ::Ice::AsyncResultPtr begin_getDeviceTerm(const ::std::string& deviceID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceTerm(deviceID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取设备操作术语
     */
    ::Ice::AsyncResultPtr begin_getDeviceTerm(const ::std::string& deviceID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceTerm(deviceID, param, context, cb, cookie);
    }

    /**
     * @param param 相关参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取设备操作术语
     */
    ::Ice::AsyncResultPtr begin_getDeviceTerm(const ::std::string& deviceID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_getDeviceTermPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceTerm(deviceID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取设备操作术语
     */
    ::Ice::AsyncResultPtr begin_getDeviceTerm(const ::std::string& deviceID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_getDeviceTermPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceTerm(deviceID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getDeviceTerm.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getDeviceTerm(::ZG6000::ListStringMap& terms, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDeviceTerm(::ZG6000::ListStringMap& iceP_terms, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDeviceTerm(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取公共操作术语
     */
    bool getCommonTerm(const ::ZG6000::StringMap& param, ::ZG6000::ListStringMap& terms, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getCommonTerm(terms, e, _iceI_begin_getCommonTerm(param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取公共操作术语
     */
    ::Ice::AsyncResultPtr begin_getCommonTerm(const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getCommonTerm(param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param param 相关参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取公共操作术语
     */
    ::Ice::AsyncResultPtr begin_getCommonTerm(const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getCommonTerm(param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取公共操作术语
     */
    ::Ice::AsyncResultPtr begin_getCommonTerm(const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getCommonTerm(param, context, cb, cookie);
    }

    /**
     * @param param 相关参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取公共操作术语
     */
    ::Ice::AsyncResultPtr begin_getCommonTerm(const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_getCommonTermPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getCommonTerm(param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param param 相关参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取公共操作术语
     */
    ::Ice::AsyncResultPtr begin_getCommonTerm(const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_getCommonTermPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getCommonTerm(param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getCommonTerm.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getCommonTerm(::ZG6000::ListStringMap& terms, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getCommonTerm(::ZG6000::ListStringMap& iceP_terms, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getCommonTerm(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建操作票项
     */
    bool createOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ListStringMap& items, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createOtItem(items, e, _iceI_begin_createOtItem(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建操作票项
     */
    ::Ice::AsyncResultPtr begin_createOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createOtItem(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建操作票项
     */
    ::Ice::AsyncResultPtr begin_createOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createOtItem(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建操作票项
     */
    ::Ice::AsyncResultPtr begin_createOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createOtItem(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建操作票项
     */
    ::Ice::AsyncResultPtr begin_createOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_createOtItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createOtItem(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建操作票项
     */
    ::Ice::AsyncResultPtr begin_createOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_createOtItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createOtItem(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_createOtItem.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_createOtItem(::ZG6000::ListStringMap& items, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createOtItem(::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createOtItem(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	删除操作票项
     */
    bool deleteOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deleteOtItem(e, _iceI_begin_deleteOtItem(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	删除操作票项
     */
    ::Ice::AsyncResultPtr begin_deleteOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deleteOtItem(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	删除操作票项
     */
    ::Ice::AsyncResultPtr begin_deleteOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteOtItem(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	删除操作票项
     */
    ::Ice::AsyncResultPtr begin_deleteOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteOtItem(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	删除操作票项
     */
    ::Ice::AsyncResultPtr begin_deleteOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_deleteOtItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteOtItem(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	删除操作票项
     */
    ::Ice::AsyncResultPtr begin_deleteOtItem(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_deleteOtItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteOtItem(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_deleteOtItem.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_deleteOtItem(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deleteOtItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deleteOtItem(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	设置操作票模拟值
     */
    bool setOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setOtSimulateValue(e, _iceI_begin_setOtSimulateValue(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	设置操作票模拟值
     */
    ::Ice::AsyncResultPtr begin_setOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setOtSimulateValue(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	设置操作票模拟值
     */
    ::Ice::AsyncResultPtr begin_setOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setOtSimulateValue(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	设置操作票模拟值
     */
    ::Ice::AsyncResultPtr begin_setOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setOtSimulateValue(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	设置操作票模拟值
     */
    ::Ice::AsyncResultPtr begin_setOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_setOtSimulateValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setOtSimulateValue(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	设置操作票模拟值
     */
    ::Ice::AsyncResultPtr begin_setOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_setOtSimulateValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setOtSimulateValue(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_setOtSimulateValue.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_setOtSimulateValue(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setOtSimulateValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setOtSimulateValue(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	清除操作票模拟值
     */
    bool clearOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_clearOtSimulateValue(e, _iceI_begin_clearOtSimulateValue(otID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	清除操作票模拟值
     */
    ::Ice::AsyncResultPtr begin_clearOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_clearOtSimulateValue(otID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	清除操作票模拟值
     */
    ::Ice::AsyncResultPtr begin_clearOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearOtSimulateValue(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	清除操作票模拟值
     */
    ::Ice::AsyncResultPtr begin_clearOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearOtSimulateValue(otID, param, context, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	清除操作票模拟值
     */
    ::Ice::AsyncResultPtr begin_clearOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOT_clearOtSimulateValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearOtSimulateValue(otID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	清除操作票模拟值
     */
    ::Ice::AsyncResultPtr begin_clearOtSimulateValue(const ::std::string& otID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_clearOtSimulateValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clearOtSimulateValue(otID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_clearOtSimulateValue.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_clearOtSimulateValue(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_clearOtSimulateValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_clearOtSimulateValue(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool downloadTask(const ::std::string& clientID, const ::ZG6000::StringList& listTaskID, ::ZG6000::ListStringMap& listTask, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_downloadTask(listTask, listItem, e, _iceI_begin_downloadTask(clientID, listTaskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::std::string& clientID, const ::ZG6000::StringList& listTaskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_downloadTask(clientID, listTaskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::std::string& clientID, const ::ZG6000::StringList& listTaskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(clientID, listTaskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::std::string& clientID, const ::ZG6000::StringList& listTaskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(clientID, listTaskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::std::string& clientID, const ::ZG6000::StringList& listTaskID, const ::ZG6000::Callback_ZGOPTaskOT_downloadTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(clientID, listTaskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::std::string& clientID, const ::ZG6000::StringList& listTaskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_downloadTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(clientID, listTaskID, context, cb, cookie);
    }

    bool end_downloadTask(::ZG6000::ListStringMap& listTask, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_downloadTask(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_downloadTask(const ::std::string&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateTask(const ::ZG6000::ListStringMap& listTask, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateTask(e, _iceI_begin_updateTask(listTask, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateTask(listTask, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::ZG6000::Callback_ZGOPTaskOT_updateTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_updateTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, context, cb, cookie);
    }

    bool end_updateTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateTask(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateItem(const ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateItem(e, _iceI_begin_updateItem(listItem, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateItem(listItem, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::Callback_ZGOPTaskOT_updateItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOT_updateItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, context, cb, cookie);
    }

    bool end_updateItem(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateItem(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGOPTaskOT : virtual public ZGOPTaskBase
{
public:

    typedef ZGOPTaskOTPrx ProxyType;
    typedef ZGOPTaskOTPtr PointerType;

    virtual ~ZGOPTaskOT();

#ifdef ICE_CPP11_COMPILER
    ZGOPTaskOT() = default;
    ZGOPTaskOT(const ZGOPTaskOT&) = default;
    ZGOPTaskOT& operator=(const ZGOPTaskOT&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param otID 操作票ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取操作票信息
     */
    virtual bool getOT(const ::std::string& otID, StringMap& head, ListStringMap& items, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getOT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskTypeID 票类型ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建操作票
     */
    virtual bool createOT(const ::std::string& taskTypeID, const StringMap& param, ::std::string& otID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createOT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 票ID
     * @param head 票头信息
     * @param items 票项信息
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	编辑操作票
     */
    virtual bool editOT(const ::std::string& otID, const StringMap& head, const ListStringMap& items, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_editOT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	转换操作票类型
     */
    virtual bool convertOT(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_convertOT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	跳过当前步骤
     */
    virtual bool skipItem(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_skipItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	开始预演
     */
    virtual bool startPreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_startPreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	停止预演
     */
    virtual bool stopPreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_stopPreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	暂停预演
     */
    virtual bool pausePreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_pausePreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	继续预演
     */
    virtual bool resumePreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_resumePreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	重试预演步骤
     */
    virtual bool retryPreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_retryPreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	确认预演
     */
    virtual bool confirmPreview(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmPreview(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param param 相关参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取设备操作术语
     */
    virtual bool getDeviceTerm(const ::std::string& deviceID, const StringMap& param, ListStringMap& terms, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceTerm(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param param 相关参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取公共操作术语
     */
    virtual bool getCommonTerm(const StringMap& param, ListStringMap& terms, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getCommonTerm(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建操作票项
     */
    virtual bool createOtItem(const ::std::string& otID, const StringMap& param, ListStringMap& items, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createOtItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	删除操作票项
     */
    virtual bool deleteOtItem(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteOtItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	设置操作票模拟值
     */
    virtual bool setOtSimulateValue(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setOtSimulateValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param otID 操作票ID
     * @param param 票参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	清除操作票模拟值
     */
    virtual bool clearOtSimulateValue(const ::std::string& otID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_clearOtSimulateValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool downloadTask(const ::std::string& clientID, const StringList& listTaskID, ListStringMap& listTask, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_downloadTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateTask(const ListStringMap& listTask, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateItem(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGOPTaskOT& lhs, const ZGOPTaskOT& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGOPTaskOT& lhs, const ZGOPTaskOT& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_getOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_getOT.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_getOT : public Callback_ZGOPTaskOT_getOT_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_getOT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        StringMap iceP_head;
        ListStringMap iceP_items;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getOT(iceP_head, iceP_items, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_head, iceP_items, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getOT.
 */
template<class T> Callback_ZGOPTaskOT_getOTPtr
newCallback_ZGOPTaskOT_getOT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_getOT<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getOT.
 */
template<class T> Callback_ZGOPTaskOT_getOTPtr
newCallback_ZGOPTaskOT_getOT(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_getOT<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_getOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_getOT.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_getOT : public Callback_ZGOPTaskOT_getOT_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_getOT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        StringMap iceP_head;
        ListStringMap iceP_items;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getOT(iceP_head, iceP_items, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_head, iceP_items, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getOT.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_getOTPtr
newCallback_ZGOPTaskOT_getOT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_getOT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getOT.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_getOTPtr
newCallback_ZGOPTaskOT_getOT(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_getOT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_createOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_createOT.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_createOT : public Callback_ZGOPTaskOT_createOT_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_createOT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_otID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createOT(iceP_otID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_otID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_createOT.
 */
template<class T> Callback_ZGOPTaskOT_createOTPtr
newCallback_ZGOPTaskOT_createOT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_createOT<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_createOT.
 */
template<class T> Callback_ZGOPTaskOT_createOTPtr
newCallback_ZGOPTaskOT_createOT(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_createOT<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_createOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_createOT.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_createOT : public Callback_ZGOPTaskOT_createOT_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_createOT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_otID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createOT(iceP_otID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_otID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_createOT.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_createOTPtr
newCallback_ZGOPTaskOT_createOT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_createOT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_createOT.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_createOTPtr
newCallback_ZGOPTaskOT_createOT(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_createOT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_editOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_editOT.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_editOT : public Callback_ZGOPTaskOT_editOT_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_editOT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_editOT(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_editOT.
 */
template<class T> Callback_ZGOPTaskOT_editOTPtr
newCallback_ZGOPTaskOT_editOT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_editOT<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_editOT.
 */
template<class T> Callback_ZGOPTaskOT_editOTPtr
newCallback_ZGOPTaskOT_editOT(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_editOT<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_editOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_editOT.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_editOT : public Callback_ZGOPTaskOT_editOT_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_editOT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_editOT(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_editOT.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_editOTPtr
newCallback_ZGOPTaskOT_editOT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_editOT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_editOT.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_editOTPtr
newCallback_ZGOPTaskOT_editOT(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_editOT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_convertOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_convertOT.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_convertOT : public Callback_ZGOPTaskOT_convertOT_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_convertOT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_convertOT(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_convertOT.
 */
template<class T> Callback_ZGOPTaskOT_convertOTPtr
newCallback_ZGOPTaskOT_convertOT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_convertOT<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_convertOT.
 */
template<class T> Callback_ZGOPTaskOT_convertOTPtr
newCallback_ZGOPTaskOT_convertOT(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_convertOT<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_convertOT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_convertOT.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_convertOT : public Callback_ZGOPTaskOT_convertOT_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_convertOT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_convertOT(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_convertOT.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_convertOTPtr
newCallback_ZGOPTaskOT_convertOT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_convertOT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_convertOT.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_convertOTPtr
newCallback_ZGOPTaskOT_convertOT(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_convertOT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_skipItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_skipItem.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_skipItem : public Callback_ZGOPTaskOT_skipItem_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_skipItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_skipItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_skipItem.
 */
template<class T> Callback_ZGOPTaskOT_skipItemPtr
newCallback_ZGOPTaskOT_skipItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_skipItem<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_skipItem.
 */
template<class T> Callback_ZGOPTaskOT_skipItemPtr
newCallback_ZGOPTaskOT_skipItem(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_skipItem<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_skipItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_skipItem.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_skipItem : public Callback_ZGOPTaskOT_skipItem_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_skipItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_skipItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_skipItem.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_skipItemPtr
newCallback_ZGOPTaskOT_skipItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_skipItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_skipItem.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_skipItemPtr
newCallback_ZGOPTaskOT_skipItem(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_skipItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_startPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_startPreview.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_startPreview : public Callback_ZGOPTaskOT_startPreview_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_startPreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_startPreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_startPreview.
 */
template<class T> Callback_ZGOPTaskOT_startPreviewPtr
newCallback_ZGOPTaskOT_startPreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_startPreview<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_startPreview.
 */
template<class T> Callback_ZGOPTaskOT_startPreviewPtr
newCallback_ZGOPTaskOT_startPreview(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_startPreview<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_startPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_startPreview.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_startPreview : public Callback_ZGOPTaskOT_startPreview_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_startPreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_startPreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_startPreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_startPreviewPtr
newCallback_ZGOPTaskOT_startPreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_startPreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_startPreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_startPreviewPtr
newCallback_ZGOPTaskOT_startPreview(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_startPreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_stopPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_stopPreview.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_stopPreview : public Callback_ZGOPTaskOT_stopPreview_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_stopPreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_stopPreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_stopPreview.
 */
template<class T> Callback_ZGOPTaskOT_stopPreviewPtr
newCallback_ZGOPTaskOT_stopPreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_stopPreview<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_stopPreview.
 */
template<class T> Callback_ZGOPTaskOT_stopPreviewPtr
newCallback_ZGOPTaskOT_stopPreview(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_stopPreview<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_stopPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_stopPreview.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_stopPreview : public Callback_ZGOPTaskOT_stopPreview_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_stopPreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_stopPreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_stopPreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_stopPreviewPtr
newCallback_ZGOPTaskOT_stopPreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_stopPreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_stopPreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_stopPreviewPtr
newCallback_ZGOPTaskOT_stopPreview(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_stopPreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_pausePreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_pausePreview.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_pausePreview : public Callback_ZGOPTaskOT_pausePreview_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_pausePreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_pausePreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_pausePreview.
 */
template<class T> Callback_ZGOPTaskOT_pausePreviewPtr
newCallback_ZGOPTaskOT_pausePreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_pausePreview<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_pausePreview.
 */
template<class T> Callback_ZGOPTaskOT_pausePreviewPtr
newCallback_ZGOPTaskOT_pausePreview(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_pausePreview<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_pausePreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_pausePreview.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_pausePreview : public Callback_ZGOPTaskOT_pausePreview_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_pausePreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_pausePreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_pausePreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_pausePreviewPtr
newCallback_ZGOPTaskOT_pausePreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_pausePreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_pausePreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_pausePreviewPtr
newCallback_ZGOPTaskOT_pausePreview(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_pausePreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_resumePreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_resumePreview.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_resumePreview : public Callback_ZGOPTaskOT_resumePreview_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_resumePreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_resumePreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_resumePreview.
 */
template<class T> Callback_ZGOPTaskOT_resumePreviewPtr
newCallback_ZGOPTaskOT_resumePreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_resumePreview<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_resumePreview.
 */
template<class T> Callback_ZGOPTaskOT_resumePreviewPtr
newCallback_ZGOPTaskOT_resumePreview(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_resumePreview<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_resumePreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_resumePreview.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_resumePreview : public Callback_ZGOPTaskOT_resumePreview_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_resumePreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_resumePreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_resumePreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_resumePreviewPtr
newCallback_ZGOPTaskOT_resumePreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_resumePreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_resumePreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_resumePreviewPtr
newCallback_ZGOPTaskOT_resumePreview(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_resumePreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_retryPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_retryPreview.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_retryPreview : public Callback_ZGOPTaskOT_retryPreview_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_retryPreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_retryPreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_retryPreview.
 */
template<class T> Callback_ZGOPTaskOT_retryPreviewPtr
newCallback_ZGOPTaskOT_retryPreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_retryPreview<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_retryPreview.
 */
template<class T> Callback_ZGOPTaskOT_retryPreviewPtr
newCallback_ZGOPTaskOT_retryPreview(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_retryPreview<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_retryPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_retryPreview.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_retryPreview : public Callback_ZGOPTaskOT_retryPreview_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_retryPreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_retryPreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_retryPreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_retryPreviewPtr
newCallback_ZGOPTaskOT_retryPreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_retryPreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_retryPreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_retryPreviewPtr
newCallback_ZGOPTaskOT_retryPreview(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_retryPreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_confirmPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_confirmPreview.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_confirmPreview : public Callback_ZGOPTaskOT_confirmPreview_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_confirmPreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmPreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_confirmPreview.
 */
template<class T> Callback_ZGOPTaskOT_confirmPreviewPtr
newCallback_ZGOPTaskOT_confirmPreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_confirmPreview<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_confirmPreview.
 */
template<class T> Callback_ZGOPTaskOT_confirmPreviewPtr
newCallback_ZGOPTaskOT_confirmPreview(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_confirmPreview<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_confirmPreview.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_confirmPreview.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_confirmPreview : public Callback_ZGOPTaskOT_confirmPreview_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_confirmPreview(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmPreview(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_confirmPreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_confirmPreviewPtr
newCallback_ZGOPTaskOT_confirmPreview(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_confirmPreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_confirmPreview.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_confirmPreviewPtr
newCallback_ZGOPTaskOT_confirmPreview(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_confirmPreview<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_getDeviceTerm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_getDeviceTerm.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_getDeviceTerm : public Callback_ZGOPTaskOT_getDeviceTerm_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_getDeviceTerm(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_terms;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceTerm(iceP_terms, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_terms, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getDeviceTerm.
 */
template<class T> Callback_ZGOPTaskOT_getDeviceTermPtr
newCallback_ZGOPTaskOT_getDeviceTerm(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_getDeviceTerm<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getDeviceTerm.
 */
template<class T> Callback_ZGOPTaskOT_getDeviceTermPtr
newCallback_ZGOPTaskOT_getDeviceTerm(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_getDeviceTerm<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_getDeviceTerm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_getDeviceTerm.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_getDeviceTerm : public Callback_ZGOPTaskOT_getDeviceTerm_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_getDeviceTerm(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_terms;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceTerm(iceP_terms, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_terms, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getDeviceTerm.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_getDeviceTermPtr
newCallback_ZGOPTaskOT_getDeviceTerm(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_getDeviceTerm<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getDeviceTerm.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_getDeviceTermPtr
newCallback_ZGOPTaskOT_getDeviceTerm(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_getDeviceTerm<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_getCommonTerm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_getCommonTerm.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_getCommonTerm : public Callback_ZGOPTaskOT_getCommonTerm_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_getCommonTerm(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_terms;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getCommonTerm(iceP_terms, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_terms, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getCommonTerm.
 */
template<class T> Callback_ZGOPTaskOT_getCommonTermPtr
newCallback_ZGOPTaskOT_getCommonTerm(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_getCommonTerm<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getCommonTerm.
 */
template<class T> Callback_ZGOPTaskOT_getCommonTermPtr
newCallback_ZGOPTaskOT_getCommonTerm(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_getCommonTerm<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_getCommonTerm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_getCommonTerm.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_getCommonTerm : public Callback_ZGOPTaskOT_getCommonTerm_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_getCommonTerm(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_terms;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getCommonTerm(iceP_terms, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_terms, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getCommonTerm.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_getCommonTermPtr
newCallback_ZGOPTaskOT_getCommonTerm(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_getCommonTerm<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_getCommonTerm.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_getCommonTermPtr
newCallback_ZGOPTaskOT_getCommonTerm(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_getCommonTerm<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_createOtItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_createOtItem.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_createOtItem : public Callback_ZGOPTaskOT_createOtItem_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_createOtItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_items;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createOtItem(iceP_items, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_items, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_createOtItem.
 */
template<class T> Callback_ZGOPTaskOT_createOtItemPtr
newCallback_ZGOPTaskOT_createOtItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_createOtItem<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_createOtItem.
 */
template<class T> Callback_ZGOPTaskOT_createOtItemPtr
newCallback_ZGOPTaskOT_createOtItem(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_createOtItem<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_createOtItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_createOtItem.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_createOtItem : public Callback_ZGOPTaskOT_createOtItem_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_createOtItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_items;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createOtItem(iceP_items, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_items, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_createOtItem.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_createOtItemPtr
newCallback_ZGOPTaskOT_createOtItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_createOtItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_createOtItem.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_createOtItemPtr
newCallback_ZGOPTaskOT_createOtItem(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_createOtItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_deleteOtItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_deleteOtItem.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_deleteOtItem : public Callback_ZGOPTaskOT_deleteOtItem_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_deleteOtItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteOtItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_deleteOtItem.
 */
template<class T> Callback_ZGOPTaskOT_deleteOtItemPtr
newCallback_ZGOPTaskOT_deleteOtItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_deleteOtItem<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_deleteOtItem.
 */
template<class T> Callback_ZGOPTaskOT_deleteOtItemPtr
newCallback_ZGOPTaskOT_deleteOtItem(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_deleteOtItem<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_deleteOtItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_deleteOtItem.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_deleteOtItem : public Callback_ZGOPTaskOT_deleteOtItem_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_deleteOtItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteOtItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_deleteOtItem.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_deleteOtItemPtr
newCallback_ZGOPTaskOT_deleteOtItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_deleteOtItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_deleteOtItem.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_deleteOtItemPtr
newCallback_ZGOPTaskOT_deleteOtItem(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_deleteOtItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_setOtSimulateValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_setOtSimulateValue.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_setOtSimulateValue : public Callback_ZGOPTaskOT_setOtSimulateValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_setOtSimulateValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setOtSimulateValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_setOtSimulateValue.
 */
template<class T> Callback_ZGOPTaskOT_setOtSimulateValuePtr
newCallback_ZGOPTaskOT_setOtSimulateValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_setOtSimulateValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_setOtSimulateValue.
 */
template<class T> Callback_ZGOPTaskOT_setOtSimulateValuePtr
newCallback_ZGOPTaskOT_setOtSimulateValue(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_setOtSimulateValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_setOtSimulateValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_setOtSimulateValue.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_setOtSimulateValue : public Callback_ZGOPTaskOT_setOtSimulateValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_setOtSimulateValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setOtSimulateValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_setOtSimulateValue.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_setOtSimulateValuePtr
newCallback_ZGOPTaskOT_setOtSimulateValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_setOtSimulateValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_setOtSimulateValue.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_setOtSimulateValuePtr
newCallback_ZGOPTaskOT_setOtSimulateValue(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_setOtSimulateValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_clearOtSimulateValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_clearOtSimulateValue.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_clearOtSimulateValue : public Callback_ZGOPTaskOT_clearOtSimulateValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_clearOtSimulateValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_clearOtSimulateValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_clearOtSimulateValue.
 */
template<class T> Callback_ZGOPTaskOT_clearOtSimulateValuePtr
newCallback_ZGOPTaskOT_clearOtSimulateValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_clearOtSimulateValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_clearOtSimulateValue.
 */
template<class T> Callback_ZGOPTaskOT_clearOtSimulateValuePtr
newCallback_ZGOPTaskOT_clearOtSimulateValue(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_clearOtSimulateValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_clearOtSimulateValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_clearOtSimulateValue.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_clearOtSimulateValue : public Callback_ZGOPTaskOT_clearOtSimulateValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_clearOtSimulateValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_clearOtSimulateValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_clearOtSimulateValue.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_clearOtSimulateValuePtr
newCallback_ZGOPTaskOT_clearOtSimulateValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_clearOtSimulateValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_clearOtSimulateValue.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_clearOtSimulateValuePtr
newCallback_ZGOPTaskOT_clearOtSimulateValue(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_clearOtSimulateValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_downloadTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_downloadTask.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_downloadTask : public Callback_ZGOPTaskOT_downloadTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_downloadTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listTask;
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_downloadTask(iceP_listTask, iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listTask, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_downloadTask.
 */
template<class T> Callback_ZGOPTaskOT_downloadTaskPtr
newCallback_ZGOPTaskOT_downloadTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_downloadTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_downloadTask.
 */
template<class T> Callback_ZGOPTaskOT_downloadTaskPtr
newCallback_ZGOPTaskOT_downloadTask(T* instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_downloadTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_downloadTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_downloadTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_downloadTask : public Callback_ZGOPTaskOT_downloadTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_downloadTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listTask;
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_downloadTask(iceP_listTask, iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listTask, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_downloadTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_downloadTaskPtr
newCallback_ZGOPTaskOT_downloadTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_downloadTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_downloadTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_downloadTaskPtr
newCallback_ZGOPTaskOT_downloadTask(T* instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_downloadTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_updateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_updateTask.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_updateTask : public Callback_ZGOPTaskOT_updateTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_updateTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_updateTask.
 */
template<class T> Callback_ZGOPTaskOT_updateTaskPtr
newCallback_ZGOPTaskOT_updateTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_updateTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_updateTask.
 */
template<class T> Callback_ZGOPTaskOT_updateTaskPtr
newCallback_ZGOPTaskOT_updateTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_updateTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_updateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_updateTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_updateTask : public Callback_ZGOPTaskOT_updateTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_updateTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_updateTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_updateTaskPtr
newCallback_ZGOPTaskOT_updateTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_updateTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_updateTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_updateTaskPtr
newCallback_ZGOPTaskOT_updateTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_updateTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_updateItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_updateItem.
 */
template<class T>
class CallbackNC_ZGOPTaskOT_updateItem : public Callback_ZGOPTaskOT_updateItem_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOT_updateItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_updateItem.
 */
template<class T> Callback_ZGOPTaskOT_updateItemPtr
newCallback_ZGOPTaskOT_updateItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_updateItem<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_updateItem.
 */
template<class T> Callback_ZGOPTaskOT_updateItemPtr
newCallback_ZGOPTaskOT_updateItem(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOT_updateItem<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOT::begin_updateItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOT_updateItem.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOT_updateItem : public Callback_ZGOPTaskOT_updateItem_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOT_updateItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOTPrx proxy = ZGOPTaskOTPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_updateItem.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_updateItemPtr
newCallback_ZGOPTaskOT_updateItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_updateItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOT::begin_updateItem.
 */
template<class T, typename CT> Callback_ZGOPTaskOT_updateItemPtr
newCallback_ZGOPTaskOT_updateItem(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOT_updateItem<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

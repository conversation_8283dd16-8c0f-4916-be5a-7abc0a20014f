//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPTaskIU.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGOPTaskIU_h__
#define __ZGOPTaskIU_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGOPTaskBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGOPTaskIU;
class ZGOPTaskIUPrx;

}

namespace ZG6000
{

class ZGOPTaskIU : public virtual ZGOPTaskBase
{
public:

    using ProxyType = ZGOPTaskIUPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getTaskItems.
     */
    struct GetTaskItemsResult
    {
        bool returnValue;
        StringMap task;
        ListStringMap items;
        ErrorInfo e;
    };

    virtual bool getTaskItems(::std::string taskID, StringMap& task, ListStringMap& items, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskItems(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to createTemplateTask.
     */
    struct CreateTemplateTaskResult
    {
        bool returnValue;
        StringList listTaskID;
        ErrorInfo e;
    };

    virtual bool createTemplateTask(::std::string templateID, StringList listAppNodeID, StringMap param, StringList& listTaskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createTemplateTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to createTemporaryTask.
     */
    struct CreateTemporaryTaskResult
    {
        bool returnValue;
        ::std::string taskID;
        ErrorInfo e;
    };

    virtual bool createTemporaryTask(ListStringMap listUnlockTemplate, StringMap param, ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createTemporaryTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to downloadTask.
     */
    struct DownloadTaskResult
    {
        bool returnValue;
        ListStringMap listTask;
        ListStringMap listItem;
        ErrorInfo e;
    };

    virtual bool downloadTask(StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_downloadTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateTask.
     */
    struct UpdateTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateTask(ListStringMap listTask, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateItem.
     */
    struct UpdateItemResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateItem(ListStringMap listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGOPTaskIUPrx : public virtual ::Ice::Proxy<ZGOPTaskIUPrx, ZGOPTaskBasePrx>
{
public:

    bool getTaskItems(const ::std::string& taskID, StringMap& task, ListStringMap& items, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIU::GetTaskItemsResult>(true, this, &ZGOPTaskIUPrx::_iceI_getTaskItems, taskID, context).get();
        task = ::std::move(_result.task);
        items = ::std::move(_result.items);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTaskItemsAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIU::GetTaskItemsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIU::GetTaskItemsResult, P>(false, this, &ZGOPTaskIUPrx::_iceI_getTaskItems, taskID, context);
    }

    ::std::function<void()>
    getTaskItemsAsync(const ::std::string& taskID,
                      ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIU::GetTaskItemsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.task), ::std::move(_result.items), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIU::GetTaskItemsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskIUPrx::_iceI_getTaskItems, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getTaskItems(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::GetTaskItemsResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool createTemplateTask(const ::std::string& templateID, const StringList& listAppNodeID, const StringMap& param, StringList& listTaskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIU::CreateTemplateTaskResult>(true, this, &ZGOPTaskIUPrx::_iceI_createTemplateTask, templateID, listAppNodeID, param, context).get();
        listTaskID = ::std::move(_result.listTaskID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto createTemplateTaskAsync(const ::std::string& templateID, const StringList& listAppNodeID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIU::CreateTemplateTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIU::CreateTemplateTaskResult, P>(false, this, &ZGOPTaskIUPrx::_iceI_createTemplateTask, templateID, listAppNodeID, param, context);
    }

    ::std::function<void()>
    createTemplateTaskAsync(const ::std::string& templateID, const StringList& listAppNodeID, const StringMap& param,
                            ::std::function<void(bool, ::ZG6000::StringList, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIU::CreateTemplateTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listTaskID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIU::CreateTemplateTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskIUPrx::_iceI_createTemplateTask, templateID, listAppNodeID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_createTemplateTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::CreateTemplateTaskResult>>&, const ::std::string&, const StringList&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool createTemporaryTask(const ListStringMap& listUnlockTemplate, const StringMap& param, ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIU::CreateTemporaryTaskResult>(true, this, &ZGOPTaskIUPrx::_iceI_createTemporaryTask, listUnlockTemplate, param, context).get();
        taskID = ::std::move(_result.taskID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto createTemporaryTaskAsync(const ListStringMap& listUnlockTemplate, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIU::CreateTemporaryTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIU::CreateTemporaryTaskResult, P>(false, this, &ZGOPTaskIUPrx::_iceI_createTemporaryTask, listUnlockTemplate, param, context);
    }

    ::std::function<void()>
    createTemporaryTaskAsync(const ListStringMap& listUnlockTemplate, const StringMap& param,
                             ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIU::CreateTemporaryTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.taskID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIU::CreateTemporaryTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskIUPrx::_iceI_createTemporaryTask, listUnlockTemplate, param, context);
    }

    /// \cond INTERNAL
    void _iceI_createTemporaryTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::CreateTemporaryTaskResult>>&, const ListStringMap&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool downloadTask(const StringList& listTaskID, ListStringMap& listTask, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIU::DownloadTaskResult>(true, this, &ZGOPTaskIUPrx::_iceI_downloadTask, listTaskID, context).get();
        listTask = ::std::move(_result.listTask);
        listItem = ::std::move(_result.listItem);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto downloadTaskAsync(const StringList& listTaskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIU::DownloadTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIU::DownloadTaskResult, P>(false, this, &ZGOPTaskIUPrx::_iceI_downloadTask, listTaskID, context);
    }

    ::std::function<void()>
    downloadTaskAsync(const StringList& listTaskID,
                      ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIU::DownloadTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listTask), ::std::move(_result.listItem), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIU::DownloadTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskIUPrx::_iceI_downloadTask, listTaskID, context);
    }

    /// \cond INTERNAL
    void _iceI_downloadTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::DownloadTaskResult>>&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool updateTask(const ListStringMap& listTask, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIU::UpdateTaskResult>(true, this, &ZGOPTaskIUPrx::_iceI_updateTask, listTask, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateTaskAsync(const ListStringMap& listTask, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIU::UpdateTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIU::UpdateTaskResult, P>(false, this, &ZGOPTaskIUPrx::_iceI_updateTask, listTask, context);
    }

    ::std::function<void()>
    updateTaskAsync(const ListStringMap& listTask,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIU::UpdateTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIU::UpdateTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskIUPrx::_iceI_updateTask, listTask, context);
    }

    /// \cond INTERNAL
    void _iceI_updateTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::UpdateTaskResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool updateItem(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIU::UpdateItemResult>(true, this, &ZGOPTaskIUPrx::_iceI_updateItem, listItem, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateItemAsync(const ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIU::UpdateItemResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIU::UpdateItemResult, P>(false, this, &ZGOPTaskIUPrx::_iceI_updateItem, listItem, context);
    }

    ::std::function<void()>
    updateItemAsync(const ListStringMap& listItem,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIU::UpdateItemResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIU::UpdateItemResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskIUPrx::_iceI_updateItem, listItem, context);
    }

    /// \cond INTERNAL
    void _iceI_updateItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::UpdateItemResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGOPTaskIUPrx() = default;
    friend ::std::shared_ptr<ZGOPTaskIUPrx> IceInternal::createProxy<ZGOPTaskIUPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGOPTaskIUPtr = ::std::shared_ptr<ZGOPTaskIU>;
using ZGOPTaskIUPrxPtr = ::std::shared_ptr<ZGOPTaskIUPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGOPTaskIU;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGOPTaskIU>&);
::IceProxy::Ice::Object* upCast(ZGOPTaskIU*);
/// \endcond

}

}

namespace ZG6000
{

class ZGOPTaskIU;
/// \cond INTERNAL
::Ice::Object* upCast(ZGOPTaskIU*);
/// \endcond
typedef ::IceInternal::Handle< ZGOPTaskIU> ZGOPTaskIUPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGOPTaskIU> ZGOPTaskIUPrx;
typedef ZGOPTaskIUPrx ZGOPTaskIUPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGOPTaskIUPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_getTaskItems.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_getTaskItems.
 */
class Callback_ZGOPTaskIU_getTaskItems_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIU_getTaskItems_Base> Callback_ZGOPTaskIU_getTaskItemsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_createTemplateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_createTemplateTask.
 */
class Callback_ZGOPTaskIU_createTemplateTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIU_createTemplateTask_Base> Callback_ZGOPTaskIU_createTemplateTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_createTemporaryTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_createTemporaryTask.
 */
class Callback_ZGOPTaskIU_createTemporaryTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIU_createTemporaryTask_Base> Callback_ZGOPTaskIU_createTemporaryTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_downloadTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_downloadTask.
 */
class Callback_ZGOPTaskIU_downloadTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIU_downloadTask_Base> Callback_ZGOPTaskIU_downloadTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_updateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_updateTask.
 */
class Callback_ZGOPTaskIU_updateTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIU_updateTask_Base> Callback_ZGOPTaskIU_updateTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_updateItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_updateItem.
 */
class Callback_ZGOPTaskIU_updateItem_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIU_updateItem_Base> Callback_ZGOPTaskIU_updateItemPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGOPTaskIU : public virtual ::Ice::Proxy<ZGOPTaskIU, ::IceProxy::ZG6000::ZGOPTaskBase>
{
public:

    bool getTaskItems(const ::std::string& taskID, ::ZG6000::StringMap& task, ::ZG6000::ListStringMap& items, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTaskItems(task, items, e, _iceI_begin_getTaskItems(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTaskItems(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPTaskIU_getTaskItemsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIU_getTaskItemsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, context, cb, cookie);
    }

    bool end_getTaskItems(::ZG6000::StringMap& task, ::ZG6000::ListStringMap& items, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTaskItems(::ZG6000::StringMap& iceP_task, ::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTaskItems(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool createTemplateTask(const ::std::string& templateID, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringMap& param, ::ZG6000::StringList& listTaskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createTemplateTask(listTaskID, e, _iceI_begin_createTemplateTask(templateID, listAppNodeID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_createTemplateTask(const ::std::string& templateID, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createTemplateTask(templateID, listAppNodeID, param, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_createTemplateTask(const ::std::string& templateID, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTemplateTask(templateID, listAppNodeID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createTemplateTask(const ::std::string& templateID, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTemplateTask(templateID, listAppNodeID, param, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createTemplateTask(const ::std::string& templateID, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskIU_createTemplateTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTemplateTask(templateID, listAppNodeID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createTemplateTask(const ::std::string& templateID, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIU_createTemplateTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTemplateTask(templateID, listAppNodeID, param, context, cb, cookie);
    }

    bool end_createTemplateTask(::ZG6000::StringList& listTaskID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createTemplateTask(::ZG6000::StringList& iceP_listTaskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createTemplateTask(const ::std::string&, const ::ZG6000::StringList&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool createTemporaryTask(const ::ZG6000::ListStringMap& listUnlockTemplate, const ::ZG6000::StringMap& param, ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createTemporaryTask(taskID, e, _iceI_begin_createTemporaryTask(listUnlockTemplate, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_createTemporaryTask(const ::ZG6000::ListStringMap& listUnlockTemplate, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createTemporaryTask(listUnlockTemplate, param, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_createTemporaryTask(const ::ZG6000::ListStringMap& listUnlockTemplate, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTemporaryTask(listUnlockTemplate, param, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createTemporaryTask(const ::ZG6000::ListStringMap& listUnlockTemplate, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTemporaryTask(listUnlockTemplate, param, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createTemporaryTask(const ::ZG6000::ListStringMap& listUnlockTemplate, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskIU_createTemporaryTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTemporaryTask(listUnlockTemplate, param, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createTemporaryTask(const ::ZG6000::ListStringMap& listUnlockTemplate, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIU_createTemporaryTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTemporaryTask(listUnlockTemplate, param, context, cb, cookie);
    }

    bool end_createTemporaryTask(::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createTemporaryTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createTemporaryTask(const ::ZG6000::ListStringMap&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool downloadTask(const ::ZG6000::StringList& listTaskID, ::ZG6000::ListStringMap& listTask, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_downloadTask(listTask, listItem, e, _iceI_begin_downloadTask(listTaskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::ZG6000::StringList& listTaskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_downloadTask(listTaskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::ZG6000::StringList& listTaskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(listTaskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::ZG6000::StringList& listTaskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(listTaskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::ZG6000::StringList& listTaskID, const ::ZG6000::Callback_ZGOPTaskIU_downloadTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(listTaskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::ZG6000::StringList& listTaskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIU_downloadTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(listTaskID, context, cb, cookie);
    }

    bool end_downloadTask(::ZG6000::ListStringMap& listTask, ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_downloadTask(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_downloadTask(const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateTask(const ::ZG6000::ListStringMap& listTask, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateTask(e, _iceI_begin_updateTask(listTask, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateTask(listTask, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::ZG6000::Callback_ZGOPTaskIU_updateTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIU_updateTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, context, cb, cookie);
    }

    bool end_updateTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateTask(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateItem(const ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateItem(e, _iceI_begin_updateItem(listItem, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateItem(listItem, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::Callback_ZGOPTaskIU_updateItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIU_updateItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, context, cb, cookie);
    }

    bool end_updateItem(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateItem(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGOPTaskIU : virtual public ZGOPTaskBase
{
public:

    typedef ZGOPTaskIUPrx ProxyType;
    typedef ZGOPTaskIUPtr PointerType;

    virtual ~ZGOPTaskIU();

#ifdef ICE_CPP11_COMPILER
    ZGOPTaskIU() = default;
    ZGOPTaskIU(const ZGOPTaskIU&) = default;
    ZGOPTaskIU& operator=(const ZGOPTaskIU&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool getTaskItems(const ::std::string& taskID, StringMap& task, ListStringMap& items, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskItems(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool createTemplateTask(const ::std::string& templateID, const StringList& listAppNodeID, const StringMap& param, StringList& listTaskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createTemplateTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool createTemporaryTask(const ListStringMap& listUnlockTemplate, const StringMap& param, ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createTemporaryTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool downloadTask(const StringList& listTaskID, ListStringMap& listTask, ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_downloadTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateTask(const ListStringMap& listTask, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateItem(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGOPTaskIU& lhs, const ZGOPTaskIU& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGOPTaskIU& lhs, const ZGOPTaskIU& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_getTaskItems.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_getTaskItems.
 */
template<class T>
class CallbackNC_ZGOPTaskIU_getTaskItems : public Callback_ZGOPTaskIU_getTaskItems_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskIU_getTaskItems(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        StringMap iceP_task;
        ListStringMap iceP_items;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskItems(iceP_task, iceP_items, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_task, iceP_items, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_getTaskItems.
 */
template<class T> Callback_ZGOPTaskIU_getTaskItemsPtr
newCallback_ZGOPTaskIU_getTaskItems(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_getTaskItems<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_getTaskItems.
 */
template<class T> Callback_ZGOPTaskIU_getTaskItemsPtr
newCallback_ZGOPTaskIU_getTaskItems(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_getTaskItems<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_getTaskItems.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_getTaskItems.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIU_getTaskItems : public Callback_ZGOPTaskIU_getTaskItems_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIU_getTaskItems(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        StringMap iceP_task;
        ListStringMap iceP_items;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskItems(iceP_task, iceP_items, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_task, iceP_items, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_getTaskItems.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_getTaskItemsPtr
newCallback_ZGOPTaskIU_getTaskItems(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_getTaskItems<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_getTaskItems.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_getTaskItemsPtr
newCallback_ZGOPTaskIU_getTaskItems(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_getTaskItems<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_createTemplateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_createTemplateTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIU_createTemplateTask : public Callback_ZGOPTaskIU_createTemplateTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringList&, const ErrorInfo&);

    CallbackNC_ZGOPTaskIU_createTemplateTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        StringList iceP_listTaskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createTemplateTask(iceP_listTaskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listTaskID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_createTemplateTask.
 */
template<class T> Callback_ZGOPTaskIU_createTemplateTaskPtr
newCallback_ZGOPTaskIU_createTemplateTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_createTemplateTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_createTemplateTask.
 */
template<class T> Callback_ZGOPTaskIU_createTemplateTaskPtr
newCallback_ZGOPTaskIU_createTemplateTask(T* instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_createTemplateTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_createTemplateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_createTemplateTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIU_createTemplateTask : public Callback_ZGOPTaskIU_createTemplateTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringList&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIU_createTemplateTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        StringList iceP_listTaskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createTemplateTask(iceP_listTaskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listTaskID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_createTemplateTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_createTemplateTaskPtr
newCallback_ZGOPTaskIU_createTemplateTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_createTemplateTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_createTemplateTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_createTemplateTaskPtr
newCallback_ZGOPTaskIU_createTemplateTask(T* instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_createTemplateTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_createTemporaryTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_createTemporaryTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIU_createTemporaryTask : public Callback_ZGOPTaskIU_createTemporaryTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGOPTaskIU_createTemporaryTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_taskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createTemporaryTask(iceP_taskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_taskID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_createTemporaryTask.
 */
template<class T> Callback_ZGOPTaskIU_createTemporaryTaskPtr
newCallback_ZGOPTaskIU_createTemporaryTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_createTemporaryTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_createTemporaryTask.
 */
template<class T> Callback_ZGOPTaskIU_createTemporaryTaskPtr
newCallback_ZGOPTaskIU_createTemporaryTask(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_createTemporaryTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_createTemporaryTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_createTemporaryTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIU_createTemporaryTask : public Callback_ZGOPTaskIU_createTemporaryTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIU_createTemporaryTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_taskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createTemporaryTask(iceP_taskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_taskID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_createTemporaryTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_createTemporaryTaskPtr
newCallback_ZGOPTaskIU_createTemporaryTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_createTemporaryTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_createTemporaryTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_createTemporaryTaskPtr
newCallback_ZGOPTaskIU_createTemporaryTask(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_createTemporaryTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_downloadTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_downloadTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIU_downloadTask : public Callback_ZGOPTaskIU_downloadTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskIU_downloadTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listTask;
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_downloadTask(iceP_listTask, iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listTask, iceP_listItem, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_downloadTask.
 */
template<class T> Callback_ZGOPTaskIU_downloadTaskPtr
newCallback_ZGOPTaskIU_downloadTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_downloadTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_downloadTask.
 */
template<class T> Callback_ZGOPTaskIU_downloadTaskPtr
newCallback_ZGOPTaskIU_downloadTask(T* instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_downloadTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_downloadTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_downloadTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIU_downloadTask : public Callback_ZGOPTaskIU_downloadTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIU_downloadTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listTask;
        ListStringMap iceP_listItem;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_downloadTask(iceP_listTask, iceP_listItem, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listTask, iceP_listItem, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_downloadTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_downloadTaskPtr
newCallback_ZGOPTaskIU_downloadTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_downloadTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_downloadTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_downloadTaskPtr
newCallback_ZGOPTaskIU_downloadTask(T* instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_downloadTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_updateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_updateTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIU_updateTask : public Callback_ZGOPTaskIU_updateTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskIU_updateTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_updateTask.
 */
template<class T> Callback_ZGOPTaskIU_updateTaskPtr
newCallback_ZGOPTaskIU_updateTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_updateTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_updateTask.
 */
template<class T> Callback_ZGOPTaskIU_updateTaskPtr
newCallback_ZGOPTaskIU_updateTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_updateTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_updateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_updateTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIU_updateTask : public Callback_ZGOPTaskIU_updateTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIU_updateTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_updateTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_updateTaskPtr
newCallback_ZGOPTaskIU_updateTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_updateTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_updateTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_updateTaskPtr
newCallback_ZGOPTaskIU_updateTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_updateTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_updateItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_updateItem.
 */
template<class T>
class CallbackNC_ZGOPTaskIU_updateItem : public Callback_ZGOPTaskIU_updateItem_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskIU_updateItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_updateItem.
 */
template<class T> Callback_ZGOPTaskIU_updateItemPtr
newCallback_ZGOPTaskIU_updateItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_updateItem<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_updateItem.
 */
template<class T> Callback_ZGOPTaskIU_updateItemPtr
newCallback_ZGOPTaskIU_updateItem(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIU_updateItem<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIU::begin_updateItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIU_updateItem.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIU_updateItem : public Callback_ZGOPTaskIU_updateItem_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIU_updateItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskIUPrx proxy = ZGOPTaskIUPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_updateItem.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_updateItemPtr
newCallback_ZGOPTaskIU_updateItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_updateItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIU::begin_updateItem.
 */
template<class T, typename CT> Callback_ZGOPTaskIU_updateItemPtr
newCallback_ZGOPTaskIU_updateItem(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIU_updateItem<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

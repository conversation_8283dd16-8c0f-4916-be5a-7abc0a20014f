//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPTaskManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGMPTaskManager.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPTaskManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPTaskManager",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGMPTaskManager_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getConfirmCondition",
    "getErrorCondition",
    "getExecCondition",
    "getTask",
    "getTaskItems",
    "getTaskList",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "pauseTask",
    "resumeDebug",
    "resumeTask",
    "startDebug",
    "startTask",
    "stopDebug",
    "stopTask",
    "test"
};
const ::std::string iceC_ZG6000_ZGMPTaskManager_getTask_name = "getTask";
const ::std::string iceC_ZG6000_ZGMPTaskManager_getTaskList_name = "getTaskList";
const ::std::string iceC_ZG6000_ZGMPTaskManager_getTaskItems_name = "getTaskItems";
const ::std::string iceC_ZG6000_ZGMPTaskManager_startTask_name = "startTask";
const ::std::string iceC_ZG6000_ZGMPTaskManager_stopTask_name = "stopTask";
const ::std::string iceC_ZG6000_ZGMPTaskManager_pauseTask_name = "pauseTask";
const ::std::string iceC_ZG6000_ZGMPTaskManager_resumeTask_name = "resumeTask";
const ::std::string iceC_ZG6000_ZGMPTaskManager_getExecCondition_name = "getExecCondition";
const ::std::string iceC_ZG6000_ZGMPTaskManager_getConfirmCondition_name = "getConfirmCondition";
const ::std::string iceC_ZG6000_ZGMPTaskManager_getErrorCondition_name = "getErrorCondition";

}

bool
ZG6000::ZGMPTaskManager::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPTaskManager_ids, iceC_ZG6000_ZGMPTaskManager_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGMPTaskManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGMPTaskManager_ids[0], &iceC_ZG6000_ZGMPTaskManager_ids[3]);
}

::std::string
ZG6000::ZGMPTaskManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPTaskManager::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGMPTaskManager";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    StringMap iceP_task;
    ErrorInfo iceP_e;
    bool ret = this->getTask(::std::move(iceP_taskID), iceP_task, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_task, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    ::std::string iceP_subsystemID;
    ::std::string iceP_majorID;
    ::std::string iceP_taskTypeID;
    istr->readAll(iceP_appNodeID, iceP_subsystemID, iceP_majorID, iceP_taskTypeID);
    inS.endReadParams();
    ListStringMap iceP_taskList;
    ErrorInfo iceP_e;
    bool ret = this->getTaskList(::std::move(iceP_appNodeID), ::std::move(iceP_subsystemID), ::std::move(iceP_majorID), ::std::move(iceP_taskTypeID), iceP_taskList, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_taskList, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getTaskItems(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_itemList;
    ErrorInfo iceP_e;
    bool ret = this->getTaskItems(::std::move(iceP_taskID), iceP_itemList, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_itemList, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_startTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_operator;
    ::std::string iceP_monitor;
    ::std::string iceP_taskID;
    istr->readAll(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->startTask(::std::move(iceP_clientID), ::std::move(iceP_operator), ::std::move(iceP_monitor), ::std::move(iceP_taskID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_stopTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_operator;
    ::std::string iceP_monitor;
    ::std::string iceP_taskID;
    istr->readAll(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->stopTask(::std::move(iceP_clientID), ::std::move(iceP_operator), ::std::move(iceP_monitor), ::std::move(iceP_taskID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_pauseTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_operator;
    ::std::string iceP_monitor;
    ::std::string iceP_taskID;
    istr->readAll(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->pauseTask(::std::move(iceP_clientID), ::std::move(iceP_operator), ::std::move(iceP_monitor), ::std::move(iceP_taskID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_resumeTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_operator;
    ::std::string iceP_monitor;
    ::std::string iceP_taskID;
    istr->readAll(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->resumeTask(::std::move(iceP_clientID), ::std::move(iceP_operator), ::std::move(iceP_monitor), ::std::move(iceP_taskID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getExecCondition(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_lstMapData;
    ErrorInfo iceP_e;
    bool ret = this->getExecCondition(::std::move(iceP_taskID), iceP_lstMapData, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_lstMapData, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getConfirmCondition(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_lstMapData;
    ErrorInfo iceP_e;
    bool ret = this->getConfirmCondition(::std::move(iceP_taskID), iceP_lstMapData, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_lstMapData, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getErrorCondition(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_lstMapData;
    ErrorInfo iceP_e;
    bool ret = this->getErrorCondition(::std::move(iceP_taskID), iceP_lstMapData, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_lstMapData, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPTaskManager_ops, iceC_ZG6000_ZGMPTaskManager_ops + 25, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPTaskManager_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getConfirmCondition(in, current);
        }
        case 4:
        {
            return _iceD_getErrorCondition(in, current);
        }
        case 5:
        {
            return _iceD_getExecCondition(in, current);
        }
        case 6:
        {
            return _iceD_getTask(in, current);
        }
        case 7:
        {
            return _iceD_getTaskItems(in, current);
        }
        case 8:
        {
            return _iceD_getTaskList(in, current);
        }
        case 9:
        {
            return _iceD_getVersion(in, current);
        }
        case 10:
        {
            return _iceD_heartDebug(in, current);
        }
        case 11:
        {
            return _iceD_ice_id(in, current);
        }
        case 12:
        {
            return _iceD_ice_ids(in, current);
        }
        case 13:
        {
            return _iceD_ice_isA(in, current);
        }
        case 14:
        {
            return _iceD_ice_ping(in, current);
        }
        case 15:
        {
            return _iceD_isDebugging(in, current);
        }
        case 16:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 17:
        {
            return _iceD_pauseTask(in, current);
        }
        case 18:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 19:
        {
            return _iceD_resumeTask(in, current);
        }
        case 20:
        {
            return _iceD_startDebug(in, current);
        }
        case 21:
        {
            return _iceD_startTask(in, current);
        }
        case 22:
        {
            return _iceD_stopDebug(in, current);
        }
        case 23:
        {
            return _iceD_stopTask(in, current);
        }
        case 24:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPTaskManagerPrx::_iceI_getTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetTaskResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getTask_name);
    outAsync->invoke(iceC_ZG6000_ZGMPTaskManager_getTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPTaskManager::GetTaskResult v;
            istr->readAll(v.task, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPTaskManagerPrx::_iceI_getTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetTaskListResult>>& outAsync, const ::std::string& iceP_appNodeID, const ::std::string& iceP_subsystemID, const ::std::string& iceP_majorID, const ::std::string& iceP_taskTypeID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getTaskList_name);
    outAsync->invoke(iceC_ZG6000_ZGMPTaskManager_getTaskList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_appNodeID, iceP_subsystemID, iceP_majorID, iceP_taskTypeID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPTaskManager::GetTaskListResult v;
            istr->readAll(v.taskList, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPTaskManagerPrx::_iceI_getTaskItems(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetTaskItemsResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getTaskItems_name);
    outAsync->invoke(iceC_ZG6000_ZGMPTaskManager_getTaskItems_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPTaskManager::GetTaskItemsResult v;
            istr->readAll(v.itemList, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPTaskManagerPrx::_iceI_startTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::StartTaskResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_operator, const ::std::string& iceP_monitor, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_startTask_name);
    outAsync->invoke(iceC_ZG6000_ZGMPTaskManager_startTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPTaskManager::StartTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPTaskManagerPrx::_iceI_stopTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::StopTaskResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_operator, const ::std::string& iceP_monitor, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_stopTask_name);
    outAsync->invoke(iceC_ZG6000_ZGMPTaskManager_stopTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPTaskManager::StopTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPTaskManagerPrx::_iceI_pauseTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::PauseTaskResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_operator, const ::std::string& iceP_monitor, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_pauseTask_name);
    outAsync->invoke(iceC_ZG6000_ZGMPTaskManager_pauseTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPTaskManager::PauseTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPTaskManagerPrx::_iceI_resumeTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::ResumeTaskResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_operator, const ::std::string& iceP_monitor, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_resumeTask_name);
    outAsync->invoke(iceC_ZG6000_ZGMPTaskManager_resumeTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPTaskManager::ResumeTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPTaskManagerPrx::_iceI_getExecCondition(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetExecConditionResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getExecCondition_name);
    outAsync->invoke(iceC_ZG6000_ZGMPTaskManager_getExecCondition_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPTaskManager::GetExecConditionResult v;
            istr->readAll(v.lstMapData, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPTaskManagerPrx::_iceI_getConfirmCondition(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetConfirmConditionResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getConfirmCondition_name);
    outAsync->invoke(iceC_ZG6000_ZGMPTaskManager_getConfirmCondition_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPTaskManager::GetConfirmConditionResult v;
            istr->readAll(v.lstMapData, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPTaskManagerPrx::_iceI_getErrorCondition(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetErrorConditionResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getErrorCondition_name);
    outAsync->invoke(iceC_ZG6000_ZGMPTaskManager_getErrorCondition_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPTaskManager::GetErrorConditionResult v;
            istr->readAll(v.lstMapData, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGMPTaskManagerPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGMPTaskManagerPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGMPTaskManagerPrx::ice_staticId()
{
    return ZGMPTaskManager::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPTaskManager_getTask_name = "getTask";

const ::std::string iceC_ZG6000_ZGMPTaskManager_getTaskList_name = "getTaskList";

const ::std::string iceC_ZG6000_ZGMPTaskManager_getTaskItems_name = "getTaskItems";

const ::std::string iceC_ZG6000_ZGMPTaskManager_startTask_name = "startTask";

const ::std::string iceC_ZG6000_ZGMPTaskManager_stopTask_name = "stopTask";

const ::std::string iceC_ZG6000_ZGMPTaskManager_pauseTask_name = "pauseTask";

const ::std::string iceC_ZG6000_ZGMPTaskManager_resumeTask_name = "resumeTask";

const ::std::string iceC_ZG6000_ZGMPTaskManager_getExecCondition_name = "getExecCondition";

const ::std::string iceC_ZG6000_ZGMPTaskManager_getConfirmCondition_name = "getConfirmCondition";

const ::std::string iceC_ZG6000_ZGMPTaskManager_getErrorCondition_name = "getErrorCondition";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGMPTaskManager* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGMPTaskManager>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGMPTaskManager;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPTaskManager::_iceI_begin_getTask(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPTaskManager_getTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPTaskManager_getTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPTaskManager_getTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPTaskManager::end_getTask(::ZG6000::StringMap& iceP_task, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_task);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPTaskManager::_iceI_end_getTask(::ZG6000::StringMap& iceP_task, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_task);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPTaskManager::_iceI_begin_getTaskList(const ::std::string& iceP_appNodeID, const ::std::string& iceP_subsystemID, const ::std::string& iceP_majorID, const ::std::string& iceP_taskTypeID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getTaskList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPTaskManager_getTaskList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPTaskManager_getTaskList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_subsystemID);
        ostr->write(iceP_majorID);
        ostr->write(iceP_taskTypeID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPTaskManager_getTaskList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPTaskManager::end_getTaskList(::ZG6000::ListStringMap& iceP_taskList, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getTaskList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskList);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPTaskManager::_iceI_end_getTaskList(::ZG6000::ListStringMap& iceP_taskList, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getTaskList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskList);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPTaskManager::_iceI_begin_getTaskItems(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getTaskItems_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPTaskManager_getTaskItems_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPTaskManager_getTaskItems_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPTaskManager_getTaskItems_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPTaskManager::end_getTaskItems(::ZG6000::ListStringMap& iceP_itemList, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getTaskItems_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_itemList);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPTaskManager::_iceI_end_getTaskItems(::ZG6000::ListStringMap& iceP_itemList, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getTaskItems_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_itemList);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPTaskManager::_iceI_begin_startTask(const ::std::string& iceP_clientID, const ::std::string& iceP_operator, const ::std::string& iceP_monitor, const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_startTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPTaskManager_startTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPTaskManager_startTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_operator);
        ostr->write(iceP_monitor);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPTaskManager_startTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPTaskManager::end_startTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_startTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPTaskManager::_iceI_end_startTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_startTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPTaskManager::_iceI_begin_stopTask(const ::std::string& iceP_clientID, const ::std::string& iceP_operator, const ::std::string& iceP_monitor, const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_stopTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPTaskManager_stopTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPTaskManager_stopTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_operator);
        ostr->write(iceP_monitor);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPTaskManager_stopTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPTaskManager::end_stopTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_stopTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPTaskManager::_iceI_end_stopTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_stopTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPTaskManager::_iceI_begin_pauseTask(const ::std::string& iceP_clientID, const ::std::string& iceP_operator, const ::std::string& iceP_monitor, const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_pauseTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPTaskManager_pauseTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPTaskManager_pauseTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_operator);
        ostr->write(iceP_monitor);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPTaskManager_pauseTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPTaskManager::end_pauseTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_pauseTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPTaskManager::_iceI_end_pauseTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_pauseTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPTaskManager::_iceI_begin_resumeTask(const ::std::string& iceP_clientID, const ::std::string& iceP_operator, const ::std::string& iceP_monitor, const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_resumeTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPTaskManager_resumeTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPTaskManager_resumeTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_operator);
        ostr->write(iceP_monitor);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPTaskManager_resumeTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPTaskManager::end_resumeTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_resumeTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPTaskManager::_iceI_end_resumeTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_resumeTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPTaskManager::_iceI_begin_getExecCondition(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getExecCondition_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPTaskManager_getExecCondition_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPTaskManager_getExecCondition_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPTaskManager_getExecCondition_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPTaskManager::end_getExecCondition(::ZG6000::ListStringMap& iceP_lstMapData, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getExecCondition_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_lstMapData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPTaskManager::_iceI_end_getExecCondition(::ZG6000::ListStringMap& iceP_lstMapData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getExecCondition_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_lstMapData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPTaskManager::_iceI_begin_getConfirmCondition(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getConfirmCondition_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPTaskManager_getConfirmCondition_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPTaskManager_getConfirmCondition_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPTaskManager_getConfirmCondition_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPTaskManager::end_getConfirmCondition(::ZG6000::ListStringMap& iceP_lstMapData, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getConfirmCondition_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_lstMapData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPTaskManager::_iceI_end_getConfirmCondition(::ZG6000::ListStringMap& iceP_lstMapData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getConfirmCondition_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_lstMapData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPTaskManager::_iceI_begin_getErrorCondition(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPTaskManager_getErrorCondition_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPTaskManager_getErrorCondition_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPTaskManager_getErrorCondition_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPTaskManager_getErrorCondition_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPTaskManager::end_getErrorCondition(::ZG6000::ListStringMap& iceP_lstMapData, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getErrorCondition_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_lstMapData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPTaskManager::_iceI_end_getErrorCondition(::ZG6000::ListStringMap& iceP_lstMapData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPTaskManager_getErrorCondition_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_lstMapData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGMPTaskManager::_newInstance() const
{
    return new ZGMPTaskManager;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGMPTaskManager::ice_staticId()
{
    return ::ZG6000::ZGMPTaskManager::ice_staticId();
}

ZG6000::ZGMPTaskManager::~ZGMPTaskManager()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGMPTaskManager* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPTaskManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPTaskManager",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGMPTaskManager::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPTaskManager_ids, iceC_ZG6000_ZGMPTaskManager_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGMPTaskManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGMPTaskManager_ids[0], &iceC_ZG6000_ZGMPTaskManager_ids[3]);
}

const ::std::string&
ZG6000::ZGMPTaskManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPTaskManager::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGMPTaskManager";
    return typeId;
#else
    return iceC_ZG6000_ZGMPTaskManager_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    StringMap iceP_task;
    ErrorInfo iceP_e;
    bool ret = this->getTask(iceP_taskID, iceP_task, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_task);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    ::std::string iceP_subsystemID;
    ::std::string iceP_majorID;
    ::std::string iceP_taskTypeID;
    istr->read(iceP_appNodeID);
    istr->read(iceP_subsystemID);
    istr->read(iceP_majorID);
    istr->read(iceP_taskTypeID);
    inS.endReadParams();
    ListStringMap iceP_taskList;
    ErrorInfo iceP_e;
    bool ret = this->getTaskList(iceP_appNodeID, iceP_subsystemID, iceP_majorID, iceP_taskTypeID, iceP_taskList, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_taskList);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getTaskItems(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_itemList;
    ErrorInfo iceP_e;
    bool ret = this->getTaskItems(iceP_taskID, iceP_itemList, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_itemList);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_startTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_operator;
    ::std::string iceP_monitor;
    ::std::string iceP_taskID;
    istr->read(iceP_clientID);
    istr->read(iceP_operator);
    istr->read(iceP_monitor);
    istr->read(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->startTask(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_stopTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_operator;
    ::std::string iceP_monitor;
    ::std::string iceP_taskID;
    istr->read(iceP_clientID);
    istr->read(iceP_operator);
    istr->read(iceP_monitor);
    istr->read(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->stopTask(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_pauseTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_operator;
    ::std::string iceP_monitor;
    ::std::string iceP_taskID;
    istr->read(iceP_clientID);
    istr->read(iceP_operator);
    istr->read(iceP_monitor);
    istr->read(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->pauseTask(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_resumeTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_operator;
    ::std::string iceP_monitor;
    ::std::string iceP_taskID;
    istr->read(iceP_clientID);
    istr->read(iceP_operator);
    istr->read(iceP_monitor);
    istr->read(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->resumeTask(iceP_clientID, iceP_operator, iceP_monitor, iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getExecCondition(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_lstMapData;
    ErrorInfo iceP_e;
    bool ret = this->getExecCondition(iceP_taskID, iceP_lstMapData, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_lstMapData);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getConfirmCondition(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_lstMapData;
    ErrorInfo iceP_e;
    bool ret = this->getConfirmCondition(iceP_taskID, iceP_lstMapData, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_lstMapData);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceD_getErrorCondition(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_lstMapData;
    ErrorInfo iceP_e;
    bool ret = this->getErrorCondition(iceP_taskID, iceP_lstMapData, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_lstMapData);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPTaskManager_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getConfirmCondition",
    "getErrorCondition",
    "getExecCondition",
    "getTask",
    "getTaskItems",
    "getTaskList",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "pauseTask",
    "resumeDebug",
    "resumeTask",
    "startDebug",
    "startTask",
    "stopDebug",
    "stopTask",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGMPTaskManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPTaskManager_all, iceC_ZG6000_ZGMPTaskManager_all + 25, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPTaskManager_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getConfirmCondition(in, current);
        }
        case 4:
        {
            return _iceD_getErrorCondition(in, current);
        }
        case 5:
        {
            return _iceD_getExecCondition(in, current);
        }
        case 6:
        {
            return _iceD_getTask(in, current);
        }
        case 7:
        {
            return _iceD_getTaskItems(in, current);
        }
        case 8:
        {
            return _iceD_getTaskList(in, current);
        }
        case 9:
        {
            return _iceD_getVersion(in, current);
        }
        case 10:
        {
            return _iceD_heartDebug(in, current);
        }
        case 11:
        {
            return _iceD_ice_id(in, current);
        }
        case 12:
        {
            return _iceD_ice_ids(in, current);
        }
        case 13:
        {
            return _iceD_ice_isA(in, current);
        }
        case 14:
        {
            return _iceD_ice_ping(in, current);
        }
        case 15:
        {
            return _iceD_isDebugging(in, current);
        }
        case 16:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 17:
        {
            return _iceD_pauseTask(in, current);
        }
        case 18:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 19:
        {
            return _iceD_resumeTask(in, current);
        }
        case 20:
        {
            return _iceD_startDebug(in, current);
        }
        case 21:
        {
            return _iceD_startTask(in, current);
        }
        case 22:
        {
            return _iceD_stopDebug(in, current);
        }
        case 23:
        {
            return _iceD_stopTask(in, current);
        }
        case 24:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGMPTaskManager::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGMPTaskManager, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGMPTaskManager::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGMPTaskManager, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGMPTaskManagerPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGMPTaskManagerPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGMPTaskManager::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPPatrolDeviceCtrl.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGOPPatrolDeviceCtrl.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPPatrolDeviceCtrl",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_ops[] =
{
    "captureImage",
    "checkState",
    "devicePresetCtrl",
    "deviceYk",
    "deviceYs",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "presetPointCtrl",
    "recordAudio",
    "recordVideo",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_presetPointCtrl_name = "presetPointCtrl";
const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_devicePresetCtrl_name = "devicePresetCtrl";
const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYk_name = "deviceYk";
const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYs_name = "deviceYs";
const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_captureImage_name = "captureImage";
const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordAudio_name = "recordAudio";
const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordVideo_name = "recordVideo";

}

bool
ZG6000::ZGOPPatrolDeviceCtrl::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids, iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGOPPatrolDeviceCtrl::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids[0], &iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids[3]);
}

::std::string
ZG6000::ZGOPPatrolDeviceCtrl::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPPatrolDeviceCtrl::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGOPPatrolDeviceCtrl";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_presetPointCtrl(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_devicePresetID;
    istr->readAll(iceP_devicePresetID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->presetPointCtrl(::std::move(iceP_devicePresetID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_devicePresetCtrl(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_presetNo;
    istr->readAll(iceP_deviceID, iceP_presetNo);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->devicePresetCtrl(::std::move(iceP_deviceID), ::std::move(iceP_presetNo), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_deviceYk(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    ::std::string iceP_propertyValue;
    istr->readAll(iceP_deviceID, iceP_propertyName, iceP_propertyValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deviceYk(::std::move(iceP_deviceID), ::std::move(iceP_propertyName), ::std::move(iceP_propertyValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_deviceYs(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    ::std::string iceP_propertyValue;
    istr->readAll(iceP_deviceID, iceP_propertyName, iceP_propertyValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deviceYs(::std::move(iceP_deviceID), ::std::move(iceP_propertyName), ::std::move(iceP_propertyValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_captureImage(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_yvID;
    istr->readAll(iceP_yvID);
    inS.endReadParams();
    ::std::string iceP_url;
    ErrorInfo iceP_e;
    bool ret = this->captureImage(::std::move(iceP_yvID), iceP_url, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_url, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_recordAudio(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_yvID;
    int iceP_duration;
    istr->readAll(iceP_yvID, iceP_duration);
    inS.endReadParams();
    ::std::string iceP_url;
    ErrorInfo iceP_e;
    bool ret = this->recordAudio(::std::move(iceP_yvID), iceP_duration, iceP_url, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_url, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_recordVideo(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_yvID;
    int iceP_duration;
    istr->readAll(iceP_yvID, iceP_duration);
    inS.endReadParams();
    ::std::string iceP_url;
    ErrorInfo iceP_e;
    bool ret = this->recordVideo(::std::move(iceP_yvID), iceP_duration, iceP_url, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_url, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPPatrolDeviceCtrl_ops, iceC_ZG6000_ZGOPPatrolDeviceCtrl_ops + 22, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPPatrolDeviceCtrl_ops)
    {
        case 0:
        {
            return _iceD_captureImage(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_devicePresetCtrl(in, current);
        }
        case 3:
        {
            return _iceD_deviceYk(in, current);
        }
        case 4:
        {
            return _iceD_deviceYs(in, current);
        }
        case 5:
        {
            return _iceD_dispatchData(in, current);
        }
        case 6:
        {
            return _iceD_exitApp(in, current);
        }
        case 7:
        {
            return _iceD_getVersion(in, current);
        }
        case 8:
        {
            return _iceD_heartDebug(in, current);
        }
        case 9:
        {
            return _iceD_ice_id(in, current);
        }
        case 10:
        {
            return _iceD_ice_ids(in, current);
        }
        case 11:
        {
            return _iceD_ice_isA(in, current);
        }
        case 12:
        {
            return _iceD_ice_ping(in, current);
        }
        case 13:
        {
            return _iceD_isDebugging(in, current);
        }
        case 14:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 15:
        {
            return _iceD_presetPointCtrl(in, current);
        }
        case 16:
        {
            return _iceD_recordAudio(in, current);
        }
        case 17:
        {
            return _iceD_recordVideo(in, current);
        }
        case 18:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 19:
        {
            return _iceD_startDebug(in, current);
        }
        case 20:
        {
            return _iceD_stopDebug(in, current);
        }
        case 21:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_presetPointCtrl(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::PresetPointCtrlResult>>& outAsync, const ::std::string& iceP_devicePresetID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_presetPointCtrl_name);
    outAsync->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_presetPointCtrl_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_devicePresetID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPPatrolDeviceCtrl::PresetPointCtrlResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_devicePresetCtrl(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::DevicePresetCtrlResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_presetNo, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_devicePresetCtrl_name);
    outAsync->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_devicePresetCtrl_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_presetNo);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPPatrolDeviceCtrl::DevicePresetCtrlResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_deviceYk(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::DeviceYkResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::std::string& iceP_propertyValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYk_name);
    outAsync->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYk_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_propertyName, iceP_propertyValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPPatrolDeviceCtrl::DeviceYkResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_deviceYs(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::DeviceYsResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::std::string& iceP_propertyValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYs_name);
    outAsync->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYs_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_propertyName, iceP_propertyValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPPatrolDeviceCtrl::DeviceYsResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_captureImage(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::CaptureImageResult>>& outAsync, const ::std::string& iceP_yvID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_captureImage_name);
    outAsync->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_captureImage_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_yvID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPPatrolDeviceCtrl::CaptureImageResult v;
            istr->readAll(v.url, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_recordAudio(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::RecordAudioResult>>& outAsync, const ::std::string& iceP_yvID, int iceP_duration, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordAudio_name);
    outAsync->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordAudio_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_yvID, iceP_duration);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPPatrolDeviceCtrl::RecordAudioResult v;
            istr->readAll(v.url, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_recordVideo(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::RecordVideoResult>>& outAsync, const ::std::string& iceP_yvID, int iceP_duration, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordVideo_name);
    outAsync->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordVideo_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_yvID, iceP_duration);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPPatrolDeviceCtrl::RecordVideoResult v;
            istr->readAll(v.url, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGOPPatrolDeviceCtrlPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGOPPatrolDeviceCtrlPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGOPPatrolDeviceCtrlPrx::ice_staticId()
{
    return ZGOPPatrolDeviceCtrl::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_presetPointCtrl_name = "presetPointCtrl";

const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_devicePresetCtrl_name = "devicePresetCtrl";

const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYk_name = "deviceYk";

const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYs_name = "deviceYs";

const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_captureImage_name = "captureImage";

const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordAudio_name = "recordAudio";

const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordVideo_name = "recordVideo";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGOPPatrolDeviceCtrl* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGOPPatrolDeviceCtrl>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGOPPatrolDeviceCtrl;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_begin_presetPointCtrl(const ::std::string& iceP_devicePresetID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_presetPointCtrl_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_presetPointCtrl_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPPatrolDeviceCtrl_presetPointCtrl_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_devicePresetID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_presetPointCtrl_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::end_presetPointCtrl(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_presetPointCtrl_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_end_presetPointCtrl(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_presetPointCtrl_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_begin_devicePresetCtrl(const ::std::string& iceP_deviceID, const ::std::string& iceP_presetNo, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_devicePresetCtrl_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_devicePresetCtrl_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPPatrolDeviceCtrl_devicePresetCtrl_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_presetNo);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_devicePresetCtrl_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::end_devicePresetCtrl(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_devicePresetCtrl_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_end_devicePresetCtrl(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_devicePresetCtrl_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_begin_deviceYk(const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::std::string& iceP_propertyValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYk_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYk_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYk_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_propertyName);
        ostr->write(iceP_propertyValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYk_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::end_deviceYk(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYk_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_end_deviceYk(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYk_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_begin_deviceYs(const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::std::string& iceP_propertyValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYs_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYs_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYs_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_propertyName);
        ostr->write(iceP_propertyValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYs_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::end_deviceYs(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYs_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_end_deviceYs(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_deviceYs_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_begin_captureImage(const ::std::string& iceP_yvID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_captureImage_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_captureImage_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPPatrolDeviceCtrl_captureImage_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_yvID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_captureImage_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::end_captureImage(::std::string& iceP_url, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_captureImage_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_url);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_end_captureImage(::std::string& iceP_url, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_captureImage_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_url);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_begin_recordAudio(const ::std::string& iceP_yvID, ::Ice::Int iceP_duration, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordAudio_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordAudio_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordAudio_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_yvID);
        ostr->write(iceP_duration);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordAudio_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::end_recordAudio(::std::string& iceP_url, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordAudio_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_url);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_end_recordAudio(::std::string& iceP_url, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordAudio_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_url);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_begin_recordVideo(const ::std::string& iceP_yvID, ::Ice::Int iceP_duration, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordVideo_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordVideo_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordVideo_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_yvID);
        ostr->write(iceP_duration);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordVideo_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::end_recordVideo(::std::string& iceP_url, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordVideo_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_url);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_iceI_end_recordVideo(::std::string& iceP_url, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPPatrolDeviceCtrl_recordVideo_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_url);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::_newInstance() const
{
    return new ZGOPPatrolDeviceCtrl;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::ice_staticId()
{
    return ::ZG6000::ZGOPPatrolDeviceCtrl::ice_staticId();
}

ZG6000::ZGOPPatrolDeviceCtrl::~ZGOPPatrolDeviceCtrl()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGOPPatrolDeviceCtrl* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPPatrolDeviceCtrl",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGOPPatrolDeviceCtrl::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids, iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGOPPatrolDeviceCtrl::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids[0], &iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids[3]);
}

const ::std::string&
ZG6000::ZGOPPatrolDeviceCtrl::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPPatrolDeviceCtrl::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGOPPatrolDeviceCtrl";
    return typeId;
#else
    return iceC_ZG6000_ZGOPPatrolDeviceCtrl_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_presetPointCtrl(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_devicePresetID;
    istr->read(iceP_devicePresetID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->presetPointCtrl(iceP_devicePresetID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_devicePresetCtrl(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_presetNo;
    istr->read(iceP_deviceID);
    istr->read(iceP_presetNo);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->devicePresetCtrl(iceP_deviceID, iceP_presetNo, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_deviceYk(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    ::std::string iceP_propertyValue;
    istr->read(iceP_deviceID);
    istr->read(iceP_propertyName);
    istr->read(iceP_propertyValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deviceYk(iceP_deviceID, iceP_propertyName, iceP_propertyValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_deviceYs(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    ::std::string iceP_propertyValue;
    istr->read(iceP_deviceID);
    istr->read(iceP_propertyName);
    istr->read(iceP_propertyValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deviceYs(iceP_deviceID, iceP_propertyName, iceP_propertyValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_captureImage(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_yvID;
    istr->read(iceP_yvID);
    inS.endReadParams();
    ::std::string iceP_url;
    ErrorInfo iceP_e;
    bool ret = this->captureImage(iceP_yvID, iceP_url, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_url);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_recordAudio(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_yvID;
    ::Ice::Int iceP_duration;
    istr->read(iceP_yvID);
    istr->read(iceP_duration);
    inS.endReadParams();
    ::std::string iceP_url;
    ErrorInfo iceP_e;
    bool ret = this->recordAudio(iceP_yvID, iceP_duration, iceP_url, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_url);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceD_recordVideo(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_yvID;
    ::Ice::Int iceP_duration;
    istr->read(iceP_yvID);
    istr->read(iceP_duration);
    inS.endReadParams();
    ::std::string iceP_url;
    ErrorInfo iceP_e;
    bool ret = this->recordVideo(iceP_yvID, iceP_duration, iceP_url, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_url);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPPatrolDeviceCtrl_all[] =
{
    "captureImage",
    "checkState",
    "devicePresetCtrl",
    "deviceYk",
    "deviceYs",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "presetPointCtrl",
    "recordAudio",
    "recordVideo",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGOPPatrolDeviceCtrl::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPPatrolDeviceCtrl_all, iceC_ZG6000_ZGOPPatrolDeviceCtrl_all + 22, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPPatrolDeviceCtrl_all)
    {
        case 0:
        {
            return _iceD_captureImage(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_devicePresetCtrl(in, current);
        }
        case 3:
        {
            return _iceD_deviceYk(in, current);
        }
        case 4:
        {
            return _iceD_deviceYs(in, current);
        }
        case 5:
        {
            return _iceD_dispatchData(in, current);
        }
        case 6:
        {
            return _iceD_exitApp(in, current);
        }
        case 7:
        {
            return _iceD_getVersion(in, current);
        }
        case 8:
        {
            return _iceD_heartDebug(in, current);
        }
        case 9:
        {
            return _iceD_ice_id(in, current);
        }
        case 10:
        {
            return _iceD_ice_ids(in, current);
        }
        case 11:
        {
            return _iceD_ice_isA(in, current);
        }
        case 12:
        {
            return _iceD_ice_ping(in, current);
        }
        case 13:
        {
            return _iceD_isDebugging(in, current);
        }
        case 14:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 15:
        {
            return _iceD_presetPointCtrl(in, current);
        }
        case 16:
        {
            return _iceD_recordAudio(in, current);
        }
        case 17:
        {
            return _iceD_recordVideo(in, current);
        }
        case 18:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 19:
        {
            return _iceD_startDebug(in, current);
        }
        case 20:
        {
            return _iceD_stopDebug(in, current);
        }
        case 21:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGOPPatrolDeviceCtrl::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGOPPatrolDeviceCtrl, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGOPPatrolDeviceCtrl::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGOPPatrolDeviceCtrl, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGOPPatrolDeviceCtrlPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGOPPatrolDeviceCtrlPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGOPPatrolDeviceCtrl::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPVoicePlay.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPVoicePlay_h__
#define __ZGSPVoicePlay_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPVoicePlay;
class ZGSPVoicePlayPrx;

}

namespace ZG6000
{

class ZGSPVoicePlay : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPVoicePlayPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param current The Current object for the invocation.
     * @brief   播放指定告警等级的声音
     */
    virtual void play(::std::string alarmLevelID, ::std::string speechText, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_play(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param current The Current object for the invocation.
     * @brief   播放声音列表
     */
    virtual void playMulti(ListStringMap listVoice, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_playMulti(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param speechText 语音文本.
     * @param current The Current object for the invocation.
     * @brief   立即播放语音
     */
    virtual void speak(::std::string speechText, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_speak(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param current The Current object for the invocation.
     * @brief   立即播放指定次数的语音
     */
    virtual void speakCount(::std::string speechText, int repeatCount, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_speakCount(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @brief   开始语音播放
     */
    virtual void start(const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_start(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @brief   停止语音播放
     */
    virtual void stop(const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_stop(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @brief   清除语音播放
     */
    virtual void clear(const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_clear(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPVoicePlayPrx : public virtual ::Ice::Proxy<ZGSPVoicePlayPrx, ZGServerBasePrx>
{
public:

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param context The Context map to send with the invocation.
     * @brief   播放指定告警等级的声音
     */
    void play(const ::std::string& alarmLevelID, const ::std::string& speechText, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPVoicePlayPrx::_iceI_play, alarmLevelID, speechText, context).get();
    }

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   播放指定告警等级的声音
     */
    template<template<typename> class P = ::std::promise>
    auto playAsync(const ::std::string& alarmLevelID, const ::std::string& speechText, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPVoicePlayPrx::_iceI_play, alarmLevelID, speechText, context);
    }

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   播放指定告警等级的声音
     */
    ::std::function<void()>
    playAsync(const ::std::string& alarmLevelID, const ::std::string& speechText,
              ::std::function<void()> response,
              ::std::function<void(::std::exception_ptr)> ex = nullptr,
              ::std::function<void(bool)> sent = nullptr,
              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPVoicePlayPrx::_iceI_play, alarmLevelID, speechText, context);
    }

    /// \cond INTERNAL
    void _iceI_play(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param context The Context map to send with the invocation.
     * @brief   播放声音列表
     */
    void playMulti(const ListStringMap& listVoice, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPVoicePlayPrx::_iceI_playMulti, listVoice, context).get();
    }

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   播放声音列表
     */
    template<template<typename> class P = ::std::promise>
    auto playMultiAsync(const ListStringMap& listVoice, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPVoicePlayPrx::_iceI_playMulti, listVoice, context);
    }

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   播放声音列表
     */
    ::std::function<void()>
    playMultiAsync(const ListStringMap& listVoice,
                   ::std::function<void()> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPVoicePlayPrx::_iceI_playMulti, listVoice, context);
    }

    /// \cond INTERNAL
    void _iceI_playMulti(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param speechText 语音文本.
     * @param context The Context map to send with the invocation.
     * @brief   立即播放语音
     */
    void speak(const ::std::string& speechText, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPVoicePlayPrx::_iceI_speak, speechText, context).get();
    }

    /**
     * @param speechText 语音文本.
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   立即播放语音
     */
    template<template<typename> class P = ::std::promise>
    auto speakAsync(const ::std::string& speechText, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPVoicePlayPrx::_iceI_speak, speechText, context);
    }

    /**
     * @param speechText 语音文本.
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   立即播放语音
     */
    ::std::function<void()>
    speakAsync(const ::std::string& speechText,
               ::std::function<void()> response,
               ::std::function<void(::std::exception_ptr)> ex = nullptr,
               ::std::function<void(bool)> sent = nullptr,
               const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPVoicePlayPrx::_iceI_speak, speechText, context);
    }

    /// \cond INTERNAL
    void _iceI_speak(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param context The Context map to send with the invocation.
     * @brief   立即播放指定次数的语音
     */
    void speakCount(const ::std::string& speechText, int repeatCount, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPVoicePlayPrx::_iceI_speakCount, speechText, repeatCount, context).get();
    }

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   立即播放指定次数的语音
     */
    template<template<typename> class P = ::std::promise>
    auto speakCountAsync(const ::std::string& speechText, int repeatCount, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPVoicePlayPrx::_iceI_speakCount, speechText, repeatCount, context);
    }

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   立即播放指定次数的语音
     */
    ::std::function<void()>
    speakCountAsync(const ::std::string& speechText, int repeatCount,
                    ::std::function<void()> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPVoicePlayPrx::_iceI_speakCount, speechText, repeatCount, context);
    }

    /// \cond INTERNAL
    void _iceI_speakCount(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @brief   开始语音播放
     */
    void start(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPVoicePlayPrx::_iceI_start, context).get();
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   开始语音播放
     */
    template<template<typename> class P = ::std::promise>
    auto startAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPVoicePlayPrx::_iceI_start, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   开始语音播放
     */
    ::std::function<void()>
    startAsync(::std::function<void()> response,
               ::std::function<void(::std::exception_ptr)> ex = nullptr,
               ::std::function<void(bool)> sent = nullptr,
               const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPVoicePlayPrx::_iceI_start, context);
    }

    /// \cond INTERNAL
    void _iceI_start(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @brief   停止语音播放
     */
    void stop(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPVoicePlayPrx::_iceI_stop, context).get();
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   停止语音播放
     */
    template<template<typename> class P = ::std::promise>
    auto stopAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPVoicePlayPrx::_iceI_stop, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   停止语音播放
     */
    ::std::function<void()>
    stopAsync(::std::function<void()> response,
              ::std::function<void(::std::exception_ptr)> ex = nullptr,
              ::std::function<void(bool)> sent = nullptr,
              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPVoicePlayPrx::_iceI_stop, context);
    }

    /// \cond INTERNAL
    void _iceI_stop(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @brief   清除语音播放
     */
    void clear(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPVoicePlayPrx::_iceI_clear, context).get();
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   清除语音播放
     */
    template<template<typename> class P = ::std::promise>
    auto clearAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPVoicePlayPrx::_iceI_clear, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   清除语音播放
     */
    ::std::function<void()>
    clearAsync(::std::function<void()> response,
               ::std::function<void(::std::exception_ptr)> ex = nullptr,
               ::std::function<void(bool)> sent = nullptr,
               const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPVoicePlayPrx::_iceI_clear, context);
    }

    /// \cond INTERNAL
    void _iceI_clear(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPVoicePlayPrx() = default;
    friend ::std::shared_ptr<ZGSPVoicePlayPrx> IceInternal::createProxy<ZGSPVoicePlayPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPVoicePlayPtr = ::std::shared_ptr<ZGSPVoicePlay>;
using ZGSPVoicePlayPrxPtr = ::std::shared_ptr<ZGSPVoicePlayPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPVoicePlay;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPVoicePlay>&);
::IceProxy::Ice::Object* upCast(ZGSPVoicePlay*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPVoicePlay;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPVoicePlay*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPVoicePlay> ZGSPVoicePlayPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPVoicePlay> ZGSPVoicePlayPrx;
typedef ZGSPVoicePlayPrx ZGSPVoicePlayPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPVoicePlayPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_play.
 */
class Callback_ZGSPVoicePlay_play_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPVoicePlay_play_Base> Callback_ZGSPVoicePlay_playPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_playMulti.
 */
class Callback_ZGSPVoicePlay_playMulti_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPVoicePlay_playMulti_Base> Callback_ZGSPVoicePlay_playMultiPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_speak.
 */
class Callback_ZGSPVoicePlay_speak_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPVoicePlay_speak_Base> Callback_ZGSPVoicePlay_speakPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_speakCount.
 */
class Callback_ZGSPVoicePlay_speakCount_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPVoicePlay_speakCount_Base> Callback_ZGSPVoicePlay_speakCountPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_start.
 */
class Callback_ZGSPVoicePlay_start_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPVoicePlay_start_Base> Callback_ZGSPVoicePlay_startPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_stop.
 */
class Callback_ZGSPVoicePlay_stop_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPVoicePlay_stop_Base> Callback_ZGSPVoicePlay_stopPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_clear.
 */
class Callback_ZGSPVoicePlay_clear_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPVoicePlay_clear_Base> Callback_ZGSPVoicePlay_clearPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPVoicePlay : public virtual ::Ice::Proxy<ZGSPVoicePlay, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param context The Context map to send with the invocation.
     * @brief   播放指定告警等级的声音
     */
    void play(const ::std::string& alarmLevelID, const ::std::string& speechText, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_play(_iceI_begin_play(alarmLevelID, speechText, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   播放指定告警等级的声音
     */
    ::Ice::AsyncResultPtr begin_play(const ::std::string& alarmLevelID, const ::std::string& speechText, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_play(alarmLevelID, speechText, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   播放指定告警等级的声音
     */
    ::Ice::AsyncResultPtr begin_play(const ::std::string& alarmLevelID, const ::std::string& speechText, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_play(alarmLevelID, speechText, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   播放指定告警等级的声音
     */
    ::Ice::AsyncResultPtr begin_play(const ::std::string& alarmLevelID, const ::std::string& speechText, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_play(alarmLevelID, speechText, context, cb, cookie);
    }

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   播放指定告警等级的声音
     */
    ::Ice::AsyncResultPtr begin_play(const ::std::string& alarmLevelID, const ::std::string& speechText, const ::ZG6000::Callback_ZGSPVoicePlay_playPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_play(alarmLevelID, speechText, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   播放指定告警等级的声音
     */
    ::Ice::AsyncResultPtr begin_play(const ::std::string& alarmLevelID, const ::std::string& speechText, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPVoicePlay_playPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_play(alarmLevelID, speechText, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_play.
     * @param result The asynchronous result object for the invocation.
     */
    void end_play(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_play(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param context The Context map to send with the invocation.
     * @brief   播放声音列表
     */
    void playMulti(const ::ZG6000::ListStringMap& listVoice, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_playMulti(_iceI_begin_playMulti(listVoice, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   播放声音列表
     */
    ::Ice::AsyncResultPtr begin_playMulti(const ::ZG6000::ListStringMap& listVoice, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_playMulti(listVoice, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   播放声音列表
     */
    ::Ice::AsyncResultPtr begin_playMulti(const ::ZG6000::ListStringMap& listVoice, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_playMulti(listVoice, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   播放声音列表
     */
    ::Ice::AsyncResultPtr begin_playMulti(const ::ZG6000::ListStringMap& listVoice, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_playMulti(listVoice, context, cb, cookie);
    }

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   播放声音列表
     */
    ::Ice::AsyncResultPtr begin_playMulti(const ::ZG6000::ListStringMap& listVoice, const ::ZG6000::Callback_ZGSPVoicePlay_playMultiPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_playMulti(listVoice, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   播放声音列表
     */
    ::Ice::AsyncResultPtr begin_playMulti(const ::ZG6000::ListStringMap& listVoice, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPVoicePlay_playMultiPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_playMulti(listVoice, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_playMulti.
     * @param result The asynchronous result object for the invocation.
     */
    void end_playMulti(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_playMulti(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param speechText 语音文本.
     * @param context The Context map to send with the invocation.
     * @brief   立即播放语音
     */
    void speak(const ::std::string& speechText, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_speak(_iceI_begin_speak(speechText, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param speechText 语音文本.
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   立即播放语音
     */
    ::Ice::AsyncResultPtr begin_speak(const ::std::string& speechText, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_speak(speechText, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param speechText 语音文本.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   立即播放语音
     */
    ::Ice::AsyncResultPtr begin_speak(const ::std::string& speechText, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_speak(speechText, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param speechText 语音文本.
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   立即播放语音
     */
    ::Ice::AsyncResultPtr begin_speak(const ::std::string& speechText, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_speak(speechText, context, cb, cookie);
    }

    /**
     * @param speechText 语音文本.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   立即播放语音
     */
    ::Ice::AsyncResultPtr begin_speak(const ::std::string& speechText, const ::ZG6000::Callback_ZGSPVoicePlay_speakPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_speak(speechText, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param speechText 语音文本.
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   立即播放语音
     */
    ::Ice::AsyncResultPtr begin_speak(const ::std::string& speechText, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPVoicePlay_speakPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_speak(speechText, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_speak.
     * @param result The asynchronous result object for the invocation.
     */
    void end_speak(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_speak(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param context The Context map to send with the invocation.
     * @brief   立即播放指定次数的语音
     */
    void speakCount(const ::std::string& speechText, ::Ice::Int repeatCount, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_speakCount(_iceI_begin_speakCount(speechText, repeatCount, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   立即播放指定次数的语音
     */
    ::Ice::AsyncResultPtr begin_speakCount(const ::std::string& speechText, ::Ice::Int repeatCount, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_speakCount(speechText, repeatCount, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   立即播放指定次数的语音
     */
    ::Ice::AsyncResultPtr begin_speakCount(const ::std::string& speechText, ::Ice::Int repeatCount, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_speakCount(speechText, repeatCount, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   立即播放指定次数的语音
     */
    ::Ice::AsyncResultPtr begin_speakCount(const ::std::string& speechText, ::Ice::Int repeatCount, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_speakCount(speechText, repeatCount, context, cb, cookie);
    }

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   立即播放指定次数的语音
     */
    ::Ice::AsyncResultPtr begin_speakCount(const ::std::string& speechText, ::Ice::Int repeatCount, const ::ZG6000::Callback_ZGSPVoicePlay_speakCountPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_speakCount(speechText, repeatCount, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   立即播放指定次数的语音
     */
    ::Ice::AsyncResultPtr begin_speakCount(const ::std::string& speechText, ::Ice::Int repeatCount, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPVoicePlay_speakCountPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_speakCount(speechText, repeatCount, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_speakCount.
     * @param result The asynchronous result object for the invocation.
     */
    void end_speakCount(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_speakCount(const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @brief   开始语音播放
     */
    void start(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_start(_iceI_begin_start(context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   开始语音播放
     */
    ::Ice::AsyncResultPtr begin_start(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_start(context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   开始语音播放
     */
    ::Ice::AsyncResultPtr begin_start(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_start(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   开始语音播放
     */
    ::Ice::AsyncResultPtr begin_start(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_start(context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   开始语音播放
     */
    ::Ice::AsyncResultPtr begin_start(const ::ZG6000::Callback_ZGSPVoicePlay_startPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_start(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   开始语音播放
     */
    ::Ice::AsyncResultPtr begin_start(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPVoicePlay_startPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_start(context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_start.
     * @param result The asynchronous result object for the invocation.
     */
    void end_start(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_start(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @brief   停止语音播放
     */
    void stop(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_stop(_iceI_begin_stop(context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   停止语音播放
     */
    ::Ice::AsyncResultPtr begin_stop(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_stop(context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   停止语音播放
     */
    ::Ice::AsyncResultPtr begin_stop(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stop(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   停止语音播放
     */
    ::Ice::AsyncResultPtr begin_stop(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stop(context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   停止语音播放
     */
    ::Ice::AsyncResultPtr begin_stop(const ::ZG6000::Callback_ZGSPVoicePlay_stopPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stop(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   停止语音播放
     */
    ::Ice::AsyncResultPtr begin_stop(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPVoicePlay_stopPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stop(context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_stop.
     * @param result The asynchronous result object for the invocation.
     */
    void end_stop(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_stop(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @brief   清除语音播放
     */
    void clear(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_clear(_iceI_begin_clear(context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   清除语音播放
     */
    ::Ice::AsyncResultPtr begin_clear(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_clear(context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   清除语音播放
     */
    ::Ice::AsyncResultPtr begin_clear(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clear(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   清除语音播放
     */
    ::Ice::AsyncResultPtr begin_clear(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clear(context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   清除语音播放
     */
    ::Ice::AsyncResultPtr begin_clear(const ::ZG6000::Callback_ZGSPVoicePlay_clearPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clear(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   清除语音播放
     */
    ::Ice::AsyncResultPtr begin_clear(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPVoicePlay_clearPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_clear(context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_clear.
     * @param result The asynchronous result object for the invocation.
     */
    void end_clear(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_clear(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPVoicePlay : virtual public ZGServerBase
{
public:

    typedef ZGSPVoicePlayPrx ProxyType;
    typedef ZGSPVoicePlayPtr PointerType;

    virtual ~ZGSPVoicePlay();

#ifdef ICE_CPP11_COMPILER
    ZGSPVoicePlay() = default;
    ZGSPVoicePlay(const ZGSPVoicePlay&) = default;
    ZGSPVoicePlay& operator=(const ZGSPVoicePlay&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param alarmLevelID 告警等级
     * @param speechText 语音文本内容
     * @param current The Current object for the invocation.
     * @brief   播放指定告警等级的声音
     */
    virtual void play(const ::std::string& alarmLevelID, const ::std::string& speechText, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_play(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listVoice 声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     * 第二个元素key为speechText，值为语音文本
     * @param current The Current object for the invocation.
     * @brief   播放声音列表
     */
    virtual void playMulti(const ListStringMap& listVoice, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_playMulti(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param speechText 语音文本.
     * @param current The Current object for the invocation.
     * @brief   立即播放语音
     */
    virtual void speak(const ::std::string& speechText, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_speak(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param speechText 语音文本
     * @param repeatCount 重复次数
     * @param current The Current object for the invocation.
     * @brief   立即播放指定次数的语音
     */
    virtual void speakCount(const ::std::string& speechText, ::Ice::Int repeatCount, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_speakCount(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @brief   开始语音播放
     */
    virtual void start(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_start(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @brief   停止语音播放
     */
    virtual void stop(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_stop(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @brief   清除语音播放
     */
    virtual void clear(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_clear(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPVoicePlay& lhs, const ZGSPVoicePlay& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPVoicePlay& lhs, const ZGSPVoicePlay& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_play.
 */
template<class T>
class CallbackNC_ZGSPVoicePlay_play : public Callback_ZGSPVoicePlay_play_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPVoicePlay_play(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 */
template<class T> Callback_ZGSPVoicePlay_playPtr
newCallback_ZGSPVoicePlay_play(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_play<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 */
template<class T> Callback_ZGSPVoicePlay_playPtr
newCallback_ZGSPVoicePlay_play(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_play<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 */
template<class T> Callback_ZGSPVoicePlay_playPtr
newCallback_ZGSPVoicePlay_play(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_play<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 */
template<class T> Callback_ZGSPVoicePlay_playPtr
newCallback_ZGSPVoicePlay_play(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_play<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_play.
 */
template<class T, typename CT>
class Callback_ZGSPVoicePlay_play : public Callback_ZGSPVoicePlay_play_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPVoicePlay_play(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_playPtr
newCallback_ZGSPVoicePlay_play(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_play<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_playPtr
newCallback_ZGSPVoicePlay_play(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_play<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_playPtr
newCallback_ZGSPVoicePlay_play(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_play<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_play.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_playPtr
newCallback_ZGSPVoicePlay_play(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_play<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_playMulti.
 */
template<class T>
class CallbackNC_ZGSPVoicePlay_playMulti : public Callback_ZGSPVoicePlay_playMulti_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPVoicePlay_playMulti(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 */
template<class T> Callback_ZGSPVoicePlay_playMultiPtr
newCallback_ZGSPVoicePlay_playMulti(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_playMulti<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 */
template<class T> Callback_ZGSPVoicePlay_playMultiPtr
newCallback_ZGSPVoicePlay_playMulti(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_playMulti<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 */
template<class T> Callback_ZGSPVoicePlay_playMultiPtr
newCallback_ZGSPVoicePlay_playMulti(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_playMulti<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 */
template<class T> Callback_ZGSPVoicePlay_playMultiPtr
newCallback_ZGSPVoicePlay_playMulti(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_playMulti<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_playMulti.
 */
template<class T, typename CT>
class Callback_ZGSPVoicePlay_playMulti : public Callback_ZGSPVoicePlay_playMulti_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPVoicePlay_playMulti(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_playMultiPtr
newCallback_ZGSPVoicePlay_playMulti(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_playMulti<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_playMultiPtr
newCallback_ZGSPVoicePlay_playMulti(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_playMulti<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_playMultiPtr
newCallback_ZGSPVoicePlay_playMulti(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_playMulti<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_playMulti.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_playMultiPtr
newCallback_ZGSPVoicePlay_playMulti(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_playMulti<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_speak.
 */
template<class T>
class CallbackNC_ZGSPVoicePlay_speak : public Callback_ZGSPVoicePlay_speak_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPVoicePlay_speak(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 */
template<class T> Callback_ZGSPVoicePlay_speakPtr
newCallback_ZGSPVoicePlay_speak(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_speak<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 */
template<class T> Callback_ZGSPVoicePlay_speakPtr
newCallback_ZGSPVoicePlay_speak(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_speak<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 */
template<class T> Callback_ZGSPVoicePlay_speakPtr
newCallback_ZGSPVoicePlay_speak(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_speak<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 */
template<class T> Callback_ZGSPVoicePlay_speakPtr
newCallback_ZGSPVoicePlay_speak(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_speak<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_speak.
 */
template<class T, typename CT>
class Callback_ZGSPVoicePlay_speak : public Callback_ZGSPVoicePlay_speak_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPVoicePlay_speak(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_speakPtr
newCallback_ZGSPVoicePlay_speak(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_speak<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_speakPtr
newCallback_ZGSPVoicePlay_speak(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_speak<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_speakPtr
newCallback_ZGSPVoicePlay_speak(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_speak<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speak.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_speakPtr
newCallback_ZGSPVoicePlay_speak(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_speak<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_speakCount.
 */
template<class T>
class CallbackNC_ZGSPVoicePlay_speakCount : public Callback_ZGSPVoicePlay_speakCount_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPVoicePlay_speakCount(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 */
template<class T> Callback_ZGSPVoicePlay_speakCountPtr
newCallback_ZGSPVoicePlay_speakCount(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_speakCount<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 */
template<class T> Callback_ZGSPVoicePlay_speakCountPtr
newCallback_ZGSPVoicePlay_speakCount(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_speakCount<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 */
template<class T> Callback_ZGSPVoicePlay_speakCountPtr
newCallback_ZGSPVoicePlay_speakCount(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_speakCount<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 */
template<class T> Callback_ZGSPVoicePlay_speakCountPtr
newCallback_ZGSPVoicePlay_speakCount(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_speakCount<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_speakCount.
 */
template<class T, typename CT>
class Callback_ZGSPVoicePlay_speakCount : public Callback_ZGSPVoicePlay_speakCount_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPVoicePlay_speakCount(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_speakCountPtr
newCallback_ZGSPVoicePlay_speakCount(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_speakCount<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_speakCountPtr
newCallback_ZGSPVoicePlay_speakCount(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_speakCount<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_speakCountPtr
newCallback_ZGSPVoicePlay_speakCount(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_speakCount<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_speakCount.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_speakCountPtr
newCallback_ZGSPVoicePlay_speakCount(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_speakCount<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_start.
 */
template<class T>
class CallbackNC_ZGSPVoicePlay_start : public Callback_ZGSPVoicePlay_start_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPVoicePlay_start(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 */
template<class T> Callback_ZGSPVoicePlay_startPtr
newCallback_ZGSPVoicePlay_start(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_start<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 */
template<class T> Callback_ZGSPVoicePlay_startPtr
newCallback_ZGSPVoicePlay_start(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_start<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 */
template<class T> Callback_ZGSPVoicePlay_startPtr
newCallback_ZGSPVoicePlay_start(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_start<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 */
template<class T> Callback_ZGSPVoicePlay_startPtr
newCallback_ZGSPVoicePlay_start(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_start<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_start.
 */
template<class T, typename CT>
class Callback_ZGSPVoicePlay_start : public Callback_ZGSPVoicePlay_start_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPVoicePlay_start(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_startPtr
newCallback_ZGSPVoicePlay_start(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_start<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_startPtr
newCallback_ZGSPVoicePlay_start(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_start<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_startPtr
newCallback_ZGSPVoicePlay_start(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_start<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_start.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_startPtr
newCallback_ZGSPVoicePlay_start(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_start<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_stop.
 */
template<class T>
class CallbackNC_ZGSPVoicePlay_stop : public Callback_ZGSPVoicePlay_stop_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPVoicePlay_stop(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 */
template<class T> Callback_ZGSPVoicePlay_stopPtr
newCallback_ZGSPVoicePlay_stop(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_stop<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 */
template<class T> Callback_ZGSPVoicePlay_stopPtr
newCallback_ZGSPVoicePlay_stop(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_stop<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 */
template<class T> Callback_ZGSPVoicePlay_stopPtr
newCallback_ZGSPVoicePlay_stop(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_stop<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 */
template<class T> Callback_ZGSPVoicePlay_stopPtr
newCallback_ZGSPVoicePlay_stop(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_stop<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_stop.
 */
template<class T, typename CT>
class Callback_ZGSPVoicePlay_stop : public Callback_ZGSPVoicePlay_stop_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPVoicePlay_stop(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_stopPtr
newCallback_ZGSPVoicePlay_stop(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_stop<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_stopPtr
newCallback_ZGSPVoicePlay_stop(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_stop<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_stopPtr
newCallback_ZGSPVoicePlay_stop(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_stop<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_stop.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_stopPtr
newCallback_ZGSPVoicePlay_stop(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_stop<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_clear.
 */
template<class T>
class CallbackNC_ZGSPVoicePlay_clear : public Callback_ZGSPVoicePlay_clear_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPVoicePlay_clear(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 */
template<class T> Callback_ZGSPVoicePlay_clearPtr
newCallback_ZGSPVoicePlay_clear(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_clear<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 */
template<class T> Callback_ZGSPVoicePlay_clearPtr
newCallback_ZGSPVoicePlay_clear(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_clear<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 */
template<class T> Callback_ZGSPVoicePlay_clearPtr
newCallback_ZGSPVoicePlay_clear(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_clear<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 */
template<class T> Callback_ZGSPVoicePlay_clearPtr
newCallback_ZGSPVoicePlay_clear(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPVoicePlay_clear<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPVoicePlay_clear.
 */
template<class T, typename CT>
class Callback_ZGSPVoicePlay_clear : public Callback_ZGSPVoicePlay_clear_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPVoicePlay_clear(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_clearPtr
newCallback_ZGSPVoicePlay_clear(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_clear<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_clearPtr
newCallback_ZGSPVoicePlay_clear(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_clear<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_clearPtr
newCallback_ZGSPVoicePlay_clear(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_clear<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPVoicePlay::begin_clear.
 */
template<class T, typename CT> Callback_ZGSPVoicePlay_clearPtr
newCallback_ZGSPVoicePlay_clear(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPVoicePlay_clear<T, CT>(instance, 0, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGDPDeviceProperty.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGDPDeviceProperty_h__
#define __ZGDPDeviceProperty_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGDPDeviceProperty;
class ZGDPDevicePropertyPrx;

}

namespace ZG6000
{

class ZGDPDeviceProperty : public virtual ZGServerBase
{
public:

    using ProxyType = ZGDPDevicePropertyPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getFieldsProperties.
     */
    struct GetFieldsPropertiesResult
    {
        bool returnValue;
        ListStringMap properties;
        ErrorInfo e;
    };

    virtual bool getFieldsProperties(::std::string deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getFieldsProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getStaticProperties.
     */
    struct GetStaticPropertiesResult
    {
        bool returnValue;
        ListStringMap properties;
        ErrorInfo e;
    };

    virtual bool getStaticProperties(::std::string deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getStaticProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDynamicProperties.
     */
    struct GetDynamicPropertiesResult
    {
        bool returnValue;
        ListStringMap properties;
        ErrorInfo e;
    };

    virtual bool getDynamicProperties(::std::string deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDynamicProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getWholeProperties.
     */
    struct GetWholePropertiesResult
    {
        bool returnValue;
        ListStringMap properties;
        ErrorInfo e;
    };

    virtual bool getWholeProperties(::std::string deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getWholeProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to mgetProperty.
     */
    struct MgetPropertyResult
    {
        bool returnValue;
        MapStringMap properties;
        ErrorInfo e;
    };

    virtual bool mgetProperty(StringList listDeviceID, ::std::string propertyName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getProperty.
     */
    struct GetPropertyResult
    {
        bool returnValue;
        StringMap property;
        ErrorInfo e;
    };

    virtual bool getProperty(::std::string deviceID, ::std::string propertyName, StringMap& property, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to mgetPropertyValues.
     */
    struct MgetPropertyValuesResult
    {
        bool returnValue;
        MapStringMap propertyValues;
        ErrorInfo e;
    };

    virtual bool mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& propertyValues, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetPropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to mgetPropertyValue.
     */
    struct MgetPropertyValueResult
    {
        bool returnValue;
        StringMap propertyValues;
        ErrorInfo e;
    };

    virtual bool mgetPropertyValue(StringList listDeviceID, ::std::string propertyName, StringMap& propertyValues, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetPropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertyValue.
     */
    struct GetPropertyValueResult
    {
        bool returnValue;
        ::std::string propertyValue;
        ErrorInfo e;
    };

    virtual bool getPropertyValue(::std::string deviceID, ::std::string propertyName, ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertyValues.
     */
    struct GetPropertyValuesResult
    {
        bool returnValue;
        StringMap values;
        ErrorInfo e;
    };

    virtual bool getPropertyValues(::std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getRuntimePropertyValue.
     */
    struct GetRuntimePropertyValueResult
    {
        bool returnValue;
        ::std::string propertyValue;
        ErrorInfo e;
    };

    virtual bool getRuntimePropertyValue(::std::string deviceID, ::std::string propertyName, ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getRuntimePropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getRuntimePropertyValues.
     */
    struct GetRuntimePropertyValuesResult
    {
        bool returnValue;
        StringMap propertyValues;
        ErrorInfo e;
    };

    virtual bool getRuntimePropertyValues(::std::string deviceID, StringList listName, StringMap& propertyValues, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getRuntimePropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updatePropertyValue.
     */
    struct UpdatePropertyValueResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updatePropertyValue(::std::string deviceID, ::std::string propertyName, ::std::string propertyValue, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updatePropertyValues.
     */
    struct UpdatePropertyValuesResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updatePropertyValues(::std::string deviceID, StringMap values, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGDPDevicePropertyPrx : public virtual ::Ice::Proxy<ZGDPDevicePropertyPrx, ZGServerBasePrx>
{
public:

    bool getFieldsProperties(const ::std::string& deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::GetFieldsPropertiesResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_getFieldsProperties, deviceID, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getFieldsPropertiesAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::GetFieldsPropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::GetFieldsPropertiesResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_getFieldsProperties, deviceID, context);
    }

    ::std::function<void()>
    getFieldsPropertiesAsync(const ::std::string& deviceID,
                             ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::GetFieldsPropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::GetFieldsPropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_getFieldsProperties, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getFieldsProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetFieldsPropertiesResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getStaticProperties(const ::std::string& deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::GetStaticPropertiesResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_getStaticProperties, deviceID, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getStaticPropertiesAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::GetStaticPropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::GetStaticPropertiesResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_getStaticProperties, deviceID, context);
    }

    ::std::function<void()>
    getStaticPropertiesAsync(const ::std::string& deviceID,
                             ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::GetStaticPropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::GetStaticPropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_getStaticProperties, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getStaticProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetStaticPropertiesResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getDynamicProperties(const ::std::string& deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::GetDynamicPropertiesResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_getDynamicProperties, deviceID, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDynamicPropertiesAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::GetDynamicPropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::GetDynamicPropertiesResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_getDynamicProperties, deviceID, context);
    }

    ::std::function<void()>
    getDynamicPropertiesAsync(const ::std::string& deviceID,
                              ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                              ::std::function<void(::std::exception_ptr)> ex = nullptr,
                              ::std::function<void(bool)> sent = nullptr,
                              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::GetDynamicPropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::GetDynamicPropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_getDynamicProperties, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getDynamicProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetDynamicPropertiesResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getWholeProperties(const ::std::string& deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::GetWholePropertiesResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_getWholeProperties, deviceID, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getWholePropertiesAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::GetWholePropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::GetWholePropertiesResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_getWholeProperties, deviceID, context);
    }

    ::std::function<void()>
    getWholePropertiesAsync(const ::std::string& deviceID,
                            ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::GetWholePropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::GetWholePropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_getWholeProperties, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getWholeProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetWholePropertiesResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool mgetProperty(const StringList& listDeviceID, const ::std::string& propertyName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::MgetPropertyResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_mgetProperty, listDeviceID, propertyName, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto mgetPropertyAsync(const StringList& listDeviceID, const ::std::string& propertyName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::MgetPropertyResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::MgetPropertyResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_mgetProperty, listDeviceID, propertyName, context);
    }

    ::std::function<void()>
    mgetPropertyAsync(const StringList& listDeviceID, const ::std::string& propertyName,
                      ::std::function<void(bool, ::ZG6000::MapStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::MgetPropertyResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::MgetPropertyResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_mgetProperty, listDeviceID, propertyName, context);
    }

    /// \cond INTERNAL
    void _iceI_mgetProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::MgetPropertyResult>>&, const StringList&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getProperty(const ::std::string& deviceID, const ::std::string& propertyName, StringMap& property, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::GetPropertyResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_getProperty, deviceID, propertyName, context).get();
        property = ::std::move(_result.property);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertyAsync(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::GetPropertyResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::GetPropertyResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_getProperty, deviceID, propertyName, context);
    }

    ::std::function<void()>
    getPropertyAsync(const ::std::string& deviceID, const ::std::string& propertyName,
                     ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::GetPropertyResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.property), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::GetPropertyResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_getProperty, deviceID, propertyName, context);
    }

    /// \cond INTERNAL
    void _iceI_getProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetPropertyResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool mgetPropertyValues(const StringList& listDeviceID, const StringList& listName, MapStringMap& propertyValues, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::MgetPropertyValuesResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_mgetPropertyValues, listDeviceID, listName, context).get();
        propertyValues = ::std::move(_result.propertyValues);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto mgetPropertyValuesAsync(const StringList& listDeviceID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::MgetPropertyValuesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::MgetPropertyValuesResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_mgetPropertyValues, listDeviceID, listName, context);
    }

    ::std::function<void()>
    mgetPropertyValuesAsync(const StringList& listDeviceID, const StringList& listName,
                            ::std::function<void(bool, ::ZG6000::MapStringMap, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::MgetPropertyValuesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.propertyValues), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::MgetPropertyValuesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_mgetPropertyValues, listDeviceID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_mgetPropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::MgetPropertyValuesResult>>&, const StringList&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool mgetPropertyValue(const StringList& listDeviceID, const ::std::string& propertyName, StringMap& propertyValues, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::MgetPropertyValueResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_mgetPropertyValue, listDeviceID, propertyName, context).get();
        propertyValues = ::std::move(_result.propertyValues);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto mgetPropertyValueAsync(const StringList& listDeviceID, const ::std::string& propertyName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::MgetPropertyValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::MgetPropertyValueResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_mgetPropertyValue, listDeviceID, propertyName, context);
    }

    ::std::function<void()>
    mgetPropertyValueAsync(const StringList& listDeviceID, const ::std::string& propertyName,
                           ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::MgetPropertyValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.propertyValues), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::MgetPropertyValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_mgetPropertyValue, listDeviceID, propertyName, context);
    }

    /// \cond INTERNAL
    void _iceI_mgetPropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::MgetPropertyValueResult>>&, const StringList&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getPropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::GetPropertyValueResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_getPropertyValue, deviceID, propertyName, context).get();
        propertyValue = ::std::move(_result.propertyValue);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertyValueAsync(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::GetPropertyValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::GetPropertyValueResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_getPropertyValue, deviceID, propertyName, context);
    }

    ::std::function<void()>
    getPropertyValueAsync(const ::std::string& deviceID, const ::std::string& propertyName,
                          ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::GetPropertyValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.propertyValue), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::GetPropertyValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_getPropertyValue, deviceID, propertyName, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetPropertyValueResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getPropertyValues(const ::std::string& deviceID, const StringList& listName, StringMap& values, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::GetPropertyValuesResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_getPropertyValues, deviceID, listName, context).get();
        values = ::std::move(_result.values);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertyValuesAsync(const ::std::string& deviceID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::GetPropertyValuesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::GetPropertyValuesResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_getPropertyValues, deviceID, listName, context);
    }

    ::std::function<void()>
    getPropertyValuesAsync(const ::std::string& deviceID, const StringList& listName,
                           ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::GetPropertyValuesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.values), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::GetPropertyValuesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_getPropertyValues, deviceID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetPropertyValuesResult>>&, const ::std::string&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool getRuntimePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::GetRuntimePropertyValueResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_getRuntimePropertyValue, deviceID, propertyName, context).get();
        propertyValue = ::std::move(_result.propertyValue);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getRuntimePropertyValueAsync(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::GetRuntimePropertyValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::GetRuntimePropertyValueResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_getRuntimePropertyValue, deviceID, propertyName, context);
    }

    ::std::function<void()>
    getRuntimePropertyValueAsync(const ::std::string& deviceID, const ::std::string& propertyName,
                                 ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                                 ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                 ::std::function<void(bool)> sent = nullptr,
                                 const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::GetRuntimePropertyValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.propertyValue), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::GetRuntimePropertyValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_getRuntimePropertyValue, deviceID, propertyName, context);
    }

    /// \cond INTERNAL
    void _iceI_getRuntimePropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetRuntimePropertyValueResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getRuntimePropertyValues(const ::std::string& deviceID, const StringList& listName, StringMap& propertyValues, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::GetRuntimePropertyValuesResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_getRuntimePropertyValues, deviceID, listName, context).get();
        propertyValues = ::std::move(_result.propertyValues);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getRuntimePropertyValuesAsync(const ::std::string& deviceID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::GetRuntimePropertyValuesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::GetRuntimePropertyValuesResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_getRuntimePropertyValues, deviceID, listName, context);
    }

    ::std::function<void()>
    getRuntimePropertyValuesAsync(const ::std::string& deviceID, const StringList& listName,
                                  ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                  ::std::function<void(bool)> sent = nullptr,
                                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::GetRuntimePropertyValuesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.propertyValues), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::GetRuntimePropertyValuesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_getRuntimePropertyValues, deviceID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_getRuntimePropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetRuntimePropertyValuesResult>>&, const ::std::string&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool updatePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::UpdatePropertyValueResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_updatePropertyValue, deviceID, propertyName, propertyValue, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updatePropertyValueAsync(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::UpdatePropertyValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::UpdatePropertyValueResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_updatePropertyValue, deviceID, propertyName, propertyValue, context);
    }

    ::std::function<void()>
    updatePropertyValueAsync(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue,
                             ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::UpdatePropertyValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::UpdatePropertyValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_updatePropertyValue, deviceID, propertyName, propertyValue, context);
    }

    /// \cond INTERNAL
    void _iceI_updatePropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::UpdatePropertyValueResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool updatePropertyValues(const ::std::string& deviceID, const StringMap& values, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceProperty::UpdatePropertyValuesResult>(true, this, &ZGDPDevicePropertyPrx::_iceI_updatePropertyValues, deviceID, values, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updatePropertyValuesAsync(const ::std::string& deviceID, const StringMap& values, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceProperty::UpdatePropertyValuesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceProperty::UpdatePropertyValuesResult, P>(false, this, &ZGDPDevicePropertyPrx::_iceI_updatePropertyValues, deviceID, values, context);
    }

    ::std::function<void()>
    updatePropertyValuesAsync(const ::std::string& deviceID, const StringMap& values,
                              ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                              ::std::function<void(::std::exception_ptr)> ex = nullptr,
                              ::std::function<void(bool)> sent = nullptr,
                              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceProperty::UpdatePropertyValuesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceProperty::UpdatePropertyValuesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDevicePropertyPrx::_iceI_updatePropertyValues, deviceID, values, context);
    }

    /// \cond INTERNAL
    void _iceI_updatePropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::UpdatePropertyValuesResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGDPDevicePropertyPrx() = default;
    friend ::std::shared_ptr<ZGDPDevicePropertyPrx> IceInternal::createProxy<ZGDPDevicePropertyPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGDPDevicePropertyPtr = ::std::shared_ptr<ZGDPDeviceProperty>;
using ZGDPDevicePropertyPrxPtr = ::std::shared_ptr<ZGDPDevicePropertyPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGDPDeviceProperty;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGDPDeviceProperty>&);
::IceProxy::Ice::Object* upCast(ZGDPDeviceProperty*);
/// \endcond

}

}

namespace ZG6000
{

class ZGDPDeviceProperty;
/// \cond INTERNAL
::Ice::Object* upCast(ZGDPDeviceProperty*);
/// \endcond
typedef ::IceInternal::Handle< ZGDPDeviceProperty> ZGDPDevicePropertyPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGDPDeviceProperty> ZGDPDevicePropertyPrx;
typedef ZGDPDevicePropertyPrx ZGDPDevicePropertyPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGDPDevicePropertyPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getFieldsProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getFieldsProperties.
 */
class Callback_ZGDPDeviceProperty_getFieldsProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_getFieldsProperties_Base> Callback_ZGDPDeviceProperty_getFieldsPropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getStaticProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getStaticProperties.
 */
class Callback_ZGDPDeviceProperty_getStaticProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_getStaticProperties_Base> Callback_ZGDPDeviceProperty_getStaticPropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getDynamicProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getDynamicProperties.
 */
class Callback_ZGDPDeviceProperty_getDynamicProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_getDynamicProperties_Base> Callback_ZGDPDeviceProperty_getDynamicPropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getWholeProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getWholeProperties.
 */
class Callback_ZGDPDeviceProperty_getWholeProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_getWholeProperties_Base> Callback_ZGDPDeviceProperty_getWholePropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_mgetProperty.
 */
class Callback_ZGDPDeviceProperty_mgetProperty_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_mgetProperty_Base> Callback_ZGDPDeviceProperty_mgetPropertyPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getProperty.
 */
class Callback_ZGDPDeviceProperty_getProperty_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_getProperty_Base> Callback_ZGDPDeviceProperty_getPropertyPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_mgetPropertyValues.
 */
class Callback_ZGDPDeviceProperty_mgetPropertyValues_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_mgetPropertyValues_Base> Callback_ZGDPDeviceProperty_mgetPropertyValuesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_mgetPropertyValue.
 */
class Callback_ZGDPDeviceProperty_mgetPropertyValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_mgetPropertyValue_Base> Callback_ZGDPDeviceProperty_mgetPropertyValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getPropertyValue.
 */
class Callback_ZGDPDeviceProperty_getPropertyValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_getPropertyValue_Base> Callback_ZGDPDeviceProperty_getPropertyValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getPropertyValues.
 */
class Callback_ZGDPDeviceProperty_getPropertyValues_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_getPropertyValues_Base> Callback_ZGDPDeviceProperty_getPropertyValuesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getRuntimePropertyValue.
 */
class Callback_ZGDPDeviceProperty_getRuntimePropertyValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_getRuntimePropertyValue_Base> Callback_ZGDPDeviceProperty_getRuntimePropertyValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getRuntimePropertyValues.
 */
class Callback_ZGDPDeviceProperty_getRuntimePropertyValues_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_getRuntimePropertyValues_Base> Callback_ZGDPDeviceProperty_getRuntimePropertyValuesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_updatePropertyValue.
 */
class Callback_ZGDPDeviceProperty_updatePropertyValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_updatePropertyValue_Base> Callback_ZGDPDeviceProperty_updatePropertyValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_updatePropertyValues.
 */
class Callback_ZGDPDeviceProperty_updatePropertyValues_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceProperty_updatePropertyValues_Base> Callback_ZGDPDeviceProperty_updatePropertyValuesPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGDPDeviceProperty : public virtual ::Ice::Proxy<ZGDPDeviceProperty, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool getFieldsProperties(const ::std::string& deviceID, ::ZG6000::ListStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getFieldsProperties(properties, e, _iceI_begin_getFieldsProperties(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getFieldsProperties(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getFieldsProperties(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getFieldsProperties(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getFieldsProperties(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getFieldsProperties(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getFieldsProperties(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getFieldsProperties(const ::std::string& deviceID, const ::ZG6000::Callback_ZGDPDeviceProperty_getFieldsPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getFieldsProperties(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getFieldsProperties(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_getFieldsPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getFieldsProperties(deviceID, context, cb, cookie);
    }

    bool end_getFieldsProperties(::ZG6000::ListStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getFieldsProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getFieldsProperties(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getStaticProperties(const ::std::string& deviceID, ::ZG6000::ListStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getStaticProperties(properties, e, _iceI_begin_getStaticProperties(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getStaticProperties(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getStaticProperties(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getStaticProperties(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getStaticProperties(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getStaticProperties(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getStaticProperties(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getStaticProperties(const ::std::string& deviceID, const ::ZG6000::Callback_ZGDPDeviceProperty_getStaticPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getStaticProperties(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getStaticProperties(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_getStaticPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getStaticProperties(deviceID, context, cb, cookie);
    }

    bool end_getStaticProperties(::ZG6000::ListStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getStaticProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getStaticProperties(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDynamicProperties(const ::std::string& deviceID, ::ZG6000::ListStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDynamicProperties(properties, e, _iceI_begin_getDynamicProperties(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDynamicProperties(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDynamicProperties(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDynamicProperties(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDynamicProperties(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDynamicProperties(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDynamicProperties(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDynamicProperties(const ::std::string& deviceID, const ::ZG6000::Callback_ZGDPDeviceProperty_getDynamicPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDynamicProperties(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDynamicProperties(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_getDynamicPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDynamicProperties(deviceID, context, cb, cookie);
    }

    bool end_getDynamicProperties(::ZG6000::ListStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDynamicProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDynamicProperties(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getWholeProperties(const ::std::string& deviceID, ::ZG6000::ListStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getWholeProperties(properties, e, _iceI_begin_getWholeProperties(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getWholeProperties(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getWholeProperties(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getWholeProperties(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWholeProperties(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getWholeProperties(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWholeProperties(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getWholeProperties(const ::std::string& deviceID, const ::ZG6000::Callback_ZGDPDeviceProperty_getWholePropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWholeProperties(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getWholeProperties(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_getWholePropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWholeProperties(deviceID, context, cb, cookie);
    }

    bool end_getWholeProperties(::ZG6000::ListStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getWholeProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getWholeProperties(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool mgetProperty(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, ::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_mgetProperty(properties, e, _iceI_begin_mgetProperty(listDeviceID, propertyName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_mgetProperty(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_mgetProperty(listDeviceID, propertyName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_mgetProperty(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetProperty(listDeviceID, propertyName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetProperty(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetProperty(listDeviceID, propertyName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetProperty(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, const ::ZG6000::Callback_ZGDPDeviceProperty_mgetPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetProperty(listDeviceID, propertyName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetProperty(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_mgetPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetProperty(listDeviceID, propertyName, context, cb, cookie);
    }

    bool end_mgetProperty(::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_mgetProperty(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_mgetProperty(const ::ZG6000::StringList&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getProperty(const ::std::string& deviceID, const ::std::string& propertyName, ::ZG6000::StringMap& property, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getProperty(property, e, _iceI_begin_getProperty(deviceID, propertyName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getProperty(deviceID, propertyName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(deviceID, propertyName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(deviceID, propertyName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& deviceID, const ::std::string& propertyName, const ::ZG6000::Callback_ZGDPDeviceProperty_getPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(deviceID, propertyName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_getPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(deviceID, propertyName, context, cb, cookie);
    }

    bool end_getProperty(::ZG6000::StringMap& property, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getProperty(::ZG6000::StringMap& iceP_property, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getProperty(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, ::ZG6000::MapStringMap& propertyValues, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_mgetPropertyValues(propertyValues, e, _iceI_begin_mgetPropertyValues(listDeviceID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_mgetPropertyValues(listDeviceID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValues(listDeviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValues(listDeviceID, listName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGDPDeviceProperty_mgetPropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValues(listDeviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_mgetPropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValues(listDeviceID, listName, context, cb, cookie);
    }

    bool end_mgetPropertyValues(::ZG6000::MapStringMap& propertyValues, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_mgetPropertyValues(::ZG6000::MapStringMap& iceP_propertyValues, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_mgetPropertyValues(const ::ZG6000::StringList&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool mgetPropertyValue(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, ::ZG6000::StringMap& propertyValues, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_mgetPropertyValue(propertyValues, e, _iceI_begin_mgetPropertyValue(listDeviceID, propertyName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValue(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_mgetPropertyValue(listDeviceID, propertyName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValue(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValue(listDeviceID, propertyName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValue(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValue(listDeviceID, propertyName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValue(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, const ::ZG6000::Callback_ZGDPDeviceProperty_mgetPropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValue(listDeviceID, propertyName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValue(const ::ZG6000::StringList& listDeviceID, const ::std::string& propertyName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_mgetPropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValue(listDeviceID, propertyName, context, cb, cookie);
    }

    bool end_mgetPropertyValue(::ZG6000::StringMap& propertyValues, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_mgetPropertyValue(::ZG6000::StringMap& iceP_propertyValues, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_mgetPropertyValue(const ::ZG6000::StringList&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, ::std::string& propertyValue, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertyValue(propertyValue, e, _iceI_begin_getPropertyValue(deviceID, propertyName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertyValue(deviceID, propertyName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(deviceID, propertyName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(deviceID, propertyName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::ZG6000::Callback_ZGDPDeviceProperty_getPropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(deviceID, propertyName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_getPropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(deviceID, propertyName, context, cb, cookie);
    }

    bool end_getPropertyValue(::std::string& propertyValue, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertyValue(::std::string& iceP_propertyValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertyValue(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, ::ZG6000::StringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertyValues(values, e, _iceI_begin_getPropertyValues(deviceID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertyValues(deviceID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(deviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(deviceID, listName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGDPDeviceProperty_getPropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(deviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_getPropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(deviceID, listName, context, cb, cookie);
    }

    bool end_getPropertyValues(::ZG6000::StringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertyValues(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertyValues(const ::std::string&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getRuntimePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, ::std::string& propertyValue, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getRuntimePropertyValue(propertyValue, e, _iceI_begin_getRuntimePropertyValue(deviceID, propertyName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getRuntimePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getRuntimePropertyValue(deviceID, propertyName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getRuntimePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRuntimePropertyValue(deviceID, propertyName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getRuntimePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRuntimePropertyValue(deviceID, propertyName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getRuntimePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::ZG6000::Callback_ZGDPDeviceProperty_getRuntimePropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRuntimePropertyValue(deviceID, propertyName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getRuntimePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_getRuntimePropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRuntimePropertyValue(deviceID, propertyName, context, cb, cookie);
    }

    bool end_getRuntimePropertyValue(::std::string& propertyValue, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getRuntimePropertyValue(::std::string& iceP_propertyValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getRuntimePropertyValue(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getRuntimePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, ::ZG6000::StringMap& propertyValues, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getRuntimePropertyValues(propertyValues, e, _iceI_begin_getRuntimePropertyValues(deviceID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getRuntimePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getRuntimePropertyValues(deviceID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getRuntimePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRuntimePropertyValues(deviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getRuntimePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRuntimePropertyValues(deviceID, listName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getRuntimePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGDPDeviceProperty_getRuntimePropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRuntimePropertyValues(deviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getRuntimePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_getRuntimePropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRuntimePropertyValues(deviceID, listName, context, cb, cookie);
    }

    bool end_getRuntimePropertyValues(::ZG6000::StringMap& propertyValues, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getRuntimePropertyValues(::ZG6000::StringMap& iceP_propertyValues, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getRuntimePropertyValues(const ::std::string&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updatePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updatePropertyValue(e, _iceI_begin_updatePropertyValue(deviceID, propertyName, propertyValue, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updatePropertyValue(deviceID, propertyName, propertyValue, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(deviceID, propertyName, propertyValue, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(deviceID, propertyName, propertyValue, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::ZG6000::Callback_ZGDPDeviceProperty_updatePropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(deviceID, propertyName, propertyValue, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_updatePropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(deviceID, propertyName, propertyValue, context, cb, cookie);
    }

    bool end_updatePropertyValue(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updatePropertyValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updatePropertyValue(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updatePropertyValues(e, _iceI_begin_updatePropertyValues(deviceID, values, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updatePropertyValues(deviceID, values, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(deviceID, values, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(deviceID, values, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, const ::ZG6000::Callback_ZGDPDeviceProperty_updatePropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(deviceID, values, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceProperty_updatePropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(deviceID, values, context, cb, cookie);
    }

    bool end_updatePropertyValues(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updatePropertyValues(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updatePropertyValues(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGDPDeviceProperty : virtual public ZGServerBase
{
public:

    typedef ZGDPDevicePropertyPrx ProxyType;
    typedef ZGDPDevicePropertyPtr PointerType;

    virtual ~ZGDPDeviceProperty();

#ifdef ICE_CPP11_COMPILER
    ZGDPDeviceProperty() = default;
    ZGDPDeviceProperty(const ZGDPDeviceProperty&) = default;
    ZGDPDeviceProperty& operator=(const ZGDPDeviceProperty&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool getFieldsProperties(const ::std::string& deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getFieldsProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getStaticProperties(const ::std::string& deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getStaticProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDynamicProperties(const ::std::string& deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDynamicProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getWholeProperties(const ::std::string& deviceID, ListStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getWholeProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool mgetProperty(const StringList& listDeviceID, const ::std::string& propertyName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getProperty(const ::std::string& deviceID, const ::std::string& propertyName, StringMap& property, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool mgetPropertyValues(const StringList& listDeviceID, const StringList& listName, MapStringMap& propertyValues, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetPropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool mgetPropertyValue(const StringList& listDeviceID, const ::std::string& propertyName, StringMap& propertyValues, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetPropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPropertyValues(const ::std::string& deviceID, const StringList& listName, StringMap& values, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getRuntimePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getRuntimePropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getRuntimePropertyValues(const ::std::string& deviceID, const StringList& listName, StringMap& propertyValues, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getRuntimePropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updatePropertyValue(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updatePropertyValues(const ::std::string& deviceID, const StringMap& values, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGDPDeviceProperty& lhs, const ZGDPDeviceProperty& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGDPDeviceProperty& lhs, const ZGDPDeviceProperty& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getFieldsProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getFieldsProperties.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_getFieldsProperties : public Callback_ZGDPDeviceProperty_getFieldsProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_getFieldsProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getFieldsProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getFieldsProperties.
 */
template<class T> Callback_ZGDPDeviceProperty_getFieldsPropertiesPtr
newCallback_ZGDPDeviceProperty_getFieldsProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getFieldsProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getFieldsProperties.
 */
template<class T> Callback_ZGDPDeviceProperty_getFieldsPropertiesPtr
newCallback_ZGDPDeviceProperty_getFieldsProperties(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getFieldsProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getFieldsProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getFieldsProperties.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_getFieldsProperties : public Callback_ZGDPDeviceProperty_getFieldsProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_getFieldsProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getFieldsProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getFieldsProperties.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getFieldsPropertiesPtr
newCallback_ZGDPDeviceProperty_getFieldsProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getFieldsProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getFieldsProperties.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getFieldsPropertiesPtr
newCallback_ZGDPDeviceProperty_getFieldsProperties(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getFieldsProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getStaticProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getStaticProperties.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_getStaticProperties : public Callback_ZGDPDeviceProperty_getStaticProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_getStaticProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getStaticProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getStaticProperties.
 */
template<class T> Callback_ZGDPDeviceProperty_getStaticPropertiesPtr
newCallback_ZGDPDeviceProperty_getStaticProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getStaticProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getStaticProperties.
 */
template<class T> Callback_ZGDPDeviceProperty_getStaticPropertiesPtr
newCallback_ZGDPDeviceProperty_getStaticProperties(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getStaticProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getStaticProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getStaticProperties.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_getStaticProperties : public Callback_ZGDPDeviceProperty_getStaticProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_getStaticProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getStaticProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getStaticProperties.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getStaticPropertiesPtr
newCallback_ZGDPDeviceProperty_getStaticProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getStaticProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getStaticProperties.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getStaticPropertiesPtr
newCallback_ZGDPDeviceProperty_getStaticProperties(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getStaticProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getDynamicProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getDynamicProperties.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_getDynamicProperties : public Callback_ZGDPDeviceProperty_getDynamicProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_getDynamicProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDynamicProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getDynamicProperties.
 */
template<class T> Callback_ZGDPDeviceProperty_getDynamicPropertiesPtr
newCallback_ZGDPDeviceProperty_getDynamicProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getDynamicProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getDynamicProperties.
 */
template<class T> Callback_ZGDPDeviceProperty_getDynamicPropertiesPtr
newCallback_ZGDPDeviceProperty_getDynamicProperties(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getDynamicProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getDynamicProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getDynamicProperties.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_getDynamicProperties : public Callback_ZGDPDeviceProperty_getDynamicProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_getDynamicProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDynamicProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getDynamicProperties.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getDynamicPropertiesPtr
newCallback_ZGDPDeviceProperty_getDynamicProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getDynamicProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getDynamicProperties.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getDynamicPropertiesPtr
newCallback_ZGDPDeviceProperty_getDynamicProperties(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getDynamicProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getWholeProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getWholeProperties.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_getWholeProperties : public Callback_ZGDPDeviceProperty_getWholeProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_getWholeProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getWholeProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getWholeProperties.
 */
template<class T> Callback_ZGDPDeviceProperty_getWholePropertiesPtr
newCallback_ZGDPDeviceProperty_getWholeProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getWholeProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getWholeProperties.
 */
template<class T> Callback_ZGDPDeviceProperty_getWholePropertiesPtr
newCallback_ZGDPDeviceProperty_getWholeProperties(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getWholeProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getWholeProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getWholeProperties.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_getWholeProperties : public Callback_ZGDPDeviceProperty_getWholeProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_getWholeProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getWholeProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getWholeProperties.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getWholePropertiesPtr
newCallback_ZGDPDeviceProperty_getWholeProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getWholeProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getWholeProperties.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getWholePropertiesPtr
newCallback_ZGDPDeviceProperty_getWholeProperties(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getWholeProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_mgetProperty.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_mgetProperty : public Callback_ZGDPDeviceProperty_mgetProperty_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_mgetProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetProperty(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetProperty.
 */
template<class T> Callback_ZGDPDeviceProperty_mgetPropertyPtr
newCallback_ZGDPDeviceProperty_mgetProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_mgetProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetProperty.
 */
template<class T> Callback_ZGDPDeviceProperty_mgetPropertyPtr
newCallback_ZGDPDeviceProperty_mgetProperty(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_mgetProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_mgetProperty.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_mgetProperty : public Callback_ZGDPDeviceProperty_mgetProperty_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_mgetProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetProperty(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetProperty.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_mgetPropertyPtr
newCallback_ZGDPDeviceProperty_mgetProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_mgetProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetProperty.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_mgetPropertyPtr
newCallback_ZGDPDeviceProperty_mgetProperty(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_mgetProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getProperty.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_getProperty : public Callback_ZGDPDeviceProperty_getProperty_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_getProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_property;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getProperty(iceP_property, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_property, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getProperty.
 */
template<class T> Callback_ZGDPDeviceProperty_getPropertyPtr
newCallback_ZGDPDeviceProperty_getProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getProperty.
 */
template<class T> Callback_ZGDPDeviceProperty_getPropertyPtr
newCallback_ZGDPDeviceProperty_getProperty(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getProperty.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_getProperty : public Callback_ZGDPDeviceProperty_getProperty_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_getProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_property;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getProperty(iceP_property, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_property, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getProperty.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getPropertyPtr
newCallback_ZGDPDeviceProperty_getProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getProperty.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getPropertyPtr
newCallback_ZGDPDeviceProperty_getProperty(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_mgetPropertyValues.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_mgetPropertyValues : public Callback_ZGDPDeviceProperty_mgetPropertyValues_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_mgetPropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_propertyValues;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetPropertyValues(iceP_propertyValues, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_propertyValues, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValues.
 */
template<class T> Callback_ZGDPDeviceProperty_mgetPropertyValuesPtr
newCallback_ZGDPDeviceProperty_mgetPropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_mgetPropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValues.
 */
template<class T> Callback_ZGDPDeviceProperty_mgetPropertyValuesPtr
newCallback_ZGDPDeviceProperty_mgetPropertyValues(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_mgetPropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_mgetPropertyValues.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_mgetPropertyValues : public Callback_ZGDPDeviceProperty_mgetPropertyValues_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_mgetPropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_propertyValues;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetPropertyValues(iceP_propertyValues, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_propertyValues, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValues.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_mgetPropertyValuesPtr
newCallback_ZGDPDeviceProperty_mgetPropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_mgetPropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValues.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_mgetPropertyValuesPtr
newCallback_ZGDPDeviceProperty_mgetPropertyValues(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_mgetPropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_mgetPropertyValue.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_mgetPropertyValue : public Callback_ZGDPDeviceProperty_mgetPropertyValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_mgetPropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_propertyValues;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetPropertyValue(iceP_propertyValues, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_propertyValues, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValue.
 */
template<class T> Callback_ZGDPDeviceProperty_mgetPropertyValuePtr
newCallback_ZGDPDeviceProperty_mgetPropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_mgetPropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValue.
 */
template<class T> Callback_ZGDPDeviceProperty_mgetPropertyValuePtr
newCallback_ZGDPDeviceProperty_mgetPropertyValue(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_mgetPropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_mgetPropertyValue.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_mgetPropertyValue : public Callback_ZGDPDeviceProperty_mgetPropertyValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_mgetPropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_propertyValues;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetPropertyValue(iceP_propertyValues, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_propertyValues, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValue.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_mgetPropertyValuePtr
newCallback_ZGDPDeviceProperty_mgetPropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_mgetPropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_mgetPropertyValue.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_mgetPropertyValuePtr
newCallback_ZGDPDeviceProperty_mgetPropertyValue(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_mgetPropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getPropertyValue.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_getPropertyValue : public Callback_ZGDPDeviceProperty_getPropertyValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_getPropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_propertyValue;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValue(iceP_propertyValue, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_propertyValue, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValue.
 */
template<class T> Callback_ZGDPDeviceProperty_getPropertyValuePtr
newCallback_ZGDPDeviceProperty_getPropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getPropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValue.
 */
template<class T> Callback_ZGDPDeviceProperty_getPropertyValuePtr
newCallback_ZGDPDeviceProperty_getPropertyValue(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getPropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getPropertyValue.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_getPropertyValue : public Callback_ZGDPDeviceProperty_getPropertyValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_getPropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_propertyValue;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValue(iceP_propertyValue, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_propertyValue, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValue.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getPropertyValuePtr
newCallback_ZGDPDeviceProperty_getPropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getPropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValue.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getPropertyValuePtr
newCallback_ZGDPDeviceProperty_getPropertyValue(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getPropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getPropertyValues.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_getPropertyValues : public Callback_ZGDPDeviceProperty_getPropertyValues_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_getPropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValues(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_values, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValues.
 */
template<class T> Callback_ZGDPDeviceProperty_getPropertyValuesPtr
newCallback_ZGDPDeviceProperty_getPropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getPropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValues.
 */
template<class T> Callback_ZGDPDeviceProperty_getPropertyValuesPtr
newCallback_ZGDPDeviceProperty_getPropertyValues(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getPropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getPropertyValues.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_getPropertyValues : public Callback_ZGDPDeviceProperty_getPropertyValues_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_getPropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValues(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_values, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValues.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getPropertyValuesPtr
newCallback_ZGDPDeviceProperty_getPropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getPropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getPropertyValues.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getPropertyValuesPtr
newCallback_ZGDPDeviceProperty_getPropertyValues(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getPropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getRuntimePropertyValue.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_getRuntimePropertyValue : public Callback_ZGDPDeviceProperty_getRuntimePropertyValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_getRuntimePropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_propertyValue;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRuntimePropertyValue(iceP_propertyValue, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_propertyValue, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValue.
 */
template<class T> Callback_ZGDPDeviceProperty_getRuntimePropertyValuePtr
newCallback_ZGDPDeviceProperty_getRuntimePropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getRuntimePropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValue.
 */
template<class T> Callback_ZGDPDeviceProperty_getRuntimePropertyValuePtr
newCallback_ZGDPDeviceProperty_getRuntimePropertyValue(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getRuntimePropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getRuntimePropertyValue.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_getRuntimePropertyValue : public Callback_ZGDPDeviceProperty_getRuntimePropertyValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_getRuntimePropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_propertyValue;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRuntimePropertyValue(iceP_propertyValue, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_propertyValue, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValue.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getRuntimePropertyValuePtr
newCallback_ZGDPDeviceProperty_getRuntimePropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getRuntimePropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValue.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getRuntimePropertyValuePtr
newCallback_ZGDPDeviceProperty_getRuntimePropertyValue(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getRuntimePropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getRuntimePropertyValues.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_getRuntimePropertyValues : public Callback_ZGDPDeviceProperty_getRuntimePropertyValues_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_getRuntimePropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_propertyValues;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRuntimePropertyValues(iceP_propertyValues, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_propertyValues, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValues.
 */
template<class T> Callback_ZGDPDeviceProperty_getRuntimePropertyValuesPtr
newCallback_ZGDPDeviceProperty_getRuntimePropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getRuntimePropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValues.
 */
template<class T> Callback_ZGDPDeviceProperty_getRuntimePropertyValuesPtr
newCallback_ZGDPDeviceProperty_getRuntimePropertyValues(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_getRuntimePropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_getRuntimePropertyValues.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_getRuntimePropertyValues : public Callback_ZGDPDeviceProperty_getRuntimePropertyValues_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_getRuntimePropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_propertyValues;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRuntimePropertyValues(iceP_propertyValues, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_propertyValues, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValues.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getRuntimePropertyValuesPtr
newCallback_ZGDPDeviceProperty_getRuntimePropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getRuntimePropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_getRuntimePropertyValues.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_getRuntimePropertyValuesPtr
newCallback_ZGDPDeviceProperty_getRuntimePropertyValues(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_getRuntimePropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_updatePropertyValue.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_updatePropertyValue : public Callback_ZGDPDeviceProperty_updatePropertyValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_updatePropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValue.
 */
template<class T> Callback_ZGDPDeviceProperty_updatePropertyValuePtr
newCallback_ZGDPDeviceProperty_updatePropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_updatePropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValue.
 */
template<class T> Callback_ZGDPDeviceProperty_updatePropertyValuePtr
newCallback_ZGDPDeviceProperty_updatePropertyValue(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_updatePropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_updatePropertyValue.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_updatePropertyValue : public Callback_ZGDPDeviceProperty_updatePropertyValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_updatePropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValue.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_updatePropertyValuePtr
newCallback_ZGDPDeviceProperty_updatePropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_updatePropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValue.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_updatePropertyValuePtr
newCallback_ZGDPDeviceProperty_updatePropertyValue(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_updatePropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_updatePropertyValues.
 */
template<class T>
class CallbackNC_ZGDPDeviceProperty_updatePropertyValues : public Callback_ZGDPDeviceProperty_updatePropertyValues_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGDPDeviceProperty_updatePropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValues(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValues.
 */
template<class T> Callback_ZGDPDeviceProperty_updatePropertyValuesPtr
newCallback_ZGDPDeviceProperty_updatePropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_updatePropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValues.
 */
template<class T> Callback_ZGDPDeviceProperty_updatePropertyValuesPtr
newCallback_ZGDPDeviceProperty_updatePropertyValues(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceProperty_updatePropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceProperty_updatePropertyValues.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceProperty_updatePropertyValues : public Callback_ZGDPDeviceProperty_updatePropertyValues_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceProperty_updatePropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDevicePropertyPrx proxy = ZGDPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValues(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValues.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_updatePropertyValuesPtr
newCallback_ZGDPDeviceProperty_updatePropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_updatePropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceProperty::begin_updatePropertyValues.
 */
template<class T, typename CT> Callback_ZGDPDeviceProperty_updatePropertyValuesPtr
newCallback_ZGDPDeviceProperty_updatePropertyValues(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceProperty_updatePropertyValues<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

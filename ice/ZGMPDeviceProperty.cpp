//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPDeviceProperty.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGMPDeviceProperty.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPDeviceProperty",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getDataIDByProperty",
    "getGroupProperties",
    "getProperties",
    "getPropertiesAll",
    "getProperty",
    "getPropertyByDataID",
    "getPropertyValue",
    "getPropertyValueEx",
    "getPropertyValues",
    "getPropertyValuesEx",
    "getTableProperties",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isAllowCtrl",
    "isDebugging",
    "isPropertyExists",
    "mgetProperties",
    "mgetPropertiesAll",
    "mgetPropertyValues",
    "mgetPropertyValuesEx",
    "mupdateProperties",
    "mupdatePropertyValues",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test",
    "updateProperties",
    "updateProperty",
    "updatePropertyValue",
    "updatePropertyValueEx",
    "updatePropertyValues",
    "updatePropertyValuesEx"
};
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_isAllowCtrl_name = "isAllowCtrl";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mgetPropertiesAll_name = "mgetPropertiesAll";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mgetProperties_name = "mgetProperties";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValues_name = "mgetPropertyValues";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValuesEx_name = "mgetPropertyValuesEx";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getGroupProperties_name = "getGroupProperties";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getTableProperties_name = "getTableProperties";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertiesAll_name = "getPropertiesAll";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getProperties_name = "getProperties";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getProperty_name = "getProperty";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertyValues_name = "getPropertyValues";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertyValuesEx_name = "getPropertyValuesEx";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertyValue_name = "getPropertyValue";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertyValueEx_name = "getPropertyValueEx";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mupdateProperties_name = "mupdateProperties";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mupdatePropertyValues_name = "mupdatePropertyValues";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updateProperty_name = "updateProperty";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updateProperties_name = "updateProperties";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValues_name = "updatePropertyValues";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValuesEx_name = "updatePropertyValuesEx";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValue_name = "updatePropertyValue";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValueEx_name = "updatePropertyValueEx";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getDataIDByProperty_name = "getDataIDByProperty";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertyByDataID_name = "getPropertyByDataID";
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_isPropertyExists_name = "isPropertyExists";

}

bool
ZG6000::ZGMPDeviceProperty::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPDeviceProperty_ids, iceC_ZG6000_ZGMPDeviceProperty_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGMPDeviceProperty::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGMPDeviceProperty_ids[0], &iceC_ZG6000_ZGMPDeviceProperty_ids[3]);
}

::std::string
ZG6000::ZGMPDeviceProperty::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPDeviceProperty::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGMPDeviceProperty";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_isAllowCtrl(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    bool iceP_allow;
    StringMap iceP_conditions;
    ErrorInfo iceP_e;
    bool ret = this->isAllowCtrl(::std::move(iceP_deviceID), iceP_allow, iceP_conditions, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_allow, iceP_conditions, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mgetPropertiesAll(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    istr->readAll(iceP_listDeviceID);
    inS.endReadParams();
    MapMapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->mgetPropertiesAll(::std::move(iceP_listDeviceID), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mgetProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    StringList iceP_listName;
    istr->readAll(iceP_listDeviceID, iceP_listName);
    inS.endReadParams();
    MapMapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->mgetProperties(::std::move(iceP_listDeviceID), ::std::move(iceP_listName), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mgetPropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    StringList iceP_listName;
    istr->readAll(iceP_listDeviceID, iceP_listName);
    inS.endReadParams();
    MapStringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->mgetPropertyValues(::std::move(iceP_listDeviceID), ::std::move(iceP_listName), iceP_values, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_values, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mgetPropertyValuesEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    StringList iceP_listName;
    istr->readAll(iceP_listDeviceID, iceP_listName);
    inS.endReadParams();
    MapStringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->mgetPropertyValuesEx(::std::move(iceP_listDeviceID), ::std::move(iceP_listName), iceP_values, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_values, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getGroupProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    MapMapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getGroupProperties(::std::move(iceP_deviceID), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getTableProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_tableName;
    istr->readAll(iceP_deviceID, iceP_tableName);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getTableProperties(::std::move(iceP_deviceID), ::std::move(iceP_tableName), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertiesAll(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getPropertiesAll(::std::move(iceP_deviceID), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringList iceP_listName;
    istr->readAll(iceP_deviceID, iceP_listName);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getProperties(::std::move(iceP_deviceID), ::std::move(iceP_listName), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    istr->readAll(iceP_deviceID, iceP_name);
    inS.endReadParams();
    StringMap iceP_property;
    ErrorInfo iceP_e;
    bool ret = this->getProperty(::std::move(iceP_deviceID), ::std::move(iceP_name), iceP_property, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_property, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringList iceP_listName;
    istr->readAll(iceP_deviceID, iceP_listName);
    inS.endReadParams();
    StringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValues(::std::move(iceP_deviceID), ::std::move(iceP_listName), iceP_values, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_values, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertyValuesEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringList iceP_listName;
    istr->readAll(iceP_deviceID, iceP_listName);
    inS.endReadParams();
    StringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValuesEx(::std::move(iceP_deviceID), ::std::move(iceP_listName), iceP_values, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_values, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    istr->readAll(iceP_deviceID, iceP_name);
    inS.endReadParams();
    ::std::string iceP_value;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValue(::std::move(iceP_deviceID), ::std::move(iceP_name), iceP_value, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_value, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertyValueEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    istr->readAll(iceP_deviceID, iceP_name);
    inS.endReadParams();
    ::std::string iceP_value;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValueEx(::std::move(iceP_deviceID), ::std::move(iceP_name), iceP_value, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_value, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mupdateProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    MapMapStringMap iceP_properties;
    bool iceP_saveToDB;
    istr->readAll(iceP_properties, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateProperties(::std::move(iceP_properties), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mupdatePropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    MapStringMap iceP_values;
    bool iceP_saveToDB;
    istr->readAll(iceP_values, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdatePropertyValues(::std::move(iceP_values), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updateProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    StringMap iceP_property;
    bool iceP_saveToDB;
    istr->readAll(iceP_deviceID, iceP_name, iceP_property, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateProperty(::std::move(iceP_deviceID), ::std::move(iceP_name), ::std::move(iceP_property), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updateProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    MapStringMap iceP_properties;
    bool iceP_saveToDB;
    istr->readAll(iceP_deviceID, iceP_properties, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateProperties(::std::move(iceP_deviceID), ::std::move(iceP_properties), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updatePropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_values;
    bool iceP_saveToDB;
    istr->readAll(iceP_deviceID, iceP_values, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValues(::std::move(iceP_deviceID), ::std::move(iceP_values), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updatePropertyValuesEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_values;
    bool iceP_saveToDB;
    istr->readAll(iceP_deviceID, iceP_values, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValuesEx(::std::move(iceP_deviceID), ::std::move(iceP_values), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updatePropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    ::std::string iceP_value;
    bool iceP_saveToDB;
    istr->readAll(iceP_deviceID, iceP_name, iceP_value, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValue(::std::move(iceP_deviceID), ::std::move(iceP_name), ::std::move(iceP_value), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updatePropertyValueEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    ::std::string iceP_value;
    bool iceP_saveToDB;
    istr->readAll(iceP_deviceID, iceP_name, iceP_value, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValueEx(::std::move(iceP_deviceID), ::std::move(iceP_name), ::std::move(iceP_value), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getDataIDByProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    istr->readAll(iceP_deviceID, iceP_name);
    inS.endReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_dataID;
    ErrorInfo iceP_e;
    bool ret = this->getDataIDByProperty(::std::move(iceP_deviceID), ::std::move(iceP_name), iceP_tableName, iceP_dataID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_tableName, iceP_dataID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertyByDataID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_dataID;
    istr->readAll(iceP_dataID);
    inS.endReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyByDataID(::std::move(iceP_dataID), iceP_deviceID, iceP_name, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_deviceID, iceP_name, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_isPropertyExists(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    istr->readAll(iceP_deviceID, iceP_name);
    inS.endReadParams();
    bool iceP_exists;
    ErrorInfo iceP_e;
    bool ret = this->isPropertyExists(::std::move(iceP_deviceID), ::std::move(iceP_name), iceP_exists, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_exists, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPDeviceProperty_ops, iceC_ZG6000_ZGMPDeviceProperty_ops + 40, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPDeviceProperty_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getDataIDByProperty(in, current);
        }
        case 4:
        {
            return _iceD_getGroupProperties(in, current);
        }
        case 5:
        {
            return _iceD_getProperties(in, current);
        }
        case 6:
        {
            return _iceD_getPropertiesAll(in, current);
        }
        case 7:
        {
            return _iceD_getProperty(in, current);
        }
        case 8:
        {
            return _iceD_getPropertyByDataID(in, current);
        }
        case 9:
        {
            return _iceD_getPropertyValue(in, current);
        }
        case 10:
        {
            return _iceD_getPropertyValueEx(in, current);
        }
        case 11:
        {
            return _iceD_getPropertyValues(in, current);
        }
        case 12:
        {
            return _iceD_getPropertyValuesEx(in, current);
        }
        case 13:
        {
            return _iceD_getTableProperties(in, current);
        }
        case 14:
        {
            return _iceD_getVersion(in, current);
        }
        case 15:
        {
            return _iceD_heartDebug(in, current);
        }
        case 16:
        {
            return _iceD_ice_id(in, current);
        }
        case 17:
        {
            return _iceD_ice_ids(in, current);
        }
        case 18:
        {
            return _iceD_ice_isA(in, current);
        }
        case 19:
        {
            return _iceD_ice_ping(in, current);
        }
        case 20:
        {
            return _iceD_isAllowCtrl(in, current);
        }
        case 21:
        {
            return _iceD_isDebugging(in, current);
        }
        case 22:
        {
            return _iceD_isPropertyExists(in, current);
        }
        case 23:
        {
            return _iceD_mgetProperties(in, current);
        }
        case 24:
        {
            return _iceD_mgetPropertiesAll(in, current);
        }
        case 25:
        {
            return _iceD_mgetPropertyValues(in, current);
        }
        case 26:
        {
            return _iceD_mgetPropertyValuesEx(in, current);
        }
        case 27:
        {
            return _iceD_mupdateProperties(in, current);
        }
        case 28:
        {
            return _iceD_mupdatePropertyValues(in, current);
        }
        case 29:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 30:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 31:
        {
            return _iceD_startDebug(in, current);
        }
        case 32:
        {
            return _iceD_stopDebug(in, current);
        }
        case 33:
        {
            return _iceD_test(in, current);
        }
        case 34:
        {
            return _iceD_updateProperties(in, current);
        }
        case 35:
        {
            return _iceD_updateProperty(in, current);
        }
        case 36:
        {
            return _iceD_updatePropertyValue(in, current);
        }
        case 37:
        {
            return _iceD_updatePropertyValueEx(in, current);
        }
        case 38:
        {
            return _iceD_updatePropertyValues(in, current);
        }
        case 39:
        {
            return _iceD_updatePropertyValuesEx(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_isAllowCtrl(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::IsAllowCtrlResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_isAllowCtrl_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_isAllowCtrl_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::IsAllowCtrlResult v;
            istr->readAll(v.allow, v.conditions, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_mgetPropertiesAll(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MgetPropertiesAllResult>>& outAsync, const StringList& iceP_listDeviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertiesAll_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertiesAll_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDeviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::MgetPropertiesAllResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_mgetProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MgetPropertiesResult>>& outAsync, const StringList& iceP_listDeviceID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mgetProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_mgetProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDeviceID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::MgetPropertiesResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_mgetPropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MgetPropertyValuesResult>>& outAsync, const StringList& iceP_listDeviceID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValues_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValues_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDeviceID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::MgetPropertyValuesResult v;
            istr->readAll(v.values, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_mgetPropertyValuesEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MgetPropertyValuesExResult>>& outAsync, const StringList& iceP_listDeviceID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValuesEx_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValuesEx_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDeviceID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::MgetPropertyValuesExResult v;
            istr->readAll(v.values, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getGroupProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetGroupPropertiesResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getGroupProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getGroupProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetGroupPropertiesResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getTableProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetTablePropertiesResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_tableName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getTableProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getTableProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_tableName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetTablePropertiesResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertiesAll(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertiesAllResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertiesAll_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertiesAll_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetPropertiesAllResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertiesResult>>& outAsync, const ::std::string& iceP_deviceID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetPropertiesResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getProperty_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getProperty_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetPropertyResult v;
            istr->readAll(v.property, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyValuesResult>>& outAsync, const ::std::string& iceP_deviceID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValues_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValues_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetPropertyValuesResult v;
            istr->readAll(v.values, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertyValuesEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyValuesExResult>>& outAsync, const ::std::string& iceP_deviceID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValuesEx_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValuesEx_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetPropertyValuesExResult v;
            istr->readAll(v.values, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyValueResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValue_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetPropertyValueResult v;
            istr->readAll(v.value, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertyValueEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyValueExResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValueEx_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValueEx_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetPropertyValueExResult v;
            istr->readAll(v.value, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_mupdateProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MupdatePropertiesResult>>& outAsync, const MapMapStringMap& iceP_properties, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mupdateProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_mupdateProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_properties, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::MupdatePropertiesResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_mupdatePropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MupdatePropertyValuesResult>>& outAsync, const MapStringMap& iceP_values, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mupdatePropertyValues_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_mupdatePropertyValues_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_values, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::MupdatePropertyValuesResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_updateProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertyResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const StringMap& iceP_property, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updateProperty_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_updateProperty_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_name, iceP_property, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::UpdatePropertyResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_updateProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertiesResult>>& outAsync, const ::std::string& iceP_deviceID, const MapStringMap& iceP_properties, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updateProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_updateProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_properties, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::UpdatePropertiesResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_updatePropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertyValuesResult>>& outAsync, const ::std::string& iceP_deviceID, const StringMap& iceP_values, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValues_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValues_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_values, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::UpdatePropertyValuesResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_updatePropertyValuesEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertyValuesExResult>>& outAsync, const ::std::string& iceP_deviceID, const StringMap& iceP_values, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValuesEx_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValuesEx_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_values, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::UpdatePropertyValuesExResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_updatePropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertyValueResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::std::string& iceP_value, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValue_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_name, iceP_value, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::UpdatePropertyValueResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_updatePropertyValueEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertyValueExResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::std::string& iceP_value, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValueEx_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValueEx_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_name, iceP_value, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::UpdatePropertyValueExResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getDataIDByProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetDataIDByPropertyResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getDataIDByProperty_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getDataIDByProperty_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetDataIDByPropertyResult v;
            istr->readAll(v.tableName, v.dataID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertyByDataID(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyByDataIDResult>>& outAsync, const ::std::string& iceP_dataID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertyByDataID_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertyByDataID_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::GetPropertyByDataIDResult v;
            istr->readAll(v.deviceID, v.name, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDevicePropertyPrx::_iceI_isPropertyExists(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::IsPropertyExistsResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_isPropertyExists_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDeviceProperty_isPropertyExists_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDeviceProperty::IsPropertyExistsResult v;
            istr->readAll(v.exists, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGMPDevicePropertyPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGMPDevicePropertyPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGMPDevicePropertyPrx::ice_staticId()
{
    return ZGMPDeviceProperty::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_isAllowCtrl_name = "isAllowCtrl";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mgetPropertiesAll_name = "mgetPropertiesAll";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mgetProperties_name = "mgetProperties";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValues_name = "mgetPropertyValues";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValuesEx_name = "mgetPropertyValuesEx";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getGroupProperties_name = "getGroupProperties";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getTableProperties_name = "getTableProperties";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertiesAll_name = "getPropertiesAll";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getProperties_name = "getProperties";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getProperty_name = "getProperty";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertyValues_name = "getPropertyValues";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertyValuesEx_name = "getPropertyValuesEx";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertyValue_name = "getPropertyValue";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertyValueEx_name = "getPropertyValueEx";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mupdateProperties_name = "mupdateProperties";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_mupdatePropertyValues_name = "mupdatePropertyValues";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updateProperty_name = "updateProperty";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updateProperties_name = "updateProperties";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValues_name = "updatePropertyValues";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValuesEx_name = "updatePropertyValuesEx";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValue_name = "updatePropertyValue";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValueEx_name = "updatePropertyValueEx";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getDataIDByProperty_name = "getDataIDByProperty";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_getPropertyByDataID_name = "getPropertyByDataID";

const ::std::string iceC_ZG6000_ZGMPDeviceProperty_isPropertyExists_name = "isPropertyExists";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGMPDeviceProperty* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGMPDeviceProperty>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGMPDeviceProperty;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_isAllowCtrl(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_isAllowCtrl_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_isAllowCtrl_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_isAllowCtrl_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_isAllowCtrl_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_isAllowCtrl(bool& iceP_allow, ::ZG6000::StringMap& iceP_conditions, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_isAllowCtrl_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_allow);
    istr->read(iceP_conditions);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_isAllowCtrl(bool& iceP_allow, ::ZG6000::StringMap& iceP_conditions, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_isAllowCtrl_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_allow);
    istr->read(iceP_conditions);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_mgetPropertiesAll(const ::ZG6000::StringList& iceP_listDeviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertiesAll_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_mgetPropertiesAll_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertiesAll_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDeviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertiesAll_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_mgetPropertiesAll(::ZG6000::MapMapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mgetPropertiesAll_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_mgetPropertiesAll(::ZG6000::MapMapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mgetPropertiesAll_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_mgetProperties(const ::ZG6000::StringList& iceP_listDeviceID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mgetProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_mgetProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_mgetProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDeviceID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_mgetProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_mgetProperties(::ZG6000::MapMapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mgetProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_mgetProperties(::ZG6000::MapMapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mgetProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_mgetPropertyValues(const ::ZG6000::StringList& iceP_listDeviceID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValues_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValues_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValues_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDeviceID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValues_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_mgetPropertyValues(::ZG6000::MapStringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValues_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_mgetPropertyValues(::ZG6000::MapStringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValues_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_mgetPropertyValuesEx(const ::ZG6000::StringList& iceP_listDeviceID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValuesEx_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValuesEx_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValuesEx_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDeviceID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValuesEx_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_mgetPropertyValuesEx(::ZG6000::MapStringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValuesEx_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_mgetPropertyValuesEx(::ZG6000::MapStringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mgetPropertyValuesEx_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getGroupProperties(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getGroupProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getGroupProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getGroupProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getGroupProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getGroupProperties(::ZG6000::MapMapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getGroupProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getGroupProperties(::ZG6000::MapMapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getGroupProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getTableProperties(const ::std::string& iceP_deviceID, const ::std::string& iceP_tableName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getTableProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getTableProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getTableProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_tableName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getTableProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getTableProperties(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getTableProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getTableProperties(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getTableProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getPropertiesAll(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertiesAll_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getPropertiesAll_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getPropertiesAll_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertiesAll_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getPropertiesAll(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertiesAll_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getPropertiesAll(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertiesAll_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getProperties(const ::std::string& iceP_deviceID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getProperties(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getProperties(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getProperty(const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getProperty_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getProperty_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getProperty_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getProperty_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getProperty(::ZG6000::StringMap& iceP_property, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getProperty_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_property);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getProperty(::ZG6000::StringMap& iceP_property, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getProperty_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_property);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getPropertyValues(const ::std::string& iceP_deviceID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValues_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValues_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValues_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValues_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getPropertyValues(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValues_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getPropertyValues(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValues_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getPropertyValuesEx(const ::std::string& iceP_deviceID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValuesEx_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValuesEx_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValuesEx_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValuesEx_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getPropertyValuesEx(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValuesEx_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getPropertyValuesEx(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValuesEx_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getPropertyValue(const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getPropertyValue(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_value);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getPropertyValue(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_value);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getPropertyValueEx(const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValueEx_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValueEx_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValueEx_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertyValueEx_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getPropertyValueEx(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValueEx_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_value);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getPropertyValueEx(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyValueEx_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_value);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_mupdateProperties(const ::ZG6000::MapMapStringMap& iceP_properties, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mupdateProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_mupdateProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_mupdateProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_properties);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_mupdateProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_mupdateProperties(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mupdateProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_mupdateProperties(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mupdateProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_mupdatePropertyValues(const ::ZG6000::MapStringMap& iceP_values, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_mupdatePropertyValues_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_mupdatePropertyValues_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_mupdatePropertyValues_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_values);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_mupdatePropertyValues_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_mupdatePropertyValues(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mupdatePropertyValues_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_mupdatePropertyValues(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_mupdatePropertyValues_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_updateProperty(const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::ZG6000::StringMap& iceP_property, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updateProperty_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_updateProperty_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_updateProperty_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_name);
        ostr->write(iceP_property);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_updateProperty_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_updateProperty(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updateProperty_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_updateProperty(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updateProperty_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_updateProperties(const ::std::string& iceP_deviceID, const ::ZG6000::MapStringMap& iceP_properties, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updateProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_updateProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_updateProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_properties);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_updateProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_updateProperties(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updateProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_updateProperties(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updateProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_updatePropertyValues(const ::std::string& iceP_deviceID, const ::ZG6000::StringMap& iceP_values, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValues_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValues_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValues_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_values);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValues_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_updatePropertyValues(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValues_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_updatePropertyValues(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValues_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_updatePropertyValuesEx(const ::std::string& iceP_deviceID, const ::ZG6000::StringMap& iceP_values, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValuesEx_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValuesEx_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValuesEx_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_values);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValuesEx_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_updatePropertyValuesEx(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValuesEx_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_updatePropertyValuesEx(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValuesEx_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_updatePropertyValue(const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::std::string& iceP_value, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_name);
        ostr->write(iceP_value);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_updatePropertyValue(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_updatePropertyValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_updatePropertyValueEx(const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::std::string& iceP_value, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValueEx_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValueEx_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValueEx_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_name);
        ostr->write(iceP_value);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValueEx_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_updatePropertyValueEx(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValueEx_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_updatePropertyValueEx(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_updatePropertyValueEx_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getDataIDByProperty(const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getDataIDByProperty_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getDataIDByProperty_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getDataIDByProperty_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getDataIDByProperty_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getDataIDByProperty(::std::string& iceP_tableName, ::std::string& iceP_dataID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getDataIDByProperty_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_tableName);
    istr->read(iceP_dataID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getDataIDByProperty(::std::string& iceP_tableName, ::std::string& iceP_dataID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getDataIDByProperty_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_tableName);
    istr->read(iceP_dataID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_getPropertyByDataID(const ::std::string& iceP_dataID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_getPropertyByDataID_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyByDataID_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_getPropertyByDataID_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_getPropertyByDataID_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_getPropertyByDataID(::std::string& iceP_deviceID, ::std::string& iceP_name, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyByDataID_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_deviceID);
    istr->read(iceP_name);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_getPropertyByDataID(::std::string& iceP_deviceID, ::std::string& iceP_name, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_getPropertyByDataID_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_deviceID);
    istr->read(iceP_name);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_begin_isPropertyExists(const ::std::string& iceP_deviceID, const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDeviceProperty_isPropertyExists_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDeviceProperty_isPropertyExists_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDeviceProperty_isPropertyExists_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDeviceProperty_isPropertyExists_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDeviceProperty::end_isPropertyExists(bool& iceP_exists, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_isPropertyExists_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_exists);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDeviceProperty::_iceI_end_isPropertyExists(bool& iceP_exists, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDeviceProperty_isPropertyExists_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_exists);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGMPDeviceProperty::_newInstance() const
{
    return new ZGMPDeviceProperty;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGMPDeviceProperty::ice_staticId()
{
    return ::ZG6000::ZGMPDeviceProperty::ice_staticId();
}

ZG6000::ZGMPDeviceProperty::~ZGMPDeviceProperty()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGMPDeviceProperty* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPDeviceProperty",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGMPDeviceProperty::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPDeviceProperty_ids, iceC_ZG6000_ZGMPDeviceProperty_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGMPDeviceProperty::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGMPDeviceProperty_ids[0], &iceC_ZG6000_ZGMPDeviceProperty_ids[3]);
}

const ::std::string&
ZG6000::ZGMPDeviceProperty::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPDeviceProperty::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGMPDeviceProperty";
    return typeId;
#else
    return iceC_ZG6000_ZGMPDeviceProperty_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_isAllowCtrl(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    bool iceP_allow;
    StringMap iceP_conditions;
    ErrorInfo iceP_e;
    bool ret = this->isAllowCtrl(iceP_deviceID, iceP_allow, iceP_conditions, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_allow);
    ostr->write(iceP_conditions);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mgetPropertiesAll(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    istr->read(iceP_listDeviceID);
    inS.endReadParams();
    MapMapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->mgetPropertiesAll(iceP_listDeviceID, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mgetProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    StringList iceP_listName;
    istr->read(iceP_listDeviceID);
    istr->read(iceP_listName);
    inS.endReadParams();
    MapMapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->mgetProperties(iceP_listDeviceID, iceP_listName, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mgetPropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    StringList iceP_listName;
    istr->read(iceP_listDeviceID);
    istr->read(iceP_listName);
    inS.endReadParams();
    MapStringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->mgetPropertyValues(iceP_listDeviceID, iceP_listName, iceP_values, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_values);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mgetPropertyValuesEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    StringList iceP_listName;
    istr->read(iceP_listDeviceID);
    istr->read(iceP_listName);
    inS.endReadParams();
    MapStringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->mgetPropertyValuesEx(iceP_listDeviceID, iceP_listName, iceP_values, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_values);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getGroupProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    MapMapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getGroupProperties(iceP_deviceID, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getTableProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_tableName;
    istr->read(iceP_deviceID);
    istr->read(iceP_tableName);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getTableProperties(iceP_deviceID, iceP_tableName, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertiesAll(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getPropertiesAll(iceP_deviceID, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringList iceP_listName;
    istr->read(iceP_deviceID);
    istr->read(iceP_listName);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getProperties(iceP_deviceID, iceP_listName, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    istr->read(iceP_deviceID);
    istr->read(iceP_name);
    inS.endReadParams();
    StringMap iceP_property;
    ErrorInfo iceP_e;
    bool ret = this->getProperty(iceP_deviceID, iceP_name, iceP_property, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_property);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringList iceP_listName;
    istr->read(iceP_deviceID);
    istr->read(iceP_listName);
    inS.endReadParams();
    StringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValues(iceP_deviceID, iceP_listName, iceP_values, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_values);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertyValuesEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringList iceP_listName;
    istr->read(iceP_deviceID);
    istr->read(iceP_listName);
    inS.endReadParams();
    StringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValuesEx(iceP_deviceID, iceP_listName, iceP_values, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_values);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    istr->read(iceP_deviceID);
    istr->read(iceP_name);
    inS.endReadParams();
    ::std::string iceP_value;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValue(iceP_deviceID, iceP_name, iceP_value, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_value);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertyValueEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    istr->read(iceP_deviceID);
    istr->read(iceP_name);
    inS.endReadParams();
    ::std::string iceP_value;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValueEx(iceP_deviceID, iceP_name, iceP_value, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_value);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mupdateProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    MapMapStringMap iceP_properties;
    bool iceP_saveToDB;
    istr->read(iceP_properties);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdateProperties(iceP_properties, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_mupdatePropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    MapStringMap iceP_values;
    bool iceP_saveToDB;
    istr->read(iceP_values);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->mupdatePropertyValues(iceP_values, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updateProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    StringMap iceP_property;
    bool iceP_saveToDB;
    istr->read(iceP_deviceID);
    istr->read(iceP_name);
    istr->read(iceP_property);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateProperty(iceP_deviceID, iceP_name, iceP_property, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updateProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    MapStringMap iceP_properties;
    bool iceP_saveToDB;
    istr->read(iceP_deviceID);
    istr->read(iceP_properties);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateProperties(iceP_deviceID, iceP_properties, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updatePropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_values;
    bool iceP_saveToDB;
    istr->read(iceP_deviceID);
    istr->read(iceP_values);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValues(iceP_deviceID, iceP_values, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updatePropertyValuesEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_values;
    bool iceP_saveToDB;
    istr->read(iceP_deviceID);
    istr->read(iceP_values);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValuesEx(iceP_deviceID, iceP_values, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updatePropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    ::std::string iceP_value;
    bool iceP_saveToDB;
    istr->read(iceP_deviceID);
    istr->read(iceP_name);
    istr->read(iceP_value);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValue(iceP_deviceID, iceP_name, iceP_value, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_updatePropertyValueEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    ::std::string iceP_value;
    bool iceP_saveToDB;
    istr->read(iceP_deviceID);
    istr->read(iceP_name);
    istr->read(iceP_value);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValueEx(iceP_deviceID, iceP_name, iceP_value, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getDataIDByProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    istr->read(iceP_deviceID);
    istr->read(iceP_name);
    inS.endReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_dataID;
    ErrorInfo iceP_e;
    bool ret = this->getDataIDByProperty(iceP_deviceID, iceP_name, iceP_tableName, iceP_dataID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_tableName);
    ostr->write(iceP_dataID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_getPropertyByDataID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_dataID;
    istr->read(iceP_dataID);
    inS.endReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyByDataID(iceP_dataID, iceP_deviceID, iceP_name, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_deviceID);
    ostr->write(iceP_name);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceD_isPropertyExists(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_name;
    istr->read(iceP_deviceID);
    istr->read(iceP_name);
    inS.endReadParams();
    bool iceP_exists;
    ErrorInfo iceP_e;
    bool ret = this->isPropertyExists(iceP_deviceID, iceP_name, iceP_exists, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_exists);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPDeviceProperty_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getDataIDByProperty",
    "getGroupProperties",
    "getProperties",
    "getPropertiesAll",
    "getProperty",
    "getPropertyByDataID",
    "getPropertyValue",
    "getPropertyValueEx",
    "getPropertyValues",
    "getPropertyValuesEx",
    "getTableProperties",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isAllowCtrl",
    "isDebugging",
    "isPropertyExists",
    "mgetProperties",
    "mgetPropertiesAll",
    "mgetPropertyValues",
    "mgetPropertyValuesEx",
    "mupdateProperties",
    "mupdatePropertyValues",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test",
    "updateProperties",
    "updateProperty",
    "updatePropertyValue",
    "updatePropertyValueEx",
    "updatePropertyValues",
    "updatePropertyValuesEx"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGMPDeviceProperty::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPDeviceProperty_all, iceC_ZG6000_ZGMPDeviceProperty_all + 40, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPDeviceProperty_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getDataIDByProperty(in, current);
        }
        case 4:
        {
            return _iceD_getGroupProperties(in, current);
        }
        case 5:
        {
            return _iceD_getProperties(in, current);
        }
        case 6:
        {
            return _iceD_getPropertiesAll(in, current);
        }
        case 7:
        {
            return _iceD_getProperty(in, current);
        }
        case 8:
        {
            return _iceD_getPropertyByDataID(in, current);
        }
        case 9:
        {
            return _iceD_getPropertyValue(in, current);
        }
        case 10:
        {
            return _iceD_getPropertyValueEx(in, current);
        }
        case 11:
        {
            return _iceD_getPropertyValues(in, current);
        }
        case 12:
        {
            return _iceD_getPropertyValuesEx(in, current);
        }
        case 13:
        {
            return _iceD_getTableProperties(in, current);
        }
        case 14:
        {
            return _iceD_getVersion(in, current);
        }
        case 15:
        {
            return _iceD_heartDebug(in, current);
        }
        case 16:
        {
            return _iceD_ice_id(in, current);
        }
        case 17:
        {
            return _iceD_ice_ids(in, current);
        }
        case 18:
        {
            return _iceD_ice_isA(in, current);
        }
        case 19:
        {
            return _iceD_ice_ping(in, current);
        }
        case 20:
        {
            return _iceD_isAllowCtrl(in, current);
        }
        case 21:
        {
            return _iceD_isDebugging(in, current);
        }
        case 22:
        {
            return _iceD_isPropertyExists(in, current);
        }
        case 23:
        {
            return _iceD_mgetProperties(in, current);
        }
        case 24:
        {
            return _iceD_mgetPropertiesAll(in, current);
        }
        case 25:
        {
            return _iceD_mgetPropertyValues(in, current);
        }
        case 26:
        {
            return _iceD_mgetPropertyValuesEx(in, current);
        }
        case 27:
        {
            return _iceD_mupdateProperties(in, current);
        }
        case 28:
        {
            return _iceD_mupdatePropertyValues(in, current);
        }
        case 29:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 30:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 31:
        {
            return _iceD_startDebug(in, current);
        }
        case 32:
        {
            return _iceD_stopDebug(in, current);
        }
        case 33:
        {
            return _iceD_test(in, current);
        }
        case 34:
        {
            return _iceD_updateProperties(in, current);
        }
        case 35:
        {
            return _iceD_updateProperty(in, current);
        }
        case 36:
        {
            return _iceD_updatePropertyValue(in, current);
        }
        case 37:
        {
            return _iceD_updatePropertyValueEx(in, current);
        }
        case 38:
        {
            return _iceD_updatePropertyValues(in, current);
        }
        case 39:
        {
            return _iceD_updatePropertyValuesEx(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGMPDeviceProperty::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGMPDeviceProperty, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGMPDeviceProperty::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGMPDeviceProperty, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGMPDevicePropertyPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGMPDevicePropertyPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGMPDeviceProperty::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

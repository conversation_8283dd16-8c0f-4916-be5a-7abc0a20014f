//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPSecureManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPSecureManager_h__
#define __ZGSPSecureManager_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPSecureManager;
class ZGSPSecureManagerPrx;

}

namespace ZG6000
{

class ZGSPSecureManager : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPSecureManagerPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to aesEncrypt.
     */
    struct AesEncryptResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ::std::string output;
        ErrorInfo e;
    };

    /**
     * @param in 原始数据
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   AES加密
     */
    virtual bool aesEncrypt(::std::string in, ::std::string& output, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_aesEncrypt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to aesDecrypt.
     */
    struct AesDecryptResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ::std::string output;
        ErrorInfo e;
    };

    /**
     * @param in 加密数据
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   AES解密
     */
    virtual bool aesDecrypt(::std::string in, ::std::string& output, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_aesDecrypt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to rsaEncrypt.
     */
    struct RsaEncryptResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ::std::string output;
        ErrorInfo e;
    };

    /**
     * @param in 原始数据
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA加密
     */
    virtual bool rsaEncrypt(::std::string in, ::std::string& output, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaEncrypt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to rsaDecrypt.
     */
    struct RsaDecryptResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ::std::string output;
        ErrorInfo e;
    };

    /**
     * @param in 加密数据
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA解密
     */
    virtual bool rsaDecrypt(::std::string in, ::std::string& output, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaDecrypt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to rsaSign.
     */
    struct RsaSignResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ::std::string sign;
        ErrorInfo e;
    };

    /**
     * @param in 消息内容
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA签名
     */
    virtual bool rsaSign(::std::string in, ::std::string& sign, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaSign(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to rsaVerify.
     */
    struct RsaVerifyResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        bool result;
        ErrorInfo e;
    };

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA验签
     */
    virtual bool rsaVerify(::std::string in, ::std::string sign, bool& result, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaVerify(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to rsaSeal.
     */
    struct RsaSealResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ::std::string output;
        ::std::string envelope;
        ErrorInfo e;
    };

    /**
     * @param in 原始数据
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA电子信封密封
     */
    virtual bool rsaSeal(::std::string in, ::std::string& output, ::std::string& envelope, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaSeal(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to rsaOpen.
     */
    struct RsaOpenResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ::std::string output;
        ErrorInfo e;
    };

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA电子信封拆封
     */
    virtual bool rsaOpen(::std::string in, ::std::string envelope, ::std::string& output, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaOpen(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPSecureManagerPrx : public virtual ::Ice::Proxy<ZGSPSecureManagerPrx, ZGServerBasePrx>
{
public:

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   AES加密
     */
    bool aesEncrypt(const ::std::string& in, ::std::string& output, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPSecureManager::AesEncryptResult>(true, this, &ZGSPSecureManagerPrx::_iceI_aesEncrypt, in, context).get();
        output = ::std::move(_result.output);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   AES加密
     */
    template<template<typename> class P = ::std::promise>
    auto aesEncryptAsync(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPSecureManager::AesEncryptResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPSecureManager::AesEncryptResult, P>(false, this, &ZGSPSecureManagerPrx::_iceI_aesEncrypt, in, context);
    }

    /**
     * @param in 原始数据
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   AES加密
     */
    ::std::function<void()>
    aesEncryptAsync(const ::std::string& in,
                    ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPSecureManager::AesEncryptResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.output), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPSecureManager::AesEncryptResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPSecureManagerPrx::_iceI_aesEncrypt, in, context);
    }

    /// \cond INTERNAL
    void _iceI_aesEncrypt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::AesEncryptResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   AES解密
     */
    bool aesDecrypt(const ::std::string& in, ::std::string& output, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPSecureManager::AesDecryptResult>(true, this, &ZGSPSecureManagerPrx::_iceI_aesDecrypt, in, context).get();
        output = ::std::move(_result.output);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   AES解密
     */
    template<template<typename> class P = ::std::promise>
    auto aesDecryptAsync(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPSecureManager::AesDecryptResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPSecureManager::AesDecryptResult, P>(false, this, &ZGSPSecureManagerPrx::_iceI_aesDecrypt, in, context);
    }

    /**
     * @param in 加密数据
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   AES解密
     */
    ::std::function<void()>
    aesDecryptAsync(const ::std::string& in,
                    ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPSecureManager::AesDecryptResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.output), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPSecureManager::AesDecryptResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPSecureManagerPrx::_iceI_aesDecrypt, in, context);
    }

    /// \cond INTERNAL
    void _iceI_aesDecrypt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::AesDecryptResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA加密
     */
    bool rsaEncrypt(const ::std::string& in, ::std::string& output, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPSecureManager::RsaEncryptResult>(true, this, &ZGSPSecureManagerPrx::_iceI_rsaEncrypt, in, context).get();
        output = ::std::move(_result.output);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   RSA加密
     */
    template<template<typename> class P = ::std::promise>
    auto rsaEncryptAsync(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPSecureManager::RsaEncryptResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPSecureManager::RsaEncryptResult, P>(false, this, &ZGSPSecureManagerPrx::_iceI_rsaEncrypt, in, context);
    }

    /**
     * @param in 原始数据
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   RSA加密
     */
    ::std::function<void()>
    rsaEncryptAsync(const ::std::string& in,
                    ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPSecureManager::RsaEncryptResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.output), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPSecureManager::RsaEncryptResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPSecureManagerPrx::_iceI_rsaEncrypt, in, context);
    }

    /// \cond INTERNAL
    void _iceI_rsaEncrypt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaEncryptResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA解密
     */
    bool rsaDecrypt(const ::std::string& in, ::std::string& output, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPSecureManager::RsaDecryptResult>(true, this, &ZGSPSecureManagerPrx::_iceI_rsaDecrypt, in, context).get();
        output = ::std::move(_result.output);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   RSA解密
     */
    template<template<typename> class P = ::std::promise>
    auto rsaDecryptAsync(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPSecureManager::RsaDecryptResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPSecureManager::RsaDecryptResult, P>(false, this, &ZGSPSecureManagerPrx::_iceI_rsaDecrypt, in, context);
    }

    /**
     * @param in 加密数据
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   RSA解密
     */
    ::std::function<void()>
    rsaDecryptAsync(const ::std::string& in,
                    ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPSecureManager::RsaDecryptResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.output), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPSecureManager::RsaDecryptResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPSecureManagerPrx::_iceI_rsaDecrypt, in, context);
    }

    /// \cond INTERNAL
    void _iceI_rsaDecrypt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaDecryptResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param in 消息内容
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA签名
     */
    bool rsaSign(const ::std::string& in, ::std::string& sign, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPSecureManager::RsaSignResult>(true, this, &ZGSPSecureManagerPrx::_iceI_rsaSign, in, context).get();
        sign = ::std::move(_result.sign);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param in 消息内容
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   RSA签名
     */
    template<template<typename> class P = ::std::promise>
    auto rsaSignAsync(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPSecureManager::RsaSignResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPSecureManager::RsaSignResult, P>(false, this, &ZGSPSecureManagerPrx::_iceI_rsaSign, in, context);
    }

    /**
     * @param in 消息内容
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   RSA签名
     */
    ::std::function<void()>
    rsaSignAsync(const ::std::string& in,
                 ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                 ::std::function<void(::std::exception_ptr)> ex = nullptr,
                 ::std::function<void(bool)> sent = nullptr,
                 const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPSecureManager::RsaSignResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.sign), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPSecureManager::RsaSignResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPSecureManagerPrx::_iceI_rsaSign, in, context);
    }

    /// \cond INTERNAL
    void _iceI_rsaSign(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaSignResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA验签
     */
    bool rsaVerify(const ::std::string& in, const ::std::string& sign, bool& result, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPSecureManager::RsaVerifyResult>(true, this, &ZGSPSecureManagerPrx::_iceI_rsaVerify, in, sign, context).get();
        result = _result.result;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   RSA验签
     */
    template<template<typename> class P = ::std::promise>
    auto rsaVerifyAsync(const ::std::string& in, const ::std::string& sign, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPSecureManager::RsaVerifyResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPSecureManager::RsaVerifyResult, P>(false, this, &ZGSPSecureManagerPrx::_iceI_rsaVerify, in, sign, context);
    }

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   RSA验签
     */
    ::std::function<void()>
    rsaVerifyAsync(const ::std::string& in, const ::std::string& sign,
                   ::std::function<void(bool, bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPSecureManager::RsaVerifyResult&& _result)
        {
            response(_result.returnValue, _result.result, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPSecureManager::RsaVerifyResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPSecureManagerPrx::_iceI_rsaVerify, in, sign, context);
    }

    /// \cond INTERNAL
    void _iceI_rsaVerify(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaVerifyResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA电子信封密封
     */
    bool rsaSeal(const ::std::string& in, ::std::string& output, ::std::string& envelope, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPSecureManager::RsaSealResult>(true, this, &ZGSPSecureManagerPrx::_iceI_rsaSeal, in, context).get();
        output = ::std::move(_result.output);
        envelope = ::std::move(_result.envelope);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   RSA电子信封密封
     */
    template<template<typename> class P = ::std::promise>
    auto rsaSealAsync(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPSecureManager::RsaSealResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPSecureManager::RsaSealResult, P>(false, this, &ZGSPSecureManagerPrx::_iceI_rsaSeal, in, context);
    }

    /**
     * @param in 原始数据
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   RSA电子信封密封
     */
    ::std::function<void()>
    rsaSealAsync(const ::std::string& in,
                 ::std::function<void(bool, ::std::string, ::std::string, ::ZG6000::ErrorInfo)> response,
                 ::std::function<void(::std::exception_ptr)> ex = nullptr,
                 ::std::function<void(bool)> sent = nullptr,
                 const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPSecureManager::RsaSealResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.output), ::std::move(_result.envelope), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPSecureManager::RsaSealResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPSecureManagerPrx::_iceI_rsaSeal, in, context);
    }

    /// \cond INTERNAL
    void _iceI_rsaSeal(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaSealResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA电子信封拆封
     */
    bool rsaOpen(const ::std::string& in, const ::std::string& envelope, ::std::string& output, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPSecureManager::RsaOpenResult>(true, this, &ZGSPSecureManagerPrx::_iceI_rsaOpen, in, envelope, context).get();
        output = ::std::move(_result.output);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   RSA电子信封拆封
     */
    template<template<typename> class P = ::std::promise>
    auto rsaOpenAsync(const ::std::string& in, const ::std::string& envelope, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPSecureManager::RsaOpenResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPSecureManager::RsaOpenResult, P>(false, this, &ZGSPSecureManagerPrx::_iceI_rsaOpen, in, envelope, context);
    }

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   RSA电子信封拆封
     */
    ::std::function<void()>
    rsaOpenAsync(const ::std::string& in, const ::std::string& envelope,
                 ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                 ::std::function<void(::std::exception_ptr)> ex = nullptr,
                 ::std::function<void(bool)> sent = nullptr,
                 const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPSecureManager::RsaOpenResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.output), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPSecureManager::RsaOpenResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPSecureManagerPrx::_iceI_rsaOpen, in, envelope, context);
    }

    /// \cond INTERNAL
    void _iceI_rsaOpen(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaOpenResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPSecureManagerPrx() = default;
    friend ::std::shared_ptr<ZGSPSecureManagerPrx> IceInternal::createProxy<ZGSPSecureManagerPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPSecureManagerPtr = ::std::shared_ptr<ZGSPSecureManager>;
using ZGSPSecureManagerPrxPtr = ::std::shared_ptr<ZGSPSecureManagerPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPSecureManager;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPSecureManager>&);
::IceProxy::Ice::Object* upCast(ZGSPSecureManager*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPSecureManager;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPSecureManager*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPSecureManager> ZGSPSecureManagerPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPSecureManager> ZGSPSecureManagerPrx;
typedef ZGSPSecureManagerPrx ZGSPSecureManagerPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPSecureManagerPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_aesEncrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_aesEncrypt.
 */
class Callback_ZGSPSecureManager_aesEncrypt_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPSecureManager_aesEncrypt_Base> Callback_ZGSPSecureManager_aesEncryptPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_aesDecrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_aesDecrypt.
 */
class Callback_ZGSPSecureManager_aesDecrypt_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPSecureManager_aesDecrypt_Base> Callback_ZGSPSecureManager_aesDecryptPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaEncrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaEncrypt.
 */
class Callback_ZGSPSecureManager_rsaEncrypt_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPSecureManager_rsaEncrypt_Base> Callback_ZGSPSecureManager_rsaEncryptPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaDecrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaDecrypt.
 */
class Callback_ZGSPSecureManager_rsaDecrypt_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPSecureManager_rsaDecrypt_Base> Callback_ZGSPSecureManager_rsaDecryptPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSign.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaSign.
 */
class Callback_ZGSPSecureManager_rsaSign_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPSecureManager_rsaSign_Base> Callback_ZGSPSecureManager_rsaSignPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaVerify.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaVerify.
 */
class Callback_ZGSPSecureManager_rsaVerify_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPSecureManager_rsaVerify_Base> Callback_ZGSPSecureManager_rsaVerifyPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSeal.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaSeal.
 */
class Callback_ZGSPSecureManager_rsaSeal_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPSecureManager_rsaSeal_Base> Callback_ZGSPSecureManager_rsaSealPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaOpen.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaOpen.
 */
class Callback_ZGSPSecureManager_rsaOpen_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPSecureManager_rsaOpen_Base> Callback_ZGSPSecureManager_rsaOpenPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPSecureManager : public virtual ::Ice::Proxy<ZGSPSecureManager, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   AES加密
     */
    bool aesEncrypt(const ::std::string& in, ::std::string& output, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_aesEncrypt(output, e, _iceI_begin_aesEncrypt(in, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   AES加密
     */
    ::Ice::AsyncResultPtr begin_aesEncrypt(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_aesEncrypt(in, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param in 原始数据
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   AES加密
     */
    ::Ice::AsyncResultPtr begin_aesEncrypt(const ::std::string& in, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_aesEncrypt(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   AES加密
     */
    ::Ice::AsyncResultPtr begin_aesEncrypt(const ::std::string& in, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_aesEncrypt(in, context, cb, cookie);
    }

    /**
     * @param in 原始数据
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   AES加密
     */
    ::Ice::AsyncResultPtr begin_aesEncrypt(const ::std::string& in, const ::ZG6000::Callback_ZGSPSecureManager_aesEncryptPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_aesEncrypt(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   AES加密
     */
    ::Ice::AsyncResultPtr begin_aesEncrypt(const ::std::string& in, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPSecureManager_aesEncryptPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_aesEncrypt(in, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_aesEncrypt.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_aesEncrypt(::std::string& output, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_aesEncrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_aesEncrypt(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   AES解密
     */
    bool aesDecrypt(const ::std::string& in, ::std::string& output, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_aesDecrypt(output, e, _iceI_begin_aesDecrypt(in, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   AES解密
     */
    ::Ice::AsyncResultPtr begin_aesDecrypt(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_aesDecrypt(in, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param in 加密数据
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   AES解密
     */
    ::Ice::AsyncResultPtr begin_aesDecrypt(const ::std::string& in, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_aesDecrypt(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   AES解密
     */
    ::Ice::AsyncResultPtr begin_aesDecrypt(const ::std::string& in, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_aesDecrypt(in, context, cb, cookie);
    }

    /**
     * @param in 加密数据
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   AES解密
     */
    ::Ice::AsyncResultPtr begin_aesDecrypt(const ::std::string& in, const ::ZG6000::Callback_ZGSPSecureManager_aesDecryptPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_aesDecrypt(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   AES解密
     */
    ::Ice::AsyncResultPtr begin_aesDecrypt(const ::std::string& in, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPSecureManager_aesDecryptPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_aesDecrypt(in, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_aesDecrypt.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_aesDecrypt(::std::string& output, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_aesDecrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_aesDecrypt(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA加密
     */
    bool rsaEncrypt(const ::std::string& in, ::std::string& output, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_rsaEncrypt(output, e, _iceI_begin_rsaEncrypt(in, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA加密
     */
    ::Ice::AsyncResultPtr begin_rsaEncrypt(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_rsaEncrypt(in, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param in 原始数据
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA加密
     */
    ::Ice::AsyncResultPtr begin_rsaEncrypt(const ::std::string& in, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaEncrypt(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA加密
     */
    ::Ice::AsyncResultPtr begin_rsaEncrypt(const ::std::string& in, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaEncrypt(in, context, cb, cookie);
    }

    /**
     * @param in 原始数据
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA加密
     */
    ::Ice::AsyncResultPtr begin_rsaEncrypt(const ::std::string& in, const ::ZG6000::Callback_ZGSPSecureManager_rsaEncryptPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaEncrypt(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA加密
     */
    ::Ice::AsyncResultPtr begin_rsaEncrypt(const ::std::string& in, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPSecureManager_rsaEncryptPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaEncrypt(in, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_rsaEncrypt.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_rsaEncrypt(::std::string& output, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_rsaEncrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_rsaEncrypt(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA解密
     */
    bool rsaDecrypt(const ::std::string& in, ::std::string& output, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_rsaDecrypt(output, e, _iceI_begin_rsaDecrypt(in, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA解密
     */
    ::Ice::AsyncResultPtr begin_rsaDecrypt(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_rsaDecrypt(in, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param in 加密数据
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA解密
     */
    ::Ice::AsyncResultPtr begin_rsaDecrypt(const ::std::string& in, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaDecrypt(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA解密
     */
    ::Ice::AsyncResultPtr begin_rsaDecrypt(const ::std::string& in, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaDecrypt(in, context, cb, cookie);
    }

    /**
     * @param in 加密数据
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA解密
     */
    ::Ice::AsyncResultPtr begin_rsaDecrypt(const ::std::string& in, const ::ZG6000::Callback_ZGSPSecureManager_rsaDecryptPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaDecrypt(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 加密数据
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA解密
     */
    ::Ice::AsyncResultPtr begin_rsaDecrypt(const ::std::string& in, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPSecureManager_rsaDecryptPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaDecrypt(in, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_rsaDecrypt.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_rsaDecrypt(::std::string& output, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_rsaDecrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_rsaDecrypt(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param in 消息内容
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA签名
     */
    bool rsaSign(const ::std::string& in, ::std::string& sign, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_rsaSign(sign, e, _iceI_begin_rsaSign(in, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param in 消息内容
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA签名
     */
    ::Ice::AsyncResultPtr begin_rsaSign(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_rsaSign(in, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param in 消息内容
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA签名
     */
    ::Ice::AsyncResultPtr begin_rsaSign(const ::std::string& in, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaSign(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 消息内容
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA签名
     */
    ::Ice::AsyncResultPtr begin_rsaSign(const ::std::string& in, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaSign(in, context, cb, cookie);
    }

    /**
     * @param in 消息内容
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA签名
     */
    ::Ice::AsyncResultPtr begin_rsaSign(const ::std::string& in, const ::ZG6000::Callback_ZGSPSecureManager_rsaSignPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaSign(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 消息内容
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA签名
     */
    ::Ice::AsyncResultPtr begin_rsaSign(const ::std::string& in, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPSecureManager_rsaSignPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaSign(in, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_rsaSign.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_rsaSign(::std::string& sign, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_rsaSign(::std::string& iceP_sign, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_rsaSign(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA验签
     */
    bool rsaVerify(const ::std::string& in, const ::std::string& sign, bool& result, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_rsaVerify(result, e, _iceI_begin_rsaVerify(in, sign, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA验签
     */
    ::Ice::AsyncResultPtr begin_rsaVerify(const ::std::string& in, const ::std::string& sign, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_rsaVerify(in, sign, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA验签
     */
    ::Ice::AsyncResultPtr begin_rsaVerify(const ::std::string& in, const ::std::string& sign, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaVerify(in, sign, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA验签
     */
    ::Ice::AsyncResultPtr begin_rsaVerify(const ::std::string& in, const ::std::string& sign, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaVerify(in, sign, context, cb, cookie);
    }

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA验签
     */
    ::Ice::AsyncResultPtr begin_rsaVerify(const ::std::string& in, const ::std::string& sign, const ::ZG6000::Callback_ZGSPSecureManager_rsaVerifyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaVerify(in, sign, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA验签
     */
    ::Ice::AsyncResultPtr begin_rsaVerify(const ::std::string& in, const ::std::string& sign, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPSecureManager_rsaVerifyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaVerify(in, sign, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_rsaVerify.
     * @param result_ The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_rsaVerify(bool& result, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result_);
    /// \cond INTERNAL

    void _iceI_end_rsaVerify(bool& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_rsaVerify(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA电子信封密封
     */
    bool rsaSeal(const ::std::string& in, ::std::string& output, ::std::string& envelope, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_rsaSeal(output, envelope, e, _iceI_begin_rsaSeal(in, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA电子信封密封
     */
    ::Ice::AsyncResultPtr begin_rsaSeal(const ::std::string& in, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_rsaSeal(in, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param in 原始数据
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA电子信封密封
     */
    ::Ice::AsyncResultPtr begin_rsaSeal(const ::std::string& in, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaSeal(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA电子信封密封
     */
    ::Ice::AsyncResultPtr begin_rsaSeal(const ::std::string& in, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaSeal(in, context, cb, cookie);
    }

    /**
     * @param in 原始数据
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA电子信封密封
     */
    ::Ice::AsyncResultPtr begin_rsaSeal(const ::std::string& in, const ::ZG6000::Callback_ZGSPSecureManager_rsaSealPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaSeal(in, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 原始数据
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA电子信封密封
     */
    ::Ice::AsyncResultPtr begin_rsaSeal(const ::std::string& in, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPSecureManager_rsaSealPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaSeal(in, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_rsaSeal.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_rsaSeal(::std::string& output, ::std::string& envelope, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_rsaSeal(::std::string& iceP_output, ::std::string& iceP_envelope, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_rsaSeal(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA电子信封拆封
     */
    bool rsaOpen(const ::std::string& in, const ::std::string& envelope, ::std::string& output, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_rsaOpen(output, e, _iceI_begin_rsaOpen(in, envelope, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA电子信封拆封
     */
    ::Ice::AsyncResultPtr begin_rsaOpen(const ::std::string& in, const ::std::string& envelope, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_rsaOpen(in, envelope, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA电子信封拆封
     */
    ::Ice::AsyncResultPtr begin_rsaOpen(const ::std::string& in, const ::std::string& envelope, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaOpen(in, envelope, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA电子信封拆封
     */
    ::Ice::AsyncResultPtr begin_rsaOpen(const ::std::string& in, const ::std::string& envelope, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaOpen(in, envelope, context, cb, cookie);
    }

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA电子信封拆封
     */
    ::Ice::AsyncResultPtr begin_rsaOpen(const ::std::string& in, const ::std::string& envelope, const ::ZG6000::Callback_ZGSPSecureManager_rsaOpenPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaOpen(in, envelope, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   RSA电子信封拆封
     */
    ::Ice::AsyncResultPtr begin_rsaOpen(const ::std::string& in, const ::std::string& envelope, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPSecureManager_rsaOpenPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_rsaOpen(in, envelope, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_rsaOpen.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_rsaOpen(::std::string& output, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_rsaOpen(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_rsaOpen(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPSecureManager : virtual public ZGServerBase
{
public:

    typedef ZGSPSecureManagerPrx ProxyType;
    typedef ZGSPSecureManagerPtr PointerType;

    virtual ~ZGSPSecureManager();

#ifdef ICE_CPP11_COMPILER
    ZGSPSecureManager() = default;
    ZGSPSecureManager(const ZGSPSecureManager&) = default;
    ZGSPSecureManager& operator=(const ZGSPSecureManager&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param in 原始数据
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   AES加密
     */
    virtual bool aesEncrypt(const ::std::string& in, ::std::string& output, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_aesEncrypt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param in 加密数据
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   AES解密
     */
    virtual bool aesDecrypt(const ::std::string& in, ::std::string& output, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_aesDecrypt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param in 原始数据
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA加密
     */
    virtual bool rsaEncrypt(const ::std::string& in, ::std::string& output, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaEncrypt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param in 加密数据
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA解密
     */
    virtual bool rsaDecrypt(const ::std::string& in, ::std::string& output, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaDecrypt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param in 消息内容
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA签名
     */
    virtual bool rsaSign(const ::std::string& in, ::std::string& sign, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaSign(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param in 消息内容
     * @param sign 签名
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA验签
     */
    virtual bool rsaVerify(const ::std::string& in, const ::std::string& sign, bool& result, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaVerify(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param in 原始数据
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA电子信封密封
     */
    virtual bool rsaSeal(const ::std::string& in, ::std::string& output, ::std::string& envelope, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaSeal(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param in 加密数据
     * @param envelope 信封
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief   RSA电子信封拆封
     */
    virtual bool rsaOpen(const ::std::string& in, const ::std::string& envelope, ::std::string& output, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_rsaOpen(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPSecureManager& lhs, const ZGSPSecureManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPSecureManager& lhs, const ZGSPSecureManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_aesEncrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_aesEncrypt.
 */
template<class T>
class CallbackNC_ZGSPSecureManager_aesEncrypt : public Callback_ZGSPSecureManager_aesEncrypt_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPSecureManager_aesEncrypt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_aesEncrypt(iceP_output, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_output, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_aesEncrypt.
 */
template<class T> Callback_ZGSPSecureManager_aesEncryptPtr
newCallback_ZGSPSecureManager_aesEncrypt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_aesEncrypt<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_aesEncrypt.
 */
template<class T> Callback_ZGSPSecureManager_aesEncryptPtr
newCallback_ZGSPSecureManager_aesEncrypt(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_aesEncrypt<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_aesEncrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_aesEncrypt.
 */
template<class T, typename CT>
class Callback_ZGSPSecureManager_aesEncrypt : public Callback_ZGSPSecureManager_aesEncrypt_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPSecureManager_aesEncrypt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_aesEncrypt(iceP_output, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_output, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_aesEncrypt.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_aesEncryptPtr
newCallback_ZGSPSecureManager_aesEncrypt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_aesEncrypt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_aesEncrypt.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_aesEncryptPtr
newCallback_ZGSPSecureManager_aesEncrypt(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_aesEncrypt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_aesDecrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_aesDecrypt.
 */
template<class T>
class CallbackNC_ZGSPSecureManager_aesDecrypt : public Callback_ZGSPSecureManager_aesDecrypt_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPSecureManager_aesDecrypt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_aesDecrypt(iceP_output, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_output, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_aesDecrypt.
 */
template<class T> Callback_ZGSPSecureManager_aesDecryptPtr
newCallback_ZGSPSecureManager_aesDecrypt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_aesDecrypt<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_aesDecrypt.
 */
template<class T> Callback_ZGSPSecureManager_aesDecryptPtr
newCallback_ZGSPSecureManager_aesDecrypt(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_aesDecrypt<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_aesDecrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_aesDecrypt.
 */
template<class T, typename CT>
class Callback_ZGSPSecureManager_aesDecrypt : public Callback_ZGSPSecureManager_aesDecrypt_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPSecureManager_aesDecrypt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_aesDecrypt(iceP_output, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_output, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_aesDecrypt.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_aesDecryptPtr
newCallback_ZGSPSecureManager_aesDecrypt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_aesDecrypt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_aesDecrypt.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_aesDecryptPtr
newCallback_ZGSPSecureManager_aesDecrypt(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_aesDecrypt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaEncrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaEncrypt.
 */
template<class T>
class CallbackNC_ZGSPSecureManager_rsaEncrypt : public Callback_ZGSPSecureManager_rsaEncrypt_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPSecureManager_rsaEncrypt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaEncrypt(iceP_output, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_output, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaEncrypt.
 */
template<class T> Callback_ZGSPSecureManager_rsaEncryptPtr
newCallback_ZGSPSecureManager_rsaEncrypt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaEncrypt<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaEncrypt.
 */
template<class T> Callback_ZGSPSecureManager_rsaEncryptPtr
newCallback_ZGSPSecureManager_rsaEncrypt(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaEncrypt<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaEncrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaEncrypt.
 */
template<class T, typename CT>
class Callback_ZGSPSecureManager_rsaEncrypt : public Callback_ZGSPSecureManager_rsaEncrypt_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPSecureManager_rsaEncrypt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaEncrypt(iceP_output, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_output, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaEncrypt.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaEncryptPtr
newCallback_ZGSPSecureManager_rsaEncrypt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaEncrypt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaEncrypt.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaEncryptPtr
newCallback_ZGSPSecureManager_rsaEncrypt(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaEncrypt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaDecrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaDecrypt.
 */
template<class T>
class CallbackNC_ZGSPSecureManager_rsaDecrypt : public Callback_ZGSPSecureManager_rsaDecrypt_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPSecureManager_rsaDecrypt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaDecrypt(iceP_output, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_output, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaDecrypt.
 */
template<class T> Callback_ZGSPSecureManager_rsaDecryptPtr
newCallback_ZGSPSecureManager_rsaDecrypt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaDecrypt<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaDecrypt.
 */
template<class T> Callback_ZGSPSecureManager_rsaDecryptPtr
newCallback_ZGSPSecureManager_rsaDecrypt(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaDecrypt<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaDecrypt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaDecrypt.
 */
template<class T, typename CT>
class Callback_ZGSPSecureManager_rsaDecrypt : public Callback_ZGSPSecureManager_rsaDecrypt_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPSecureManager_rsaDecrypt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaDecrypt(iceP_output, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_output, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaDecrypt.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaDecryptPtr
newCallback_ZGSPSecureManager_rsaDecrypt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaDecrypt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaDecrypt.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaDecryptPtr
newCallback_ZGSPSecureManager_rsaDecrypt(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaDecrypt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSign.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaSign.
 */
template<class T>
class CallbackNC_ZGSPSecureManager_rsaSign : public Callback_ZGSPSecureManager_rsaSign_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPSecureManager_rsaSign(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_sign;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaSign(iceP_sign, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_sign, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSign.
 */
template<class T> Callback_ZGSPSecureManager_rsaSignPtr
newCallback_ZGSPSecureManager_rsaSign(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaSign<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSign.
 */
template<class T> Callback_ZGSPSecureManager_rsaSignPtr
newCallback_ZGSPSecureManager_rsaSign(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaSign<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSign.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaSign.
 */
template<class T, typename CT>
class Callback_ZGSPSecureManager_rsaSign : public Callback_ZGSPSecureManager_rsaSign_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPSecureManager_rsaSign(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_sign;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaSign(iceP_sign, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_sign, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSign.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaSignPtr
newCallback_ZGSPSecureManager_rsaSign(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaSign<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSign.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaSignPtr
newCallback_ZGSPSecureManager_rsaSign(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaSign<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaVerify.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaVerify.
 */
template<class T>
class CallbackNC_ZGSPSecureManager_rsaVerify : public Callback_ZGSPSecureManager_rsaVerify_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&);

    CallbackNC_ZGSPSecureManager_rsaVerify(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        bool iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaVerify(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_result, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaVerify.
 */
template<class T> Callback_ZGSPSecureManager_rsaVerifyPtr
newCallback_ZGSPSecureManager_rsaVerify(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaVerify<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaVerify.
 */
template<class T> Callback_ZGSPSecureManager_rsaVerifyPtr
newCallback_ZGSPSecureManager_rsaVerify(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaVerify<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaVerify.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaVerify.
 */
template<class T, typename CT>
class Callback_ZGSPSecureManager_rsaVerify : public Callback_ZGSPSecureManager_rsaVerify_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&, const CT&);

    Callback_ZGSPSecureManager_rsaVerify(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        bool iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaVerify(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_result, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaVerify.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaVerifyPtr
newCallback_ZGSPSecureManager_rsaVerify(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaVerify<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaVerify.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaVerifyPtr
newCallback_ZGSPSecureManager_rsaVerify(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaVerify<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSeal.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaSeal.
 */
template<class T>
class CallbackNC_ZGSPSecureManager_rsaSeal : public Callback_ZGSPSecureManager_rsaSeal_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPSecureManager_rsaSeal(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ::std::string iceP_envelope;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaSeal(iceP_output, iceP_envelope, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_output, iceP_envelope, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSeal.
 */
template<class T> Callback_ZGSPSecureManager_rsaSealPtr
newCallback_ZGSPSecureManager_rsaSeal(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaSeal<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSeal.
 */
template<class T> Callback_ZGSPSecureManager_rsaSealPtr
newCallback_ZGSPSecureManager_rsaSeal(T* instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaSeal<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSeal.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaSeal.
 */
template<class T, typename CT>
class Callback_ZGSPSecureManager_rsaSeal : public Callback_ZGSPSecureManager_rsaSeal_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPSecureManager_rsaSeal(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ::std::string iceP_envelope;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaSeal(iceP_output, iceP_envelope, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_output, iceP_envelope, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSeal.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaSealPtr
newCallback_ZGSPSecureManager_rsaSeal(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaSeal<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaSeal.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaSealPtr
newCallback_ZGSPSecureManager_rsaSeal(T* instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaSeal<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaOpen.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaOpen.
 */
template<class T>
class CallbackNC_ZGSPSecureManager_rsaOpen : public Callback_ZGSPSecureManager_rsaOpen_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPSecureManager_rsaOpen(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaOpen(iceP_output, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_output, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaOpen.
 */
template<class T> Callback_ZGSPSecureManager_rsaOpenPtr
newCallback_ZGSPSecureManager_rsaOpen(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaOpen<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaOpen.
 */
template<class T> Callback_ZGSPSecureManager_rsaOpenPtr
newCallback_ZGSPSecureManager_rsaOpen(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPSecureManager_rsaOpen<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPSecureManager::begin_rsaOpen.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPSecureManager_rsaOpen.
 */
template<class T, typename CT>
class Callback_ZGSPSecureManager_rsaOpen : public Callback_ZGSPSecureManager_rsaOpen_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPSecureManager_rsaOpen(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPSecureManagerPrx proxy = ZGSPSecureManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_output;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_rsaOpen(iceP_output, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_output, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaOpen.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaOpenPtr
newCallback_ZGSPSecureManager_rsaOpen(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaOpen<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPSecureManager::begin_rsaOpen.
 */
template<class T, typename CT> Callback_ZGSPSecureManager_rsaOpenPtr
newCallback_ZGSPSecureManager_rsaOpen(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPSecureManager_rsaOpen<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

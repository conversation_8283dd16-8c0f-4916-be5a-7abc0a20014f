//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPRegionManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGMPRegionManager.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPRegionManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPRegionManager",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGMPRegionManager_ops[] =
{
    "checkState",
    "clearRegionPeople",
    "closeAlarm",
    "dispatchData",
    "exitApp",
    "getRegionAccess",
    "getRegionList",
    "getRegionPeople",
    "getRegionYv",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resetWarn",
    "resumeDebug",
    "setupAlarm",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGMPRegionManager_getRegionList_name = "getRegionList";
const ::std::string iceC_ZG6000_ZGMPRegionManager_getRegionAccess_name = "getRegionAccess";
const ::std::string iceC_ZG6000_ZGMPRegionManager_getRegionPeople_name = "getRegionPeople";
const ::std::string iceC_ZG6000_ZGMPRegionManager_getRegionYv_name = "getRegionYv";
const ::std::string iceC_ZG6000_ZGMPRegionManager_clearRegionPeople_name = "clearRegionPeople";
const ::std::string iceC_ZG6000_ZGMPRegionManager_resetWarn_name = "resetWarn";
const ::std::string iceC_ZG6000_ZGMPRegionManager_setupAlarm_name = "setupAlarm";
const ::std::string iceC_ZG6000_ZGMPRegionManager_closeAlarm_name = "closeAlarm";

}

bool
ZG6000::ZGMPRegionManager::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPRegionManager_ids, iceC_ZG6000_ZGMPRegionManager_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGMPRegionManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGMPRegionManager_ids[0], &iceC_ZG6000_ZGMPRegionManager_ids[3]);
}

::std::string
ZG6000::ZGMPRegionManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPRegionManager::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGMPRegionManager";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_getRegionList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_param;
    istr->readAll(iceP_param);
    inS.endReadParams();
    ListStringMap iceP_listRegion;
    ErrorInfo iceP_e;
    bool ret = this->getRegionList(::std::move(iceP_param), iceP_listRegion, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listRegion, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_getRegionAccess(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->readAll(iceP_regionID);
    inS.endReadParams();
    ListStringMap iceP_ListAccess;
    ErrorInfo iceP_e;
    bool ret = this->getRegionAccess(::std::move(iceP_regionID), iceP_ListAccess, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_ListAccess, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_getRegionPeople(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->readAll(iceP_regionID);
    inS.endReadParams();
    ListStringMap iceP_listPeople;
    ErrorInfo iceP_e;
    bool ret = this->getRegionPeople(::std::move(iceP_regionID), iceP_listPeople, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listPeople, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_getRegionYv(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->readAll(iceP_regionID);
    inS.endReadParams();
    ListStringMap iceP_listYv;
    ErrorInfo iceP_e;
    bool ret = this->getRegionYv(::std::move(iceP_regionID), iceP_listYv, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listYv, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_clearRegionPeople(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->readAll(iceP_regionID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->clearRegionPeople(::std::move(iceP_regionID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_resetWarn(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->readAll(iceP_regionID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->resetWarn(::std::move(iceP_regionID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_setupAlarm(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->readAll(iceP_regionID);
    inS.endReadParams();
    this->setupAlarm(::std::move(iceP_regionID), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_closeAlarm(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->readAll(iceP_regionID);
    inS.endReadParams();
    this->closeAlarm(::std::move(iceP_regionID), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPRegionManager_ops, iceC_ZG6000_ZGMPRegionManager_ops + 23, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPRegionManager_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_clearRegionPeople(in, current);
        }
        case 2:
        {
            return _iceD_closeAlarm(in, current);
        }
        case 3:
        {
            return _iceD_dispatchData(in, current);
        }
        case 4:
        {
            return _iceD_exitApp(in, current);
        }
        case 5:
        {
            return _iceD_getRegionAccess(in, current);
        }
        case 6:
        {
            return _iceD_getRegionList(in, current);
        }
        case 7:
        {
            return _iceD_getRegionPeople(in, current);
        }
        case 8:
        {
            return _iceD_getRegionYv(in, current);
        }
        case 9:
        {
            return _iceD_getVersion(in, current);
        }
        case 10:
        {
            return _iceD_heartDebug(in, current);
        }
        case 11:
        {
            return _iceD_ice_id(in, current);
        }
        case 12:
        {
            return _iceD_ice_ids(in, current);
        }
        case 13:
        {
            return _iceD_ice_isA(in, current);
        }
        case 14:
        {
            return _iceD_ice_ping(in, current);
        }
        case 15:
        {
            return _iceD_isDebugging(in, current);
        }
        case 16:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 17:
        {
            return _iceD_resetWarn(in, current);
        }
        case 18:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 19:
        {
            return _iceD_setupAlarm(in, current);
        }
        case 20:
        {
            return _iceD_startDebug(in, current);
        }
        case 21:
        {
            return _iceD_stopDebug(in, current);
        }
        case 22:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPRegionManagerPrx::_iceI_getRegionList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::GetRegionListResult>>& outAsync, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_getRegionList_name);
    outAsync->invoke(iceC_ZG6000_ZGMPRegionManager_getRegionList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPRegionManager::GetRegionListResult v;
            istr->readAll(v.listRegion, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPRegionManagerPrx::_iceI_getRegionAccess(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::GetRegionAccessResult>>& outAsync, const ::std::string& iceP_regionID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_getRegionAccess_name);
    outAsync->invoke(iceC_ZG6000_ZGMPRegionManager_getRegionAccess_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_regionID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPRegionManager::GetRegionAccessResult v;
            istr->readAll(v.ListAccess, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPRegionManagerPrx::_iceI_getRegionPeople(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::GetRegionPeopleResult>>& outAsync, const ::std::string& iceP_regionID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_getRegionPeople_name);
    outAsync->invoke(iceC_ZG6000_ZGMPRegionManager_getRegionPeople_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_regionID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPRegionManager::GetRegionPeopleResult v;
            istr->readAll(v.listPeople, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPRegionManagerPrx::_iceI_getRegionYv(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::GetRegionYvResult>>& outAsync, const ::std::string& iceP_regionID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_getRegionYv_name);
    outAsync->invoke(iceC_ZG6000_ZGMPRegionManager_getRegionYv_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_regionID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPRegionManager::GetRegionYvResult v;
            istr->readAll(v.listYv, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPRegionManagerPrx::_iceI_clearRegionPeople(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::ClearRegionPeopleResult>>& outAsync, const ::std::string& iceP_regionID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_clearRegionPeople_name);
    outAsync->invoke(iceC_ZG6000_ZGMPRegionManager_clearRegionPeople_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_regionID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPRegionManager::ClearRegionPeopleResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPRegionManagerPrx::_iceI_resetWarn(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRegionManager::ResetWarnResult>>& outAsync, const ::std::string& iceP_regionID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_resetWarn_name);
    outAsync->invoke(iceC_ZG6000_ZGMPRegionManager_resetWarn_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_regionID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPRegionManager::ResetWarnResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPRegionManagerPrx::_iceI_setupAlarm(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_regionID, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGMPRegionManager_setupAlarm_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_regionID);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPRegionManagerPrx::_iceI_closeAlarm(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_regionID, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGMPRegionManager_closeAlarm_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_regionID);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGMPRegionManagerPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGMPRegionManagerPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGMPRegionManagerPrx::ice_staticId()
{
    return ZGMPRegionManager::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPRegionManager_getRegionList_name = "getRegionList";

const ::std::string iceC_ZG6000_ZGMPRegionManager_getRegionAccess_name = "getRegionAccess";

const ::std::string iceC_ZG6000_ZGMPRegionManager_getRegionPeople_name = "getRegionPeople";

const ::std::string iceC_ZG6000_ZGMPRegionManager_getRegionYv_name = "getRegionYv";

const ::std::string iceC_ZG6000_ZGMPRegionManager_clearRegionPeople_name = "clearRegionPeople";

const ::std::string iceC_ZG6000_ZGMPRegionManager_resetWarn_name = "resetWarn";

const ::std::string iceC_ZG6000_ZGMPRegionManager_setupAlarm_name = "setupAlarm";

const ::std::string iceC_ZG6000_ZGMPRegionManager_closeAlarm_name = "closeAlarm";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGMPRegionManager* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGMPRegionManager>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGMPRegionManager;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPRegionManager::_iceI_begin_getRegionList(const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_getRegionList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPRegionManager_getRegionList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPRegionManager_getRegionList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPRegionManager_getRegionList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPRegionManager::end_getRegionList(::ZG6000::ListStringMap& iceP_listRegion, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_getRegionList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listRegion);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPRegionManager::_iceI_end_getRegionList(::ZG6000::ListStringMap& iceP_listRegion, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_getRegionList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listRegion);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPRegionManager::_iceI_begin_getRegionAccess(const ::std::string& iceP_regionID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_getRegionAccess_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPRegionManager_getRegionAccess_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPRegionManager_getRegionAccess_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_regionID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPRegionManager_getRegionAccess_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPRegionManager::end_getRegionAccess(::ZG6000::ListStringMap& iceP_ListAccess, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_getRegionAccess_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_ListAccess);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPRegionManager::_iceI_end_getRegionAccess(::ZG6000::ListStringMap& iceP_ListAccess, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_getRegionAccess_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_ListAccess);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPRegionManager::_iceI_begin_getRegionPeople(const ::std::string& iceP_regionID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_getRegionPeople_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPRegionManager_getRegionPeople_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPRegionManager_getRegionPeople_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_regionID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPRegionManager_getRegionPeople_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPRegionManager::end_getRegionPeople(::ZG6000::ListStringMap& iceP_listPeople, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_getRegionPeople_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listPeople);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPRegionManager::_iceI_end_getRegionPeople(::ZG6000::ListStringMap& iceP_listPeople, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_getRegionPeople_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listPeople);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPRegionManager::_iceI_begin_getRegionYv(const ::std::string& iceP_regionID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_getRegionYv_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPRegionManager_getRegionYv_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPRegionManager_getRegionYv_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_regionID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPRegionManager_getRegionYv_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPRegionManager::end_getRegionYv(::ZG6000::ListStringMap& iceP_listYv, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_getRegionYv_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listYv);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPRegionManager::_iceI_end_getRegionYv(::ZG6000::ListStringMap& iceP_listYv, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_getRegionYv_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listYv);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPRegionManager::_iceI_begin_clearRegionPeople(const ::std::string& iceP_regionID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_clearRegionPeople_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPRegionManager_clearRegionPeople_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPRegionManager_clearRegionPeople_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_regionID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPRegionManager_clearRegionPeople_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPRegionManager::end_clearRegionPeople(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_clearRegionPeople_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPRegionManager::_iceI_end_clearRegionPeople(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_clearRegionPeople_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPRegionManager::_iceI_begin_resetWarn(const ::std::string& iceP_regionID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRegionManager_resetWarn_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPRegionManager_resetWarn_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPRegionManager_resetWarn_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_regionID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPRegionManager_resetWarn_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPRegionManager::end_resetWarn(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_resetWarn_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPRegionManager::_iceI_end_resetWarn(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRegionManager_resetWarn_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPRegionManager::_iceI_begin_setupAlarm(const ::std::string& iceP_regionID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPRegionManager_setupAlarm_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPRegionManager_setupAlarm_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_regionID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPRegionManager_setupAlarm_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGMPRegionManager::end_setupAlarm(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGMPRegionManager_setupAlarm_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPRegionManager::_iceI_begin_closeAlarm(const ::std::string& iceP_regionID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPRegionManager_closeAlarm_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPRegionManager_closeAlarm_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_regionID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPRegionManager_closeAlarm_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGMPRegionManager::end_closeAlarm(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGMPRegionManager_closeAlarm_name);
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGMPRegionManager::_newInstance() const
{
    return new ZGMPRegionManager;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGMPRegionManager::ice_staticId()
{
    return ::ZG6000::ZGMPRegionManager::ice_staticId();
}

ZG6000::ZGMPRegionManager::~ZGMPRegionManager()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGMPRegionManager* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPRegionManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPRegionManager",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGMPRegionManager::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPRegionManager_ids, iceC_ZG6000_ZGMPRegionManager_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGMPRegionManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGMPRegionManager_ids[0], &iceC_ZG6000_ZGMPRegionManager_ids[3]);
}

const ::std::string&
ZG6000::ZGMPRegionManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPRegionManager::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGMPRegionManager";
    return typeId;
#else
    return iceC_ZG6000_ZGMPRegionManager_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_getRegionList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_param;
    istr->read(iceP_param);
    inS.endReadParams();
    ListStringMap iceP_listRegion;
    ErrorInfo iceP_e;
    bool ret = this->getRegionList(iceP_param, iceP_listRegion, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listRegion);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_getRegionAccess(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->read(iceP_regionID);
    inS.endReadParams();
    ListStringMap iceP_ListAccess;
    ErrorInfo iceP_e;
    bool ret = this->getRegionAccess(iceP_regionID, iceP_ListAccess, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_ListAccess);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_getRegionPeople(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->read(iceP_regionID);
    inS.endReadParams();
    ListStringMap iceP_listPeople;
    ErrorInfo iceP_e;
    bool ret = this->getRegionPeople(iceP_regionID, iceP_listPeople, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listPeople);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_getRegionYv(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->read(iceP_regionID);
    inS.endReadParams();
    ListStringMap iceP_listYv;
    ErrorInfo iceP_e;
    bool ret = this->getRegionYv(iceP_regionID, iceP_listYv, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listYv);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_clearRegionPeople(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->read(iceP_regionID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->clearRegionPeople(iceP_regionID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_resetWarn(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->read(iceP_regionID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->resetWarn(iceP_regionID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_setupAlarm(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->read(iceP_regionID);
    inS.endReadParams();
    this->setupAlarm(iceP_regionID, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceD_closeAlarm(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_regionID;
    istr->read(iceP_regionID);
    inS.endReadParams();
    this->closeAlarm(iceP_regionID, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPRegionManager_all[] =
{
    "checkState",
    "clearRegionPeople",
    "closeAlarm",
    "dispatchData",
    "exitApp",
    "getRegionAccess",
    "getRegionList",
    "getRegionPeople",
    "getRegionYv",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resetWarn",
    "resumeDebug",
    "setupAlarm",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGMPRegionManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPRegionManager_all, iceC_ZG6000_ZGMPRegionManager_all + 23, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPRegionManager_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_clearRegionPeople(in, current);
        }
        case 2:
        {
            return _iceD_closeAlarm(in, current);
        }
        case 3:
        {
            return _iceD_dispatchData(in, current);
        }
        case 4:
        {
            return _iceD_exitApp(in, current);
        }
        case 5:
        {
            return _iceD_getRegionAccess(in, current);
        }
        case 6:
        {
            return _iceD_getRegionList(in, current);
        }
        case 7:
        {
            return _iceD_getRegionPeople(in, current);
        }
        case 8:
        {
            return _iceD_getRegionYv(in, current);
        }
        case 9:
        {
            return _iceD_getVersion(in, current);
        }
        case 10:
        {
            return _iceD_heartDebug(in, current);
        }
        case 11:
        {
            return _iceD_ice_id(in, current);
        }
        case 12:
        {
            return _iceD_ice_ids(in, current);
        }
        case 13:
        {
            return _iceD_ice_isA(in, current);
        }
        case 14:
        {
            return _iceD_ice_ping(in, current);
        }
        case 15:
        {
            return _iceD_isDebugging(in, current);
        }
        case 16:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 17:
        {
            return _iceD_resetWarn(in, current);
        }
        case 18:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 19:
        {
            return _iceD_setupAlarm(in, current);
        }
        case 20:
        {
            return _iceD_startDebug(in, current);
        }
        case 21:
        {
            return _iceD_stopDebug(in, current);
        }
        case 22:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGMPRegionManager::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGMPRegionManager, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGMPRegionManager::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGMPRegionManager, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGMPRegionManagerPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGMPRegionManagerPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGMPRegionManager::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

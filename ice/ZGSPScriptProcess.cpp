//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPScriptProcess.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSPScriptProcess.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPScriptProcess_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPScriptProcess",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSPScriptProcess_ops[] =
{
    "callBatch",
    "callJson",
    "callToBool",
    "callToDouble",
    "callToInt",
    "callToString",
    "checkState",
    "dispatchData",
    "evaluateByJson",
    "evaluateByList",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "invoke",
    "invokeOneway",
    "invokeToBool",
    "invokeToDouble",
    "invokeToInt",
    "invokeToString",
    "invokeToStringList",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGSPScriptProcess_evaluateByJson_name = "evaluateByJson";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_evaluateByList_name = "evaluateByList";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeOneway_name = "invokeOneway";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_invoke_name = "invoke";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeToBool_name = "invokeToBool";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeToInt_name = "invokeToInt";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeToDouble_name = "invokeToDouble";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeToString_name = "invokeToString";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeToStringList_name = "invokeToStringList";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_callBatch_name = "callBatch";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_callJson_name = "callJson";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_callToBool_name = "callToBool";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_callToInt_name = "callToInt";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_callToDouble_name = "callToDouble";
const ::std::string iceC_ZG6000_ZGSPScriptProcess_callToString_name = "callToString";

}

bool
ZG6000::ZGSPScriptProcess::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPScriptProcess_ids, iceC_ZG6000_ZGSPScriptProcess_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSPScriptProcess::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSPScriptProcess_ids[0], &iceC_ZG6000_ZGSPScriptProcess_ids[3]);
}

::std::string
ZG6000::ZGSPScriptProcess::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPScriptProcess::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSPScriptProcess";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_evaluateByJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_funcName;
    ::std::string iceP_jsonParam;
    ::std::string iceP_script;
    istr->readAll(iceP_funcName, iceP_jsonParam, iceP_script);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->evaluateByJson(::std::move(iceP_funcName), ::std::move(iceP_jsonParam), ::std::move(iceP_script), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_evaluateByList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_funcName;
    StringList iceP_listParam;
    ::std::string iceP_script;
    istr->readAll(iceP_funcName, iceP_listParam, iceP_script);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->evaluateByList(::std::move(iceP_funcName), ::std::move(iceP_listParam), ::std::move(iceP_script), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listRuleID;
    istr->readAll(iceP_listRuleID);
    inS.endReadParams();
    this->invokeOneway(::std::move(iceP_listRuleID), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invoke(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->readAll(iceP_ruleID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->invoke(::std::move(iceP_ruleID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeToBool(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->readAll(iceP_ruleID);
    inS.endReadParams();
    bool iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->invokeToBool(::std::move(iceP_ruleID), iceP_result, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_result, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeToInt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->readAll(iceP_ruleID);
    inS.endReadParams();
    int iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->invokeToInt(::std::move(iceP_ruleID), iceP_result, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_result, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeToDouble(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->readAll(iceP_ruleID);
    inS.endReadParams();
    double iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->invokeToDouble(::std::move(iceP_ruleID), iceP_result, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_result, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeToString(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->readAll(iceP_ruleID);
    inS.endReadParams();
    ::std::string iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->invokeToString(::std::move(iceP_ruleID), iceP_result, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_result, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeToStringList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->readAll(iceP_ruleID);
    inS.endReadParams();
    StringList iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->invokeToStringList(::std::move(iceP_ruleID), iceP_result, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_result, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listExpress;
    istr->readAll(iceP_listExpress);
    inS.endReadParams();
    this->callBatch(::std::move(iceP_listExpress), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_expressID;
    ::std::string iceP_jsonParam;
    istr->readAll(iceP_expressID, iceP_jsonParam);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->callJson(::std::move(iceP_expressID), ::std::move(iceP_jsonParam), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callToBool(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_expressID;
    ::std::string iceP_jsonParam;
    istr->readAll(iceP_expressID, iceP_jsonParam);
    inS.endReadParams();
    bool iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->callToBool(::std::move(iceP_expressID), ::std::move(iceP_jsonParam), iceP_result, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_result, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callToInt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_expressID;
    ::std::string iceP_jsonParam;
    istr->readAll(iceP_expressID, iceP_jsonParam);
    inS.endReadParams();
    int iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->callToInt(::std::move(iceP_expressID), ::std::move(iceP_jsonParam), iceP_result, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_result, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callToDouble(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_expressID;
    ::std::string iceP_jsonParam;
    istr->readAll(iceP_expressID, iceP_jsonParam);
    inS.endReadParams();
    double iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->callToDouble(::std::move(iceP_expressID), ::std::move(iceP_jsonParam), iceP_result, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_result, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callToString(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_expressID;
    ::std::string iceP_jsonParam;
    istr->readAll(iceP_expressID, iceP_jsonParam);
    inS.endReadParams();
    ::std::string iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->callToString(::std::move(iceP_expressID), ::std::move(iceP_jsonParam), iceP_result, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_result, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPScriptProcess_ops, iceC_ZG6000_ZGSPScriptProcess_ops + 30, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPScriptProcess_ops)
    {
        case 0:
        {
            return _iceD_callBatch(in, current);
        }
        case 1:
        {
            return _iceD_callJson(in, current);
        }
        case 2:
        {
            return _iceD_callToBool(in, current);
        }
        case 3:
        {
            return _iceD_callToDouble(in, current);
        }
        case 4:
        {
            return _iceD_callToInt(in, current);
        }
        case 5:
        {
            return _iceD_callToString(in, current);
        }
        case 6:
        {
            return _iceD_checkState(in, current);
        }
        case 7:
        {
            return _iceD_dispatchData(in, current);
        }
        case 8:
        {
            return _iceD_evaluateByJson(in, current);
        }
        case 9:
        {
            return _iceD_evaluateByList(in, current);
        }
        case 10:
        {
            return _iceD_exitApp(in, current);
        }
        case 11:
        {
            return _iceD_getVersion(in, current);
        }
        case 12:
        {
            return _iceD_heartDebug(in, current);
        }
        case 13:
        {
            return _iceD_ice_id(in, current);
        }
        case 14:
        {
            return _iceD_ice_ids(in, current);
        }
        case 15:
        {
            return _iceD_ice_isA(in, current);
        }
        case 16:
        {
            return _iceD_ice_ping(in, current);
        }
        case 17:
        {
            return _iceD_invoke(in, current);
        }
        case 18:
        {
            return _iceD_invokeOneway(in, current);
        }
        case 19:
        {
            return _iceD_invokeToBool(in, current);
        }
        case 20:
        {
            return _iceD_invokeToDouble(in, current);
        }
        case 21:
        {
            return _iceD_invokeToInt(in, current);
        }
        case 22:
        {
            return _iceD_invokeToString(in, current);
        }
        case 23:
        {
            return _iceD_invokeToStringList(in, current);
        }
        case 24:
        {
            return _iceD_isDebugging(in, current);
        }
        case 25:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 26:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 27:
        {
            return _iceD_startDebug(in, current);
        }
        case 28:
        {
            return _iceD_stopDebug(in, current);
        }
        case 29:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_evaluateByJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::EvaluateByJsonResult>>& outAsync, const ::std::string& iceP_funcName, const ::std::string& iceP_jsonParam, const ::std::string& iceP_script, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_evaluateByJson_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_evaluateByJson_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_funcName, iceP_jsonParam, iceP_script);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::EvaluateByJsonResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_evaluateByList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::EvaluateByListResult>>& outAsync, const ::std::string& iceP_funcName, const StringList& iceP_listParam, const ::std::string& iceP_script, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_evaluateByList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_evaluateByList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_funcName, iceP_listParam, iceP_script);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::EvaluateByListResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_invokeOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const StringList& iceP_listRuleID, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeOneway_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listRuleID);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_invoke(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeResult>>& outAsync, const ::std::string& iceP_ruleID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invoke_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_invoke_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_ruleID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::InvokeResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_invokeToBool(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeToBoolResult>>& outAsync, const ::std::string& iceP_ruleID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invokeToBool_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeToBool_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_ruleID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::InvokeToBoolResult v;
            istr->readAll(v.result, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_invokeToInt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeToIntResult>>& outAsync, const ::std::string& iceP_ruleID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invokeToInt_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeToInt_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_ruleID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::InvokeToIntResult v;
            istr->readAll(v.result, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_invokeToDouble(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeToDoubleResult>>& outAsync, const ::std::string& iceP_ruleID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invokeToDouble_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeToDouble_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_ruleID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::InvokeToDoubleResult v;
            istr->readAll(v.result, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_invokeToString(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeToStringResult>>& outAsync, const ::std::string& iceP_ruleID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invokeToString_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeToString_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_ruleID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::InvokeToStringResult v;
            istr->readAll(v.result, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_invokeToStringList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeToStringListResult>>& outAsync, const ::std::string& iceP_ruleID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invokeToStringList_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeToStringList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_ruleID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::InvokeToStringListResult v;
            istr->readAll(v.result, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_callBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ListStringMap& iceP_listExpress, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_callBatch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listExpress);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_callJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::CallJsonResult>>& outAsync, const ::std::string& iceP_expressID, const ::std::string& iceP_jsonParam, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_callJson_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_callJson_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_expressID, iceP_jsonParam);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::CallJsonResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_callToBool(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::CallToBoolResult>>& outAsync, const ::std::string& iceP_expressID, const ::std::string& iceP_jsonParam, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_callToBool_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_callToBool_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_expressID, iceP_jsonParam);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::CallToBoolResult v;
            istr->readAll(v.result, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_callToInt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::CallToIntResult>>& outAsync, const ::std::string& iceP_expressID, const ::std::string& iceP_jsonParam, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_callToInt_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_callToInt_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_expressID, iceP_jsonParam);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::CallToIntResult v;
            istr->readAll(v.result, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_callToDouble(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::CallToDoubleResult>>& outAsync, const ::std::string& iceP_expressID, const ::std::string& iceP_jsonParam, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_callToDouble_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_callToDouble_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_expressID, iceP_jsonParam);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::CallToDoubleResult v;
            istr->readAll(v.result, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPScriptProcessPrx::_iceI_callToString(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::CallToStringResult>>& outAsync, const ::std::string& iceP_expressID, const ::std::string& iceP_jsonParam, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_callToString_name);
    outAsync->invoke(iceC_ZG6000_ZGSPScriptProcess_callToString_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_expressID, iceP_jsonParam);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPScriptProcess::CallToStringResult v;
            istr->readAll(v.result, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSPScriptProcessPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSPScriptProcessPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSPScriptProcessPrx::ice_staticId()
{
    return ZGSPScriptProcess::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPScriptProcess_evaluateByJson_name = "evaluateByJson";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_evaluateByList_name = "evaluateByList";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeOneway_name = "invokeOneway";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_invoke_name = "invoke";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeToBool_name = "invokeToBool";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeToInt_name = "invokeToInt";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeToDouble_name = "invokeToDouble";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeToString_name = "invokeToString";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_invokeToStringList_name = "invokeToStringList";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_callBatch_name = "callBatch";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_callJson_name = "callJson";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_callToBool_name = "callToBool";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_callToInt_name = "callToInt";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_callToDouble_name = "callToDouble";

const ::std::string iceC_ZG6000_ZGSPScriptProcess_callToString_name = "callToString";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSPScriptProcess* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSPScriptProcess>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSPScriptProcess;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_evaluateByJson(const ::std::string& iceP_funcName, const ::std::string& iceP_jsonParam, const ::std::string& iceP_script, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_evaluateByJson_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_evaluateByJson_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_evaluateByJson_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_funcName);
        ostr->write(iceP_jsonParam);
        ostr->write(iceP_script);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_evaluateByJson_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_evaluateByJson(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_evaluateByJson_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_evaluateByJson(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_evaluateByJson_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_evaluateByList(const ::std::string& iceP_funcName, const ::ZG6000::StringList& iceP_listParam, const ::std::string& iceP_script, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_evaluateByList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_evaluateByList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_evaluateByList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_funcName);
        ostr->write(iceP_listParam);
        ostr->write(iceP_script);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_evaluateByList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_evaluateByList(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_evaluateByList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_evaluateByList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_evaluateByList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_invokeOneway(const ::ZG6000::StringList& iceP_listRuleID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_invokeOneway_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_invokeOneway_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listRuleID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeOneway_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPScriptProcess::end_invokeOneway(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPScriptProcess_invokeOneway_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_invoke(const ::std::string& iceP_ruleID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invoke_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_invoke_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_invoke_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_ruleID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_invoke_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_invoke(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invoke_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_invoke(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invoke_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_invokeToBool(const ::std::string& iceP_ruleID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invokeToBool_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_invokeToBool_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_invokeToBool_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_ruleID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeToBool_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_invokeToBool(bool& iceP_result, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invokeToBool_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_invokeToBool(bool& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invokeToBool_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_invokeToInt(const ::std::string& iceP_ruleID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invokeToInt_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_invokeToInt_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_invokeToInt_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_ruleID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeToInt_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_invokeToInt(::Ice::Int& iceP_result, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invokeToInt_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_invokeToInt(::Ice::Int& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invokeToInt_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_invokeToDouble(const ::std::string& iceP_ruleID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invokeToDouble_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_invokeToDouble_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_invokeToDouble_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_ruleID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeToDouble_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_invokeToDouble(::Ice::Double& iceP_result, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invokeToDouble_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_invokeToDouble(::Ice::Double& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invokeToDouble_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_invokeToString(const ::std::string& iceP_ruleID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invokeToString_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_invokeToString_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_invokeToString_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_ruleID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeToString_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_invokeToString(::std::string& iceP_result, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invokeToString_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_invokeToString(::std::string& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invokeToString_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_invokeToStringList(const ::std::string& iceP_ruleID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_invokeToStringList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_invokeToStringList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_invokeToStringList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_ruleID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_invokeToStringList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_invokeToStringList(::ZG6000::StringList& iceP_result, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invokeToStringList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_invokeToStringList(::ZG6000::StringList& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_invokeToStringList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_callBatch(const ::ZG6000::ListStringMap& iceP_listExpress, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_callBatch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_callBatch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listExpress);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_callBatch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPScriptProcess::end_callBatch(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPScriptProcess_callBatch_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_callJson(const ::std::string& iceP_expressID, const ::std::string& iceP_jsonParam, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_callJson_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_callJson_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_callJson_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_expressID);
        ostr->write(iceP_jsonParam);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_callJson_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_callJson(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_callJson_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_callJson(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_callJson_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_callToBool(const ::std::string& iceP_expressID, const ::std::string& iceP_jsonParam, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_callToBool_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_callToBool_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_callToBool_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_expressID);
        ostr->write(iceP_jsonParam);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_callToBool_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_callToBool(bool& iceP_result, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_callToBool_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_callToBool(bool& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_callToBool_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_callToInt(const ::std::string& iceP_expressID, const ::std::string& iceP_jsonParam, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_callToInt_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_callToInt_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_callToInt_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_expressID);
        ostr->write(iceP_jsonParam);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_callToInt_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_callToInt(::Ice::Int& iceP_result, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_callToInt_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_callToInt(::Ice::Int& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_callToInt_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_callToDouble(const ::std::string& iceP_expressID, const ::std::string& iceP_jsonParam, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_callToDouble_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_callToDouble_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_callToDouble_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_expressID);
        ostr->write(iceP_jsonParam);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_callToDouble_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_callToDouble(::Ice::Double& iceP_result, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_callToDouble_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_callToDouble(::Ice::Double& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_callToDouble_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPScriptProcess::_iceI_begin_callToString(const ::std::string& iceP_expressID, const ::std::string& iceP_jsonParam, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPScriptProcess_callToString_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPScriptProcess_callToString_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPScriptProcess_callToString_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_expressID);
        ostr->write(iceP_jsonParam);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPScriptProcess_callToString_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPScriptProcess::end_callToString(::std::string& iceP_result, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_callToString_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPScriptProcess::_iceI_end_callToString(::std::string& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPScriptProcess_callToString_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSPScriptProcess::_newInstance() const
{
    return new ZGSPScriptProcess;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSPScriptProcess::ice_staticId()
{
    return ::ZG6000::ZGSPScriptProcess::ice_staticId();
}

ZG6000::ZGSPScriptProcess::~ZGSPScriptProcess()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSPScriptProcess* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPScriptProcess_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPScriptProcess",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSPScriptProcess::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPScriptProcess_ids, iceC_ZG6000_ZGSPScriptProcess_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSPScriptProcess::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSPScriptProcess_ids[0], &iceC_ZG6000_ZGSPScriptProcess_ids[3]);
}

const ::std::string&
ZG6000::ZGSPScriptProcess::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPScriptProcess::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSPScriptProcess";
    return typeId;
#else
    return iceC_ZG6000_ZGSPScriptProcess_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_evaluateByJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_funcName;
    ::std::string iceP_jsonParam;
    ::std::string iceP_script;
    istr->read(iceP_funcName);
    istr->read(iceP_jsonParam);
    istr->read(iceP_script);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->evaluateByJson(iceP_funcName, iceP_jsonParam, iceP_script, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_evaluateByList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_funcName;
    StringList iceP_listParam;
    ::std::string iceP_script;
    istr->read(iceP_funcName);
    istr->read(iceP_listParam);
    istr->read(iceP_script);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->evaluateByList(iceP_funcName, iceP_listParam, iceP_script, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeOneway(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listRuleID;
    istr->read(iceP_listRuleID);
    inS.endReadParams();
    this->invokeOneway(iceP_listRuleID, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invoke(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->read(iceP_ruleID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->invoke(iceP_ruleID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeToBool(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->read(iceP_ruleID);
    inS.endReadParams();
    bool iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->invokeToBool(iceP_ruleID, iceP_result, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_result);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeToInt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->read(iceP_ruleID);
    inS.endReadParams();
    ::Ice::Int iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->invokeToInt(iceP_ruleID, iceP_result, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_result);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeToDouble(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->read(iceP_ruleID);
    inS.endReadParams();
    ::Ice::Double iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->invokeToDouble(iceP_ruleID, iceP_result, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_result);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeToString(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->read(iceP_ruleID);
    inS.endReadParams();
    ::std::string iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->invokeToString(iceP_ruleID, iceP_result, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_result);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_invokeToStringList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_ruleID;
    istr->read(iceP_ruleID);
    inS.endReadParams();
    StringList iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->invokeToStringList(iceP_ruleID, iceP_result, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_result);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listExpress;
    istr->read(iceP_listExpress);
    inS.endReadParams();
    this->callBatch(iceP_listExpress, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callJson(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_expressID;
    ::std::string iceP_jsonParam;
    istr->read(iceP_expressID);
    istr->read(iceP_jsonParam);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->callJson(iceP_expressID, iceP_jsonParam, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callToBool(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_expressID;
    ::std::string iceP_jsonParam;
    istr->read(iceP_expressID);
    istr->read(iceP_jsonParam);
    inS.endReadParams();
    bool iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->callToBool(iceP_expressID, iceP_jsonParam, iceP_result, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_result);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callToInt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_expressID;
    ::std::string iceP_jsonParam;
    istr->read(iceP_expressID);
    istr->read(iceP_jsonParam);
    inS.endReadParams();
    ::Ice::Int iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->callToInt(iceP_expressID, iceP_jsonParam, iceP_result, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_result);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callToDouble(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_expressID;
    ::std::string iceP_jsonParam;
    istr->read(iceP_expressID);
    istr->read(iceP_jsonParam);
    inS.endReadParams();
    ::Ice::Double iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->callToDouble(iceP_expressID, iceP_jsonParam, iceP_result, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_result);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceD_callToString(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_expressID;
    ::std::string iceP_jsonParam;
    istr->read(iceP_expressID);
    istr->read(iceP_jsonParam);
    inS.endReadParams();
    ::std::string iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->callToString(iceP_expressID, iceP_jsonParam, iceP_result, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_result);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPScriptProcess_all[] =
{
    "callBatch",
    "callJson",
    "callToBool",
    "callToDouble",
    "callToInt",
    "callToString",
    "checkState",
    "dispatchData",
    "evaluateByJson",
    "evaluateByList",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "invoke",
    "invokeOneway",
    "invokeToBool",
    "invokeToDouble",
    "invokeToInt",
    "invokeToString",
    "invokeToStringList",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSPScriptProcess::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPScriptProcess_all, iceC_ZG6000_ZGSPScriptProcess_all + 30, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPScriptProcess_all)
    {
        case 0:
        {
            return _iceD_callBatch(in, current);
        }
        case 1:
        {
            return _iceD_callJson(in, current);
        }
        case 2:
        {
            return _iceD_callToBool(in, current);
        }
        case 3:
        {
            return _iceD_callToDouble(in, current);
        }
        case 4:
        {
            return _iceD_callToInt(in, current);
        }
        case 5:
        {
            return _iceD_callToString(in, current);
        }
        case 6:
        {
            return _iceD_checkState(in, current);
        }
        case 7:
        {
            return _iceD_dispatchData(in, current);
        }
        case 8:
        {
            return _iceD_evaluateByJson(in, current);
        }
        case 9:
        {
            return _iceD_evaluateByList(in, current);
        }
        case 10:
        {
            return _iceD_exitApp(in, current);
        }
        case 11:
        {
            return _iceD_getVersion(in, current);
        }
        case 12:
        {
            return _iceD_heartDebug(in, current);
        }
        case 13:
        {
            return _iceD_ice_id(in, current);
        }
        case 14:
        {
            return _iceD_ice_ids(in, current);
        }
        case 15:
        {
            return _iceD_ice_isA(in, current);
        }
        case 16:
        {
            return _iceD_ice_ping(in, current);
        }
        case 17:
        {
            return _iceD_invoke(in, current);
        }
        case 18:
        {
            return _iceD_invokeOneway(in, current);
        }
        case 19:
        {
            return _iceD_invokeToBool(in, current);
        }
        case 20:
        {
            return _iceD_invokeToDouble(in, current);
        }
        case 21:
        {
            return _iceD_invokeToInt(in, current);
        }
        case 22:
        {
            return _iceD_invokeToString(in, current);
        }
        case 23:
        {
            return _iceD_invokeToStringList(in, current);
        }
        case 24:
        {
            return _iceD_isDebugging(in, current);
        }
        case 25:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 26:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 27:
        {
            return _iceD_startDebug(in, current);
        }
        case 28:
        {
            return _iceD_stopDebug(in, current);
        }
        case 29:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSPScriptProcess::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSPScriptProcess, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSPScriptProcess::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSPScriptProcess, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSPScriptProcessPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSPScriptProcessPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSPScriptProcess::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

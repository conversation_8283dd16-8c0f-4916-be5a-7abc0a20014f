//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGDPDeviceManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGDPDeviceManager.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGDPDeviceManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGDPDeviceManager",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGDPDeviceManager_ops[] =
{
    "addDevice",
    "addDeviceResume",
    "changeDeviceState",
    "changePart",
    "checkState",
    "dispatchData",
    "exitApp",
    "getDeviceHistory",
    "getDevicePartHistory",
    "getDeviceResume",
    "getDevices",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "removeDevice",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test",
    "updateDevice"
};
const ::std::string iceC_ZG6000_ZGDPDeviceManager_getDevices_name = "getDevices";
const ::std::string iceC_ZG6000_ZGDPDeviceManager_addDevice_name = "addDevice";
const ::std::string iceC_ZG6000_ZGDPDeviceManager_updateDevice_name = "updateDevice";
const ::std::string iceC_ZG6000_ZGDPDeviceManager_removeDevice_name = "removeDevice";
const ::std::string iceC_ZG6000_ZGDPDeviceManager_changeDeviceState_name = "changeDeviceState";
const ::std::string iceC_ZG6000_ZGDPDeviceManager_changePart_name = "changePart";
const ::std::string iceC_ZG6000_ZGDPDeviceManager_getDeviceHistory_name = "getDeviceHistory";
const ::std::string iceC_ZG6000_ZGDPDeviceManager_getDevicePartHistory_name = "getDevicePartHistory";
const ::std::string iceC_ZG6000_ZGDPDeviceManager_addDeviceResume_name = "addDeviceResume";
const ::std::string iceC_ZG6000_ZGDPDeviceManager_getDeviceResume_name = "getDeviceResume";

}

bool
ZG6000::ZGDPDeviceManager::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGDPDeviceManager_ids, iceC_ZG6000_ZGDPDeviceManager_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGDPDeviceManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGDPDeviceManager_ids[0], &iceC_ZG6000_ZGDPDeviceManager_ids[3]);
}

::std::string
ZG6000::ZGDPDeviceManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGDPDeviceManager::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGDPDeviceManager";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_getDevices(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_condition;
    int iceP_offset;
    int iceP_limit;
    ::std::string iceP_orderField;
    ::std::string iceP_orderType;
    istr->readAll(iceP_condition, iceP_offset, iceP_limit, iceP_orderField, iceP_orderType);
    inS.endReadParams();
    ListStringMap iceP_devices;
    ErrorInfo iceP_e;
    bool ret = this->getDevices(::std::move(iceP_condition), iceP_offset, iceP_limit, ::std::move(iceP_orderField), ::std::move(iceP_orderType), iceP_devices, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_devices, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_addDevice(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_properties;
    istr->readAll(iceP_deviceID, iceP_properties);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addDevice(::std::move(iceP_deviceID), ::std::move(iceP_properties), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_updateDevice(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_properties;
    istr->readAll(iceP_deviceID, iceP_properties);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateDevice(::std::move(iceP_deviceID), ::std::move(iceP_properties), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_removeDevice(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->removeDevice(::std::move(iceP_deviceID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_changeDeviceState(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_params;
    istr->readAll(iceP_deviceID, iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->changeDeviceState(::std::move(iceP_deviceID), ::std::move(iceP_params), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_changePart(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_parentPartID;
    StringMap iceP_partParam;
    istr->readAll(iceP_parentPartID, iceP_partParam);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->changePart(::std::move(iceP_parentPartID), ::std::move(iceP_partParam), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_getDeviceHistory(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_listDeviceHistory;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceHistory(::std::move(iceP_deviceID), iceP_listDeviceHistory, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listDeviceHistory, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_getDevicePartHistory(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_listPartHistory;
    ErrorInfo iceP_e;
    bool ret = this->getDevicePartHistory(::std::move(iceP_deviceID), iceP_listPartHistory, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listPartHistory, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_addDeviceResume(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_deviceResume;
    istr->readAll(iceP_deviceID, iceP_deviceResume);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addDeviceResume(::std::move(iceP_deviceID), ::std::move(iceP_deviceResume), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_getDeviceResume(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_listDeviceResume;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceResume(::std::move(iceP_deviceID), iceP_listDeviceResume, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listDeviceResume, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGDPDeviceManager_ops, iceC_ZG6000_ZGDPDeviceManager_ops + 25, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGDPDeviceManager_ops)
    {
        case 0:
        {
            return _iceD_addDevice(in, current);
        }
        case 1:
        {
            return _iceD_addDeviceResume(in, current);
        }
        case 2:
        {
            return _iceD_changeDeviceState(in, current);
        }
        case 3:
        {
            return _iceD_changePart(in, current);
        }
        case 4:
        {
            return _iceD_checkState(in, current);
        }
        case 5:
        {
            return _iceD_dispatchData(in, current);
        }
        case 6:
        {
            return _iceD_exitApp(in, current);
        }
        case 7:
        {
            return _iceD_getDeviceHistory(in, current);
        }
        case 8:
        {
            return _iceD_getDevicePartHistory(in, current);
        }
        case 9:
        {
            return _iceD_getDeviceResume(in, current);
        }
        case 10:
        {
            return _iceD_getDevices(in, current);
        }
        case 11:
        {
            return _iceD_getVersion(in, current);
        }
        case 12:
        {
            return _iceD_heartDebug(in, current);
        }
        case 13:
        {
            return _iceD_ice_id(in, current);
        }
        case 14:
        {
            return _iceD_ice_ids(in, current);
        }
        case 15:
        {
            return _iceD_ice_isA(in, current);
        }
        case 16:
        {
            return _iceD_ice_ping(in, current);
        }
        case 17:
        {
            return _iceD_isDebugging(in, current);
        }
        case 18:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 19:
        {
            return _iceD_removeDevice(in, current);
        }
        case 20:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 21:
        {
            return _iceD_startDebug(in, current);
        }
        case 22:
        {
            return _iceD_stopDebug(in, current);
        }
        case 23:
        {
            return _iceD_test(in, current);
        }
        case 24:
        {
            return _iceD_updateDevice(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDeviceManagerPrx::_iceI_getDevices(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::GetDevicesResult>>& outAsync, const ::std::string& iceP_condition, int iceP_offset, int iceP_limit, const ::std::string& iceP_orderField, const ::std::string& iceP_orderType, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_getDevices_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceManager_getDevices_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_condition, iceP_offset, iceP_limit, iceP_orderField, iceP_orderType);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceManager::GetDevicesResult v;
            istr->readAll(v.devices, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDeviceManagerPrx::_iceI_addDevice(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::AddDeviceResult>>& outAsync, const ::std::string& iceP_deviceID, const StringMap& iceP_properties, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_addDevice_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceManager_addDevice_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_properties);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceManager::AddDeviceResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDeviceManagerPrx::_iceI_updateDevice(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::UpdateDeviceResult>>& outAsync, const ::std::string& iceP_deviceID, const StringMap& iceP_properties, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_updateDevice_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceManager_updateDevice_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_properties);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceManager::UpdateDeviceResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDeviceManagerPrx::_iceI_removeDevice(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::RemoveDeviceResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_removeDevice_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceManager_removeDevice_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceManager::RemoveDeviceResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDeviceManagerPrx::_iceI_changeDeviceState(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::ChangeDeviceStateResult>>& outAsync, const ::std::string& iceP_deviceID, const StringMap& iceP_params, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_changeDeviceState_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceManager_changeDeviceState_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_params);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceManager::ChangeDeviceStateResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDeviceManagerPrx::_iceI_changePart(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::ChangePartResult>>& outAsync, const ::std::string& iceP_parentPartID, const StringMap& iceP_partParam, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_changePart_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceManager_changePart_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_parentPartID, iceP_partParam);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceManager::ChangePartResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDeviceManagerPrx::_iceI_getDeviceHistory(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::GetDeviceHistoryResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_getDeviceHistory_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceManager_getDeviceHistory_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceManager::GetDeviceHistoryResult v;
            istr->readAll(v.listDeviceHistory, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDeviceManagerPrx::_iceI_getDevicePartHistory(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::GetDevicePartHistoryResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_getDevicePartHistory_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceManager_getDevicePartHistory_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceManager::GetDevicePartHistoryResult v;
            istr->readAll(v.listPartHistory, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDeviceManagerPrx::_iceI_addDeviceResume(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::AddDeviceResumeResult>>& outAsync, const ::std::string& iceP_deviceID, const StringMap& iceP_deviceResume, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_addDeviceResume_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceManager_addDeviceResume_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_deviceResume);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceManager::AddDeviceResumeResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDeviceManagerPrx::_iceI_getDeviceResume(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::GetDeviceResumeResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_getDeviceResume_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceManager_getDeviceResume_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceManager::GetDeviceResumeResult v;
            istr->readAll(v.listDeviceResume, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGDPDeviceManagerPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGDPDeviceManagerPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGDPDeviceManagerPrx::ice_staticId()
{
    return ZGDPDeviceManager::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGDPDeviceManager_getDevices_name = "getDevices";

const ::std::string iceC_ZG6000_ZGDPDeviceManager_addDevice_name = "addDevice";

const ::std::string iceC_ZG6000_ZGDPDeviceManager_updateDevice_name = "updateDevice";

const ::std::string iceC_ZG6000_ZGDPDeviceManager_removeDevice_name = "removeDevice";

const ::std::string iceC_ZG6000_ZGDPDeviceManager_changeDeviceState_name = "changeDeviceState";

const ::std::string iceC_ZG6000_ZGDPDeviceManager_changePart_name = "changePart";

const ::std::string iceC_ZG6000_ZGDPDeviceManager_getDeviceHistory_name = "getDeviceHistory";

const ::std::string iceC_ZG6000_ZGDPDeviceManager_getDevicePartHistory_name = "getDevicePartHistory";

const ::std::string iceC_ZG6000_ZGDPDeviceManager_addDeviceResume_name = "addDeviceResume";

const ::std::string iceC_ZG6000_ZGDPDeviceManager_getDeviceResume_name = "getDeviceResume";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGDPDeviceManager* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGDPDeviceManager>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGDPDeviceManager;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceManager::_iceI_begin_getDevices(const ::std::string& iceP_condition, ::Ice::Int iceP_offset, ::Ice::Int iceP_limit, const ::std::string& iceP_orderField, const ::std::string& iceP_orderType, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_getDevices_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceManager_getDevices_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceManager_getDevices_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_condition);
        ostr->write(iceP_offset);
        ostr->write(iceP_limit);
        ostr->write(iceP_orderField);
        ostr->write(iceP_orderType);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceManager_getDevices_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceManager::end_getDevices(::ZG6000::ListStringMap& iceP_devices, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_getDevices_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_devices);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceManager::_iceI_end_getDevices(::ZG6000::ListStringMap& iceP_devices, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_getDevices_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_devices);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceManager::_iceI_begin_addDevice(const ::std::string& iceP_deviceID, const ::ZG6000::StringMap& iceP_properties, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_addDevice_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceManager_addDevice_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceManager_addDevice_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_properties);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceManager_addDevice_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceManager::end_addDevice(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_addDevice_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceManager::_iceI_end_addDevice(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_addDevice_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceManager::_iceI_begin_updateDevice(const ::std::string& iceP_deviceID, const ::ZG6000::StringMap& iceP_properties, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_updateDevice_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceManager_updateDevice_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceManager_updateDevice_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_properties);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceManager_updateDevice_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceManager::end_updateDevice(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_updateDevice_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceManager::_iceI_end_updateDevice(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_updateDevice_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceManager::_iceI_begin_removeDevice(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_removeDevice_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceManager_removeDevice_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceManager_removeDevice_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceManager_removeDevice_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceManager::end_removeDevice(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_removeDevice_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceManager::_iceI_end_removeDevice(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_removeDevice_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceManager::_iceI_begin_changeDeviceState(const ::std::string& iceP_deviceID, const ::ZG6000::StringMap& iceP_params, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_changeDeviceState_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceManager_changeDeviceState_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceManager_changeDeviceState_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_params);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceManager_changeDeviceState_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceManager::end_changeDeviceState(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_changeDeviceState_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceManager::_iceI_end_changeDeviceState(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_changeDeviceState_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceManager::_iceI_begin_changePart(const ::std::string& iceP_parentPartID, const ::ZG6000::StringMap& iceP_partParam, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_changePart_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceManager_changePart_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceManager_changePart_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_parentPartID);
        ostr->write(iceP_partParam);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceManager_changePart_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceManager::end_changePart(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_changePart_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceManager::_iceI_end_changePart(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_changePart_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceManager::_iceI_begin_getDeviceHistory(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_getDeviceHistory_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceManager_getDeviceHistory_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceManager_getDeviceHistory_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceManager_getDeviceHistory_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceManager::end_getDeviceHistory(::ZG6000::ListStringMap& iceP_listDeviceHistory, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_getDeviceHistory_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listDeviceHistory);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceManager::_iceI_end_getDeviceHistory(::ZG6000::ListStringMap& iceP_listDeviceHistory, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_getDeviceHistory_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listDeviceHistory);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceManager::_iceI_begin_getDevicePartHistory(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_getDevicePartHistory_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceManager_getDevicePartHistory_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceManager_getDevicePartHistory_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceManager_getDevicePartHistory_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceManager::end_getDevicePartHistory(::ZG6000::ListStringMap& iceP_listPartHistory, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_getDevicePartHistory_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listPartHistory);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceManager::_iceI_end_getDevicePartHistory(::ZG6000::ListStringMap& iceP_listPartHistory, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_getDevicePartHistory_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listPartHistory);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceManager::_iceI_begin_addDeviceResume(const ::std::string& iceP_deviceID, const ::ZG6000::StringMap& iceP_deviceResume, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_addDeviceResume_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceManager_addDeviceResume_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceManager_addDeviceResume_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_deviceResume);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceManager_addDeviceResume_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceManager::end_addDeviceResume(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_addDeviceResume_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceManager::_iceI_end_addDeviceResume(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_addDeviceResume_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceManager::_iceI_begin_getDeviceResume(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceManager_getDeviceResume_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceManager_getDeviceResume_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceManager_getDeviceResume_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceManager_getDeviceResume_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceManager::end_getDeviceResume(::ZG6000::ListStringMap& iceP_listDeviceResume, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_getDeviceResume_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listDeviceResume);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceManager::_iceI_end_getDeviceResume(::ZG6000::ListStringMap& iceP_listDeviceResume, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceManager_getDeviceResume_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listDeviceResume);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGDPDeviceManager::_newInstance() const
{
    return new ZGDPDeviceManager;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGDPDeviceManager::ice_staticId()
{
    return ::ZG6000::ZGDPDeviceManager::ice_staticId();
}

ZG6000::ZGDPDeviceManager::~ZGDPDeviceManager()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGDPDeviceManager* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGDPDeviceManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGDPDeviceManager",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGDPDeviceManager::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGDPDeviceManager_ids, iceC_ZG6000_ZGDPDeviceManager_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGDPDeviceManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGDPDeviceManager_ids[0], &iceC_ZG6000_ZGDPDeviceManager_ids[3]);
}

const ::std::string&
ZG6000::ZGDPDeviceManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGDPDeviceManager::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGDPDeviceManager";
    return typeId;
#else
    return iceC_ZG6000_ZGDPDeviceManager_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_getDevices(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_condition;
    ::Ice::Int iceP_offset;
    ::Ice::Int iceP_limit;
    ::std::string iceP_orderField;
    ::std::string iceP_orderType;
    istr->read(iceP_condition);
    istr->read(iceP_offset);
    istr->read(iceP_limit);
    istr->read(iceP_orderField);
    istr->read(iceP_orderType);
    inS.endReadParams();
    ListStringMap iceP_devices;
    ErrorInfo iceP_e;
    bool ret = this->getDevices(iceP_condition, iceP_offset, iceP_limit, iceP_orderField, iceP_orderType, iceP_devices, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_devices);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_addDevice(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_properties;
    istr->read(iceP_deviceID);
    istr->read(iceP_properties);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addDevice(iceP_deviceID, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_updateDevice(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_properties;
    istr->read(iceP_deviceID);
    istr->read(iceP_properties);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateDevice(iceP_deviceID, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_removeDevice(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->removeDevice(iceP_deviceID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_changeDeviceState(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_params;
    istr->read(iceP_deviceID);
    istr->read(iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->changeDeviceState(iceP_deviceID, iceP_params, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_changePart(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_parentPartID;
    StringMap iceP_partParam;
    istr->read(iceP_parentPartID);
    istr->read(iceP_partParam);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->changePart(iceP_parentPartID, iceP_partParam, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_getDeviceHistory(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_listDeviceHistory;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceHistory(iceP_deviceID, iceP_listDeviceHistory, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listDeviceHistory);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_getDevicePartHistory(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_listPartHistory;
    ErrorInfo iceP_e;
    bool ret = this->getDevicePartHistory(iceP_deviceID, iceP_listPartHistory, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listPartHistory);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_addDeviceResume(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_deviceResume;
    istr->read(iceP_deviceID);
    istr->read(iceP_deviceResume);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addDeviceResume(iceP_deviceID, iceP_deviceResume, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceD_getDeviceResume(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_listDeviceResume;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceResume(iceP_deviceID, iceP_listDeviceResume, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listDeviceResume);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGDPDeviceManager_all[] =
{
    "addDevice",
    "addDeviceResume",
    "changeDeviceState",
    "changePart",
    "checkState",
    "dispatchData",
    "exitApp",
    "getDeviceHistory",
    "getDevicePartHistory",
    "getDeviceResume",
    "getDevices",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "removeDevice",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test",
    "updateDevice"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGDPDeviceManager_all, iceC_ZG6000_ZGDPDeviceManager_all + 25, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGDPDeviceManager_all)
    {
        case 0:
        {
            return _iceD_addDevice(in, current);
        }
        case 1:
        {
            return _iceD_addDeviceResume(in, current);
        }
        case 2:
        {
            return _iceD_changeDeviceState(in, current);
        }
        case 3:
        {
            return _iceD_changePart(in, current);
        }
        case 4:
        {
            return _iceD_checkState(in, current);
        }
        case 5:
        {
            return _iceD_dispatchData(in, current);
        }
        case 6:
        {
            return _iceD_exitApp(in, current);
        }
        case 7:
        {
            return _iceD_getDeviceHistory(in, current);
        }
        case 8:
        {
            return _iceD_getDevicePartHistory(in, current);
        }
        case 9:
        {
            return _iceD_getDeviceResume(in, current);
        }
        case 10:
        {
            return _iceD_getDevices(in, current);
        }
        case 11:
        {
            return _iceD_getVersion(in, current);
        }
        case 12:
        {
            return _iceD_heartDebug(in, current);
        }
        case 13:
        {
            return _iceD_ice_id(in, current);
        }
        case 14:
        {
            return _iceD_ice_ids(in, current);
        }
        case 15:
        {
            return _iceD_ice_isA(in, current);
        }
        case 16:
        {
            return _iceD_ice_ping(in, current);
        }
        case 17:
        {
            return _iceD_isDebugging(in, current);
        }
        case 18:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 19:
        {
            return _iceD_removeDevice(in, current);
        }
        case 20:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 21:
        {
            return _iceD_startDebug(in, current);
        }
        case 22:
        {
            return _iceD_stopDebug(in, current);
        }
        case 23:
        {
            return _iceD_test(in, current);
        }
        case 24:
        {
            return _iceD_updateDevice(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGDPDeviceManager::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGDPDeviceManager, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGDPDeviceManager::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGDPDeviceManager, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGDPDeviceManagerPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGDPDeviceManagerPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGDPDeviceManager::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPDeviceProperty.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGMPDeviceProperty_h__
#define __ZGMPDeviceProperty_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGMPDeviceProperty;
class ZGMPDevicePropertyPrx;

}

namespace ZG6000
{

using MapMapStringMap = ::std::map<::std::string, MapStringMap>;

using ListMapStringMap = ::std::vector<MapStringMap>;

}

namespace ZG6000
{

class ZGMPDeviceProperty : public virtual ZGServerBase
{
public:

    using ProxyType = ZGMPDevicePropertyPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to isAllowCtrl.
     */
    struct IsAllowCtrlResult
    {
        bool returnValue;
        bool allow;
        StringMap conditions;
        ErrorInfo e;
    };

    virtual bool isAllowCtrl(::std::string deviceID, bool& allow, StringMap& conditions, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_isAllowCtrl(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to mgetPropertiesAll.
     */
    struct MgetPropertiesAllResult
    {
        bool returnValue;
        MapMapStringMap properties;
        ErrorInfo e;
    };

    virtual bool mgetPropertiesAll(StringList listDeviceID, MapMapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetPropertiesAll(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to mgetProperties.
     */
    struct MgetPropertiesResult
    {
        bool returnValue;
        MapMapStringMap properties;
        ErrorInfo e;
    };

    virtual bool mgetProperties(StringList listDeviceID, StringList listName, MapMapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to mgetPropertyValues.
     */
    struct MgetPropertyValuesResult
    {
        bool returnValue;
        MapStringMap values;
        ErrorInfo e;
    };

    virtual bool mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& values, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetPropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to mgetPropertyValuesEx.
     */
    struct MgetPropertyValuesExResult
    {
        bool returnValue;
        MapStringMap values;
        ErrorInfo e;
    };

    virtual bool mgetPropertyValuesEx(StringList listDeviceID, StringList listName, MapStringMap& values, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetPropertyValuesEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getGroupProperties.
     */
    struct GetGroupPropertiesResult
    {
        bool returnValue;
        MapMapStringMap properties;
        ErrorInfo e;
    };

    virtual bool getGroupProperties(::std::string deviceID, MapMapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getGroupProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getTableProperties.
     */
    struct GetTablePropertiesResult
    {
        bool returnValue;
        MapStringMap properties;
        ErrorInfo e;
    };

    virtual bool getTableProperties(::std::string deviceID, ::std::string tableName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTableProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertiesAll.
     */
    struct GetPropertiesAllResult
    {
        bool returnValue;
        MapStringMap properties;
        ErrorInfo e;
    };

    virtual bool getPropertiesAll(::std::string deviceID, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertiesAll(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getProperties.
     */
    struct GetPropertiesResult
    {
        bool returnValue;
        MapStringMap properties;
        ErrorInfo e;
    };

    virtual bool getProperties(::std::string deviceID, StringList listName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getProperty.
     */
    struct GetPropertyResult
    {
        bool returnValue;
        StringMap property;
        ErrorInfo e;
    };

    virtual bool getProperty(::std::string deviceID, ::std::string name, StringMap& property, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertyValues.
     */
    struct GetPropertyValuesResult
    {
        bool returnValue;
        StringMap values;
        ErrorInfo e;
    };

    virtual bool getPropertyValues(::std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertyValuesEx.
     */
    struct GetPropertyValuesExResult
    {
        bool returnValue;
        StringMap values;
        ErrorInfo e;
    };

    virtual bool getPropertyValuesEx(::std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValuesEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertyValue.
     */
    struct GetPropertyValueResult
    {
        bool returnValue;
        ::std::string value;
        ErrorInfo e;
    };

    virtual bool getPropertyValue(::std::string deviceID, ::std::string name, ::std::string& value, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertyValueEx.
     */
    struct GetPropertyValueExResult
    {
        bool returnValue;
        ::std::string value;
        ErrorInfo e;
    };

    virtual bool getPropertyValueEx(::std::string deviceID, ::std::string name, ::std::string& value, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValueEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to mupdateProperties.
     */
    struct MupdatePropertiesResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool mupdateProperties(MapMapStringMap properties, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_mupdateProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to mupdatePropertyValues.
     */
    struct MupdatePropertyValuesResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool mupdatePropertyValues(MapStringMap values, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_mupdatePropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateProperty.
     */
    struct UpdatePropertyResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateProperty(::std::string deviceID, ::std::string name, StringMap property, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateProperties.
     */
    struct UpdatePropertiesResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateProperties(::std::string deviceID, MapStringMap properties, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updatePropertyValues.
     */
    struct UpdatePropertyValuesResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updatePropertyValues(::std::string deviceID, StringMap values, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updatePropertyValuesEx.
     */
    struct UpdatePropertyValuesExResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updatePropertyValuesEx(::std::string deviceID, StringMap values, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValuesEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updatePropertyValue.
     */
    struct UpdatePropertyValueResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updatePropertyValue(::std::string deviceID, ::std::string name, ::std::string value, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updatePropertyValueEx.
     */
    struct UpdatePropertyValueExResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updatePropertyValueEx(::std::string deviceID, ::std::string name, ::std::string value, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValueEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDataIDByProperty.
     */
    struct GetDataIDByPropertyResult
    {
        bool returnValue;
        ::std::string tableName;
        ::std::string dataID;
        ErrorInfo e;
    };

    virtual bool getDataIDByProperty(::std::string deviceID, ::std::string name, ::std::string& tableName, ::std::string& dataID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDataIDByProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertyByDataID.
     */
    struct GetPropertyByDataIDResult
    {
        bool returnValue;
        ::std::string deviceID;
        ::std::string name;
        ErrorInfo e;
    };

    virtual bool getPropertyByDataID(::std::string dataID, ::std::string& deviceID, ::std::string& name, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyByDataID(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to isPropertyExists.
     */
    struct IsPropertyExistsResult
    {
        bool returnValue;
        bool exists;
        ErrorInfo e;
    };

    virtual bool isPropertyExists(::std::string deviceID, ::std::string name, bool& exists, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_isPropertyExists(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGMPDevicePropertyPrx : public virtual ::Ice::Proxy<ZGMPDevicePropertyPrx, ZGServerBasePrx>
{
public:

    bool isAllowCtrl(const ::std::string& deviceID, bool& allow, StringMap& conditions, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::IsAllowCtrlResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_isAllowCtrl, deviceID, context).get();
        allow = _result.allow;
        conditions = ::std::move(_result.conditions);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto isAllowCtrlAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::IsAllowCtrlResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::IsAllowCtrlResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_isAllowCtrl, deviceID, context);
    }

    ::std::function<void()>
    isAllowCtrlAsync(const ::std::string& deviceID,
                     ::std::function<void(bool, bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::IsAllowCtrlResult&& _result)
        {
            response(_result.returnValue, _result.allow, ::std::move(_result.conditions), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::IsAllowCtrlResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_isAllowCtrl, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_isAllowCtrl(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::IsAllowCtrlResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool mgetPropertiesAll(const StringList& listDeviceID, MapMapStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::MgetPropertiesAllResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_mgetPropertiesAll, listDeviceID, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto mgetPropertiesAllAsync(const StringList& listDeviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::MgetPropertiesAllResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::MgetPropertiesAllResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_mgetPropertiesAll, listDeviceID, context);
    }

    ::std::function<void()>
    mgetPropertiesAllAsync(const StringList& listDeviceID,
                           ::std::function<void(bool, ::ZG6000::MapMapStringMap, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::MgetPropertiesAllResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::MgetPropertiesAllResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_mgetPropertiesAll, listDeviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_mgetPropertiesAll(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MgetPropertiesAllResult>>&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool mgetProperties(const StringList& listDeviceID, const StringList& listName, MapMapStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::MgetPropertiesResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_mgetProperties, listDeviceID, listName, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto mgetPropertiesAsync(const StringList& listDeviceID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::MgetPropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::MgetPropertiesResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_mgetProperties, listDeviceID, listName, context);
    }

    ::std::function<void()>
    mgetPropertiesAsync(const StringList& listDeviceID, const StringList& listName,
                        ::std::function<void(bool, ::ZG6000::MapMapStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::MgetPropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::MgetPropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_mgetProperties, listDeviceID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_mgetProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MgetPropertiesResult>>&, const StringList&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool mgetPropertyValues(const StringList& listDeviceID, const StringList& listName, MapStringMap& values, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::MgetPropertyValuesResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_mgetPropertyValues, listDeviceID, listName, context).get();
        values = ::std::move(_result.values);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto mgetPropertyValuesAsync(const StringList& listDeviceID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::MgetPropertyValuesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::MgetPropertyValuesResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_mgetPropertyValues, listDeviceID, listName, context);
    }

    ::std::function<void()>
    mgetPropertyValuesAsync(const StringList& listDeviceID, const StringList& listName,
                            ::std::function<void(bool, ::ZG6000::MapStringMap, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::MgetPropertyValuesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.values), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::MgetPropertyValuesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_mgetPropertyValues, listDeviceID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_mgetPropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MgetPropertyValuesResult>>&, const StringList&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool mgetPropertyValuesEx(const StringList& listDeviceID, const StringList& listName, MapStringMap& values, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::MgetPropertyValuesExResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_mgetPropertyValuesEx, listDeviceID, listName, context).get();
        values = ::std::move(_result.values);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto mgetPropertyValuesExAsync(const StringList& listDeviceID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::MgetPropertyValuesExResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::MgetPropertyValuesExResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_mgetPropertyValuesEx, listDeviceID, listName, context);
    }

    ::std::function<void()>
    mgetPropertyValuesExAsync(const StringList& listDeviceID, const StringList& listName,
                              ::std::function<void(bool, ::ZG6000::MapStringMap, ::ZG6000::ErrorInfo)> response,
                              ::std::function<void(::std::exception_ptr)> ex = nullptr,
                              ::std::function<void(bool)> sent = nullptr,
                              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::MgetPropertyValuesExResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.values), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::MgetPropertyValuesExResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_mgetPropertyValuesEx, listDeviceID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_mgetPropertyValuesEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MgetPropertyValuesExResult>>&, const StringList&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool getGroupProperties(const ::std::string& deviceID, MapMapStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetGroupPropertiesResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getGroupProperties, deviceID, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getGroupPropertiesAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetGroupPropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetGroupPropertiesResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getGroupProperties, deviceID, context);
    }

    ::std::function<void()>
    getGroupPropertiesAsync(const ::std::string& deviceID,
                            ::std::function<void(bool, ::ZG6000::MapMapStringMap, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetGroupPropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetGroupPropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getGroupProperties, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getGroupProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetGroupPropertiesResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getTableProperties(const ::std::string& deviceID, const ::std::string& tableName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetTablePropertiesResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getTableProperties, deviceID, tableName, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTablePropertiesAsync(const ::std::string& deviceID, const ::std::string& tableName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetTablePropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetTablePropertiesResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getTableProperties, deviceID, tableName, context);
    }

    ::std::function<void()>
    getTablePropertiesAsync(const ::std::string& deviceID, const ::std::string& tableName,
                            ::std::function<void(bool, ::ZG6000::MapStringMap, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetTablePropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetTablePropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getTableProperties, deviceID, tableName, context);
    }

    /// \cond INTERNAL
    void _iceI_getTableProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetTablePropertiesResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getPropertiesAll(const ::std::string& deviceID, MapStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertiesAllResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getPropertiesAll, deviceID, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertiesAllAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetPropertiesAllResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertiesAllResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getPropertiesAll, deviceID, context);
    }

    ::std::function<void()>
    getPropertiesAllAsync(const ::std::string& deviceID,
                          ::std::function<void(bool, ::ZG6000::MapStringMap, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetPropertiesAllResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetPropertiesAllResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertiesAll, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertiesAll(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertiesAllResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getProperties(const ::std::string& deviceID, const StringList& listName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertiesResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getProperties, deviceID, listName, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertiesAsync(const ::std::string& deviceID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetPropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertiesResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getProperties, deviceID, listName, context);
    }

    ::std::function<void()>
    getPropertiesAsync(const ::std::string& deviceID, const StringList& listName,
                       ::std::function<void(bool, ::ZG6000::MapStringMap, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetPropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetPropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getProperties, deviceID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_getProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertiesResult>>&, const ::std::string&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool getProperty(const ::std::string& deviceID, const ::std::string& name, StringMap& property, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getProperty, deviceID, name, context).get();
        property = ::std::move(_result.property);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertyAsync(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetPropertyResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getProperty, deviceID, name, context);
    }

    ::std::function<void()>
    getPropertyAsync(const ::std::string& deviceID, const ::std::string& name,
                     ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetPropertyResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.property), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetPropertyResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getProperty, deviceID, name, context);
    }

    /// \cond INTERNAL
    void _iceI_getProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getPropertyValues(const ::std::string& deviceID, const StringList& listName, StringMap& values, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyValuesResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getPropertyValues, deviceID, listName, context).get();
        values = ::std::move(_result.values);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertyValuesAsync(const ::std::string& deviceID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetPropertyValuesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyValuesResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getPropertyValues, deviceID, listName, context);
    }

    ::std::function<void()>
    getPropertyValuesAsync(const ::std::string& deviceID, const StringList& listName,
                           ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetPropertyValuesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.values), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetPropertyValuesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertyValues, deviceID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyValuesResult>>&, const ::std::string&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool getPropertyValuesEx(const ::std::string& deviceID, const StringList& listName, StringMap& values, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyValuesExResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getPropertyValuesEx, deviceID, listName, context).get();
        values = ::std::move(_result.values);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertyValuesExAsync(const ::std::string& deviceID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetPropertyValuesExResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyValuesExResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getPropertyValuesEx, deviceID, listName, context);
    }

    ::std::function<void()>
    getPropertyValuesExAsync(const ::std::string& deviceID, const StringList& listName,
                             ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetPropertyValuesExResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.values), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetPropertyValuesExResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertyValuesEx, deviceID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertyValuesEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyValuesExResult>>&, const ::std::string&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool getPropertyValue(const ::std::string& deviceID, const ::std::string& name, ::std::string& value, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyValueResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getPropertyValue, deviceID, name, context).get();
        value = ::std::move(_result.value);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertyValueAsync(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetPropertyValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyValueResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getPropertyValue, deviceID, name, context);
    }

    ::std::function<void()>
    getPropertyValueAsync(const ::std::string& deviceID, const ::std::string& name,
                          ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetPropertyValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.value), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetPropertyValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertyValue, deviceID, name, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyValueResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getPropertyValueEx(const ::std::string& deviceID, const ::std::string& name, ::std::string& value, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyValueExResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getPropertyValueEx, deviceID, name, context).get();
        value = ::std::move(_result.value);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertyValueExAsync(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetPropertyValueExResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyValueExResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getPropertyValueEx, deviceID, name, context);
    }

    ::std::function<void()>
    getPropertyValueExAsync(const ::std::string& deviceID, const ::std::string& name,
                            ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetPropertyValueExResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.value), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetPropertyValueExResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertyValueEx, deviceID, name, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertyValueEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyValueExResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool mupdateProperties(const MapMapStringMap& properties, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::MupdatePropertiesResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_mupdateProperties, properties, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto mupdatePropertiesAsync(const MapMapStringMap& properties, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::MupdatePropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::MupdatePropertiesResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_mupdateProperties, properties, saveToDB, context);
    }

    ::std::function<void()>
    mupdatePropertiesAsync(const MapMapStringMap& properties, bool saveToDB,
                           ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::MupdatePropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::MupdatePropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_mupdateProperties, properties, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_mupdateProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MupdatePropertiesResult>>&, const MapMapStringMap&, bool, const ::Ice::Context&);
    /// \endcond

    bool mupdatePropertyValues(const MapStringMap& values, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::MupdatePropertyValuesResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_mupdatePropertyValues, values, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto mupdatePropertyValuesAsync(const MapStringMap& values, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::MupdatePropertyValuesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::MupdatePropertyValuesResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_mupdatePropertyValues, values, saveToDB, context);
    }

    ::std::function<void()>
    mupdatePropertyValuesAsync(const MapStringMap& values, bool saveToDB,
                               ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                               ::std::function<void(::std::exception_ptr)> ex = nullptr,
                               ::std::function<void(bool)> sent = nullptr,
                               const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::MupdatePropertyValuesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::MupdatePropertyValuesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_mupdatePropertyValues, values, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_mupdatePropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::MupdatePropertyValuesResult>>&, const MapStringMap&, bool, const ::Ice::Context&);
    /// \endcond

    bool updateProperty(const ::std::string& deviceID, const ::std::string& name, const StringMap& property, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertyResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_updateProperty, deviceID, name, property, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updatePropertyAsync(const ::std::string& deviceID, const ::std::string& name, const StringMap& property, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::UpdatePropertyResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertyResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_updateProperty, deviceID, name, property, saveToDB, context);
    }

    ::std::function<void()>
    updatePropertyAsync(const ::std::string& deviceID, const ::std::string& name, const StringMap& property, bool saveToDB,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::UpdatePropertyResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::UpdatePropertyResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_updateProperty, deviceID, name, property, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_updateProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertyResult>>&, const ::std::string&, const ::std::string&, const StringMap&, bool, const ::Ice::Context&);
    /// \endcond

    bool updateProperties(const ::std::string& deviceID, const MapStringMap& properties, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertiesResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_updateProperties, deviceID, properties, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updatePropertiesAsync(const ::std::string& deviceID, const MapStringMap& properties, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::UpdatePropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertiesResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_updateProperties, deviceID, properties, saveToDB, context);
    }

    ::std::function<void()>
    updatePropertiesAsync(const ::std::string& deviceID, const MapStringMap& properties, bool saveToDB,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::UpdatePropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::UpdatePropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_updateProperties, deviceID, properties, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_updateProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertiesResult>>&, const ::std::string&, const MapStringMap&, bool, const ::Ice::Context&);
    /// \endcond

    bool updatePropertyValues(const ::std::string& deviceID, const StringMap& values, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertyValuesResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_updatePropertyValues, deviceID, values, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updatePropertyValuesAsync(const ::std::string& deviceID, const StringMap& values, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::UpdatePropertyValuesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertyValuesResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_updatePropertyValues, deviceID, values, saveToDB, context);
    }

    ::std::function<void()>
    updatePropertyValuesAsync(const ::std::string& deviceID, const StringMap& values, bool saveToDB,
                              ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                              ::std::function<void(::std::exception_ptr)> ex = nullptr,
                              ::std::function<void(bool)> sent = nullptr,
                              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::UpdatePropertyValuesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::UpdatePropertyValuesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_updatePropertyValues, deviceID, values, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_updatePropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertyValuesResult>>&, const ::std::string&, const StringMap&, bool, const ::Ice::Context&);
    /// \endcond

    bool updatePropertyValuesEx(const ::std::string& deviceID, const StringMap& values, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertyValuesExResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_updatePropertyValuesEx, deviceID, values, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updatePropertyValuesExAsync(const ::std::string& deviceID, const StringMap& values, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::UpdatePropertyValuesExResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertyValuesExResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_updatePropertyValuesEx, deviceID, values, saveToDB, context);
    }

    ::std::function<void()>
    updatePropertyValuesExAsync(const ::std::string& deviceID, const StringMap& values, bool saveToDB,
                                ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                                ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                ::std::function<void(bool)> sent = nullptr,
                                const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::UpdatePropertyValuesExResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::UpdatePropertyValuesExResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_updatePropertyValuesEx, deviceID, values, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_updatePropertyValuesEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertyValuesExResult>>&, const ::std::string&, const StringMap&, bool, const ::Ice::Context&);
    /// \endcond

    bool updatePropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertyValueResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_updatePropertyValue, deviceID, name, value, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updatePropertyValueAsync(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::UpdatePropertyValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertyValueResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_updatePropertyValue, deviceID, name, value, saveToDB, context);
    }

    ::std::function<void()>
    updatePropertyValueAsync(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB,
                             ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::UpdatePropertyValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::UpdatePropertyValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_updatePropertyValue, deviceID, name, value, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_updatePropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertyValueResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, bool, const ::Ice::Context&);
    /// \endcond

    bool updatePropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertyValueExResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_updatePropertyValueEx, deviceID, name, value, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updatePropertyValueExAsync(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::UpdatePropertyValueExResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::UpdatePropertyValueExResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_updatePropertyValueEx, deviceID, name, value, saveToDB, context);
    }

    ::std::function<void()>
    updatePropertyValueExAsync(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB,
                               ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                               ::std::function<void(::std::exception_ptr)> ex = nullptr,
                               ::std::function<void(bool)> sent = nullptr,
                               const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::UpdatePropertyValueExResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::UpdatePropertyValueExResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_updatePropertyValueEx, deviceID, name, value, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_updatePropertyValueEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::UpdatePropertyValueExResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, bool, const ::Ice::Context&);
    /// \endcond

    bool getDataIDByProperty(const ::std::string& deviceID, const ::std::string& name, ::std::string& tableName, ::std::string& dataID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetDataIDByPropertyResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getDataIDByProperty, deviceID, name, context).get();
        tableName = ::std::move(_result.tableName);
        dataID = ::std::move(_result.dataID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDataIDByPropertyAsync(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetDataIDByPropertyResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetDataIDByPropertyResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getDataIDByProperty, deviceID, name, context);
    }

    ::std::function<void()>
    getDataIDByPropertyAsync(const ::std::string& deviceID, const ::std::string& name,
                             ::std::function<void(bool, ::std::string, ::std::string, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetDataIDByPropertyResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.tableName), ::std::move(_result.dataID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetDataIDByPropertyResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getDataIDByProperty, deviceID, name, context);
    }

    /// \cond INTERNAL
    void _iceI_getDataIDByProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetDataIDByPropertyResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getPropertyByDataID(const ::std::string& dataID, ::std::string& deviceID, ::std::string& name, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyByDataIDResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_getPropertyByDataID, dataID, context).get();
        deviceID = ::std::move(_result.deviceID);
        name = ::std::move(_result.name);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getPropertyByDataIDAsync(const ::std::string& dataID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::GetPropertyByDataIDResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::GetPropertyByDataIDResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_getPropertyByDataID, dataID, context);
    }

    ::std::function<void()>
    getPropertyByDataIDAsync(const ::std::string& dataID,
                             ::std::function<void(bool, ::std::string, ::std::string, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::GetPropertyByDataIDResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.deviceID), ::std::move(_result.name), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::GetPropertyByDataIDResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_getPropertyByDataID, dataID, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertyByDataID(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::GetPropertyByDataIDResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool isPropertyExists(const ::std::string& deviceID, const ::std::string& name, bool& exists, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDeviceProperty::IsPropertyExistsResult>(true, this, &ZGMPDevicePropertyPrx::_iceI_isPropertyExists, deviceID, name, context).get();
        exists = _result.exists;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto isPropertyExistsAsync(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDeviceProperty::IsPropertyExistsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDeviceProperty::IsPropertyExistsResult, P>(false, this, &ZGMPDevicePropertyPrx::_iceI_isPropertyExists, deviceID, name, context);
    }

    ::std::function<void()>
    isPropertyExistsAsync(const ::std::string& deviceID, const ::std::string& name,
                          ::std::function<void(bool, bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDeviceProperty::IsPropertyExistsResult&& _result)
        {
            response(_result.returnValue, _result.exists, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDeviceProperty::IsPropertyExistsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDevicePropertyPrx::_iceI_isPropertyExists, deviceID, name, context);
    }

    /// \cond INTERNAL
    void _iceI_isPropertyExists(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDeviceProperty::IsPropertyExistsResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGMPDevicePropertyPrx() = default;
    friend ::std::shared_ptr<ZGMPDevicePropertyPrx> IceInternal::createProxy<ZGMPDevicePropertyPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGMPDevicePropertyPtr = ::std::shared_ptr<ZGMPDeviceProperty>;
using ZGMPDevicePropertyPrxPtr = ::std::shared_ptr<ZGMPDevicePropertyPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGMPDeviceProperty;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGMPDeviceProperty>&);
::IceProxy::Ice::Object* upCast(ZGMPDeviceProperty*);
/// \endcond

}

}

namespace ZG6000
{

class ZGMPDeviceProperty;
/// \cond INTERNAL
::Ice::Object* upCast(ZGMPDeviceProperty*);
/// \endcond
typedef ::IceInternal::Handle< ZGMPDeviceProperty> ZGMPDevicePropertyPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGMPDeviceProperty> ZGMPDevicePropertyPrx;
typedef ZGMPDevicePropertyPrx ZGMPDevicePropertyPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGMPDevicePropertyPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

typedef ::std::map< ::std::string, MapStringMap> MapMapStringMap;

typedef ::std::vector<MapStringMap> ListMapStringMap;

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_isAllowCtrl.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_isAllowCtrl.
 */
class Callback_ZGMPDeviceProperty_isAllowCtrl_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_isAllowCtrl_Base> Callback_ZGMPDeviceProperty_isAllowCtrlPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertiesAll.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetPropertiesAll.
 */
class Callback_ZGMPDeviceProperty_mgetPropertiesAll_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_mgetPropertiesAll_Base> Callback_ZGMPDeviceProperty_mgetPropertiesAllPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetProperties.
 */
class Callback_ZGMPDeviceProperty_mgetProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_mgetProperties_Base> Callback_ZGMPDeviceProperty_mgetPropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetPropertyValues.
 */
class Callback_ZGMPDeviceProperty_mgetPropertyValues_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_mgetPropertyValues_Base> Callback_ZGMPDeviceProperty_mgetPropertyValuesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValuesEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetPropertyValuesEx.
 */
class Callback_ZGMPDeviceProperty_mgetPropertyValuesEx_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_mgetPropertyValuesEx_Base> Callback_ZGMPDeviceProperty_mgetPropertyValuesExPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getGroupProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getGroupProperties.
 */
class Callback_ZGMPDeviceProperty_getGroupProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getGroupProperties_Base> Callback_ZGMPDeviceProperty_getGroupPropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getTableProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getTableProperties.
 */
class Callback_ZGMPDeviceProperty_getTableProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getTableProperties_Base> Callback_ZGMPDeviceProperty_getTablePropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertiesAll.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertiesAll.
 */
class Callback_ZGMPDeviceProperty_getPropertiesAll_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getPropertiesAll_Base> Callback_ZGMPDeviceProperty_getPropertiesAllPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getProperties.
 */
class Callback_ZGMPDeviceProperty_getProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getProperties_Base> Callback_ZGMPDeviceProperty_getPropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getProperty.
 */
class Callback_ZGMPDeviceProperty_getProperty_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getProperty_Base> Callback_ZGMPDeviceProperty_getPropertyPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValues.
 */
class Callback_ZGMPDeviceProperty_getPropertyValues_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getPropertyValues_Base> Callback_ZGMPDeviceProperty_getPropertyValuesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValuesEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValuesEx.
 */
class Callback_ZGMPDeviceProperty_getPropertyValuesEx_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getPropertyValuesEx_Base> Callback_ZGMPDeviceProperty_getPropertyValuesExPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValue.
 */
class Callback_ZGMPDeviceProperty_getPropertyValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getPropertyValue_Base> Callback_ZGMPDeviceProperty_getPropertyValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValueEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValueEx.
 */
class Callback_ZGMPDeviceProperty_getPropertyValueEx_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getPropertyValueEx_Base> Callback_ZGMPDeviceProperty_getPropertyValueExPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdateProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mupdateProperties.
 */
class Callback_ZGMPDeviceProperty_mupdateProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_mupdateProperties_Base> Callback_ZGMPDeviceProperty_mupdatePropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mupdatePropertyValues.
 */
class Callback_ZGMPDeviceProperty_mupdatePropertyValues_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_mupdatePropertyValues_Base> Callback_ZGMPDeviceProperty_mupdatePropertyValuesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updateProperty.
 */
class Callback_ZGMPDeviceProperty_updateProperty_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_updateProperty_Base> Callback_ZGMPDeviceProperty_updatePropertyPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updateProperties.
 */
class Callback_ZGMPDeviceProperty_updateProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_updateProperties_Base> Callback_ZGMPDeviceProperty_updatePropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValues.
 */
class Callback_ZGMPDeviceProperty_updatePropertyValues_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_updatePropertyValues_Base> Callback_ZGMPDeviceProperty_updatePropertyValuesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValuesEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValuesEx.
 */
class Callback_ZGMPDeviceProperty_updatePropertyValuesEx_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_updatePropertyValuesEx_Base> Callback_ZGMPDeviceProperty_updatePropertyValuesExPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValue.
 */
class Callback_ZGMPDeviceProperty_updatePropertyValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_updatePropertyValue_Base> Callback_ZGMPDeviceProperty_updatePropertyValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValueEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValueEx.
 */
class Callback_ZGMPDeviceProperty_updatePropertyValueEx_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_updatePropertyValueEx_Base> Callback_ZGMPDeviceProperty_updatePropertyValueExPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getDataIDByProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getDataIDByProperty.
 */
class Callback_ZGMPDeviceProperty_getDataIDByProperty_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getDataIDByProperty_Base> Callback_ZGMPDeviceProperty_getDataIDByPropertyPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyByDataID.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyByDataID.
 */
class Callback_ZGMPDeviceProperty_getPropertyByDataID_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_getPropertyByDataID_Base> Callback_ZGMPDeviceProperty_getPropertyByDataIDPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_isPropertyExists.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_isPropertyExists.
 */
class Callback_ZGMPDeviceProperty_isPropertyExists_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDeviceProperty_isPropertyExists_Base> Callback_ZGMPDeviceProperty_isPropertyExistsPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGMPDeviceProperty : public virtual ::Ice::Proxy<ZGMPDeviceProperty, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool isAllowCtrl(const ::std::string& deviceID, bool& allow, ::ZG6000::StringMap& conditions, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_isAllowCtrl(allow, conditions, e, _iceI_begin_isAllowCtrl(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_isAllowCtrl(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_isAllowCtrl(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_isAllowCtrl(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isAllowCtrl(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isAllowCtrl(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isAllowCtrl(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isAllowCtrl(const ::std::string& deviceID, const ::ZG6000::Callback_ZGMPDeviceProperty_isAllowCtrlPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isAllowCtrl(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isAllowCtrl(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_isAllowCtrlPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isAllowCtrl(deviceID, context, cb, cookie);
    }

    bool end_isAllowCtrl(bool& allow, ::ZG6000::StringMap& conditions, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_isAllowCtrl(bool& iceP_allow, ::ZG6000::StringMap& iceP_conditions, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_isAllowCtrl(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool mgetPropertiesAll(const ::ZG6000::StringList& listDeviceID, ::ZG6000::MapMapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_mgetPropertiesAll(properties, e, _iceI_begin_mgetPropertiesAll(listDeviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_mgetPropertiesAll(const ::ZG6000::StringList& listDeviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_mgetPropertiesAll(listDeviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertiesAll(const ::ZG6000::StringList& listDeviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertiesAll(listDeviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertiesAll(const ::ZG6000::StringList& listDeviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertiesAll(listDeviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertiesAll(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::Callback_ZGMPDeviceProperty_mgetPropertiesAllPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertiesAll(listDeviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertiesAll(const ::ZG6000::StringList& listDeviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_mgetPropertiesAllPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertiesAll(listDeviceID, context, cb, cookie);
    }

    bool end_mgetPropertiesAll(::ZG6000::MapMapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_mgetPropertiesAll(::ZG6000::MapMapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_mgetPropertiesAll(const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool mgetProperties(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, ::ZG6000::MapMapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_mgetProperties(properties, e, _iceI_begin_mgetProperties(listDeviceID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_mgetProperties(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_mgetProperties(listDeviceID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_mgetProperties(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetProperties(listDeviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetProperties(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetProperties(listDeviceID, listName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetProperties(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGMPDeviceProperty_mgetPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetProperties(listDeviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetProperties(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_mgetPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetProperties(listDeviceID, listName, context, cb, cookie);
    }

    bool end_mgetProperties(::ZG6000::MapMapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_mgetProperties(::ZG6000::MapMapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_mgetProperties(const ::ZG6000::StringList&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, ::ZG6000::MapStringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_mgetPropertyValues(values, e, _iceI_begin_mgetPropertyValues(listDeviceID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_mgetPropertyValues(listDeviceID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValues(listDeviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValues(listDeviceID, listName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGMPDeviceProperty_mgetPropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValues(listDeviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValues(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_mgetPropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValues(listDeviceID, listName, context, cb, cookie);
    }

    bool end_mgetPropertyValues(::ZG6000::MapStringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_mgetPropertyValues(::ZG6000::MapStringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_mgetPropertyValues(const ::ZG6000::StringList&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool mgetPropertyValuesEx(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, ::ZG6000::MapStringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_mgetPropertyValuesEx(values, e, _iceI_begin_mgetPropertyValuesEx(listDeviceID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValuesEx(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_mgetPropertyValuesEx(listDeviceID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValuesEx(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValuesEx(listDeviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValuesEx(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValuesEx(listDeviceID, listName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValuesEx(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGMPDeviceProperty_mgetPropertyValuesExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValuesEx(listDeviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mgetPropertyValuesEx(const ::ZG6000::StringList& listDeviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_mgetPropertyValuesExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mgetPropertyValuesEx(listDeviceID, listName, context, cb, cookie);
    }

    bool end_mgetPropertyValuesEx(::ZG6000::MapStringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_mgetPropertyValuesEx(::ZG6000::MapStringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_mgetPropertyValuesEx(const ::ZG6000::StringList&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getGroupProperties(const ::std::string& deviceID, ::ZG6000::MapMapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getGroupProperties(properties, e, _iceI_begin_getGroupProperties(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getGroupProperties(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getGroupProperties(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getGroupProperties(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getGroupProperties(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getGroupProperties(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getGroupProperties(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getGroupProperties(const ::std::string& deviceID, const ::ZG6000::Callback_ZGMPDeviceProperty_getGroupPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getGroupProperties(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getGroupProperties(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getGroupPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getGroupProperties(deviceID, context, cb, cookie);
    }

    bool end_getGroupProperties(::ZG6000::MapMapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getGroupProperties(::ZG6000::MapMapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getGroupProperties(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getTableProperties(const ::std::string& deviceID, const ::std::string& tableName, ::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTableProperties(properties, e, _iceI_begin_getTableProperties(deviceID, tableName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTableProperties(const ::std::string& deviceID, const ::std::string& tableName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTableProperties(deviceID, tableName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTableProperties(const ::std::string& deviceID, const ::std::string& tableName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTableProperties(deviceID, tableName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTableProperties(const ::std::string& deviceID, const ::std::string& tableName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTableProperties(deviceID, tableName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTableProperties(const ::std::string& deviceID, const ::std::string& tableName, const ::ZG6000::Callback_ZGMPDeviceProperty_getTablePropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTableProperties(deviceID, tableName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTableProperties(const ::std::string& deviceID, const ::std::string& tableName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getTablePropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTableProperties(deviceID, tableName, context, cb, cookie);
    }

    bool end_getTableProperties(::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTableProperties(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTableProperties(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPropertiesAll(const ::std::string& deviceID, ::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertiesAll(properties, e, _iceI_begin_getPropertiesAll(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPropertiesAll(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertiesAll(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPropertiesAll(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertiesAll(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertiesAll(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertiesAll(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertiesAll(const ::std::string& deviceID, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertiesAllPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertiesAll(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertiesAll(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertiesAllPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertiesAll(deviceID, context, cb, cookie);
    }

    bool end_getPropertiesAll(::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertiesAll(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertiesAll(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getProperties(const ::std::string& deviceID, const ::ZG6000::StringList& listName, ::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getProperties(properties, e, _iceI_begin_getProperties(deviceID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getProperties(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getProperties(deviceID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getProperties(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperties(deviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getProperties(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperties(deviceID, listName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getProperties(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperties(deviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getProperties(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperties(deviceID, listName, context, cb, cookie);
    }

    bool end_getProperties(::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getProperties(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getProperties(const ::std::string&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getProperty(const ::std::string& deviceID, const ::std::string& name, ::ZG6000::StringMap& property, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getProperty(property, e, _iceI_begin_getProperty(deviceID, name, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getProperty(deviceID, name, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& deviceID, const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(deviceID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(deviceID, name, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(deviceID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(deviceID, name, context, cb, cookie);
    }

    bool end_getProperty(::ZG6000::StringMap& property, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getProperty(::ZG6000::StringMap& iceP_property, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getProperty(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, ::ZG6000::StringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertyValues(values, e, _iceI_begin_getPropertyValues(deviceID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertyValues(deviceID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(deviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(deviceID, listName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(deviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(deviceID, listName, context, cb, cookie);
    }

    bool end_getPropertyValues(::ZG6000::StringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertyValues(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertyValues(const ::std::string&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringList& listName, ::ZG6000::StringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertyValuesEx(values, e, _iceI_begin_getPropertyValuesEx(deviceID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertyValuesEx(deviceID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValuesEx(deviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValuesEx(deviceID, listName, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyValuesExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValuesEx(deviceID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyValuesExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValuesEx(deviceID, listName, context, cb, cookie);
    }

    bool end_getPropertyValuesEx(::ZG6000::StringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertyValuesEx(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertyValuesEx(const ::std::string&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPropertyValue(const ::std::string& deviceID, const ::std::string& name, ::std::string& value, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertyValue(value, e, _iceI_begin_getPropertyValue(deviceID, name, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertyValue(deviceID, name, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(deviceID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(deviceID, name, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(deviceID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(deviceID, name, context, cb, cookie);
    }

    bool end_getPropertyValue(::std::string& value, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertyValue(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertyValue(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPropertyValueEx(const ::std::string& deviceID, const ::std::string& name, ::std::string& value, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertyValueEx(value, e, _iceI_begin_getPropertyValueEx(deviceID, name, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertyValueEx(deviceID, name, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValueEx(deviceID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValueEx(deviceID, name, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyValueExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValueEx(deviceID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyValueExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValueEx(deviceID, name, context, cb, cookie);
    }

    bool end_getPropertyValueEx(::std::string& value, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertyValueEx(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertyValueEx(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool mupdateProperties(const ::ZG6000::MapMapStringMap& properties, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_mupdateProperties(e, _iceI_begin_mupdateProperties(properties, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_mupdateProperties(const ::ZG6000::MapMapStringMap& properties, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_mupdateProperties(properties, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_mupdateProperties(const ::ZG6000::MapMapStringMap& properties, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mupdateProperties(properties, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mupdateProperties(const ::ZG6000::MapMapStringMap& properties, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mupdateProperties(properties, saveToDB, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mupdateProperties(const ::ZG6000::MapMapStringMap& properties, bool saveToDB, const ::ZG6000::Callback_ZGMPDeviceProperty_mupdatePropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mupdateProperties(properties, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mupdateProperties(const ::ZG6000::MapMapStringMap& properties, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_mupdatePropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mupdateProperties(properties, saveToDB, context, cb, cookie);
    }

    bool end_mupdateProperties(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_mupdateProperties(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_mupdateProperties(const ::ZG6000::MapMapStringMap&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool mupdatePropertyValues(const ::ZG6000::MapStringMap& values, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_mupdatePropertyValues(e, _iceI_begin_mupdatePropertyValues(values, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_mupdatePropertyValues(const ::ZG6000::MapStringMap& values, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_mupdatePropertyValues(values, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_mupdatePropertyValues(const ::ZG6000::MapStringMap& values, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mupdatePropertyValues(values, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mupdatePropertyValues(const ::ZG6000::MapStringMap& values, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mupdatePropertyValues(values, saveToDB, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mupdatePropertyValues(const ::ZG6000::MapStringMap& values, bool saveToDB, const ::ZG6000::Callback_ZGMPDeviceProperty_mupdatePropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mupdatePropertyValues(values, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_mupdatePropertyValues(const ::ZG6000::MapStringMap& values, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_mupdatePropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_mupdatePropertyValues(values, saveToDB, context, cb, cookie);
    }

    bool end_mupdatePropertyValues(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_mupdatePropertyValues(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_mupdatePropertyValues(const ::ZG6000::MapStringMap&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateProperty(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateProperty(e, _iceI_begin_updateProperty(deviceID, name, property, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateProperty(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateProperty(deviceID, name, property, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateProperty(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperty(deviceID, name, property, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateProperty(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperty(deviceID, name, property, saveToDB, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateProperty(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperty(deviceID, name, property, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateProperty(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperty(deviceID, name, property, saveToDB, context, cb, cookie);
    }

    bool end_updateProperty(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateProperty(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateProperty(const ::std::string&, const ::std::string&, const ::ZG6000::StringMap&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateProperties(const ::std::string& deviceID, const ::ZG6000::MapStringMap& properties, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateProperties(e, _iceI_begin_updateProperties(deviceID, properties, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateProperties(const ::std::string& deviceID, const ::ZG6000::MapStringMap& properties, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateProperties(deviceID, properties, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateProperties(const ::std::string& deviceID, const ::ZG6000::MapStringMap& properties, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperties(deviceID, properties, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateProperties(const ::std::string& deviceID, const ::ZG6000::MapStringMap& properties, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperties(deviceID, properties, saveToDB, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateProperties(const ::std::string& deviceID, const ::ZG6000::MapStringMap& properties, bool saveToDB, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperties(deviceID, properties, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateProperties(const ::std::string& deviceID, const ::ZG6000::MapStringMap& properties, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperties(deviceID, properties, saveToDB, context, cb, cookie);
    }

    bool end_updateProperties(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateProperties(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateProperties(const ::std::string&, const ::ZG6000::MapStringMap&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updatePropertyValues(e, _iceI_begin_updatePropertyValues(deviceID, values, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updatePropertyValues(deviceID, values, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(deviceID, values, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(deviceID, values, saveToDB, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(deviceID, values, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(deviceID, values, saveToDB, context, cb, cookie);
    }

    bool end_updatePropertyValues(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updatePropertyValues(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updatePropertyValues(const ::std::string&, const ::ZG6000::StringMap&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updatePropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updatePropertyValuesEx(e, _iceI_begin_updatePropertyValuesEx(deviceID, values, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updatePropertyValuesEx(deviceID, values, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValuesEx(deviceID, values, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValuesEx(deviceID, values, saveToDB, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertyValuesExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValuesEx(deviceID, values, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValuesEx(const ::std::string& deviceID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertyValuesExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValuesEx(deviceID, values, saveToDB, context, cb, cookie);
    }

    bool end_updatePropertyValuesEx(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updatePropertyValuesEx(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updatePropertyValuesEx(const ::std::string&, const ::ZG6000::StringMap&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updatePropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updatePropertyValue(e, _iceI_begin_updatePropertyValue(deviceID, name, value, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updatePropertyValue(deviceID, name, value, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(deviceID, name, value, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(deviceID, name, value, saveToDB, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(deviceID, name, value, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(deviceID, name, value, saveToDB, context, cb, cookie);
    }

    bool end_updatePropertyValue(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updatePropertyValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updatePropertyValue(const ::std::string&, const ::std::string&, const ::std::string&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updatePropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updatePropertyValueEx(e, _iceI_begin_updatePropertyValueEx(deviceID, name, value, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updatePropertyValueEx(deviceID, name, value, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValueEx(deviceID, name, value, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValueEx(deviceID, name, value, saveToDB, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertyValueExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValueEx(deviceID, name, value, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updatePropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_updatePropertyValueExPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValueEx(deviceID, name, value, saveToDB, context, cb, cookie);
    }

    bool end_updatePropertyValueEx(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updatePropertyValueEx(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updatePropertyValueEx(const ::std::string&, const ::std::string&, const ::std::string&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDataIDByProperty(const ::std::string& deviceID, const ::std::string& name, ::std::string& tableName, ::std::string& dataID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDataIDByProperty(tableName, dataID, e, _iceI_begin_getDataIDByProperty(deviceID, name, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDataIDByProperty(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDataIDByProperty(deviceID, name, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDataIDByProperty(const ::std::string& deviceID, const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataIDByProperty(deviceID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDataIDByProperty(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataIDByProperty(deviceID, name, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDataIDByProperty(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::Callback_ZGMPDeviceProperty_getDataIDByPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataIDByProperty(deviceID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDataIDByProperty(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getDataIDByPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataIDByProperty(deviceID, name, context, cb, cookie);
    }

    bool end_getDataIDByProperty(::std::string& tableName, ::std::string& dataID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDataIDByProperty(::std::string& iceP_tableName, ::std::string& iceP_dataID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDataIDByProperty(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getPropertyByDataID(const ::std::string& dataID, ::std::string& deviceID, ::std::string& name, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertyByDataID(deviceID, name, e, _iceI_begin_getPropertyByDataID(dataID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getPropertyByDataID(const ::std::string& dataID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertyByDataID(dataID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getPropertyByDataID(const ::std::string& dataID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyByDataID(dataID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyByDataID(const ::std::string& dataID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyByDataID(dataID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyByDataID(const ::std::string& dataID, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyByDataIDPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyByDataID(dataID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getPropertyByDataID(const ::std::string& dataID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_getPropertyByDataIDPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyByDataID(dataID, context, cb, cookie);
    }

    bool end_getPropertyByDataID(::std::string& deviceID, ::std::string& name, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertyByDataID(::std::string& iceP_deviceID, ::std::string& iceP_name, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertyByDataID(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool isPropertyExists(const ::std::string& deviceID, const ::std::string& name, bool& exists, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_isPropertyExists(exists, e, _iceI_begin_isPropertyExists(deviceID, name, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_isPropertyExists(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_isPropertyExists(deviceID, name, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_isPropertyExists(const ::std::string& deviceID, const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isPropertyExists(deviceID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isPropertyExists(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isPropertyExists(deviceID, name, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isPropertyExists(const ::std::string& deviceID, const ::std::string& name, const ::ZG6000::Callback_ZGMPDeviceProperty_isPropertyExistsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isPropertyExists(deviceID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isPropertyExists(const ::std::string& deviceID, const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDeviceProperty_isPropertyExistsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isPropertyExists(deviceID, name, context, cb, cookie);
    }

    bool end_isPropertyExists(bool& exists, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_isPropertyExists(bool& iceP_exists, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_isPropertyExists(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGMPDeviceProperty : virtual public ZGServerBase
{
public:

    typedef ZGMPDevicePropertyPrx ProxyType;
    typedef ZGMPDevicePropertyPtr PointerType;

    virtual ~ZGMPDeviceProperty();

#ifdef ICE_CPP11_COMPILER
    ZGMPDeviceProperty() = default;
    ZGMPDeviceProperty(const ZGMPDeviceProperty&) = default;
    ZGMPDeviceProperty& operator=(const ZGMPDeviceProperty&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool isAllowCtrl(const ::std::string& deviceID, bool& allow, StringMap& conditions, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_isAllowCtrl(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool mgetPropertiesAll(const StringList& listDeviceID, MapMapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetPropertiesAll(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool mgetProperties(const StringList& listDeviceID, const StringList& listName, MapMapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool mgetPropertyValues(const StringList& listDeviceID, const StringList& listName, MapStringMap& values, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetPropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool mgetPropertyValuesEx(const StringList& listDeviceID, const StringList& listName, MapStringMap& values, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_mgetPropertyValuesEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getGroupProperties(const ::std::string& deviceID, MapMapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getGroupProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getTableProperties(const ::std::string& deviceID, const ::std::string& tableName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTableProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPropertiesAll(const ::std::string& deviceID, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertiesAll(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getProperties(const ::std::string& deviceID, const StringList& listName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getProperty(const ::std::string& deviceID, const ::std::string& name, StringMap& property, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPropertyValues(const ::std::string& deviceID, const StringList& listName, StringMap& values, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPropertyValuesEx(const ::std::string& deviceID, const StringList& listName, StringMap& values, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValuesEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPropertyValue(const ::std::string& deviceID, const ::std::string& name, ::std::string& value, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPropertyValueEx(const ::std::string& deviceID, const ::std::string& name, ::std::string& value, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValueEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool mupdateProperties(const MapMapStringMap& properties, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_mupdateProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool mupdatePropertyValues(const MapStringMap& values, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_mupdatePropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateProperty(const ::std::string& deviceID, const ::std::string& name, const StringMap& property, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateProperties(const ::std::string& deviceID, const MapStringMap& properties, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updatePropertyValues(const ::std::string& deviceID, const StringMap& values, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updatePropertyValuesEx(const ::std::string& deviceID, const StringMap& values, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValuesEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updatePropertyValue(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updatePropertyValueEx(const ::std::string& deviceID, const ::std::string& name, const ::std::string& value, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValueEx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDataIDByProperty(const ::std::string& deviceID, const ::std::string& name, ::std::string& tableName, ::std::string& dataID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDataIDByProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getPropertyByDataID(const ::std::string& dataID, ::std::string& deviceID, ::std::string& name, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyByDataID(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool isPropertyExists(const ::std::string& deviceID, const ::std::string& name, bool& exists, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_isPropertyExists(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGMPDeviceProperty& lhs, const ZGMPDeviceProperty& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGMPDeviceProperty& lhs, const ZGMPDeviceProperty& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_isAllowCtrl.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_isAllowCtrl.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_isAllowCtrl : public Callback_ZGMPDeviceProperty_isAllowCtrl_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_isAllowCtrl(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        bool iceP_allow;
        StringMap iceP_conditions;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isAllowCtrl(iceP_allow, iceP_conditions, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_allow, iceP_conditions, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_isAllowCtrl.
 */
template<class T> Callback_ZGMPDeviceProperty_isAllowCtrlPtr
newCallback_ZGMPDeviceProperty_isAllowCtrl(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_isAllowCtrl<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_isAllowCtrl.
 */
template<class T> Callback_ZGMPDeviceProperty_isAllowCtrlPtr
newCallback_ZGMPDeviceProperty_isAllowCtrl(T* instance, void (T::*cb)(bool, bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_isAllowCtrl<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_isAllowCtrl.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_isAllowCtrl.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_isAllowCtrl : public Callback_ZGMPDeviceProperty_isAllowCtrl_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_isAllowCtrl(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        bool iceP_allow;
        StringMap iceP_conditions;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isAllowCtrl(iceP_allow, iceP_conditions, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_allow, iceP_conditions, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_isAllowCtrl.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_isAllowCtrlPtr
newCallback_ZGMPDeviceProperty_isAllowCtrl(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_isAllowCtrl<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_isAllowCtrl.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_isAllowCtrlPtr
newCallback_ZGMPDeviceProperty_isAllowCtrl(T* instance, void (T::*cb)(bool, bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_isAllowCtrl<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertiesAll.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetPropertiesAll.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_mgetPropertiesAll : public Callback_ZGMPDeviceProperty_mgetPropertiesAll_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapMapStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_mgetPropertiesAll(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapMapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetPropertiesAll(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertiesAll.
 */
template<class T> Callback_ZGMPDeviceProperty_mgetPropertiesAllPtr
newCallback_ZGMPDeviceProperty_mgetPropertiesAll(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mgetPropertiesAll<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertiesAll.
 */
template<class T> Callback_ZGMPDeviceProperty_mgetPropertiesAllPtr
newCallback_ZGMPDeviceProperty_mgetPropertiesAll(T* instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mgetPropertiesAll<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertiesAll.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetPropertiesAll.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_mgetPropertiesAll : public Callback_ZGMPDeviceProperty_mgetPropertiesAll_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapMapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_mgetPropertiesAll(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapMapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetPropertiesAll(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertiesAll.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mgetPropertiesAllPtr
newCallback_ZGMPDeviceProperty_mgetPropertiesAll(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mgetPropertiesAll<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertiesAll.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mgetPropertiesAllPtr
newCallback_ZGMPDeviceProperty_mgetPropertiesAll(T* instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mgetPropertiesAll<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetProperties.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_mgetProperties : public Callback_ZGMPDeviceProperty_mgetProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapMapStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_mgetProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapMapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_mgetPropertiesPtr
newCallback_ZGMPDeviceProperty_mgetProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mgetProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_mgetPropertiesPtr
newCallback_ZGMPDeviceProperty_mgetProperties(T* instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mgetProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetProperties.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_mgetProperties : public Callback_ZGMPDeviceProperty_mgetProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapMapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_mgetProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapMapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mgetPropertiesPtr
newCallback_ZGMPDeviceProperty_mgetProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mgetProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mgetPropertiesPtr
newCallback_ZGMPDeviceProperty_mgetProperties(T* instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mgetProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetPropertyValues.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_mgetPropertyValues : public Callback_ZGMPDeviceProperty_mgetPropertyValues_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_mgetPropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetPropertyValues(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_values, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValues.
 */
template<class T> Callback_ZGMPDeviceProperty_mgetPropertyValuesPtr
newCallback_ZGMPDeviceProperty_mgetPropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mgetPropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValues.
 */
template<class T> Callback_ZGMPDeviceProperty_mgetPropertyValuesPtr
newCallback_ZGMPDeviceProperty_mgetPropertyValues(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mgetPropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetPropertyValues.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_mgetPropertyValues : public Callback_ZGMPDeviceProperty_mgetPropertyValues_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_mgetPropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetPropertyValues(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_values, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mgetPropertyValuesPtr
newCallback_ZGMPDeviceProperty_mgetPropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mgetPropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mgetPropertyValuesPtr
newCallback_ZGMPDeviceProperty_mgetPropertyValues(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mgetPropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValuesEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetPropertyValuesEx.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_mgetPropertyValuesEx : public Callback_ZGMPDeviceProperty_mgetPropertyValuesEx_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_mgetPropertyValuesEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetPropertyValuesEx(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_values, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValuesEx.
 */
template<class T> Callback_ZGMPDeviceProperty_mgetPropertyValuesExPtr
newCallback_ZGMPDeviceProperty_mgetPropertyValuesEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mgetPropertyValuesEx<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValuesEx.
 */
template<class T> Callback_ZGMPDeviceProperty_mgetPropertyValuesExPtr
newCallback_ZGMPDeviceProperty_mgetPropertyValuesEx(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mgetPropertyValuesEx<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValuesEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mgetPropertyValuesEx.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_mgetPropertyValuesEx : public Callback_ZGMPDeviceProperty_mgetPropertyValuesEx_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_mgetPropertyValuesEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mgetPropertyValuesEx(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_values, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValuesEx.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mgetPropertyValuesExPtr
newCallback_ZGMPDeviceProperty_mgetPropertyValuesEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mgetPropertyValuesEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mgetPropertyValuesEx.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mgetPropertyValuesExPtr
newCallback_ZGMPDeviceProperty_mgetPropertyValuesEx(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mgetPropertyValuesEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getGroupProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getGroupProperties.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getGroupProperties : public Callback_ZGMPDeviceProperty_getGroupProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapMapStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getGroupProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapMapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getGroupProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getGroupProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_getGroupPropertiesPtr
newCallback_ZGMPDeviceProperty_getGroupProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getGroupProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getGroupProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_getGroupPropertiesPtr
newCallback_ZGMPDeviceProperty_getGroupProperties(T* instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getGroupProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getGroupProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getGroupProperties.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getGroupProperties : public Callback_ZGMPDeviceProperty_getGroupProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapMapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getGroupProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapMapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getGroupProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getGroupProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getGroupPropertiesPtr
newCallback_ZGMPDeviceProperty_getGroupProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getGroupProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getGroupProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getGroupPropertiesPtr
newCallback_ZGMPDeviceProperty_getGroupProperties(T* instance, void (T::*cb)(bool, const MapMapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getGroupProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getTableProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getTableProperties.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getTableProperties : public Callback_ZGMPDeviceProperty_getTableProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getTableProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTableProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getTableProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_getTablePropertiesPtr
newCallback_ZGMPDeviceProperty_getTableProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getTableProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getTableProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_getTablePropertiesPtr
newCallback_ZGMPDeviceProperty_getTableProperties(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getTableProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getTableProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getTableProperties.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getTableProperties : public Callback_ZGMPDeviceProperty_getTableProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getTableProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTableProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getTableProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getTablePropertiesPtr
newCallback_ZGMPDeviceProperty_getTableProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getTableProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getTableProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getTablePropertiesPtr
newCallback_ZGMPDeviceProperty_getTableProperties(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getTableProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertiesAll.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertiesAll.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getPropertiesAll : public Callback_ZGMPDeviceProperty_getPropertiesAll_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getPropertiesAll(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertiesAll(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertiesAll.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertiesAllPtr
newCallback_ZGMPDeviceProperty_getPropertiesAll(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertiesAll<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertiesAll.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertiesAllPtr
newCallback_ZGMPDeviceProperty_getPropertiesAll(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertiesAll<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertiesAll.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertiesAll.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getPropertiesAll : public Callback_ZGMPDeviceProperty_getPropertiesAll_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getPropertiesAll(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertiesAll(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertiesAll.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertiesAllPtr
newCallback_ZGMPDeviceProperty_getPropertiesAll(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertiesAll<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertiesAll.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertiesAllPtr
newCallback_ZGMPDeviceProperty_getPropertiesAll(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertiesAll<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getProperties.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getProperties : public Callback_ZGMPDeviceProperty_getProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertiesPtr
newCallback_ZGMPDeviceProperty_getProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertiesPtr
newCallback_ZGMPDeviceProperty_getProperties(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getProperties.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getProperties : public Callback_ZGMPDeviceProperty_getProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertiesPtr
newCallback_ZGMPDeviceProperty_getProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertiesPtr
newCallback_ZGMPDeviceProperty_getProperties(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getProperty.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getProperty : public Callback_ZGMPDeviceProperty_getProperty_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_property;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getProperty(iceP_property, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_property, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperty.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyPtr
newCallback_ZGMPDeviceProperty_getProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperty.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyPtr
newCallback_ZGMPDeviceProperty_getProperty(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getProperty.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getProperty : public Callback_ZGMPDeviceProperty_getProperty_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_property;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getProperty(iceP_property, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_property, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperty.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyPtr
newCallback_ZGMPDeviceProperty_getProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getProperty.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyPtr
newCallback_ZGMPDeviceProperty_getProperty(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValues.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getPropertyValues : public Callback_ZGMPDeviceProperty_getPropertyValues_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getPropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValues(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_values, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValues.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyValuesPtr
newCallback_ZGMPDeviceProperty_getPropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValues.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyValuesPtr
newCallback_ZGMPDeviceProperty_getPropertyValues(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValues.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getPropertyValues : public Callback_ZGMPDeviceProperty_getPropertyValues_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getPropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValues(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_values, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyValuesPtr
newCallback_ZGMPDeviceProperty_getPropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyValuesPtr
newCallback_ZGMPDeviceProperty_getPropertyValues(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValuesEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValuesEx.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getPropertyValuesEx : public Callback_ZGMPDeviceProperty_getPropertyValuesEx_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getPropertyValuesEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValuesEx(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_values, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValuesEx.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyValuesExPtr
newCallback_ZGMPDeviceProperty_getPropertyValuesEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertyValuesEx<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValuesEx.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyValuesExPtr
newCallback_ZGMPDeviceProperty_getPropertyValuesEx(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertyValuesEx<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValuesEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValuesEx.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getPropertyValuesEx : public Callback_ZGMPDeviceProperty_getPropertyValuesEx_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getPropertyValuesEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValuesEx(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_values, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValuesEx.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyValuesExPtr
newCallback_ZGMPDeviceProperty_getPropertyValuesEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertyValuesEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValuesEx.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyValuesExPtr
newCallback_ZGMPDeviceProperty_getPropertyValuesEx(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertyValuesEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValue.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getPropertyValue : public Callback_ZGMPDeviceProperty_getPropertyValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getPropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_value;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValue(iceP_value, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_value, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValue.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyValuePtr
newCallback_ZGMPDeviceProperty_getPropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValue.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyValuePtr
newCallback_ZGMPDeviceProperty_getPropertyValue(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValue.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getPropertyValue : public Callback_ZGMPDeviceProperty_getPropertyValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getPropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_value;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValue(iceP_value, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_value, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValue.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyValuePtr
newCallback_ZGMPDeviceProperty_getPropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValue.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyValuePtr
newCallback_ZGMPDeviceProperty_getPropertyValue(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValueEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValueEx.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getPropertyValueEx : public Callback_ZGMPDeviceProperty_getPropertyValueEx_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getPropertyValueEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_value;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValueEx(iceP_value, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_value, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValueEx.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyValueExPtr
newCallback_ZGMPDeviceProperty_getPropertyValueEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertyValueEx<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValueEx.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyValueExPtr
newCallback_ZGMPDeviceProperty_getPropertyValueEx(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertyValueEx<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValueEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyValueEx.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getPropertyValueEx : public Callback_ZGMPDeviceProperty_getPropertyValueEx_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getPropertyValueEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_value;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValueEx(iceP_value, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_value, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValueEx.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyValueExPtr
newCallback_ZGMPDeviceProperty_getPropertyValueEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertyValueEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyValueEx.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyValueExPtr
newCallback_ZGMPDeviceProperty_getPropertyValueEx(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertyValueEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdateProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mupdateProperties.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_mupdateProperties : public Callback_ZGMPDeviceProperty_mupdateProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_mupdateProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mupdateProperties(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdateProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_mupdatePropertiesPtr
newCallback_ZGMPDeviceProperty_mupdateProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mupdateProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdateProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_mupdatePropertiesPtr
newCallback_ZGMPDeviceProperty_mupdateProperties(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mupdateProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdateProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mupdateProperties.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_mupdateProperties : public Callback_ZGMPDeviceProperty_mupdateProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_mupdateProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mupdateProperties(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdateProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mupdatePropertiesPtr
newCallback_ZGMPDeviceProperty_mupdateProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mupdateProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdateProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mupdatePropertiesPtr
newCallback_ZGMPDeviceProperty_mupdateProperties(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mupdateProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mupdatePropertyValues.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_mupdatePropertyValues : public Callback_ZGMPDeviceProperty_mupdatePropertyValues_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_mupdatePropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mupdatePropertyValues(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdatePropertyValues.
 */
template<class T> Callback_ZGMPDeviceProperty_mupdatePropertyValuesPtr
newCallback_ZGMPDeviceProperty_mupdatePropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mupdatePropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdatePropertyValues.
 */
template<class T> Callback_ZGMPDeviceProperty_mupdatePropertyValuesPtr
newCallback_ZGMPDeviceProperty_mupdatePropertyValues(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_mupdatePropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_mupdatePropertyValues.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_mupdatePropertyValues : public Callback_ZGMPDeviceProperty_mupdatePropertyValues_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_mupdatePropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_mupdatePropertyValues(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdatePropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mupdatePropertyValuesPtr
newCallback_ZGMPDeviceProperty_mupdatePropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mupdatePropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_mupdatePropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_mupdatePropertyValuesPtr
newCallback_ZGMPDeviceProperty_mupdatePropertyValues(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_mupdatePropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updateProperty.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_updateProperty : public Callback_ZGMPDeviceProperty_updateProperty_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_updateProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateProperty(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperty.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertyPtr
newCallback_ZGMPDeviceProperty_updateProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updateProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperty.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertyPtr
newCallback_ZGMPDeviceProperty_updateProperty(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updateProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updateProperty.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_updateProperty : public Callback_ZGMPDeviceProperty_updateProperty_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_updateProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateProperty(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperty.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertyPtr
newCallback_ZGMPDeviceProperty_updateProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updateProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperty.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertyPtr
newCallback_ZGMPDeviceProperty_updateProperty(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updateProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updateProperties.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_updateProperties : public Callback_ZGMPDeviceProperty_updateProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_updateProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateProperties(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertiesPtr
newCallback_ZGMPDeviceProperty_updateProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updateProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperties.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertiesPtr
newCallback_ZGMPDeviceProperty_updateProperties(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updateProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updateProperties.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_updateProperties : public Callback_ZGMPDeviceProperty_updateProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_updateProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateProperties(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertiesPtr
newCallback_ZGMPDeviceProperty_updateProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updateProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updateProperties.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertiesPtr
newCallback_ZGMPDeviceProperty_updateProperties(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updateProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValues.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_updatePropertyValues : public Callback_ZGMPDeviceProperty_updatePropertyValues_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_updatePropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValues(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValues.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertyValuesPtr
newCallback_ZGMPDeviceProperty_updatePropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updatePropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValues.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertyValuesPtr
newCallback_ZGMPDeviceProperty_updatePropertyValues(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updatePropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValues.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_updatePropertyValues : public Callback_ZGMPDeviceProperty_updatePropertyValues_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_updatePropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValues(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertyValuesPtr
newCallback_ZGMPDeviceProperty_updatePropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updatePropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertyValuesPtr
newCallback_ZGMPDeviceProperty_updatePropertyValues(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updatePropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValuesEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValuesEx.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_updatePropertyValuesEx : public Callback_ZGMPDeviceProperty_updatePropertyValuesEx_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_updatePropertyValuesEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValuesEx(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValuesEx.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertyValuesExPtr
newCallback_ZGMPDeviceProperty_updatePropertyValuesEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updatePropertyValuesEx<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValuesEx.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertyValuesExPtr
newCallback_ZGMPDeviceProperty_updatePropertyValuesEx(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updatePropertyValuesEx<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValuesEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValuesEx.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_updatePropertyValuesEx : public Callback_ZGMPDeviceProperty_updatePropertyValuesEx_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_updatePropertyValuesEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValuesEx(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValuesEx.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertyValuesExPtr
newCallback_ZGMPDeviceProperty_updatePropertyValuesEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updatePropertyValuesEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValuesEx.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertyValuesExPtr
newCallback_ZGMPDeviceProperty_updatePropertyValuesEx(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updatePropertyValuesEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValue.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_updatePropertyValue : public Callback_ZGMPDeviceProperty_updatePropertyValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_updatePropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValue.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertyValuePtr
newCallback_ZGMPDeviceProperty_updatePropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updatePropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValue.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertyValuePtr
newCallback_ZGMPDeviceProperty_updatePropertyValue(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updatePropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValue.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_updatePropertyValue : public Callback_ZGMPDeviceProperty_updatePropertyValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_updatePropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValue.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertyValuePtr
newCallback_ZGMPDeviceProperty_updatePropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updatePropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValue.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertyValuePtr
newCallback_ZGMPDeviceProperty_updatePropertyValue(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updatePropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValueEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValueEx.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_updatePropertyValueEx : public Callback_ZGMPDeviceProperty_updatePropertyValueEx_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_updatePropertyValueEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValueEx(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValueEx.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertyValueExPtr
newCallback_ZGMPDeviceProperty_updatePropertyValueEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updatePropertyValueEx<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValueEx.
 */
template<class T> Callback_ZGMPDeviceProperty_updatePropertyValueExPtr
newCallback_ZGMPDeviceProperty_updatePropertyValueEx(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_updatePropertyValueEx<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValueEx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_updatePropertyValueEx.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_updatePropertyValueEx : public Callback_ZGMPDeviceProperty_updatePropertyValueEx_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_updatePropertyValueEx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValueEx(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValueEx.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertyValueExPtr
newCallback_ZGMPDeviceProperty_updatePropertyValueEx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updatePropertyValueEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_updatePropertyValueEx.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_updatePropertyValueExPtr
newCallback_ZGMPDeviceProperty_updatePropertyValueEx(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_updatePropertyValueEx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getDataIDByProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getDataIDByProperty.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getDataIDByProperty : public Callback_ZGMPDeviceProperty_getDataIDByProperty_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getDataIDByProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_tableName;
        ::std::string iceP_dataID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDataIDByProperty(iceP_tableName, iceP_dataID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_tableName, iceP_dataID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getDataIDByProperty.
 */
template<class T> Callback_ZGMPDeviceProperty_getDataIDByPropertyPtr
newCallback_ZGMPDeviceProperty_getDataIDByProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getDataIDByProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getDataIDByProperty.
 */
template<class T> Callback_ZGMPDeviceProperty_getDataIDByPropertyPtr
newCallback_ZGMPDeviceProperty_getDataIDByProperty(T* instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getDataIDByProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getDataIDByProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getDataIDByProperty.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getDataIDByProperty : public Callback_ZGMPDeviceProperty_getDataIDByProperty_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getDataIDByProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_tableName;
        ::std::string iceP_dataID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDataIDByProperty(iceP_tableName, iceP_dataID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_tableName, iceP_dataID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getDataIDByProperty.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getDataIDByPropertyPtr
newCallback_ZGMPDeviceProperty_getDataIDByProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getDataIDByProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getDataIDByProperty.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getDataIDByPropertyPtr
newCallback_ZGMPDeviceProperty_getDataIDByProperty(T* instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getDataIDByProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyByDataID.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyByDataID.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_getPropertyByDataID : public Callback_ZGMPDeviceProperty_getPropertyByDataID_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_getPropertyByDataID(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_deviceID;
        ::std::string iceP_name;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyByDataID(iceP_deviceID, iceP_name, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_deviceID, iceP_name, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyByDataID.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyByDataIDPtr
newCallback_ZGMPDeviceProperty_getPropertyByDataID(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertyByDataID<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyByDataID.
 */
template<class T> Callback_ZGMPDeviceProperty_getPropertyByDataIDPtr
newCallback_ZGMPDeviceProperty_getPropertyByDataID(T* instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_getPropertyByDataID<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyByDataID.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_getPropertyByDataID.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_getPropertyByDataID : public Callback_ZGMPDeviceProperty_getPropertyByDataID_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_getPropertyByDataID(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_deviceID;
        ::std::string iceP_name;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyByDataID(iceP_deviceID, iceP_name, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_deviceID, iceP_name, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyByDataID.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyByDataIDPtr
newCallback_ZGMPDeviceProperty_getPropertyByDataID(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertyByDataID<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_getPropertyByDataID.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_getPropertyByDataIDPtr
newCallback_ZGMPDeviceProperty_getPropertyByDataID(T* instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_getPropertyByDataID<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_isPropertyExists.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_isPropertyExists.
 */
template<class T>
class CallbackNC_ZGMPDeviceProperty_isPropertyExists : public Callback_ZGMPDeviceProperty_isPropertyExists_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&);

    CallbackNC_ZGMPDeviceProperty_isPropertyExists(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        bool iceP_exists;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isPropertyExists(iceP_exists, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_exists, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_isPropertyExists.
 */
template<class T> Callback_ZGMPDeviceProperty_isPropertyExistsPtr
newCallback_ZGMPDeviceProperty_isPropertyExists(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_isPropertyExists<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_isPropertyExists.
 */
template<class T> Callback_ZGMPDeviceProperty_isPropertyExistsPtr
newCallback_ZGMPDeviceProperty_isPropertyExists(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDeviceProperty_isPropertyExists<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDeviceProperty::begin_isPropertyExists.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDeviceProperty_isPropertyExists.
 */
template<class T, typename CT>
class Callback_ZGMPDeviceProperty_isPropertyExists : public Callback_ZGMPDeviceProperty_isPropertyExists_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDeviceProperty_isPropertyExists(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDevicePropertyPrx proxy = ZGMPDevicePropertyPrx::uncheckedCast(result->getProxy());
        bool iceP_exists;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isPropertyExists(iceP_exists, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_exists, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_isPropertyExists.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_isPropertyExistsPtr
newCallback_ZGMPDeviceProperty_isPropertyExists(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_isPropertyExists<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDeviceProperty::begin_isPropertyExists.
 */
template<class T, typename CT> Callback_ZGMPDeviceProperty_isPropertyExistsPtr
newCallback_ZGMPDeviceProperty_isPropertyExists(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDeviceProperty_isPropertyExists<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

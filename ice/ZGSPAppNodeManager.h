//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPAppNodeManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPAppNodeManager_h__
#define __ZGSPAppNodeManager_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPAppNodeManager;
class ZGSPAppNodeManagerPrx;

}

namespace ZG6000
{

class ZGSPAppNodeManager : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPAppNodeManagerPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getChild.
     */
    struct GetChildResult
    {
        bool returnValue;
        ListStringMap listChild;
        ErrorInfo e;
    };

    virtual bool getChild(::std::string appNodeID, ListStringMap& listChild, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getChild(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getParent.
     */
    struct GetParentResult
    {
        bool returnValue;
        ListStringMap listParent;
        ErrorInfo e;
    };

    virtual bool getParent(::std::string appNodeID, ListStringMap& listParent, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getParent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDescendant.
     */
    struct GetDescendantResult
    {
        bool returnValue;
        ListStringMap listDescendant;
        ErrorInfo e;
    };

    virtual bool getDescendant(::std::string appNodeID, ListStringMap& listDescendant, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDescendant(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getAncestor.
     */
    struct GetAncestorResult
    {
        bool returnValue;
        ListStringMap listAncestor;
        ErrorInfo e;
    };

    virtual bool getAncestor(::std::string appNodeID, ListStringMap& listAncestor, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getAncestor(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getListYv.
     */
    struct GetListYvResult
    {
        bool returnValue;
        ListStringMap listYv;
        ErrorInfo e;
    };

    virtual bool getListYv(::std::string appNodeID, ListStringMap& listYv, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getListYv(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPAppNodeManagerPrx : public virtual ::Ice::Proxy<ZGSPAppNodeManagerPrx, ZGServerBasePrx>
{
public:

    bool getChild(const ::std::string& appNodeID, ListStringMap& listChild, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPAppNodeManager::GetChildResult>(true, this, &ZGSPAppNodeManagerPrx::_iceI_getChild, appNodeID, context).get();
        listChild = ::std::move(_result.listChild);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getChildAsync(const ::std::string& appNodeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPAppNodeManager::GetChildResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPAppNodeManager::GetChildResult, P>(false, this, &ZGSPAppNodeManagerPrx::_iceI_getChild, appNodeID, context);
    }

    ::std::function<void()>
    getChildAsync(const ::std::string& appNodeID,
                  ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPAppNodeManager::GetChildResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listChild), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPAppNodeManager::GetChildResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPAppNodeManagerPrx::_iceI_getChild, appNodeID, context);
    }

    /// \cond INTERNAL
    void _iceI_getChild(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPAppNodeManager::GetChildResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getParent(const ::std::string& appNodeID, ListStringMap& listParent, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPAppNodeManager::GetParentResult>(true, this, &ZGSPAppNodeManagerPrx::_iceI_getParent, appNodeID, context).get();
        listParent = ::std::move(_result.listParent);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getParentAsync(const ::std::string& appNodeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPAppNodeManager::GetParentResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPAppNodeManager::GetParentResult, P>(false, this, &ZGSPAppNodeManagerPrx::_iceI_getParent, appNodeID, context);
    }

    ::std::function<void()>
    getParentAsync(const ::std::string& appNodeID,
                   ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPAppNodeManager::GetParentResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listParent), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPAppNodeManager::GetParentResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPAppNodeManagerPrx::_iceI_getParent, appNodeID, context);
    }

    /// \cond INTERNAL
    void _iceI_getParent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPAppNodeManager::GetParentResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getDescendant(const ::std::string& appNodeID, ListStringMap& listDescendant, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPAppNodeManager::GetDescendantResult>(true, this, &ZGSPAppNodeManagerPrx::_iceI_getDescendant, appNodeID, context).get();
        listDescendant = ::std::move(_result.listDescendant);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDescendantAsync(const ::std::string& appNodeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPAppNodeManager::GetDescendantResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPAppNodeManager::GetDescendantResult, P>(false, this, &ZGSPAppNodeManagerPrx::_iceI_getDescendant, appNodeID, context);
    }

    ::std::function<void()>
    getDescendantAsync(const ::std::string& appNodeID,
                       ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPAppNodeManager::GetDescendantResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listDescendant), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPAppNodeManager::GetDescendantResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPAppNodeManagerPrx::_iceI_getDescendant, appNodeID, context);
    }

    /// \cond INTERNAL
    void _iceI_getDescendant(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPAppNodeManager::GetDescendantResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getAncestor(const ::std::string& appNodeID, ListStringMap& listAncestor, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPAppNodeManager::GetAncestorResult>(true, this, &ZGSPAppNodeManagerPrx::_iceI_getAncestor, appNodeID, context).get();
        listAncestor = ::std::move(_result.listAncestor);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getAncestorAsync(const ::std::string& appNodeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPAppNodeManager::GetAncestorResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPAppNodeManager::GetAncestorResult, P>(false, this, &ZGSPAppNodeManagerPrx::_iceI_getAncestor, appNodeID, context);
    }

    ::std::function<void()>
    getAncestorAsync(const ::std::string& appNodeID,
                     ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPAppNodeManager::GetAncestorResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listAncestor), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPAppNodeManager::GetAncestorResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPAppNodeManagerPrx::_iceI_getAncestor, appNodeID, context);
    }

    /// \cond INTERNAL
    void _iceI_getAncestor(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPAppNodeManager::GetAncestorResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getListYv(const ::std::string& appNodeID, ListStringMap& listYv, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPAppNodeManager::GetListYvResult>(true, this, &ZGSPAppNodeManagerPrx::_iceI_getListYv, appNodeID, context).get();
        listYv = ::std::move(_result.listYv);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getListYvAsync(const ::std::string& appNodeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPAppNodeManager::GetListYvResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPAppNodeManager::GetListYvResult, P>(false, this, &ZGSPAppNodeManagerPrx::_iceI_getListYv, appNodeID, context);
    }

    ::std::function<void()>
    getListYvAsync(const ::std::string& appNodeID,
                   ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPAppNodeManager::GetListYvResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listYv), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPAppNodeManager::GetListYvResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPAppNodeManagerPrx::_iceI_getListYv, appNodeID, context);
    }

    /// \cond INTERNAL
    void _iceI_getListYv(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPAppNodeManager::GetListYvResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPAppNodeManagerPrx() = default;
    friend ::std::shared_ptr<ZGSPAppNodeManagerPrx> IceInternal::createProxy<ZGSPAppNodeManagerPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPAppNodeManagerPtr = ::std::shared_ptr<ZGSPAppNodeManager>;
using ZGSPAppNodeManagerPrxPtr = ::std::shared_ptr<ZGSPAppNodeManagerPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPAppNodeManager;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPAppNodeManager>&);
::IceProxy::Ice::Object* upCast(ZGSPAppNodeManager*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPAppNodeManager;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPAppNodeManager*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPAppNodeManager> ZGSPAppNodeManagerPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPAppNodeManager> ZGSPAppNodeManagerPrx;
typedef ZGSPAppNodeManagerPrx ZGSPAppNodeManagerPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPAppNodeManagerPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getChild.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getChild.
 */
class Callback_ZGSPAppNodeManager_getChild_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPAppNodeManager_getChild_Base> Callback_ZGSPAppNodeManager_getChildPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getParent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getParent.
 */
class Callback_ZGSPAppNodeManager_getParent_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPAppNodeManager_getParent_Base> Callback_ZGSPAppNodeManager_getParentPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getDescendant.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getDescendant.
 */
class Callback_ZGSPAppNodeManager_getDescendant_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPAppNodeManager_getDescendant_Base> Callback_ZGSPAppNodeManager_getDescendantPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getAncestor.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getAncestor.
 */
class Callback_ZGSPAppNodeManager_getAncestor_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPAppNodeManager_getAncestor_Base> Callback_ZGSPAppNodeManager_getAncestorPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getListYv.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getListYv.
 */
class Callback_ZGSPAppNodeManager_getListYv_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPAppNodeManager_getListYv_Base> Callback_ZGSPAppNodeManager_getListYvPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPAppNodeManager : public virtual ::Ice::Proxy<ZGSPAppNodeManager, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool getChild(const ::std::string& appNodeID, ::ZG6000::ListStringMap& listChild, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getChild(listChild, e, _iceI_begin_getChild(appNodeID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getChild(const ::std::string& appNodeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getChild(appNodeID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getChild(const ::std::string& appNodeID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getChild(appNodeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getChild(const ::std::string& appNodeID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getChild(appNodeID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getChild(const ::std::string& appNodeID, const ::ZG6000::Callback_ZGSPAppNodeManager_getChildPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getChild(appNodeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getChild(const ::std::string& appNodeID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPAppNodeManager_getChildPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getChild(appNodeID, context, cb, cookie);
    }

    bool end_getChild(::ZG6000::ListStringMap& listChild, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getChild(::ZG6000::ListStringMap& iceP_listChild, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getChild(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getParent(const ::std::string& appNodeID, ::ZG6000::ListStringMap& listParent, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getParent(listParent, e, _iceI_begin_getParent(appNodeID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getParent(const ::std::string& appNodeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getParent(appNodeID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getParent(const ::std::string& appNodeID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getParent(appNodeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getParent(const ::std::string& appNodeID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getParent(appNodeID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getParent(const ::std::string& appNodeID, const ::ZG6000::Callback_ZGSPAppNodeManager_getParentPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getParent(appNodeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getParent(const ::std::string& appNodeID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPAppNodeManager_getParentPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getParent(appNodeID, context, cb, cookie);
    }

    bool end_getParent(::ZG6000::ListStringMap& listParent, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getParent(::ZG6000::ListStringMap& iceP_listParent, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getParent(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDescendant(const ::std::string& appNodeID, ::ZG6000::ListStringMap& listDescendant, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDescendant(listDescendant, e, _iceI_begin_getDescendant(appNodeID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDescendant(const ::std::string& appNodeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDescendant(appNodeID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDescendant(const ::std::string& appNodeID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDescendant(appNodeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDescendant(const ::std::string& appNodeID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDescendant(appNodeID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDescendant(const ::std::string& appNodeID, const ::ZG6000::Callback_ZGSPAppNodeManager_getDescendantPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDescendant(appNodeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDescendant(const ::std::string& appNodeID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPAppNodeManager_getDescendantPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDescendant(appNodeID, context, cb, cookie);
    }

    bool end_getDescendant(::ZG6000::ListStringMap& listDescendant, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDescendant(::ZG6000::ListStringMap& iceP_listDescendant, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDescendant(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getAncestor(const ::std::string& appNodeID, ::ZG6000::ListStringMap& listAncestor, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getAncestor(listAncestor, e, _iceI_begin_getAncestor(appNodeID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getAncestor(const ::std::string& appNodeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getAncestor(appNodeID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getAncestor(const ::std::string& appNodeID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAncestor(appNodeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getAncestor(const ::std::string& appNodeID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAncestor(appNodeID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getAncestor(const ::std::string& appNodeID, const ::ZG6000::Callback_ZGSPAppNodeManager_getAncestorPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAncestor(appNodeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getAncestor(const ::std::string& appNodeID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPAppNodeManager_getAncestorPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAncestor(appNodeID, context, cb, cookie);
    }

    bool end_getAncestor(::ZG6000::ListStringMap& listAncestor, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getAncestor(::ZG6000::ListStringMap& iceP_listAncestor, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getAncestor(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getListYv(const ::std::string& appNodeID, ::ZG6000::ListStringMap& listYv, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getListYv(listYv, e, _iceI_begin_getListYv(appNodeID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getListYv(const ::std::string& appNodeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getListYv(appNodeID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getListYv(const ::std::string& appNodeID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getListYv(appNodeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getListYv(const ::std::string& appNodeID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getListYv(appNodeID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getListYv(const ::std::string& appNodeID, const ::ZG6000::Callback_ZGSPAppNodeManager_getListYvPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getListYv(appNodeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getListYv(const ::std::string& appNodeID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPAppNodeManager_getListYvPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getListYv(appNodeID, context, cb, cookie);
    }

    bool end_getListYv(::ZG6000::ListStringMap& listYv, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getListYv(::ZG6000::ListStringMap& iceP_listYv, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getListYv(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPAppNodeManager : virtual public ZGServerBase
{
public:

    typedef ZGSPAppNodeManagerPrx ProxyType;
    typedef ZGSPAppNodeManagerPtr PointerType;

    virtual ~ZGSPAppNodeManager();

#ifdef ICE_CPP11_COMPILER
    ZGSPAppNodeManager() = default;
    ZGSPAppNodeManager(const ZGSPAppNodeManager&) = default;
    ZGSPAppNodeManager& operator=(const ZGSPAppNodeManager&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool getChild(const ::std::string& appNodeID, ListStringMap& listChild, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getChild(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getParent(const ::std::string& appNodeID, ListStringMap& listParent, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getParent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDescendant(const ::std::string& appNodeID, ListStringMap& listDescendant, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDescendant(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getAncestor(const ::std::string& appNodeID, ListStringMap& listAncestor, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getAncestor(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getListYv(const ::std::string& appNodeID, ListStringMap& listYv, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getListYv(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPAppNodeManager& lhs, const ZGSPAppNodeManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPAppNodeManager& lhs, const ZGSPAppNodeManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getChild.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getChild.
 */
template<class T>
class CallbackNC_ZGSPAppNodeManager_getChild : public Callback_ZGSPAppNodeManager_getChild_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPAppNodeManager_getChild(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPAppNodeManagerPrx proxy = ZGSPAppNodeManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listChild;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getChild(iceP_listChild, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listChild, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getChild.
 */
template<class T> Callback_ZGSPAppNodeManager_getChildPtr
newCallback_ZGSPAppNodeManager_getChild(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPAppNodeManager_getChild<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getChild.
 */
template<class T> Callback_ZGSPAppNodeManager_getChildPtr
newCallback_ZGSPAppNodeManager_getChild(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPAppNodeManager_getChild<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getChild.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getChild.
 */
template<class T, typename CT>
class Callback_ZGSPAppNodeManager_getChild : public Callback_ZGSPAppNodeManager_getChild_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPAppNodeManager_getChild(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPAppNodeManagerPrx proxy = ZGSPAppNodeManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listChild;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getChild(iceP_listChild, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listChild, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getChild.
 */
template<class T, typename CT> Callback_ZGSPAppNodeManager_getChildPtr
newCallback_ZGSPAppNodeManager_getChild(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPAppNodeManager_getChild<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getChild.
 */
template<class T, typename CT> Callback_ZGSPAppNodeManager_getChildPtr
newCallback_ZGSPAppNodeManager_getChild(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPAppNodeManager_getChild<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getParent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getParent.
 */
template<class T>
class CallbackNC_ZGSPAppNodeManager_getParent : public Callback_ZGSPAppNodeManager_getParent_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPAppNodeManager_getParent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPAppNodeManagerPrx proxy = ZGSPAppNodeManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listParent;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getParent(iceP_listParent, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listParent, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getParent.
 */
template<class T> Callback_ZGSPAppNodeManager_getParentPtr
newCallback_ZGSPAppNodeManager_getParent(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPAppNodeManager_getParent<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getParent.
 */
template<class T> Callback_ZGSPAppNodeManager_getParentPtr
newCallback_ZGSPAppNodeManager_getParent(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPAppNodeManager_getParent<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getParent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getParent.
 */
template<class T, typename CT>
class Callback_ZGSPAppNodeManager_getParent : public Callback_ZGSPAppNodeManager_getParent_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPAppNodeManager_getParent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPAppNodeManagerPrx proxy = ZGSPAppNodeManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listParent;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getParent(iceP_listParent, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listParent, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getParent.
 */
template<class T, typename CT> Callback_ZGSPAppNodeManager_getParentPtr
newCallback_ZGSPAppNodeManager_getParent(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPAppNodeManager_getParent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getParent.
 */
template<class T, typename CT> Callback_ZGSPAppNodeManager_getParentPtr
newCallback_ZGSPAppNodeManager_getParent(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPAppNodeManager_getParent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getDescendant.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getDescendant.
 */
template<class T>
class CallbackNC_ZGSPAppNodeManager_getDescendant : public Callback_ZGSPAppNodeManager_getDescendant_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPAppNodeManager_getDescendant(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPAppNodeManagerPrx proxy = ZGSPAppNodeManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listDescendant;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDescendant(iceP_listDescendant, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listDescendant, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getDescendant.
 */
template<class T> Callback_ZGSPAppNodeManager_getDescendantPtr
newCallback_ZGSPAppNodeManager_getDescendant(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPAppNodeManager_getDescendant<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getDescendant.
 */
template<class T> Callback_ZGSPAppNodeManager_getDescendantPtr
newCallback_ZGSPAppNodeManager_getDescendant(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPAppNodeManager_getDescendant<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getDescendant.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getDescendant.
 */
template<class T, typename CT>
class Callback_ZGSPAppNodeManager_getDescendant : public Callback_ZGSPAppNodeManager_getDescendant_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPAppNodeManager_getDescendant(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPAppNodeManagerPrx proxy = ZGSPAppNodeManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listDescendant;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDescendant(iceP_listDescendant, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listDescendant, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getDescendant.
 */
template<class T, typename CT> Callback_ZGSPAppNodeManager_getDescendantPtr
newCallback_ZGSPAppNodeManager_getDescendant(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPAppNodeManager_getDescendant<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getDescendant.
 */
template<class T, typename CT> Callback_ZGSPAppNodeManager_getDescendantPtr
newCallback_ZGSPAppNodeManager_getDescendant(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPAppNodeManager_getDescendant<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getAncestor.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getAncestor.
 */
template<class T>
class CallbackNC_ZGSPAppNodeManager_getAncestor : public Callback_ZGSPAppNodeManager_getAncestor_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPAppNodeManager_getAncestor(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPAppNodeManagerPrx proxy = ZGSPAppNodeManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listAncestor;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getAncestor(iceP_listAncestor, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listAncestor, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getAncestor.
 */
template<class T> Callback_ZGSPAppNodeManager_getAncestorPtr
newCallback_ZGSPAppNodeManager_getAncestor(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPAppNodeManager_getAncestor<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getAncestor.
 */
template<class T> Callback_ZGSPAppNodeManager_getAncestorPtr
newCallback_ZGSPAppNodeManager_getAncestor(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPAppNodeManager_getAncestor<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getAncestor.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getAncestor.
 */
template<class T, typename CT>
class Callback_ZGSPAppNodeManager_getAncestor : public Callback_ZGSPAppNodeManager_getAncestor_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPAppNodeManager_getAncestor(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPAppNodeManagerPrx proxy = ZGSPAppNodeManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listAncestor;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getAncestor(iceP_listAncestor, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listAncestor, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getAncestor.
 */
template<class T, typename CT> Callback_ZGSPAppNodeManager_getAncestorPtr
newCallback_ZGSPAppNodeManager_getAncestor(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPAppNodeManager_getAncestor<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getAncestor.
 */
template<class T, typename CT> Callback_ZGSPAppNodeManager_getAncestorPtr
newCallback_ZGSPAppNodeManager_getAncestor(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPAppNodeManager_getAncestor<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getListYv.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getListYv.
 */
template<class T>
class CallbackNC_ZGSPAppNodeManager_getListYv : public Callback_ZGSPAppNodeManager_getListYv_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPAppNodeManager_getListYv(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPAppNodeManagerPrx proxy = ZGSPAppNodeManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listYv;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getListYv(iceP_listYv, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listYv, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getListYv.
 */
template<class T> Callback_ZGSPAppNodeManager_getListYvPtr
newCallback_ZGSPAppNodeManager_getListYv(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPAppNodeManager_getListYv<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getListYv.
 */
template<class T> Callback_ZGSPAppNodeManager_getListYvPtr
newCallback_ZGSPAppNodeManager_getListYv(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPAppNodeManager_getListYv<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPAppNodeManager::begin_getListYv.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPAppNodeManager_getListYv.
 */
template<class T, typename CT>
class Callback_ZGSPAppNodeManager_getListYv : public Callback_ZGSPAppNodeManager_getListYv_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPAppNodeManager_getListYv(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPAppNodeManagerPrx proxy = ZGSPAppNodeManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listYv;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getListYv(iceP_listYv, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listYv, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getListYv.
 */
template<class T, typename CT> Callback_ZGSPAppNodeManager_getListYvPtr
newCallback_ZGSPAppNodeManager_getListYv(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPAppNodeManager_getListYv<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPAppNodeManager::begin_getListYv.
 */
template<class T, typename CT> Callback_ZGSPAppNodeManager_getListYvPtr
newCallback_ZGSPAppNodeManager_getListYv(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPAppNodeManager_getListYv<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

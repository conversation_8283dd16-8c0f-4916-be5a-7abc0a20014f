//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPLocalProcess.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGMPLocalProcess.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPLocalProcess_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPLocalProcess",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGMPLocalProcess_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

bool
ZG6000::ZGMPLocalProcess::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPLocalProcess_ids, iceC_ZG6000_ZGMPLocalProcess_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGMPLocalProcess::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGMPLocalProcess_ids[0], &iceC_ZG6000_ZGMPLocalProcess_ids[3]);
}

::std::string
ZG6000::ZGMPLocalProcess::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPLocalProcess::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGMPLocalProcess";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGMPLocalProcess::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPLocalProcess_ops, iceC_ZG6000_ZGMPLocalProcess_ops + 15, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPLocalProcess_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getVersion(in, current);
        }
        case 4:
        {
            return _iceD_heartDebug(in, current);
        }
        case 5:
        {
            return _iceD_ice_id(in, current);
        }
        case 6:
        {
            return _iceD_ice_ids(in, current);
        }
        case 7:
        {
            return _iceD_ice_isA(in, current);
        }
        case 8:
        {
            return _iceD_ice_ping(in, current);
        }
        case 9:
        {
            return _iceD_isDebugging(in, current);
        }
        case 10:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 11:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 12:
        {
            return _iceD_startDebug(in, current);
        }
        case 13:
        {
            return _iceD_stopDebug(in, current);
        }
        case 14:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGMPLocalProcessPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGMPLocalProcessPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGMPLocalProcessPrx::ice_staticId()
{
    return ZGMPLocalProcess::ice_staticId();
}

#else // C++98 mapping

namespace
{

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGMPLocalProcess* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGMPLocalProcess>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGMPLocalProcess;
        v->_copyFrom(proxy);
    }
}
/// \endcond

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGMPLocalProcess::_newInstance() const
{
    return new ZGMPLocalProcess;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGMPLocalProcess::ice_staticId()
{
    return ::ZG6000::ZGMPLocalProcess::ice_staticId();
}

ZG6000::ZGMPLocalProcess::~ZGMPLocalProcess()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGMPLocalProcess* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPLocalProcess_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPLocalProcess",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGMPLocalProcess::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPLocalProcess_ids, iceC_ZG6000_ZGMPLocalProcess_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGMPLocalProcess::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGMPLocalProcess_ids[0], &iceC_ZG6000_ZGMPLocalProcess_ids[3]);
}

const ::std::string&
ZG6000::ZGMPLocalProcess::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPLocalProcess::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGMPLocalProcess";
    return typeId;
#else
    return iceC_ZG6000_ZGMPLocalProcess_ids[1];
#endif
}

namespace
{
const ::std::string iceC_ZG6000_ZGMPLocalProcess_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGMPLocalProcess::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPLocalProcess_all, iceC_ZG6000_ZGMPLocalProcess_all + 15, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPLocalProcess_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getVersion(in, current);
        }
        case 4:
        {
            return _iceD_heartDebug(in, current);
        }
        case 5:
        {
            return _iceD_ice_id(in, current);
        }
        case 6:
        {
            return _iceD_ice_ids(in, current);
        }
        case 7:
        {
            return _iceD_ice_isA(in, current);
        }
        case 8:
        {
            return _iceD_ice_ping(in, current);
        }
        case 9:
        {
            return _iceD_isDebugging(in, current);
        }
        case 10:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 11:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 12:
        {
            return _iceD_startDebug(in, current);
        }
        case 13:
        {
            return _iceD_stopDebug(in, current);
        }
        case 14:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGMPLocalProcess::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGMPLocalProcess, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGMPLocalProcess::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGMPLocalProcess, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGMPLocalProcessPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGMPLocalProcessPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGMPLocalProcess::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

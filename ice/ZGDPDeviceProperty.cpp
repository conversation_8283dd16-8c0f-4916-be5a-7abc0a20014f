//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGDPDeviceProperty.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGDPDeviceProperty.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGDPDeviceProperty",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getDynamicProperties",
    "getFieldsProperties",
    "getProperty",
    "getPropertyValue",
    "getPropertyValues",
    "getRuntimePropertyValue",
    "getRuntimePropertyValues",
    "getStaticProperties",
    "getVersion",
    "getWholeProperties",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "mgetProperty",
    "mgetPropertyValue",
    "mgetPropertyValues",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test",
    "updatePropertyValue",
    "updatePropertyValues"
};
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getFieldsProperties_name = "getFieldsProperties";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getStaticProperties_name = "getStaticProperties";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getDynamicProperties_name = "getDynamicProperties";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getWholeProperties_name = "getWholeProperties";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_mgetProperty_name = "mgetProperty";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getProperty_name = "getProperty";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValues_name = "mgetPropertyValues";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValue_name = "mgetPropertyValue";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getPropertyValue_name = "getPropertyValue";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getPropertyValues_name = "getPropertyValues";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValue_name = "getRuntimePropertyValue";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValues_name = "getRuntimePropertyValues";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValue_name = "updatePropertyValue";
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValues_name = "updatePropertyValues";

}

bool
ZG6000::ZGDPDeviceProperty::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGDPDeviceProperty_ids, iceC_ZG6000_ZGDPDeviceProperty_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGDPDeviceProperty::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGDPDeviceProperty_ids[0], &iceC_ZG6000_ZGDPDeviceProperty_ids[3]);
}

::std::string
ZG6000::ZGDPDeviceProperty::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGDPDeviceProperty::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGDPDeviceProperty";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getFieldsProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getFieldsProperties(::std::move(iceP_deviceID), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getStaticProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getStaticProperties(::std::move(iceP_deviceID), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getDynamicProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getDynamicProperties(::std::move(iceP_deviceID), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getWholeProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->readAll(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getWholeProperties(::std::move(iceP_deviceID), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_mgetProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    ::std::string iceP_propertyName;
    istr->readAll(iceP_listDeviceID, iceP_propertyName);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->mgetProperty(::std::move(iceP_listDeviceID), ::std::move(iceP_propertyName), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    istr->readAll(iceP_deviceID, iceP_propertyName);
    inS.endReadParams();
    StringMap iceP_property;
    ErrorInfo iceP_e;
    bool ret = this->getProperty(::std::move(iceP_deviceID), ::std::move(iceP_propertyName), iceP_property, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_property, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_mgetPropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    StringList iceP_listName;
    istr->readAll(iceP_listDeviceID, iceP_listName);
    inS.endReadParams();
    MapStringMap iceP_propertyValues;
    ErrorInfo iceP_e;
    bool ret = this->mgetPropertyValues(::std::move(iceP_listDeviceID), ::std::move(iceP_listName), iceP_propertyValues, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_propertyValues, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_mgetPropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    ::std::string iceP_propertyName;
    istr->readAll(iceP_listDeviceID, iceP_propertyName);
    inS.endReadParams();
    StringMap iceP_propertyValues;
    ErrorInfo iceP_e;
    bool ret = this->mgetPropertyValue(::std::move(iceP_listDeviceID), ::std::move(iceP_propertyName), iceP_propertyValues, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_propertyValues, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getPropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    istr->readAll(iceP_deviceID, iceP_propertyName);
    inS.endReadParams();
    ::std::string iceP_propertyValue;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValue(::std::move(iceP_deviceID), ::std::move(iceP_propertyName), iceP_propertyValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_propertyValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getPropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringList iceP_listName;
    istr->readAll(iceP_deviceID, iceP_listName);
    inS.endReadParams();
    StringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValues(::std::move(iceP_deviceID), ::std::move(iceP_listName), iceP_values, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_values, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getRuntimePropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    istr->readAll(iceP_deviceID, iceP_propertyName);
    inS.endReadParams();
    ::std::string iceP_propertyValue;
    ErrorInfo iceP_e;
    bool ret = this->getRuntimePropertyValue(::std::move(iceP_deviceID), ::std::move(iceP_propertyName), iceP_propertyValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_propertyValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getRuntimePropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringList iceP_listName;
    istr->readAll(iceP_deviceID, iceP_listName);
    inS.endReadParams();
    StringMap iceP_propertyValues;
    ErrorInfo iceP_e;
    bool ret = this->getRuntimePropertyValues(::std::move(iceP_deviceID), ::std::move(iceP_listName), iceP_propertyValues, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_propertyValues, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_updatePropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    ::std::string iceP_propertyValue;
    istr->readAll(iceP_deviceID, iceP_propertyName, iceP_propertyValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValue(::std::move(iceP_deviceID), ::std::move(iceP_propertyName), ::std::move(iceP_propertyValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_updatePropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_values;
    istr->readAll(iceP_deviceID, iceP_values);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValues(::std::move(iceP_deviceID), ::std::move(iceP_values), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGDPDeviceProperty_ops, iceC_ZG6000_ZGDPDeviceProperty_ops + 29, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGDPDeviceProperty_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getDynamicProperties(in, current);
        }
        case 4:
        {
            return _iceD_getFieldsProperties(in, current);
        }
        case 5:
        {
            return _iceD_getProperty(in, current);
        }
        case 6:
        {
            return _iceD_getPropertyValue(in, current);
        }
        case 7:
        {
            return _iceD_getPropertyValues(in, current);
        }
        case 8:
        {
            return _iceD_getRuntimePropertyValue(in, current);
        }
        case 9:
        {
            return _iceD_getRuntimePropertyValues(in, current);
        }
        case 10:
        {
            return _iceD_getStaticProperties(in, current);
        }
        case 11:
        {
            return _iceD_getVersion(in, current);
        }
        case 12:
        {
            return _iceD_getWholeProperties(in, current);
        }
        case 13:
        {
            return _iceD_heartDebug(in, current);
        }
        case 14:
        {
            return _iceD_ice_id(in, current);
        }
        case 15:
        {
            return _iceD_ice_ids(in, current);
        }
        case 16:
        {
            return _iceD_ice_isA(in, current);
        }
        case 17:
        {
            return _iceD_ice_ping(in, current);
        }
        case 18:
        {
            return _iceD_isDebugging(in, current);
        }
        case 19:
        {
            return _iceD_mgetProperty(in, current);
        }
        case 20:
        {
            return _iceD_mgetPropertyValue(in, current);
        }
        case 21:
        {
            return _iceD_mgetPropertyValues(in, current);
        }
        case 22:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 23:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 24:
        {
            return _iceD_startDebug(in, current);
        }
        case 25:
        {
            return _iceD_stopDebug(in, current);
        }
        case 26:
        {
            return _iceD_test(in, current);
        }
        case 27:
        {
            return _iceD_updatePropertyValue(in, current);
        }
        case 28:
        {
            return _iceD_updatePropertyValues(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_getFieldsProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetFieldsPropertiesResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getFieldsProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_getFieldsProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::GetFieldsPropertiesResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_getStaticProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetStaticPropertiesResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getStaticProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_getStaticProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::GetStaticPropertiesResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_getDynamicProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetDynamicPropertiesResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getDynamicProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_getDynamicProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::GetDynamicPropertiesResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_getWholeProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetWholePropertiesResult>>& outAsync, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getWholeProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_getWholeProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::GetWholePropertiesResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_mgetProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::MgetPropertyResult>>& outAsync, const StringList& iceP_listDeviceID, const ::std::string& iceP_propertyName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_mgetProperty_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_mgetProperty_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDeviceID, iceP_propertyName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::MgetPropertyResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_getProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetPropertyResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getProperty_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_getProperty_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_propertyName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::GetPropertyResult v;
            istr->readAll(v.property, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_mgetPropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::MgetPropertyValuesResult>>& outAsync, const StringList& iceP_listDeviceID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValues_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValues_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDeviceID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::MgetPropertyValuesResult v;
            istr->readAll(v.propertyValues, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_mgetPropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::MgetPropertyValueResult>>& outAsync, const StringList& iceP_listDeviceID, const ::std::string& iceP_propertyName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValue_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDeviceID, iceP_propertyName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::MgetPropertyValueResult v;
            istr->readAll(v.propertyValues, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_getPropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetPropertyValueResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getPropertyValue_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_getPropertyValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_propertyName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::GetPropertyValueResult v;
            istr->readAll(v.propertyValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_getPropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetPropertyValuesResult>>& outAsync, const ::std::string& iceP_deviceID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getPropertyValues_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_getPropertyValues_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::GetPropertyValuesResult v;
            istr->readAll(v.values, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_getRuntimePropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetRuntimePropertyValueResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValue_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_propertyName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::GetRuntimePropertyValueResult v;
            istr->readAll(v.propertyValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_getRuntimePropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::GetRuntimePropertyValuesResult>>& outAsync, const ::std::string& iceP_deviceID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValues_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValues_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::GetRuntimePropertyValuesResult v;
            istr->readAll(v.propertyValues, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_updatePropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::UpdatePropertyValueResult>>& outAsync, const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::std::string& iceP_propertyValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValue_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_propertyName, iceP_propertyValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::UpdatePropertyValueResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGDPDevicePropertyPrx::_iceI_updatePropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceProperty::UpdatePropertyValuesResult>>& outAsync, const ::std::string& iceP_deviceID, const StringMap& iceP_values, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValues_name);
    outAsync->invoke(iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValues_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_values);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGDPDeviceProperty::UpdatePropertyValuesResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGDPDevicePropertyPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGDPDevicePropertyPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGDPDevicePropertyPrx::ice_staticId()
{
    return ZGDPDeviceProperty::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getFieldsProperties_name = "getFieldsProperties";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getStaticProperties_name = "getStaticProperties";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getDynamicProperties_name = "getDynamicProperties";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getWholeProperties_name = "getWholeProperties";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_mgetProperty_name = "mgetProperty";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getProperty_name = "getProperty";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValues_name = "mgetPropertyValues";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValue_name = "mgetPropertyValue";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getPropertyValue_name = "getPropertyValue";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getPropertyValues_name = "getPropertyValues";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValue_name = "getRuntimePropertyValue";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValues_name = "getRuntimePropertyValues";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValue_name = "updatePropertyValue";

const ::std::string iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValues_name = "updatePropertyValues";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGDPDeviceProperty* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGDPDeviceProperty>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGDPDeviceProperty;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_getFieldsProperties(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getFieldsProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_getFieldsProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_getFieldsProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_getFieldsProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_getFieldsProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getFieldsProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_getFieldsProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getFieldsProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_getStaticProperties(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getStaticProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_getStaticProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_getStaticProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_getStaticProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_getStaticProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getStaticProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_getStaticProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getStaticProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_getDynamicProperties(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getDynamicProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_getDynamicProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_getDynamicProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_getDynamicProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_getDynamicProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getDynamicProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_getDynamicProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getDynamicProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_getWholeProperties(const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getWholeProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_getWholeProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_getWholeProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_getWholeProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_getWholeProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getWholeProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_getWholeProperties(::ZG6000::ListStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getWholeProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_mgetProperty(const ::ZG6000::StringList& iceP_listDeviceID, const ::std::string& iceP_propertyName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_mgetProperty_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_mgetProperty_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_mgetProperty_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDeviceID);
        ostr->write(iceP_propertyName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_mgetProperty_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_mgetProperty(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_mgetProperty_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_mgetProperty(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_mgetProperty_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_getProperty(const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getProperty_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_getProperty_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_getProperty_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_propertyName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_getProperty_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_getProperty(::ZG6000::StringMap& iceP_property, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getProperty_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_property);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_getProperty(::ZG6000::StringMap& iceP_property, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getProperty_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_property);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_mgetPropertyValues(const ::ZG6000::StringList& iceP_listDeviceID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValues_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValues_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValues_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDeviceID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValues_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_mgetPropertyValues(::ZG6000::MapStringMap& iceP_propertyValues, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValues_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_propertyValues);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_mgetPropertyValues(::ZG6000::MapStringMap& iceP_propertyValues, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValues_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_propertyValues);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_mgetPropertyValue(const ::ZG6000::StringList& iceP_listDeviceID, const ::std::string& iceP_propertyName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDeviceID);
        ostr->write(iceP_propertyName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_mgetPropertyValue(::ZG6000::StringMap& iceP_propertyValues, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_propertyValues);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_mgetPropertyValue(::ZG6000::StringMap& iceP_propertyValues, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_mgetPropertyValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_propertyValues);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_getPropertyValue(const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getPropertyValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_getPropertyValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_getPropertyValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_propertyName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_getPropertyValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_getPropertyValue(::std::string& iceP_propertyValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getPropertyValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_propertyValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_getPropertyValue(::std::string& iceP_propertyValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getPropertyValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_propertyValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_getPropertyValues(const ::std::string& iceP_deviceID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getPropertyValues_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_getPropertyValues_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_getPropertyValues_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_getPropertyValues_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_getPropertyValues(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getPropertyValues_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_getPropertyValues(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getPropertyValues_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_getRuntimePropertyValue(const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_propertyName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_getRuntimePropertyValue(::std::string& iceP_propertyValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_propertyValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_getRuntimePropertyValue(::std::string& iceP_propertyValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_propertyValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_getRuntimePropertyValues(const ::std::string& iceP_deviceID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValues_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValues_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValues_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValues_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_getRuntimePropertyValues(::ZG6000::StringMap& iceP_propertyValues, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValues_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_propertyValues);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_getRuntimePropertyValues(::ZG6000::StringMap& iceP_propertyValues, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_getRuntimePropertyValues_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_propertyValues);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_updatePropertyValue(const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::std::string& iceP_propertyValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_propertyName);
        ostr->write(iceP_propertyValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_updatePropertyValue(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_updatePropertyValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_begin_updatePropertyValues(const ::std::string& iceP_deviceID, const ::ZG6000::StringMap& iceP_values, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValues_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValues_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValues_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_values);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValues_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGDPDeviceProperty::end_updatePropertyValues(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValues_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGDPDeviceProperty::_iceI_end_updatePropertyValues(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGDPDeviceProperty_updatePropertyValues_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGDPDeviceProperty::_newInstance() const
{
    return new ZGDPDeviceProperty;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGDPDeviceProperty::ice_staticId()
{
    return ::ZG6000::ZGDPDeviceProperty::ice_staticId();
}

ZG6000::ZGDPDeviceProperty::~ZGDPDeviceProperty()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGDPDeviceProperty* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGDPDeviceProperty",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGDPDeviceProperty::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGDPDeviceProperty_ids, iceC_ZG6000_ZGDPDeviceProperty_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGDPDeviceProperty::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGDPDeviceProperty_ids[0], &iceC_ZG6000_ZGDPDeviceProperty_ids[3]);
}

const ::std::string&
ZG6000::ZGDPDeviceProperty::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGDPDeviceProperty::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGDPDeviceProperty";
    return typeId;
#else
    return iceC_ZG6000_ZGDPDeviceProperty_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getFieldsProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getFieldsProperties(iceP_deviceID, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getStaticProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getStaticProperties(iceP_deviceID, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getDynamicProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getDynamicProperties(iceP_deviceID, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getWholeProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getWholeProperties(iceP_deviceID, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_mgetProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    ::std::string iceP_propertyName;
    istr->read(iceP_listDeviceID);
    istr->read(iceP_propertyName);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->mgetProperty(iceP_listDeviceID, iceP_propertyName, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    istr->read(iceP_deviceID);
    istr->read(iceP_propertyName);
    inS.endReadParams();
    StringMap iceP_property;
    ErrorInfo iceP_e;
    bool ret = this->getProperty(iceP_deviceID, iceP_propertyName, iceP_property, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_property);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_mgetPropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    StringList iceP_listName;
    istr->read(iceP_listDeviceID);
    istr->read(iceP_listName);
    inS.endReadParams();
    MapStringMap iceP_propertyValues;
    ErrorInfo iceP_e;
    bool ret = this->mgetPropertyValues(iceP_listDeviceID, iceP_listName, iceP_propertyValues, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_propertyValues);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_mgetPropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDeviceID;
    ::std::string iceP_propertyName;
    istr->read(iceP_listDeviceID);
    istr->read(iceP_propertyName);
    inS.endReadParams();
    StringMap iceP_propertyValues;
    ErrorInfo iceP_e;
    bool ret = this->mgetPropertyValue(iceP_listDeviceID, iceP_propertyName, iceP_propertyValues, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_propertyValues);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getPropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    istr->read(iceP_deviceID);
    istr->read(iceP_propertyName);
    inS.endReadParams();
    ::std::string iceP_propertyValue;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValue(iceP_deviceID, iceP_propertyName, iceP_propertyValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_propertyValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getPropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringList iceP_listName;
    istr->read(iceP_deviceID);
    istr->read(iceP_listName);
    inS.endReadParams();
    StringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValues(iceP_deviceID, iceP_listName, iceP_values, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_values);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getRuntimePropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    istr->read(iceP_deviceID);
    istr->read(iceP_propertyName);
    inS.endReadParams();
    ::std::string iceP_propertyValue;
    ErrorInfo iceP_e;
    bool ret = this->getRuntimePropertyValue(iceP_deviceID, iceP_propertyName, iceP_propertyValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_propertyValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_getRuntimePropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringList iceP_listName;
    istr->read(iceP_deviceID);
    istr->read(iceP_listName);
    inS.endReadParams();
    StringMap iceP_propertyValues;
    ErrorInfo iceP_e;
    bool ret = this->getRuntimePropertyValues(iceP_deviceID, iceP_listName, iceP_propertyValues, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_propertyValues);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_updatePropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    ::std::string iceP_propertyValue;
    istr->read(iceP_deviceID);
    istr->read(iceP_propertyName);
    istr->read(iceP_propertyValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValue(iceP_deviceID, iceP_propertyName, iceP_propertyValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceD_updatePropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_values;
    istr->read(iceP_deviceID);
    istr->read(iceP_values);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValues(iceP_deviceID, iceP_values, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGDPDeviceProperty_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getDynamicProperties",
    "getFieldsProperties",
    "getProperty",
    "getPropertyValue",
    "getPropertyValues",
    "getRuntimePropertyValue",
    "getRuntimePropertyValues",
    "getStaticProperties",
    "getVersion",
    "getWholeProperties",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "mgetProperty",
    "mgetPropertyValue",
    "mgetPropertyValues",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test",
    "updatePropertyValue",
    "updatePropertyValues"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGDPDeviceProperty::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGDPDeviceProperty_all, iceC_ZG6000_ZGDPDeviceProperty_all + 29, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGDPDeviceProperty_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getDynamicProperties(in, current);
        }
        case 4:
        {
            return _iceD_getFieldsProperties(in, current);
        }
        case 5:
        {
            return _iceD_getProperty(in, current);
        }
        case 6:
        {
            return _iceD_getPropertyValue(in, current);
        }
        case 7:
        {
            return _iceD_getPropertyValues(in, current);
        }
        case 8:
        {
            return _iceD_getRuntimePropertyValue(in, current);
        }
        case 9:
        {
            return _iceD_getRuntimePropertyValues(in, current);
        }
        case 10:
        {
            return _iceD_getStaticProperties(in, current);
        }
        case 11:
        {
            return _iceD_getVersion(in, current);
        }
        case 12:
        {
            return _iceD_getWholeProperties(in, current);
        }
        case 13:
        {
            return _iceD_heartDebug(in, current);
        }
        case 14:
        {
            return _iceD_ice_id(in, current);
        }
        case 15:
        {
            return _iceD_ice_ids(in, current);
        }
        case 16:
        {
            return _iceD_ice_isA(in, current);
        }
        case 17:
        {
            return _iceD_ice_ping(in, current);
        }
        case 18:
        {
            return _iceD_isDebugging(in, current);
        }
        case 19:
        {
            return _iceD_mgetProperty(in, current);
        }
        case 20:
        {
            return _iceD_mgetPropertyValue(in, current);
        }
        case 21:
        {
            return _iceD_mgetPropertyValues(in, current);
        }
        case 22:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 23:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 24:
        {
            return _iceD_startDebug(in, current);
        }
        case 25:
        {
            return _iceD_stopDebug(in, current);
        }
        case 26:
        {
            return _iceD_test(in, current);
        }
        case 27:
        {
            return _iceD_updatePropertyValue(in, current);
        }
        case 28:
        {
            return _iceD_updatePropertyValues(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGDPDeviceProperty::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGDPDeviceProperty, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGDPDeviceProperty::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGDPDeviceProperty, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGDPDevicePropertyPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGDPDevicePropertyPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGDPDeviceProperty::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

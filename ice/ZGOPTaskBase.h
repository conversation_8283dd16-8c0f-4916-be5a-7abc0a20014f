//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPTaskBase.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGOPTaskBase_h__
#define __ZGOPTaskBase_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGOPTaskBase;
class ZGOPTaskBasePrx;

}

namespace ZG6000
{

class ZGOPTaskBase : public virtual ZGServerBase
{
public:

    using ProxyType = ZGOPTaskBasePrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to deleteTask.
     */
    struct DeleteTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	删除任务
     */
    virtual bool deleteTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getTaskList.
     */
    struct GetTaskListResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ListStringMap listTask;
        ErrorInfo e;
    };

    /**
     * @param param 查询参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取任务列表
     */
    virtual bool getTaskList(StringMap param, ListStringMap& listTask, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to startTask.
     */
    struct StartTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	启动任务
     */
    virtual bool startTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_startTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to pauseTask.
     */
    struct PauseTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	暂停任务
     */
    virtual bool pauseTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_pauseTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to resumeTask.
     */
    struct ResumeTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	继续任务
     */
    virtual bool resumeTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_resumeTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to retryTask.
     */
    struct RetryTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	重试任务
     */
    virtual bool retryTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_retryTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to abolishTask.
     */
    struct AbolishTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	作废任务
     */
    virtual bool abolishTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_abolishTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to confirmTask.
     */
    struct ConfirmTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	确认任务
     */
    virtual bool confirmTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGOPTaskBasePrx : public virtual ::Ice::Proxy<ZGOPTaskBasePrx, ZGServerBasePrx>
{
public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	删除任务
     */
    bool deleteTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskBase::DeleteTaskResult>(true, this, &ZGOPTaskBasePrx::_iceI_deleteTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	删除任务
     */
    template<template<typename> class P = ::std::promise>
    auto deleteTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskBase::DeleteTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskBase::DeleteTaskResult, P>(false, this, &ZGOPTaskBasePrx::_iceI_deleteTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	删除任务
     */
    ::std::function<void()>
    deleteTaskAsync(const ::std::string& taskID, const StringMap& param,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskBase::DeleteTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskBase::DeleteTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskBasePrx::_iceI_deleteTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_deleteTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::DeleteTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取任务列表
     */
    bool getTaskList(const StringMap& param, ListStringMap& listTask, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskBase::GetTaskListResult>(true, this, &ZGOPTaskBasePrx::_iceI_getTaskList, param, context).get();
        listTask = ::std::move(_result.listTask);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	获取任务列表
     */
    template<template<typename> class P = ::std::promise>
    auto getTaskListAsync(const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskBase::GetTaskListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskBase::GetTaskListResult, P>(false, this, &ZGOPTaskBasePrx::_iceI_getTaskList, param, context);
    }

    /**
     * @param param 查询参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	获取任务列表
     */
    ::std::function<void()>
    getTaskListAsync(const StringMap& param,
                     ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskBase::GetTaskListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listTask), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskBase::GetTaskListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskBasePrx::_iceI_getTaskList, param, context);
    }

    /// \cond INTERNAL
    void _iceI_getTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::GetTaskListResult>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	启动任务
     */
    bool startTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskBase::StartTaskResult>(true, this, &ZGOPTaskBasePrx::_iceI_startTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	启动任务
     */
    template<template<typename> class P = ::std::promise>
    auto startTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskBase::StartTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskBase::StartTaskResult, P>(false, this, &ZGOPTaskBasePrx::_iceI_startTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	启动任务
     */
    ::std::function<void()>
    startTaskAsync(const ::std::string& taskID, const StringMap& param,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskBase::StartTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskBase::StartTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskBasePrx::_iceI_startTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_startTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::StartTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	暂停任务
     */
    bool pauseTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskBase::PauseTaskResult>(true, this, &ZGOPTaskBasePrx::_iceI_pauseTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	暂停任务
     */
    template<template<typename> class P = ::std::promise>
    auto pauseTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskBase::PauseTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskBase::PauseTaskResult, P>(false, this, &ZGOPTaskBasePrx::_iceI_pauseTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	暂停任务
     */
    ::std::function<void()>
    pauseTaskAsync(const ::std::string& taskID, const StringMap& param,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskBase::PauseTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskBase::PauseTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskBasePrx::_iceI_pauseTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_pauseTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::PauseTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	继续任务
     */
    bool resumeTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskBase::ResumeTaskResult>(true, this, &ZGOPTaskBasePrx::_iceI_resumeTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	继续任务
     */
    template<template<typename> class P = ::std::promise>
    auto resumeTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskBase::ResumeTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskBase::ResumeTaskResult, P>(false, this, &ZGOPTaskBasePrx::_iceI_resumeTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	继续任务
     */
    ::std::function<void()>
    resumeTaskAsync(const ::std::string& taskID, const StringMap& param,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskBase::ResumeTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskBase::ResumeTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskBasePrx::_iceI_resumeTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_resumeTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::ResumeTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	重试任务
     */
    bool retryTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskBase::RetryTaskResult>(true, this, &ZGOPTaskBasePrx::_iceI_retryTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	重试任务
     */
    template<template<typename> class P = ::std::promise>
    auto retryTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskBase::RetryTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskBase::RetryTaskResult, P>(false, this, &ZGOPTaskBasePrx::_iceI_retryTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	重试任务
     */
    ::std::function<void()>
    retryTaskAsync(const ::std::string& taskID, const StringMap& param,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskBase::RetryTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskBase::RetryTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskBasePrx::_iceI_retryTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_retryTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::RetryTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	作废任务
     */
    bool abolishTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskBase::AbolishTaskResult>(true, this, &ZGOPTaskBasePrx::_iceI_abolishTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	作废任务
     */
    template<template<typename> class P = ::std::promise>
    auto abolishTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskBase::AbolishTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskBase::AbolishTaskResult, P>(false, this, &ZGOPTaskBasePrx::_iceI_abolishTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	作废任务
     */
    ::std::function<void()>
    abolishTaskAsync(const ::std::string& taskID, const StringMap& param,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskBase::AbolishTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskBase::AbolishTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskBasePrx::_iceI_abolishTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_abolishTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::AbolishTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	确认任务
     */
    bool confirmTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskBase::ConfirmTaskResult>(true, this, &ZGOPTaskBasePrx::_iceI_confirmTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	确认任务
     */
    template<template<typename> class P = ::std::promise>
    auto confirmTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskBase::ConfirmTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskBase::ConfirmTaskResult, P>(false, this, &ZGOPTaskBasePrx::_iceI_confirmTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	确认任务
     */
    ::std::function<void()>
    confirmTaskAsync(const ::std::string& taskID, const StringMap& param,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskBase::ConfirmTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskBase::ConfirmTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskBasePrx::_iceI_confirmTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_confirmTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskBase::ConfirmTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGOPTaskBasePrx() = default;
    friend ::std::shared_ptr<ZGOPTaskBasePrx> IceInternal::createProxy<ZGOPTaskBasePrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGOPTaskBasePtr = ::std::shared_ptr<ZGOPTaskBase>;
using ZGOPTaskBasePrxPtr = ::std::shared_ptr<ZGOPTaskBasePrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGOPTaskBase;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGOPTaskBase>&);
::IceProxy::Ice::Object* upCast(ZGOPTaskBase*);
/// \endcond

}

}

namespace ZG6000
{

class ZGOPTaskBase;
/// \cond INTERNAL
::Ice::Object* upCast(ZGOPTaskBase*);
/// \endcond
typedef ::IceInternal::Handle< ZGOPTaskBase> ZGOPTaskBasePtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGOPTaskBase> ZGOPTaskBasePrx;
typedef ZGOPTaskBasePrx ZGOPTaskBasePrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGOPTaskBasePtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_deleteTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_deleteTask.
 */
class Callback_ZGOPTaskBase_deleteTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskBase_deleteTask_Base> Callback_ZGOPTaskBase_deleteTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_getTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_getTaskList.
 */
class Callback_ZGOPTaskBase_getTaskList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskBase_getTaskList_Base> Callback_ZGOPTaskBase_getTaskListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_startTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_startTask.
 */
class Callback_ZGOPTaskBase_startTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskBase_startTask_Base> Callback_ZGOPTaskBase_startTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_pauseTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_pauseTask.
 */
class Callback_ZGOPTaskBase_pauseTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskBase_pauseTask_Base> Callback_ZGOPTaskBase_pauseTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_resumeTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_resumeTask.
 */
class Callback_ZGOPTaskBase_resumeTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskBase_resumeTask_Base> Callback_ZGOPTaskBase_resumeTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_retryTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_retryTask.
 */
class Callback_ZGOPTaskBase_retryTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskBase_retryTask_Base> Callback_ZGOPTaskBase_retryTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_abolishTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_abolishTask.
 */
class Callback_ZGOPTaskBase_abolishTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskBase_abolishTask_Base> Callback_ZGOPTaskBase_abolishTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_confirmTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_confirmTask.
 */
class Callback_ZGOPTaskBase_confirmTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskBase_confirmTask_Base> Callback_ZGOPTaskBase_confirmTaskPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGOPTaskBase : public virtual ::Ice::Proxy<ZGOPTaskBase, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	删除任务
     */
    bool deleteTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deleteTask(e, _iceI_begin_deleteTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	删除任务
     */
    ::Ice::AsyncResultPtr begin_deleteTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deleteTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	删除任务
     */
    ::Ice::AsyncResultPtr begin_deleteTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	删除任务
     */
    ::Ice::AsyncResultPtr begin_deleteTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	删除任务
     */
    ::Ice::AsyncResultPtr begin_deleteTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskBase_deleteTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	删除任务
     */
    ::Ice::AsyncResultPtr begin_deleteTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskBase_deleteTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_deleteTask.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_deleteTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deleteTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deleteTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取任务列表
     */
    bool getTaskList(const ::ZG6000::StringMap& param, ::ZG6000::ListStringMap& listTask, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTaskList(listTask, e, _iceI_begin_getTaskList(param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取任务列表
     */
    ::Ice::AsyncResultPtr begin_getTaskList(const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTaskList(param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param param 查询参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取任务列表
     */
    ::Ice::AsyncResultPtr begin_getTaskList(const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取任务列表
     */
    ::Ice::AsyncResultPtr begin_getTaskList(const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(param, context, cb, cookie);
    }

    /**
     * @param param 查询参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取任务列表
     */
    ::Ice::AsyncResultPtr begin_getTaskList(const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskBase_getTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param param 查询参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取任务列表
     */
    ::Ice::AsyncResultPtr begin_getTaskList(const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskBase_getTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getTaskList.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getTaskList(::ZG6000::ListStringMap& listTask, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTaskList(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTaskList(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	启动任务
     */
    bool startTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_startTask(e, _iceI_begin_startTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	启动任务
     */
    ::Ice::AsyncResultPtr begin_startTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_startTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	启动任务
     */
    ::Ice::AsyncResultPtr begin_startTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	启动任务
     */
    ::Ice::AsyncResultPtr begin_startTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	启动任务
     */
    ::Ice::AsyncResultPtr begin_startTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskBase_startTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	启动任务
     */
    ::Ice::AsyncResultPtr begin_startTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskBase_startTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_startTask.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_startTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_startTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_startTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	暂停任务
     */
    bool pauseTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_pauseTask(e, _iceI_begin_pauseTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	暂停任务
     */
    ::Ice::AsyncResultPtr begin_pauseTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_pauseTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	暂停任务
     */
    ::Ice::AsyncResultPtr begin_pauseTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	暂停任务
     */
    ::Ice::AsyncResultPtr begin_pauseTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	暂停任务
     */
    ::Ice::AsyncResultPtr begin_pauseTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskBase_pauseTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	暂停任务
     */
    ::Ice::AsyncResultPtr begin_pauseTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskBase_pauseTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_pauseTask.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_pauseTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_pauseTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_pauseTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	继续任务
     */
    bool resumeTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_resumeTask(e, _iceI_begin_resumeTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	继续任务
     */
    ::Ice::AsyncResultPtr begin_resumeTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_resumeTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	继续任务
     */
    ::Ice::AsyncResultPtr begin_resumeTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	继续任务
     */
    ::Ice::AsyncResultPtr begin_resumeTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	继续任务
     */
    ::Ice::AsyncResultPtr begin_resumeTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskBase_resumeTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	继续任务
     */
    ::Ice::AsyncResultPtr begin_resumeTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskBase_resumeTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_resumeTask.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_resumeTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_resumeTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_resumeTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	重试任务
     */
    bool retryTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_retryTask(e, _iceI_begin_retryTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	重试任务
     */
    ::Ice::AsyncResultPtr begin_retryTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_retryTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	重试任务
     */
    ::Ice::AsyncResultPtr begin_retryTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_retryTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	重试任务
     */
    ::Ice::AsyncResultPtr begin_retryTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_retryTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	重试任务
     */
    ::Ice::AsyncResultPtr begin_retryTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskBase_retryTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_retryTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	重试任务
     */
    ::Ice::AsyncResultPtr begin_retryTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskBase_retryTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_retryTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_retryTask.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_retryTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_retryTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_retryTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	作废任务
     */
    bool abolishTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_abolishTask(e, _iceI_begin_abolishTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	作废任务
     */
    ::Ice::AsyncResultPtr begin_abolishTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_abolishTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	作废任务
     */
    ::Ice::AsyncResultPtr begin_abolishTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_abolishTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	作废任务
     */
    ::Ice::AsyncResultPtr begin_abolishTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_abolishTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	作废任务
     */
    ::Ice::AsyncResultPtr begin_abolishTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskBase_abolishTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_abolishTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	作废任务
     */
    ::Ice::AsyncResultPtr begin_abolishTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskBase_abolishTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_abolishTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_abolishTask.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_abolishTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_abolishTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_abolishTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	确认任务
     */
    bool confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_confirmTask(e, _iceI_begin_confirmTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	确认任务
     */
    ::Ice::AsyncResultPtr begin_confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_confirmTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	确认任务
     */
    ::Ice::AsyncResultPtr begin_confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	确认任务
     */
    ::Ice::AsyncResultPtr begin_confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	确认任务
     */
    ::Ice::AsyncResultPtr begin_confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskBase_confirmTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	确认任务
     */
    ::Ice::AsyncResultPtr begin_confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskBase_confirmTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_confirmTask.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_confirmTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_confirmTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_confirmTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGOPTaskBase : virtual public ZGServerBase
{
public:

    typedef ZGOPTaskBasePrx ProxyType;
    typedef ZGOPTaskBasePtr PointerType;

    virtual ~ZGOPTaskBase();

#ifdef ICE_CPP11_COMPILER
    ZGOPTaskBase() = default;
    ZGOPTaskBase(const ZGOPTaskBase&) = default;
    ZGOPTaskBase& operator=(const ZGOPTaskBase&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	删除任务
     */
    virtual bool deleteTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param param 查询参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取任务列表
     */
    virtual bool getTaskList(const StringMap& param, ListStringMap& listTask, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	启动任务
     */
    virtual bool startTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_startTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	暂停任务
     */
    virtual bool pauseTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_pauseTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	继续任务
     */
    virtual bool resumeTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_resumeTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	重试任务
     */
    virtual bool retryTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_retryTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	作废任务
     */
    virtual bool abolishTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_abolishTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	确认任务
     */
    virtual bool confirmTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGOPTaskBase& lhs, const ZGOPTaskBase& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGOPTaskBase& lhs, const ZGOPTaskBase& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_deleteTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_deleteTask.
 */
template<class T>
class CallbackNC_ZGOPTaskBase_deleteTask : public Callback_ZGOPTaskBase_deleteTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskBase_deleteTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_deleteTask.
 */
template<class T> Callback_ZGOPTaskBase_deleteTaskPtr
newCallback_ZGOPTaskBase_deleteTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_deleteTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_deleteTask.
 */
template<class T> Callback_ZGOPTaskBase_deleteTaskPtr
newCallback_ZGOPTaskBase_deleteTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_deleteTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_deleteTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_deleteTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskBase_deleteTask : public Callback_ZGOPTaskBase_deleteTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskBase_deleteTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_deleteTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_deleteTaskPtr
newCallback_ZGOPTaskBase_deleteTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_deleteTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_deleteTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_deleteTaskPtr
newCallback_ZGOPTaskBase_deleteTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_deleteTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_getTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_getTaskList.
 */
template<class T>
class CallbackNC_ZGOPTaskBase_getTaskList : public Callback_ZGOPTaskBase_getTaskList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskBase_getTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listTask;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskList(iceP_listTask, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listTask, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_getTaskList.
 */
template<class T> Callback_ZGOPTaskBase_getTaskListPtr
newCallback_ZGOPTaskBase_getTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_getTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_getTaskList.
 */
template<class T> Callback_ZGOPTaskBase_getTaskListPtr
newCallback_ZGOPTaskBase_getTaskList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_getTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_getTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_getTaskList.
 */
template<class T, typename CT>
class Callback_ZGOPTaskBase_getTaskList : public Callback_ZGOPTaskBase_getTaskList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskBase_getTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listTask;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskList(iceP_listTask, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listTask, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_getTaskList.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_getTaskListPtr
newCallback_ZGOPTaskBase_getTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_getTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_getTaskList.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_getTaskListPtr
newCallback_ZGOPTaskBase_getTaskList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_getTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_startTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_startTask.
 */
template<class T>
class CallbackNC_ZGOPTaskBase_startTask : public Callback_ZGOPTaskBase_startTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskBase_startTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_startTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_startTask.
 */
template<class T> Callback_ZGOPTaskBase_startTaskPtr
newCallback_ZGOPTaskBase_startTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_startTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_startTask.
 */
template<class T> Callback_ZGOPTaskBase_startTaskPtr
newCallback_ZGOPTaskBase_startTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_startTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_startTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_startTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskBase_startTask : public Callback_ZGOPTaskBase_startTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskBase_startTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_startTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_startTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_startTaskPtr
newCallback_ZGOPTaskBase_startTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_startTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_startTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_startTaskPtr
newCallback_ZGOPTaskBase_startTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_startTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_pauseTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_pauseTask.
 */
template<class T>
class CallbackNC_ZGOPTaskBase_pauseTask : public Callback_ZGOPTaskBase_pauseTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskBase_pauseTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_pauseTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_pauseTask.
 */
template<class T> Callback_ZGOPTaskBase_pauseTaskPtr
newCallback_ZGOPTaskBase_pauseTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_pauseTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_pauseTask.
 */
template<class T> Callback_ZGOPTaskBase_pauseTaskPtr
newCallback_ZGOPTaskBase_pauseTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_pauseTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_pauseTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_pauseTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskBase_pauseTask : public Callback_ZGOPTaskBase_pauseTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskBase_pauseTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_pauseTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_pauseTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_pauseTaskPtr
newCallback_ZGOPTaskBase_pauseTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_pauseTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_pauseTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_pauseTaskPtr
newCallback_ZGOPTaskBase_pauseTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_pauseTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_resumeTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_resumeTask.
 */
template<class T>
class CallbackNC_ZGOPTaskBase_resumeTask : public Callback_ZGOPTaskBase_resumeTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskBase_resumeTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_resumeTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_resumeTask.
 */
template<class T> Callback_ZGOPTaskBase_resumeTaskPtr
newCallback_ZGOPTaskBase_resumeTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_resumeTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_resumeTask.
 */
template<class T> Callback_ZGOPTaskBase_resumeTaskPtr
newCallback_ZGOPTaskBase_resumeTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_resumeTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_resumeTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_resumeTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskBase_resumeTask : public Callback_ZGOPTaskBase_resumeTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskBase_resumeTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_resumeTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_resumeTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_resumeTaskPtr
newCallback_ZGOPTaskBase_resumeTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_resumeTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_resumeTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_resumeTaskPtr
newCallback_ZGOPTaskBase_resumeTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_resumeTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_retryTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_retryTask.
 */
template<class T>
class CallbackNC_ZGOPTaskBase_retryTask : public Callback_ZGOPTaskBase_retryTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskBase_retryTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_retryTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_retryTask.
 */
template<class T> Callback_ZGOPTaskBase_retryTaskPtr
newCallback_ZGOPTaskBase_retryTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_retryTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_retryTask.
 */
template<class T> Callback_ZGOPTaskBase_retryTaskPtr
newCallback_ZGOPTaskBase_retryTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_retryTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_retryTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_retryTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskBase_retryTask : public Callback_ZGOPTaskBase_retryTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskBase_retryTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_retryTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_retryTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_retryTaskPtr
newCallback_ZGOPTaskBase_retryTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_retryTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_retryTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_retryTaskPtr
newCallback_ZGOPTaskBase_retryTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_retryTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_abolishTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_abolishTask.
 */
template<class T>
class CallbackNC_ZGOPTaskBase_abolishTask : public Callback_ZGOPTaskBase_abolishTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskBase_abolishTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_abolishTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_abolishTask.
 */
template<class T> Callback_ZGOPTaskBase_abolishTaskPtr
newCallback_ZGOPTaskBase_abolishTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_abolishTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_abolishTask.
 */
template<class T> Callback_ZGOPTaskBase_abolishTaskPtr
newCallback_ZGOPTaskBase_abolishTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_abolishTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_abolishTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_abolishTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskBase_abolishTask : public Callback_ZGOPTaskBase_abolishTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskBase_abolishTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_abolishTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_abolishTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_abolishTaskPtr
newCallback_ZGOPTaskBase_abolishTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_abolishTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_abolishTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_abolishTaskPtr
newCallback_ZGOPTaskBase_abolishTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_abolishTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_confirmTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_confirmTask.
 */
template<class T>
class CallbackNC_ZGOPTaskBase_confirmTask : public Callback_ZGOPTaskBase_confirmTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskBase_confirmTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_confirmTask.
 */
template<class T> Callback_ZGOPTaskBase_confirmTaskPtr
newCallback_ZGOPTaskBase_confirmTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_confirmTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_confirmTask.
 */
template<class T> Callback_ZGOPTaskBase_confirmTaskPtr
newCallback_ZGOPTaskBase_confirmTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskBase_confirmTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskBase::begin_confirmTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskBase_confirmTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskBase_confirmTask : public Callback_ZGOPTaskBase_confirmTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskBase_confirmTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskBasePrx proxy = ZGOPTaskBasePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_confirmTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_confirmTaskPtr
newCallback_ZGOPTaskBase_confirmTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_confirmTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskBase::begin_confirmTask.
 */
template<class T, typename CT> Callback_ZGOPTaskBase_confirmTaskPtr
newCallback_ZGOPTaskBase_confirmTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskBase_confirmTask<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

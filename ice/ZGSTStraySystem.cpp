//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSTStraySystem.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSTStraySystem.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSTStraySystem_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSTStraySystem",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSTStraySystem_ops[] =
{
    "calculateOffset",
    "checkState",
    "dispatchData",
    "exitApp",
    "getSystemParam",
    "getValidStations",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "setMeasureStation",
    "setSystemParam",
    "startCalculate",
    "startDebug",
    "stopCalculate",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGSTStraySystem_setMeasureStation_name = "setMeasureStation";
const ::std::string iceC_ZG6000_ZGSTStraySystem_calculateOffset_name = "calculateOffset";
const ::std::string iceC_ZG6000_ZGSTStraySystem_startCalculate_name = "startCalculate";
const ::std::string iceC_ZG6000_ZGSTStraySystem_stopCalculate_name = "stopCalculate";
const ::std::string iceC_ZG6000_ZGSTStraySystem_getValidStations_name = "getValidStations";
const ::std::string iceC_ZG6000_ZGSTStraySystem_getSystemParam_name = "getSystemParam";
const ::std::string iceC_ZG6000_ZGSTStraySystem_setSystemParam_name = "setSystemParam";

}

bool
ZG6000::ZGSTStraySystem::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSTStraySystem_ids, iceC_ZG6000_ZGSTStraySystem_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSTStraySystem::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSTStraySystem_ids[0], &iceC_ZG6000_ZGSTStraySystem_ids[3]);
}

::std::string
ZG6000::ZGSTStraySystem::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSTStraySystem::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSTStraySystem";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_setMeasureStation(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_inStationID;
    ::std::string iceP_outStationID;
    istr->readAll(iceP_clientID, iceP_inStationID, iceP_outStationID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setMeasureStation(::std::move(iceP_clientID), ::std::move(iceP_inStationID), ::std::move(iceP_outStationID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_calculateOffset(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ErrorInfo iceP_e;
    bool ret = this->calculateOffset(iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_startCalculate(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ErrorInfo iceP_e;
    bool ret = this->startCalculate(iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_stopCalculate(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ErrorInfo iceP_e;
    bool ret = this->stopCalculate(iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_getValidStations(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listMapStation;
    ErrorInfo iceP_e;
    bool ret = this->getValidStations(iceP_listMapStation, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listMapStation, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_getSystemParam(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    StringMap iceP_systemParam;
    ErrorInfo iceP_e;
    bool ret = this->getSystemParam(iceP_systemParam, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_systemParam, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_setSystemParam(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_systemParam;
    istr->readAll(iceP_systemParam);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setSystemParam(::std::move(iceP_systemParam), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSTStraySystem_ops, iceC_ZG6000_ZGSTStraySystem_ops + 22, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSTStraySystem_ops)
    {
        case 0:
        {
            return _iceD_calculateOffset(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_dispatchData(in, current);
        }
        case 3:
        {
            return _iceD_exitApp(in, current);
        }
        case 4:
        {
            return _iceD_getSystemParam(in, current);
        }
        case 5:
        {
            return _iceD_getValidStations(in, current);
        }
        case 6:
        {
            return _iceD_getVersion(in, current);
        }
        case 7:
        {
            return _iceD_heartDebug(in, current);
        }
        case 8:
        {
            return _iceD_ice_id(in, current);
        }
        case 9:
        {
            return _iceD_ice_ids(in, current);
        }
        case 10:
        {
            return _iceD_ice_isA(in, current);
        }
        case 11:
        {
            return _iceD_ice_ping(in, current);
        }
        case 12:
        {
            return _iceD_isDebugging(in, current);
        }
        case 13:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 14:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 15:
        {
            return _iceD_setMeasureStation(in, current);
        }
        case 16:
        {
            return _iceD_setSystemParam(in, current);
        }
        case 17:
        {
            return _iceD_startCalculate(in, current);
        }
        case 18:
        {
            return _iceD_startDebug(in, current);
        }
        case 19:
        {
            return _iceD_stopCalculate(in, current);
        }
        case 20:
        {
            return _iceD_stopDebug(in, current);
        }
        case 21:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStraySystemPrx::_iceI_setMeasureStation(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::SetMeasureStationResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_inStationID, const ::std::string& iceP_outStationID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_setMeasureStation_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStraySystem_setMeasureStation_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_inStationID, iceP_outStationID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStraySystem::SetMeasureStationResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStraySystemPrx::_iceI_calculateOffset(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::CalculateOffsetResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_calculateOffset_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStraySystem_calculateOffset_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStraySystem::CalculateOffsetResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStraySystemPrx::_iceI_startCalculate(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::StartCalculateResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_startCalculate_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStraySystem_startCalculate_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStraySystem::StartCalculateResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStraySystemPrx::_iceI_stopCalculate(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::StopCalculateResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_stopCalculate_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStraySystem_stopCalculate_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStraySystem::StopCalculateResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStraySystemPrx::_iceI_getValidStations(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::GetValidStationsResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_getValidStations_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStraySystem_getValidStations_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStraySystem::GetValidStationsResult v;
            istr->readAll(v.listMapStation, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStraySystemPrx::_iceI_getSystemParam(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::GetSystemParamResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_getSystemParam_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStraySystem_getSystemParam_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStraySystem::GetSystemParamResult v;
            istr->readAll(v.systemParam, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStraySystemPrx::_iceI_setSystemParam(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::SetSystemParamResult>>& outAsync, const StringMap& iceP_systemParam, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_setSystemParam_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStraySystem_setSystemParam_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_systemParam);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStraySystem::SetSystemParamResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSTStraySystemPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSTStraySystemPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSTStraySystemPrx::ice_staticId()
{
    return ZGSTStraySystem::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSTStraySystem_setMeasureStation_name = "setMeasureStation";

const ::std::string iceC_ZG6000_ZGSTStraySystem_calculateOffset_name = "calculateOffset";

const ::std::string iceC_ZG6000_ZGSTStraySystem_startCalculate_name = "startCalculate";

const ::std::string iceC_ZG6000_ZGSTStraySystem_stopCalculate_name = "stopCalculate";

const ::std::string iceC_ZG6000_ZGSTStraySystem_getValidStations_name = "getValidStations";

const ::std::string iceC_ZG6000_ZGSTStraySystem_getSystemParam_name = "getSystemParam";

const ::std::string iceC_ZG6000_ZGSTStraySystem_setSystemParam_name = "setSystemParam";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSTStraySystem* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSTStraySystem>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSTStraySystem;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStraySystem::_iceI_begin_setMeasureStation(const ::std::string& iceP_clientID, const ::std::string& iceP_inStationID, const ::std::string& iceP_outStationID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_setMeasureStation_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStraySystem_setMeasureStation_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStraySystem_setMeasureStation_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_inStationID);
        ostr->write(iceP_outStationID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSTStraySystem_setMeasureStation_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStraySystem::end_setMeasureStation(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_setMeasureStation_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStraySystem::_iceI_end_setMeasureStation(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_setMeasureStation_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStraySystem::_iceI_begin_calculateOffset(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_calculateOffset_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStraySystem_calculateOffset_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStraySystem_calculateOffset_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSTStraySystem_calculateOffset_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStraySystem::end_calculateOffset(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_calculateOffset_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStraySystem::_iceI_end_calculateOffset(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_calculateOffset_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStraySystem::_iceI_begin_startCalculate(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_startCalculate_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStraySystem_startCalculate_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStraySystem_startCalculate_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSTStraySystem_startCalculate_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStraySystem::end_startCalculate(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_startCalculate_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStraySystem::_iceI_end_startCalculate(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_startCalculate_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStraySystem::_iceI_begin_stopCalculate(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_stopCalculate_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStraySystem_stopCalculate_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStraySystem_stopCalculate_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSTStraySystem_stopCalculate_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStraySystem::end_stopCalculate(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_stopCalculate_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStraySystem::_iceI_end_stopCalculate(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_stopCalculate_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStraySystem::_iceI_begin_getValidStations(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_getValidStations_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStraySystem_getValidStations_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStraySystem_getValidStations_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSTStraySystem_getValidStations_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStraySystem::end_getValidStations(::ZG6000::ListStringMap& iceP_listMapStation, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_getValidStations_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listMapStation);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStraySystem::_iceI_end_getValidStations(::ZG6000::ListStringMap& iceP_listMapStation, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_getValidStations_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listMapStation);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStraySystem::_iceI_begin_getSystemParam(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_getSystemParam_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStraySystem_getSystemParam_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStraySystem_getSystemParam_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSTStraySystem_getSystemParam_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStraySystem::end_getSystemParam(::ZG6000::StringMap& iceP_systemParam, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_getSystemParam_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_systemParam);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStraySystem::_iceI_end_getSystemParam(::ZG6000::StringMap& iceP_systemParam, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_getSystemParam_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_systemParam);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStraySystem::_iceI_begin_setSystemParam(const ::ZG6000::StringMap& iceP_systemParam, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStraySystem_setSystemParam_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStraySystem_setSystemParam_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStraySystem_setSystemParam_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_systemParam);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSTStraySystem_setSystemParam_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStraySystem::end_setSystemParam(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_setSystemParam_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStraySystem::_iceI_end_setSystemParam(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStraySystem_setSystemParam_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSTStraySystem::_newInstance() const
{
    return new ZGSTStraySystem;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSTStraySystem::ice_staticId()
{
    return ::ZG6000::ZGSTStraySystem::ice_staticId();
}

ZG6000::ZGSTStraySystem::~ZGSTStraySystem()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSTStraySystem* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSTStraySystem_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSTStraySystem",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSTStraySystem::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSTStraySystem_ids, iceC_ZG6000_ZGSTStraySystem_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSTStraySystem::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSTStraySystem_ids[0], &iceC_ZG6000_ZGSTStraySystem_ids[3]);
}

const ::std::string&
ZG6000::ZGSTStraySystem::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSTStraySystem::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSTStraySystem";
    return typeId;
#else
    return iceC_ZG6000_ZGSTStraySystem_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_setMeasureStation(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_inStationID;
    ::std::string iceP_outStationID;
    istr->read(iceP_clientID);
    istr->read(iceP_inStationID);
    istr->read(iceP_outStationID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setMeasureStation(iceP_clientID, iceP_inStationID, iceP_outStationID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_calculateOffset(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ErrorInfo iceP_e;
    bool ret = this->calculateOffset(iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_startCalculate(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ErrorInfo iceP_e;
    bool ret = this->startCalculate(iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_stopCalculate(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ErrorInfo iceP_e;
    bool ret = this->stopCalculate(iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_getValidStations(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listMapStation;
    ErrorInfo iceP_e;
    bool ret = this->getValidStations(iceP_listMapStation, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listMapStation);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_getSystemParam(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    StringMap iceP_systemParam;
    ErrorInfo iceP_e;
    bool ret = this->getSystemParam(iceP_systemParam, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_systemParam);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceD_setSystemParam(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_systemParam;
    istr->read(iceP_systemParam);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setSystemParam(iceP_systemParam, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSTStraySystem_all[] =
{
    "calculateOffset",
    "checkState",
    "dispatchData",
    "exitApp",
    "getSystemParam",
    "getValidStations",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "setMeasureStation",
    "setSystemParam",
    "startCalculate",
    "startDebug",
    "stopCalculate",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSTStraySystem::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSTStraySystem_all, iceC_ZG6000_ZGSTStraySystem_all + 22, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSTStraySystem_all)
    {
        case 0:
        {
            return _iceD_calculateOffset(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_dispatchData(in, current);
        }
        case 3:
        {
            return _iceD_exitApp(in, current);
        }
        case 4:
        {
            return _iceD_getSystemParam(in, current);
        }
        case 5:
        {
            return _iceD_getValidStations(in, current);
        }
        case 6:
        {
            return _iceD_getVersion(in, current);
        }
        case 7:
        {
            return _iceD_heartDebug(in, current);
        }
        case 8:
        {
            return _iceD_ice_id(in, current);
        }
        case 9:
        {
            return _iceD_ice_ids(in, current);
        }
        case 10:
        {
            return _iceD_ice_isA(in, current);
        }
        case 11:
        {
            return _iceD_ice_ping(in, current);
        }
        case 12:
        {
            return _iceD_isDebugging(in, current);
        }
        case 13:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 14:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 15:
        {
            return _iceD_setMeasureStation(in, current);
        }
        case 16:
        {
            return _iceD_setSystemParam(in, current);
        }
        case 17:
        {
            return _iceD_startCalculate(in, current);
        }
        case 18:
        {
            return _iceD_startDebug(in, current);
        }
        case 19:
        {
            return _iceD_stopCalculate(in, current);
        }
        case 20:
        {
            return _iceD_stopDebug(in, current);
        }
        case 21:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSTStraySystem::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSTStraySystem, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSTStraySystem::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSTStraySystem, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSTStraySystemPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSTStraySystemPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSTStraySystem::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

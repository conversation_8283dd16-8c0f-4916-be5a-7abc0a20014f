//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPVoicePlay.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSPVoicePlay.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPVoicePlay_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPVoicePlay",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSPVoicePlay_ops[] =
{
    "checkState",
    "clear",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "play",
    "playMulti",
    "resumeDebug",
    "speak",
    "speakCount",
    "start",
    "startDebug",
    "stop",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGSPVoicePlay_play_name = "play";
const ::std::string iceC_ZG6000_ZGSPVoicePlay_playMulti_name = "playMulti";
const ::std::string iceC_ZG6000_ZGSPVoicePlay_speak_name = "speak";
const ::std::string iceC_ZG6000_ZGSPVoicePlay_speakCount_name = "speakCount";
const ::std::string iceC_ZG6000_ZGSPVoicePlay_start_name = "start";
const ::std::string iceC_ZG6000_ZGSPVoicePlay_stop_name = "stop";
const ::std::string iceC_ZG6000_ZGSPVoicePlay_clear_name = "clear";

}

bool
ZG6000::ZGSPVoicePlay::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPVoicePlay_ids, iceC_ZG6000_ZGSPVoicePlay_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSPVoicePlay::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSPVoicePlay_ids[0], &iceC_ZG6000_ZGSPVoicePlay_ids[3]);
}

::std::string
ZG6000::ZGSPVoicePlay::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPVoicePlay::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSPVoicePlay";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_play(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_alarmLevelID;
    ::std::string iceP_speechText;
    istr->readAll(iceP_alarmLevelID, iceP_speechText);
    inS.endReadParams();
    this->play(::std::move(iceP_alarmLevelID), ::std::move(iceP_speechText), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_playMulti(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listVoice;
    istr->readAll(iceP_listVoice);
    inS.endReadParams();
    this->playMulti(::std::move(iceP_listVoice), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_speak(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_speechText;
    istr->readAll(iceP_speechText);
    inS.endReadParams();
    this->speak(::std::move(iceP_speechText), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_speakCount(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_speechText;
    int iceP_repeatCount;
    istr->readAll(iceP_speechText, iceP_repeatCount);
    inS.endReadParams();
    this->speakCount(::std::move(iceP_speechText), iceP_repeatCount, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_start(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    this->start(current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_stop(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    this->stop(current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_clear(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    this->clear(current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPVoicePlay_ops, iceC_ZG6000_ZGSPVoicePlay_ops + 22, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPVoicePlay_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_clear(in, current);
        }
        case 2:
        {
            return _iceD_dispatchData(in, current);
        }
        case 3:
        {
            return _iceD_exitApp(in, current);
        }
        case 4:
        {
            return _iceD_getVersion(in, current);
        }
        case 5:
        {
            return _iceD_heartDebug(in, current);
        }
        case 6:
        {
            return _iceD_ice_id(in, current);
        }
        case 7:
        {
            return _iceD_ice_ids(in, current);
        }
        case 8:
        {
            return _iceD_ice_isA(in, current);
        }
        case 9:
        {
            return _iceD_ice_ping(in, current);
        }
        case 10:
        {
            return _iceD_isDebugging(in, current);
        }
        case 11:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 12:
        {
            return _iceD_play(in, current);
        }
        case 13:
        {
            return _iceD_playMulti(in, current);
        }
        case 14:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 15:
        {
            return _iceD_speak(in, current);
        }
        case 16:
        {
            return _iceD_speakCount(in, current);
        }
        case 17:
        {
            return _iceD_start(in, current);
        }
        case 18:
        {
            return _iceD_startDebug(in, current);
        }
        case 19:
        {
            return _iceD_stop(in, current);
        }
        case 20:
        {
            return _iceD_stopDebug(in, current);
        }
        case 21:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPVoicePlayPrx::_iceI_play(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_alarmLevelID, const ::std::string& iceP_speechText, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPVoicePlay_play_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_alarmLevelID, iceP_speechText);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPVoicePlayPrx::_iceI_playMulti(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ListStringMap& iceP_listVoice, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPVoicePlay_playMulti_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listVoice);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPVoicePlayPrx::_iceI_speak(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_speechText, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPVoicePlay_speak_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_speechText);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPVoicePlayPrx::_iceI_speakCount(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_speechText, int iceP_repeatCount, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPVoicePlay_speakCount_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_speechText, iceP_repeatCount);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPVoicePlayPrx::_iceI_start(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPVoicePlay_start_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPVoicePlayPrx::_iceI_stop(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPVoicePlay_stop_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPVoicePlayPrx::_iceI_clear(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPVoicePlay_clear_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSPVoicePlayPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSPVoicePlayPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSPVoicePlayPrx::ice_staticId()
{
    return ZGSPVoicePlay::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPVoicePlay_play_name = "play";

const ::std::string iceC_ZG6000_ZGSPVoicePlay_playMulti_name = "playMulti";

const ::std::string iceC_ZG6000_ZGSPVoicePlay_speak_name = "speak";

const ::std::string iceC_ZG6000_ZGSPVoicePlay_speakCount_name = "speakCount";

const ::std::string iceC_ZG6000_ZGSPVoicePlay_start_name = "start";

const ::std::string iceC_ZG6000_ZGSPVoicePlay_stop_name = "stop";

const ::std::string iceC_ZG6000_ZGSPVoicePlay_clear_name = "clear";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSPVoicePlay* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSPVoicePlay>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSPVoicePlay;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPVoicePlay::_iceI_begin_play(const ::std::string& iceP_alarmLevelID, const ::std::string& iceP_speechText, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPVoicePlay_play_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPVoicePlay_play_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_alarmLevelID);
        ostr->write(iceP_speechText);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPVoicePlay_play_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPVoicePlay::end_play(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPVoicePlay_play_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPVoicePlay::_iceI_begin_playMulti(const ::ZG6000::ListStringMap& iceP_listVoice, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPVoicePlay_playMulti_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPVoicePlay_playMulti_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listVoice);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPVoicePlay_playMulti_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPVoicePlay::end_playMulti(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPVoicePlay_playMulti_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPVoicePlay::_iceI_begin_speak(const ::std::string& iceP_speechText, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPVoicePlay_speak_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPVoicePlay_speak_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_speechText);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPVoicePlay_speak_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPVoicePlay::end_speak(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPVoicePlay_speak_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPVoicePlay::_iceI_begin_speakCount(const ::std::string& iceP_speechText, ::Ice::Int iceP_repeatCount, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPVoicePlay_speakCount_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPVoicePlay_speakCount_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_speechText);
        ostr->write(iceP_repeatCount);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPVoicePlay_speakCount_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPVoicePlay::end_speakCount(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPVoicePlay_speakCount_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPVoicePlay::_iceI_begin_start(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPVoicePlay_start_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPVoicePlay_start_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSPVoicePlay_start_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPVoicePlay::end_start(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPVoicePlay_start_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPVoicePlay::_iceI_begin_stop(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPVoicePlay_stop_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPVoicePlay_stop_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSPVoicePlay_stop_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPVoicePlay::end_stop(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPVoicePlay_stop_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPVoicePlay::_iceI_begin_clear(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPVoicePlay_clear_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPVoicePlay_clear_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSPVoicePlay_clear_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPVoicePlay::end_clear(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPVoicePlay_clear_name);
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSPVoicePlay::_newInstance() const
{
    return new ZGSPVoicePlay;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSPVoicePlay::ice_staticId()
{
    return ::ZG6000::ZGSPVoicePlay::ice_staticId();
}

ZG6000::ZGSPVoicePlay::~ZGSPVoicePlay()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSPVoicePlay* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPVoicePlay_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPVoicePlay",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSPVoicePlay::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPVoicePlay_ids, iceC_ZG6000_ZGSPVoicePlay_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSPVoicePlay::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSPVoicePlay_ids[0], &iceC_ZG6000_ZGSPVoicePlay_ids[3]);
}

const ::std::string&
ZG6000::ZGSPVoicePlay::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPVoicePlay::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSPVoicePlay";
    return typeId;
#else
    return iceC_ZG6000_ZGSPVoicePlay_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_play(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_alarmLevelID;
    ::std::string iceP_speechText;
    istr->read(iceP_alarmLevelID);
    istr->read(iceP_speechText);
    inS.endReadParams();
    this->play(iceP_alarmLevelID, iceP_speechText, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_playMulti(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listVoice;
    istr->read(iceP_listVoice);
    inS.endReadParams();
    this->playMulti(iceP_listVoice, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_speak(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_speechText;
    istr->read(iceP_speechText);
    inS.endReadParams();
    this->speak(iceP_speechText, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_speakCount(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_speechText;
    ::Ice::Int iceP_repeatCount;
    istr->read(iceP_speechText);
    istr->read(iceP_repeatCount);
    inS.endReadParams();
    this->speakCount(iceP_speechText, iceP_repeatCount, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_start(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    this->start(current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_stop(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    this->stop(current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceD_clear(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    this->clear(current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPVoicePlay_all[] =
{
    "checkState",
    "clear",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "play",
    "playMulti",
    "resumeDebug",
    "speak",
    "speakCount",
    "start",
    "startDebug",
    "stop",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSPVoicePlay::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPVoicePlay_all, iceC_ZG6000_ZGSPVoicePlay_all + 22, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPVoicePlay_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_clear(in, current);
        }
        case 2:
        {
            return _iceD_dispatchData(in, current);
        }
        case 3:
        {
            return _iceD_exitApp(in, current);
        }
        case 4:
        {
            return _iceD_getVersion(in, current);
        }
        case 5:
        {
            return _iceD_heartDebug(in, current);
        }
        case 6:
        {
            return _iceD_ice_id(in, current);
        }
        case 7:
        {
            return _iceD_ice_ids(in, current);
        }
        case 8:
        {
            return _iceD_ice_isA(in, current);
        }
        case 9:
        {
            return _iceD_ice_ping(in, current);
        }
        case 10:
        {
            return _iceD_isDebugging(in, current);
        }
        case 11:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 12:
        {
            return _iceD_play(in, current);
        }
        case 13:
        {
            return _iceD_playMulti(in, current);
        }
        case 14:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 15:
        {
            return _iceD_speak(in, current);
        }
        case 16:
        {
            return _iceD_speakCount(in, current);
        }
        case 17:
        {
            return _iceD_start(in, current);
        }
        case 18:
        {
            return _iceD_startDebug(in, current);
        }
        case 19:
        {
            return _iceD_stop(in, current);
        }
        case 20:
        {
            return _iceD_stopDebug(in, current);
        }
        case 21:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSPVoicePlay::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSPVoicePlay, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSPVoicePlay::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSPVoicePlay, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSPVoicePlayPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSPVoicePlayPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSPVoicePlay::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

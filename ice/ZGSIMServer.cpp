//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSIMServer.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSIMServer.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSIMServer_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSIMServer",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSIMServer_ops[] =
{
    "addNetworkAddr",
    "addNetworkAddrEx",
    "addTaskList",
    "bindNetInterface",
    "checkState",
    "clearTaskList",
    "deleteTaskList",
    "dispatchData",
    "exitApp",
    "getDatasetData",
    "getDatasetDataNum",
    "getDatasetList",
    "getDeviceData",
    "getDeviceDataNum",
    "getDeviceList",
    "getDeviceRun",
    "getNetInterface",
    "getNetworkAddr",
    "getPortData",
    "getPortDataNum",
    "getPortList",
    "getPrimaryData",
    "getPrimaryDataNum",
    "getPrimaryList",
    "getServerState",
    "getSignalValue",
    "getTaskList",
    "getTaskTest",
    "getTypeList",
    "getUserGroupData",
    "getUserGroupDataNum",
    "getUserGroupList",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "removeNetworkAddr",
    "removeNetworkAddrEx",
    "resumeDebug",
    "setDatasetAction",
    "setDeviceAction",
    "setDeviceRun",
    "setPortAction",
    "setPrimaryAction",
    "setServerAction",
    "setSignalValue",
    "setTaskTest",
    "setUserGroupAction",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGSIMServer_getServerState_name = "getServerState";
const ::std::string iceC_ZG6000_ZGSIMServer_setServerAction_name = "setServerAction";
const ::std::string iceC_ZG6000_ZGSIMServer_getDeviceList_name = "getDeviceList";
const ::std::string iceC_ZG6000_ZGSIMServer_setDeviceAction_name = "setDeviceAction";
const ::std::string iceC_ZG6000_ZGSIMServer_getPrimaryList_name = "getPrimaryList";
const ::std::string iceC_ZG6000_ZGSIMServer_getPortList_name = "getPortList";
const ::std::string iceC_ZG6000_ZGSIMServer_getDatasetList_name = "getDatasetList";
const ::std::string iceC_ZG6000_ZGSIMServer_getUserGroupList_name = "getUserGroupList";
const ::std::string iceC_ZG6000_ZGSIMServer_setPrimaryAction_name = "setPrimaryAction";
const ::std::string iceC_ZG6000_ZGSIMServer_setPortAction_name = "setPortAction";
const ::std::string iceC_ZG6000_ZGSIMServer_setDatasetAction_name = "setDatasetAction";
const ::std::string iceC_ZG6000_ZGSIMServer_setUserGroupAction_name = "setUserGroupAction";
const ::std::string iceC_ZG6000_ZGSIMServer_getSignalValue_name = "getSignalValue";
const ::std::string iceC_ZG6000_ZGSIMServer_setSignalValue_name = "setSignalValue";
const ::std::string iceC_ZG6000_ZGSIMServer_getDeviceData_name = "getDeviceData";
const ::std::string iceC_ZG6000_ZGSIMServer_getPrimaryData_name = "getPrimaryData";
const ::std::string iceC_ZG6000_ZGSIMServer_getPortData_name = "getPortData";
const ::std::string iceC_ZG6000_ZGSIMServer_getDatasetData_name = "getDatasetData";
const ::std::string iceC_ZG6000_ZGSIMServer_getUserGroupData_name = "getUserGroupData";
const ::std::string iceC_ZG6000_ZGSIMServer_getDeviceDataNum_name = "getDeviceDataNum";
const ::std::string iceC_ZG6000_ZGSIMServer_getPrimaryDataNum_name = "getPrimaryDataNum";
const ::std::string iceC_ZG6000_ZGSIMServer_getPortDataNum_name = "getPortDataNum";
const ::std::string iceC_ZG6000_ZGSIMServer_getDatasetDataNum_name = "getDatasetDataNum";
const ::std::string iceC_ZG6000_ZGSIMServer_getUserGroupDataNum_name = "getUserGroupDataNum";
const ::std::string iceC_ZG6000_ZGSIMServer_getNetInterface_name = "getNetInterface";
const ::std::string iceC_ZG6000_ZGSIMServer_getNetworkAddr_name = "getNetworkAddr";
const ::std::string iceC_ZG6000_ZGSIMServer_addNetworkAddr_name = "addNetworkAddr";
const ::std::string iceC_ZG6000_ZGSIMServer_removeNetworkAddr_name = "removeNetworkAddr";
const ::std::string iceC_ZG6000_ZGSIMServer_addNetworkAddrEx_name = "addNetworkAddrEx";
const ::std::string iceC_ZG6000_ZGSIMServer_removeNetworkAddrEx_name = "removeNetworkAddrEx";
const ::std::string iceC_ZG6000_ZGSIMServer_bindNetInterface_name = "bindNetInterface";
const ::std::string iceC_ZG6000_ZGSIMServer_getTypeList_name = "getTypeList";
const ::std::string iceC_ZG6000_ZGSIMServer_getDeviceRun_name = "getDeviceRun";
const ::std::string iceC_ZG6000_ZGSIMServer_setDeviceRun_name = "setDeviceRun";
const ::std::string iceC_ZG6000_ZGSIMServer_addTaskList_name = "addTaskList";
const ::std::string iceC_ZG6000_ZGSIMServer_deleteTaskList_name = "deleteTaskList";
const ::std::string iceC_ZG6000_ZGSIMServer_clearTaskList_name = "clearTaskList";
const ::std::string iceC_ZG6000_ZGSIMServer_getTaskList_name = "getTaskList";
const ::std::string iceC_ZG6000_ZGSIMServer_setTaskTest_name = "setTaskTest";
const ::std::string iceC_ZG6000_ZGSIMServer_getTaskTest_name = "getTaskTest";

}

bool
ZG6000::ZGSIMServer::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSIMServer_ids, iceC_ZG6000_ZGSIMServer_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSIMServer::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSIMServer_ids[0], &iceC_ZG6000_ZGSIMServer_ids[3]);
}

::std::string
ZG6000::ZGSIMServer::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSIMServer::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSIMServer";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getServerState(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    StringMap iceP_mapValue;
    ErrorInfo iceP_e;
    bool ret = this->getServerState(iceP_mapValue, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_mapValue, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setServerAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_mapValue;
    istr->readAll(iceP_mapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setServerAction(::std::move(iceP_mapValue), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDeviceList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceList(iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setDeviceAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->readAll(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setDeviceAction(::std::move(iceP_listItem), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPrimaryList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getPrimaryList(iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPortList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getPortList(iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDatasetList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getDatasetList(iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getUserGroupList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getUserGroupList(iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setPrimaryAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->readAll(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setPrimaryAction(::std::move(iceP_listItem), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setPortAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->readAll(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setPortAction(::std::move(iceP_listItem), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setDatasetAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->readAll(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setDatasetAction(::std::move(iceP_listItem), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setUserGroupAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->readAll(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setUserGroupAction(::std::move(iceP_listItem), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getSignalValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    StringList iceP_listDataID;
    istr->readAll(iceP_dataType, iceP_listDataID);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getSignalValue(iceP_dataType, ::std::move(iceP_listDataID), iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setSignalValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ListStringMap iceP_listItem;
    istr->readAll(iceP_dataType, iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setSignalValue(iceP_dataType, ::std::move(iceP_listItem), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDeviceData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ::std::string iceP_deviceID;
    int iceP_page;
    int iceP_limit;
    istr->readAll(iceP_dataType, iceP_deviceID, iceP_page, iceP_limit);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceData(iceP_dataType, ::std::move(iceP_deviceID), iceP_page, iceP_limit, iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPrimaryData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ::std::string iceP_primaryID;
    int iceP_page;
    int iceP_limit;
    istr->readAll(iceP_dataType, iceP_primaryID, iceP_page, iceP_limit);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getPrimaryData(iceP_dataType, ::std::move(iceP_primaryID), iceP_page, iceP_limit, iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPortData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ::std::string iceP_portID;
    int iceP_page;
    int iceP_limit;
    istr->readAll(iceP_dataType, iceP_portID, iceP_page, iceP_limit);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getPortData(iceP_dataType, ::std::move(iceP_portID), iceP_page, iceP_limit, iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDatasetData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ::std::string iceP_datasetID;
    int iceP_page;
    int iceP_limit;
    istr->readAll(iceP_dataType, iceP_datasetID, iceP_page, iceP_limit);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getDatasetData(iceP_dataType, ::std::move(iceP_datasetID), iceP_page, iceP_limit, iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getUserGroupData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ::std::string iceP_userGroupID;
    int iceP_page;
    int iceP_limit;
    istr->readAll(iceP_dataType, iceP_userGroupID, iceP_page, iceP_limit);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getUserGroupData(iceP_dataType, ::std::move(iceP_userGroupID), iceP_page, iceP_limit, iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDeviceDataNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ::std::string iceP_deviceID;
    istr->readAll(iceP_dataType, iceP_deviceID);
    inS.endReadParams();
    int iceP_num;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceDataNum(iceP_dataType, ::std::move(iceP_deviceID), iceP_num, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_num, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPrimaryDataNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ::std::string iceP_primaryID;
    istr->readAll(iceP_dataType, iceP_primaryID);
    inS.endReadParams();
    int iceP_num;
    ErrorInfo iceP_e;
    bool ret = this->getPrimaryDataNum(iceP_dataType, ::std::move(iceP_primaryID), iceP_num, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_num, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPortDataNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ::std::string iceP_portID;
    istr->readAll(iceP_dataType, iceP_portID);
    inS.endReadParams();
    int iceP_num;
    ErrorInfo iceP_e;
    bool ret = this->getPortDataNum(iceP_dataType, ::std::move(iceP_portID), iceP_num, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_num, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDatasetDataNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ::std::string iceP_datasetID;
    istr->readAll(iceP_dataType, iceP_datasetID);
    inS.endReadParams();
    int iceP_num;
    ErrorInfo iceP_e;
    bool ret = this->getDatasetDataNum(iceP_dataType, ::std::move(iceP_datasetID), iceP_num, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_num, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getUserGroupDataNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_dataType;
    ::std::string iceP_userGroupID;
    istr->readAll(iceP_dataType, iceP_userGroupID);
    inS.endReadParams();
    int iceP_num;
    ErrorInfo iceP_e;
    bool ret = this->getUserGroupDataNum(iceP_dataType, ::std::move(iceP_userGroupID), iceP_num, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_num, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getNetInterface(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    StringList iceP_listName;
    ErrorInfo iceP_e;
    bool ret = this->getNetInterface(iceP_listName, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listName, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getNetworkAddr(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_name;
    istr->readAll(iceP_name);
    inS.endReadParams();
    ListStringMap iceP_listIPv4;
    ErrorInfo iceP_e;
    bool ret = this->getNetworkAddr(::std::move(iceP_name), iceP_listIPv4, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listIPv4, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_addNetworkAddr(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_name;
    istr->readAll(iceP_name);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addNetworkAddr(::std::move(iceP_name), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_removeNetworkAddr(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_name;
    istr->readAll(iceP_name);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->removeNetworkAddr(::std::move(iceP_name), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_addNetworkAddrEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_name;
    ListStringMap iceP_listIPv4;
    istr->readAll(iceP_name, iceP_listIPv4);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addNetworkAddrEx(::std::move(iceP_name), ::std::move(iceP_listIPv4), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_removeNetworkAddrEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_name;
    ListStringMap iceP_listIPv4;
    istr->readAll(iceP_name, iceP_listIPv4);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->removeNetworkAddrEx(::std::move(iceP_name), ::std::move(iceP_listIPv4), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_bindNetInterface(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_name;
    istr->readAll(iceP_name);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->bindNetInterface(::std::move(iceP_name), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getTypeList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_type;
    istr->readAll(iceP_type);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getTypeList(iceP_type, iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDeviceRun(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceRun(iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setDeviceRun(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->readAll(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setDeviceRun(::std::move(iceP_listItem), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_addTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_type;
    StringList iceP_listID;
    istr->readAll(iceP_type, iceP_listID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addTaskList(iceP_type, ::std::move(iceP_listID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_deleteTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_type;
    StringList iceP_listID;
    istr->readAll(iceP_type, iceP_listID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteTaskList(iceP_type, ::std::move(iceP_listID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_clearTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_type;
    istr->readAll(iceP_type);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->clearTaskList(iceP_type, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_type;
    istr->readAll(iceP_type);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getTaskList(iceP_type, iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setTaskTest(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_type;
    int iceP_testAction;
    ::std::string iceP_option;
    istr->readAll(iceP_type, iceP_testAction, iceP_option);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setTaskTest(iceP_type, iceP_testAction, ::std::move(iceP_option), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getTaskTest(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    int iceP_type;
    istr->readAll(iceP_type);
    inS.endReadParams();
    int iceP_testState;
    ErrorInfo iceP_e;
    bool ret = this->getTaskTest(iceP_type, iceP_testState, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_testState, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSIMServer_ops, iceC_ZG6000_ZGSIMServer_ops + 55, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSIMServer_ops)
    {
        case 0:
        {
            return _iceD_addNetworkAddr(in, current);
        }
        case 1:
        {
            return _iceD_addNetworkAddrEx(in, current);
        }
        case 2:
        {
            return _iceD_addTaskList(in, current);
        }
        case 3:
        {
            return _iceD_bindNetInterface(in, current);
        }
        case 4:
        {
            return _iceD_checkState(in, current);
        }
        case 5:
        {
            return _iceD_clearTaskList(in, current);
        }
        case 6:
        {
            return _iceD_deleteTaskList(in, current);
        }
        case 7:
        {
            return _iceD_dispatchData(in, current);
        }
        case 8:
        {
            return _iceD_exitApp(in, current);
        }
        case 9:
        {
            return _iceD_getDatasetData(in, current);
        }
        case 10:
        {
            return _iceD_getDatasetDataNum(in, current);
        }
        case 11:
        {
            return _iceD_getDatasetList(in, current);
        }
        case 12:
        {
            return _iceD_getDeviceData(in, current);
        }
        case 13:
        {
            return _iceD_getDeviceDataNum(in, current);
        }
        case 14:
        {
            return _iceD_getDeviceList(in, current);
        }
        case 15:
        {
            return _iceD_getDeviceRun(in, current);
        }
        case 16:
        {
            return _iceD_getNetInterface(in, current);
        }
        case 17:
        {
            return _iceD_getNetworkAddr(in, current);
        }
        case 18:
        {
            return _iceD_getPortData(in, current);
        }
        case 19:
        {
            return _iceD_getPortDataNum(in, current);
        }
        case 20:
        {
            return _iceD_getPortList(in, current);
        }
        case 21:
        {
            return _iceD_getPrimaryData(in, current);
        }
        case 22:
        {
            return _iceD_getPrimaryDataNum(in, current);
        }
        case 23:
        {
            return _iceD_getPrimaryList(in, current);
        }
        case 24:
        {
            return _iceD_getServerState(in, current);
        }
        case 25:
        {
            return _iceD_getSignalValue(in, current);
        }
        case 26:
        {
            return _iceD_getTaskList(in, current);
        }
        case 27:
        {
            return _iceD_getTaskTest(in, current);
        }
        case 28:
        {
            return _iceD_getTypeList(in, current);
        }
        case 29:
        {
            return _iceD_getUserGroupData(in, current);
        }
        case 30:
        {
            return _iceD_getUserGroupDataNum(in, current);
        }
        case 31:
        {
            return _iceD_getUserGroupList(in, current);
        }
        case 32:
        {
            return _iceD_getVersion(in, current);
        }
        case 33:
        {
            return _iceD_heartDebug(in, current);
        }
        case 34:
        {
            return _iceD_ice_id(in, current);
        }
        case 35:
        {
            return _iceD_ice_ids(in, current);
        }
        case 36:
        {
            return _iceD_ice_isA(in, current);
        }
        case 37:
        {
            return _iceD_ice_ping(in, current);
        }
        case 38:
        {
            return _iceD_isDebugging(in, current);
        }
        case 39:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 40:
        {
            return _iceD_removeNetworkAddr(in, current);
        }
        case 41:
        {
            return _iceD_removeNetworkAddrEx(in, current);
        }
        case 42:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 43:
        {
            return _iceD_setDatasetAction(in, current);
        }
        case 44:
        {
            return _iceD_setDeviceAction(in, current);
        }
        case 45:
        {
            return _iceD_setDeviceRun(in, current);
        }
        case 46:
        {
            return _iceD_setPortAction(in, current);
        }
        case 47:
        {
            return _iceD_setPrimaryAction(in, current);
        }
        case 48:
        {
            return _iceD_setServerAction(in, current);
        }
        case 49:
        {
            return _iceD_setSignalValue(in, current);
        }
        case 50:
        {
            return _iceD_setTaskTest(in, current);
        }
        case 51:
        {
            return _iceD_setUserGroupAction(in, current);
        }
        case 52:
        {
            return _iceD_startDebug(in, current);
        }
        case 53:
        {
            return _iceD_stopDebug(in, current);
        }
        case 54:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getServerState(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetServerStateResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getServerState_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getServerState_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetServerStateResult v;
            istr->readAll(v.mapValue, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_setServerAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetServerActionResult>>& outAsync, const StringMap& iceP_mapValue, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setServerAction_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_setServerAction_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_mapValue);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::SetServerActionResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getDeviceList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDeviceListResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDeviceList_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getDeviceList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetDeviceListResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_setDeviceAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetDeviceActionResult>>& outAsync, const ListStringMap& iceP_listItem, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setDeviceAction_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_setDeviceAction_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listItem);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::SetDeviceActionResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getPrimaryList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPrimaryListResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPrimaryList_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getPrimaryList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetPrimaryListResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getPortList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPortListResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPortList_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getPortList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetPortListResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getDatasetList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDatasetListResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDatasetList_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getDatasetList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetDatasetListResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getUserGroupList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetUserGroupListResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getUserGroupList_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getUserGroupList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetUserGroupListResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_setPrimaryAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetPrimaryActionResult>>& outAsync, const ListStringMap& iceP_listItem, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setPrimaryAction_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_setPrimaryAction_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listItem);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::SetPrimaryActionResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_setPortAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetPortActionResult>>& outAsync, const ListStringMap& iceP_listItem, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setPortAction_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_setPortAction_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listItem);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::SetPortActionResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_setDatasetAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetDatasetActionResult>>& outAsync, const ListStringMap& iceP_listItem, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setDatasetAction_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_setDatasetAction_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listItem);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::SetDatasetActionResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_setUserGroupAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetUserGroupActionResult>>& outAsync, const ListStringMap& iceP_listItem, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setUserGroupAction_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_setUserGroupAction_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listItem);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::SetUserGroupActionResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getSignalValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetSignalValueResult>>& outAsync, int iceP_dataType, const StringList& iceP_listDataID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getSignalValue_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getSignalValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_listDataID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetSignalValueResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_setSignalValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetSignalValueResult>>& outAsync, int iceP_dataType, const ListStringMap& iceP_listItem, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setSignalValue_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_setSignalValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_listItem);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::SetSignalValueResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getDeviceData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDeviceDataResult>>& outAsync, int iceP_dataType, const ::std::string& iceP_deviceID, int iceP_page, int iceP_limit, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDeviceData_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getDeviceData_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_deviceID, iceP_page, iceP_limit);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetDeviceDataResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getPrimaryData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPrimaryDataResult>>& outAsync, int iceP_dataType, const ::std::string& iceP_primaryID, int iceP_page, int iceP_limit, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPrimaryData_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getPrimaryData_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_primaryID, iceP_page, iceP_limit);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetPrimaryDataResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getPortData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPortDataResult>>& outAsync, int iceP_dataType, const ::std::string& iceP_portID, int iceP_page, int iceP_limit, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPortData_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getPortData_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_portID, iceP_page, iceP_limit);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetPortDataResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getDatasetData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDatasetDataResult>>& outAsync, int iceP_dataType, const ::std::string& iceP_datasetID, int iceP_page, int iceP_limit, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDatasetData_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getDatasetData_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_datasetID, iceP_page, iceP_limit);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetDatasetDataResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getUserGroupData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetUserGroupDataResult>>& outAsync, int iceP_dataType, const ::std::string& iceP_userGroupID, int iceP_page, int iceP_limit, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getUserGroupData_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getUserGroupData_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_userGroupID, iceP_page, iceP_limit);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetUserGroupDataResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getDeviceDataNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDeviceDataNumResult>>& outAsync, int iceP_dataType, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDeviceDataNum_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getDeviceDataNum_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetDeviceDataNumResult v;
            istr->readAll(v.num, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getPrimaryDataNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPrimaryDataNumResult>>& outAsync, int iceP_dataType, const ::std::string& iceP_primaryID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPrimaryDataNum_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getPrimaryDataNum_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_primaryID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetPrimaryDataNumResult v;
            istr->readAll(v.num, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getPortDataNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetPortDataNumResult>>& outAsync, int iceP_dataType, const ::std::string& iceP_portID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPortDataNum_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getPortDataNum_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_portID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetPortDataNumResult v;
            istr->readAll(v.num, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getDatasetDataNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDatasetDataNumResult>>& outAsync, int iceP_dataType, const ::std::string& iceP_datasetID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDatasetDataNum_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getDatasetDataNum_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_datasetID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetDatasetDataNumResult v;
            istr->readAll(v.num, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getUserGroupDataNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetUserGroupDataNumResult>>& outAsync, int iceP_dataType, const ::std::string& iceP_userGroupID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getUserGroupDataNum_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getUserGroupDataNum_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataType, iceP_userGroupID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetUserGroupDataNumResult v;
            istr->readAll(v.num, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getNetInterface(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetNetInterfaceResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getNetInterface_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getNetInterface_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetNetInterfaceResult v;
            istr->readAll(v.listName, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getNetworkAddr(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetNetworkAddrResult>>& outAsync, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getNetworkAddr_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getNetworkAddr_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetNetworkAddrResult v;
            istr->readAll(v.listIPv4, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_addNetworkAddr(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::AddNetworkAddrResult>>& outAsync, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_addNetworkAddr_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_addNetworkAddr_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::AddNetworkAddrResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_removeNetworkAddr(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::RemoveNetworkAddrResult>>& outAsync, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_removeNetworkAddr_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_removeNetworkAddr_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::RemoveNetworkAddrResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_addNetworkAddrEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::AddNetworkAddrExResult>>& outAsync, const ::std::string& iceP_name, const ListStringMap& iceP_listIPv4, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_addNetworkAddrEx_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_addNetworkAddrEx_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_name, iceP_listIPv4);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::AddNetworkAddrExResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_removeNetworkAddrEx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::RemoveNetworkAddrExResult>>& outAsync, const ::std::string& iceP_name, const ListStringMap& iceP_listIPv4, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_removeNetworkAddrEx_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_removeNetworkAddrEx_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_name, iceP_listIPv4);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::RemoveNetworkAddrExResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_bindNetInterface(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::BindNetInterfaceResult>>& outAsync, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_bindNetInterface_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_bindNetInterface_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::BindNetInterfaceResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getTypeList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetTypeListResult>>& outAsync, int iceP_type, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getTypeList_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getTypeList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_type);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetTypeListResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getDeviceRun(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetDeviceRunResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDeviceRun_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getDeviceRun_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetDeviceRunResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_setDeviceRun(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetDeviceRunResult>>& outAsync, const ListStringMap& iceP_listItem, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setDeviceRun_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_setDeviceRun_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listItem);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::SetDeviceRunResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_addTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::AddTaskListResult>>& outAsync, int iceP_type, const StringList& iceP_listID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_addTaskList_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_addTaskList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_type, iceP_listID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::AddTaskListResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_deleteTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::DeleteTaskListResult>>& outAsync, int iceP_type, const StringList& iceP_listID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_deleteTaskList_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_deleteTaskList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_type, iceP_listID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::DeleteTaskListResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_clearTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::ClearTaskListResult>>& outAsync, int iceP_type, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_clearTaskList_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_clearTaskList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_type);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::ClearTaskListResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetTaskListResult>>& outAsync, int iceP_type, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getTaskList_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getTaskList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_type);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetTaskListResult v;
            istr->readAll(v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_setTaskTest(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::SetTaskTestResult>>& outAsync, int iceP_type, int iceP_testAction, const ::std::string& iceP_option, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setTaskTest_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_setTaskTest_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_type, iceP_testAction, iceP_option);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::SetTaskTestResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSIMServerPrx::_iceI_getTaskTest(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSIMServer::GetTaskTestResult>>& outAsync, int iceP_type, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getTaskTest_name);
    outAsync->invoke(iceC_ZG6000_ZGSIMServer_getTaskTest_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_type);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSIMServer::GetTaskTestResult v;
            istr->readAll(v.testState, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSIMServerPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSIMServerPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSIMServerPrx::ice_staticId()
{
    return ZGSIMServer::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSIMServer_getServerState_name = "getServerState";

const ::std::string iceC_ZG6000_ZGSIMServer_setServerAction_name = "setServerAction";

const ::std::string iceC_ZG6000_ZGSIMServer_getDeviceList_name = "getDeviceList";

const ::std::string iceC_ZG6000_ZGSIMServer_setDeviceAction_name = "setDeviceAction";

const ::std::string iceC_ZG6000_ZGSIMServer_getPrimaryList_name = "getPrimaryList";

const ::std::string iceC_ZG6000_ZGSIMServer_getPortList_name = "getPortList";

const ::std::string iceC_ZG6000_ZGSIMServer_getDatasetList_name = "getDatasetList";

const ::std::string iceC_ZG6000_ZGSIMServer_getUserGroupList_name = "getUserGroupList";

const ::std::string iceC_ZG6000_ZGSIMServer_setPrimaryAction_name = "setPrimaryAction";

const ::std::string iceC_ZG6000_ZGSIMServer_setPortAction_name = "setPortAction";

const ::std::string iceC_ZG6000_ZGSIMServer_setDatasetAction_name = "setDatasetAction";

const ::std::string iceC_ZG6000_ZGSIMServer_setUserGroupAction_name = "setUserGroupAction";

const ::std::string iceC_ZG6000_ZGSIMServer_getSignalValue_name = "getSignalValue";

const ::std::string iceC_ZG6000_ZGSIMServer_setSignalValue_name = "setSignalValue";

const ::std::string iceC_ZG6000_ZGSIMServer_getDeviceData_name = "getDeviceData";

const ::std::string iceC_ZG6000_ZGSIMServer_getPrimaryData_name = "getPrimaryData";

const ::std::string iceC_ZG6000_ZGSIMServer_getPortData_name = "getPortData";

const ::std::string iceC_ZG6000_ZGSIMServer_getDatasetData_name = "getDatasetData";

const ::std::string iceC_ZG6000_ZGSIMServer_getUserGroupData_name = "getUserGroupData";

const ::std::string iceC_ZG6000_ZGSIMServer_getDeviceDataNum_name = "getDeviceDataNum";

const ::std::string iceC_ZG6000_ZGSIMServer_getPrimaryDataNum_name = "getPrimaryDataNum";

const ::std::string iceC_ZG6000_ZGSIMServer_getPortDataNum_name = "getPortDataNum";

const ::std::string iceC_ZG6000_ZGSIMServer_getDatasetDataNum_name = "getDatasetDataNum";

const ::std::string iceC_ZG6000_ZGSIMServer_getUserGroupDataNum_name = "getUserGroupDataNum";

const ::std::string iceC_ZG6000_ZGSIMServer_getNetInterface_name = "getNetInterface";

const ::std::string iceC_ZG6000_ZGSIMServer_getNetworkAddr_name = "getNetworkAddr";

const ::std::string iceC_ZG6000_ZGSIMServer_addNetworkAddr_name = "addNetworkAddr";

const ::std::string iceC_ZG6000_ZGSIMServer_removeNetworkAddr_name = "removeNetworkAddr";

const ::std::string iceC_ZG6000_ZGSIMServer_addNetworkAddrEx_name = "addNetworkAddrEx";

const ::std::string iceC_ZG6000_ZGSIMServer_removeNetworkAddrEx_name = "removeNetworkAddrEx";

const ::std::string iceC_ZG6000_ZGSIMServer_bindNetInterface_name = "bindNetInterface";

const ::std::string iceC_ZG6000_ZGSIMServer_getTypeList_name = "getTypeList";

const ::std::string iceC_ZG6000_ZGSIMServer_getDeviceRun_name = "getDeviceRun";

const ::std::string iceC_ZG6000_ZGSIMServer_setDeviceRun_name = "setDeviceRun";

const ::std::string iceC_ZG6000_ZGSIMServer_addTaskList_name = "addTaskList";

const ::std::string iceC_ZG6000_ZGSIMServer_deleteTaskList_name = "deleteTaskList";

const ::std::string iceC_ZG6000_ZGSIMServer_clearTaskList_name = "clearTaskList";

const ::std::string iceC_ZG6000_ZGSIMServer_getTaskList_name = "getTaskList";

const ::std::string iceC_ZG6000_ZGSIMServer_setTaskTest_name = "setTaskTest";

const ::std::string iceC_ZG6000_ZGSIMServer_getTaskTest_name = "getTaskTest";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSIMServer* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSIMServer>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSIMServer;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getServerState(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getServerState_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getServerState_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getServerState_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getServerState_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getServerState(::ZG6000::StringMap& iceP_mapValue, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getServerState_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getServerState(::ZG6000::StringMap& iceP_mapValue, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getServerState_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_mapValue);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_setServerAction(const ::ZG6000::StringMap& iceP_mapValue, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setServerAction_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_setServerAction_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_setServerAction_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_mapValue);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_setServerAction_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_setServerAction(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setServerAction_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_setServerAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setServerAction_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getDeviceList(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDeviceList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getDeviceList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getDeviceList_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getDeviceList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getDeviceList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDeviceList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getDeviceList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDeviceList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_setDeviceAction(const ::ZG6000::ListStringMap& iceP_listItem, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setDeviceAction_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_setDeviceAction_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_setDeviceAction_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listItem);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_setDeviceAction_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_setDeviceAction(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setDeviceAction_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_setDeviceAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setDeviceAction_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getPrimaryList(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPrimaryList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getPrimaryList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getPrimaryList_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getPrimaryList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getPrimaryList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPrimaryList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getPrimaryList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPrimaryList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getPortList(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPortList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getPortList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getPortList_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getPortList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getPortList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPortList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getPortList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPortList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getDatasetList(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDatasetList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getDatasetList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getDatasetList_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getDatasetList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getDatasetList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDatasetList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getDatasetList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDatasetList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getUserGroupList(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getUserGroupList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getUserGroupList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getUserGroupList_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getUserGroupList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getUserGroupList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getUserGroupList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getUserGroupList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getUserGroupList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_setPrimaryAction(const ::ZG6000::ListStringMap& iceP_listItem, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setPrimaryAction_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_setPrimaryAction_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_setPrimaryAction_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listItem);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_setPrimaryAction_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_setPrimaryAction(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setPrimaryAction_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_setPrimaryAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setPrimaryAction_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_setPortAction(const ::ZG6000::ListStringMap& iceP_listItem, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setPortAction_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_setPortAction_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_setPortAction_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listItem);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_setPortAction_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_setPortAction(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setPortAction_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_setPortAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setPortAction_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_setDatasetAction(const ::ZG6000::ListStringMap& iceP_listItem, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setDatasetAction_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_setDatasetAction_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_setDatasetAction_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listItem);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_setDatasetAction_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_setDatasetAction(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setDatasetAction_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_setDatasetAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setDatasetAction_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_setUserGroupAction(const ::ZG6000::ListStringMap& iceP_listItem, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setUserGroupAction_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_setUserGroupAction_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_setUserGroupAction_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listItem);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_setUserGroupAction_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_setUserGroupAction(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setUserGroupAction_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_setUserGroupAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setUserGroupAction_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getSignalValue(::Ice::Int iceP_dataType, const ::ZG6000::StringList& iceP_listDataID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getSignalValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getSignalValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getSignalValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_listDataID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getSignalValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getSignalValue(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getSignalValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getSignalValue(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getSignalValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_setSignalValue(::Ice::Int iceP_dataType, const ::ZG6000::ListStringMap& iceP_listItem, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setSignalValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_setSignalValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_setSignalValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_listItem);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_setSignalValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_setSignalValue(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setSignalValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_setSignalValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setSignalValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getDeviceData(::Ice::Int iceP_dataType, const ::std::string& iceP_deviceID, ::Ice::Int iceP_page, ::Ice::Int iceP_limit, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDeviceData_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getDeviceData_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getDeviceData_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_page);
        ostr->write(iceP_limit);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getDeviceData_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getDeviceData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDeviceData_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getDeviceData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDeviceData_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getPrimaryData(::Ice::Int iceP_dataType, const ::std::string& iceP_primaryID, ::Ice::Int iceP_page, ::Ice::Int iceP_limit, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPrimaryData_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getPrimaryData_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getPrimaryData_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_primaryID);
        ostr->write(iceP_page);
        ostr->write(iceP_limit);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getPrimaryData_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getPrimaryData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPrimaryData_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getPrimaryData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPrimaryData_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getPortData(::Ice::Int iceP_dataType, const ::std::string& iceP_portID, ::Ice::Int iceP_page, ::Ice::Int iceP_limit, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPortData_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getPortData_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getPortData_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_portID);
        ostr->write(iceP_page);
        ostr->write(iceP_limit);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getPortData_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getPortData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPortData_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getPortData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPortData_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getDatasetData(::Ice::Int iceP_dataType, const ::std::string& iceP_datasetID, ::Ice::Int iceP_page, ::Ice::Int iceP_limit, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDatasetData_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getDatasetData_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getDatasetData_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_page);
        ostr->write(iceP_limit);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getDatasetData_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getDatasetData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDatasetData_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getDatasetData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDatasetData_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getUserGroupData(::Ice::Int iceP_dataType, const ::std::string& iceP_userGroupID, ::Ice::Int iceP_page, ::Ice::Int iceP_limit, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getUserGroupData_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getUserGroupData_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getUserGroupData_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_userGroupID);
        ostr->write(iceP_page);
        ostr->write(iceP_limit);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getUserGroupData_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getUserGroupData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getUserGroupData_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getUserGroupData(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getUserGroupData_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getDeviceDataNum(::Ice::Int iceP_dataType, const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDeviceDataNum_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getDeviceDataNum_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getDeviceDataNum_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getDeviceDataNum_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getDeviceDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDeviceDataNum_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_num);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getDeviceDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDeviceDataNum_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_num);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getPrimaryDataNum(::Ice::Int iceP_dataType, const ::std::string& iceP_primaryID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPrimaryDataNum_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getPrimaryDataNum_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getPrimaryDataNum_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_primaryID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getPrimaryDataNum_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getPrimaryDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPrimaryDataNum_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_num);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getPrimaryDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPrimaryDataNum_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_num);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getPortDataNum(::Ice::Int iceP_dataType, const ::std::string& iceP_portID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getPortDataNum_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getPortDataNum_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getPortDataNum_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_portID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getPortDataNum_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getPortDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPortDataNum_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_num);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getPortDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getPortDataNum_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_num);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getDatasetDataNum(::Ice::Int iceP_dataType, const ::std::string& iceP_datasetID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDatasetDataNum_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getDatasetDataNum_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getDatasetDataNum_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_datasetID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getDatasetDataNum_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getDatasetDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDatasetDataNum_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_num);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getDatasetDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDatasetDataNum_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_num);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getUserGroupDataNum(::Ice::Int iceP_dataType, const ::std::string& iceP_userGroupID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getUserGroupDataNum_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getUserGroupDataNum_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getUserGroupDataNum_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataType);
        ostr->write(iceP_userGroupID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getUserGroupDataNum_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getUserGroupDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getUserGroupDataNum_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_num);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getUserGroupDataNum(::Ice::Int& iceP_num, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getUserGroupDataNum_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_num);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getNetInterface(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getNetInterface_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getNetInterface_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getNetInterface_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getNetInterface_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getNetInterface(::ZG6000::StringList& iceP_listName, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getNetInterface_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listName);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getNetInterface(::ZG6000::StringList& iceP_listName, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getNetInterface_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listName);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getNetworkAddr(const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getNetworkAddr_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getNetworkAddr_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getNetworkAddr_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getNetworkAddr_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getNetworkAddr(::ZG6000::ListStringMap& iceP_listIPv4, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getNetworkAddr_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listIPv4);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getNetworkAddr(::ZG6000::ListStringMap& iceP_listIPv4, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getNetworkAddr_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listIPv4);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_addNetworkAddr(const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_addNetworkAddr_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_addNetworkAddr_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_addNetworkAddr_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_addNetworkAddr_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_addNetworkAddr(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_addNetworkAddr_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_addNetworkAddr(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_addNetworkAddr_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_removeNetworkAddr(const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_removeNetworkAddr_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_removeNetworkAddr_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_removeNetworkAddr_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_removeNetworkAddr_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_removeNetworkAddr(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_removeNetworkAddr_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_removeNetworkAddr(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_removeNetworkAddr_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_addNetworkAddrEx(const ::std::string& iceP_name, const ::ZG6000::ListStringMap& iceP_listIPv4, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_addNetworkAddrEx_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_addNetworkAddrEx_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_addNetworkAddrEx_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_name);
        ostr->write(iceP_listIPv4);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_addNetworkAddrEx_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_addNetworkAddrEx(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_addNetworkAddrEx_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_addNetworkAddrEx(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_addNetworkAddrEx_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_removeNetworkAddrEx(const ::std::string& iceP_name, const ::ZG6000::ListStringMap& iceP_listIPv4, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_removeNetworkAddrEx_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_removeNetworkAddrEx_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_removeNetworkAddrEx_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_name);
        ostr->write(iceP_listIPv4);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_removeNetworkAddrEx_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_removeNetworkAddrEx(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_removeNetworkAddrEx_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_removeNetworkAddrEx(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_removeNetworkAddrEx_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_bindNetInterface(const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_bindNetInterface_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_bindNetInterface_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_bindNetInterface_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_bindNetInterface_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_bindNetInterface(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_bindNetInterface_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_bindNetInterface(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_bindNetInterface_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getTypeList(::Ice::Int iceP_type, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getTypeList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getTypeList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getTypeList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_type);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getTypeList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getTypeList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getTypeList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getTypeList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getTypeList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getDeviceRun(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getDeviceRun_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getDeviceRun_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getDeviceRun_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getDeviceRun_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getDeviceRun(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDeviceRun_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getDeviceRun(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getDeviceRun_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_setDeviceRun(const ::ZG6000::ListStringMap& iceP_listItem, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setDeviceRun_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_setDeviceRun_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_setDeviceRun_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listItem);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_setDeviceRun_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_setDeviceRun(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setDeviceRun_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_setDeviceRun(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setDeviceRun_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_addTaskList(::Ice::Int iceP_type, const ::ZG6000::StringList& iceP_listID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_addTaskList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_addTaskList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_addTaskList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_type);
        ostr->write(iceP_listID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_addTaskList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_addTaskList(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_addTaskList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_addTaskList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_addTaskList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_deleteTaskList(::Ice::Int iceP_type, const ::ZG6000::StringList& iceP_listID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_deleteTaskList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_deleteTaskList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_deleteTaskList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_type);
        ostr->write(iceP_listID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_deleteTaskList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_deleteTaskList(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_deleteTaskList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_deleteTaskList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_deleteTaskList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_clearTaskList(::Ice::Int iceP_type, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_clearTaskList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_clearTaskList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_clearTaskList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_type);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_clearTaskList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_clearTaskList(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_clearTaskList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_clearTaskList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_clearTaskList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getTaskList(::Ice::Int iceP_type, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getTaskList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getTaskList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getTaskList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_type);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getTaskList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getTaskList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getTaskList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getTaskList(::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getTaskList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_setTaskTest(::Ice::Int iceP_type, ::Ice::Int iceP_testAction, const ::std::string& iceP_option, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_setTaskTest_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_setTaskTest_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_setTaskTest_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_type);
        ostr->write(iceP_testAction);
        ostr->write(iceP_option);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_setTaskTest_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_setTaskTest(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setTaskTest_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_setTaskTest(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_setTaskTest_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSIMServer::_iceI_begin_getTaskTest(::Ice::Int iceP_type, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSIMServer_getTaskTest_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSIMServer_getTaskTest_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSIMServer_getTaskTest_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_type);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSIMServer_getTaskTest_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSIMServer::end_getTaskTest(::Ice::Int& iceP_testState, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getTaskTest_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_testState);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSIMServer::_iceI_end_getTaskTest(::Ice::Int& iceP_testState, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSIMServer_getTaskTest_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_testState);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSIMServer::_newInstance() const
{
    return new ZGSIMServer;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSIMServer::ice_staticId()
{
    return ::ZG6000::ZGSIMServer::ice_staticId();
}

ZG6000::ZGSIMServer::~ZGSIMServer()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSIMServer* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSIMServer_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSIMServer",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSIMServer::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSIMServer_ids, iceC_ZG6000_ZGSIMServer_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSIMServer::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSIMServer_ids[0], &iceC_ZG6000_ZGSIMServer_ids[3]);
}

const ::std::string&
ZG6000::ZGSIMServer::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSIMServer::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSIMServer";
    return typeId;
#else
    return iceC_ZG6000_ZGSIMServer_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getServerState(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    StringMap iceP_mapValue;
    ErrorInfo iceP_e;
    bool ret = this->getServerState(iceP_mapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_mapValue);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setServerAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_mapValue;
    istr->read(iceP_mapValue);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setServerAction(iceP_mapValue, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDeviceList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceList(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setDeviceAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->read(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setDeviceAction(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPrimaryList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getPrimaryList(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPortList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getPortList(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDatasetList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getDatasetList(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getUserGroupList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getUserGroupList(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setPrimaryAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->read(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setPrimaryAction(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setPortAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->read(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setPortAction(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setDatasetAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->read(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setDatasetAction(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setUserGroupAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->read(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setUserGroupAction(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getSignalValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    StringList iceP_listDataID;
    istr->read(iceP_dataType);
    istr->read(iceP_listDataID);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getSignalValue(iceP_dataType, iceP_listDataID, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setSignalValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ListStringMap iceP_listItem;
    istr->read(iceP_dataType);
    istr->read(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setSignalValue(iceP_dataType, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDeviceData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ::std::string iceP_deviceID;
    ::Ice::Int iceP_page;
    ::Ice::Int iceP_limit;
    istr->read(iceP_dataType);
    istr->read(iceP_deviceID);
    istr->read(iceP_page);
    istr->read(iceP_limit);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceData(iceP_dataType, iceP_deviceID, iceP_page, iceP_limit, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPrimaryData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ::std::string iceP_primaryID;
    ::Ice::Int iceP_page;
    ::Ice::Int iceP_limit;
    istr->read(iceP_dataType);
    istr->read(iceP_primaryID);
    istr->read(iceP_page);
    istr->read(iceP_limit);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getPrimaryData(iceP_dataType, iceP_primaryID, iceP_page, iceP_limit, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPortData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ::std::string iceP_portID;
    ::Ice::Int iceP_page;
    ::Ice::Int iceP_limit;
    istr->read(iceP_dataType);
    istr->read(iceP_portID);
    istr->read(iceP_page);
    istr->read(iceP_limit);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getPortData(iceP_dataType, iceP_portID, iceP_page, iceP_limit, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDatasetData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ::std::string iceP_datasetID;
    ::Ice::Int iceP_page;
    ::Ice::Int iceP_limit;
    istr->read(iceP_dataType);
    istr->read(iceP_datasetID);
    istr->read(iceP_page);
    istr->read(iceP_limit);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getDatasetData(iceP_dataType, iceP_datasetID, iceP_page, iceP_limit, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getUserGroupData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ::std::string iceP_userGroupID;
    ::Ice::Int iceP_page;
    ::Ice::Int iceP_limit;
    istr->read(iceP_dataType);
    istr->read(iceP_userGroupID);
    istr->read(iceP_page);
    istr->read(iceP_limit);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getUserGroupData(iceP_dataType, iceP_userGroupID, iceP_page, iceP_limit, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDeviceDataNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ::std::string iceP_deviceID;
    istr->read(iceP_dataType);
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ::Ice::Int iceP_num;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceDataNum(iceP_dataType, iceP_deviceID, iceP_num, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_num);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPrimaryDataNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ::std::string iceP_primaryID;
    istr->read(iceP_dataType);
    istr->read(iceP_primaryID);
    inS.endReadParams();
    ::Ice::Int iceP_num;
    ErrorInfo iceP_e;
    bool ret = this->getPrimaryDataNum(iceP_dataType, iceP_primaryID, iceP_num, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_num);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getPortDataNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ::std::string iceP_portID;
    istr->read(iceP_dataType);
    istr->read(iceP_portID);
    inS.endReadParams();
    ::Ice::Int iceP_num;
    ErrorInfo iceP_e;
    bool ret = this->getPortDataNum(iceP_dataType, iceP_portID, iceP_num, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_num);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDatasetDataNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ::std::string iceP_datasetID;
    istr->read(iceP_dataType);
    istr->read(iceP_datasetID);
    inS.endReadParams();
    ::Ice::Int iceP_num;
    ErrorInfo iceP_e;
    bool ret = this->getDatasetDataNum(iceP_dataType, iceP_datasetID, iceP_num, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_num);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getUserGroupDataNum(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_dataType;
    ::std::string iceP_userGroupID;
    istr->read(iceP_dataType);
    istr->read(iceP_userGroupID);
    inS.endReadParams();
    ::Ice::Int iceP_num;
    ErrorInfo iceP_e;
    bool ret = this->getUserGroupDataNum(iceP_dataType, iceP_userGroupID, iceP_num, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_num);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getNetInterface(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    StringList iceP_listName;
    ErrorInfo iceP_e;
    bool ret = this->getNetInterface(iceP_listName, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listName);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getNetworkAddr(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_name;
    istr->read(iceP_name);
    inS.endReadParams();
    ListStringMap iceP_listIPv4;
    ErrorInfo iceP_e;
    bool ret = this->getNetworkAddr(iceP_name, iceP_listIPv4, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listIPv4);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_addNetworkAddr(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_name;
    istr->read(iceP_name);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addNetworkAddr(iceP_name, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_removeNetworkAddr(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_name;
    istr->read(iceP_name);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->removeNetworkAddr(iceP_name, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_addNetworkAddrEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_name;
    ListStringMap iceP_listIPv4;
    istr->read(iceP_name);
    istr->read(iceP_listIPv4);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addNetworkAddrEx(iceP_name, iceP_listIPv4, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_removeNetworkAddrEx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_name;
    ListStringMap iceP_listIPv4;
    istr->read(iceP_name);
    istr->read(iceP_listIPv4);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->removeNetworkAddrEx(iceP_name, iceP_listIPv4, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_bindNetInterface(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_name;
    istr->read(iceP_name);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->bindNetInterface(iceP_name, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getTypeList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_type;
    istr->read(iceP_type);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getTypeList(iceP_type, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getDeviceRun(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceRun(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setDeviceRun(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->read(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setDeviceRun(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_addTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_type;
    StringList iceP_listID;
    istr->read(iceP_type);
    istr->read(iceP_listID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addTaskList(iceP_type, iceP_listID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_deleteTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_type;
    StringList iceP_listID;
    istr->read(iceP_type);
    istr->read(iceP_listID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteTaskList(iceP_type, iceP_listID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_clearTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_type;
    istr->read(iceP_type);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->clearTaskList(iceP_type, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_type;
    istr->read(iceP_type);
    inS.endReadParams();
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->getTaskList(iceP_type, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_setTaskTest(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_type;
    ::Ice::Int iceP_testAction;
    ::std::string iceP_option;
    istr->read(iceP_type);
    istr->read(iceP_testAction);
    istr->read(iceP_option);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setTaskTest(iceP_type, iceP_testAction, iceP_option, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceD_getTaskTest(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::Ice::Int iceP_type;
    istr->read(iceP_type);
    inS.endReadParams();
    ::Ice::Int iceP_testState;
    ErrorInfo iceP_e;
    bool ret = this->getTaskTest(iceP_type, iceP_testState, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_testState);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSIMServer_all[] =
{
    "addNetworkAddr",
    "addNetworkAddrEx",
    "addTaskList",
    "bindNetInterface",
    "checkState",
    "clearTaskList",
    "deleteTaskList",
    "dispatchData",
    "exitApp",
    "getDatasetData",
    "getDatasetDataNum",
    "getDatasetList",
    "getDeviceData",
    "getDeviceDataNum",
    "getDeviceList",
    "getDeviceRun",
    "getNetInterface",
    "getNetworkAddr",
    "getPortData",
    "getPortDataNum",
    "getPortList",
    "getPrimaryData",
    "getPrimaryDataNum",
    "getPrimaryList",
    "getServerState",
    "getSignalValue",
    "getTaskList",
    "getTaskTest",
    "getTypeList",
    "getUserGroupData",
    "getUserGroupDataNum",
    "getUserGroupList",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "removeNetworkAddr",
    "removeNetworkAddrEx",
    "resumeDebug",
    "setDatasetAction",
    "setDeviceAction",
    "setDeviceRun",
    "setPortAction",
    "setPrimaryAction",
    "setServerAction",
    "setSignalValue",
    "setTaskTest",
    "setUserGroupAction",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSIMServer::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSIMServer_all, iceC_ZG6000_ZGSIMServer_all + 55, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSIMServer_all)
    {
        case 0:
        {
            return _iceD_addNetworkAddr(in, current);
        }
        case 1:
        {
            return _iceD_addNetworkAddrEx(in, current);
        }
        case 2:
        {
            return _iceD_addTaskList(in, current);
        }
        case 3:
        {
            return _iceD_bindNetInterface(in, current);
        }
        case 4:
        {
            return _iceD_checkState(in, current);
        }
        case 5:
        {
            return _iceD_clearTaskList(in, current);
        }
        case 6:
        {
            return _iceD_deleteTaskList(in, current);
        }
        case 7:
        {
            return _iceD_dispatchData(in, current);
        }
        case 8:
        {
            return _iceD_exitApp(in, current);
        }
        case 9:
        {
            return _iceD_getDatasetData(in, current);
        }
        case 10:
        {
            return _iceD_getDatasetDataNum(in, current);
        }
        case 11:
        {
            return _iceD_getDatasetList(in, current);
        }
        case 12:
        {
            return _iceD_getDeviceData(in, current);
        }
        case 13:
        {
            return _iceD_getDeviceDataNum(in, current);
        }
        case 14:
        {
            return _iceD_getDeviceList(in, current);
        }
        case 15:
        {
            return _iceD_getDeviceRun(in, current);
        }
        case 16:
        {
            return _iceD_getNetInterface(in, current);
        }
        case 17:
        {
            return _iceD_getNetworkAddr(in, current);
        }
        case 18:
        {
            return _iceD_getPortData(in, current);
        }
        case 19:
        {
            return _iceD_getPortDataNum(in, current);
        }
        case 20:
        {
            return _iceD_getPortList(in, current);
        }
        case 21:
        {
            return _iceD_getPrimaryData(in, current);
        }
        case 22:
        {
            return _iceD_getPrimaryDataNum(in, current);
        }
        case 23:
        {
            return _iceD_getPrimaryList(in, current);
        }
        case 24:
        {
            return _iceD_getServerState(in, current);
        }
        case 25:
        {
            return _iceD_getSignalValue(in, current);
        }
        case 26:
        {
            return _iceD_getTaskList(in, current);
        }
        case 27:
        {
            return _iceD_getTaskTest(in, current);
        }
        case 28:
        {
            return _iceD_getTypeList(in, current);
        }
        case 29:
        {
            return _iceD_getUserGroupData(in, current);
        }
        case 30:
        {
            return _iceD_getUserGroupDataNum(in, current);
        }
        case 31:
        {
            return _iceD_getUserGroupList(in, current);
        }
        case 32:
        {
            return _iceD_getVersion(in, current);
        }
        case 33:
        {
            return _iceD_heartDebug(in, current);
        }
        case 34:
        {
            return _iceD_ice_id(in, current);
        }
        case 35:
        {
            return _iceD_ice_ids(in, current);
        }
        case 36:
        {
            return _iceD_ice_isA(in, current);
        }
        case 37:
        {
            return _iceD_ice_ping(in, current);
        }
        case 38:
        {
            return _iceD_isDebugging(in, current);
        }
        case 39:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 40:
        {
            return _iceD_removeNetworkAddr(in, current);
        }
        case 41:
        {
            return _iceD_removeNetworkAddrEx(in, current);
        }
        case 42:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 43:
        {
            return _iceD_setDatasetAction(in, current);
        }
        case 44:
        {
            return _iceD_setDeviceAction(in, current);
        }
        case 45:
        {
            return _iceD_setDeviceRun(in, current);
        }
        case 46:
        {
            return _iceD_setPortAction(in, current);
        }
        case 47:
        {
            return _iceD_setPrimaryAction(in, current);
        }
        case 48:
        {
            return _iceD_setServerAction(in, current);
        }
        case 49:
        {
            return _iceD_setSignalValue(in, current);
        }
        case 50:
        {
            return _iceD_setTaskTest(in, current);
        }
        case 51:
        {
            return _iceD_setUserGroupAction(in, current);
        }
        case 52:
        {
            return _iceD_startDebug(in, current);
        }
        case 53:
        {
            return _iceD_stopDebug(in, current);
        }
        case 54:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSIMServer::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSIMServer, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSIMServer::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSIMServer, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSIMServerPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSIMServerPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSIMServer::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSTStrayDevice.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSTStrayDevice.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSTStrayDevice_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSTStrayDevice",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSTStrayDevice_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getAllStations",
    "getDataBySensor",
    "getDataBySensors",
    "getSensorByStation",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resetCalculation",
    "resumeDebug",
    "sendYs",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGSTStrayDevice_getAllStations_name = "getAllStations";
const ::std::string iceC_ZG6000_ZGSTStrayDevice_getSensorByStation_name = "getSensorByStation";
const ::std::string iceC_ZG6000_ZGSTStrayDevice_getDataBySensor_name = "getDataBySensor";
const ::std::string iceC_ZG6000_ZGSTStrayDevice_getDataBySensors_name = "getDataBySensors";
const ::std::string iceC_ZG6000_ZGSTStrayDevice_sendYs_name = "sendYs";
const ::std::string iceC_ZG6000_ZGSTStrayDevice_resetCalculation_name = "resetCalculation";

}

bool
ZG6000::ZGSTStrayDevice::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSTStrayDevice_ids, iceC_ZG6000_ZGSTStrayDevice_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSTStrayDevice::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSTStrayDevice_ids[0], &iceC_ZG6000_ZGSTStrayDevice_ids[3]);
}

::std::string
ZG6000::ZGSTStrayDevice::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSTStrayDevice::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSTStrayDevice";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_getAllStations(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listMapStation;
    ErrorInfo iceP_e;
    bool ret = this->getAllStations(iceP_listMapStation, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listMapStation, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_getSensorByStation(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_stationID;
    istr->readAll(iceP_stationID);
    inS.endReadParams();
    ListStringMap iceP_listMapSensor;
    ErrorInfo iceP_e;
    bool ret = this->getSensorByStation(::std::move(iceP_stationID), iceP_listMapSensor, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listMapSensor, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_getDataBySensor(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_sensorID;
    istr->readAll(iceP_sensorID);
    inS.endReadParams();
    ListStringMap iceP_listData;
    ErrorInfo iceP_e;
    bool ret = this->getDataBySensor(::std::move(iceP_sensorID), iceP_listData, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listData, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_getDataBySensors(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listSensor;
    istr->readAll(iceP_listSensor);
    inS.endReadParams();
    ListStringMap iceP_listData;
    ErrorInfo iceP_e;
    bool ret = this->getDataBySensors(::std::move(iceP_listSensor), iceP_listData, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listData, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_sendYs(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    ::std::string iceP_propertyValue;
    bool iceP_automatic;
    istr->readAll(iceP_clientID, iceP_deviceID, iceP_propertyName, iceP_propertyValue, iceP_automatic);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->sendYs(::std::move(iceP_clientID), ::std::move(iceP_deviceID), ::std::move(iceP_propertyName), ::std::move(iceP_propertyValue), iceP_automatic, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_resetCalculation(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    this->resetCalculation(current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSTStrayDevice_ops, iceC_ZG6000_ZGSTStrayDevice_ops + 21, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSTStrayDevice_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getAllStations(in, current);
        }
        case 4:
        {
            return _iceD_getDataBySensor(in, current);
        }
        case 5:
        {
            return _iceD_getDataBySensors(in, current);
        }
        case 6:
        {
            return _iceD_getSensorByStation(in, current);
        }
        case 7:
        {
            return _iceD_getVersion(in, current);
        }
        case 8:
        {
            return _iceD_heartDebug(in, current);
        }
        case 9:
        {
            return _iceD_ice_id(in, current);
        }
        case 10:
        {
            return _iceD_ice_ids(in, current);
        }
        case 11:
        {
            return _iceD_ice_isA(in, current);
        }
        case 12:
        {
            return _iceD_ice_ping(in, current);
        }
        case 13:
        {
            return _iceD_isDebugging(in, current);
        }
        case 14:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 15:
        {
            return _iceD_resetCalculation(in, current);
        }
        case 16:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 17:
        {
            return _iceD_sendYs(in, current);
        }
        case 18:
        {
            return _iceD_startDebug(in, current);
        }
        case 19:
        {
            return _iceD_stopDebug(in, current);
        }
        case 20:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStrayDevicePrx::_iceI_getAllStations(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStrayDevice::GetAllStationsResult>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStrayDevice_getAllStations_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStrayDevice_getAllStations_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStrayDevice::GetAllStationsResult v;
            istr->readAll(v.listMapStation, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStrayDevicePrx::_iceI_getSensorByStation(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStrayDevice::GetSensorByStationResult>>& outAsync, const ::std::string& iceP_stationID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStrayDevice_getSensorByStation_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStrayDevice_getSensorByStation_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_stationID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStrayDevice::GetSensorByStationResult v;
            istr->readAll(v.listMapSensor, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStrayDevicePrx::_iceI_getDataBySensor(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStrayDevice::GetDataBySensorResult>>& outAsync, const ::std::string& iceP_sensorID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStrayDevice_getDataBySensor_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStrayDevice_getDataBySensor_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_sensorID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStrayDevice::GetDataBySensorResult v;
            istr->readAll(v.listData, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStrayDevicePrx::_iceI_getDataBySensors(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStrayDevice::GetDataBySensorsResult>>& outAsync, const StringList& iceP_listSensor, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStrayDevice_getDataBySensors_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStrayDevice_getDataBySensors_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listSensor);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStrayDevice::GetDataBySensorsResult v;
            istr->readAll(v.listData, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStrayDevicePrx::_iceI_sendYs(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStrayDevice::SendYsResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::std::string& iceP_propertyValue, bool iceP_automatic, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStrayDevice_sendYs_name);
    outAsync->invoke(iceC_ZG6000_ZGSTStrayDevice_sendYs_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_deviceID, iceP_propertyName, iceP_propertyValue, iceP_automatic);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSTStrayDevice::SendYsResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSTStrayDevicePrx::_iceI_resetCalculation(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSTStrayDevice_resetCalculation_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSTStrayDevicePrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSTStrayDevicePrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSTStrayDevicePrx::ice_staticId()
{
    return ZGSTStrayDevice::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSTStrayDevice_getAllStations_name = "getAllStations";

const ::std::string iceC_ZG6000_ZGSTStrayDevice_getSensorByStation_name = "getSensorByStation";

const ::std::string iceC_ZG6000_ZGSTStrayDevice_getDataBySensor_name = "getDataBySensor";

const ::std::string iceC_ZG6000_ZGSTStrayDevice_getDataBySensors_name = "getDataBySensors";

const ::std::string iceC_ZG6000_ZGSTStrayDevice_sendYs_name = "sendYs";

const ::std::string iceC_ZG6000_ZGSTStrayDevice_resetCalculation_name = "resetCalculation";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSTStrayDevice* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSTStrayDevice>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSTStrayDevice;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStrayDevice::_iceI_begin_getAllStations(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStrayDevice_getAllStations_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStrayDevice_getAllStations_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStrayDevice_getAllStations_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSTStrayDevice_getAllStations_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStrayDevice::end_getAllStations(::ZG6000::ListStringMap& iceP_listMapStation, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStrayDevice_getAllStations_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listMapStation);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStrayDevice::_iceI_end_getAllStations(::ZG6000::ListStringMap& iceP_listMapStation, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStrayDevice_getAllStations_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listMapStation);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStrayDevice::_iceI_begin_getSensorByStation(const ::std::string& iceP_stationID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStrayDevice_getSensorByStation_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStrayDevice_getSensorByStation_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStrayDevice_getSensorByStation_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_stationID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSTStrayDevice_getSensorByStation_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStrayDevice::end_getSensorByStation(::ZG6000::ListStringMap& iceP_listMapSensor, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStrayDevice_getSensorByStation_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listMapSensor);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStrayDevice::_iceI_end_getSensorByStation(::ZG6000::ListStringMap& iceP_listMapSensor, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStrayDevice_getSensorByStation_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listMapSensor);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStrayDevice::_iceI_begin_getDataBySensor(const ::std::string& iceP_sensorID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStrayDevice_getDataBySensor_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStrayDevice_getDataBySensor_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStrayDevice_getDataBySensor_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_sensorID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSTStrayDevice_getDataBySensor_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStrayDevice::end_getDataBySensor(::ZG6000::ListStringMap& iceP_listData, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStrayDevice_getDataBySensor_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStrayDevice::_iceI_end_getDataBySensor(::ZG6000::ListStringMap& iceP_listData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStrayDevice_getDataBySensor_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStrayDevice::_iceI_begin_getDataBySensors(const ::ZG6000::StringList& iceP_listSensor, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStrayDevice_getDataBySensors_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStrayDevice_getDataBySensors_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStrayDevice_getDataBySensors_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listSensor);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSTStrayDevice_getDataBySensors_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStrayDevice::end_getDataBySensors(::ZG6000::ListStringMap& iceP_listData, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStrayDevice_getDataBySensors_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStrayDevice::_iceI_end_getDataBySensors(::ZG6000::ListStringMap& iceP_listData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStrayDevice_getDataBySensors_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStrayDevice::_iceI_begin_sendYs(const ::std::string& iceP_clientID, const ::std::string& iceP_deviceID, const ::std::string& iceP_propertyName, const ::std::string& iceP_propertyValue, bool iceP_automatic, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSTStrayDevice_sendYs_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStrayDevice_sendYs_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStrayDevice_sendYs_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_propertyName);
        ostr->write(iceP_propertyValue);
        ostr->write(iceP_automatic);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSTStrayDevice_sendYs_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSTStrayDevice::end_sendYs(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStrayDevice_sendYs_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSTStrayDevice::_iceI_end_sendYs(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSTStrayDevice_sendYs_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSTStrayDevice::_iceI_begin_resetCalculation(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSTStrayDevice_resetCalculation_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSTStrayDevice_resetCalculation_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGSTStrayDevice_resetCalculation_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSTStrayDevice::end_resetCalculation(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSTStrayDevice_resetCalculation_name);
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSTStrayDevice::_newInstance() const
{
    return new ZGSTStrayDevice;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSTStrayDevice::ice_staticId()
{
    return ::ZG6000::ZGSTStrayDevice::ice_staticId();
}

ZG6000::ZGSTStrayDevice::~ZGSTStrayDevice()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSTStrayDevice* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSTStrayDevice_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSTStrayDevice",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSTStrayDevice::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSTStrayDevice_ids, iceC_ZG6000_ZGSTStrayDevice_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSTStrayDevice::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSTStrayDevice_ids[0], &iceC_ZG6000_ZGSTStrayDevice_ids[3]);
}

const ::std::string&
ZG6000::ZGSTStrayDevice::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSTStrayDevice::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSTStrayDevice";
    return typeId;
#else
    return iceC_ZG6000_ZGSTStrayDevice_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_getAllStations(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ListStringMap iceP_listMapStation;
    ErrorInfo iceP_e;
    bool ret = this->getAllStations(iceP_listMapStation, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listMapStation);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_getSensorByStation(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_stationID;
    istr->read(iceP_stationID);
    inS.endReadParams();
    ListStringMap iceP_listMapSensor;
    ErrorInfo iceP_e;
    bool ret = this->getSensorByStation(iceP_stationID, iceP_listMapSensor, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listMapSensor);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_getDataBySensor(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_sensorID;
    istr->read(iceP_sensorID);
    inS.endReadParams();
    ListStringMap iceP_listData;
    ErrorInfo iceP_e;
    bool ret = this->getDataBySensor(iceP_sensorID, iceP_listData, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listData);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_getDataBySensors(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listSensor;
    istr->read(iceP_listSensor);
    inS.endReadParams();
    ListStringMap iceP_listData;
    ErrorInfo iceP_e;
    bool ret = this->getDataBySensors(iceP_listSensor, iceP_listData, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listData);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_sendYs(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_deviceID;
    ::std::string iceP_propertyName;
    ::std::string iceP_propertyValue;
    bool iceP_automatic;
    istr->read(iceP_clientID);
    istr->read(iceP_deviceID);
    istr->read(iceP_propertyName);
    istr->read(iceP_propertyValue);
    istr->read(iceP_automatic);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->sendYs(iceP_clientID, iceP_deviceID, iceP_propertyName, iceP_propertyValue, iceP_automatic, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceD_resetCalculation(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    this->resetCalculation(current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSTStrayDevice_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getAllStations",
    "getDataBySensor",
    "getDataBySensors",
    "getSensorByStation",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resetCalculation",
    "resumeDebug",
    "sendYs",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSTStrayDevice::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSTStrayDevice_all, iceC_ZG6000_ZGSTStrayDevice_all + 21, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSTStrayDevice_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getAllStations(in, current);
        }
        case 4:
        {
            return _iceD_getDataBySensor(in, current);
        }
        case 5:
        {
            return _iceD_getDataBySensors(in, current);
        }
        case 6:
        {
            return _iceD_getSensorByStation(in, current);
        }
        case 7:
        {
            return _iceD_getVersion(in, current);
        }
        case 8:
        {
            return _iceD_heartDebug(in, current);
        }
        case 9:
        {
            return _iceD_ice_id(in, current);
        }
        case 10:
        {
            return _iceD_ice_ids(in, current);
        }
        case 11:
        {
            return _iceD_ice_isA(in, current);
        }
        case 12:
        {
            return _iceD_ice_ping(in, current);
        }
        case 13:
        {
            return _iceD_isDebugging(in, current);
        }
        case 14:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 15:
        {
            return _iceD_resetCalculation(in, current);
        }
        case 16:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 17:
        {
            return _iceD_sendYs(in, current);
        }
        case 18:
        {
            return _iceD_startDebug(in, current);
        }
        case 19:
        {
            return _iceD_stopDebug(in, current);
        }
        case 20:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSTStrayDevice::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSTStrayDevice, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSTStrayDevice::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSTStrayDevice, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSTStrayDevicePtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSTStrayDevicePtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSTStrayDevice::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGServerBase.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGServerBase.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGServerBase_ids[2] =
{
    "::Ice::Object",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGServerBase_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGServerBase_checkState_name = "checkState";
const ::std::string iceC_ZG6000_ZGServerBase_test_name = "test";
const ::std::string iceC_ZG6000_ZGServerBase_exitApp_name = "exitApp";
const ::std::string iceC_ZG6000_ZGServerBase_getVersion_name = "getVersion";
const ::std::string iceC_ZG6000_ZGServerBase_isDebugging_name = "isDebugging";
const ::std::string iceC_ZG6000_ZGServerBase_startDebug_name = "startDebug";
const ::std::string iceC_ZG6000_ZGServerBase_stopDebug_name = "stopDebug";
const ::std::string iceC_ZG6000_ZGServerBase_pauseDebug_name = "pauseDebug";
const ::std::string iceC_ZG6000_ZGServerBase_resumeDebug_name = "resumeDebug";
const ::std::string iceC_ZG6000_ZGServerBase_heartDebug_name = "heartDebug";
const ::std::string iceC_ZG6000_ZGServerBase_dispatchData_name = "dispatchData";

}

bool
ZG6000::ZGServerBase::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGServerBase_ids, iceC_ZG6000_ZGServerBase_ids + 2, s);
}

::std::vector<::std::string>
ZG6000::ZGServerBase::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGServerBase_ids[0], &iceC_ZG6000_ZGServerBase_ids[2]);
}

::std::string
ZG6000::ZGServerBase::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGServerBase::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGServerBase";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_checkState(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->checkState(current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_test(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->test(current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_exitApp(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    this->exitApp(current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_getVersion(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    ::std::string ret = this->getVersion(current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_isDebugging(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->isDebugging(current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_startDebug(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_lstDebugLevel;
    istr->readAll(iceP_lstDebugLevel);
    inS.endReadParams();
    bool ret = this->startDebug(::std::move(iceP_lstDebugLevel), current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_stopDebug(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->stopDebug(current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_pauseDebug(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->pauseDebug(current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_resumeDebug(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->resumeDebug(current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_heartDebug(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->heartDebug(current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_dispatchData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_oper;
    ::std::string iceP_reason;
    ::std::string iceP_time;
    ListRecord iceP_listRecord;
    istr->readAll(iceP_tableName, iceP_oper, iceP_reason, iceP_time, iceP_listRecord);
    inS.endReadParams();
    this->dispatchData(::std::move(iceP_tableName), ::std::move(iceP_oper), ::std::move(iceP_reason), ::std::move(iceP_time), ::std::move(iceP_listRecord), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGServerBase_ops, iceC_ZG6000_ZGServerBase_ops + 15, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGServerBase_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getVersion(in, current);
        }
        case 4:
        {
            return _iceD_heartDebug(in, current);
        }
        case 5:
        {
            return _iceD_ice_id(in, current);
        }
        case 6:
        {
            return _iceD_ice_ids(in, current);
        }
        case 7:
        {
            return _iceD_ice_isA(in, current);
        }
        case 8:
        {
            return _iceD_ice_ping(in, current);
        }
        case 9:
        {
            return _iceD_isDebugging(in, current);
        }
        case 10:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 11:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 12:
        {
            return _iceD_startDebug(in, current);
        }
        case 13:
        {
            return _iceD_stopDebug(in, current);
        }
        case 14:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_checkState(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_checkState_name);
    outAsync->invoke(iceC_ZG6000_ZGServerBase_checkState_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_test(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_test_name);
    outAsync->invoke(iceC_ZG6000_ZGServerBase_test_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_exitApp(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGServerBase_exitApp_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_getVersion(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<::std::string>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_getVersion_name);
    outAsync->invoke(iceC_ZG6000_ZGServerBase_getVersion_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_isDebugging(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_isDebugging_name);
    outAsync->invoke(iceC_ZG6000_ZGServerBase_isDebugging_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_startDebug(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>& outAsync, const StringList& iceP_lstDebugLevel, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_startDebug_name);
    outAsync->invoke(iceC_ZG6000_ZGServerBase_startDebug_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_lstDebugLevel);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_stopDebug(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_stopDebug_name);
    outAsync->invoke(iceC_ZG6000_ZGServerBase_stopDebug_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_pauseDebug(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_pauseDebug_name);
    outAsync->invoke(iceC_ZG6000_ZGServerBase_pauseDebug_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_resumeDebug(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_resumeDebug_name);
    outAsync->invoke(iceC_ZG6000_ZGServerBase_resumeDebug_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_heartDebug(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>& outAsync, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_heartDebug_name);
    outAsync->invoke(iceC_ZG6000_ZGServerBase_heartDebug_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        nullptr,
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGServerBasePrx::_iceI_dispatchData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_oper, const ::std::string& iceP_reason, const ::std::string& iceP_time, const ListRecord& iceP_listRecord, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGServerBase_dispatchData_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_oper, iceP_reason, iceP_time, iceP_listRecord);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGServerBasePrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGServerBasePrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGServerBasePrx::ice_staticId()
{
    return ZGServerBase::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGServerBase_checkState_name = "checkState";

const ::std::string iceC_ZG6000_ZGServerBase_test_name = "test";

const ::std::string iceC_ZG6000_ZGServerBase_exitApp_name = "exitApp";

const ::std::string iceC_ZG6000_ZGServerBase_getVersion_name = "getVersion";

const ::std::string iceC_ZG6000_ZGServerBase_isDebugging_name = "isDebugging";

const ::std::string iceC_ZG6000_ZGServerBase_startDebug_name = "startDebug";

const ::std::string iceC_ZG6000_ZGServerBase_stopDebug_name = "stopDebug";

const ::std::string iceC_ZG6000_ZGServerBase_pauseDebug_name = "pauseDebug";

const ::std::string iceC_ZG6000_ZGServerBase_resumeDebug_name = "resumeDebug";

const ::std::string iceC_ZG6000_ZGServerBase_heartDebug_name = "heartDebug";

const ::std::string iceC_ZG6000_ZGServerBase_dispatchData_name = "dispatchData";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGServerBase* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGServerBase>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGServerBase;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_checkState(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_checkState_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_checkState_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_checkState_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGServerBase_checkState_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGServerBase::end_checkState(const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGServerBase_checkState_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_test(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_test_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_test_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_test_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGServerBase_test_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGServerBase::end_test(const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGServerBase_test_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_exitApp(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_exitApp_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_exitApp_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGServerBase_exitApp_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGServerBase::end_exitApp(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGServerBase_exitApp_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_getVersion(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_getVersion_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_getVersion_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_getVersion_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGServerBase_getVersion_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

::std::string
IceProxy::ZG6000::ZGServerBase::end_getVersion(const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGServerBase_getVersion_name);
    ::std::string ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_isDebugging(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_isDebugging_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_isDebugging_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_isDebugging_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGServerBase_isDebugging_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGServerBase::end_isDebugging(const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGServerBase_isDebugging_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_startDebug(const ::ZG6000::StringList& iceP_lstDebugLevel, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_startDebug_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_startDebug_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_startDebug_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_lstDebugLevel);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGServerBase_startDebug_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGServerBase::end_startDebug(const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGServerBase_startDebug_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_stopDebug(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_stopDebug_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_stopDebug_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_stopDebug_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGServerBase_stopDebug_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGServerBase::end_stopDebug(const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGServerBase_stopDebug_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_pauseDebug(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_pauseDebug_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_pauseDebug_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_pauseDebug_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGServerBase_pauseDebug_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGServerBase::end_pauseDebug(const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGServerBase_pauseDebug_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_resumeDebug(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_resumeDebug_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_resumeDebug_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_resumeDebug_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGServerBase_resumeDebug_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGServerBase::end_resumeDebug(const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGServerBase_resumeDebug_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_heartDebug(const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGServerBase_heartDebug_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_heartDebug_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_heartDebug_name, ::Ice::Normal, context);
        result->writeEmptyParams();
        result->invoke(iceC_ZG6000_ZGServerBase_heartDebug_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGServerBase::end_heartDebug(const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGServerBase_heartDebug_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGServerBase::_iceI_begin_dispatchData(const ::std::string& iceP_tableName, const ::std::string& iceP_oper, const ::std::string& iceP_reason, const ::std::string& iceP_time, const ::ZG6000::ListRecord& iceP_listRecord, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGServerBase_dispatchData_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGServerBase_dispatchData_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_oper);
        ostr->write(iceP_reason);
        ostr->write(iceP_time);
        ostr->write(iceP_listRecord);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGServerBase_dispatchData_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGServerBase::end_dispatchData(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGServerBase_dispatchData_name);
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGServerBase::_newInstance() const
{
    return new ZGServerBase;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGServerBase::ice_staticId()
{
    return ::ZG6000::ZGServerBase::ice_staticId();
}

ZG6000::ZGServerBase::~ZGServerBase()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGServerBase* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGServerBase_ids[2] =
{
    "::Ice::Object",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGServerBase::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGServerBase_ids, iceC_ZG6000_ZGServerBase_ids + 2, s);
}

::std::vector< ::std::string>
ZG6000::ZGServerBase::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGServerBase_ids[0], &iceC_ZG6000_ZGServerBase_ids[2]);
}

const ::std::string&
ZG6000::ZGServerBase::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGServerBase::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGServerBase";
    return typeId;
#else
    return iceC_ZG6000_ZGServerBase_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_checkState(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->checkState(current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_test(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->test(current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_exitApp(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    this->exitApp(current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_getVersion(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    ::std::string ret = this->getVersion(current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_isDebugging(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->isDebugging(current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_startDebug(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_lstDebugLevel;
    istr->read(iceP_lstDebugLevel);
    inS.endReadParams();
    bool ret = this->startDebug(iceP_lstDebugLevel, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_stopDebug(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->stopDebug(current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_pauseDebug(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->pauseDebug(current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_resumeDebug(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->resumeDebug(current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_heartDebug(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    inS.readEmptyParams();
    bool ret = this->heartDebug(current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceD_dispatchData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_oper;
    ::std::string iceP_reason;
    ::std::string iceP_time;
    ListRecord iceP_listRecord;
    istr->read(iceP_tableName);
    istr->read(iceP_oper);
    istr->read(iceP_reason);
    istr->read(iceP_time);
    istr->read(iceP_listRecord);
    inS.endReadParams();
    this->dispatchData(iceP_tableName, iceP_oper, iceP_reason, iceP_time, iceP_listRecord, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGServerBase_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGServerBase::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGServerBase_all, iceC_ZG6000_ZGServerBase_all + 15, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGServerBase_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getVersion(in, current);
        }
        case 4:
        {
            return _iceD_heartDebug(in, current);
        }
        case 5:
        {
            return _iceD_ice_id(in, current);
        }
        case 6:
        {
            return _iceD_ice_ids(in, current);
        }
        case 7:
        {
            return _iceD_ice_isA(in, current);
        }
        case 8:
        {
            return _iceD_ice_ping(in, current);
        }
        case 9:
        {
            return _iceD_isDebugging(in, current);
        }
        case 10:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 11:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 12:
        {
            return _iceD_startDebug(in, current);
        }
        case 13:
        {
            return _iceD_stopDebug(in, current);
        }
        case 14:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGServerBase::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGServerBase, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGServerBase::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGServerBase, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGServerBasePtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGServerBasePtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGServerBase::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

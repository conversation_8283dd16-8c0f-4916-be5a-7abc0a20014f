//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPEventParse.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPEventParse_h__
#define __ZGSPEventParse_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPEventParse;
class ZGSPEventParsePrx;

}

namespace ZG6000
{

class ZGSPEventParse : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPEventParsePrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPEventParsePrx : public virtual ::Ice::Proxy<ZGSPEventParsePrx, ZGServerBasePrx>
{
public:

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPEventParsePrx() = default;
    friend ::std::shared_ptr<ZGSPEventParsePrx> IceInternal::createProxy<ZGSPEventParsePrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPEventParsePtr = ::std::shared_ptr<ZGSPEventParse>;
using ZGSPEventParsePrxPtr = ::std::shared_ptr<ZGSPEventParsePrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPEventParse;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPEventParse>&);
::IceProxy::Ice::Object* upCast(ZGSPEventParse*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPEventParse;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPEventParse*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPEventParse> ZGSPEventParsePtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPEventParse> ZGSPEventParsePrx;
typedef ZGSPEventParsePrx ZGSPEventParsePrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPEventParsePtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPEventParse : public virtual ::Ice::Proxy<ZGSPEventParse, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPEventParse : virtual public ZGServerBase
{
public:

    typedef ZGSPEventParsePrx ProxyType;
    typedef ZGSPEventParsePtr PointerType;

    virtual ~ZGSPEventParse();

#ifdef ICE_CPP11_COMPILER
    ZGSPEventParse() = default;
    ZGSPEventParse(const ZGSPEventParse&) = default;
    ZGSPEventParse& operator=(const ZGSPEventParse&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPEventParse& lhs, const ZGSPEventParse& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPEventParse& lhs, const ZGSPEventParse& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPDatasetProperty.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGMPDatasetProperty_h__
#define __ZGMPDatasetProperty_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGMPDatasetProperty;
class ZGMPDatasetPropertyPrx;

}

namespace ZG6000
{

class ZGMPDatasetProperty : public virtual ZGServerBase
{
public:

    using ProxyType = ZGMPDatasetPropertyPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getPropertiesAll.
     */
    struct GetPropertiesAllResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        MapStringMap properties;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集所有属性
     */
    virtual bool getPropertiesAll(::std::string datasetID, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertiesAll(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getProperties.
     */
    struct GetPropertiesResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        MapStringMap properties;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称列表的属性
     */
    virtual bool getProperties(::std::string datasetID, StringList listName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getProperty.
     */
    struct GetPropertyResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        StringMap property;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称的属性
     */
    virtual bool getProperty(::std::string datasetID, ::std::string name, StringMap& property, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertyValues.
     */
    struct GetPropertyValuesResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        StringMap values;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称列表的属性值
     */
    virtual bool getPropertyValues(::std::string datasetID, StringList listName, StringMap& values, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertyValue.
     */
    struct GetPropertyValueResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ::std::string value;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称的属性值
     */
    virtual bool getPropertyValue(::std::string datasetID, ::std::string name, ::std::string& value, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateProperties.
     */
    struct UpdatePropertiesResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性集合
     */
    virtual bool updateProperties(::std::string datasetID, MapStringMap properties, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateProperty.
     */
    struct UpdatePropertyResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性
     */
    virtual bool updateProperty(::std::string datasetID, ::std::string name, StringMap property, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updatePropertyValues.
     */
    struct UpdatePropertyValuesResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性值集合
     */
    virtual bool updatePropertyValues(::std::string datasetID, StringMap values, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updatePropertyValue.
     */
    struct UpdatePropertyValueResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性值
     */
    virtual bool updatePropertyValue(::std::string datasetID, ::std::string name, ::std::string value, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDataIDByProperty.
     */
    struct GetDataIDByPropertyResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ::std::string tableName;
        ::std::string dataID;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   根据数据集属性名获取数据ID
     */
    virtual bool getDataIDByProperty(::std::string datasetID, ::std::string name, ::std::string& tableName, ::std::string& dataID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDataIDByProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getPropertyByDataID.
     */
    struct GetPropertyByDataIDResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ::std::string datasetID;
        ::std::string name;
        ErrorInfo e;
    };

    /**
     * @param dataID 数据ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   根据数据ID获取数据集属性名
     */
    virtual bool getPropertyByDataID(::std::string dataID, ::std::string& datasetID, ::std::string& name, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyByDataID(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to isPropertyExists.
     */
    struct IsPropertyExistsResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        bool exists;
        ErrorInfo e;
    };

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   查询指定的数据集属性是否存在
     */
    virtual bool isPropertyExists(::std::string datasetID, ::std::string name, bool& exists, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_isPropertyExists(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGMPDatasetPropertyPrx : public virtual ::Ice::Proxy<ZGMPDatasetPropertyPrx, ZGServerBasePrx>
{
public:

    /**
     * @param datasetID 数据集ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集所有属性
     */
    bool getPropertiesAll(const ::std::string& datasetID, MapStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertiesAllResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_getPropertiesAll, datasetID, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   获取数据集所有属性
     */
    template<template<typename> class P = ::std::promise>
    auto getPropertiesAllAsync(const ::std::string& datasetID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::GetPropertiesAllResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertiesAllResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_getPropertiesAll, datasetID, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   获取数据集所有属性
     */
    ::std::function<void()>
    getPropertiesAllAsync(const ::std::string& datasetID,
                          ::std::function<void(bool, ::ZG6000::MapStringMap, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::GetPropertiesAllResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::GetPropertiesAllResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_getPropertiesAll, datasetID, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertiesAll(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertiesAllResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称列表的属性
     */
    bool getProperties(const ::std::string& datasetID, const StringList& listName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertiesResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_getProperties, datasetID, listName, context).get();
        properties = ::std::move(_result.properties);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   获取数据集指定名称列表的属性
     */
    template<template<typename> class P = ::std::promise>
    auto getPropertiesAsync(const ::std::string& datasetID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::GetPropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertiesResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_getProperties, datasetID, listName, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   获取数据集指定名称列表的属性
     */
    ::std::function<void()>
    getPropertiesAsync(const ::std::string& datasetID, const StringList& listName,
                       ::std::function<void(bool, ::ZG6000::MapStringMap, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::GetPropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.properties), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::GetPropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_getProperties, datasetID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_getProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertiesResult>>&, const ::std::string&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称的属性
     */
    bool getProperty(const ::std::string& datasetID, const ::std::string& name, StringMap& property, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertyResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_getProperty, datasetID, name, context).get();
        property = ::std::move(_result.property);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   获取数据集指定名称的属性
     */
    template<template<typename> class P = ::std::promise>
    auto getPropertyAsync(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::GetPropertyResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertyResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_getProperty, datasetID, name, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   获取数据集指定名称的属性
     */
    ::std::function<void()>
    getPropertyAsync(const ::std::string& datasetID, const ::std::string& name,
                     ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::GetPropertyResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.property), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::GetPropertyResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_getProperty, datasetID, name, context);
    }

    /// \cond INTERNAL
    void _iceI_getProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertyResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称列表的属性值
     */
    bool getPropertyValues(const ::std::string& datasetID, const StringList& listName, StringMap& values, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertyValuesResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_getPropertyValues, datasetID, listName, context).get();
        values = ::std::move(_result.values);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   获取数据集指定名称列表的属性值
     */
    template<template<typename> class P = ::std::promise>
    auto getPropertyValuesAsync(const ::std::string& datasetID, const StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::GetPropertyValuesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertyValuesResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_getPropertyValues, datasetID, listName, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   获取数据集指定名称列表的属性值
     */
    ::std::function<void()>
    getPropertyValuesAsync(const ::std::string& datasetID, const StringList& listName,
                           ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::GetPropertyValuesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.values), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::GetPropertyValuesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_getPropertyValues, datasetID, listName, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertyValuesResult>>&, const ::std::string&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称的属性值
     */
    bool getPropertyValue(const ::std::string& datasetID, const ::std::string& name, ::std::string& value, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertyValueResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_getPropertyValue, datasetID, name, context).get();
        value = ::std::move(_result.value);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   获取数据集指定名称的属性值
     */
    template<template<typename> class P = ::std::promise>
    auto getPropertyValueAsync(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::GetPropertyValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertyValueResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_getPropertyValue, datasetID, name, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   获取数据集指定名称的属性值
     */
    ::std::function<void()>
    getPropertyValueAsync(const ::std::string& datasetID, const ::std::string& name,
                          ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::GetPropertyValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.value), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::GetPropertyValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_getPropertyValue, datasetID, name, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertyValueResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性集合
     */
    bool updateProperties(const ::std::string& datasetID, const MapStringMap& properties, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::UpdatePropertiesResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_updateProperties, datasetID, properties, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   更新数据集指定的属性集合
     */
    template<template<typename> class P = ::std::promise>
    auto updatePropertiesAsync(const ::std::string& datasetID, const MapStringMap& properties, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::UpdatePropertiesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::UpdatePropertiesResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_updateProperties, datasetID, properties, saveToDB, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   更新数据集指定的属性集合
     */
    ::std::function<void()>
    updatePropertiesAsync(const ::std::string& datasetID, const MapStringMap& properties, bool saveToDB,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::UpdatePropertiesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::UpdatePropertiesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_updateProperties, datasetID, properties, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_updateProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::UpdatePropertiesResult>>&, const ::std::string&, const MapStringMap&, bool, const ::Ice::Context&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性
     */
    bool updateProperty(const ::std::string& datasetID, const ::std::string& name, const StringMap& property, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::UpdatePropertyResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_updateProperty, datasetID, name, property, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   更新数据集指定的属性
     */
    template<template<typename> class P = ::std::promise>
    auto updatePropertyAsync(const ::std::string& datasetID, const ::std::string& name, const StringMap& property, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::UpdatePropertyResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::UpdatePropertyResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_updateProperty, datasetID, name, property, saveToDB, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   更新数据集指定的属性
     */
    ::std::function<void()>
    updatePropertyAsync(const ::std::string& datasetID, const ::std::string& name, const StringMap& property, bool saveToDB,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::UpdatePropertyResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::UpdatePropertyResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_updateProperty, datasetID, name, property, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_updateProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::UpdatePropertyResult>>&, const ::std::string&, const ::std::string&, const StringMap&, bool, const ::Ice::Context&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性值集合
     */
    bool updatePropertyValues(const ::std::string& datasetID, const StringMap& values, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::UpdatePropertyValuesResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_updatePropertyValues, datasetID, values, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   更新数据集指定的属性值集合
     */
    template<template<typename> class P = ::std::promise>
    auto updatePropertyValuesAsync(const ::std::string& datasetID, const StringMap& values, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::UpdatePropertyValuesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::UpdatePropertyValuesResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_updatePropertyValues, datasetID, values, saveToDB, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   更新数据集指定的属性值集合
     */
    ::std::function<void()>
    updatePropertyValuesAsync(const ::std::string& datasetID, const StringMap& values, bool saveToDB,
                              ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                              ::std::function<void(::std::exception_ptr)> ex = nullptr,
                              ::std::function<void(bool)> sent = nullptr,
                              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::UpdatePropertyValuesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::UpdatePropertyValuesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_updatePropertyValues, datasetID, values, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_updatePropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::UpdatePropertyValuesResult>>&, const ::std::string&, const StringMap&, bool, const ::Ice::Context&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性值
     */
    bool updatePropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::std::string& value, bool saveToDB, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::UpdatePropertyValueResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_updatePropertyValue, datasetID, name, value, saveToDB, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   更新数据集指定的属性值
     */
    template<template<typename> class P = ::std::promise>
    auto updatePropertyValueAsync(const ::std::string& datasetID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::UpdatePropertyValueResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::UpdatePropertyValueResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_updatePropertyValue, datasetID, name, value, saveToDB, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   更新数据集指定的属性值
     */
    ::std::function<void()>
    updatePropertyValueAsync(const ::std::string& datasetID, const ::std::string& name, const ::std::string& value, bool saveToDB,
                             ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::UpdatePropertyValueResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::UpdatePropertyValueResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_updatePropertyValue, datasetID, name, value, saveToDB, context);
    }

    /// \cond INTERNAL
    void _iceI_updatePropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::UpdatePropertyValueResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, bool, const ::Ice::Context&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   根据数据集属性名获取数据ID
     */
    bool getDataIDByProperty(const ::std::string& datasetID, const ::std::string& name, ::std::string& tableName, ::std::string& dataID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::GetDataIDByPropertyResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_getDataIDByProperty, datasetID, name, context).get();
        tableName = ::std::move(_result.tableName);
        dataID = ::std::move(_result.dataID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   根据数据集属性名获取数据ID
     */
    template<template<typename> class P = ::std::promise>
    auto getDataIDByPropertyAsync(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::GetDataIDByPropertyResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::GetDataIDByPropertyResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_getDataIDByProperty, datasetID, name, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   根据数据集属性名获取数据ID
     */
    ::std::function<void()>
    getDataIDByPropertyAsync(const ::std::string& datasetID, const ::std::string& name,
                             ::std::function<void(bool, ::std::string, ::std::string, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::GetDataIDByPropertyResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.tableName), ::std::move(_result.dataID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::GetDataIDByPropertyResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_getDataIDByProperty, datasetID, name, context);
    }

    /// \cond INTERNAL
    void _iceI_getDataIDByProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetDataIDByPropertyResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param dataID 数据ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   根据数据ID获取数据集属性名
     */
    bool getPropertyByDataID(const ::std::string& dataID, ::std::string& datasetID, ::std::string& name, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertyByDataIDResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_getPropertyByDataID, dataID, context).get();
        datasetID = ::std::move(_result.datasetID);
        name = ::std::move(_result.name);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param dataID 数据ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   根据数据ID获取数据集属性名
     */
    template<template<typename> class P = ::std::promise>
    auto getPropertyByDataIDAsync(const ::std::string& dataID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::GetPropertyByDataIDResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::GetPropertyByDataIDResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_getPropertyByDataID, dataID, context);
    }

    /**
     * @param dataID 数据ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   根据数据ID获取数据集属性名
     */
    ::std::function<void()>
    getPropertyByDataIDAsync(const ::std::string& dataID,
                             ::std::function<void(bool, ::std::string, ::std::string, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::GetPropertyByDataIDResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.datasetID), ::std::move(_result.name), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::GetPropertyByDataIDResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_getPropertyByDataID, dataID, context);
    }

    /// \cond INTERNAL
    void _iceI_getPropertyByDataID(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertyByDataIDResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   查询指定的数据集属性是否存在
     */
    bool isPropertyExists(const ::std::string& datasetID, const ::std::string& name, bool& exists, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPDatasetProperty::IsPropertyExistsResult>(true, this, &ZGMPDatasetPropertyPrx::_iceI_isPropertyExists, datasetID, name, context).get();
        exists = _result.exists;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   查询指定的数据集属性是否存在
     */
    template<template<typename> class P = ::std::promise>
    auto isPropertyExistsAsync(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPDatasetProperty::IsPropertyExistsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPDatasetProperty::IsPropertyExistsResult, P>(false, this, &ZGMPDatasetPropertyPrx::_iceI_isPropertyExists, datasetID, name, context);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   查询指定的数据集属性是否存在
     */
    ::std::function<void()>
    isPropertyExistsAsync(const ::std::string& datasetID, const ::std::string& name,
                          ::std::function<void(bool, bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPDatasetProperty::IsPropertyExistsResult&& _result)
        {
            response(_result.returnValue, _result.exists, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPDatasetProperty::IsPropertyExistsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPDatasetPropertyPrx::_iceI_isPropertyExists, datasetID, name, context);
    }

    /// \cond INTERNAL
    void _iceI_isPropertyExists(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::IsPropertyExistsResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGMPDatasetPropertyPrx() = default;
    friend ::std::shared_ptr<ZGMPDatasetPropertyPrx> IceInternal::createProxy<ZGMPDatasetPropertyPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGMPDatasetPropertyPtr = ::std::shared_ptr<ZGMPDatasetProperty>;
using ZGMPDatasetPropertyPrxPtr = ::std::shared_ptr<ZGMPDatasetPropertyPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGMPDatasetProperty;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGMPDatasetProperty>&);
::IceProxy::Ice::Object* upCast(ZGMPDatasetProperty*);
/// \endcond

}

}

namespace ZG6000
{

class ZGMPDatasetProperty;
/// \cond INTERNAL
::Ice::Object* upCast(ZGMPDatasetProperty*);
/// \endcond
typedef ::IceInternal::Handle< ZGMPDatasetProperty> ZGMPDatasetPropertyPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGMPDatasetProperty> ZGMPDatasetPropertyPrx;
typedef ZGMPDatasetPropertyPrx ZGMPDatasetPropertyPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGMPDatasetPropertyPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertiesAll.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertiesAll.
 */
class Callback_ZGMPDatasetProperty_getPropertiesAll_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_getPropertiesAll_Base> Callback_ZGMPDatasetProperty_getPropertiesAllPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getProperties.
 */
class Callback_ZGMPDatasetProperty_getProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_getProperties_Base> Callback_ZGMPDatasetProperty_getPropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getProperty.
 */
class Callback_ZGMPDatasetProperty_getProperty_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_getProperty_Base> Callback_ZGMPDatasetProperty_getPropertyPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertyValues.
 */
class Callback_ZGMPDatasetProperty_getPropertyValues_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_getPropertyValues_Base> Callback_ZGMPDatasetProperty_getPropertyValuesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertyValue.
 */
class Callback_ZGMPDatasetProperty_getPropertyValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_getPropertyValue_Base> Callback_ZGMPDatasetProperty_getPropertyValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updateProperties.
 */
class Callback_ZGMPDatasetProperty_updateProperties_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_updateProperties_Base> Callback_ZGMPDatasetProperty_updatePropertiesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updateProperty.
 */
class Callback_ZGMPDatasetProperty_updateProperty_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_updateProperty_Base> Callback_ZGMPDatasetProperty_updatePropertyPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updatePropertyValues.
 */
class Callback_ZGMPDatasetProperty_updatePropertyValues_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_updatePropertyValues_Base> Callback_ZGMPDatasetProperty_updatePropertyValuesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updatePropertyValue.
 */
class Callback_ZGMPDatasetProperty_updatePropertyValue_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_updatePropertyValue_Base> Callback_ZGMPDatasetProperty_updatePropertyValuePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getDataIDByProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getDataIDByProperty.
 */
class Callback_ZGMPDatasetProperty_getDataIDByProperty_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_getDataIDByProperty_Base> Callback_ZGMPDatasetProperty_getDataIDByPropertyPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyByDataID.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertyByDataID.
 */
class Callback_ZGMPDatasetProperty_getPropertyByDataID_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_getPropertyByDataID_Base> Callback_ZGMPDatasetProperty_getPropertyByDataIDPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_isPropertyExists.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_isPropertyExists.
 */
class Callback_ZGMPDatasetProperty_isPropertyExists_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPDatasetProperty_isPropertyExists_Base> Callback_ZGMPDatasetProperty_isPropertyExistsPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGMPDatasetProperty : public virtual ::Ice::Proxy<ZGMPDatasetProperty, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * @param datasetID 数据集ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集所有属性
     */
    bool getPropertiesAll(const ::std::string& datasetID, ::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertiesAll(properties, e, _iceI_begin_getPropertiesAll(datasetID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集所有属性
     */
    ::Ice::AsyncResultPtr begin_getPropertiesAll(const ::std::string& datasetID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertiesAll(datasetID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集所有属性
     */
    ::Ice::AsyncResultPtr begin_getPropertiesAll(const ::std::string& datasetID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertiesAll(datasetID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集所有属性
     */
    ::Ice::AsyncResultPtr begin_getPropertiesAll(const ::std::string& datasetID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertiesAll(datasetID, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集所有属性
     */
    ::Ice::AsyncResultPtr begin_getPropertiesAll(const ::std::string& datasetID, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertiesAllPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertiesAll(datasetID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集所有属性
     */
    ::Ice::AsyncResultPtr begin_getPropertiesAll(const ::std::string& datasetID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertiesAllPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertiesAll(datasetID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getPropertiesAll.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getPropertiesAll(::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertiesAll(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertiesAll(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称列表的属性
     */
    bool getProperties(const ::std::string& datasetID, const ::ZG6000::StringList& listName, ::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getProperties(properties, e, _iceI_begin_getProperties(datasetID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称列表的属性
     */
    ::Ice::AsyncResultPtr begin_getProperties(const ::std::string& datasetID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getProperties(datasetID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称列表的属性
     */
    ::Ice::AsyncResultPtr begin_getProperties(const ::std::string& datasetID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperties(datasetID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称列表的属性
     */
    ::Ice::AsyncResultPtr begin_getProperties(const ::std::string& datasetID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperties(datasetID, listName, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称列表的属性
     */
    ::Ice::AsyncResultPtr begin_getProperties(const ::std::string& datasetID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperties(datasetID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称列表的属性
     */
    ::Ice::AsyncResultPtr begin_getProperties(const ::std::string& datasetID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperties(datasetID, listName, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getProperties.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getProperties(::ZG6000::MapStringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getProperties(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getProperties(const ::std::string&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称的属性
     */
    bool getProperty(const ::std::string& datasetID, const ::std::string& name, ::ZG6000::StringMap& property, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getProperty(property, e, _iceI_begin_getProperty(datasetID, name, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称的属性
     */
    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getProperty(datasetID, name, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称的属性
     */
    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& datasetID, const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(datasetID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称的属性
     */
    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(datasetID, name, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称的属性
     */
    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& datasetID, const ::std::string& name, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(datasetID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称的属性
     */
    ::Ice::AsyncResultPtr begin_getProperty(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getProperty(datasetID, name, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getProperty.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getProperty(::ZG6000::StringMap& property, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getProperty(::ZG6000::StringMap& iceP_property, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getProperty(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称列表的属性值
     */
    bool getPropertyValues(const ::std::string& datasetID, const ::ZG6000::StringList& listName, ::ZG6000::StringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertyValues(values, e, _iceI_begin_getPropertyValues(datasetID, listName, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称列表的属性值
     */
    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& datasetID, const ::ZG6000::StringList& listName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertyValues(datasetID, listName, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称列表的属性值
     */
    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& datasetID, const ::ZG6000::StringList& listName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(datasetID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称列表的属性值
     */
    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& datasetID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(datasetID, listName, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称列表的属性值
     */
    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& datasetID, const ::ZG6000::StringList& listName, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(datasetID, listName, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称列表的属性值
     */
    ::Ice::AsyncResultPtr begin_getPropertyValues(const ::std::string& datasetID, const ::ZG6000::StringList& listName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValues(datasetID, listName, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getPropertyValues.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getPropertyValues(::ZG6000::StringMap& values, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertyValues(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertyValues(const ::std::string&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称的属性值
     */
    bool getPropertyValue(const ::std::string& datasetID, const ::std::string& name, ::std::string& value, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertyValue(value, e, _iceI_begin_getPropertyValue(datasetID, name, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称的属性值
     */
    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertyValue(datasetID, name, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称的属性值
     */
    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(datasetID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称的属性值
     */
    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(datasetID, name, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称的属性值
     */
    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(datasetID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   获取数据集指定名称的属性值
     */
    ::Ice::AsyncResultPtr begin_getPropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyValue(datasetID, name, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getPropertyValue.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getPropertyValue(::std::string& value, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertyValue(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertyValue(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性集合
     */
    bool updateProperties(const ::std::string& datasetID, const ::ZG6000::MapStringMap& properties, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateProperties(e, _iceI_begin_updateProperties(datasetID, properties, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性集合
     */
    ::Ice::AsyncResultPtr begin_updateProperties(const ::std::string& datasetID, const ::ZG6000::MapStringMap& properties, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateProperties(datasetID, properties, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性集合
     */
    ::Ice::AsyncResultPtr begin_updateProperties(const ::std::string& datasetID, const ::ZG6000::MapStringMap& properties, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperties(datasetID, properties, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性集合
     */
    ::Ice::AsyncResultPtr begin_updateProperties(const ::std::string& datasetID, const ::ZG6000::MapStringMap& properties, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperties(datasetID, properties, saveToDB, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性集合
     */
    ::Ice::AsyncResultPtr begin_updateProperties(const ::std::string& datasetID, const ::ZG6000::MapStringMap& properties, bool saveToDB, const ::ZG6000::Callback_ZGMPDatasetProperty_updatePropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperties(datasetID, properties, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性集合
     */
    ::Ice::AsyncResultPtr begin_updateProperties(const ::std::string& datasetID, const ::ZG6000::MapStringMap& properties, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_updatePropertiesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperties(datasetID, properties, saveToDB, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_updateProperties.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_updateProperties(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateProperties(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateProperties(const ::std::string&, const ::ZG6000::MapStringMap&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性
     */
    bool updateProperty(const ::std::string& datasetID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateProperty(e, _iceI_begin_updateProperty(datasetID, name, property, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性
     */
    ::Ice::AsyncResultPtr begin_updateProperty(const ::std::string& datasetID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateProperty(datasetID, name, property, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性
     */
    ::Ice::AsyncResultPtr begin_updateProperty(const ::std::string& datasetID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperty(datasetID, name, property, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性
     */
    ::Ice::AsyncResultPtr begin_updateProperty(const ::std::string& datasetID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperty(datasetID, name, property, saveToDB, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性
     */
    ::Ice::AsyncResultPtr begin_updateProperty(const ::std::string& datasetID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, const ::ZG6000::Callback_ZGMPDatasetProperty_updatePropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperty(datasetID, name, property, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性
     */
    ::Ice::AsyncResultPtr begin_updateProperty(const ::std::string& datasetID, const ::std::string& name, const ::ZG6000::StringMap& property, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_updatePropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateProperty(datasetID, name, property, saveToDB, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_updateProperty.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_updateProperty(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateProperty(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateProperty(const ::std::string&, const ::std::string&, const ::ZG6000::StringMap&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性值集合
     */
    bool updatePropertyValues(const ::std::string& datasetID, const ::ZG6000::StringMap& values, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updatePropertyValues(e, _iceI_begin_updatePropertyValues(datasetID, values, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性值集合
     */
    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& datasetID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updatePropertyValues(datasetID, values, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性值集合
     */
    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& datasetID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(datasetID, values, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性值集合
     */
    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& datasetID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(datasetID, values, saveToDB, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性值集合
     */
    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& datasetID, const ::ZG6000::StringMap& values, bool saveToDB, const ::ZG6000::Callback_ZGMPDatasetProperty_updatePropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(datasetID, values, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性值集合
     */
    ::Ice::AsyncResultPtr begin_updatePropertyValues(const ::std::string& datasetID, const ::ZG6000::StringMap& values, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_updatePropertyValuesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValues(datasetID, values, saveToDB, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_updatePropertyValues.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_updatePropertyValues(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updatePropertyValues(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updatePropertyValues(const ::std::string&, const ::ZG6000::StringMap&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性值
     */
    bool updatePropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::std::string& value, bool saveToDB, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updatePropertyValue(e, _iceI_begin_updatePropertyValue(datasetID, name, value, saveToDB, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性值
     */
    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updatePropertyValue(datasetID, name, value, saveToDB, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性值
     */
    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(datasetID, name, value, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性值
     */
    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(datasetID, name, value, saveToDB, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性值
     */
    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::ZG6000::Callback_ZGMPDatasetProperty_updatePropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(datasetID, name, value, saveToDB, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   更新数据集指定的属性值
     */
    ::Ice::AsyncResultPtr begin_updatePropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::std::string& value, bool saveToDB, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_updatePropertyValuePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updatePropertyValue(datasetID, name, value, saveToDB, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_updatePropertyValue.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_updatePropertyValue(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updatePropertyValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updatePropertyValue(const ::std::string&, const ::std::string&, const ::std::string&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   根据数据集属性名获取数据ID
     */
    bool getDataIDByProperty(const ::std::string& datasetID, const ::std::string& name, ::std::string& tableName, ::std::string& dataID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDataIDByProperty(tableName, dataID, e, _iceI_begin_getDataIDByProperty(datasetID, name, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   根据数据集属性名获取数据ID
     */
    ::Ice::AsyncResultPtr begin_getDataIDByProperty(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDataIDByProperty(datasetID, name, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   根据数据集属性名获取数据ID
     */
    ::Ice::AsyncResultPtr begin_getDataIDByProperty(const ::std::string& datasetID, const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataIDByProperty(datasetID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   根据数据集属性名获取数据ID
     */
    ::Ice::AsyncResultPtr begin_getDataIDByProperty(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataIDByProperty(datasetID, name, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   根据数据集属性名获取数据ID
     */
    ::Ice::AsyncResultPtr begin_getDataIDByProperty(const ::std::string& datasetID, const ::std::string& name, const ::ZG6000::Callback_ZGMPDatasetProperty_getDataIDByPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataIDByProperty(datasetID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   根据数据集属性名获取数据ID
     */
    ::Ice::AsyncResultPtr begin_getDataIDByProperty(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_getDataIDByPropertyPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataIDByProperty(datasetID, name, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getDataIDByProperty.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getDataIDByProperty(::std::string& tableName, ::std::string& dataID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDataIDByProperty(::std::string& iceP_tableName, ::std::string& iceP_dataID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDataIDByProperty(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param dataID 数据ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   根据数据ID获取数据集属性名
     */
    bool getPropertyByDataID(const ::std::string& dataID, ::std::string& datasetID, ::std::string& name, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getPropertyByDataID(datasetID, name, e, _iceI_begin_getPropertyByDataID(dataID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param dataID 数据ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   根据数据ID获取数据集属性名
     */
    ::Ice::AsyncResultPtr begin_getPropertyByDataID(const ::std::string& dataID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getPropertyByDataID(dataID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param dataID 数据ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   根据数据ID获取数据集属性名
     */
    ::Ice::AsyncResultPtr begin_getPropertyByDataID(const ::std::string& dataID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyByDataID(dataID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param dataID 数据ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   根据数据ID获取数据集属性名
     */
    ::Ice::AsyncResultPtr begin_getPropertyByDataID(const ::std::string& dataID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyByDataID(dataID, context, cb, cookie);
    }

    /**
     * @param dataID 数据ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   根据数据ID获取数据集属性名
     */
    ::Ice::AsyncResultPtr begin_getPropertyByDataID(const ::std::string& dataID, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertyByDataIDPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyByDataID(dataID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param dataID 数据ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   根据数据ID获取数据集属性名
     */
    ::Ice::AsyncResultPtr begin_getPropertyByDataID(const ::std::string& dataID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_getPropertyByDataIDPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getPropertyByDataID(dataID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getPropertyByDataID.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getPropertyByDataID(::std::string& datasetID, ::std::string& name, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getPropertyByDataID(::std::string& iceP_datasetID, ::std::string& iceP_name, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getPropertyByDataID(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   查询指定的数据集属性是否存在
     */
    bool isPropertyExists(const ::std::string& datasetID, const ::std::string& name, bool& exists, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_isPropertyExists(exists, e, _iceI_begin_isPropertyExists(datasetID, name, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   查询指定的数据集属性是否存在
     */
    ::Ice::AsyncResultPtr begin_isPropertyExists(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_isPropertyExists(datasetID, name, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   查询指定的数据集属性是否存在
     */
    ::Ice::AsyncResultPtr begin_isPropertyExists(const ::std::string& datasetID, const ::std::string& name, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isPropertyExists(datasetID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   查询指定的数据集属性是否存在
     */
    ::Ice::AsyncResultPtr begin_isPropertyExists(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isPropertyExists(datasetID, name, context, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   查询指定的数据集属性是否存在
     */
    ::Ice::AsyncResultPtr begin_isPropertyExists(const ::std::string& datasetID, const ::std::string& name, const ::ZG6000::Callback_ZGMPDatasetProperty_isPropertyExistsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isPropertyExists(datasetID, name, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   查询指定的数据集属性是否存在
     */
    ::Ice::AsyncResultPtr begin_isPropertyExists(const ::std::string& datasetID, const ::std::string& name, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPDatasetProperty_isPropertyExistsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isPropertyExists(datasetID, name, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_isPropertyExists.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_isPropertyExists(bool& exists, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_isPropertyExists(bool& iceP_exists, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_isPropertyExists(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGMPDatasetProperty : virtual public ZGServerBase
{
public:

    typedef ZGMPDatasetPropertyPrx ProxyType;
    typedef ZGMPDatasetPropertyPtr PointerType;

    virtual ~ZGMPDatasetProperty();

#ifdef ICE_CPP11_COMPILER
    ZGMPDatasetProperty() = default;
    ZGMPDatasetProperty(const ZGMPDatasetProperty&) = default;
    ZGMPDatasetProperty& operator=(const ZGMPDatasetProperty&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param datasetID 数据集ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集所有属性
     */
    virtual bool getPropertiesAll(const ::std::string& datasetID, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertiesAll(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称列表的属性
     */
    virtual bool getProperties(const ::std::string& datasetID, const StringList& listName, MapStringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称的属性
     */
    virtual bool getProperty(const ::std::string& datasetID, const ::std::string& name, StringMap& property, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param listName 属性名列表
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称列表的属性值
     */
    virtual bool getPropertyValues(const ::std::string& datasetID, const StringList& listName, StringMap& values, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   获取数据集指定名称的属性值
     */
    virtual bool getPropertyValue(const ::std::string& datasetID, const ::std::string& name, ::std::string& value, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param properties 属性集合
     * @param saveToDB 是否更新到数据库
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性集合
     */
    virtual bool updateProperties(const ::std::string& datasetID, const MapStringMap& properties, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateProperties(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param property 更新属性
     * @param saveToDB 是否更新到数据库
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性
     */
    virtual bool updateProperty(const ::std::string& datasetID, const ::std::string& name, const StringMap& property, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param values 属性值集合
     * @param saveToDB 是否更新到数据库
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性值集合
     */
    virtual bool updatePropertyValues(const ::std::string& datasetID, const StringMap& values, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValues(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param value 属性值
     * @param saveToDB 是否更新到数据库
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   更新数据集指定的属性值
     */
    virtual bool updatePropertyValue(const ::std::string& datasetID, const ::std::string& name, const ::std::string& value, bool saveToDB, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updatePropertyValue(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   根据数据集属性名获取数据ID
     */
    virtual bool getDataIDByProperty(const ::std::string& datasetID, const ::std::string& name, ::std::string& tableName, ::std::string& dataID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDataIDByProperty(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param dataID 数据ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   根据数据ID获取数据集属性名
     */
    virtual bool getPropertyByDataID(const ::std::string& dataID, ::std::string& datasetID, ::std::string& name, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getPropertyByDataID(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param datasetID 数据集ID
     * @param name 属性名
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief   查询指定的数据集属性是否存在
     */
    virtual bool isPropertyExists(const ::std::string& datasetID, const ::std::string& name, bool& exists, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_isPropertyExists(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGMPDatasetProperty& lhs, const ZGMPDatasetProperty& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGMPDatasetProperty& lhs, const ZGMPDatasetProperty& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertiesAll.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertiesAll.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_getPropertiesAll : public Callback_ZGMPDatasetProperty_getPropertiesAll_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_getPropertiesAll(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertiesAll(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertiesAll.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertiesAllPtr
newCallback_ZGMPDatasetProperty_getPropertiesAll(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getPropertiesAll<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertiesAll.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertiesAllPtr
newCallback_ZGMPDatasetProperty_getPropertiesAll(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getPropertiesAll<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertiesAll.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertiesAll.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_getPropertiesAll : public Callback_ZGMPDatasetProperty_getPropertiesAll_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_getPropertiesAll(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertiesAll(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertiesAll.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertiesAllPtr
newCallback_ZGMPDatasetProperty_getPropertiesAll(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getPropertiesAll<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertiesAll.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertiesAllPtr
newCallback_ZGMPDatasetProperty_getPropertiesAll(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getPropertiesAll<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getProperties.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_getProperties : public Callback_ZGMPDatasetProperty_getProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_getProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_properties, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperties.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertiesPtr
newCallback_ZGMPDatasetProperty_getProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperties.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertiesPtr
newCallback_ZGMPDatasetProperty_getProperties(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getProperties.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_getProperties : public Callback_ZGMPDatasetProperty_getProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const MapStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_getProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        MapStringMap iceP_properties;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getProperties(iceP_properties, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_properties, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperties.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertiesPtr
newCallback_ZGMPDatasetProperty_getProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperties.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertiesPtr
newCallback_ZGMPDatasetProperty_getProperties(T* instance, void (T::*cb)(bool, const MapStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getProperty.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_getProperty : public Callback_ZGMPDatasetProperty_getProperty_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_getProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_property;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getProperty(iceP_property, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_property, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperty.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertyPtr
newCallback_ZGMPDatasetProperty_getProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperty.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertyPtr
newCallback_ZGMPDatasetProperty_getProperty(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getProperty.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_getProperty : public Callback_ZGMPDatasetProperty_getProperty_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_getProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_property;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getProperty(iceP_property, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_property, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperty.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertyPtr
newCallback_ZGMPDatasetProperty_getProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getProperty.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertyPtr
newCallback_ZGMPDatasetProperty_getProperty(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertyValues.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_getPropertyValues : public Callback_ZGMPDatasetProperty_getPropertyValues_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_getPropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValues(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_values, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValues.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertyValuesPtr
newCallback_ZGMPDatasetProperty_getPropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getPropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValues.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertyValuesPtr
newCallback_ZGMPDatasetProperty_getPropertyValues(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getPropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertyValues.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_getPropertyValues : public Callback_ZGMPDatasetProperty_getPropertyValues_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_getPropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        StringMap iceP_values;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValues(iceP_values, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_values, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertyValuesPtr
newCallback_ZGMPDatasetProperty_getPropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getPropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertyValuesPtr
newCallback_ZGMPDatasetProperty_getPropertyValues(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getPropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertyValue.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_getPropertyValue : public Callback_ZGMPDatasetProperty_getPropertyValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_getPropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_value;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValue(iceP_value, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_value, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValue.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertyValuePtr
newCallback_ZGMPDatasetProperty_getPropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getPropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValue.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertyValuePtr
newCallback_ZGMPDatasetProperty_getPropertyValue(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getPropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertyValue.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_getPropertyValue : public Callback_ZGMPDatasetProperty_getPropertyValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_getPropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_value;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyValue(iceP_value, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_value, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValue.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertyValuePtr
newCallback_ZGMPDatasetProperty_getPropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getPropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyValue.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertyValuePtr
newCallback_ZGMPDatasetProperty_getPropertyValue(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getPropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updateProperties.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_updateProperties : public Callback_ZGMPDatasetProperty_updateProperties_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_updateProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateProperties(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperties.
 */
template<class T> Callback_ZGMPDatasetProperty_updatePropertiesPtr
newCallback_ZGMPDatasetProperty_updateProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_updateProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperties.
 */
template<class T> Callback_ZGMPDatasetProperty_updatePropertiesPtr
newCallback_ZGMPDatasetProperty_updateProperties(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_updateProperties<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperties.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updateProperties.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_updateProperties : public Callback_ZGMPDatasetProperty_updateProperties_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_updateProperties(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateProperties(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperties.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_updatePropertiesPtr
newCallback_ZGMPDatasetProperty_updateProperties(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_updateProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperties.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_updatePropertiesPtr
newCallback_ZGMPDatasetProperty_updateProperties(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_updateProperties<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updateProperty.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_updateProperty : public Callback_ZGMPDatasetProperty_updateProperty_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_updateProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateProperty(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperty.
 */
template<class T> Callback_ZGMPDatasetProperty_updatePropertyPtr
newCallback_ZGMPDatasetProperty_updateProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_updateProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperty.
 */
template<class T> Callback_ZGMPDatasetProperty_updatePropertyPtr
newCallback_ZGMPDatasetProperty_updateProperty(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_updateProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updateProperty.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_updateProperty : public Callback_ZGMPDatasetProperty_updateProperty_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_updateProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateProperty(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperty.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_updatePropertyPtr
newCallback_ZGMPDatasetProperty_updateProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_updateProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updateProperty.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_updatePropertyPtr
newCallback_ZGMPDatasetProperty_updateProperty(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_updateProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updatePropertyValues.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_updatePropertyValues : public Callback_ZGMPDatasetProperty_updatePropertyValues_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_updatePropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValues(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValues.
 */
template<class T> Callback_ZGMPDatasetProperty_updatePropertyValuesPtr
newCallback_ZGMPDatasetProperty_updatePropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_updatePropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValues.
 */
template<class T> Callback_ZGMPDatasetProperty_updatePropertyValuesPtr
newCallback_ZGMPDatasetProperty_updatePropertyValues(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_updatePropertyValues<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValues.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updatePropertyValues.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_updatePropertyValues : public Callback_ZGMPDatasetProperty_updatePropertyValues_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_updatePropertyValues(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValues(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_updatePropertyValuesPtr
newCallback_ZGMPDatasetProperty_updatePropertyValues(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_updatePropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValues.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_updatePropertyValuesPtr
newCallback_ZGMPDatasetProperty_updatePropertyValues(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_updatePropertyValues<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updatePropertyValue.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_updatePropertyValue : public Callback_ZGMPDatasetProperty_updatePropertyValue_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_updatePropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValue.
 */
template<class T> Callback_ZGMPDatasetProperty_updatePropertyValuePtr
newCallback_ZGMPDatasetProperty_updatePropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_updatePropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValue.
 */
template<class T> Callback_ZGMPDatasetProperty_updatePropertyValuePtr
newCallback_ZGMPDatasetProperty_updatePropertyValue(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_updatePropertyValue<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValue.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_updatePropertyValue.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_updatePropertyValue : public Callback_ZGMPDatasetProperty_updatePropertyValue_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_updatePropertyValue(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updatePropertyValue(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValue.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_updatePropertyValuePtr
newCallback_ZGMPDatasetProperty_updatePropertyValue(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_updatePropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_updatePropertyValue.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_updatePropertyValuePtr
newCallback_ZGMPDatasetProperty_updatePropertyValue(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_updatePropertyValue<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getDataIDByProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getDataIDByProperty.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_getDataIDByProperty : public Callback_ZGMPDatasetProperty_getDataIDByProperty_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_getDataIDByProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_tableName;
        ::std::string iceP_dataID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDataIDByProperty(iceP_tableName, iceP_dataID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_tableName, iceP_dataID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getDataIDByProperty.
 */
template<class T> Callback_ZGMPDatasetProperty_getDataIDByPropertyPtr
newCallback_ZGMPDatasetProperty_getDataIDByProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getDataIDByProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getDataIDByProperty.
 */
template<class T> Callback_ZGMPDatasetProperty_getDataIDByPropertyPtr
newCallback_ZGMPDatasetProperty_getDataIDByProperty(T* instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getDataIDByProperty<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getDataIDByProperty.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getDataIDByProperty.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_getDataIDByProperty : public Callback_ZGMPDatasetProperty_getDataIDByProperty_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_getDataIDByProperty(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_tableName;
        ::std::string iceP_dataID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDataIDByProperty(iceP_tableName, iceP_dataID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_tableName, iceP_dataID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getDataIDByProperty.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getDataIDByPropertyPtr
newCallback_ZGMPDatasetProperty_getDataIDByProperty(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getDataIDByProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getDataIDByProperty.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getDataIDByPropertyPtr
newCallback_ZGMPDatasetProperty_getDataIDByProperty(T* instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getDataIDByProperty<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyByDataID.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertyByDataID.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_getPropertyByDataID : public Callback_ZGMPDatasetProperty_getPropertyByDataID_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_getPropertyByDataID(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_datasetID;
        ::std::string iceP_name;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyByDataID(iceP_datasetID, iceP_name, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_datasetID, iceP_name, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyByDataID.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertyByDataIDPtr
newCallback_ZGMPDatasetProperty_getPropertyByDataID(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getPropertyByDataID<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyByDataID.
 */
template<class T> Callback_ZGMPDatasetProperty_getPropertyByDataIDPtr
newCallback_ZGMPDatasetProperty_getPropertyByDataID(T* instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_getPropertyByDataID<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyByDataID.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_getPropertyByDataID.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_getPropertyByDataID : public Callback_ZGMPDatasetProperty_getPropertyByDataID_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_getPropertyByDataID(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_datasetID;
        ::std::string iceP_name;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getPropertyByDataID(iceP_datasetID, iceP_name, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_datasetID, iceP_name, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyByDataID.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertyByDataIDPtr
newCallback_ZGMPDatasetProperty_getPropertyByDataID(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getPropertyByDataID<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_getPropertyByDataID.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_getPropertyByDataIDPtr
newCallback_ZGMPDatasetProperty_getPropertyByDataID(T* instance, void (T::*cb)(bool, const ::std::string&, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_getPropertyByDataID<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_isPropertyExists.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_isPropertyExists.
 */
template<class T>
class CallbackNC_ZGMPDatasetProperty_isPropertyExists : public Callback_ZGMPDatasetProperty_isPropertyExists_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&);

    CallbackNC_ZGMPDatasetProperty_isPropertyExists(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        bool iceP_exists;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isPropertyExists(iceP_exists, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_exists, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_isPropertyExists.
 */
template<class T> Callback_ZGMPDatasetProperty_isPropertyExistsPtr
newCallback_ZGMPDatasetProperty_isPropertyExists(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_isPropertyExists<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_isPropertyExists.
 */
template<class T> Callback_ZGMPDatasetProperty_isPropertyExistsPtr
newCallback_ZGMPDatasetProperty_isPropertyExists(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPDatasetProperty_isPropertyExists<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPDatasetProperty::begin_isPropertyExists.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPDatasetProperty_isPropertyExists.
 */
template<class T, typename CT>
class Callback_ZGMPDatasetProperty_isPropertyExists : public Callback_ZGMPDatasetProperty_isPropertyExists_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&, const CT&);

    Callback_ZGMPDatasetProperty_isPropertyExists(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPDatasetPropertyPrx proxy = ZGMPDatasetPropertyPrx::uncheckedCast(result->getProxy());
        bool iceP_exists;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isPropertyExists(iceP_exists, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_exists, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_isPropertyExists.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_isPropertyExistsPtr
newCallback_ZGMPDatasetProperty_isPropertyExists(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_isPropertyExists<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPDatasetProperty::begin_isPropertyExists.
 */
template<class T, typename CT> Callback_ZGMPDatasetProperty_isPropertyExistsPtr
newCallback_ZGMPDatasetProperty_isPropertyExists(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPDatasetProperty_isPropertyExists<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

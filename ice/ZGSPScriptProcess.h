//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPScriptProcess.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPScriptProcess_h__
#define __ZGSPScriptProcess_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPScriptProcess;
class ZGSPScriptProcessPrx;

}

namespace ZG6000
{

class ZGSPScriptProcess : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPScriptProcessPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to evaluateByJson.
     */
    struct EvaluateByJsonResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   评估函数语法是否正确
     */
    virtual bool evaluateByJson(::std::string funcName, ::std::string jsonParam, ::std::string script, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_evaluateByJson(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to evaluateByList.
     */
    struct EvaluateByListResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   评估函数语法是否正确
     */
    virtual bool evaluateByList(::std::string funcName, StringList listParam, ::std::string script, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_evaluateByList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listRuleID 规则ID列表
     * @param current The Current object for the invocation.
     * @return 单向调用，不返回执行结果
     * @brief   批量调用规则
     */
    virtual void invokeOneway(StringList listRuleID, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeOneway(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to invoke.
     */
    struct InvokeResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param ruleID 规则ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，并返回执行结果
     */
    virtual bool invoke(::std::string ruleID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_invoke(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to invokeToBool.
     */
    struct InvokeToBoolResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        bool result;
        ErrorInfo e;
    };

    /**
     * @param ruleID 规则ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    virtual bool invokeToBool(::std::string ruleID, bool& result, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeToBool(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to invokeToInt.
     */
    struct InvokeToIntResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        int result;
        ErrorInfo e;
    };

    /**
     * @param ruleID 规则ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    virtual bool invokeToInt(::std::string ruleID, int& result, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeToInt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to invokeToDouble.
     */
    struct InvokeToDoubleResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        double result;
        ErrorInfo e;
    };

    /**
     * @param ruleID 规则ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    virtual bool invokeToDouble(::std::string ruleID, double& result, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeToDouble(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to invokeToString.
     */
    struct InvokeToStringResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        ::std::string result;
        ErrorInfo e;
    };

    /**
     * @param ruleID 规则ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    virtual bool invokeToString(::std::string ruleID, ::std::string& result, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeToString(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to invokeToStringList.
     */
    struct InvokeToStringListResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        StringList result;
        ErrorInfo e;
    };

    /**
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    virtual bool invokeToStringList(::std::string ruleID, StringList& result, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeToStringList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listExpress 表达式列表
     * @param current The Current object for the invocation.
     * @brief   批量调用表达式(单向调用)
     */
    virtual void callBatch(ListStringMap listExpress, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_callBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to callJson.
     */
    struct CallJsonResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param expressID 表达式ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果
     */
    virtual bool callJson(::std::string expressID, ::std::string jsonParam, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_callJson(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to callToBool.
     */
    struct CallToBoolResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        bool result;
        ErrorInfo e;
    };

    /**
     * @param expressID 表达式ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    virtual bool callToBool(::std::string expressID, ::std::string jsonParam, bool& result, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_callToBool(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to callToInt.
     */
    struct CallToIntResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        int result;
        ErrorInfo e;
    };

    /**
     * @param expressID 表达式ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    virtual bool callToInt(::std::string expressID, ::std::string jsonParam, int& result, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_callToInt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to callToDouble.
     */
    struct CallToDoubleResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        double result;
        ErrorInfo e;
    };

    /**
     * @param expressID 表达式ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    virtual bool callToDouble(::std::string expressID, ::std::string jsonParam, double& result, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_callToDouble(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to callToString.
     */
    struct CallToStringResult
    {
        /** 调用成功返回true，失败返回false。 */
        bool returnValue;
        ::std::string result;
        ErrorInfo e;
    };

    /**
     * @param expressID 表达式ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    virtual bool callToString(::std::string expressID, ::std::string jsonParam, ::std::string& result, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_callToString(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPScriptProcessPrx : public virtual ::Ice::Proxy<ZGSPScriptProcessPrx, ZGServerBasePrx>
{
public:

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   评估函数语法是否正确
     */
    bool evaluateByJson(const ::std::string& funcName, const ::std::string& jsonParam, const ::std::string& script, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::EvaluateByJsonResult>(true, this, &ZGSPScriptProcessPrx::_iceI_evaluateByJson, funcName, jsonParam, script, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   评估函数语法是否正确
     */
    template<template<typename> class P = ::std::promise>
    auto evaluateByJsonAsync(const ::std::string& funcName, const ::std::string& jsonParam, const ::std::string& script, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::EvaluateByJsonResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::EvaluateByJsonResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_evaluateByJson, funcName, jsonParam, script, context);
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   评估函数语法是否正确
     */
    ::std::function<void()>
    evaluateByJsonAsync(const ::std::string& funcName, const ::std::string& jsonParam, const ::std::string& script,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::EvaluateByJsonResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::EvaluateByJsonResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_evaluateByJson, funcName, jsonParam, script, context);
    }

    /// \cond INTERNAL
    void _iceI_evaluateByJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::EvaluateByJsonResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   评估函数语法是否正确
     */
    bool evaluateByList(const ::std::string& funcName, const StringList& listParam, const ::std::string& script, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::EvaluateByListResult>(true, this, &ZGSPScriptProcessPrx::_iceI_evaluateByList, funcName, listParam, script, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   评估函数语法是否正确
     */
    template<template<typename> class P = ::std::promise>
    auto evaluateByListAsync(const ::std::string& funcName, const StringList& listParam, const ::std::string& script, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::EvaluateByListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::EvaluateByListResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_evaluateByList, funcName, listParam, script, context);
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   评估函数语法是否正确
     */
    ::std::function<void()>
    evaluateByListAsync(const ::std::string& funcName, const StringList& listParam, const ::std::string& script,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::EvaluateByListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::EvaluateByListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_evaluateByList, funcName, listParam, script, context);
    }

    /// \cond INTERNAL
    void _iceI_evaluateByList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::EvaluateByListResult>>&, const ::std::string&, const StringList&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param listRuleID 规则ID列表
     * @param context The Context map to send with the invocation.
     * @return 单向调用，不返回执行结果
     * @brief   批量调用规则
     */
    void invokeOneway(const StringList& listRuleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPScriptProcessPrx::_iceI_invokeOneway, listRuleID, context).get();
    }

    /**
     * @param listRuleID 规则ID列表
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   批量调用规则
     */
    template<template<typename> class P = ::std::promise>
    auto invokeOnewayAsync(const StringList& listRuleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPScriptProcessPrx::_iceI_invokeOneway, listRuleID, context);
    }

    /**
     * @param listRuleID 规则ID列表
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   批量调用规则
     */
    ::std::function<void()>
    invokeOnewayAsync(const StringList& listRuleID,
                      ::std::function<void()> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_invokeOneway, listRuleID, context);
    }

    /// \cond INTERNAL
    void _iceI_invokeOneway(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，并返回执行结果
     */
    bool invoke(const ::std::string& ruleID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::InvokeResult>(true, this, &ZGSPScriptProcessPrx::_iceI_invoke, ruleID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的规则，并返回执行结果
     */
    template<template<typename> class P = ::std::promise>
    auto invokeAsync(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::InvokeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::InvokeResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_invoke, ruleID, context);
    }

    /**
     * @param ruleID 规则ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的规则，并返回执行结果
     */
    ::std::function<void()>
    invokeAsync(const ::std::string& ruleID,
                ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                ::std::function<void(::std::exception_ptr)> ex = nullptr,
                ::std::function<void(bool)> sent = nullptr,
                const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::InvokeResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::InvokeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_invoke, ruleID, context);
    }

    /// \cond INTERNAL
    void _iceI_invoke(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    bool invokeToBool(const ::std::string& ruleID, bool& result, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::InvokeToBoolResult>(true, this, &ZGSPScriptProcessPrx::_iceI_invokeToBool, ruleID, context).get();
        result = _result.result;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    template<template<typename> class P = ::std::promise>
    auto invokeToBoolAsync(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::InvokeToBoolResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::InvokeToBoolResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_invokeToBool, ruleID, context);
    }

    /**
     * @param ruleID 规则ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::std::function<void()>
    invokeToBoolAsync(const ::std::string& ruleID,
                      ::std::function<void(bool, bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::InvokeToBoolResult&& _result)
        {
            response(_result.returnValue, _result.result, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::InvokeToBoolResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_invokeToBool, ruleID, context);
    }

    /// \cond INTERNAL
    void _iceI_invokeToBool(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeToBoolResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    bool invokeToInt(const ::std::string& ruleID, int& result, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::InvokeToIntResult>(true, this, &ZGSPScriptProcessPrx::_iceI_invokeToInt, ruleID, context).get();
        result = _result.result;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    template<template<typename> class P = ::std::promise>
    auto invokeToIntAsync(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::InvokeToIntResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::InvokeToIntResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_invokeToInt, ruleID, context);
    }

    /**
     * @param ruleID 规则ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::std::function<void()>
    invokeToIntAsync(const ::std::string& ruleID,
                     ::std::function<void(bool, int, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::InvokeToIntResult&& _result)
        {
            response(_result.returnValue, _result.result, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::InvokeToIntResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_invokeToInt, ruleID, context);
    }

    /// \cond INTERNAL
    void _iceI_invokeToInt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeToIntResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    bool invokeToDouble(const ::std::string& ruleID, double& result, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::InvokeToDoubleResult>(true, this, &ZGSPScriptProcessPrx::_iceI_invokeToDouble, ruleID, context).get();
        result = _result.result;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    template<template<typename> class P = ::std::promise>
    auto invokeToDoubleAsync(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::InvokeToDoubleResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::InvokeToDoubleResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_invokeToDouble, ruleID, context);
    }

    /**
     * @param ruleID 规则ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::std::function<void()>
    invokeToDoubleAsync(const ::std::string& ruleID,
                        ::std::function<void(bool, double, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::InvokeToDoubleResult&& _result)
        {
            response(_result.returnValue, _result.result, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::InvokeToDoubleResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_invokeToDouble, ruleID, context);
    }

    /// \cond INTERNAL
    void _iceI_invokeToDouble(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeToDoubleResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    bool invokeToString(const ::std::string& ruleID, ::std::string& result, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::InvokeToStringResult>(true, this, &ZGSPScriptProcessPrx::_iceI_invokeToString, ruleID, context).get();
        result = ::std::move(_result.result);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    template<template<typename> class P = ::std::promise>
    auto invokeToStringAsync(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::InvokeToStringResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::InvokeToStringResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_invokeToString, ruleID, context);
    }

    /**
     * @param ruleID 规则ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::std::function<void()>
    invokeToStringAsync(const ::std::string& ruleID,
                        ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::InvokeToStringResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.result), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::InvokeToStringResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_invokeToString, ruleID, context);
    }

    /// \cond INTERNAL
    void _iceI_invokeToString(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeToStringResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    bool invokeToStringList(const ::std::string& ruleID, StringList& result, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::InvokeToStringListResult>(true, this, &ZGSPScriptProcessPrx::_iceI_invokeToStringList, ruleID, context).get();
        result = ::std::move(_result.result);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    template<template<typename> class P = ::std::promise>
    auto invokeToStringListAsync(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::InvokeToStringListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::InvokeToStringListResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_invokeToStringList, ruleID, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::std::function<void()>
    invokeToStringListAsync(const ::std::string& ruleID,
                            ::std::function<void(bool, ::ZG6000::StringList, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::InvokeToStringListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.result), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::InvokeToStringListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_invokeToStringList, ruleID, context);
    }

    /// \cond INTERNAL
    void _iceI_invokeToStringList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::InvokeToStringListResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param listExpress 表达式列表
     * @param context The Context map to send with the invocation.
     * @brief   批量调用表达式(单向调用)
     */
    void callBatch(const ListStringMap& listExpress, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPScriptProcessPrx::_iceI_callBatch, listExpress, context).get();
    }

    /**
     * @param listExpress 表达式列表
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   批量调用表达式(单向调用)
     */
    template<template<typename> class P = ::std::promise>
    auto callBatchAsync(const ListStringMap& listExpress, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPScriptProcessPrx::_iceI_callBatch, listExpress, context);
    }

    /**
     * @param listExpress 表达式列表
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   批量调用表达式(单向调用)
     */
    ::std::function<void()>
    callBatchAsync(const ListStringMap& listExpress,
                   ::std::function<void()> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_callBatch, listExpress, context);
    }

    /// \cond INTERNAL
    void _iceI_callBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果
     */
    bool callJson(const ::std::string& expressID, const ::std::string& jsonParam, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::CallJsonResult>(true, this, &ZGSPScriptProcessPrx::_iceI_callJson, expressID, jsonParam, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果
     */
    template<template<typename> class P = ::std::promise>
    auto callJsonAsync(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::CallJsonResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::CallJsonResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_callJson, expressID, jsonParam, context);
    }

    /**
     * @param expressID 表达式ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的表达式，并返回执行结果
     */
    ::std::function<void()>
    callJsonAsync(const ::std::string& expressID, const ::std::string& jsonParam,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::CallJsonResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::CallJsonResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_callJson, expressID, jsonParam, context);
    }

    /// \cond INTERNAL
    void _iceI_callJson(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::CallJsonResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    bool callToBool(const ::std::string& expressID, const ::std::string& jsonParam, bool& result, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::CallToBoolResult>(true, this, &ZGSPScriptProcessPrx::_iceI_callToBool, expressID, jsonParam, context).get();
        result = _result.result;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    template<template<typename> class P = ::std::promise>
    auto callToBoolAsync(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::CallToBoolResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::CallToBoolResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_callToBool, expressID, jsonParam, context);
    }

    /**
     * @param expressID 表达式ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::std::function<void()>
    callToBoolAsync(const ::std::string& expressID, const ::std::string& jsonParam,
                    ::std::function<void(bool, bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::CallToBoolResult&& _result)
        {
            response(_result.returnValue, _result.result, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::CallToBoolResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_callToBool, expressID, jsonParam, context);
    }

    /// \cond INTERNAL
    void _iceI_callToBool(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::CallToBoolResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    bool callToInt(const ::std::string& expressID, const ::std::string& jsonParam, int& result, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::CallToIntResult>(true, this, &ZGSPScriptProcessPrx::_iceI_callToInt, expressID, jsonParam, context).get();
        result = _result.result;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    template<template<typename> class P = ::std::promise>
    auto callToIntAsync(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::CallToIntResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::CallToIntResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_callToInt, expressID, jsonParam, context);
    }

    /**
     * @param expressID 表达式ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::std::function<void()>
    callToIntAsync(const ::std::string& expressID, const ::std::string& jsonParam,
                   ::std::function<void(bool, int, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::CallToIntResult&& _result)
        {
            response(_result.returnValue, _result.result, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::CallToIntResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_callToInt, expressID, jsonParam, context);
    }

    /// \cond INTERNAL
    void _iceI_callToInt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::CallToIntResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    bool callToDouble(const ::std::string& expressID, const ::std::string& jsonParam, double& result, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::CallToDoubleResult>(true, this, &ZGSPScriptProcessPrx::_iceI_callToDouble, expressID, jsonParam, context).get();
        result = _result.result;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    template<template<typename> class P = ::std::promise>
    auto callToDoubleAsync(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::CallToDoubleResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::CallToDoubleResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_callToDouble, expressID, jsonParam, context);
    }

    /**
     * @param expressID 表达式ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::std::function<void()>
    callToDoubleAsync(const ::std::string& expressID, const ::std::string& jsonParam,
                      ::std::function<void(bool, double, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::CallToDoubleResult&& _result)
        {
            response(_result.returnValue, _result.result, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::CallToDoubleResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_callToDouble, expressID, jsonParam, context);
    }

    /// \cond INTERNAL
    void _iceI_callToDouble(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::CallToDoubleResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    bool callToString(const ::std::string& expressID, const ::std::string& jsonParam, ::std::string& result, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPScriptProcess::CallToStringResult>(true, this, &ZGSPScriptProcessPrx::_iceI_callToString, expressID, jsonParam, context).get();
        result = ::std::move(_result.result);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    template<template<typename> class P = ::std::promise>
    auto callToStringAsync(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPScriptProcess::CallToStringResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPScriptProcess::CallToStringResult, P>(false, this, &ZGSPScriptProcessPrx::_iceI_callToString, expressID, jsonParam, context);
    }

    /**
     * @param expressID 表达式ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::std::function<void()>
    callToStringAsync(const ::std::string& expressID, const ::std::string& jsonParam,
                      ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPScriptProcess::CallToStringResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.result), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPScriptProcess::CallToStringResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPScriptProcessPrx::_iceI_callToString, expressID, jsonParam, context);
    }

    /// \cond INTERNAL
    void _iceI_callToString(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPScriptProcess::CallToStringResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPScriptProcessPrx() = default;
    friend ::std::shared_ptr<ZGSPScriptProcessPrx> IceInternal::createProxy<ZGSPScriptProcessPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPScriptProcessPtr = ::std::shared_ptr<ZGSPScriptProcess>;
using ZGSPScriptProcessPrxPtr = ::std::shared_ptr<ZGSPScriptProcessPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPScriptProcess;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPScriptProcess>&);
::IceProxy::Ice::Object* upCast(ZGSPScriptProcess*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPScriptProcess;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPScriptProcess*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPScriptProcess> ZGSPScriptProcessPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPScriptProcess> ZGSPScriptProcessPrx;
typedef ZGSPScriptProcessPrx ZGSPScriptProcessPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPScriptProcessPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByJson.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_evaluateByJson.
 */
class Callback_ZGSPScriptProcess_evaluateByJson_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_evaluateByJson_Base> Callback_ZGSPScriptProcess_evaluateByJsonPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_evaluateByList.
 */
class Callback_ZGSPScriptProcess_evaluateByList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_evaluateByList_Base> Callback_ZGSPScriptProcess_evaluateByListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeOneway.
 */
class Callback_ZGSPScriptProcess_invokeOneway_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_invokeOneway_Base> Callback_ZGSPScriptProcess_invokeOnewayPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invoke.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invoke.
 */
class Callback_ZGSPScriptProcess_invoke_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_invoke_Base> Callback_ZGSPScriptProcess_invokePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToBool.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToBool.
 */
class Callback_ZGSPScriptProcess_invokeToBool_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_invokeToBool_Base> Callback_ZGSPScriptProcess_invokeToBoolPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToInt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToInt.
 */
class Callback_ZGSPScriptProcess_invokeToInt_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_invokeToInt_Base> Callback_ZGSPScriptProcess_invokeToIntPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToDouble.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToDouble.
 */
class Callback_ZGSPScriptProcess_invokeToDouble_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_invokeToDouble_Base> Callback_ZGSPScriptProcess_invokeToDoublePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToString.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToString.
 */
class Callback_ZGSPScriptProcess_invokeToString_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_invokeToString_Base> Callback_ZGSPScriptProcess_invokeToStringPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToStringList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToStringList.
 */
class Callback_ZGSPScriptProcess_invokeToStringList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_invokeToStringList_Base> Callback_ZGSPScriptProcess_invokeToStringListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callBatch.
 */
class Callback_ZGSPScriptProcess_callBatch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_callBatch_Base> Callback_ZGSPScriptProcess_callBatchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callJson.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callJson.
 */
class Callback_ZGSPScriptProcess_callJson_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_callJson_Base> Callback_ZGSPScriptProcess_callJsonPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToBool.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToBool.
 */
class Callback_ZGSPScriptProcess_callToBool_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_callToBool_Base> Callback_ZGSPScriptProcess_callToBoolPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToInt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToInt.
 */
class Callback_ZGSPScriptProcess_callToInt_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_callToInt_Base> Callback_ZGSPScriptProcess_callToIntPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToDouble.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToDouble.
 */
class Callback_ZGSPScriptProcess_callToDouble_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_callToDouble_Base> Callback_ZGSPScriptProcess_callToDoublePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToString.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToString.
 */
class Callback_ZGSPScriptProcess_callToString_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPScriptProcess_callToString_Base> Callback_ZGSPScriptProcess_callToStringPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPScriptProcess : public virtual ::Ice::Proxy<ZGSPScriptProcess, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   评估函数语法是否正确
     */
    bool evaluateByJson(const ::std::string& funcName, const ::std::string& jsonParam, const ::std::string& script, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_evaluateByJson(e, _iceI_begin_evaluateByJson(funcName, jsonParam, script, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   评估函数语法是否正确
     */
    ::Ice::AsyncResultPtr begin_evaluateByJson(const ::std::string& funcName, const ::std::string& jsonParam, const ::std::string& script, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_evaluateByJson(funcName, jsonParam, script, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   评估函数语法是否正确
     */
    ::Ice::AsyncResultPtr begin_evaluateByJson(const ::std::string& funcName, const ::std::string& jsonParam, const ::std::string& script, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_evaluateByJson(funcName, jsonParam, script, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   评估函数语法是否正确
     */
    ::Ice::AsyncResultPtr begin_evaluateByJson(const ::std::string& funcName, const ::std::string& jsonParam, const ::std::string& script, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_evaluateByJson(funcName, jsonParam, script, context, cb, cookie);
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   评估函数语法是否正确
     */
    ::Ice::AsyncResultPtr begin_evaluateByJson(const ::std::string& funcName, const ::std::string& jsonParam, const ::std::string& script, const ::ZG6000::Callback_ZGSPScriptProcess_evaluateByJsonPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_evaluateByJson(funcName, jsonParam, script, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   评估函数语法是否正确
     */
    ::Ice::AsyncResultPtr begin_evaluateByJson(const ::std::string& funcName, const ::std::string& jsonParam, const ::std::string& script, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_evaluateByJsonPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_evaluateByJson(funcName, jsonParam, script, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_evaluateByJson.
     * @param result The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_evaluateByJson(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_evaluateByJson(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_evaluateByJson(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   评估函数语法是否正确
     */
    bool evaluateByList(const ::std::string& funcName, const ::ZG6000::StringList& listParam, const ::std::string& script, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_evaluateByList(e, _iceI_begin_evaluateByList(funcName, listParam, script, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   评估函数语法是否正确
     */
    ::Ice::AsyncResultPtr begin_evaluateByList(const ::std::string& funcName, const ::ZG6000::StringList& listParam, const ::std::string& script, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_evaluateByList(funcName, listParam, script, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   评估函数语法是否正确
     */
    ::Ice::AsyncResultPtr begin_evaluateByList(const ::std::string& funcName, const ::ZG6000::StringList& listParam, const ::std::string& script, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_evaluateByList(funcName, listParam, script, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   评估函数语法是否正确
     */
    ::Ice::AsyncResultPtr begin_evaluateByList(const ::std::string& funcName, const ::ZG6000::StringList& listParam, const ::std::string& script, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_evaluateByList(funcName, listParam, script, context, cb, cookie);
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   评估函数语法是否正确
     */
    ::Ice::AsyncResultPtr begin_evaluateByList(const ::std::string& funcName, const ::ZG6000::StringList& listParam, const ::std::string& script, const ::ZG6000::Callback_ZGSPScriptProcess_evaluateByListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_evaluateByList(funcName, listParam, script, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   评估函数语法是否正确
     */
    ::Ice::AsyncResultPtr begin_evaluateByList(const ::std::string& funcName, const ::ZG6000::StringList& listParam, const ::std::string& script, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_evaluateByListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_evaluateByList(funcName, listParam, script, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_evaluateByList.
     * @param result The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_evaluateByList(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_evaluateByList(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_evaluateByList(const ::std::string&, const ::ZG6000::StringList&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param listRuleID 规则ID列表
     * @param context The Context map to send with the invocation.
     * @return 单向调用，不返回执行结果
     * @brief   批量调用规则
     */
    void invokeOneway(const ::ZG6000::StringList& listRuleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_invokeOneway(_iceI_begin_invokeOneway(listRuleID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param listRuleID 规则ID列表
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   批量调用规则
     */
    ::Ice::AsyncResultPtr begin_invokeOneway(const ::ZG6000::StringList& listRuleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_invokeOneway(listRuleID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param listRuleID 规则ID列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   批量调用规则
     */
    ::Ice::AsyncResultPtr begin_invokeOneway(const ::ZG6000::StringList& listRuleID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeOneway(listRuleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listRuleID 规则ID列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   批量调用规则
     */
    ::Ice::AsyncResultPtr begin_invokeOneway(const ::ZG6000::StringList& listRuleID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeOneway(listRuleID, context, cb, cookie);
    }

    /**
     * @param listRuleID 规则ID列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   批量调用规则
     */
    ::Ice::AsyncResultPtr begin_invokeOneway(const ::ZG6000::StringList& listRuleID, const ::ZG6000::Callback_ZGSPScriptProcess_invokeOnewayPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeOneway(listRuleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listRuleID 规则ID列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   批量调用规则
     */
    ::Ice::AsyncResultPtr begin_invokeOneway(const ::ZG6000::StringList& listRuleID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_invokeOnewayPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeOneway(listRuleID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_invokeOneway.
     * @param result The asynchronous result object for the invocation.
     * @return 单向调用，不返回执行结果
     */
    void end_invokeOneway(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_invokeOneway(const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，并返回执行结果
     */
    bool invoke(const ::std::string& ruleID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_invoke(e, _iceI_begin_invoke(ruleID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，并返回执行结果
     */
    ::Ice::AsyncResultPtr begin_invoke(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_invoke(ruleID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param ruleID 规则ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，并返回执行结果
     */
    ::Ice::AsyncResultPtr begin_invoke(const ::std::string& ruleID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invoke(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，并返回执行结果
     */
    ::Ice::AsyncResultPtr begin_invoke(const ::std::string& ruleID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invoke(ruleID, context, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，并返回执行结果
     */
    ::Ice::AsyncResultPtr begin_invoke(const ::std::string& ruleID, const ::ZG6000::Callback_ZGSPScriptProcess_invokePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invoke(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，并返回执行结果
     */
    ::Ice::AsyncResultPtr begin_invoke(const ::std::string& ruleID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_invokePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invoke(ruleID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_invoke.
     * @param result The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_invoke(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_invoke(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_invoke(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    bool invokeToBool(const ::std::string& ruleID, bool& result, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_invokeToBool(result, e, _iceI_begin_invokeToBool(ruleID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToBool(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_invokeToBool(ruleID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param ruleID 规则ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToBool(const ::std::string& ruleID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToBool(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToBool(const ::std::string& ruleID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToBool(ruleID, context, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToBool(const ::std::string& ruleID, const ::ZG6000::Callback_ZGSPScriptProcess_invokeToBoolPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToBool(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToBool(const ::std::string& ruleID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_invokeToBoolPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToBool(ruleID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_invokeToBool.
     * @param result_ The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_invokeToBool(bool& result, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result_);
    /// \cond INTERNAL

    void _iceI_end_invokeToBool(bool& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_invokeToBool(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    bool invokeToInt(const ::std::string& ruleID, ::Ice::Int& result, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_invokeToInt(result, e, _iceI_begin_invokeToInt(ruleID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToInt(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_invokeToInt(ruleID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param ruleID 规则ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToInt(const ::std::string& ruleID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToInt(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToInt(const ::std::string& ruleID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToInt(ruleID, context, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToInt(const ::std::string& ruleID, const ::ZG6000::Callback_ZGSPScriptProcess_invokeToIntPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToInt(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToInt(const ::std::string& ruleID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_invokeToIntPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToInt(ruleID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_invokeToInt.
     * @param result_ The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_invokeToInt(::Ice::Int& result, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result_);
    /// \cond INTERNAL

    void _iceI_end_invokeToInt(::Ice::Int& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_invokeToInt(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    bool invokeToDouble(const ::std::string& ruleID, ::Ice::Double& result, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_invokeToDouble(result, e, _iceI_begin_invokeToDouble(ruleID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToDouble(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_invokeToDouble(ruleID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param ruleID 规则ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToDouble(const ::std::string& ruleID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToDouble(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToDouble(const ::std::string& ruleID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToDouble(ruleID, context, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToDouble(const ::std::string& ruleID, const ::ZG6000::Callback_ZGSPScriptProcess_invokeToDoublePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToDouble(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToDouble(const ::std::string& ruleID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_invokeToDoublePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToDouble(ruleID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_invokeToDouble.
     * @param result_ The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_invokeToDouble(::Ice::Double& result, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result_);
    /// \cond INTERNAL

    void _iceI_end_invokeToDouble(::Ice::Double& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_invokeToDouble(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    bool invokeToString(const ::std::string& ruleID, ::std::string& result, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_invokeToString(result, e, _iceI_begin_invokeToString(ruleID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToString(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_invokeToString(ruleID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param ruleID 规则ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToString(const ::std::string& ruleID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToString(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToString(const ::std::string& ruleID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToString(ruleID, context, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToString(const ::std::string& ruleID, const ::ZG6000::Callback_ZGSPScriptProcess_invokeToStringPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToString(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param ruleID 规则ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToString(const ::std::string& ruleID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_invokeToStringPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToString(ruleID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_invokeToString.
     * @param result_ The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_invokeToString(::std::string& result, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result_);
    /// \cond INTERNAL

    void _iceI_end_invokeToString(::std::string& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_invokeToString(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    bool invokeToStringList(const ::std::string& ruleID, ::ZG6000::StringList& result, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_invokeToStringList(result, e, _iceI_begin_invokeToStringList(ruleID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToStringList(const ::std::string& ruleID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_invokeToStringList(ruleID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToStringList(const ::std::string& ruleID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToStringList(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToStringList(const ::std::string& ruleID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToStringList(ruleID, context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToStringList(const ::std::string& ruleID, const ::ZG6000::Callback_ZGSPScriptProcess_invokeToStringListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToStringList(ruleID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_invokeToStringList(const ::std::string& ruleID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_invokeToStringListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_invokeToStringList(ruleID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_invokeToStringList.
     * @param result_ The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_invokeToStringList(::ZG6000::StringList& result, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result_);
    /// \cond INTERNAL

    void _iceI_end_invokeToStringList(::ZG6000::StringList& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_invokeToStringList(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param listExpress 表达式列表
     * @param context The Context map to send with the invocation.
     * @brief   批量调用表达式(单向调用)
     */
    void callBatch(const ::ZG6000::ListStringMap& listExpress, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_callBatch(_iceI_begin_callBatch(listExpress, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param listExpress 表达式列表
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   批量调用表达式(单向调用)
     */
    ::Ice::AsyncResultPtr begin_callBatch(const ::ZG6000::ListStringMap& listExpress, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_callBatch(listExpress, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param listExpress 表达式列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   批量调用表达式(单向调用)
     */
    ::Ice::AsyncResultPtr begin_callBatch(const ::ZG6000::ListStringMap& listExpress, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callBatch(listExpress, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listExpress 表达式列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   批量调用表达式(单向调用)
     */
    ::Ice::AsyncResultPtr begin_callBatch(const ::ZG6000::ListStringMap& listExpress, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callBatch(listExpress, context, cb, cookie);
    }

    /**
     * @param listExpress 表达式列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   批量调用表达式(单向调用)
     */
    ::Ice::AsyncResultPtr begin_callBatch(const ::ZG6000::ListStringMap& listExpress, const ::ZG6000::Callback_ZGSPScriptProcess_callBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callBatch(listExpress, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listExpress 表达式列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   批量调用表达式(单向调用)
     */
    ::Ice::AsyncResultPtr begin_callBatch(const ::ZG6000::ListStringMap& listExpress, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_callBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callBatch(listExpress, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_callBatch.
     * @param result The asynchronous result object for the invocation.
     */
    void end_callBatch(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_callBatch(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果
     */
    bool callJson(const ::std::string& expressID, const ::std::string& jsonParam, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_callJson(e, _iceI_begin_callJson(expressID, jsonParam, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果
     */
    ::Ice::AsyncResultPtr begin_callJson(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_callJson(expressID, jsonParam, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param expressID 表达式ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果
     */
    ::Ice::AsyncResultPtr begin_callJson(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callJson(expressID, jsonParam, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果
     */
    ::Ice::AsyncResultPtr begin_callJson(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callJson(expressID, jsonParam, context, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果
     */
    ::Ice::AsyncResultPtr begin_callJson(const ::std::string& expressID, const ::std::string& jsonParam, const ::ZG6000::Callback_ZGSPScriptProcess_callJsonPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callJson(expressID, jsonParam, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果
     */
    ::Ice::AsyncResultPtr begin_callJson(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_callJsonPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callJson(expressID, jsonParam, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_callJson.
     * @param result The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_callJson(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_callJson(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_callJson(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    bool callToBool(const ::std::string& expressID, const ::std::string& jsonParam, bool& result, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_callToBool(result, e, _iceI_begin_callToBool(expressID, jsonParam, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToBool(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_callToBool(expressID, jsonParam, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param expressID 表达式ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToBool(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToBool(expressID, jsonParam, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToBool(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToBool(expressID, jsonParam, context, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToBool(const ::std::string& expressID, const ::std::string& jsonParam, const ::ZG6000::Callback_ZGSPScriptProcess_callToBoolPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToBool(expressID, jsonParam, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToBool(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_callToBoolPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToBool(expressID, jsonParam, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_callToBool.
     * @param result_ The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_callToBool(bool& result, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result_);
    /// \cond INTERNAL

    void _iceI_end_callToBool(bool& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_callToBool(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    bool callToInt(const ::std::string& expressID, const ::std::string& jsonParam, ::Ice::Int& result, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_callToInt(result, e, _iceI_begin_callToInt(expressID, jsonParam, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToInt(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_callToInt(expressID, jsonParam, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param expressID 表达式ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToInt(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToInt(expressID, jsonParam, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToInt(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToInt(expressID, jsonParam, context, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToInt(const ::std::string& expressID, const ::std::string& jsonParam, const ::ZG6000::Callback_ZGSPScriptProcess_callToIntPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToInt(expressID, jsonParam, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToInt(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_callToIntPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToInt(expressID, jsonParam, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_callToInt.
     * @param result_ The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_callToInt(::Ice::Int& result, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result_);
    /// \cond INTERNAL

    void _iceI_end_callToInt(::Ice::Int& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_callToInt(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    bool callToDouble(const ::std::string& expressID, const ::std::string& jsonParam, ::Ice::Double& result, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_callToDouble(result, e, _iceI_begin_callToDouble(expressID, jsonParam, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToDouble(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_callToDouble(expressID, jsonParam, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param expressID 表达式ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToDouble(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToDouble(expressID, jsonParam, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToDouble(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToDouble(expressID, jsonParam, context, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToDouble(const ::std::string& expressID, const ::std::string& jsonParam, const ::ZG6000::Callback_ZGSPScriptProcess_callToDoublePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToDouble(expressID, jsonParam, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToDouble(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_callToDoublePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToDouble(expressID, jsonParam, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_callToDouble.
     * @param result_ The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_callToDouble(::Ice::Double& result, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result_);
    /// \cond INTERNAL

    void _iceI_end_callToDouble(::Ice::Double& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_callToDouble(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    bool callToString(const ::std::string& expressID, const ::std::string& jsonParam, ::std::string& result, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_callToString(result, e, _iceI_begin_callToString(expressID, jsonParam, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToString(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_callToString(expressID, jsonParam, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param expressID 表达式ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToString(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToString(expressID, jsonParam, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToString(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToString(expressID, jsonParam, context, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToString(const ::std::string& expressID, const ::std::string& jsonParam, const ::ZG6000::Callback_ZGSPScriptProcess_callToStringPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToString(expressID, jsonParam, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param expressID 表达式ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    ::Ice::AsyncResultPtr begin_callToString(const ::std::string& expressID, const ::std::string& jsonParam, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPScriptProcess_callToStringPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_callToString(expressID, jsonParam, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_callToString.
     * @param result_ The asynchronous result object for the invocation.
     * @return 调用成功返回true，失败返回false。
     */
    bool end_callToString(::std::string& result, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result_);
    /// \cond INTERNAL

    void _iceI_end_callToString(::std::string& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_callToString(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPScriptProcess : virtual public ZGServerBase
{
public:

    typedef ZGSPScriptProcessPrx ProxyType;
    typedef ZGSPScriptProcessPtr PointerType;

    virtual ~ZGSPScriptProcess();

#ifdef ICE_CPP11_COMPILER
    ZGSPScriptProcess() = default;
    ZGSPScriptProcess(const ZGSPScriptProcess&) = default;
    ZGSPScriptProcess& operator=(const ZGSPScriptProcess&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   评估函数语法是否正确
     */
    virtual bool evaluateByJson(const ::std::string& funcName, const ::std::string& jsonParam, const ::std::string& script, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_evaluateByJson(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param funcName 函数名称
     * @param script 脚本内容
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   评估函数语法是否正确
     */
    virtual bool evaluateByList(const ::std::string& funcName, const StringList& listParam, const ::std::string& script, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_evaluateByList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listRuleID 规则ID列表
     * @param current The Current object for the invocation.
     * @return 单向调用，不返回执行结果
     * @brief   批量调用规则
     */
    virtual void invokeOneway(const StringList& listRuleID, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeOneway(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param ruleID 规则ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，并返回执行结果
     */
    virtual bool invoke(const ::std::string& ruleID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_invoke(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param ruleID 规则ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    virtual bool invokeToBool(const ::std::string& ruleID, bool& result, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeToBool(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param ruleID 规则ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    virtual bool invokeToInt(const ::std::string& ruleID, ::Ice::Int& result, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeToInt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param ruleID 规则ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    virtual bool invokeToDouble(const ::std::string& ruleID, ::Ice::Double& result, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeToDouble(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param ruleID 规则ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    virtual bool invokeToString(const ::std::string& ruleID, ::std::string& result, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeToString(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的规则，返回执行结果和返回值
     */
    virtual bool invokeToStringList(const ::std::string& ruleID, StringList& result, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_invokeToStringList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listExpress 表达式列表
     * @param current The Current object for the invocation.
     * @brief   批量调用表达式(单向调用)
     */
    virtual void callBatch(const ListStringMap& listExpress, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_callBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param expressID 表达式ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果
     */
    virtual bool callJson(const ::std::string& expressID, const ::std::string& jsonParam, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_callJson(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param expressID 表达式ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    virtual bool callToBool(const ::std::string& expressID, const ::std::string& jsonParam, bool& result, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_callToBool(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param expressID 表达式ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    virtual bool callToInt(const ::std::string& expressID, const ::std::string& jsonParam, ::Ice::Int& result, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_callToInt(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param expressID 表达式ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    virtual bool callToDouble(const ::std::string& expressID, const ::std::string& jsonParam, ::Ice::Double& result, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_callToDouble(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param expressID 表达式ID
     * @param current The Current object for the invocation.
     * @return 调用成功返回true，失败返回false。
     * @brief   调用指定的表达式，并返回执行结果和返回值
     */
    virtual bool callToString(const ::std::string& expressID, const ::std::string& jsonParam, ::std::string& result, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_callToString(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPScriptProcess& lhs, const ZGSPScriptProcess& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPScriptProcess& lhs, const ZGSPScriptProcess& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByJson.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_evaluateByJson.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_evaluateByJson : public Callback_ZGSPScriptProcess_evaluateByJson_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_evaluateByJson(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_evaluateByJson(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByJson.
 */
template<class T> Callback_ZGSPScriptProcess_evaluateByJsonPtr
newCallback_ZGSPScriptProcess_evaluateByJson(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_evaluateByJson<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByJson.
 */
template<class T> Callback_ZGSPScriptProcess_evaluateByJsonPtr
newCallback_ZGSPScriptProcess_evaluateByJson(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_evaluateByJson<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByJson.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_evaluateByJson.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_evaluateByJson : public Callback_ZGSPScriptProcess_evaluateByJson_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_evaluateByJson(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_evaluateByJson(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByJson.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_evaluateByJsonPtr
newCallback_ZGSPScriptProcess_evaluateByJson(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_evaluateByJson<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByJson.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_evaluateByJsonPtr
newCallback_ZGSPScriptProcess_evaluateByJson(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_evaluateByJson<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_evaluateByList.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_evaluateByList : public Callback_ZGSPScriptProcess_evaluateByList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_evaluateByList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_evaluateByList(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByList.
 */
template<class T> Callback_ZGSPScriptProcess_evaluateByListPtr
newCallback_ZGSPScriptProcess_evaluateByList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_evaluateByList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByList.
 */
template<class T> Callback_ZGSPScriptProcess_evaluateByListPtr
newCallback_ZGSPScriptProcess_evaluateByList(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_evaluateByList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_evaluateByList.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_evaluateByList : public Callback_ZGSPScriptProcess_evaluateByList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_evaluateByList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_evaluateByList(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByList.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_evaluateByListPtr
newCallback_ZGSPScriptProcess_evaluateByList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_evaluateByList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_evaluateByList.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_evaluateByListPtr
newCallback_ZGSPScriptProcess_evaluateByList(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_evaluateByList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeOneway.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_invokeOneway : public Callback_ZGSPScriptProcess_invokeOneway_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPScriptProcess_invokeOneway(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 */
template<class T> Callback_ZGSPScriptProcess_invokeOnewayPtr
newCallback_ZGSPScriptProcess_invokeOneway(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeOneway<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 */
template<class T> Callback_ZGSPScriptProcess_invokeOnewayPtr
newCallback_ZGSPScriptProcess_invokeOneway(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeOneway<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 */
template<class T> Callback_ZGSPScriptProcess_invokeOnewayPtr
newCallback_ZGSPScriptProcess_invokeOneway(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeOneway<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 */
template<class T> Callback_ZGSPScriptProcess_invokeOnewayPtr
newCallback_ZGSPScriptProcess_invokeOneway(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeOneway<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeOneway.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_invokeOneway : public Callback_ZGSPScriptProcess_invokeOneway_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPScriptProcess_invokeOneway(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeOnewayPtr
newCallback_ZGSPScriptProcess_invokeOneway(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeOneway<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeOnewayPtr
newCallback_ZGSPScriptProcess_invokeOneway(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeOneway<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeOnewayPtr
newCallback_ZGSPScriptProcess_invokeOneway(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeOneway<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeOneway.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeOnewayPtr
newCallback_ZGSPScriptProcess_invokeOneway(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeOneway<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invoke.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invoke.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_invoke : public Callback_ZGSPScriptProcess_invoke_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_invoke(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invoke(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invoke.
 */
template<class T> Callback_ZGSPScriptProcess_invokePtr
newCallback_ZGSPScriptProcess_invoke(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invoke<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invoke.
 */
template<class T> Callback_ZGSPScriptProcess_invokePtr
newCallback_ZGSPScriptProcess_invoke(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invoke<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invoke.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invoke.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_invoke : public Callback_ZGSPScriptProcess_invoke_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_invoke(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invoke(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invoke.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokePtr
newCallback_ZGSPScriptProcess_invoke(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invoke<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invoke.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokePtr
newCallback_ZGSPScriptProcess_invoke(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invoke<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToBool.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToBool.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_invokeToBool : public Callback_ZGSPScriptProcess_invokeToBool_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_invokeToBool(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        bool iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invokeToBool(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_result, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToBool.
 */
template<class T> Callback_ZGSPScriptProcess_invokeToBoolPtr
newCallback_ZGSPScriptProcess_invokeToBool(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeToBool<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToBool.
 */
template<class T> Callback_ZGSPScriptProcess_invokeToBoolPtr
newCallback_ZGSPScriptProcess_invokeToBool(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeToBool<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToBool.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToBool.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_invokeToBool : public Callback_ZGSPScriptProcess_invokeToBool_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_invokeToBool(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        bool iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invokeToBool(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_result, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToBool.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeToBoolPtr
newCallback_ZGSPScriptProcess_invokeToBool(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeToBool<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToBool.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeToBoolPtr
newCallback_ZGSPScriptProcess_invokeToBool(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeToBool<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToInt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToInt.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_invokeToInt : public Callback_ZGSPScriptProcess_invokeToInt_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_invokeToInt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invokeToInt(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_result, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToInt.
 */
template<class T> Callback_ZGSPScriptProcess_invokeToIntPtr
newCallback_ZGSPScriptProcess_invokeToInt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeToInt<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToInt.
 */
template<class T> Callback_ZGSPScriptProcess_invokeToIntPtr
newCallback_ZGSPScriptProcess_invokeToInt(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeToInt<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToInt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToInt.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_invokeToInt : public Callback_ZGSPScriptProcess_invokeToInt_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_invokeToInt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invokeToInt(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_result, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToInt.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeToIntPtr
newCallback_ZGSPScriptProcess_invokeToInt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeToInt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToInt.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeToIntPtr
newCallback_ZGSPScriptProcess_invokeToInt(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeToInt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToDouble.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToDouble.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_invokeToDouble : public Callback_ZGSPScriptProcess_invokeToDouble_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Double, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_invokeToDouble(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::Ice::Double iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invokeToDouble(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_result, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToDouble.
 */
template<class T> Callback_ZGSPScriptProcess_invokeToDoublePtr
newCallback_ZGSPScriptProcess_invokeToDouble(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Double, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeToDouble<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToDouble.
 */
template<class T> Callback_ZGSPScriptProcess_invokeToDoublePtr
newCallback_ZGSPScriptProcess_invokeToDouble(T* instance, void (T::*cb)(bool, ::Ice::Double, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeToDouble<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToDouble.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToDouble.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_invokeToDouble : public Callback_ZGSPScriptProcess_invokeToDouble_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Double, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_invokeToDouble(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::Ice::Double iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invokeToDouble(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_result, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToDouble.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeToDoublePtr
newCallback_ZGSPScriptProcess_invokeToDouble(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Double, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeToDouble<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToDouble.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeToDoublePtr
newCallback_ZGSPScriptProcess_invokeToDouble(T* instance, void (T::*cb)(bool, ::Ice::Double, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeToDouble<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToString.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToString.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_invokeToString : public Callback_ZGSPScriptProcess_invokeToString_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_invokeToString(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invokeToString(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_result, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToString.
 */
template<class T> Callback_ZGSPScriptProcess_invokeToStringPtr
newCallback_ZGSPScriptProcess_invokeToString(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeToString<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToString.
 */
template<class T> Callback_ZGSPScriptProcess_invokeToStringPtr
newCallback_ZGSPScriptProcess_invokeToString(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeToString<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToString.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToString.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_invokeToString : public Callback_ZGSPScriptProcess_invokeToString_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_invokeToString(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invokeToString(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_result, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToString.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeToStringPtr
newCallback_ZGSPScriptProcess_invokeToString(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeToString<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToString.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeToStringPtr
newCallback_ZGSPScriptProcess_invokeToString(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeToString<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToStringList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToStringList.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_invokeToStringList : public Callback_ZGSPScriptProcess_invokeToStringList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringList&, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_invokeToStringList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        StringList iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invokeToStringList(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_result, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToStringList.
 */
template<class T> Callback_ZGSPScriptProcess_invokeToStringListPtr
newCallback_ZGSPScriptProcess_invokeToStringList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeToStringList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToStringList.
 */
template<class T> Callback_ZGSPScriptProcess_invokeToStringListPtr
newCallback_ZGSPScriptProcess_invokeToStringList(T* instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_invokeToStringList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToStringList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_invokeToStringList.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_invokeToStringList : public Callback_ZGSPScriptProcess_invokeToStringList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringList&, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_invokeToStringList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        StringList iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_invokeToStringList(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_result, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToStringList.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeToStringListPtr
newCallback_ZGSPScriptProcess_invokeToStringList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeToStringList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_invokeToStringList.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_invokeToStringListPtr
newCallback_ZGSPScriptProcess_invokeToStringList(T* instance, void (T::*cb)(bool, const StringList&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_invokeToStringList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callBatch.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_callBatch : public Callback_ZGSPScriptProcess_callBatch_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPScriptProcess_callBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 */
template<class T> Callback_ZGSPScriptProcess_callBatchPtr
newCallback_ZGSPScriptProcess_callBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 */
template<class T> Callback_ZGSPScriptProcess_callBatchPtr
newCallback_ZGSPScriptProcess_callBatch(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callBatch<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 */
template<class T> Callback_ZGSPScriptProcess_callBatchPtr
newCallback_ZGSPScriptProcess_callBatch(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 */
template<class T> Callback_ZGSPScriptProcess_callBatchPtr
newCallback_ZGSPScriptProcess_callBatch(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callBatch<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callBatch.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_callBatch : public Callback_ZGSPScriptProcess_callBatch_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPScriptProcess_callBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callBatchPtr
newCallback_ZGSPScriptProcess_callBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callBatchPtr
newCallback_ZGSPScriptProcess_callBatch(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callBatch<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callBatchPtr
newCallback_ZGSPScriptProcess_callBatch(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callBatch.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callBatchPtr
newCallback_ZGSPScriptProcess_callBatch(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callBatch<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callJson.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callJson.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_callJson : public Callback_ZGSPScriptProcess_callJson_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_callJson(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_callJson(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callJson.
 */
template<class T> Callback_ZGSPScriptProcess_callJsonPtr
newCallback_ZGSPScriptProcess_callJson(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callJson<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callJson.
 */
template<class T> Callback_ZGSPScriptProcess_callJsonPtr
newCallback_ZGSPScriptProcess_callJson(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callJson<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callJson.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callJson.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_callJson : public Callback_ZGSPScriptProcess_callJson_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_callJson(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_callJson(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callJson.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callJsonPtr
newCallback_ZGSPScriptProcess_callJson(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callJson<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callJson.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callJsonPtr
newCallback_ZGSPScriptProcess_callJson(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callJson<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToBool.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToBool.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_callToBool : public Callback_ZGSPScriptProcess_callToBool_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_callToBool(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        bool iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_callToBool(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_result, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToBool.
 */
template<class T> Callback_ZGSPScriptProcess_callToBoolPtr
newCallback_ZGSPScriptProcess_callToBool(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callToBool<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToBool.
 */
template<class T> Callback_ZGSPScriptProcess_callToBoolPtr
newCallback_ZGSPScriptProcess_callToBool(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callToBool<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToBool.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToBool.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_callToBool : public Callback_ZGSPScriptProcess_callToBool_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_callToBool(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        bool iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_callToBool(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_result, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToBool.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callToBoolPtr
newCallback_ZGSPScriptProcess_callToBool(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callToBool<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToBool.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callToBoolPtr
newCallback_ZGSPScriptProcess_callToBool(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callToBool<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToInt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToInt.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_callToInt : public Callback_ZGSPScriptProcess_callToInt_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_callToInt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_callToInt(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_result, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToInt.
 */
template<class T> Callback_ZGSPScriptProcess_callToIntPtr
newCallback_ZGSPScriptProcess_callToInt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callToInt<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToInt.
 */
template<class T> Callback_ZGSPScriptProcess_callToIntPtr
newCallback_ZGSPScriptProcess_callToInt(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callToInt<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToInt.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToInt.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_callToInt : public Callback_ZGSPScriptProcess_callToInt_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_callToInt(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_callToInt(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_result, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToInt.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callToIntPtr
newCallback_ZGSPScriptProcess_callToInt(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callToInt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToInt.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callToIntPtr
newCallback_ZGSPScriptProcess_callToInt(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callToInt<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToDouble.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToDouble.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_callToDouble : public Callback_ZGSPScriptProcess_callToDouble_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Double, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_callToDouble(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::Ice::Double iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_callToDouble(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_result, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToDouble.
 */
template<class T> Callback_ZGSPScriptProcess_callToDoublePtr
newCallback_ZGSPScriptProcess_callToDouble(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Double, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callToDouble<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToDouble.
 */
template<class T> Callback_ZGSPScriptProcess_callToDoublePtr
newCallback_ZGSPScriptProcess_callToDouble(T* instance, void (T::*cb)(bool, ::Ice::Double, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callToDouble<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToDouble.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToDouble.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_callToDouble : public Callback_ZGSPScriptProcess_callToDouble_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Double, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_callToDouble(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::Ice::Double iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_callToDouble(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_result, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToDouble.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callToDoublePtr
newCallback_ZGSPScriptProcess_callToDouble(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Double, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callToDouble<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToDouble.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callToDoublePtr
newCallback_ZGSPScriptProcess_callToDouble(T* instance, void (T::*cb)(bool, ::Ice::Double, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callToDouble<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToString.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToString.
 */
template<class T>
class CallbackNC_ZGSPScriptProcess_callToString : public Callback_ZGSPScriptProcess_callToString_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPScriptProcess_callToString(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_callToString(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_result, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToString.
 */
template<class T> Callback_ZGSPScriptProcess_callToStringPtr
newCallback_ZGSPScriptProcess_callToString(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callToString<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToString.
 */
template<class T> Callback_ZGSPScriptProcess_callToStringPtr
newCallback_ZGSPScriptProcess_callToString(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPScriptProcess_callToString<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPScriptProcess::begin_callToString.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPScriptProcess_callToString.
 */
template<class T, typename CT>
class Callback_ZGSPScriptProcess_callToString : public Callback_ZGSPScriptProcess_callToString_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPScriptProcess_callToString(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPScriptProcessPrx proxy = ZGSPScriptProcessPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_result;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_callToString(iceP_result, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_result, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToString.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callToStringPtr
newCallback_ZGSPScriptProcess_callToString(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callToString<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPScriptProcess::begin_callToString.
 */
template<class T, typename CT> Callback_ZGSPScriptProcess_callToStringPtr
newCallback_ZGSPScriptProcess_callToString(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPScriptProcess_callToString<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

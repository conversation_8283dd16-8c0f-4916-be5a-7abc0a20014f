//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPWPManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGOPWPManager_h__
#define __ZGOPWPManager_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGOPWPManager;
class ZGOPWPManagerPrx;

}

namespace ZG6000
{

class ZGOPWPManager : public virtual ZGServerBase
{
public:

    using ProxyType = ZGOPWPManagerPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getWPTaskList.
     */
    struct GetWPTaskListResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 请销点任务列表 */
        ListStringMap listTask;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param listTask 请销点任务列表
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取请销点任务列表
     */
    virtual bool getWPTaskList(StringMap param, ListStringMap& listTask, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getWPTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to createWPTask.
     */
    struct CreateWPTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ::std::string taskID;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    virtual bool createWPTask(StringMap param, StringList listUserID, ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createWPTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to editWPTask.
     */
    struct EditWPTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 编辑请销点任务
     */
    virtual bool editWPTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_editWPTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to editWPUser.
     */
    struct EditWPUserResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    virtual bool editWPUser(::std::string taskID, StringList listUserID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_editWPUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deleteWPTask.
     */
    struct DeleteWPTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 删除请销点任务
     * @param 任务ID
     */
    virtual bool deleteWPTask(::std::string taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteWPTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to abolishWPTask.
     */
    struct AbolishWPTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 作废请销点任务
     */
    virtual bool abolishWPTask(::std::string taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_abolishWPTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to confirmTask.
     */
    struct ConfirmTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 确认请销点任务
     */
    virtual bool confirmTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to backTask.
     */
    struct BackTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 回退请销点任务
     */
    virtual bool backTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_backTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getWPUser.
     */
    struct GetWPUserResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 人员列表 */
        ListStringMap listUser;
        /** 执行失败时的错误描述 */
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param listUser 人员列表
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取请销点人员
     */
    virtual bool getWPUser(::std::string taskID, ListStringMap& listUser, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getWPUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGOPWPManagerPrx : public virtual ::Ice::Proxy<ZGOPWPManagerPrx, ZGServerBasePrx>
{
public:

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param listTask 请销点任务列表
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取请销点任务列表
     */
    bool getWPTaskList(const StringMap& param, ListStringMap& listTask, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPWPManager::GetWPTaskListResult>(true, this, &ZGOPWPManagerPrx::_iceI_getWPTaskList, param, context).get();
        listTask = ::std::move(_result.listTask);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 获取请销点任务列表
     */
    template<template<typename> class P = ::std::promise>
    auto getWPTaskListAsync(const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPWPManager::GetWPTaskListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPWPManager::GetWPTaskListResult, P>(false, this, &ZGOPWPManagerPrx::_iceI_getWPTaskList, param, context);
    }

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 获取请销点任务列表
     */
    ::std::function<void()>
    getWPTaskListAsync(const StringMap& param,
                       ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPWPManager::GetWPTaskListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listTask), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPWPManager::GetWPTaskListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPWPManagerPrx::_iceI_getWPTaskList, param, context);
    }

    /// \cond INTERNAL
    void _iceI_getWPTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::GetWPTaskListResult>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    bool createWPTask(const StringMap& param, const StringList& listUserID, ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPWPManager::CreateWPTaskResult>(true, this, &ZGOPWPManagerPrx::_iceI_createWPTask, param, listUserID, context).get();
        taskID = ::std::move(_result.taskID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    template<template<typename> class P = ::std::promise>
    auto createWPTaskAsync(const StringMap& param, const StringList& listUserID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPWPManager::CreateWPTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPWPManager::CreateWPTaskResult, P>(false, this, &ZGOPWPManagerPrx::_iceI_createWPTask, param, listUserID, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    ::std::function<void()>
    createWPTaskAsync(const StringMap& param, const StringList& listUserID,
                      ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPWPManager::CreateWPTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.taskID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPWPManager::CreateWPTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPWPManagerPrx::_iceI_createWPTask, param, listUserID, context);
    }

    /// \cond INTERNAL
    void _iceI_createWPTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::CreateWPTaskResult>>&, const StringMap&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 编辑请销点任务
     */
    bool editWPTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPWPManager::EditWPTaskResult>(true, this, &ZGOPWPManagerPrx::_iceI_editWPTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 编辑请销点任务
     */
    template<template<typename> class P = ::std::promise>
    auto editWPTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPWPManager::EditWPTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPWPManager::EditWPTaskResult, P>(false, this, &ZGOPWPManagerPrx::_iceI_editWPTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 编辑请销点任务
     */
    ::std::function<void()>
    editWPTaskAsync(const ::std::string& taskID, const StringMap& param,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPWPManager::EditWPTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPWPManager::EditWPTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPWPManagerPrx::_iceI_editWPTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_editWPTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::EditWPTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    bool editWPUser(const ::std::string& taskID, const StringList& listUserID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPWPManager::EditWPUserResult>(true, this, &ZGOPWPManagerPrx::_iceI_editWPUser, taskID, listUserID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    template<template<typename> class P = ::std::promise>
    auto editWPUserAsync(const ::std::string& taskID, const StringList& listUserID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPWPManager::EditWPUserResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPWPManager::EditWPUserResult, P>(false, this, &ZGOPWPManagerPrx::_iceI_editWPUser, taskID, listUserID, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    ::std::function<void()>
    editWPUserAsync(const ::std::string& taskID, const StringList& listUserID,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPWPManager::EditWPUserResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPWPManager::EditWPUserResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPWPManagerPrx::_iceI_editWPUser, taskID, listUserID, context);
    }

    /// \cond INTERNAL
    void _iceI_editWPUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::EditWPUserResult>>&, const ::std::string&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 删除请销点任务
     * @param 任务ID
     */
    bool deleteWPTask(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPWPManager::DeleteWPTaskResult>(true, this, &ZGOPWPManagerPrx::_iceI_deleteWPTask, taskID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 删除请销点任务
     * @param 任务ID
     */
    template<template<typename> class P = ::std::promise>
    auto deleteWPTaskAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPWPManager::DeleteWPTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPWPManager::DeleteWPTaskResult, P>(false, this, &ZGOPWPManagerPrx::_iceI_deleteWPTask, taskID, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 删除请销点任务
     * @param 任务ID
     */
    ::std::function<void()>
    deleteWPTaskAsync(const ::std::string& taskID,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPWPManager::DeleteWPTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPWPManager::DeleteWPTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPWPManagerPrx::_iceI_deleteWPTask, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_deleteWPTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::DeleteWPTaskResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 作废请销点任务
     */
    bool abolishWPTask(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPWPManager::AbolishWPTaskResult>(true, this, &ZGOPWPManagerPrx::_iceI_abolishWPTask, taskID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 作废请销点任务
     */
    template<template<typename> class P = ::std::promise>
    auto abolishWPTaskAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPWPManager::AbolishWPTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPWPManager::AbolishWPTaskResult, P>(false, this, &ZGOPWPManagerPrx::_iceI_abolishWPTask, taskID, context);
    }

    /**
     * @param taskID 任务ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 作废请销点任务
     */
    ::std::function<void()>
    abolishWPTaskAsync(const ::std::string& taskID,
                       ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPWPManager::AbolishWPTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPWPManager::AbolishWPTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPWPManagerPrx::_iceI_abolishWPTask, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_abolishWPTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::AbolishWPTaskResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 确认请销点任务
     */
    bool confirmTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPWPManager::ConfirmTaskResult>(true, this, &ZGOPWPManagerPrx::_iceI_confirmTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 确认请销点任务
     */
    template<template<typename> class P = ::std::promise>
    auto confirmTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPWPManager::ConfirmTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPWPManager::ConfirmTaskResult, P>(false, this, &ZGOPWPManagerPrx::_iceI_confirmTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 确认请销点任务
     */
    ::std::function<void()>
    confirmTaskAsync(const ::std::string& taskID, const StringMap& param,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPWPManager::ConfirmTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPWPManager::ConfirmTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPWPManagerPrx::_iceI_confirmTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_confirmTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::ConfirmTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 回退请销点任务
     */
    bool backTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPWPManager::BackTaskResult>(true, this, &ZGOPWPManagerPrx::_iceI_backTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 回退请销点任务
     */
    template<template<typename> class P = ::std::promise>
    auto backTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPWPManager::BackTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPWPManager::BackTaskResult, P>(false, this, &ZGOPWPManagerPrx::_iceI_backTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 回退请销点任务
     */
    ::std::function<void()>
    backTaskAsync(const ::std::string& taskID, const StringMap& param,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPWPManager::BackTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPWPManager::BackTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPWPManagerPrx::_iceI_backTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_backTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::BackTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param listUser 人员列表
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取请销点人员
     */
    bool getWPUser(const ::std::string& taskID, ListStringMap& listUser, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPWPManager::GetWPUserResult>(true, this, &ZGOPWPManagerPrx::_iceI_getWPUser, taskID, context).get();
        listUser = ::std::move(_result.listUser);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief 获取请销点人员
     */
    template<template<typename> class P = ::std::promise>
    auto getWPUserAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPWPManager::GetWPUserResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPWPManager::GetWPUserResult, P>(false, this, &ZGOPWPManagerPrx::_iceI_getWPUser, taskID, context);
    }

    /**
     * @param taskID 任务ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief 获取请销点人员
     */
    ::std::function<void()>
    getWPUserAsync(const ::std::string& taskID,
                   ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPWPManager::GetWPUserResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listUser), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPWPManager::GetWPUserResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPWPManagerPrx::_iceI_getWPUser, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getWPUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::GetWPUserResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGOPWPManagerPrx() = default;
    friend ::std::shared_ptr<ZGOPWPManagerPrx> IceInternal::createProxy<ZGOPWPManagerPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGOPWPManagerPtr = ::std::shared_ptr<ZGOPWPManager>;
using ZGOPWPManagerPrxPtr = ::std::shared_ptr<ZGOPWPManagerPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGOPWPManager;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGOPWPManager>&);
::IceProxy::Ice::Object* upCast(ZGOPWPManager*);
/// \endcond

}

}

namespace ZG6000
{

class ZGOPWPManager;
/// \cond INTERNAL
::Ice::Object* upCast(ZGOPWPManager*);
/// \endcond
typedef ::IceInternal::Handle< ZGOPWPManager> ZGOPWPManagerPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGOPWPManager> ZGOPWPManagerPrx;
typedef ZGOPWPManagerPrx ZGOPWPManagerPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGOPWPManagerPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_getWPTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_getWPTaskList.
 */
class Callback_ZGOPWPManager_getWPTaskList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPWPManager_getWPTaskList_Base> Callback_ZGOPWPManager_getWPTaskListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_createWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_createWPTask.
 */
class Callback_ZGOPWPManager_createWPTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPWPManager_createWPTask_Base> Callback_ZGOPWPManager_createWPTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_editWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_editWPTask.
 */
class Callback_ZGOPWPManager_editWPTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPWPManager_editWPTask_Base> Callback_ZGOPWPManager_editWPTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_editWPUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_editWPUser.
 */
class Callback_ZGOPWPManager_editWPUser_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPWPManager_editWPUser_Base> Callback_ZGOPWPManager_editWPUserPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_deleteWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_deleteWPTask.
 */
class Callback_ZGOPWPManager_deleteWPTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPWPManager_deleteWPTask_Base> Callback_ZGOPWPManager_deleteWPTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_abolishWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_abolishWPTask.
 */
class Callback_ZGOPWPManager_abolishWPTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPWPManager_abolishWPTask_Base> Callback_ZGOPWPManager_abolishWPTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_confirmTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_confirmTask.
 */
class Callback_ZGOPWPManager_confirmTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPWPManager_confirmTask_Base> Callback_ZGOPWPManager_confirmTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_backTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_backTask.
 */
class Callback_ZGOPWPManager_backTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPWPManager_backTask_Base> Callback_ZGOPWPManager_backTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_getWPUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_getWPUser.
 */
class Callback_ZGOPWPManager_getWPUser_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPWPManager_getWPUser_Base> Callback_ZGOPWPManager_getWPUserPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGOPWPManager : public virtual ::Ice::Proxy<ZGOPWPManager, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param listTask 请销点任务列表
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取请销点任务列表
     */
    bool getWPTaskList(const ::ZG6000::StringMap& param, ::ZG6000::ListStringMap& listTask, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getWPTaskList(listTask, e, _iceI_begin_getWPTaskList(param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取请销点任务列表
     */
    ::Ice::AsyncResultPtr begin_getWPTaskList(const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getWPTaskList(param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取请销点任务列表
     */
    ::Ice::AsyncResultPtr begin_getWPTaskList(const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWPTaskList(param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取请销点任务列表
     */
    ::Ice::AsyncResultPtr begin_getWPTaskList(const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWPTaskList(param, context, cb, cookie);
    }

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取请销点任务列表
     */
    ::Ice::AsyncResultPtr begin_getWPTaskList(const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPWPManager_getWPTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWPTaskList(param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取请销点任务列表
     */
    ::Ice::AsyncResultPtr begin_getWPTaskList(const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPWPManager_getWPTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWPTaskList(param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getWPTaskList.
     * @param listTask 请销点任务列表
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getWPTaskList(::ZG6000::ListStringMap& listTask, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getWPTaskList(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getWPTaskList(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    bool createWPTask(const ::ZG6000::StringMap& param, const ::ZG6000::StringList& listUserID, ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createWPTask(taskID, e, _iceI_begin_createWPTask(param, listUserID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    ::Ice::AsyncResultPtr begin_createWPTask(const ::ZG6000::StringMap& param, const ::ZG6000::StringList& listUserID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createWPTask(param, listUserID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    ::Ice::AsyncResultPtr begin_createWPTask(const ::ZG6000::StringMap& param, const ::ZG6000::StringList& listUserID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createWPTask(param, listUserID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    ::Ice::AsyncResultPtr begin_createWPTask(const ::ZG6000::StringMap& param, const ::ZG6000::StringList& listUserID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createWPTask(param, listUserID, context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    ::Ice::AsyncResultPtr begin_createWPTask(const ::ZG6000::StringMap& param, const ::ZG6000::StringList& listUserID, const ::ZG6000::Callback_ZGOPWPManager_createWPTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createWPTask(param, listUserID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    ::Ice::AsyncResultPtr begin_createWPTask(const ::ZG6000::StringMap& param, const ::ZG6000::StringList& listUserID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPWPManager_createWPTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createWPTask(param, listUserID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_createWPTask.
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_createWPTask(::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createWPTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createWPTask(const ::ZG6000::StringMap&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 编辑请销点任务
     */
    bool editWPTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_editWPTask(e, _iceI_begin_editWPTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 编辑请销点任务
     */
    ::Ice::AsyncResultPtr begin_editWPTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_editWPTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 编辑请销点任务
     */
    ::Ice::AsyncResultPtr begin_editWPTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editWPTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 编辑请销点任务
     */
    ::Ice::AsyncResultPtr begin_editWPTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editWPTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 编辑请销点任务
     */
    ::Ice::AsyncResultPtr begin_editWPTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPWPManager_editWPTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editWPTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 编辑请销点任务
     */
    ::Ice::AsyncResultPtr begin_editWPTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPWPManager_editWPTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editWPTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_editWPTask.
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_editWPTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_editWPTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_editWPTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    bool editWPUser(const ::std::string& taskID, const ::ZG6000::StringList& listUserID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_editWPUser(e, _iceI_begin_editWPUser(taskID, listUserID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    ::Ice::AsyncResultPtr begin_editWPUser(const ::std::string& taskID, const ::ZG6000::StringList& listUserID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_editWPUser(taskID, listUserID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    ::Ice::AsyncResultPtr begin_editWPUser(const ::std::string& taskID, const ::ZG6000::StringList& listUserID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editWPUser(taskID, listUserID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    ::Ice::AsyncResultPtr begin_editWPUser(const ::std::string& taskID, const ::ZG6000::StringList& listUserID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editWPUser(taskID, listUserID, context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    ::Ice::AsyncResultPtr begin_editWPUser(const ::std::string& taskID, const ::ZG6000::StringList& listUserID, const ::ZG6000::Callback_ZGOPWPManager_editWPUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editWPUser(taskID, listUserID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    ::Ice::AsyncResultPtr begin_editWPUser(const ::std::string& taskID, const ::ZG6000::StringList& listUserID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPWPManager_editWPUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editWPUser(taskID, listUserID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_editWPUser.
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_editWPUser(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_editWPUser(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_editWPUser(const ::std::string&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 删除请销点任务
     * @param 任务ID
     */
    bool deleteWPTask(const ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deleteWPTask(e, _iceI_begin_deleteWPTask(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 删除请销点任务
     * @param 任务ID
     */
    ::Ice::AsyncResultPtr begin_deleteWPTask(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deleteWPTask(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 删除请销点任务
     * @param 任务ID
     */
    ::Ice::AsyncResultPtr begin_deleteWPTask(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteWPTask(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 删除请销点任务
     * @param 任务ID
     */
    ::Ice::AsyncResultPtr begin_deleteWPTask(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteWPTask(taskID, context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 删除请销点任务
     * @param 任务ID
     */
    ::Ice::AsyncResultPtr begin_deleteWPTask(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPWPManager_deleteWPTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteWPTask(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 删除请销点任务
     * @param 任务ID
     */
    ::Ice::AsyncResultPtr begin_deleteWPTask(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPWPManager_deleteWPTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteWPTask(taskID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_deleteWPTask.
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_deleteWPTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deleteWPTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deleteWPTask(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 作废请销点任务
     */
    bool abolishWPTask(const ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_abolishWPTask(e, _iceI_begin_abolishWPTask(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 作废请销点任务
     */
    ::Ice::AsyncResultPtr begin_abolishWPTask(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_abolishWPTask(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 作废请销点任务
     */
    ::Ice::AsyncResultPtr begin_abolishWPTask(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_abolishWPTask(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 作废请销点任务
     */
    ::Ice::AsyncResultPtr begin_abolishWPTask(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_abolishWPTask(taskID, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 作废请销点任务
     */
    ::Ice::AsyncResultPtr begin_abolishWPTask(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPWPManager_abolishWPTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_abolishWPTask(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 作废请销点任务
     */
    ::Ice::AsyncResultPtr begin_abolishWPTask(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPWPManager_abolishWPTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_abolishWPTask(taskID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_abolishWPTask.
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_abolishWPTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_abolishWPTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_abolishWPTask(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 确认请销点任务
     */
    bool confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_confirmTask(e, _iceI_begin_confirmTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 确认请销点任务
     */
    ::Ice::AsyncResultPtr begin_confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_confirmTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 确认请销点任务
     */
    ::Ice::AsyncResultPtr begin_confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 确认请销点任务
     */
    ::Ice::AsyncResultPtr begin_confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 确认请销点任务
     */
    ::Ice::AsyncResultPtr begin_confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPWPManager_confirmTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 确认请销点任务
     */
    ::Ice::AsyncResultPtr begin_confirmTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPWPManager_confirmTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_confirmTask.
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_confirmTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_confirmTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_confirmTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 回退请销点任务
     */
    bool backTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_backTask(e, _iceI_begin_backTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 回退请销点任务
     */
    ::Ice::AsyncResultPtr begin_backTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_backTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 回退请销点任务
     */
    ::Ice::AsyncResultPtr begin_backTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_backTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 回退请销点任务
     */
    ::Ice::AsyncResultPtr begin_backTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_backTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 回退请销点任务
     */
    ::Ice::AsyncResultPtr begin_backTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPWPManager_backTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_backTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 回退请销点任务
     */
    ::Ice::AsyncResultPtr begin_backTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPWPManager_backTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_backTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_backTask.
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_backTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_backTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_backTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param listUser 人员列表
     * @param e 执行失败时的错误描述
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取请销点人员
     */
    bool getWPUser(const ::std::string& taskID, ::ZG6000::ListStringMap& listUser, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getWPUser(listUser, e, _iceI_begin_getWPUser(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取请销点人员
     */
    ::Ice::AsyncResultPtr begin_getWPUser(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getWPUser(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取请销点人员
     */
    ::Ice::AsyncResultPtr begin_getWPUser(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWPUser(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取请销点人员
     */
    ::Ice::AsyncResultPtr begin_getWPUser(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWPUser(taskID, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取请销点人员
     */
    ::Ice::AsyncResultPtr begin_getWPUser(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPWPManager_getWPUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWPUser(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief 获取请销点人员
     */
    ::Ice::AsyncResultPtr begin_getWPUser(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPWPManager_getWPUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getWPUser(taskID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getWPUser.
     * @param listUser 人员列表
     * @param e 执行失败时的错误描述
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getWPUser(::ZG6000::ListStringMap& listUser, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getWPUser(::ZG6000::ListStringMap& iceP_listUser, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getWPUser(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGOPWPManager : virtual public ZGServerBase
{
public:

    typedef ZGOPWPManagerPrx ProxyType;
    typedef ZGOPWPManagerPtr PointerType;

    virtual ~ZGOPWPManager();

#ifdef ICE_CPP11_COMPILER
    ZGOPWPManager() = default;
    ZGOPWPManager(const ZGOPWPManager&) = default;
    ZGOPWPManager& operator=(const ZGOPWPManager&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param listTask 请销点任务列表
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取请销点任务列表
     */
    virtual bool getWPTaskList(const StringMap& param, ListStringMap& listTask, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getWPTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 创建请销点任务
     * @param 任务参数
     * @param 人员列表
     * @param 创建的任务ID
     */
    virtual bool createWPTask(const StringMap& param, const StringList& listUserID, ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createWPTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 编辑请销点任务
     */
    virtual bool editWPTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_editWPTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 编辑请销点人员
     * @param 任务ID
     * @param 人员列表
     */
    virtual bool editWPUser(const ::std::string& taskID, const StringList& listUserID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_editWPUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 删除请销点任务
     * @param 任务ID
     */
    virtual bool deleteWPTask(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteWPTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 作废请销点任务
     */
    virtual bool abolishWPTask(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_abolishWPTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 确认请销点任务
     */
    virtual bool confirmTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 回退请销点任务
     */
    virtual bool backTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_backTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param listUser 人员列表
     * @param e 执行失败时的错误描述
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief 获取请销点人员
     */
    virtual bool getWPUser(const ::std::string& taskID, ListStringMap& listUser, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getWPUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGOPWPManager& lhs, const ZGOPWPManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGOPWPManager& lhs, const ZGOPWPManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_getWPTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_getWPTaskList.
 */
template<class T>
class CallbackNC_ZGOPWPManager_getWPTaskList : public Callback_ZGOPWPManager_getWPTaskList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPWPManager_getWPTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listTask;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getWPTaskList(iceP_listTask, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listTask, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_getWPTaskList.
 */
template<class T> Callback_ZGOPWPManager_getWPTaskListPtr
newCallback_ZGOPWPManager_getWPTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_getWPTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_getWPTaskList.
 */
template<class T> Callback_ZGOPWPManager_getWPTaskListPtr
newCallback_ZGOPWPManager_getWPTaskList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_getWPTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_getWPTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_getWPTaskList.
 */
template<class T, typename CT>
class Callback_ZGOPWPManager_getWPTaskList : public Callback_ZGOPWPManager_getWPTaskList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPWPManager_getWPTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listTask;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getWPTaskList(iceP_listTask, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listTask, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_getWPTaskList.
 */
template<class T, typename CT> Callback_ZGOPWPManager_getWPTaskListPtr
newCallback_ZGOPWPManager_getWPTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_getWPTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_getWPTaskList.
 */
template<class T, typename CT> Callback_ZGOPWPManager_getWPTaskListPtr
newCallback_ZGOPWPManager_getWPTaskList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_getWPTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_createWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_createWPTask.
 */
template<class T>
class CallbackNC_ZGOPWPManager_createWPTask : public Callback_ZGOPWPManager_createWPTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGOPWPManager_createWPTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_taskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createWPTask(iceP_taskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_taskID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_createWPTask.
 */
template<class T> Callback_ZGOPWPManager_createWPTaskPtr
newCallback_ZGOPWPManager_createWPTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_createWPTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_createWPTask.
 */
template<class T> Callback_ZGOPWPManager_createWPTaskPtr
newCallback_ZGOPWPManager_createWPTask(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_createWPTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_createWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_createWPTask.
 */
template<class T, typename CT>
class Callback_ZGOPWPManager_createWPTask : public Callback_ZGOPWPManager_createWPTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGOPWPManager_createWPTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_taskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createWPTask(iceP_taskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_taskID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_createWPTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_createWPTaskPtr
newCallback_ZGOPWPManager_createWPTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_createWPTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_createWPTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_createWPTaskPtr
newCallback_ZGOPWPManager_createWPTask(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_createWPTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_editWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_editWPTask.
 */
template<class T>
class CallbackNC_ZGOPWPManager_editWPTask : public Callback_ZGOPWPManager_editWPTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPWPManager_editWPTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_editWPTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_editWPTask.
 */
template<class T> Callback_ZGOPWPManager_editWPTaskPtr
newCallback_ZGOPWPManager_editWPTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_editWPTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_editWPTask.
 */
template<class T> Callback_ZGOPWPManager_editWPTaskPtr
newCallback_ZGOPWPManager_editWPTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_editWPTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_editWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_editWPTask.
 */
template<class T, typename CT>
class Callback_ZGOPWPManager_editWPTask : public Callback_ZGOPWPManager_editWPTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPWPManager_editWPTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_editWPTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_editWPTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_editWPTaskPtr
newCallback_ZGOPWPManager_editWPTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_editWPTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_editWPTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_editWPTaskPtr
newCallback_ZGOPWPManager_editWPTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_editWPTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_editWPUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_editWPUser.
 */
template<class T>
class CallbackNC_ZGOPWPManager_editWPUser : public Callback_ZGOPWPManager_editWPUser_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPWPManager_editWPUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_editWPUser(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_editWPUser.
 */
template<class T> Callback_ZGOPWPManager_editWPUserPtr
newCallback_ZGOPWPManager_editWPUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_editWPUser<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_editWPUser.
 */
template<class T> Callback_ZGOPWPManager_editWPUserPtr
newCallback_ZGOPWPManager_editWPUser(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_editWPUser<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_editWPUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_editWPUser.
 */
template<class T, typename CT>
class Callback_ZGOPWPManager_editWPUser : public Callback_ZGOPWPManager_editWPUser_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPWPManager_editWPUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_editWPUser(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_editWPUser.
 */
template<class T, typename CT> Callback_ZGOPWPManager_editWPUserPtr
newCallback_ZGOPWPManager_editWPUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_editWPUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_editWPUser.
 */
template<class T, typename CT> Callback_ZGOPWPManager_editWPUserPtr
newCallback_ZGOPWPManager_editWPUser(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_editWPUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_deleteWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_deleteWPTask.
 */
template<class T>
class CallbackNC_ZGOPWPManager_deleteWPTask : public Callback_ZGOPWPManager_deleteWPTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPWPManager_deleteWPTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteWPTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_deleteWPTask.
 */
template<class T> Callback_ZGOPWPManager_deleteWPTaskPtr
newCallback_ZGOPWPManager_deleteWPTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_deleteWPTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_deleteWPTask.
 */
template<class T> Callback_ZGOPWPManager_deleteWPTaskPtr
newCallback_ZGOPWPManager_deleteWPTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_deleteWPTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_deleteWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_deleteWPTask.
 */
template<class T, typename CT>
class Callback_ZGOPWPManager_deleteWPTask : public Callback_ZGOPWPManager_deleteWPTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPWPManager_deleteWPTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteWPTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_deleteWPTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_deleteWPTaskPtr
newCallback_ZGOPWPManager_deleteWPTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_deleteWPTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_deleteWPTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_deleteWPTaskPtr
newCallback_ZGOPWPManager_deleteWPTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_deleteWPTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_abolishWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_abolishWPTask.
 */
template<class T>
class CallbackNC_ZGOPWPManager_abolishWPTask : public Callback_ZGOPWPManager_abolishWPTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPWPManager_abolishWPTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_abolishWPTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_abolishWPTask.
 */
template<class T> Callback_ZGOPWPManager_abolishWPTaskPtr
newCallback_ZGOPWPManager_abolishWPTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_abolishWPTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_abolishWPTask.
 */
template<class T> Callback_ZGOPWPManager_abolishWPTaskPtr
newCallback_ZGOPWPManager_abolishWPTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_abolishWPTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_abolishWPTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_abolishWPTask.
 */
template<class T, typename CT>
class Callback_ZGOPWPManager_abolishWPTask : public Callback_ZGOPWPManager_abolishWPTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPWPManager_abolishWPTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_abolishWPTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_abolishWPTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_abolishWPTaskPtr
newCallback_ZGOPWPManager_abolishWPTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_abolishWPTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_abolishWPTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_abolishWPTaskPtr
newCallback_ZGOPWPManager_abolishWPTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_abolishWPTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_confirmTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_confirmTask.
 */
template<class T>
class CallbackNC_ZGOPWPManager_confirmTask : public Callback_ZGOPWPManager_confirmTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPWPManager_confirmTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_confirmTask.
 */
template<class T> Callback_ZGOPWPManager_confirmTaskPtr
newCallback_ZGOPWPManager_confirmTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_confirmTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_confirmTask.
 */
template<class T> Callback_ZGOPWPManager_confirmTaskPtr
newCallback_ZGOPWPManager_confirmTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_confirmTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_confirmTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_confirmTask.
 */
template<class T, typename CT>
class Callback_ZGOPWPManager_confirmTask : public Callback_ZGOPWPManager_confirmTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPWPManager_confirmTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_confirmTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_confirmTaskPtr
newCallback_ZGOPWPManager_confirmTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_confirmTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_confirmTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_confirmTaskPtr
newCallback_ZGOPWPManager_confirmTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_confirmTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_backTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_backTask.
 */
template<class T>
class CallbackNC_ZGOPWPManager_backTask : public Callback_ZGOPWPManager_backTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPWPManager_backTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_backTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_backTask.
 */
template<class T> Callback_ZGOPWPManager_backTaskPtr
newCallback_ZGOPWPManager_backTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_backTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_backTask.
 */
template<class T> Callback_ZGOPWPManager_backTaskPtr
newCallback_ZGOPWPManager_backTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_backTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_backTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_backTask.
 */
template<class T, typename CT>
class Callback_ZGOPWPManager_backTask : public Callback_ZGOPWPManager_backTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPWPManager_backTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_backTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_backTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_backTaskPtr
newCallback_ZGOPWPManager_backTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_backTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_backTask.
 */
template<class T, typename CT> Callback_ZGOPWPManager_backTaskPtr
newCallback_ZGOPWPManager_backTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_backTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_getWPUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_getWPUser.
 */
template<class T>
class CallbackNC_ZGOPWPManager_getWPUser : public Callback_ZGOPWPManager_getWPUser_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPWPManager_getWPUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listUser;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getWPUser(iceP_listUser, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listUser, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_getWPUser.
 */
template<class T> Callback_ZGOPWPManager_getWPUserPtr
newCallback_ZGOPWPManager_getWPUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_getWPUser<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_getWPUser.
 */
template<class T> Callback_ZGOPWPManager_getWPUserPtr
newCallback_ZGOPWPManager_getWPUser(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPWPManager_getWPUser<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPWPManager::begin_getWPUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPWPManager_getWPUser.
 */
template<class T, typename CT>
class Callback_ZGOPWPManager_getWPUser : public Callback_ZGOPWPManager_getWPUser_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPWPManager_getWPUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPWPManagerPrx proxy = ZGOPWPManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listUser;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getWPUser(iceP_listUser, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listUser, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_getWPUser.
 */
template<class T, typename CT> Callback_ZGOPWPManager_getWPUserPtr
newCallback_ZGOPWPManager_getWPUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_getWPUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPWPManager::begin_getWPUser.
 */
template<class T, typename CT> Callback_ZGOPWPManager_getWPUserPtr
newCallback_ZGOPWPManager_getWPUser(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPWPManager_getWPUser<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

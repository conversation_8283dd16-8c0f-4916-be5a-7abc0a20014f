//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPExamManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSPExamManager.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPExamManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPExamManager",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSPExamManager_ops[] =
{
    "checkState",
    "createExam",
    "deleteExam",
    "dispatchData",
    "execStep",
    "exitApp",
    "finishExam",
    "getExamInfo",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGSPExamManager_createExam_name = "createExam";
const ::std::string iceC_ZG6000_ZGSPExamManager_getExamInfo_name = "getExamInfo";
const ::std::string iceC_ZG6000_ZGSPExamManager_execStep_name = "execStep";
const ::std::string iceC_ZG6000_ZGSPExamManager_deleteExam_name = "deleteExam";
const ::std::string iceC_ZG6000_ZGSPExamManager_finishExam_name = "finishExam";

}

bool
ZG6000::ZGSPExamManager::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPExamManager_ids, iceC_ZG6000_ZGSPExamManager_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSPExamManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSPExamManager_ids[0], &iceC_ZG6000_ZGSPExamManager_ids[3]);
}

::std::string
ZG6000::ZGSPExamManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPExamManager::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSPExamManager";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceD_createExam(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_paramExamID;
    istr->readAll(iceP_paramExamID);
    inS.endReadParams();
    ::std::string iceP_examID;
    ErrorInfo iceP_e;
    bool ret = this->createExam(::std::move(iceP_paramExamID), iceP_examID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_examID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceD_getExamInfo(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_examID;
    istr->readAll(iceP_examID);
    inS.endReadParams();
    ::std::string iceP_examInfo;
    ErrorInfo iceP_e;
    bool ret = this->getExamInfo(::std::move(iceP_examID), iceP_examInfo, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_examInfo, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceD_execStep(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    ::std::string iceP_stepID;
    StringMap iceP_params;
    istr->readAll(iceP_appNodeID, iceP_stepID, iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->execStep(::std::move(iceP_appNodeID), ::std::move(iceP_stepID), ::std::move(iceP_params), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceD_deleteExam(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_examID;
    istr->readAll(iceP_examID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteExam(::std::move(iceP_examID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceD_finishExam(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_examID;
    istr->readAll(iceP_examID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->finishExam(::std::move(iceP_examID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPExamManager_ops, iceC_ZG6000_ZGSPExamManager_ops + 20, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPExamManager_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_createExam(in, current);
        }
        case 2:
        {
            return _iceD_deleteExam(in, current);
        }
        case 3:
        {
            return _iceD_dispatchData(in, current);
        }
        case 4:
        {
            return _iceD_execStep(in, current);
        }
        case 5:
        {
            return _iceD_exitApp(in, current);
        }
        case 6:
        {
            return _iceD_finishExam(in, current);
        }
        case 7:
        {
            return _iceD_getExamInfo(in, current);
        }
        case 8:
        {
            return _iceD_getVersion(in, current);
        }
        case 9:
        {
            return _iceD_heartDebug(in, current);
        }
        case 10:
        {
            return _iceD_ice_id(in, current);
        }
        case 11:
        {
            return _iceD_ice_ids(in, current);
        }
        case 12:
        {
            return _iceD_ice_isA(in, current);
        }
        case 13:
        {
            return _iceD_ice_ping(in, current);
        }
        case 14:
        {
            return _iceD_isDebugging(in, current);
        }
        case 15:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 16:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 17:
        {
            return _iceD_startDebug(in, current);
        }
        case 18:
        {
            return _iceD_stopDebug(in, current);
        }
        case 19:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPExamManagerPrx::_iceI_createExam(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPExamManager::CreateExamResult>>& outAsync, const ::std::string& iceP_paramExamID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPExamManager_createExam_name);
    outAsync->invoke(iceC_ZG6000_ZGSPExamManager_createExam_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_paramExamID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPExamManager::CreateExamResult v;
            istr->readAll(v.examID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPExamManagerPrx::_iceI_getExamInfo(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPExamManager::GetExamInfoResult>>& outAsync, const ::std::string& iceP_examID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPExamManager_getExamInfo_name);
    outAsync->invoke(iceC_ZG6000_ZGSPExamManager_getExamInfo_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_examID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPExamManager::GetExamInfoResult v;
            istr->readAll(v.examInfo, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPExamManagerPrx::_iceI_execStep(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPExamManager::ExecStepResult>>& outAsync, const ::std::string& iceP_appNodeID, const ::std::string& iceP_stepID, const StringMap& iceP_params, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPExamManager_execStep_name);
    outAsync->invoke(iceC_ZG6000_ZGSPExamManager_execStep_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_appNodeID, iceP_stepID, iceP_params);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPExamManager::ExecStepResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPExamManagerPrx::_iceI_deleteExam(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPExamManager::DeleteExamResult>>& outAsync, const ::std::string& iceP_examID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPExamManager_deleteExam_name);
    outAsync->invoke(iceC_ZG6000_ZGSPExamManager_deleteExam_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_examID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPExamManager::DeleteExamResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPExamManagerPrx::_iceI_finishExam(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPExamManager::FinishExamResult>>& outAsync, const ::std::string& iceP_examID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPExamManager_finishExam_name);
    outAsync->invoke(iceC_ZG6000_ZGSPExamManager_finishExam_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_examID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPExamManager::FinishExamResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSPExamManagerPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSPExamManagerPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSPExamManagerPrx::ice_staticId()
{
    return ZGSPExamManager::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPExamManager_createExam_name = "createExam";

const ::std::string iceC_ZG6000_ZGSPExamManager_getExamInfo_name = "getExamInfo";

const ::std::string iceC_ZG6000_ZGSPExamManager_execStep_name = "execStep";

const ::std::string iceC_ZG6000_ZGSPExamManager_deleteExam_name = "deleteExam";

const ::std::string iceC_ZG6000_ZGSPExamManager_finishExam_name = "finishExam";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSPExamManager* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSPExamManager>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSPExamManager;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPExamManager::_iceI_begin_createExam(const ::std::string& iceP_paramExamID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPExamManager_createExam_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPExamManager_createExam_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPExamManager_createExam_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_paramExamID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPExamManager_createExam_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPExamManager::end_createExam(::std::string& iceP_examID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPExamManager_createExam_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_examID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPExamManager::_iceI_end_createExam(::std::string& iceP_examID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPExamManager_createExam_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_examID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPExamManager::_iceI_begin_getExamInfo(const ::std::string& iceP_examID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPExamManager_getExamInfo_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPExamManager_getExamInfo_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPExamManager_getExamInfo_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_examID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPExamManager_getExamInfo_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPExamManager::end_getExamInfo(::std::string& iceP_examInfo, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPExamManager_getExamInfo_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_examInfo);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPExamManager::_iceI_end_getExamInfo(::std::string& iceP_examInfo, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPExamManager_getExamInfo_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_examInfo);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPExamManager::_iceI_begin_execStep(const ::std::string& iceP_appNodeID, const ::std::string& iceP_stepID, const ::ZG6000::StringMap& iceP_params, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPExamManager_execStep_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPExamManager_execStep_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPExamManager_execStep_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_stepID);
        ostr->write(iceP_params);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPExamManager_execStep_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPExamManager::end_execStep(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPExamManager_execStep_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPExamManager::_iceI_end_execStep(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPExamManager_execStep_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPExamManager::_iceI_begin_deleteExam(const ::std::string& iceP_examID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPExamManager_deleteExam_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPExamManager_deleteExam_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPExamManager_deleteExam_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_examID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPExamManager_deleteExam_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPExamManager::end_deleteExam(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPExamManager_deleteExam_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPExamManager::_iceI_end_deleteExam(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPExamManager_deleteExam_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPExamManager::_iceI_begin_finishExam(const ::std::string& iceP_examID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPExamManager_finishExam_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPExamManager_finishExam_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPExamManager_finishExam_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_examID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPExamManager_finishExam_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPExamManager::end_finishExam(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPExamManager_finishExam_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPExamManager::_iceI_end_finishExam(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPExamManager_finishExam_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSPExamManager::_newInstance() const
{
    return new ZGSPExamManager;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSPExamManager::ice_staticId()
{
    return ::ZG6000::ZGSPExamManager::ice_staticId();
}

ZG6000::ZGSPExamManager::~ZGSPExamManager()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSPExamManager* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPExamManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPExamManager",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSPExamManager::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPExamManager_ids, iceC_ZG6000_ZGSPExamManager_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSPExamManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSPExamManager_ids[0], &iceC_ZG6000_ZGSPExamManager_ids[3]);
}

const ::std::string&
ZG6000::ZGSPExamManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPExamManager::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSPExamManager";
    return typeId;
#else
    return iceC_ZG6000_ZGSPExamManager_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceD_createExam(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_paramExamID;
    istr->read(iceP_paramExamID);
    inS.endReadParams();
    ::std::string iceP_examID;
    ErrorInfo iceP_e;
    bool ret = this->createExam(iceP_paramExamID, iceP_examID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_examID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceD_getExamInfo(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_examID;
    istr->read(iceP_examID);
    inS.endReadParams();
    ::std::string iceP_examInfo;
    ErrorInfo iceP_e;
    bool ret = this->getExamInfo(iceP_examID, iceP_examInfo, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_examInfo);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceD_execStep(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    ::std::string iceP_stepID;
    StringMap iceP_params;
    istr->read(iceP_appNodeID);
    istr->read(iceP_stepID);
    istr->read(iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->execStep(iceP_appNodeID, iceP_stepID, iceP_params, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceD_deleteExam(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_examID;
    istr->read(iceP_examID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteExam(iceP_examID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceD_finishExam(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_examID;
    istr->read(iceP_examID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->finishExam(iceP_examID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPExamManager_all[] =
{
    "checkState",
    "createExam",
    "deleteExam",
    "dispatchData",
    "execStep",
    "exitApp",
    "finishExam",
    "getExamInfo",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSPExamManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPExamManager_all, iceC_ZG6000_ZGSPExamManager_all + 20, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPExamManager_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_createExam(in, current);
        }
        case 2:
        {
            return _iceD_deleteExam(in, current);
        }
        case 3:
        {
            return _iceD_dispatchData(in, current);
        }
        case 4:
        {
            return _iceD_execStep(in, current);
        }
        case 5:
        {
            return _iceD_exitApp(in, current);
        }
        case 6:
        {
            return _iceD_finishExam(in, current);
        }
        case 7:
        {
            return _iceD_getExamInfo(in, current);
        }
        case 8:
        {
            return _iceD_getVersion(in, current);
        }
        case 9:
        {
            return _iceD_heartDebug(in, current);
        }
        case 10:
        {
            return _iceD_ice_id(in, current);
        }
        case 11:
        {
            return _iceD_ice_ids(in, current);
        }
        case 12:
        {
            return _iceD_ice_isA(in, current);
        }
        case 13:
        {
            return _iceD_ice_ping(in, current);
        }
        case 14:
        {
            return _iceD_isDebugging(in, current);
        }
        case 15:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 16:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 17:
        {
            return _iceD_startDebug(in, current);
        }
        case 18:
        {
            return _iceD_stopDebug(in, current);
        }
        case 19:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSPExamManager::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSPExamManager, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSPExamManager::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSPExamManager, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSPExamManagerPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSPExamManagerPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSPExamManager::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

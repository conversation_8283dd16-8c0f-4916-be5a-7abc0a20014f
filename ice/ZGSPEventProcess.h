//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPEventProcess.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPEventProcess_h__
#define __ZGSPEventProcess_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPEventProcess;
class ZGSPEventProcessPrx;

}

namespace ZG6000
{

class ZGSPEventProcess : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPEventProcessPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param current The Current object for the invocation.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    virtual void processZGMPEvents(ListStringMap listEvent, ListStringList listListAppNodeID, StringList listIsPublishEvent, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_processZGMPEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param current The Current object for the invocation.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    virtual void processZGMPDatasetEvents(ListStringMap listEvent, StringList listAppNodeID, StringList listIsPublishEvent, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_processZGMPDatasetEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param current The Current object for the invocation.
     * @brief   处理系统平台事件
     */
    virtual void processZGSPEvents(ListStringMap listEvent, StringList listIsPublishEvent, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_processZGSPEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param current The Current object for the invocation.
     * @brief   处理设备管理平台事件
     */
    virtual void processZGDPEvents(ListStringMap listEvent, StringList listIsPublishEvent, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_processZGDPEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param current The Current object for the invocation.
     * @brief   处理运维管理平台事件
     */
    virtual void processZGOPEvents(ListStringMap listEvent, StringList listIsPublishEvent, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_processZGOPEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param event 要处理的事件
     * @param current The Current object for the invocation.
     * @brief   处理事件(其他服务调用)
     */
    virtual void processEvent(StringMap event, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_processEvent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to confirmEvents.
     */
    struct ConfirmEventsResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param current The Current object for the invocation.
     * @brief   确认事件(其他服务调用)
     */
    virtual bool confirmEvents(ListStringMap listEvent, ::std::string userID, ::std::string userName, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to confirmDPEvent.
     */
    struct ConfirmDPEventResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param current The Current object for the invocation.
     * @brief   确认设备事件(其他服务调用)
     */
    virtual bool confirmDPEvent(ListStringMap listEvent, ::std::string userID, ::std::string userName, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmDPEvent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getUnconfirmedEvent.
     */
    struct GetUnconfirmedEventResult
    {
        bool returnValue;
        ListStringMap listEvent;
        ErrorInfo e;
    };

    virtual bool getUnconfirmedEvent(::std::string appNodeID, ::std::string subsystemID, ListStringMap& listEvent, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getUnconfirmedEvent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getUnconfirmedPosEvent.
     */
    struct GetUnconfirmedPosEventResult
    {
        bool returnValue;
        ListStringMap listEvent;
        ErrorInfo e;
    };

    virtual bool getUnconfirmedPosEvent(::std::string appNodeID, ::std::string subsystemID, ::std::string position, ListStringMap& listEvent, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getUnconfirmedPosEvent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getUnconfirmedEventNum.
     */
    struct GetUnconfirmedEventNumResult
    {
        bool returnValue;
        StringMap appNodeEventNum;
        ErrorInfo e;
    };

    virtual bool getUnconfirmedEventNum(StringMap& appNodeEventNum, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getUnconfirmedEventNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPEventProcessPrx : public virtual ::Ice::Proxy<ZGSPEventProcessPrx, ZGServerBasePrx>
{
public:

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    void processZGMPEvents(const ListStringMap& listEvent, const ListStringList& listListAppNodeID, const StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPEventProcessPrx::_iceI_processZGMPEvents, listEvent, listListAppNodeID, listIsPublishEvent, context).get();
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    template<template<typename> class P = ::std::promise>
    auto processZGMPEventsAsync(const ListStringMap& listEvent, const ListStringList& listListAppNodeID, const StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPEventProcessPrx::_iceI_processZGMPEvents, listEvent, listListAppNodeID, listIsPublishEvent, context);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    ::std::function<void()>
    processZGMPEventsAsync(const ListStringMap& listEvent, const ListStringList& listListAppNodeID, const StringList& listIsPublishEvent,
                           ::std::function<void()> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_processZGMPEvents, listEvent, listListAppNodeID, listIsPublishEvent, context);
    }

    /// \cond INTERNAL
    void _iceI_processZGMPEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ListStringMap&, const ListStringList&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    void processZGMPDatasetEvents(const ListStringMap& listEvent, const StringList& listAppNodeID, const StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPEventProcessPrx::_iceI_processZGMPDatasetEvents, listEvent, listAppNodeID, listIsPublishEvent, context).get();
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    template<template<typename> class P = ::std::promise>
    auto processZGMPDatasetEventsAsync(const ListStringMap& listEvent, const StringList& listAppNodeID, const StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPEventProcessPrx::_iceI_processZGMPDatasetEvents, listEvent, listAppNodeID, listIsPublishEvent, context);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    ::std::function<void()>
    processZGMPDatasetEventsAsync(const ListStringMap& listEvent, const StringList& listAppNodeID, const StringList& listIsPublishEvent,
                                  ::std::function<void()> response,
                                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                  ::std::function<void(bool)> sent = nullptr,
                                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_processZGMPDatasetEvents, listEvent, listAppNodeID, listIsPublishEvent, context);
    }

    /// \cond INTERNAL
    void _iceI_processZGMPDatasetEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ListStringMap&, const StringList&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @brief   处理系统平台事件
     */
    void processZGSPEvents(const ListStringMap& listEvent, const StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPEventProcessPrx::_iceI_processZGSPEvents, listEvent, listIsPublishEvent, context).get();
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   处理系统平台事件
     */
    template<template<typename> class P = ::std::promise>
    auto processZGSPEventsAsync(const ListStringMap& listEvent, const StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPEventProcessPrx::_iceI_processZGSPEvents, listEvent, listIsPublishEvent, context);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   处理系统平台事件
     */
    ::std::function<void()>
    processZGSPEventsAsync(const ListStringMap& listEvent, const StringList& listIsPublishEvent,
                           ::std::function<void()> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_processZGSPEvents, listEvent, listIsPublishEvent, context);
    }

    /// \cond INTERNAL
    void _iceI_processZGSPEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ListStringMap&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @brief   处理设备管理平台事件
     */
    void processZGDPEvents(const ListStringMap& listEvent, const StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPEventProcessPrx::_iceI_processZGDPEvents, listEvent, listIsPublishEvent, context).get();
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   处理设备管理平台事件
     */
    template<template<typename> class P = ::std::promise>
    auto processZGDPEventsAsync(const ListStringMap& listEvent, const StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPEventProcessPrx::_iceI_processZGDPEvents, listEvent, listIsPublishEvent, context);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   处理设备管理平台事件
     */
    ::std::function<void()>
    processZGDPEventsAsync(const ListStringMap& listEvent, const StringList& listIsPublishEvent,
                           ::std::function<void()> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_processZGDPEvents, listEvent, listIsPublishEvent, context);
    }

    /// \cond INTERNAL
    void _iceI_processZGDPEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ListStringMap&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @brief   处理运维管理平台事件
     */
    void processZGOPEvents(const ListStringMap& listEvent, const StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPEventProcessPrx::_iceI_processZGOPEvents, listEvent, listIsPublishEvent, context).get();
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   处理运维管理平台事件
     */
    template<template<typename> class P = ::std::promise>
    auto processZGOPEventsAsync(const ListStringMap& listEvent, const StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPEventProcessPrx::_iceI_processZGOPEvents, listEvent, listIsPublishEvent, context);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   处理运维管理平台事件
     */
    ::std::function<void()>
    processZGOPEventsAsync(const ListStringMap& listEvent, const StringList& listIsPublishEvent,
                           ::std::function<void()> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_processZGOPEvents, listEvent, listIsPublishEvent, context);
    }

    /// \cond INTERNAL
    void _iceI_processZGOPEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ListStringMap&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param event 要处理的事件
     * @param context The Context map to send with the invocation.
     * @brief   处理事件(其他服务调用)
     */
    void processEvent(const StringMap& event, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPEventProcessPrx::_iceI_processEvent, event, context).get();
    }

    /**
     * @param event 要处理的事件
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   处理事件(其他服务调用)
     */
    template<template<typename> class P = ::std::promise>
    auto processEventAsync(const StringMap& event, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPEventProcessPrx::_iceI_processEvent, event, context);
    }

    /**
     * @param event 要处理的事件
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   处理事件(其他服务调用)
     */
    ::std::function<void()>
    processEventAsync(const StringMap& event,
                      ::std::function<void()> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_processEvent, event, context);
    }

    /// \cond INTERNAL
    void _iceI_processEvent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @brief   确认事件(其他服务调用)
     */
    bool confirmEvents(const ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPEventProcess::ConfirmEventsResult>(true, this, &ZGSPEventProcessPrx::_iceI_confirmEvents, listEvent, userID, userName, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   确认事件(其他服务调用)
     */
    template<template<typename> class P = ::std::promise>
    auto confirmEventsAsync(const ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPEventProcess::ConfirmEventsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPEventProcess::ConfirmEventsResult, P>(false, this, &ZGSPEventProcessPrx::_iceI_confirmEvents, listEvent, userID, userName, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   确认事件(其他服务调用)
     */
    ::std::function<void()>
    confirmEventsAsync(const ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName,
                       ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPEventProcess::ConfirmEventsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPEventProcess::ConfirmEventsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_confirmEvents, listEvent, userID, userName, context);
    }

    /// \cond INTERNAL
    void _iceI_confirmEvents(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPEventProcess::ConfirmEventsResult>>&, const ListStringMap&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @brief   确认设备事件(其他服务调用)
     */
    bool confirmDPEvent(const ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPEventProcess::ConfirmDPEventResult>(true, this, &ZGSPEventProcessPrx::_iceI_confirmDPEvent, listEvent, userID, userName, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief   确认设备事件(其他服务调用)
     */
    template<template<typename> class P = ::std::promise>
    auto confirmDPEventAsync(const ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPEventProcess::ConfirmDPEventResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPEventProcess::ConfirmDPEventResult, P>(false, this, &ZGSPEventProcessPrx::_iceI_confirmDPEvent, listEvent, userID, userName, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief   确认设备事件(其他服务调用)
     */
    ::std::function<void()>
    confirmDPEventAsync(const ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPEventProcess::ConfirmDPEventResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPEventProcess::ConfirmDPEventResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_confirmDPEvent, listEvent, userID, userName, context);
    }

    /// \cond INTERNAL
    void _iceI_confirmDPEvent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPEventProcess::ConfirmDPEventResult>>&, const ListStringMap&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getUnconfirmedEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, ListStringMap& listEvent, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPEventProcess::GetUnconfirmedEventResult>(true, this, &ZGSPEventProcessPrx::_iceI_getUnconfirmedEvent, appNodeID, subsystemID, context).get();
        listEvent = ::std::move(_result.listEvent);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getUnconfirmedEventAsync(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPEventProcess::GetUnconfirmedEventResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPEventProcess::GetUnconfirmedEventResult, P>(false, this, &ZGSPEventProcessPrx::_iceI_getUnconfirmedEvent, appNodeID, subsystemID, context);
    }

    ::std::function<void()>
    getUnconfirmedEventAsync(const ::std::string& appNodeID, const ::std::string& subsystemID,
                             ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPEventProcess::GetUnconfirmedEventResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listEvent), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPEventProcess::GetUnconfirmedEventResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_getUnconfirmedEvent, appNodeID, subsystemID, context);
    }

    /// \cond INTERNAL
    void _iceI_getUnconfirmedEvent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPEventProcess::GetUnconfirmedEventResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getUnconfirmedPosEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& position, ListStringMap& listEvent, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPEventProcess::GetUnconfirmedPosEventResult>(true, this, &ZGSPEventProcessPrx::_iceI_getUnconfirmedPosEvent, appNodeID, subsystemID, position, context).get();
        listEvent = ::std::move(_result.listEvent);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getUnconfirmedPosEventAsync(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& position, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPEventProcess::GetUnconfirmedPosEventResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPEventProcess::GetUnconfirmedPosEventResult, P>(false, this, &ZGSPEventProcessPrx::_iceI_getUnconfirmedPosEvent, appNodeID, subsystemID, position, context);
    }

    ::std::function<void()>
    getUnconfirmedPosEventAsync(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& position,
                                ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                                ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                ::std::function<void(bool)> sent = nullptr,
                                const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPEventProcess::GetUnconfirmedPosEventResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listEvent), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPEventProcess::GetUnconfirmedPosEventResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_getUnconfirmedPosEvent, appNodeID, subsystemID, position, context);
    }

    /// \cond INTERNAL
    void _iceI_getUnconfirmedPosEvent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPEventProcess::GetUnconfirmedPosEventResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getUnconfirmedEventNum(StringMap& appNodeEventNum, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPEventProcess::GetUnconfirmedEventNumResult>(true, this, &ZGSPEventProcessPrx::_iceI_getUnconfirmedEventNum, context).get();
        appNodeEventNum = ::std::move(_result.appNodeEventNum);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getUnconfirmedEventNumAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPEventProcess::GetUnconfirmedEventNumResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPEventProcess::GetUnconfirmedEventNumResult, P>(false, this, &ZGSPEventProcessPrx::_iceI_getUnconfirmedEventNum, context);
    }

    ::std::function<void()>
    getUnconfirmedEventNumAsync(::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                                ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                ::std::function<void(bool)> sent = nullptr,
                                const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPEventProcess::GetUnconfirmedEventNumResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.appNodeEventNum), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPEventProcess::GetUnconfirmedEventNumResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPEventProcessPrx::_iceI_getUnconfirmedEventNum, context);
    }

    /// \cond INTERNAL
    void _iceI_getUnconfirmedEventNum(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPEventProcess::GetUnconfirmedEventNumResult>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPEventProcessPrx() = default;
    friend ::std::shared_ptr<ZGSPEventProcessPrx> IceInternal::createProxy<ZGSPEventProcessPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPEventProcessPtr = ::std::shared_ptr<ZGSPEventProcess>;
using ZGSPEventProcessPrxPtr = ::std::shared_ptr<ZGSPEventProcessPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPEventProcess;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPEventProcess>&);
::IceProxy::Ice::Object* upCast(ZGSPEventProcess*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPEventProcess;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPEventProcess*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPEventProcess> ZGSPEventProcessPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPEventProcess> ZGSPEventProcessPrx;
typedef ZGSPEventProcessPrx ZGSPEventProcessPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPEventProcessPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGMPEvents.
 */
class Callback_ZGSPEventProcess_processZGMPEvents_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_processZGMPEvents_Base> Callback_ZGSPEventProcess_processZGMPEventsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGMPDatasetEvents.
 */
class Callback_ZGSPEventProcess_processZGMPDatasetEvents_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_processZGMPDatasetEvents_Base> Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGSPEvents.
 */
class Callback_ZGSPEventProcess_processZGSPEvents_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_processZGSPEvents_Base> Callback_ZGSPEventProcess_processZGSPEventsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGDPEvents.
 */
class Callback_ZGSPEventProcess_processZGDPEvents_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_processZGDPEvents_Base> Callback_ZGSPEventProcess_processZGDPEventsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGOPEvents.
 */
class Callback_ZGSPEventProcess_processZGOPEvents_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_processZGOPEvents_Base> Callback_ZGSPEventProcess_processZGOPEventsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processEvent.
 */
class Callback_ZGSPEventProcess_processEvent_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_processEvent_Base> Callback_ZGSPEventProcess_processEventPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_confirmEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_confirmEvents.
 */
class Callback_ZGSPEventProcess_confirmEvents_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_confirmEvents_Base> Callback_ZGSPEventProcess_confirmEventsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_confirmDPEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_confirmDPEvent.
 */
class Callback_ZGSPEventProcess_confirmDPEvent_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_confirmDPEvent_Base> Callback_ZGSPEventProcess_confirmDPEventPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_getUnconfirmedEvent.
 */
class Callback_ZGSPEventProcess_getUnconfirmedEvent_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_getUnconfirmedEvent_Base> Callback_ZGSPEventProcess_getUnconfirmedEventPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedPosEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_getUnconfirmedPosEvent.
 */
class Callback_ZGSPEventProcess_getUnconfirmedPosEvent_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_getUnconfirmedPosEvent_Base> Callback_ZGSPEventProcess_getUnconfirmedPosEventPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEventNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_getUnconfirmedEventNum.
 */
class Callback_ZGSPEventProcess_getUnconfirmedEventNum_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPEventProcess_getUnconfirmedEventNum_Base> Callback_ZGSPEventProcess_getUnconfirmedEventNumPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPEventProcess : public virtual ::Ice::Proxy<ZGSPEventProcess, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    void processZGMPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::ListStringList& listListAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_processZGMPEvents(_iceI_begin_processZGMPEvents(listEvent, listListAppNodeID, listIsPublishEvent, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    ::Ice::AsyncResultPtr begin_processZGMPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::ListStringList& listListAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_processZGMPEvents(listEvent, listListAppNodeID, listIsPublishEvent, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    ::Ice::AsyncResultPtr begin_processZGMPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::ListStringList& listListAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGMPEvents(listEvent, listListAppNodeID, listIsPublishEvent, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    ::Ice::AsyncResultPtr begin_processZGMPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::ListStringList& listListAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGMPEvents(listEvent, listListAppNodeID, listIsPublishEvent, context, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    ::Ice::AsyncResultPtr begin_processZGMPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::ListStringList& listListAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::ZG6000::Callback_ZGSPEventProcess_processZGMPEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGMPEvents(listEvent, listListAppNodeID, listIsPublishEvent, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    ::Ice::AsyncResultPtr begin_processZGMPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::ListStringList& listListAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_processZGMPEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGMPEvents(listEvent, listListAppNodeID, listIsPublishEvent, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_processZGMPEvents.
     * @param result The asynchronous result object for the invocation.
     */
    void end_processZGMPEvents(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_processZGMPEvents(const ::ZG6000::ListStringMap&, const ::ZG6000::ListStringList&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    void processZGMPDatasetEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_processZGMPDatasetEvents(_iceI_begin_processZGMPDatasetEvents(listEvent, listAppNodeID, listIsPublishEvent, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    ::Ice::AsyncResultPtr begin_processZGMPDatasetEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_processZGMPDatasetEvents(listEvent, listAppNodeID, listIsPublishEvent, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    ::Ice::AsyncResultPtr begin_processZGMPDatasetEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGMPDatasetEvents(listEvent, listAppNodeID, listIsPublishEvent, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    ::Ice::AsyncResultPtr begin_processZGMPDatasetEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGMPDatasetEvents(listEvent, listAppNodeID, listIsPublishEvent, context, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    ::Ice::AsyncResultPtr begin_processZGMPDatasetEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::ZG6000::Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGMPDatasetEvents(listEvent, listAppNodeID, listIsPublishEvent, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    ::Ice::AsyncResultPtr begin_processZGMPDatasetEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listAppNodeID, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGMPDatasetEvents(listEvent, listAppNodeID, listIsPublishEvent, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_processZGMPDatasetEvents.
     * @param result The asynchronous result object for the invocation.
     */
    void end_processZGMPDatasetEvents(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_processZGMPDatasetEvents(const ::ZG6000::ListStringMap&, const ::ZG6000::StringList&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @brief   处理系统平台事件
     */
    void processZGSPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_processZGSPEvents(_iceI_begin_processZGSPEvents(listEvent, listIsPublishEvent, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理系统平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGSPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_processZGSPEvents(listEvent, listIsPublishEvent, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理系统平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGSPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGSPEvents(listEvent, listIsPublishEvent, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理系统平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGSPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGSPEvents(listEvent, listIsPublishEvent, context, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理系统平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGSPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::ZG6000::Callback_ZGSPEventProcess_processZGSPEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGSPEvents(listEvent, listIsPublishEvent, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理系统平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGSPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_processZGSPEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGSPEvents(listEvent, listIsPublishEvent, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_processZGSPEvents.
     * @param result The asynchronous result object for the invocation.
     */
    void end_processZGSPEvents(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_processZGSPEvents(const ::ZG6000::ListStringMap&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @brief   处理设备管理平台事件
     */
    void processZGDPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_processZGDPEvents(_iceI_begin_processZGDPEvents(listEvent, listIsPublishEvent, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理设备管理平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGDPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_processZGDPEvents(listEvent, listIsPublishEvent, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理设备管理平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGDPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGDPEvents(listEvent, listIsPublishEvent, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理设备管理平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGDPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGDPEvents(listEvent, listIsPublishEvent, context, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理设备管理平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGDPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::ZG6000::Callback_ZGSPEventProcess_processZGDPEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGDPEvents(listEvent, listIsPublishEvent, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理设备管理平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGDPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_processZGDPEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGDPEvents(listEvent, listIsPublishEvent, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_processZGDPEvents.
     * @param result The asynchronous result object for the invocation.
     */
    void end_processZGDPEvents(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_processZGDPEvents(const ::ZG6000::ListStringMap&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @brief   处理运维管理平台事件
     */
    void processZGOPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_processZGOPEvents(_iceI_begin_processZGOPEvents(listEvent, listIsPublishEvent, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理运维管理平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGOPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_processZGOPEvents(listEvent, listIsPublishEvent, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理运维管理平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGOPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGOPEvents(listEvent, listIsPublishEvent, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理运维管理平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGOPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGOPEvents(listEvent, listIsPublishEvent, context, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理运维管理平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGOPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::ZG6000::Callback_ZGSPEventProcess_processZGOPEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGOPEvents(listEvent, listIsPublishEvent, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理运维管理平台事件
     */
    ::Ice::AsyncResultPtr begin_processZGOPEvents(const ::ZG6000::ListStringMap& listEvent, const ::ZG6000::StringList& listIsPublishEvent, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_processZGOPEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processZGOPEvents(listEvent, listIsPublishEvent, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_processZGOPEvents.
     * @param result The asynchronous result object for the invocation.
     */
    void end_processZGOPEvents(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_processZGOPEvents(const ::ZG6000::ListStringMap&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param event 要处理的事件
     * @param context The Context map to send with the invocation.
     * @brief   处理事件(其他服务调用)
     */
    void processEvent(const ::ZG6000::StringMap& event, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_processEvent(_iceI_begin_processEvent(event, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param event 要处理的事件
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_processEvent(const ::ZG6000::StringMap& event, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_processEvent(event, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param event 要处理的事件
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_processEvent(const ::ZG6000::StringMap& event, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processEvent(event, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param event 要处理的事件
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_processEvent(const ::ZG6000::StringMap& event, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processEvent(event, context, cb, cookie);
    }

    /**
     * @param event 要处理的事件
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_processEvent(const ::ZG6000::StringMap& event, const ::ZG6000::Callback_ZGSPEventProcess_processEventPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processEvent(event, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param event 要处理的事件
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   处理事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_processEvent(const ::ZG6000::StringMap& event, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_processEventPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_processEvent(event, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_processEvent.
     * @param result The asynchronous result object for the invocation.
     */
    void end_processEvent(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_processEvent(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @brief   确认事件(其他服务调用)
     */
    bool confirmEvents(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_confirmEvents(e, _iceI_begin_confirmEvents(listEvent, userID, userName, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   确认事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_confirmEvents(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_confirmEvents(listEvent, userID, userName, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   确认事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_confirmEvents(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmEvents(listEvent, userID, userName, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   确认事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_confirmEvents(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmEvents(listEvent, userID, userName, context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   确认事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_confirmEvents(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::ZG6000::Callback_ZGSPEventProcess_confirmEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmEvents(listEvent, userID, userName, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   确认事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_confirmEvents(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_confirmEventsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmEvents(listEvent, userID, userName, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_confirmEvents.
     * @param result The asynchronous result object for the invocation.
     */
    bool end_confirmEvents(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_confirmEvents(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_confirmEvents(const ::ZG6000::ListStringMap&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @brief   确认设备事件(其他服务调用)
     */
    bool confirmDPEvent(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_confirmDPEvent(e, _iceI_begin_confirmDPEvent(listEvent, userID, userName, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   确认设备事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_confirmDPEvent(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_confirmDPEvent(listEvent, userID, userName, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   确认设备事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_confirmDPEvent(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmDPEvent(listEvent, userID, userName, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   确认设备事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_confirmDPEvent(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmDPEvent(listEvent, userID, userName, context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   确认设备事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_confirmDPEvent(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::ZG6000::Callback_ZGSPEventProcess_confirmDPEventPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmDPEvent(listEvent, userID, userName, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief   确认设备事件(其他服务调用)
     */
    ::Ice::AsyncResultPtr begin_confirmDPEvent(const ::ZG6000::ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_confirmDPEventPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmDPEvent(listEvent, userID, userName, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_confirmDPEvent.
     * @param result The asynchronous result object for the invocation.
     */
    bool end_confirmDPEvent(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_confirmDPEvent(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_confirmDPEvent(const ::ZG6000::ListStringMap&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getUnconfirmedEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, ::ZG6000::ListStringMap& listEvent, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getUnconfirmedEvent(listEvent, e, _iceI_begin_getUnconfirmedEvent(appNodeID, subsystemID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getUnconfirmedEvent(appNodeID, subsystemID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedEvent(appNodeID, subsystemID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedEvent(appNodeID, subsystemID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::ZG6000::Callback_ZGSPEventProcess_getUnconfirmedEventPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedEvent(appNodeID, subsystemID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_getUnconfirmedEventPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedEvent(appNodeID, subsystemID, context, cb, cookie);
    }

    bool end_getUnconfirmedEvent(::ZG6000::ListStringMap& listEvent, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getUnconfirmedEvent(::ZG6000::ListStringMap& iceP_listEvent, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getUnconfirmedEvent(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getUnconfirmedPosEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& position, ::ZG6000::ListStringMap& listEvent, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getUnconfirmedPosEvent(listEvent, e, _iceI_begin_getUnconfirmedPosEvent(appNodeID, subsystemID, position, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedPosEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& position, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getUnconfirmedPosEvent(appNodeID, subsystemID, position, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedPosEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& position, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedPosEvent(appNodeID, subsystemID, position, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedPosEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& position, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedPosEvent(appNodeID, subsystemID, position, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedPosEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& position, const ::ZG6000::Callback_ZGSPEventProcess_getUnconfirmedPosEventPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedPosEvent(appNodeID, subsystemID, position, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedPosEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& position, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_getUnconfirmedPosEventPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedPosEvent(appNodeID, subsystemID, position, context, cb, cookie);
    }

    bool end_getUnconfirmedPosEvent(::ZG6000::ListStringMap& listEvent, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getUnconfirmedPosEvent(::ZG6000::ListStringMap& iceP_listEvent, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getUnconfirmedPosEvent(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getUnconfirmedEventNum(::ZG6000::StringMap& appNodeEventNum, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getUnconfirmedEventNum(appNodeEventNum, e, _iceI_begin_getUnconfirmedEventNum(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedEventNum(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getUnconfirmedEventNum(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedEventNum(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedEventNum(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedEventNum(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedEventNum(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedEventNum(const ::ZG6000::Callback_ZGSPEventProcess_getUnconfirmedEventNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedEventNum(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUnconfirmedEventNum(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPEventProcess_getUnconfirmedEventNumPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUnconfirmedEventNum(context, cb, cookie);
    }

    bool end_getUnconfirmedEventNum(::ZG6000::StringMap& appNodeEventNum, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getUnconfirmedEventNum(::ZG6000::StringMap& iceP_appNodeEventNum, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getUnconfirmedEventNum(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPEventProcess : virtual public ZGServerBase
{
public:

    typedef ZGSPEventProcessPrx ProxyType;
    typedef ZGSPEventProcessPtr PointerType;

    virtual ~ZGSPEventProcess();

#ifdef ICE_CPP11_COMPILER
    ZGSPEventProcess() = default;
    ZGSPEventProcess(const ZGSPEventProcess&) = default;
    ZGSPEventProcess& operator=(const ZGSPEventProcess&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listListAppNodeID 每个事件关联的应用节点ID列表的列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param current The Current object for the invocation.
     * @brief   处理监控平台事件
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    virtual void processZGMPEvents(const ListStringMap& listEvent, const ListStringList& listListAppNodeID, const StringList& listIsPublishEvent, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_processZGMPEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listAppNodeID 事件列表中的所有事件关联的应用节点ID列表
     * @param listIsPublishEvent 是否发布事件列表
     * @param current The Current object for the invocation.
     * @brief   处理监控平台数据集事件
     * @attention 该接口只能用于处理数据集事件
     */
    virtual void processZGMPDatasetEvents(const ListStringMap& listEvent, const StringList& listAppNodeID, const StringList& listIsPublishEvent, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_processZGMPDatasetEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param current The Current object for the invocation.
     * @brief   处理系统平台事件
     */
    virtual void processZGSPEvents(const ListStringMap& listEvent, const StringList& listIsPublishEvent, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_processZGSPEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param current The Current object for the invocation.
     * @brief   处理设备管理平台事件
     */
    virtual void processZGDPEvents(const ListStringMap& listEvent, const StringList& listIsPublishEvent, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_processZGDPEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listEvent 事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param listIsPublishEvent 是否发布事件列表
     * @param current The Current object for the invocation.
     * @brief   处理运维管理平台事件
     */
    virtual void processZGOPEvents(const ListStringMap& listEvent, const StringList& listIsPublishEvent, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_processZGOPEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param event 要处理的事件
     * @param current The Current object for the invocation.
     * @brief   处理事件(其他服务调用)
     */
    virtual void processEvent(const StringMap& event, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_processEvent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @brief   确认事件(其他服务调用)
     */
    virtual bool confirmEvents(const ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmEvents(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @brief   确认设备事件(其他服务调用)
     */
    virtual bool confirmDPEvent(const ListStringMap& listEvent, const ::std::string& userID, const ::std::string& userName, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmDPEvent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getUnconfirmedEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, ListStringMap& listEvent, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getUnconfirmedEvent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getUnconfirmedPosEvent(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& position, ListStringMap& listEvent, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getUnconfirmedPosEvent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getUnconfirmedEventNum(StringMap& appNodeEventNum, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getUnconfirmedEventNum(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPEventProcess& lhs, const ZGSPEventProcess& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPEventProcess& lhs, const ZGSPEventProcess& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGMPEvents.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_processZGMPEvents : public Callback_ZGSPEventProcess_processZGMPEvents_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPEventProcess_processZGMPEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGMPEventsPtr
newCallback_ZGSPEventProcess_processZGMPEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGMPEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGMPEventsPtr
newCallback_ZGSPEventProcess_processZGMPEvents(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGMPEvents<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGMPEventsPtr
newCallback_ZGSPEventProcess_processZGMPEvents(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGMPEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGMPEventsPtr
newCallback_ZGSPEventProcess_processZGMPEvents(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGMPEvents<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGMPEvents.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_processZGMPEvents : public Callback_ZGSPEventProcess_processZGMPEvents_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPEventProcess_processZGMPEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGMPEventsPtr
newCallback_ZGSPEventProcess_processZGMPEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGMPEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGMPEventsPtr
newCallback_ZGSPEventProcess_processZGMPEvents(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGMPEvents<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGMPEventsPtr
newCallback_ZGSPEventProcess_processZGMPEvents(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGMPEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGMPEventsPtr
newCallback_ZGSPEventProcess_processZGMPEvents(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGMPEvents<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGMPDatasetEvents.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_processZGMPDatasetEvents : public Callback_ZGSPEventProcess_processZGMPDatasetEvents_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPEventProcess_processZGMPDatasetEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr
newCallback_ZGSPEventProcess_processZGMPDatasetEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGMPDatasetEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr
newCallback_ZGSPEventProcess_processZGMPDatasetEvents(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGMPDatasetEvents<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr
newCallback_ZGSPEventProcess_processZGMPDatasetEvents(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGMPDatasetEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr
newCallback_ZGSPEventProcess_processZGMPDatasetEvents(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGMPDatasetEvents<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGMPDatasetEvents.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_processZGMPDatasetEvents : public Callback_ZGSPEventProcess_processZGMPDatasetEvents_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPEventProcess_processZGMPDatasetEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr
newCallback_ZGSPEventProcess_processZGMPDatasetEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGMPDatasetEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr
newCallback_ZGSPEventProcess_processZGMPDatasetEvents(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGMPDatasetEvents<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr
newCallback_ZGSPEventProcess_processZGMPDatasetEvents(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGMPDatasetEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGMPDatasetEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGMPDatasetEventsPtr
newCallback_ZGSPEventProcess_processZGMPDatasetEvents(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGMPDatasetEvents<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGSPEvents.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_processZGSPEvents : public Callback_ZGSPEventProcess_processZGSPEvents_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPEventProcess_processZGSPEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGSPEventsPtr
newCallback_ZGSPEventProcess_processZGSPEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGSPEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGSPEventsPtr
newCallback_ZGSPEventProcess_processZGSPEvents(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGSPEvents<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGSPEventsPtr
newCallback_ZGSPEventProcess_processZGSPEvents(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGSPEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGSPEventsPtr
newCallback_ZGSPEventProcess_processZGSPEvents(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGSPEvents<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGSPEvents.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_processZGSPEvents : public Callback_ZGSPEventProcess_processZGSPEvents_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPEventProcess_processZGSPEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGSPEventsPtr
newCallback_ZGSPEventProcess_processZGSPEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGSPEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGSPEventsPtr
newCallback_ZGSPEventProcess_processZGSPEvents(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGSPEvents<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGSPEventsPtr
newCallback_ZGSPEventProcess_processZGSPEvents(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGSPEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGSPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGSPEventsPtr
newCallback_ZGSPEventProcess_processZGSPEvents(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGSPEvents<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGDPEvents.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_processZGDPEvents : public Callback_ZGSPEventProcess_processZGDPEvents_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPEventProcess_processZGDPEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGDPEventsPtr
newCallback_ZGSPEventProcess_processZGDPEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGDPEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGDPEventsPtr
newCallback_ZGSPEventProcess_processZGDPEvents(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGDPEvents<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGDPEventsPtr
newCallback_ZGSPEventProcess_processZGDPEvents(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGDPEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGDPEventsPtr
newCallback_ZGSPEventProcess_processZGDPEvents(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGDPEvents<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGDPEvents.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_processZGDPEvents : public Callback_ZGSPEventProcess_processZGDPEvents_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPEventProcess_processZGDPEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGDPEventsPtr
newCallback_ZGSPEventProcess_processZGDPEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGDPEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGDPEventsPtr
newCallback_ZGSPEventProcess_processZGDPEvents(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGDPEvents<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGDPEventsPtr
newCallback_ZGSPEventProcess_processZGDPEvents(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGDPEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGDPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGDPEventsPtr
newCallback_ZGSPEventProcess_processZGDPEvents(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGDPEvents<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGOPEvents.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_processZGOPEvents : public Callback_ZGSPEventProcess_processZGOPEvents_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPEventProcess_processZGOPEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGOPEventsPtr
newCallback_ZGSPEventProcess_processZGOPEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGOPEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGOPEventsPtr
newCallback_ZGSPEventProcess_processZGOPEvents(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGOPEvents<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGOPEventsPtr
newCallback_ZGSPEventProcess_processZGOPEvents(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGOPEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 */
template<class T> Callback_ZGSPEventProcess_processZGOPEventsPtr
newCallback_ZGSPEventProcess_processZGOPEvents(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processZGOPEvents<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processZGOPEvents.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_processZGOPEvents : public Callback_ZGSPEventProcess_processZGOPEvents_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPEventProcess_processZGOPEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGOPEventsPtr
newCallback_ZGSPEventProcess_processZGOPEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGOPEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGOPEventsPtr
newCallback_ZGSPEventProcess_processZGOPEvents(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGOPEvents<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGOPEventsPtr
newCallback_ZGSPEventProcess_processZGOPEvents(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGOPEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processZGOPEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processZGOPEventsPtr
newCallback_ZGSPEventProcess_processZGOPEvents(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processZGOPEvents<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processEvent.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_processEvent : public Callback_ZGSPEventProcess_processEvent_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPEventProcess_processEvent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 */
template<class T> Callback_ZGSPEventProcess_processEventPtr
newCallback_ZGSPEventProcess_processEvent(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processEvent<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 */
template<class T> Callback_ZGSPEventProcess_processEventPtr
newCallback_ZGSPEventProcess_processEvent(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processEvent<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 */
template<class T> Callback_ZGSPEventProcess_processEventPtr
newCallback_ZGSPEventProcess_processEvent(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processEvent<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 */
template<class T> Callback_ZGSPEventProcess_processEventPtr
newCallback_ZGSPEventProcess_processEvent(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_processEvent<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_processEvent.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_processEvent : public Callback_ZGSPEventProcess_processEvent_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPEventProcess_processEvent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processEventPtr
newCallback_ZGSPEventProcess_processEvent(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processEvent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processEventPtr
newCallback_ZGSPEventProcess_processEvent(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processEvent<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processEventPtr
newCallback_ZGSPEventProcess_processEvent(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processEvent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_processEvent.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_processEventPtr
newCallback_ZGSPEventProcess_processEvent(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_processEvent<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_confirmEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_confirmEvents.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_confirmEvents : public Callback_ZGSPEventProcess_confirmEvents_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPEventProcess_confirmEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPEventProcessPrx proxy = ZGSPEventProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmEvents(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_confirmEvents.
 */
template<class T> Callback_ZGSPEventProcess_confirmEventsPtr
newCallback_ZGSPEventProcess_confirmEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_confirmEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_confirmEvents.
 */
template<class T> Callback_ZGSPEventProcess_confirmEventsPtr
newCallback_ZGSPEventProcess_confirmEvents(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_confirmEvents<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_confirmEvents.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_confirmEvents.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_confirmEvents : public Callback_ZGSPEventProcess_confirmEvents_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPEventProcess_confirmEvents(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPEventProcessPrx proxy = ZGSPEventProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmEvents(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_confirmEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_confirmEventsPtr
newCallback_ZGSPEventProcess_confirmEvents(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_confirmEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_confirmEvents.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_confirmEventsPtr
newCallback_ZGSPEventProcess_confirmEvents(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_confirmEvents<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_confirmDPEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_confirmDPEvent.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_confirmDPEvent : public Callback_ZGSPEventProcess_confirmDPEvent_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPEventProcess_confirmDPEvent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPEventProcessPrx proxy = ZGSPEventProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmDPEvent(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_confirmDPEvent.
 */
template<class T> Callback_ZGSPEventProcess_confirmDPEventPtr
newCallback_ZGSPEventProcess_confirmDPEvent(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_confirmDPEvent<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_confirmDPEvent.
 */
template<class T> Callback_ZGSPEventProcess_confirmDPEventPtr
newCallback_ZGSPEventProcess_confirmDPEvent(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_confirmDPEvent<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_confirmDPEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_confirmDPEvent.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_confirmDPEvent : public Callback_ZGSPEventProcess_confirmDPEvent_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPEventProcess_confirmDPEvent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPEventProcessPrx proxy = ZGSPEventProcessPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmDPEvent(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_confirmDPEvent.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_confirmDPEventPtr
newCallback_ZGSPEventProcess_confirmDPEvent(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_confirmDPEvent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_confirmDPEvent.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_confirmDPEventPtr
newCallback_ZGSPEventProcess_confirmDPEvent(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_confirmDPEvent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_getUnconfirmedEvent.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_getUnconfirmedEvent : public Callback_ZGSPEventProcess_getUnconfirmedEvent_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPEventProcess_getUnconfirmedEvent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPEventProcessPrx proxy = ZGSPEventProcessPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listEvent;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUnconfirmedEvent(iceP_listEvent, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listEvent, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEvent.
 */
template<class T> Callback_ZGSPEventProcess_getUnconfirmedEventPtr
newCallback_ZGSPEventProcess_getUnconfirmedEvent(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_getUnconfirmedEvent<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEvent.
 */
template<class T> Callback_ZGSPEventProcess_getUnconfirmedEventPtr
newCallback_ZGSPEventProcess_getUnconfirmedEvent(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_getUnconfirmedEvent<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_getUnconfirmedEvent.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_getUnconfirmedEvent : public Callback_ZGSPEventProcess_getUnconfirmedEvent_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPEventProcess_getUnconfirmedEvent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPEventProcessPrx proxy = ZGSPEventProcessPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listEvent;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUnconfirmedEvent(iceP_listEvent, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listEvent, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEvent.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_getUnconfirmedEventPtr
newCallback_ZGSPEventProcess_getUnconfirmedEvent(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_getUnconfirmedEvent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEvent.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_getUnconfirmedEventPtr
newCallback_ZGSPEventProcess_getUnconfirmedEvent(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_getUnconfirmedEvent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedPosEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_getUnconfirmedPosEvent.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_getUnconfirmedPosEvent : public Callback_ZGSPEventProcess_getUnconfirmedPosEvent_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPEventProcess_getUnconfirmedPosEvent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPEventProcessPrx proxy = ZGSPEventProcessPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listEvent;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUnconfirmedPosEvent(iceP_listEvent, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listEvent, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedPosEvent.
 */
template<class T> Callback_ZGSPEventProcess_getUnconfirmedPosEventPtr
newCallback_ZGSPEventProcess_getUnconfirmedPosEvent(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_getUnconfirmedPosEvent<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedPosEvent.
 */
template<class T> Callback_ZGSPEventProcess_getUnconfirmedPosEventPtr
newCallback_ZGSPEventProcess_getUnconfirmedPosEvent(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_getUnconfirmedPosEvent<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedPosEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_getUnconfirmedPosEvent.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_getUnconfirmedPosEvent : public Callback_ZGSPEventProcess_getUnconfirmedPosEvent_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPEventProcess_getUnconfirmedPosEvent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPEventProcessPrx proxy = ZGSPEventProcessPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listEvent;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUnconfirmedPosEvent(iceP_listEvent, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listEvent, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedPosEvent.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_getUnconfirmedPosEventPtr
newCallback_ZGSPEventProcess_getUnconfirmedPosEvent(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_getUnconfirmedPosEvent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedPosEvent.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_getUnconfirmedPosEventPtr
newCallback_ZGSPEventProcess_getUnconfirmedPosEvent(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_getUnconfirmedPosEvent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEventNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_getUnconfirmedEventNum.
 */
template<class T>
class CallbackNC_ZGSPEventProcess_getUnconfirmedEventNum : public Callback_ZGSPEventProcess_getUnconfirmedEventNum_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGSPEventProcess_getUnconfirmedEventNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPEventProcessPrx proxy = ZGSPEventProcessPrx::uncheckedCast(result->getProxy());
        StringMap iceP_appNodeEventNum;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUnconfirmedEventNum(iceP_appNodeEventNum, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_appNodeEventNum, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEventNum.
 */
template<class T> Callback_ZGSPEventProcess_getUnconfirmedEventNumPtr
newCallback_ZGSPEventProcess_getUnconfirmedEventNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_getUnconfirmedEventNum<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEventNum.
 */
template<class T> Callback_ZGSPEventProcess_getUnconfirmedEventNumPtr
newCallback_ZGSPEventProcess_getUnconfirmedEventNum(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPEventProcess_getUnconfirmedEventNum<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEventNum.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPEventProcess_getUnconfirmedEventNum.
 */
template<class T, typename CT>
class Callback_ZGSPEventProcess_getUnconfirmedEventNum : public Callback_ZGSPEventProcess_getUnconfirmedEventNum_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPEventProcess_getUnconfirmedEventNum(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPEventProcessPrx proxy = ZGSPEventProcessPrx::uncheckedCast(result->getProxy());
        StringMap iceP_appNodeEventNum;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUnconfirmedEventNum(iceP_appNodeEventNum, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_appNodeEventNum, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEventNum.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_getUnconfirmedEventNumPtr
newCallback_ZGSPEventProcess_getUnconfirmedEventNum(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_getUnconfirmedEventNum<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPEventProcess::begin_getUnconfirmedEventNum.
 */
template<class T, typename CT> Callback_ZGSPEventProcess_getUnconfirmedEventNumPtr
newCallback_ZGSPEventProcess_getUnconfirmedEventNum(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPEventProcess_getUnconfirmedEventNum<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

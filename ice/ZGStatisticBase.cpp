//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGStatisticBase.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGStatisticBase.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGStatisticBase_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGServerBase",
    "::ZG6000::ZGStatisticBase"
};
const ::std::string iceC_ZG6000_ZGStatisticBase_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "statistic",
    "statisticBatch",
    "statisticStart",
    "statisticStartAndCalc",
    "statisticStartBatch",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGStatisticBase_statistic_name = "statistic";
const ::std::string iceC_ZG6000_ZGStatisticBase_statisticBatch_name = "statisticBatch";
const ::std::string iceC_ZG6000_ZGStatisticBase_statisticStart_name = "statisticStart";
const ::std::string iceC_ZG6000_ZGStatisticBase_statisticStartAndCalc_name = "statisticStartAndCalc";
const ::std::string iceC_ZG6000_ZGStatisticBase_statisticStartBatch_name = "statisticStartBatch";

}

bool
ZG6000::ZGStatisticBase::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGStatisticBase_ids, iceC_ZG6000_ZGStatisticBase_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGStatisticBase::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGStatisticBase_ids[0], &iceC_ZG6000_ZGStatisticBase_ids[3]);
}

::std::string
ZG6000::ZGStatisticBase::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGStatisticBase::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGStatisticBase";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceD_statistic(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_id;
    istr->readAll(iceP_id);
    inS.endReadParams();
    this->statistic(::std::move(iceP_id), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceD_statisticBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listID;
    istr->readAll(iceP_listID);
    inS.endReadParams();
    this->statisticBatch(::std::move(iceP_listID), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceD_statisticStart(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_id;
    istr->readAll(iceP_id);
    inS.endReadParams();
    this->statisticStart(::std::move(iceP_id), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceD_statisticStartAndCalc(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_id;
    istr->readAll(iceP_id);
    inS.endReadParams();
    this->statisticStartAndCalc(::std::move(iceP_id), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceD_statisticStartBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listID;
    istr->readAll(iceP_listID);
    inS.endReadParams();
    this->statisticStartBatch(::std::move(iceP_listID), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGStatisticBase_ops, iceC_ZG6000_ZGStatisticBase_ops + 20, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGStatisticBase_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getVersion(in, current);
        }
        case 4:
        {
            return _iceD_heartDebug(in, current);
        }
        case 5:
        {
            return _iceD_ice_id(in, current);
        }
        case 6:
        {
            return _iceD_ice_ids(in, current);
        }
        case 7:
        {
            return _iceD_ice_isA(in, current);
        }
        case 8:
        {
            return _iceD_ice_ping(in, current);
        }
        case 9:
        {
            return _iceD_isDebugging(in, current);
        }
        case 10:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 11:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 12:
        {
            return _iceD_startDebug(in, current);
        }
        case 13:
        {
            return _iceD_statistic(in, current);
        }
        case 14:
        {
            return _iceD_statisticBatch(in, current);
        }
        case 15:
        {
            return _iceD_statisticStart(in, current);
        }
        case 16:
        {
            return _iceD_statisticStartAndCalc(in, current);
        }
        case 17:
        {
            return _iceD_statisticStartBatch(in, current);
        }
        case 18:
        {
            return _iceD_stopDebug(in, current);
        }
        case 19:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGStatisticBasePrx::_iceI_statistic(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_id, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGStatisticBase_statistic_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_id);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGStatisticBasePrx::_iceI_statisticBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const StringList& iceP_listID, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGStatisticBase_statisticBatch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listID);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGStatisticBasePrx::_iceI_statisticStart(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_id, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGStatisticBase_statisticStart_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_id);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGStatisticBasePrx::_iceI_statisticStartAndCalc(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_id, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGStatisticBase_statisticStartAndCalc_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_id);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGStatisticBasePrx::_iceI_statisticStartBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const StringList& iceP_listID, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGStatisticBase_statisticStartBatch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listID);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGStatisticBasePrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGStatisticBasePrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGStatisticBasePrx::ice_staticId()
{
    return ZGStatisticBase::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGStatisticBase_statistic_name = "statistic";

const ::std::string iceC_ZG6000_ZGStatisticBase_statisticBatch_name = "statisticBatch";

const ::std::string iceC_ZG6000_ZGStatisticBase_statisticStart_name = "statisticStart";

const ::std::string iceC_ZG6000_ZGStatisticBase_statisticStartAndCalc_name = "statisticStartAndCalc";

const ::std::string iceC_ZG6000_ZGStatisticBase_statisticStartBatch_name = "statisticStartBatch";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGStatisticBase* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGStatisticBase>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGStatisticBase;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGStatisticBase::_iceI_begin_statistic(const ::std::string& iceP_id, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGStatisticBase_statistic_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGStatisticBase_statistic_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_id);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGStatisticBase_statistic_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGStatisticBase::end_statistic(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGStatisticBase_statistic_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGStatisticBase::_iceI_begin_statisticBatch(const ::ZG6000::StringList& iceP_listID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGStatisticBase_statisticBatch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGStatisticBase_statisticBatch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGStatisticBase_statisticBatch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGStatisticBase::end_statisticBatch(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGStatisticBase_statisticBatch_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGStatisticBase::_iceI_begin_statisticStart(const ::std::string& iceP_id, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGStatisticBase_statisticStart_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGStatisticBase_statisticStart_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_id);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGStatisticBase_statisticStart_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGStatisticBase::end_statisticStart(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGStatisticBase_statisticStart_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGStatisticBase::_iceI_begin_statisticStartAndCalc(const ::std::string& iceP_id, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGStatisticBase_statisticStartAndCalc_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGStatisticBase_statisticStartAndCalc_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_id);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGStatisticBase_statisticStartAndCalc_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGStatisticBase::end_statisticStartAndCalc(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGStatisticBase_statisticStartAndCalc_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGStatisticBase::_iceI_begin_statisticStartBatch(const ::ZG6000::StringList& iceP_listID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGStatisticBase_statisticStartBatch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGStatisticBase_statisticStartBatch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGStatisticBase_statisticStartBatch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGStatisticBase::end_statisticStartBatch(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGStatisticBase_statisticStartBatch_name);
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGStatisticBase::_newInstance() const
{
    return new ZGStatisticBase;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGStatisticBase::ice_staticId()
{
    return ::ZG6000::ZGStatisticBase::ice_staticId();
}

ZG6000::ZGStatisticBase::~ZGStatisticBase()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGStatisticBase* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGStatisticBase_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGServerBase",
    "::ZG6000::ZGStatisticBase"
};

}

bool
ZG6000::ZGStatisticBase::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGStatisticBase_ids, iceC_ZG6000_ZGStatisticBase_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGStatisticBase::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGStatisticBase_ids[0], &iceC_ZG6000_ZGStatisticBase_ids[3]);
}

const ::std::string&
ZG6000::ZGStatisticBase::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGStatisticBase::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGStatisticBase";
    return typeId;
#else
    return iceC_ZG6000_ZGStatisticBase_ids[2];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceD_statistic(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_id;
    istr->read(iceP_id);
    inS.endReadParams();
    this->statistic(iceP_id, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceD_statisticBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listID;
    istr->read(iceP_listID);
    inS.endReadParams();
    this->statisticBatch(iceP_listID, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceD_statisticStart(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_id;
    istr->read(iceP_id);
    inS.endReadParams();
    this->statisticStart(iceP_id, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceD_statisticStartAndCalc(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_id;
    istr->read(iceP_id);
    inS.endReadParams();
    this->statisticStartAndCalc(iceP_id, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceD_statisticStartBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listID;
    istr->read(iceP_listID);
    inS.endReadParams();
    this->statisticStartBatch(iceP_listID, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGStatisticBase_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "statistic",
    "statisticBatch",
    "statisticStart",
    "statisticStartAndCalc",
    "statisticStartBatch",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGStatisticBase::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGStatisticBase_all, iceC_ZG6000_ZGStatisticBase_all + 20, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGStatisticBase_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getVersion(in, current);
        }
        case 4:
        {
            return _iceD_heartDebug(in, current);
        }
        case 5:
        {
            return _iceD_ice_id(in, current);
        }
        case 6:
        {
            return _iceD_ice_ids(in, current);
        }
        case 7:
        {
            return _iceD_ice_isA(in, current);
        }
        case 8:
        {
            return _iceD_ice_ping(in, current);
        }
        case 9:
        {
            return _iceD_isDebugging(in, current);
        }
        case 10:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 11:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 12:
        {
            return _iceD_startDebug(in, current);
        }
        case 13:
        {
            return _iceD_statistic(in, current);
        }
        case 14:
        {
            return _iceD_statisticBatch(in, current);
        }
        case 15:
        {
            return _iceD_statisticStart(in, current);
        }
        case 16:
        {
            return _iceD_statisticStartAndCalc(in, current);
        }
        case 17:
        {
            return _iceD_statisticStartBatch(in, current);
        }
        case 18:
        {
            return _iceD_stopDebug(in, current);
        }
        case 19:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGStatisticBase::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGStatisticBase, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGStatisticBase::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGStatisticBase, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGStatisticBasePtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGStatisticBasePtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGStatisticBase::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

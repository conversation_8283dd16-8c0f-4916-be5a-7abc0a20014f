//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPModifyOnline.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPModifyOnline_h__
#define __ZGSPModifyOnline_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPModifyOnline;
class ZGSPModifyOnlinePrx;

}

namespace ZG6000
{

class ZGSPModifyOnline : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPModifyOnlinePrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to syncData.
     */
    struct SyncDataResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool syncData(ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_syncData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPModifyOnlinePrx : public virtual ::Ice::Proxy<ZGSPModifyOnlinePrx, ZGServerBasePrx>
{
public:

    bool syncData(ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPModifyOnline::SyncDataResult>(true, this, &ZGSPModifyOnlinePrx::_iceI_syncData, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto syncDataAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPModifyOnline::SyncDataResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPModifyOnline::SyncDataResult, P>(false, this, &ZGSPModifyOnlinePrx::_iceI_syncData, context);
    }

    ::std::function<void()>
    syncDataAsync(::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPModifyOnline::SyncDataResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPModifyOnline::SyncDataResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPModifyOnlinePrx::_iceI_syncData, context);
    }

    /// \cond INTERNAL
    void _iceI_syncData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPModifyOnline::SyncDataResult>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPModifyOnlinePrx() = default;
    friend ::std::shared_ptr<ZGSPModifyOnlinePrx> IceInternal::createProxy<ZGSPModifyOnlinePrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPModifyOnlinePtr = ::std::shared_ptr<ZGSPModifyOnline>;
using ZGSPModifyOnlinePrxPtr = ::std::shared_ptr<ZGSPModifyOnlinePrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPModifyOnline;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPModifyOnline>&);
::IceProxy::Ice::Object* upCast(ZGSPModifyOnline*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPModifyOnline;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPModifyOnline*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPModifyOnline> ZGSPModifyOnlinePtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPModifyOnline> ZGSPModifyOnlinePrx;
typedef ZGSPModifyOnlinePrx ZGSPModifyOnlinePrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPModifyOnlinePtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPModifyOnline::begin_syncData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPModifyOnline_syncData.
 */
class Callback_ZGSPModifyOnline_syncData_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPModifyOnline_syncData_Base> Callback_ZGSPModifyOnline_syncDataPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPModifyOnline : public virtual ::Ice::Proxy<ZGSPModifyOnline, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool syncData(::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_syncData(e, _iceI_begin_syncData(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_syncData(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_syncData(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_syncData(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_syncData(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_syncData(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_syncData(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_syncData(const ::ZG6000::Callback_ZGSPModifyOnline_syncDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_syncData(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_syncData(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPModifyOnline_syncDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_syncData(context, cb, cookie);
    }

    bool end_syncData(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_syncData(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_syncData(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPModifyOnline : virtual public ZGServerBase
{
public:

    typedef ZGSPModifyOnlinePrx ProxyType;
    typedef ZGSPModifyOnlinePtr PointerType;

    virtual ~ZGSPModifyOnline();

#ifdef ICE_CPP11_COMPILER
    ZGSPModifyOnline() = default;
    ZGSPModifyOnline(const ZGSPModifyOnline&) = default;
    ZGSPModifyOnline& operator=(const ZGSPModifyOnline&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool syncData(ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_syncData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPModifyOnline& lhs, const ZGSPModifyOnline& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPModifyOnline& lhs, const ZGSPModifyOnline& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPModifyOnline::begin_syncData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPModifyOnline_syncData.
 */
template<class T>
class CallbackNC_ZGSPModifyOnline_syncData : public Callback_ZGSPModifyOnline_syncData_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPModifyOnline_syncData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPModifyOnlinePrx proxy = ZGSPModifyOnlinePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_syncData(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPModifyOnline::begin_syncData.
 */
template<class T> Callback_ZGSPModifyOnline_syncDataPtr
newCallback_ZGSPModifyOnline_syncData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPModifyOnline_syncData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPModifyOnline::begin_syncData.
 */
template<class T> Callback_ZGSPModifyOnline_syncDataPtr
newCallback_ZGSPModifyOnline_syncData(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPModifyOnline_syncData<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPModifyOnline::begin_syncData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPModifyOnline_syncData.
 */
template<class T, typename CT>
class Callback_ZGSPModifyOnline_syncData : public Callback_ZGSPModifyOnline_syncData_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPModifyOnline_syncData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPModifyOnlinePrx proxy = ZGSPModifyOnlinePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_syncData(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPModifyOnline::begin_syncData.
 */
template<class T, typename CT> Callback_ZGSPModifyOnline_syncDataPtr
newCallback_ZGSPModifyOnline_syncData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPModifyOnline_syncData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPModifyOnline::begin_syncData.
 */
template<class T, typename CT> Callback_ZGSPModifyOnline_syncDataPtr
newCallback_ZGSPModifyOnline_syncData(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPModifyOnline_syncData<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

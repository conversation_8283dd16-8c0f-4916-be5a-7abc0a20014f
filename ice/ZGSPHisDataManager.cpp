//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPHisDataManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSPHisDataManager.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPHisDataManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPHisDataManager",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSPHisDataManager_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "queryStoreText",
    "queryStoreYc",
    "queryStoreYm",
    "queryStoreYx",
    "queryTableCount",
    "queryTableData",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryTableData_name = "queryTableData";
const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryTableCount_name = "queryTableCount";
const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryStoreYc_name = "queryStoreYc";
const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryStoreYx_name = "queryStoreYx";
const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryStoreText_name = "queryStoreText";
const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryStoreYm_name = "queryStoreYm";

}

bool
ZG6000::ZGSPHisDataManager::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPHisDataManager_ids, iceC_ZG6000_ZGSPHisDataManager_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSPHisDataManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSPHisDataManager_ids[0], &iceC_ZG6000_ZGSPHisDataManager_ids[3]);
}

::std::string
ZG6000::ZGSPHisDataManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPHisDataManager::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSPHisDataManager";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryTableData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_condition;
    int iceP_offset;
    int iceP_limit;
    ::std::string iceP_orderField;
    ::std::string iceP_orderType;
    istr->readAll(iceP_tableName, iceP_condition, iceP_offset, iceP_limit, iceP_orderField, iceP_orderType);
    inS.endReadParams();
    StringList iceP_listTitle;
    ListStringList iceP_listValues;
    ErrorInfo iceP_e;
    bool ret = this->queryTableData(::std::move(iceP_tableName), ::std::move(iceP_condition), iceP_offset, iceP_limit, ::std::move(iceP_orderField), ::std::move(iceP_orderType), iceP_listTitle, iceP_listValues, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listTitle, iceP_listValues, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryTableCount(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_condition;
    istr->readAll(iceP_tableName, iceP_condition);
    inS.endReadParams();
    int iceP_count;
    ErrorInfo iceP_e;
    bool ret = this->queryTableCount(::std::move(iceP_tableName), ::std::move(iceP_condition), iceP_count, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_count, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryStoreYc(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDevice;
    StringList iceP_listProperty;
    ::std::string iceP_startTime;
    ::std::string iceP_endTime;
    istr->readAll(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime);
    inS.endReadParams();
    ListStringMap iceP_listResult;
    ErrorInfo iceP_e;
    bool ret = this->queryStoreYc(::std::move(iceP_listDevice), ::std::move(iceP_listProperty), ::std::move(iceP_startTime), ::std::move(iceP_endTime), iceP_listResult, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listResult, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryStoreYx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDevice;
    StringList iceP_listProperty;
    ::std::string iceP_startTime;
    ::std::string iceP_endTime;
    istr->readAll(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime);
    inS.endReadParams();
    ListStringMap iceP_listResult;
    ErrorInfo iceP_e;
    bool ret = this->queryStoreYx(::std::move(iceP_listDevice), ::std::move(iceP_listProperty), ::std::move(iceP_startTime), ::std::move(iceP_endTime), iceP_listResult, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listResult, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryStoreText(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDevice;
    StringList iceP_listProperty;
    ::std::string iceP_startTime;
    ::std::string iceP_endTime;
    istr->readAll(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime);
    inS.endReadParams();
    ListStringMap iceP_listResult;
    ErrorInfo iceP_e;
    bool ret = this->queryStoreText(::std::move(iceP_listDevice), ::std::move(iceP_listProperty), ::std::move(iceP_startTime), ::std::move(iceP_endTime), iceP_listResult, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listResult, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryStoreYm(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listDevice;
    StringList iceP_listProperty;
    ::std::string iceP_startTime;
    ::std::string iceP_endTime;
    istr->readAll(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime);
    inS.endReadParams();
    ListStringMap iceP_listResult;
    ErrorInfo iceP_e;
    bool ret = this->queryStoreYm(::std::move(iceP_listDevice), ::std::move(iceP_listProperty), ::std::move(iceP_startTime), ::std::move(iceP_endTime), iceP_listResult, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listResult, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPHisDataManager_ops, iceC_ZG6000_ZGSPHisDataManager_ops + 21, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPHisDataManager_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getVersion(in, current);
        }
        case 4:
        {
            return _iceD_heartDebug(in, current);
        }
        case 5:
        {
            return _iceD_ice_id(in, current);
        }
        case 6:
        {
            return _iceD_ice_ids(in, current);
        }
        case 7:
        {
            return _iceD_ice_isA(in, current);
        }
        case 8:
        {
            return _iceD_ice_ping(in, current);
        }
        case 9:
        {
            return _iceD_isDebugging(in, current);
        }
        case 10:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 11:
        {
            return _iceD_queryStoreText(in, current);
        }
        case 12:
        {
            return _iceD_queryStoreYc(in, current);
        }
        case 13:
        {
            return _iceD_queryStoreYm(in, current);
        }
        case 14:
        {
            return _iceD_queryStoreYx(in, current);
        }
        case 15:
        {
            return _iceD_queryTableCount(in, current);
        }
        case 16:
        {
            return _iceD_queryTableData(in, current);
        }
        case 17:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 18:
        {
            return _iceD_startDebug(in, current);
        }
        case 19:
        {
            return _iceD_stopDebug(in, current);
        }
        case 20:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPHisDataManagerPrx::_iceI_queryTableData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryTableDataResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_condition, int iceP_offset, int iceP_limit, const ::std::string& iceP_orderField, const ::std::string& iceP_orderType, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryTableData_name);
    outAsync->invoke(iceC_ZG6000_ZGSPHisDataManager_queryTableData_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_condition, iceP_offset, iceP_limit, iceP_orderField, iceP_orderType);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPHisDataManager::QueryTableDataResult v;
            istr->readAll(v.listTitle, v.listValues, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPHisDataManagerPrx::_iceI_queryTableCount(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryTableCountResult>>& outAsync, const ::std::string& iceP_tableName, const ::std::string& iceP_condition, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryTableCount_name);
    outAsync->invoke(iceC_ZG6000_ZGSPHisDataManager_queryTableCount_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_tableName, iceP_condition);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPHisDataManager::QueryTableCountResult v;
            istr->readAll(v.count, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPHisDataManagerPrx::_iceI_queryStoreYc(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryStoreYcResult>>& outAsync, const StringList& iceP_listDevice, const StringList& iceP_listProperty, const ::std::string& iceP_startTime, const ::std::string& iceP_endTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryStoreYc_name);
    outAsync->invoke(iceC_ZG6000_ZGSPHisDataManager_queryStoreYc_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPHisDataManager::QueryStoreYcResult v;
            istr->readAll(v.listResult, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPHisDataManagerPrx::_iceI_queryStoreYx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryStoreYxResult>>& outAsync, const StringList& iceP_listDevice, const StringList& iceP_listProperty, const ::std::string& iceP_startTime, const ::std::string& iceP_endTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryStoreYx_name);
    outAsync->invoke(iceC_ZG6000_ZGSPHisDataManager_queryStoreYx_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPHisDataManager::QueryStoreYxResult v;
            istr->readAll(v.listResult, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPHisDataManagerPrx::_iceI_queryStoreText(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryStoreTextResult>>& outAsync, const StringList& iceP_listDevice, const StringList& iceP_listProperty, const ::std::string& iceP_startTime, const ::std::string& iceP_endTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryStoreText_name);
    outAsync->invoke(iceC_ZG6000_ZGSPHisDataManager_queryStoreText_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPHisDataManager::QueryStoreTextResult v;
            istr->readAll(v.listResult, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPHisDataManagerPrx::_iceI_queryStoreYm(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryStoreYmResult>>& outAsync, const StringList& iceP_listDevice, const StringList& iceP_listProperty, const ::std::string& iceP_startTime, const ::std::string& iceP_endTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryStoreYm_name);
    outAsync->invoke(iceC_ZG6000_ZGSPHisDataManager_queryStoreYm_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPHisDataManager::QueryStoreYmResult v;
            istr->readAll(v.listResult, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSPHisDataManagerPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSPHisDataManagerPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSPHisDataManagerPrx::ice_staticId()
{
    return ZGSPHisDataManager::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryTableData_name = "queryTableData";

const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryTableCount_name = "queryTableCount";

const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryStoreYc_name = "queryStoreYc";

const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryStoreYx_name = "queryStoreYx";

const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryStoreText_name = "queryStoreText";

const ::std::string iceC_ZG6000_ZGSPHisDataManager_queryStoreYm_name = "queryStoreYm";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSPHisDataManager* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSPHisDataManager>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSPHisDataManager;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPHisDataManager::_iceI_begin_queryTableData(const ::std::string& iceP_tableName, const ::std::string& iceP_condition, ::Ice::Int iceP_offset, ::Ice::Int iceP_limit, const ::std::string& iceP_orderField, const ::std::string& iceP_orderType, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryTableData_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPHisDataManager_queryTableData_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPHisDataManager_queryTableData_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_condition);
        ostr->write(iceP_offset);
        ostr->write(iceP_limit);
        ostr->write(iceP_orderField);
        ostr->write(iceP_orderType);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPHisDataManager_queryTableData_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPHisDataManager::end_queryTableData(::ZG6000::StringList& iceP_listTitle, ::ZG6000::ListStringList& iceP_listValues, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryTableData_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTitle);
    istr->read(iceP_listValues);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPHisDataManager::_iceI_end_queryTableData(::ZG6000::StringList& iceP_listTitle, ::ZG6000::ListStringList& iceP_listValues, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryTableData_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTitle);
    istr->read(iceP_listValues);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPHisDataManager::_iceI_begin_queryTableCount(const ::std::string& iceP_tableName, const ::std::string& iceP_condition, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryTableCount_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPHisDataManager_queryTableCount_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPHisDataManager_queryTableCount_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_tableName);
        ostr->write(iceP_condition);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPHisDataManager_queryTableCount_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPHisDataManager::end_queryTableCount(::Ice::Int& iceP_count, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryTableCount_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_count);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPHisDataManager::_iceI_end_queryTableCount(::Ice::Int& iceP_count, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryTableCount_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_count);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPHisDataManager::_iceI_begin_queryStoreYc(const ::ZG6000::StringList& iceP_listDevice, const ::ZG6000::StringList& iceP_listProperty, const ::std::string& iceP_startTime, const ::std::string& iceP_endTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryStoreYc_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPHisDataManager_queryStoreYc_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPHisDataManager_queryStoreYc_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDevice);
        ostr->write(iceP_listProperty);
        ostr->write(iceP_startTime);
        ostr->write(iceP_endTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPHisDataManager_queryStoreYc_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPHisDataManager::end_queryStoreYc(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryStoreYc_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listResult);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPHisDataManager::_iceI_end_queryStoreYc(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryStoreYc_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listResult);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPHisDataManager::_iceI_begin_queryStoreYx(const ::ZG6000::StringList& iceP_listDevice, const ::ZG6000::StringList& iceP_listProperty, const ::std::string& iceP_startTime, const ::std::string& iceP_endTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryStoreYx_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPHisDataManager_queryStoreYx_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPHisDataManager_queryStoreYx_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDevice);
        ostr->write(iceP_listProperty);
        ostr->write(iceP_startTime);
        ostr->write(iceP_endTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPHisDataManager_queryStoreYx_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPHisDataManager::end_queryStoreYx(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryStoreYx_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listResult);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPHisDataManager::_iceI_end_queryStoreYx(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryStoreYx_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listResult);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPHisDataManager::_iceI_begin_queryStoreText(const ::ZG6000::StringList& iceP_listDevice, const ::ZG6000::StringList& iceP_listProperty, const ::std::string& iceP_startTime, const ::std::string& iceP_endTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryStoreText_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPHisDataManager_queryStoreText_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPHisDataManager_queryStoreText_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDevice);
        ostr->write(iceP_listProperty);
        ostr->write(iceP_startTime);
        ostr->write(iceP_endTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPHisDataManager_queryStoreText_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPHisDataManager::end_queryStoreText(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryStoreText_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listResult);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPHisDataManager::_iceI_end_queryStoreText(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryStoreText_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listResult);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPHisDataManager::_iceI_begin_queryStoreYm(const ::ZG6000::StringList& iceP_listDevice, const ::ZG6000::StringList& iceP_listProperty, const ::std::string& iceP_startTime, const ::std::string& iceP_endTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPHisDataManager_queryStoreYm_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPHisDataManager_queryStoreYm_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPHisDataManager_queryStoreYm_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listDevice);
        ostr->write(iceP_listProperty);
        ostr->write(iceP_startTime);
        ostr->write(iceP_endTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPHisDataManager_queryStoreYm_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPHisDataManager::end_queryStoreYm(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryStoreYm_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listResult);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPHisDataManager::_iceI_end_queryStoreYm(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPHisDataManager_queryStoreYm_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listResult);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSPHisDataManager::_newInstance() const
{
    return new ZGSPHisDataManager;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSPHisDataManager::ice_staticId()
{
    return ::ZG6000::ZGSPHisDataManager::ice_staticId();
}

ZG6000::ZGSPHisDataManager::~ZGSPHisDataManager()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSPHisDataManager* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPHisDataManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPHisDataManager",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSPHisDataManager::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPHisDataManager_ids, iceC_ZG6000_ZGSPHisDataManager_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSPHisDataManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSPHisDataManager_ids[0], &iceC_ZG6000_ZGSPHisDataManager_ids[3]);
}

const ::std::string&
ZG6000::ZGSPHisDataManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPHisDataManager::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSPHisDataManager";
    return typeId;
#else
    return iceC_ZG6000_ZGSPHisDataManager_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryTableData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_condition;
    ::Ice::Int iceP_offset;
    ::Ice::Int iceP_limit;
    ::std::string iceP_orderField;
    ::std::string iceP_orderType;
    istr->read(iceP_tableName);
    istr->read(iceP_condition);
    istr->read(iceP_offset);
    istr->read(iceP_limit);
    istr->read(iceP_orderField);
    istr->read(iceP_orderType);
    inS.endReadParams();
    StringList iceP_listTitle;
    ListStringList iceP_listValues;
    ErrorInfo iceP_e;
    bool ret = this->queryTableData(iceP_tableName, iceP_condition, iceP_offset, iceP_limit, iceP_orderField, iceP_orderType, iceP_listTitle, iceP_listValues, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listTitle);
    ostr->write(iceP_listValues);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryTableCount(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_condition;
    istr->read(iceP_tableName);
    istr->read(iceP_condition);
    inS.endReadParams();
    ::Ice::Int iceP_count;
    ErrorInfo iceP_e;
    bool ret = this->queryTableCount(iceP_tableName, iceP_condition, iceP_count, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_count);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryStoreYc(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDevice;
    StringList iceP_listProperty;
    ::std::string iceP_startTime;
    ::std::string iceP_endTime;
    istr->read(iceP_listDevice);
    istr->read(iceP_listProperty);
    istr->read(iceP_startTime);
    istr->read(iceP_endTime);
    inS.endReadParams();
    ListStringMap iceP_listResult;
    ErrorInfo iceP_e;
    bool ret = this->queryStoreYc(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime, iceP_listResult, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listResult);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryStoreYx(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDevice;
    StringList iceP_listProperty;
    ::std::string iceP_startTime;
    ::std::string iceP_endTime;
    istr->read(iceP_listDevice);
    istr->read(iceP_listProperty);
    istr->read(iceP_startTime);
    istr->read(iceP_endTime);
    inS.endReadParams();
    ListStringMap iceP_listResult;
    ErrorInfo iceP_e;
    bool ret = this->queryStoreYx(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime, iceP_listResult, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listResult);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryStoreText(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDevice;
    StringList iceP_listProperty;
    ::std::string iceP_startTime;
    ::std::string iceP_endTime;
    istr->read(iceP_listDevice);
    istr->read(iceP_listProperty);
    istr->read(iceP_startTime);
    istr->read(iceP_endTime);
    inS.endReadParams();
    ListStringMap iceP_listResult;
    ErrorInfo iceP_e;
    bool ret = this->queryStoreText(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime, iceP_listResult, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listResult);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceD_queryStoreYm(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listDevice;
    StringList iceP_listProperty;
    ::std::string iceP_startTime;
    ::std::string iceP_endTime;
    istr->read(iceP_listDevice);
    istr->read(iceP_listProperty);
    istr->read(iceP_startTime);
    istr->read(iceP_endTime);
    inS.endReadParams();
    ListStringMap iceP_listResult;
    ErrorInfo iceP_e;
    bool ret = this->queryStoreYm(iceP_listDevice, iceP_listProperty, iceP_startTime, iceP_endTime, iceP_listResult, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listResult);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPHisDataManager_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "queryStoreText",
    "queryStoreYc",
    "queryStoreYm",
    "queryStoreYx",
    "queryTableCount",
    "queryTableData",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSPHisDataManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPHisDataManager_all, iceC_ZG6000_ZGSPHisDataManager_all + 21, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPHisDataManager_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getVersion(in, current);
        }
        case 4:
        {
            return _iceD_heartDebug(in, current);
        }
        case 5:
        {
            return _iceD_ice_id(in, current);
        }
        case 6:
        {
            return _iceD_ice_ids(in, current);
        }
        case 7:
        {
            return _iceD_ice_isA(in, current);
        }
        case 8:
        {
            return _iceD_ice_ping(in, current);
        }
        case 9:
        {
            return _iceD_isDebugging(in, current);
        }
        case 10:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 11:
        {
            return _iceD_queryStoreText(in, current);
        }
        case 12:
        {
            return _iceD_queryStoreYc(in, current);
        }
        case 13:
        {
            return _iceD_queryStoreYm(in, current);
        }
        case 14:
        {
            return _iceD_queryStoreYx(in, current);
        }
        case 15:
        {
            return _iceD_queryTableCount(in, current);
        }
        case 16:
        {
            return _iceD_queryTableData(in, current);
        }
        case 17:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 18:
        {
            return _iceD_startDebug(in, current);
        }
        case 19:
        {
            return _iceD_stopDebug(in, current);
        }
        case 20:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSPHisDataManager::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSPHisDataManager, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSPHisDataManager::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSPHisDataManager, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSPHisDataManagerPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSPHisDataManagerPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSPHisDataManager::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

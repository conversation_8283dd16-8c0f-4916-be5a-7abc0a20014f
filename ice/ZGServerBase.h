//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGServerBase.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGServerBase_h__
#define __ZGServerBase_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerCommon.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

#include <ZGServerBaseExport.h>

namespace ZG6000
{

class ZGServerBase;
class ZGServerBasePrx;

}

namespace ZG6000
{

class ZGServerBase : public virtual ::Ice::Object
{
public:

    using ProxyType = ZGServerBasePrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool checkState(const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_checkState(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool test(const ::Ice::Current& current);
    /// \cond INTERNAL
    bool _iceD_test(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void exitApp(const ::Ice::Current& current);
    /// \cond INTERNAL
    bool _iceD_exitApp(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual ::std::string getVersion(const ::Ice::Current& current);
    /// \cond INTERNAL
    bool _iceD_getVersion(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool isDebugging(const ::Ice::Current& current);
    /// \cond INTERNAL
    bool _iceD_isDebugging(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool startDebug(StringList lstDebugLevel, const ::Ice::Current& current);
    /// \cond INTERNAL
    bool _iceD_startDebug(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool stopDebug(const ::Ice::Current& current);
    /// \cond INTERNAL
    bool _iceD_stopDebug(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool pauseDebug(const ::Ice::Current& current);
    /// \cond INTERNAL
    bool _iceD_pauseDebug(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool resumeDebug(const ::Ice::Current& current);
    /// \cond INTERNAL
    bool _iceD_resumeDebug(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool heartDebug(const ::Ice::Current& current);
    /// \cond INTERNAL
    bool _iceD_heartDebug(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void dispatchData(::std::string tableName, ::std::string oper, ::std::string reason, ::std::string time, ListRecord listRecord, const ::Ice::Current& current);
    /// \cond INTERNAL
    bool _iceD_dispatchData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGServerBasePrx : public virtual ::Ice::Proxy<ZGServerBasePrx, ::Ice::ObjectPrx>
{
public:

    bool checkState(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makePromiseOutgoing<bool>(true, this, &ZGServerBasePrx::_iceI_checkState, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto checkStateAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<bool>>().get_future())
    {
        return _makePromiseOutgoing<bool, P>(false, this, &ZGServerBasePrx::_iceI_checkState, context);
    }

    ::std::function<void()>
    checkStateAsync(::std::function<void(bool)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<bool>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_checkState, context);
    }

    /// \cond INTERNAL
    void _iceI_checkState(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>&, const ::Ice::Context&);
    /// \endcond

    bool test(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makePromiseOutgoing<bool>(true, this, &ZGServerBasePrx::_iceI_test, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto testAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<bool>>().get_future())
    {
        return _makePromiseOutgoing<bool, P>(false, this, &ZGServerBasePrx::_iceI_test, context);
    }

    ::std::function<void()>
    testAsync(::std::function<void(bool)> response,
              ::std::function<void(::std::exception_ptr)> ex = nullptr,
              ::std::function<void(bool)> sent = nullptr,
              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<bool>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_test, context);
    }

    /// \cond INTERNAL
    void _iceI_test(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>&, const ::Ice::Context&);
    /// \endcond

    void exitApp(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGServerBasePrx::_iceI_exitApp, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto exitAppAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGServerBasePrx::_iceI_exitApp, context);
    }

    ::std::function<void()>
    exitAppAsync(::std::function<void()> response,
                 ::std::function<void(::std::exception_ptr)> ex = nullptr,
                 ::std::function<void(bool)> sent = nullptr,
                 const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_exitApp, context);
    }

    /// \cond INTERNAL
    void _iceI_exitApp(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::Ice::Context&);
    /// \endcond

    ::std::string getVersion(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makePromiseOutgoing<::std::string>(true, this, &ZGServerBasePrx::_iceI_getVersion, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto getVersionAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<::std::string>>().get_future())
    {
        return _makePromiseOutgoing<::std::string, P>(false, this, &ZGServerBasePrx::_iceI_getVersion, context);
    }

    ::std::function<void()>
    getVersionAsync(::std::function<void(::std::string)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<::std::string>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_getVersion, context);
    }

    /// \cond INTERNAL
    void _iceI_getVersion(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<::std::string>>&, const ::Ice::Context&);
    /// \endcond

    bool isDebugging(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makePromiseOutgoing<bool>(true, this, &ZGServerBasePrx::_iceI_isDebugging, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto isDebuggingAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<bool>>().get_future())
    {
        return _makePromiseOutgoing<bool, P>(false, this, &ZGServerBasePrx::_iceI_isDebugging, context);
    }

    ::std::function<void()>
    isDebuggingAsync(::std::function<void(bool)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<bool>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_isDebugging, context);
    }

    /// \cond INTERNAL
    void _iceI_isDebugging(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>&, const ::Ice::Context&);
    /// \endcond

    bool startDebug(const StringList& lstDebugLevel, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makePromiseOutgoing<bool>(true, this, &ZGServerBasePrx::_iceI_startDebug, lstDebugLevel, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto startDebugAsync(const StringList& lstDebugLevel, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<bool>>().get_future())
    {
        return _makePromiseOutgoing<bool, P>(false, this, &ZGServerBasePrx::_iceI_startDebug, lstDebugLevel, context);
    }

    ::std::function<void()>
    startDebugAsync(const StringList& lstDebugLevel,
                    ::std::function<void(bool)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<bool>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_startDebug, lstDebugLevel, context);
    }

    /// \cond INTERNAL
    void _iceI_startDebug(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool stopDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makePromiseOutgoing<bool>(true, this, &ZGServerBasePrx::_iceI_stopDebug, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto stopDebugAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<bool>>().get_future())
    {
        return _makePromiseOutgoing<bool, P>(false, this, &ZGServerBasePrx::_iceI_stopDebug, context);
    }

    ::std::function<void()>
    stopDebugAsync(::std::function<void(bool)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<bool>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_stopDebug, context);
    }

    /// \cond INTERNAL
    void _iceI_stopDebug(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>&, const ::Ice::Context&);
    /// \endcond

    bool pauseDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makePromiseOutgoing<bool>(true, this, &ZGServerBasePrx::_iceI_pauseDebug, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto pauseDebugAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<bool>>().get_future())
    {
        return _makePromiseOutgoing<bool, P>(false, this, &ZGServerBasePrx::_iceI_pauseDebug, context);
    }

    ::std::function<void()>
    pauseDebugAsync(::std::function<void(bool)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<bool>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_pauseDebug, context);
    }

    /// \cond INTERNAL
    void _iceI_pauseDebug(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>&, const ::Ice::Context&);
    /// \endcond

    bool resumeDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makePromiseOutgoing<bool>(true, this, &ZGServerBasePrx::_iceI_resumeDebug, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto resumeDebugAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<bool>>().get_future())
    {
        return _makePromiseOutgoing<bool, P>(false, this, &ZGServerBasePrx::_iceI_resumeDebug, context);
    }

    ::std::function<void()>
    resumeDebugAsync(::std::function<void(bool)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<bool>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_resumeDebug, context);
    }

    /// \cond INTERNAL
    void _iceI_resumeDebug(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>&, const ::Ice::Context&);
    /// \endcond

    bool heartDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makePromiseOutgoing<bool>(true, this, &ZGServerBasePrx::_iceI_heartDebug, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto heartDebugAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<bool>>().get_future())
    {
        return _makePromiseOutgoing<bool, P>(false, this, &ZGServerBasePrx::_iceI_heartDebug, context);
    }

    ::std::function<void()>
    heartDebugAsync(::std::function<void(bool)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<bool>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_heartDebug, context);
    }

    /// \cond INTERNAL
    void _iceI_heartDebug(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<bool>>&, const ::Ice::Context&);
    /// \endcond

    void dispatchData(const ::std::string& tableName, const ::std::string& oper, const ::std::string& reason, const ::std::string& time, const ListRecord& listRecord, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGServerBasePrx::_iceI_dispatchData, tableName, oper, reason, time, listRecord, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto dispatchDataAsync(const ::std::string& tableName, const ::std::string& oper, const ::std::string& reason, const ::std::string& time, const ListRecord& listRecord, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGServerBasePrx::_iceI_dispatchData, tableName, oper, reason, time, listRecord, context);
    }

    ::std::function<void()>
    dispatchDataAsync(const ::std::string& tableName, const ::std::string& oper, const ::std::string& reason, const ::std::string& time, const ListRecord& listRecord,
                      ::std::function<void()> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGServerBasePrx::_iceI_dispatchData, tableName, oper, reason, time, listRecord, context);
    }

    /// \cond INTERNAL
    void _iceI_dispatchData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ListRecord&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGServerBasePrx() = default;
    friend ::std::shared_ptr<ZGServerBasePrx> IceInternal::createProxy<ZGServerBasePrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGServerBasePtr = ::std::shared_ptr<ZGServerBase>;
using ZGServerBasePrxPtr = ::std::shared_ptr<ZGServerBasePrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGServerBase;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGServerBase>&);
::IceProxy::Ice::Object* upCast(ZGServerBase*);
/// \endcond

}

}

namespace ZG6000
{

class ZGServerBase;
/// \cond INTERNAL
::Ice::Object* upCast(ZGServerBase*);
/// \endcond
typedef ::IceInternal::Handle< ZGServerBase> ZGServerBasePtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGServerBase> ZGServerBasePrx;
typedef ZGServerBasePrx ZGServerBasePrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGServerBasePtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_checkState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_checkState.
 */
class Callback_ZGServerBase_checkState_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_checkState_Base> Callback_ZGServerBase_checkStatePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_test.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_test.
 */
class Callback_ZGServerBase_test_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_test_Base> Callback_ZGServerBase_testPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_exitApp.
 */
class Callback_ZGServerBase_exitApp_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_exitApp_Base> Callback_ZGServerBase_exitAppPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_getVersion.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_getVersion.
 */
class Callback_ZGServerBase_getVersion_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_getVersion_Base> Callback_ZGServerBase_getVersionPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_isDebugging.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_isDebugging.
 */
class Callback_ZGServerBase_isDebugging_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_isDebugging_Base> Callback_ZGServerBase_isDebuggingPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_startDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_startDebug.
 */
class Callback_ZGServerBase_startDebug_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_startDebug_Base> Callback_ZGServerBase_startDebugPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_stopDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_stopDebug.
 */
class Callback_ZGServerBase_stopDebug_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_stopDebug_Base> Callback_ZGServerBase_stopDebugPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_pauseDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_pauseDebug.
 */
class Callback_ZGServerBase_pauseDebug_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_pauseDebug_Base> Callback_ZGServerBase_pauseDebugPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_resumeDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_resumeDebug.
 */
class Callback_ZGServerBase_resumeDebug_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_resumeDebug_Base> Callback_ZGServerBase_resumeDebugPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_heartDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_heartDebug.
 */
class Callback_ZGServerBase_heartDebug_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_heartDebug_Base> Callback_ZGServerBase_heartDebugPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_dispatchData.
 */
class Callback_ZGServerBase_dispatchData_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGServerBase_dispatchData_Base> Callback_ZGServerBase_dispatchDataPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGServerBase : public virtual ::Ice::Proxy<ZGServerBase, ::IceProxy::Ice::Object>
{
public:

    bool checkState(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_checkState(_iceI_begin_checkState(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_checkState(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_checkState(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_checkState(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_checkState(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_checkState(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_checkState(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_checkState(const ::ZG6000::Callback_ZGServerBase_checkStatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_checkState(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_checkState(const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_checkStatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_checkState(context, cb, cookie);
    }

    bool end_checkState(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_checkState(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool test(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_test(_iceI_begin_test(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_test(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_test(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_test(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_test(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_test(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_test(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_test(const ::ZG6000::Callback_ZGServerBase_testPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_test(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_test(const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_testPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_test(context, cb, cookie);
    }

    bool end_test(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_test(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    void exitApp(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_exitApp(_iceI_begin_exitApp(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_exitApp(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_exitApp(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_exitApp(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_exitApp(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_exitApp(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_exitApp(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_exitApp(const ::ZG6000::Callback_ZGServerBase_exitAppPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_exitApp(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_exitApp(const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_exitAppPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_exitApp(context, cb, cookie);
    }

    void end_exitApp(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_exitApp(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    ::std::string getVersion(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getVersion(_iceI_begin_getVersion(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getVersion(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getVersion(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getVersion(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getVersion(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getVersion(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getVersion(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getVersion(const ::ZG6000::Callback_ZGServerBase_getVersionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getVersion(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getVersion(const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_getVersionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getVersion(context, cb, cookie);
    }

    ::std::string end_getVersion(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_getVersion(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool isDebugging(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_isDebugging(_iceI_begin_isDebugging(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_isDebugging(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_isDebugging(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_isDebugging(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isDebugging(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isDebugging(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isDebugging(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isDebugging(const ::ZG6000::Callback_ZGServerBase_isDebuggingPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isDebugging(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isDebugging(const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_isDebuggingPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isDebugging(context, cb, cookie);
    }

    bool end_isDebugging(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_isDebugging(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool startDebug(const ::ZG6000::StringList& lstDebugLevel, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_startDebug(_iceI_begin_startDebug(lstDebugLevel, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_startDebug(const ::ZG6000::StringList& lstDebugLevel, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_startDebug(lstDebugLevel, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_startDebug(const ::ZG6000::StringList& lstDebugLevel, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startDebug(lstDebugLevel, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_startDebug(const ::ZG6000::StringList& lstDebugLevel, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startDebug(lstDebugLevel, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_startDebug(const ::ZG6000::StringList& lstDebugLevel, const ::ZG6000::Callback_ZGServerBase_startDebugPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startDebug(lstDebugLevel, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_startDebug(const ::ZG6000::StringList& lstDebugLevel, const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_startDebugPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startDebug(lstDebugLevel, context, cb, cookie);
    }

    bool end_startDebug(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_startDebug(const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool stopDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_stopDebug(_iceI_begin_stopDebug(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_stopDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_stopDebug(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_stopDebug(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopDebug(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_stopDebug(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopDebug(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_stopDebug(const ::ZG6000::Callback_ZGServerBase_stopDebugPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopDebug(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_stopDebug(const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_stopDebugPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopDebug(context, cb, cookie);
    }

    bool end_stopDebug(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_stopDebug(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool pauseDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_pauseDebug(_iceI_begin_pauseDebug(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_pauseDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_pauseDebug(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_pauseDebug(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseDebug(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_pauseDebug(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseDebug(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_pauseDebug(const ::ZG6000::Callback_ZGServerBase_pauseDebugPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseDebug(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_pauseDebug(const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_pauseDebugPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseDebug(context, cb, cookie);
    }

    bool end_pauseDebug(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_pauseDebug(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool resumeDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_resumeDebug(_iceI_begin_resumeDebug(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_resumeDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_resumeDebug(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_resumeDebug(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeDebug(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resumeDebug(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeDebug(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resumeDebug(const ::ZG6000::Callback_ZGServerBase_resumeDebugPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeDebug(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resumeDebug(const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_resumeDebugPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeDebug(context, cb, cookie);
    }

    bool end_resumeDebug(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_resumeDebug(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool heartDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_heartDebug(_iceI_begin_heartDebug(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_heartDebug(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_heartDebug(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_heartDebug(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_heartDebug(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_heartDebug(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_heartDebug(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_heartDebug(const ::ZG6000::Callback_ZGServerBase_heartDebugPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_heartDebug(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_heartDebug(const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_heartDebugPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_heartDebug(context, cb, cookie);
    }

    bool end_heartDebug(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_heartDebug(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    void dispatchData(const ::std::string& tableName, const ::std::string& oper, const ::std::string& reason, const ::std::string& time, const ::ZG6000::ListRecord& listRecord, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_dispatchData(_iceI_begin_dispatchData(tableName, oper, reason, time, listRecord, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_dispatchData(const ::std::string& tableName, const ::std::string& oper, const ::std::string& reason, const ::std::string& time, const ::ZG6000::ListRecord& listRecord, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_dispatchData(tableName, oper, reason, time, listRecord, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_dispatchData(const ::std::string& tableName, const ::std::string& oper, const ::std::string& reason, const ::std::string& time, const ::ZG6000::ListRecord& listRecord, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_dispatchData(tableName, oper, reason, time, listRecord, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_dispatchData(const ::std::string& tableName, const ::std::string& oper, const ::std::string& reason, const ::std::string& time, const ::ZG6000::ListRecord& listRecord, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_dispatchData(tableName, oper, reason, time, listRecord, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_dispatchData(const ::std::string& tableName, const ::std::string& oper, const ::std::string& reason, const ::std::string& time, const ::ZG6000::ListRecord& listRecord, const ::ZG6000::Callback_ZGServerBase_dispatchDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_dispatchData(tableName, oper, reason, time, listRecord, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_dispatchData(const ::std::string& tableName, const ::std::string& oper, const ::std::string& reason, const ::std::string& time, const ::ZG6000::ListRecord& listRecord, const ::Ice::Context& context, const ::ZG6000::Callback_ZGServerBase_dispatchDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_dispatchData(tableName, oper, reason, time, listRecord, context, cb, cookie);
    }

    void end_dispatchData(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_dispatchData(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::ZG6000::ListRecord&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGServerBase : public virtual ::Ice::Object
{
public:

    typedef ZGServerBasePrx ProxyType;
    typedef ZGServerBasePtr PointerType;

    virtual ~ZGServerBase();

#ifdef ICE_CPP11_COMPILER
    ZGServerBase() = default;
    ZGServerBase(const ZGServerBase&) = default;
    ZGServerBase& operator=(const ZGServerBase&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool checkState(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_checkState(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool test(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_test(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void exitApp(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_exitApp(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual ::std::string getVersion(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getVersion(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool isDebugging(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_isDebugging(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool startDebug(const StringList& lstDebugLevel, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_startDebug(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool stopDebug(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_stopDebug(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool pauseDebug(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_pauseDebug(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool resumeDebug(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_resumeDebug(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool heartDebug(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_heartDebug(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void dispatchData(const ::std::string& tableName, const ::std::string& oper, const ::std::string& reason, const ::std::string& time, const ListRecord& listRecord, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_dispatchData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGServerBase& lhs, const ZGServerBase& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGServerBase& lhs, const ZGServerBase& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_checkState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_checkState.
 */
template<class T>
class CallbackNC_ZGServerBase_checkState : public Callback_ZGServerBase_checkState_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool);

    CallbackNC_ZGServerBase_checkState(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_checkState(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_checkState.
 */
template<class T> Callback_ZGServerBase_checkStatePtr
newCallback_ZGServerBase_checkState(const IceUtil::Handle<T>& instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_checkState<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_checkState.
 */
template<class T> Callback_ZGServerBase_checkStatePtr
newCallback_ZGServerBase_checkState(T* instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_checkState<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_checkState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_checkState.
 */
template<class T, typename CT>
class Callback_ZGServerBase_checkState : public Callback_ZGServerBase_checkState_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const CT&);

    Callback_ZGServerBase_checkState(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_checkState(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_checkState.
 */
template<class T, typename CT> Callback_ZGServerBase_checkStatePtr
newCallback_ZGServerBase_checkState(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_checkState<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_checkState.
 */
template<class T, typename CT> Callback_ZGServerBase_checkStatePtr
newCallback_ZGServerBase_checkState(T* instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_checkState<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_test.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_test.
 */
template<class T>
class CallbackNC_ZGServerBase_test : public Callback_ZGServerBase_test_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool);

    CallbackNC_ZGServerBase_test(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_test(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_test.
 */
template<class T> Callback_ZGServerBase_testPtr
newCallback_ZGServerBase_test(const IceUtil::Handle<T>& instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_test<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_test.
 */
template<class T> Callback_ZGServerBase_testPtr
newCallback_ZGServerBase_test(T* instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_test<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_test.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_test.
 */
template<class T, typename CT>
class Callback_ZGServerBase_test : public Callback_ZGServerBase_test_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const CT&);

    Callback_ZGServerBase_test(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_test(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_test.
 */
template<class T, typename CT> Callback_ZGServerBase_testPtr
newCallback_ZGServerBase_test(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_test<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_test.
 */
template<class T, typename CT> Callback_ZGServerBase_testPtr
newCallback_ZGServerBase_test(T* instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_test<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_exitApp.
 */
template<class T>
class CallbackNC_ZGServerBase_exitApp : public Callback_ZGServerBase_exitApp_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGServerBase_exitApp(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 */
template<class T> Callback_ZGServerBase_exitAppPtr
newCallback_ZGServerBase_exitApp(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_exitApp<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 */
template<class T> Callback_ZGServerBase_exitAppPtr
newCallback_ZGServerBase_exitApp(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_exitApp<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 */
template<class T> Callback_ZGServerBase_exitAppPtr
newCallback_ZGServerBase_exitApp(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_exitApp<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 */
template<class T> Callback_ZGServerBase_exitAppPtr
newCallback_ZGServerBase_exitApp(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_exitApp<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_exitApp.
 */
template<class T, typename CT>
class Callback_ZGServerBase_exitApp : public Callback_ZGServerBase_exitApp_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGServerBase_exitApp(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 */
template<class T, typename CT> Callback_ZGServerBase_exitAppPtr
newCallback_ZGServerBase_exitApp(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_exitApp<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 */
template<class T, typename CT> Callback_ZGServerBase_exitAppPtr
newCallback_ZGServerBase_exitApp(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_exitApp<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 */
template<class T, typename CT> Callback_ZGServerBase_exitAppPtr
newCallback_ZGServerBase_exitApp(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_exitApp<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_exitApp.
 */
template<class T, typename CT> Callback_ZGServerBase_exitAppPtr
newCallback_ZGServerBase_exitApp(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_exitApp<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_getVersion.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_getVersion.
 */
template<class T>
class CallbackNC_ZGServerBase_getVersion : public Callback_ZGServerBase_getVersion_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(const ::std::string&);

    CallbackNC_ZGServerBase_getVersion(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        ::std::string ret;
        try
        {
            ret = proxy->end_getVersion(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_getVersion.
 */
template<class T> Callback_ZGServerBase_getVersionPtr
newCallback_ZGServerBase_getVersion(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::std::string&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_getVersion<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_getVersion.
 */
template<class T> Callback_ZGServerBase_getVersionPtr
newCallback_ZGServerBase_getVersion(T* instance, void (T::*cb)(const ::std::string&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_getVersion<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_getVersion.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_getVersion.
 */
template<class T, typename CT>
class Callback_ZGServerBase_getVersion : public Callback_ZGServerBase_getVersion_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const ::std::string&, const CT&);

    Callback_ZGServerBase_getVersion(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        ::std::string ret;
        try
        {
            ret = proxy->end_getVersion(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_getVersion.
 */
template<class T, typename CT> Callback_ZGServerBase_getVersionPtr
newCallback_ZGServerBase_getVersion(const IceUtil::Handle<T>& instance, void (T::*cb)(const ::std::string&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_getVersion<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_getVersion.
 */
template<class T, typename CT> Callback_ZGServerBase_getVersionPtr
newCallback_ZGServerBase_getVersion(T* instance, void (T::*cb)(const ::std::string&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_getVersion<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_isDebugging.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_isDebugging.
 */
template<class T>
class CallbackNC_ZGServerBase_isDebugging : public Callback_ZGServerBase_isDebugging_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool);

    CallbackNC_ZGServerBase_isDebugging(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_isDebugging(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_isDebugging.
 */
template<class T> Callback_ZGServerBase_isDebuggingPtr
newCallback_ZGServerBase_isDebugging(const IceUtil::Handle<T>& instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_isDebugging<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_isDebugging.
 */
template<class T> Callback_ZGServerBase_isDebuggingPtr
newCallback_ZGServerBase_isDebugging(T* instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_isDebugging<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_isDebugging.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_isDebugging.
 */
template<class T, typename CT>
class Callback_ZGServerBase_isDebugging : public Callback_ZGServerBase_isDebugging_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const CT&);

    Callback_ZGServerBase_isDebugging(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_isDebugging(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_isDebugging.
 */
template<class T, typename CT> Callback_ZGServerBase_isDebuggingPtr
newCallback_ZGServerBase_isDebugging(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_isDebugging<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_isDebugging.
 */
template<class T, typename CT> Callback_ZGServerBase_isDebuggingPtr
newCallback_ZGServerBase_isDebugging(T* instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_isDebugging<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_startDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_startDebug.
 */
template<class T>
class CallbackNC_ZGServerBase_startDebug : public Callback_ZGServerBase_startDebug_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool);

    CallbackNC_ZGServerBase_startDebug(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_startDebug(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_startDebug.
 */
template<class T> Callback_ZGServerBase_startDebugPtr
newCallback_ZGServerBase_startDebug(const IceUtil::Handle<T>& instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_startDebug<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_startDebug.
 */
template<class T> Callback_ZGServerBase_startDebugPtr
newCallback_ZGServerBase_startDebug(T* instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_startDebug<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_startDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_startDebug.
 */
template<class T, typename CT>
class Callback_ZGServerBase_startDebug : public Callback_ZGServerBase_startDebug_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const CT&);

    Callback_ZGServerBase_startDebug(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_startDebug(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_startDebug.
 */
template<class T, typename CT> Callback_ZGServerBase_startDebugPtr
newCallback_ZGServerBase_startDebug(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_startDebug<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_startDebug.
 */
template<class T, typename CT> Callback_ZGServerBase_startDebugPtr
newCallback_ZGServerBase_startDebug(T* instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_startDebug<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_stopDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_stopDebug.
 */
template<class T>
class CallbackNC_ZGServerBase_stopDebug : public Callback_ZGServerBase_stopDebug_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool);

    CallbackNC_ZGServerBase_stopDebug(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_stopDebug(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_stopDebug.
 */
template<class T> Callback_ZGServerBase_stopDebugPtr
newCallback_ZGServerBase_stopDebug(const IceUtil::Handle<T>& instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_stopDebug<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_stopDebug.
 */
template<class T> Callback_ZGServerBase_stopDebugPtr
newCallback_ZGServerBase_stopDebug(T* instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_stopDebug<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_stopDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_stopDebug.
 */
template<class T, typename CT>
class Callback_ZGServerBase_stopDebug : public Callback_ZGServerBase_stopDebug_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const CT&);

    Callback_ZGServerBase_stopDebug(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_stopDebug(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_stopDebug.
 */
template<class T, typename CT> Callback_ZGServerBase_stopDebugPtr
newCallback_ZGServerBase_stopDebug(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_stopDebug<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_stopDebug.
 */
template<class T, typename CT> Callback_ZGServerBase_stopDebugPtr
newCallback_ZGServerBase_stopDebug(T* instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_stopDebug<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_pauseDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_pauseDebug.
 */
template<class T>
class CallbackNC_ZGServerBase_pauseDebug : public Callback_ZGServerBase_pauseDebug_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool);

    CallbackNC_ZGServerBase_pauseDebug(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_pauseDebug(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_pauseDebug.
 */
template<class T> Callback_ZGServerBase_pauseDebugPtr
newCallback_ZGServerBase_pauseDebug(const IceUtil::Handle<T>& instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_pauseDebug<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_pauseDebug.
 */
template<class T> Callback_ZGServerBase_pauseDebugPtr
newCallback_ZGServerBase_pauseDebug(T* instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_pauseDebug<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_pauseDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_pauseDebug.
 */
template<class T, typename CT>
class Callback_ZGServerBase_pauseDebug : public Callback_ZGServerBase_pauseDebug_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const CT&);

    Callback_ZGServerBase_pauseDebug(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_pauseDebug(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_pauseDebug.
 */
template<class T, typename CT> Callback_ZGServerBase_pauseDebugPtr
newCallback_ZGServerBase_pauseDebug(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_pauseDebug<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_pauseDebug.
 */
template<class T, typename CT> Callback_ZGServerBase_pauseDebugPtr
newCallback_ZGServerBase_pauseDebug(T* instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_pauseDebug<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_resumeDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_resumeDebug.
 */
template<class T>
class CallbackNC_ZGServerBase_resumeDebug : public Callback_ZGServerBase_resumeDebug_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool);

    CallbackNC_ZGServerBase_resumeDebug(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_resumeDebug(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_resumeDebug.
 */
template<class T> Callback_ZGServerBase_resumeDebugPtr
newCallback_ZGServerBase_resumeDebug(const IceUtil::Handle<T>& instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_resumeDebug<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_resumeDebug.
 */
template<class T> Callback_ZGServerBase_resumeDebugPtr
newCallback_ZGServerBase_resumeDebug(T* instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_resumeDebug<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_resumeDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_resumeDebug.
 */
template<class T, typename CT>
class Callback_ZGServerBase_resumeDebug : public Callback_ZGServerBase_resumeDebug_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const CT&);

    Callback_ZGServerBase_resumeDebug(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_resumeDebug(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_resumeDebug.
 */
template<class T, typename CT> Callback_ZGServerBase_resumeDebugPtr
newCallback_ZGServerBase_resumeDebug(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_resumeDebug<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_resumeDebug.
 */
template<class T, typename CT> Callback_ZGServerBase_resumeDebugPtr
newCallback_ZGServerBase_resumeDebug(T* instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_resumeDebug<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_heartDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_heartDebug.
 */
template<class T>
class CallbackNC_ZGServerBase_heartDebug : public Callback_ZGServerBase_heartDebug_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool);

    CallbackNC_ZGServerBase_heartDebug(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_heartDebug(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_heartDebug.
 */
template<class T> Callback_ZGServerBase_heartDebugPtr
newCallback_ZGServerBase_heartDebug(const IceUtil::Handle<T>& instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_heartDebug<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_heartDebug.
 */
template<class T> Callback_ZGServerBase_heartDebugPtr
newCallback_ZGServerBase_heartDebug(T* instance, void (T::*cb)(bool), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_heartDebug<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_heartDebug.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_heartDebug.
 */
template<class T, typename CT>
class Callback_ZGServerBase_heartDebug : public Callback_ZGServerBase_heartDebug_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const CT&);

    Callback_ZGServerBase_heartDebug(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGServerBasePrx proxy = ZGServerBasePrx::uncheckedCast(result->getProxy());
        bool ret;
        try
        {
            ret = proxy->end_heartDebug(result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_heartDebug.
 */
template<class T, typename CT> Callback_ZGServerBase_heartDebugPtr
newCallback_ZGServerBase_heartDebug(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_heartDebug<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_heartDebug.
 */
template<class T, typename CT> Callback_ZGServerBase_heartDebugPtr
newCallback_ZGServerBase_heartDebug(T* instance, void (T::*cb)(bool, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_heartDebug<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_dispatchData.
 */
template<class T>
class CallbackNC_ZGServerBase_dispatchData : public Callback_ZGServerBase_dispatchData_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGServerBase_dispatchData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 */
template<class T> Callback_ZGServerBase_dispatchDataPtr
newCallback_ZGServerBase_dispatchData(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_dispatchData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 */
template<class T> Callback_ZGServerBase_dispatchDataPtr
newCallback_ZGServerBase_dispatchData(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_dispatchData<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 */
template<class T> Callback_ZGServerBase_dispatchDataPtr
newCallback_ZGServerBase_dispatchData(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_dispatchData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 */
template<class T> Callback_ZGServerBase_dispatchDataPtr
newCallback_ZGServerBase_dispatchData(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGServerBase_dispatchData<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGServerBase_dispatchData.
 */
template<class T, typename CT>
class Callback_ZGServerBase_dispatchData : public Callback_ZGServerBase_dispatchData_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGServerBase_dispatchData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 */
template<class T, typename CT> Callback_ZGServerBase_dispatchDataPtr
newCallback_ZGServerBase_dispatchData(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_dispatchData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 */
template<class T, typename CT> Callback_ZGServerBase_dispatchDataPtr
newCallback_ZGServerBase_dispatchData(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_dispatchData<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 */
template<class T, typename CT> Callback_ZGServerBase_dispatchDataPtr
newCallback_ZGServerBase_dispatchData(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_dispatchData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGServerBase::begin_dispatchData.
 */
template<class T, typename CT> Callback_ZGServerBase_dispatchDataPtr
newCallback_ZGServerBase_dispatchData(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGServerBase_dispatchData<T, CT>(instance, 0, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPSecureManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSPSecureManager.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPSecureManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPSecureManager",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSPSecureManager_ops[] =
{
    "aesDecrypt",
    "aesEncrypt",
    "checkState",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "rsaDecrypt",
    "rsaEncrypt",
    "rsaOpen",
    "rsaSeal",
    "rsaSign",
    "rsaVerify",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGSPSecureManager_aesEncrypt_name = "aesEncrypt";
const ::std::string iceC_ZG6000_ZGSPSecureManager_aesDecrypt_name = "aesDecrypt";
const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaEncrypt_name = "rsaEncrypt";
const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaDecrypt_name = "rsaDecrypt";
const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaSign_name = "rsaSign";
const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaVerify_name = "rsaVerify";
const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaSeal_name = "rsaSeal";
const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaOpen_name = "rsaOpen";

}

bool
ZG6000::ZGSPSecureManager::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPSecureManager_ids, iceC_ZG6000_ZGSPSecureManager_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSPSecureManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSPSecureManager_ids[0], &iceC_ZG6000_ZGSPSecureManager_ids[3]);
}

::std::string
ZG6000::ZGSPSecureManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPSecureManager::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSPSecureManager";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_aesEncrypt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->readAll(iceP_in);
    inS.endReadParams();
    ::std::string iceP_output;
    ErrorInfo iceP_e;
    bool ret = this->aesEncrypt(::std::move(iceP_in), iceP_output, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_output, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_aesDecrypt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->readAll(iceP_in);
    inS.endReadParams();
    ::std::string iceP_output;
    ErrorInfo iceP_e;
    bool ret = this->aesDecrypt(::std::move(iceP_in), iceP_output, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_output, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaEncrypt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->readAll(iceP_in);
    inS.endReadParams();
    ::std::string iceP_output;
    ErrorInfo iceP_e;
    bool ret = this->rsaEncrypt(::std::move(iceP_in), iceP_output, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_output, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaDecrypt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->readAll(iceP_in);
    inS.endReadParams();
    ::std::string iceP_output;
    ErrorInfo iceP_e;
    bool ret = this->rsaDecrypt(::std::move(iceP_in), iceP_output, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_output, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaSign(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->readAll(iceP_in);
    inS.endReadParams();
    ::std::string iceP_sign;
    ErrorInfo iceP_e;
    bool ret = this->rsaSign(::std::move(iceP_in), iceP_sign, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_sign, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaVerify(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_in;
    ::std::string iceP_sign;
    istr->readAll(iceP_in, iceP_sign);
    inS.endReadParams();
    bool iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->rsaVerify(::std::move(iceP_in), ::std::move(iceP_sign), iceP_result, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_result, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaSeal(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->readAll(iceP_in);
    inS.endReadParams();
    ::std::string iceP_output;
    ::std::string iceP_envelope;
    ErrorInfo iceP_e;
    bool ret = this->rsaSeal(::std::move(iceP_in), iceP_output, iceP_envelope, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_output, iceP_envelope, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaOpen(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_in;
    ::std::string iceP_envelope;
    istr->readAll(iceP_in, iceP_envelope);
    inS.endReadParams();
    ::std::string iceP_output;
    ErrorInfo iceP_e;
    bool ret = this->rsaOpen(::std::move(iceP_in), ::std::move(iceP_envelope), iceP_output, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_output, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPSecureManager_ops, iceC_ZG6000_ZGSPSecureManager_ops + 23, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPSecureManager_ops)
    {
        case 0:
        {
            return _iceD_aesDecrypt(in, current);
        }
        case 1:
        {
            return _iceD_aesEncrypt(in, current);
        }
        case 2:
        {
            return _iceD_checkState(in, current);
        }
        case 3:
        {
            return _iceD_dispatchData(in, current);
        }
        case 4:
        {
            return _iceD_exitApp(in, current);
        }
        case 5:
        {
            return _iceD_getVersion(in, current);
        }
        case 6:
        {
            return _iceD_heartDebug(in, current);
        }
        case 7:
        {
            return _iceD_ice_id(in, current);
        }
        case 8:
        {
            return _iceD_ice_ids(in, current);
        }
        case 9:
        {
            return _iceD_ice_isA(in, current);
        }
        case 10:
        {
            return _iceD_ice_ping(in, current);
        }
        case 11:
        {
            return _iceD_isDebugging(in, current);
        }
        case 12:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 13:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 14:
        {
            return _iceD_rsaDecrypt(in, current);
        }
        case 15:
        {
            return _iceD_rsaEncrypt(in, current);
        }
        case 16:
        {
            return _iceD_rsaOpen(in, current);
        }
        case 17:
        {
            return _iceD_rsaSeal(in, current);
        }
        case 18:
        {
            return _iceD_rsaSign(in, current);
        }
        case 19:
        {
            return _iceD_rsaVerify(in, current);
        }
        case 20:
        {
            return _iceD_startDebug(in, current);
        }
        case 21:
        {
            return _iceD_stopDebug(in, current);
        }
        case 22:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPSecureManagerPrx::_iceI_aesEncrypt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::AesEncryptResult>>& outAsync, const ::std::string& iceP_in, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_aesEncrypt_name);
    outAsync->invoke(iceC_ZG6000_ZGSPSecureManager_aesEncrypt_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_in);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPSecureManager::AesEncryptResult v;
            istr->readAll(v.output, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPSecureManagerPrx::_iceI_aesDecrypt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::AesDecryptResult>>& outAsync, const ::std::string& iceP_in, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_aesDecrypt_name);
    outAsync->invoke(iceC_ZG6000_ZGSPSecureManager_aesDecrypt_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_in);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPSecureManager::AesDecryptResult v;
            istr->readAll(v.output, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPSecureManagerPrx::_iceI_rsaEncrypt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaEncryptResult>>& outAsync, const ::std::string& iceP_in, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaEncrypt_name);
    outAsync->invoke(iceC_ZG6000_ZGSPSecureManager_rsaEncrypt_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_in);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPSecureManager::RsaEncryptResult v;
            istr->readAll(v.output, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPSecureManagerPrx::_iceI_rsaDecrypt(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaDecryptResult>>& outAsync, const ::std::string& iceP_in, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaDecrypt_name);
    outAsync->invoke(iceC_ZG6000_ZGSPSecureManager_rsaDecrypt_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_in);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPSecureManager::RsaDecryptResult v;
            istr->readAll(v.output, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPSecureManagerPrx::_iceI_rsaSign(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaSignResult>>& outAsync, const ::std::string& iceP_in, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaSign_name);
    outAsync->invoke(iceC_ZG6000_ZGSPSecureManager_rsaSign_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_in);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPSecureManager::RsaSignResult v;
            istr->readAll(v.sign, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPSecureManagerPrx::_iceI_rsaVerify(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaVerifyResult>>& outAsync, const ::std::string& iceP_in, const ::std::string& iceP_sign, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaVerify_name);
    outAsync->invoke(iceC_ZG6000_ZGSPSecureManager_rsaVerify_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_in, iceP_sign);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPSecureManager::RsaVerifyResult v;
            istr->readAll(v.result, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPSecureManagerPrx::_iceI_rsaSeal(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaSealResult>>& outAsync, const ::std::string& iceP_in, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaSeal_name);
    outAsync->invoke(iceC_ZG6000_ZGSPSecureManager_rsaSeal_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_in);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPSecureManager::RsaSealResult v;
            istr->readAll(v.output, v.envelope, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPSecureManagerPrx::_iceI_rsaOpen(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPSecureManager::RsaOpenResult>>& outAsync, const ::std::string& iceP_in, const ::std::string& iceP_envelope, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaOpen_name);
    outAsync->invoke(iceC_ZG6000_ZGSPSecureManager_rsaOpen_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_in, iceP_envelope);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPSecureManager::RsaOpenResult v;
            istr->readAll(v.output, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSPSecureManagerPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSPSecureManagerPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSPSecureManagerPrx::ice_staticId()
{
    return ZGSPSecureManager::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPSecureManager_aesEncrypt_name = "aesEncrypt";

const ::std::string iceC_ZG6000_ZGSPSecureManager_aesDecrypt_name = "aesDecrypt";

const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaEncrypt_name = "rsaEncrypt";

const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaDecrypt_name = "rsaDecrypt";

const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaSign_name = "rsaSign";

const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaVerify_name = "rsaVerify";

const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaSeal_name = "rsaSeal";

const ::std::string iceC_ZG6000_ZGSPSecureManager_rsaOpen_name = "rsaOpen";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSPSecureManager* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSPSecureManager>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSPSecureManager;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPSecureManager::_iceI_begin_aesEncrypt(const ::std::string& iceP_in, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_aesEncrypt_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPSecureManager_aesEncrypt_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPSecureManager_aesEncrypt_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_in);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPSecureManager_aesEncrypt_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPSecureManager::end_aesEncrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_aesEncrypt_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPSecureManager::_iceI_end_aesEncrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_aesEncrypt_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPSecureManager::_iceI_begin_aesDecrypt(const ::std::string& iceP_in, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_aesDecrypt_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPSecureManager_aesDecrypt_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPSecureManager_aesDecrypt_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_in);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPSecureManager_aesDecrypt_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPSecureManager::end_aesDecrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_aesDecrypt_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPSecureManager::_iceI_end_aesDecrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_aesDecrypt_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPSecureManager::_iceI_begin_rsaEncrypt(const ::std::string& iceP_in, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaEncrypt_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPSecureManager_rsaEncrypt_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPSecureManager_rsaEncrypt_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_in);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPSecureManager_rsaEncrypt_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPSecureManager::end_rsaEncrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaEncrypt_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPSecureManager::_iceI_end_rsaEncrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaEncrypt_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPSecureManager::_iceI_begin_rsaDecrypt(const ::std::string& iceP_in, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaDecrypt_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPSecureManager_rsaDecrypt_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPSecureManager_rsaDecrypt_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_in);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPSecureManager_rsaDecrypt_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPSecureManager::end_rsaDecrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaDecrypt_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPSecureManager::_iceI_end_rsaDecrypt(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaDecrypt_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPSecureManager::_iceI_begin_rsaSign(const ::std::string& iceP_in, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaSign_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPSecureManager_rsaSign_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPSecureManager_rsaSign_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_in);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPSecureManager_rsaSign_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPSecureManager::end_rsaSign(::std::string& iceP_sign, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaSign_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_sign);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPSecureManager::_iceI_end_rsaSign(::std::string& iceP_sign, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaSign_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_sign);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPSecureManager::_iceI_begin_rsaVerify(const ::std::string& iceP_in, const ::std::string& iceP_sign, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaVerify_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPSecureManager_rsaVerify_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPSecureManager_rsaVerify_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_in);
        ostr->write(iceP_sign);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPSecureManager_rsaVerify_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPSecureManager::end_rsaVerify(bool& iceP_result, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaVerify_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPSecureManager::_iceI_end_rsaVerify(bool& iceP_result, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaVerify_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_result);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPSecureManager::_iceI_begin_rsaSeal(const ::std::string& iceP_in, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaSeal_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPSecureManager_rsaSeal_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPSecureManager_rsaSeal_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_in);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPSecureManager_rsaSeal_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPSecureManager::end_rsaSeal(::std::string& iceP_output, ::std::string& iceP_envelope, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaSeal_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_envelope);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPSecureManager::_iceI_end_rsaSeal(::std::string& iceP_output, ::std::string& iceP_envelope, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaSeal_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_envelope);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPSecureManager::_iceI_begin_rsaOpen(const ::std::string& iceP_in, const ::std::string& iceP_envelope, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPSecureManager_rsaOpen_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPSecureManager_rsaOpen_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPSecureManager_rsaOpen_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_in);
        ostr->write(iceP_envelope);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPSecureManager_rsaOpen_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPSecureManager::end_rsaOpen(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaOpen_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPSecureManager::_iceI_end_rsaOpen(::std::string& iceP_output, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPSecureManager_rsaOpen_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_output);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSPSecureManager::_newInstance() const
{
    return new ZGSPSecureManager;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSPSecureManager::ice_staticId()
{
    return ::ZG6000::ZGSPSecureManager::ice_staticId();
}

ZG6000::ZGSPSecureManager::~ZGSPSecureManager()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSPSecureManager* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPSecureManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPSecureManager",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSPSecureManager::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPSecureManager_ids, iceC_ZG6000_ZGSPSecureManager_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSPSecureManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSPSecureManager_ids[0], &iceC_ZG6000_ZGSPSecureManager_ids[3]);
}

const ::std::string&
ZG6000::ZGSPSecureManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPSecureManager::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSPSecureManager";
    return typeId;
#else
    return iceC_ZG6000_ZGSPSecureManager_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_aesEncrypt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->read(iceP_in);
    inS.endReadParams();
    ::std::string iceP_output;
    ErrorInfo iceP_e;
    bool ret = this->aesEncrypt(iceP_in, iceP_output, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_output);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_aesDecrypt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->read(iceP_in);
    inS.endReadParams();
    ::std::string iceP_output;
    ErrorInfo iceP_e;
    bool ret = this->aesDecrypt(iceP_in, iceP_output, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_output);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaEncrypt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->read(iceP_in);
    inS.endReadParams();
    ::std::string iceP_output;
    ErrorInfo iceP_e;
    bool ret = this->rsaEncrypt(iceP_in, iceP_output, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_output);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaDecrypt(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->read(iceP_in);
    inS.endReadParams();
    ::std::string iceP_output;
    ErrorInfo iceP_e;
    bool ret = this->rsaDecrypt(iceP_in, iceP_output, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_output);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaSign(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->read(iceP_in);
    inS.endReadParams();
    ::std::string iceP_sign;
    ErrorInfo iceP_e;
    bool ret = this->rsaSign(iceP_in, iceP_sign, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_sign);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaVerify(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_in;
    ::std::string iceP_sign;
    istr->read(iceP_in);
    istr->read(iceP_sign);
    inS.endReadParams();
    bool iceP_result;
    ErrorInfo iceP_e;
    bool ret = this->rsaVerify(iceP_in, iceP_sign, iceP_result, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_result);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaSeal(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_in;
    istr->read(iceP_in);
    inS.endReadParams();
    ::std::string iceP_output;
    ::std::string iceP_envelope;
    ErrorInfo iceP_e;
    bool ret = this->rsaSeal(iceP_in, iceP_output, iceP_envelope, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_output);
    ostr->write(iceP_envelope);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceD_rsaOpen(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_in;
    ::std::string iceP_envelope;
    istr->read(iceP_in);
    istr->read(iceP_envelope);
    inS.endReadParams();
    ::std::string iceP_output;
    ErrorInfo iceP_e;
    bool ret = this->rsaOpen(iceP_in, iceP_envelope, iceP_output, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_output);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPSecureManager_all[] =
{
    "aesDecrypt",
    "aesEncrypt",
    "checkState",
    "dispatchData",
    "exitApp",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "rsaDecrypt",
    "rsaEncrypt",
    "rsaOpen",
    "rsaSeal",
    "rsaSign",
    "rsaVerify",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSPSecureManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPSecureManager_all, iceC_ZG6000_ZGSPSecureManager_all + 23, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPSecureManager_all)
    {
        case 0:
        {
            return _iceD_aesDecrypt(in, current);
        }
        case 1:
        {
            return _iceD_aesEncrypt(in, current);
        }
        case 2:
        {
            return _iceD_checkState(in, current);
        }
        case 3:
        {
            return _iceD_dispatchData(in, current);
        }
        case 4:
        {
            return _iceD_exitApp(in, current);
        }
        case 5:
        {
            return _iceD_getVersion(in, current);
        }
        case 6:
        {
            return _iceD_heartDebug(in, current);
        }
        case 7:
        {
            return _iceD_ice_id(in, current);
        }
        case 8:
        {
            return _iceD_ice_ids(in, current);
        }
        case 9:
        {
            return _iceD_ice_isA(in, current);
        }
        case 10:
        {
            return _iceD_ice_ping(in, current);
        }
        case 11:
        {
            return _iceD_isDebugging(in, current);
        }
        case 12:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 13:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 14:
        {
            return _iceD_rsaDecrypt(in, current);
        }
        case 15:
        {
            return _iceD_rsaEncrypt(in, current);
        }
        case 16:
        {
            return _iceD_rsaOpen(in, current);
        }
        case 17:
        {
            return _iceD_rsaSeal(in, current);
        }
        case 18:
        {
            return _iceD_rsaSign(in, current);
        }
        case 19:
        {
            return _iceD_rsaVerify(in, current);
        }
        case 20:
        {
            return _iceD_startDebug(in, current);
        }
        case 21:
        {
            return _iceD_stopDebug(in, current);
        }
        case 22:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSPSecureManager::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSPSecureManager, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSPSecureManager::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSPSecureManager, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSPSecureManagerPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSPSecureManagerPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSPSecureManager::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

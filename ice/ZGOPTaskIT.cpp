//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPTaskIT.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGOPTaskIT.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPTaskIT_ids[4] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPTaskBase",
    "::ZG6000::ZGOPTaskIT",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGOPTaskIT_ops[] =
{
    "abolishTask",
    "checkState",
    "confirmTask",
    "convertTask",
    "createCustomTask",
    "createSpecialTask",
    "createTypicalTask",
    "createUAVTask",
    "deleteTask",
    "dispatchData",
    "downloadTask",
    "editTask",
    "exitApp",
    "getItemActions",
    "getTaskItems",
    "getTaskList",
    "getTaskTypeObjects",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "pauseTask",
    "resumeDebug",
    "resumeTask",
    "retryTask",
    "skipItem",
    "startDebug",
    "startTask",
    "stopDebug",
    "test",
    "updateAction",
    "updateItem",
    "updateTask"
};
const ::std::string iceC_ZG6000_ZGOPTaskIT_getTaskItems_name = "getTaskItems";
const ::std::string iceC_ZG6000_ZGOPTaskIT_getItemActions_name = "getItemActions";
const ::std::string iceC_ZG6000_ZGOPTaskIT_createTypicalTask_name = "createTypicalTask";
const ::std::string iceC_ZG6000_ZGOPTaskIT_getTaskTypeObjects_name = "getTaskTypeObjects";
const ::std::string iceC_ZG6000_ZGOPTaskIT_createSpecialTask_name = "createSpecialTask";
const ::std::string iceC_ZG6000_ZGOPTaskIT_createCustomTask_name = "createCustomTask";
const ::std::string iceC_ZG6000_ZGOPTaskIT_createUAVTask_name = "createUAVTask";
const ::std::string iceC_ZG6000_ZGOPTaskIT_editTask_name = "editTask";
const ::std::string iceC_ZG6000_ZGOPTaskIT_convertTask_name = "convertTask";
const ::std::string iceC_ZG6000_ZGOPTaskIT_skipItem_name = "skipItem";
const ::std::string iceC_ZG6000_ZGOPTaskIT_updateTask_name = "updateTask";
const ::std::string iceC_ZG6000_ZGOPTaskIT_updateItem_name = "updateItem";
const ::std::string iceC_ZG6000_ZGOPTaskIT_updateAction_name = "updateAction";
const ::std::string iceC_ZG6000_ZGOPTaskIT_downloadTask_name = "downloadTask";

}

bool
ZG6000::ZGOPTaskIT::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPTaskIT_ids, iceC_ZG6000_ZGOPTaskIT_ids + 4, s);
}

::std::vector<::std::string>
ZG6000::ZGOPTaskIT::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGOPTaskIT_ids[0], &iceC_ZG6000_ZGOPTaskIT_ids[4]);
}

::std::string
ZG6000::ZGOPTaskIT::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPTaskIT::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGOPTaskIT";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_getTaskItems(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    StringMap iceP_task;
    ListStringMap iceP_items;
    ErrorInfo iceP_e;
    bool ret = this->getTaskItems(::std::move(iceP_taskID), iceP_task, iceP_items, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_task, iceP_items, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_getItemActions(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_itemID;
    istr->readAll(iceP_itemID);
    inS.endReadParams();
    StringMap iceP_item;
    ListStringMap iceP_actions;
    ErrorInfo iceP_e;
    bool ret = this->getItemActions(::std::move(iceP_itemID), iceP_item, iceP_actions, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_item, iceP_actions, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_createTypicalTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_params;
    istr->readAll(iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->createTypicalTask(::std::move(iceP_params), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_getTaskTypeObjects(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_params;
    istr->readAll(iceP_params);
    inS.endReadParams();
    ListStringMap iceP_listObject;
    ErrorInfo iceP_e;
    bool ret = this->getTaskTypeObjects(::std::move(iceP_params), iceP_listObject, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listObject, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_createSpecialTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskTypeID;
    StringList iceP_listObjectID;
    StringMap iceP_params;
    istr->readAll(iceP_taskTypeID, iceP_listObjectID, iceP_params);
    inS.endReadParams();
    ::std::string iceP_taskID;
    ErrorInfo iceP_e;
    bool ret = this->createSpecialTask(::std::move(iceP_taskTypeID), ::std::move(iceP_listObjectID), ::std::move(iceP_params), iceP_taskID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_taskID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_createCustomTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    StringMap iceP_params;
    istr->readAll(iceP_listItem, iceP_params);
    inS.endReadParams();
    ::std::string iceP_taskID;
    ErrorInfo iceP_e;
    bool ret = this->createCustomTask(::std::move(iceP_listItem), ::std::move(iceP_params), iceP_taskID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_taskID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_createUAVTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_task;
    ListStringMap iceP_listPreset;
    ListStringMap iceP_listItem;
    ListStringMap iceP_listAction;
    istr->readAll(iceP_task, iceP_listPreset, iceP_listItem, iceP_listAction);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->createUAVTask(::std::move(iceP_task), ::std::move(iceP_listPreset), ::std::move(iceP_listItem), ::std::move(iceP_listAction), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_editTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_task;
    ListStringMap iceP_items;
    istr->readAll(iceP_taskID, iceP_task, iceP_items);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->editTask(::std::move(iceP_taskID), ::std::move(iceP_task), ::std::move(iceP_items), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_convertTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_params;
    istr->readAll(iceP_taskID, iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->convertTask(::std::move(iceP_taskID), ::std::move(iceP_params), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_skipItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_itemID;
    StringMap iceP_params;
    istr->readAll(iceP_itemID, iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->skipItem(::std::move(iceP_itemID), ::std::move(iceP_params), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_updateTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listTask;
    istr->readAll(iceP_listTask);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateTask(::std::move(iceP_listTask), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_updateItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->readAll(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateItem(::std::move(iceP_listItem), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_updateAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listAction;
    istr->readAll(iceP_listAction);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateAction(::std::move(iceP_listAction), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_downloadTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listTaskID;
    istr->readAll(iceP_listTaskID);
    inS.endReadParams();
    ListStringMap iceP_listTask;
    ListStringMap iceP_listItem;
    ListStringMap iceP_listAction;
    ErrorInfo iceP_e;
    bool ret = this->downloadTask(::std::move(iceP_listTaskID), iceP_listTask, iceP_listItem, iceP_listAction, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listTask, iceP_listItem, iceP_listAction, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPTaskIT_ops, iceC_ZG6000_ZGOPTaskIT_ops + 37, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPTaskIT_ops)
    {
        case 0:
        {
            return _iceD_abolishTask(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_confirmTask(in, current);
        }
        case 3:
        {
            return _iceD_convertTask(in, current);
        }
        case 4:
        {
            return _iceD_createCustomTask(in, current);
        }
        case 5:
        {
            return _iceD_createSpecialTask(in, current);
        }
        case 6:
        {
            return _iceD_createTypicalTask(in, current);
        }
        case 7:
        {
            return _iceD_createUAVTask(in, current);
        }
        case 8:
        {
            return _iceD_deleteTask(in, current);
        }
        case 9:
        {
            return _iceD_dispatchData(in, current);
        }
        case 10:
        {
            return _iceD_downloadTask(in, current);
        }
        case 11:
        {
            return _iceD_editTask(in, current);
        }
        case 12:
        {
            return _iceD_exitApp(in, current);
        }
        case 13:
        {
            return _iceD_getItemActions(in, current);
        }
        case 14:
        {
            return _iceD_getTaskItems(in, current);
        }
        case 15:
        {
            return _iceD_getTaskList(in, current);
        }
        case 16:
        {
            return _iceD_getTaskTypeObjects(in, current);
        }
        case 17:
        {
            return _iceD_getVersion(in, current);
        }
        case 18:
        {
            return _iceD_heartDebug(in, current);
        }
        case 19:
        {
            return _iceD_ice_id(in, current);
        }
        case 20:
        {
            return _iceD_ice_ids(in, current);
        }
        case 21:
        {
            return _iceD_ice_isA(in, current);
        }
        case 22:
        {
            return _iceD_ice_ping(in, current);
        }
        case 23:
        {
            return _iceD_isDebugging(in, current);
        }
        case 24:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 25:
        {
            return _iceD_pauseTask(in, current);
        }
        case 26:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 27:
        {
            return _iceD_resumeTask(in, current);
        }
        case 28:
        {
            return _iceD_retryTask(in, current);
        }
        case 29:
        {
            return _iceD_skipItem(in, current);
        }
        case 30:
        {
            return _iceD_startDebug(in, current);
        }
        case 31:
        {
            return _iceD_startTask(in, current);
        }
        case 32:
        {
            return _iceD_stopDebug(in, current);
        }
        case 33:
        {
            return _iceD_test(in, current);
        }
        case 34:
        {
            return _iceD_updateAction(in, current);
        }
        case 35:
        {
            return _iceD_updateItem(in, current);
        }
        case 36:
        {
            return _iceD_updateTask(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_getTaskItems(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::GetTaskItemsResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_getTaskItems_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_getTaskItems_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::GetTaskItemsResult v;
            istr->readAll(v.task, v.items, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_getItemActions(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::GetItemActionsResult>>& outAsync, const ::std::string& iceP_itemID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_getItemActions_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_getItemActions_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_itemID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::GetItemActionsResult v;
            istr->readAll(v.item, v.actions, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_createTypicalTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::CreateTypicalTaskResult>>& outAsync, const StringMap& iceP_params, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_createTypicalTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_createTypicalTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_params);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::CreateTypicalTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_getTaskTypeObjects(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::GetTaskTypeObjectsResult>>& outAsync, const StringMap& iceP_params, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_getTaskTypeObjects_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_getTaskTypeObjects_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_params);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::GetTaskTypeObjectsResult v;
            istr->readAll(v.listObject, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_createSpecialTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::CreateSpecialTaskResult>>& outAsync, const ::std::string& iceP_taskTypeID, const StringList& iceP_listObjectID, const StringMap& iceP_params, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_createSpecialTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_createSpecialTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskTypeID, iceP_listObjectID, iceP_params);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::CreateSpecialTaskResult v;
            istr->readAll(v.taskID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_createCustomTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::CreateCustomTaskResult>>& outAsync, const ListStringMap& iceP_listItem, const StringMap& iceP_params, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_createCustomTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_createCustomTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listItem, iceP_params);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::CreateCustomTaskResult v;
            istr->readAll(v.taskID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_createUAVTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::CreateUAVTaskResult>>& outAsync, const StringMap& iceP_task, const ListStringMap& iceP_listPreset, const ListStringMap& iceP_listItem, const ListStringMap& iceP_listAction, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_createUAVTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_createUAVTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_task, iceP_listPreset, iceP_listItem, iceP_listAction);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::CreateUAVTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_editTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::EditTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_task, const ListStringMap& iceP_items, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_editTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_editTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_task, iceP_items);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::EditTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_convertTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::ConvertTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_params, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_convertTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_convertTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_params);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::ConvertTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_skipItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::SkipItemResult>>& outAsync, const ::std::string& iceP_itemID, const StringMap& iceP_params, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_skipItem_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_skipItem_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_itemID, iceP_params);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::SkipItemResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_updateTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::UpdateTaskResult>>& outAsync, const ListStringMap& iceP_listTask, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_updateTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_updateTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listTask);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::UpdateTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_updateItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::UpdateItemResult>>& outAsync, const ListStringMap& iceP_listItem, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_updateItem_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_updateItem_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listItem);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::UpdateItemResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_updateAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::UpdateActionResult>>& outAsync, const ListStringMap& iceP_listAction, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_updateAction_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_updateAction_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listAction);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::UpdateActionResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskITPrx::_iceI_downloadTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::DownloadTaskResult>>& outAsync, const StringList& iceP_listTaskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_downloadTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIT_downloadTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listTaskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIT::DownloadTaskResult v;
            istr->readAll(v.listTask, v.listItem, v.listAction, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGOPTaskITPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGOPTaskITPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGOPTaskITPrx::ice_staticId()
{
    return ZGOPTaskIT::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPTaskIT_getTaskItems_name = "getTaskItems";

const ::std::string iceC_ZG6000_ZGOPTaskIT_getItemActions_name = "getItemActions";

const ::std::string iceC_ZG6000_ZGOPTaskIT_createTypicalTask_name = "createTypicalTask";

const ::std::string iceC_ZG6000_ZGOPTaskIT_getTaskTypeObjects_name = "getTaskTypeObjects";

const ::std::string iceC_ZG6000_ZGOPTaskIT_createSpecialTask_name = "createSpecialTask";

const ::std::string iceC_ZG6000_ZGOPTaskIT_createCustomTask_name = "createCustomTask";

const ::std::string iceC_ZG6000_ZGOPTaskIT_createUAVTask_name = "createUAVTask";

const ::std::string iceC_ZG6000_ZGOPTaskIT_editTask_name = "editTask";

const ::std::string iceC_ZG6000_ZGOPTaskIT_convertTask_name = "convertTask";

const ::std::string iceC_ZG6000_ZGOPTaskIT_skipItem_name = "skipItem";

const ::std::string iceC_ZG6000_ZGOPTaskIT_updateTask_name = "updateTask";

const ::std::string iceC_ZG6000_ZGOPTaskIT_updateItem_name = "updateItem";

const ::std::string iceC_ZG6000_ZGOPTaskIT_updateAction_name = "updateAction";

const ::std::string iceC_ZG6000_ZGOPTaskIT_downloadTask_name = "downloadTask";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGOPTaskIT* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGOPTaskIT>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGOPTaskIT;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_getTaskItems(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_getTaskItems_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_getTaskItems_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_getTaskItems_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_getTaskItems_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_getTaskItems(::ZG6000::StringMap& iceP_task, ::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_getTaskItems_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_task);
    istr->read(iceP_items);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_getTaskItems(::ZG6000::StringMap& iceP_task, ::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_getTaskItems_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_task);
    istr->read(iceP_items);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_getItemActions(const ::std::string& iceP_itemID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_getItemActions_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_getItemActions_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_getItemActions_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_itemID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_getItemActions_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_getItemActions(::ZG6000::StringMap& iceP_item, ::ZG6000::ListStringMap& iceP_actions, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_getItemActions_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_item);
    istr->read(iceP_actions);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_getItemActions(::ZG6000::StringMap& iceP_item, ::ZG6000::ListStringMap& iceP_actions, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_getItemActions_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_item);
    istr->read(iceP_actions);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_createTypicalTask(const ::ZG6000::StringMap& iceP_params, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_createTypicalTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_createTypicalTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_createTypicalTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_params);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_createTypicalTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_createTypicalTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_createTypicalTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_createTypicalTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_createTypicalTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_getTaskTypeObjects(const ::ZG6000::StringMap& iceP_params, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_getTaskTypeObjects_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_getTaskTypeObjects_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_getTaskTypeObjects_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_params);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_getTaskTypeObjects_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_getTaskTypeObjects(::ZG6000::ListStringMap& iceP_listObject, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_getTaskTypeObjects_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listObject);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_getTaskTypeObjects(::ZG6000::ListStringMap& iceP_listObject, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_getTaskTypeObjects_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listObject);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_createSpecialTask(const ::std::string& iceP_taskTypeID, const ::ZG6000::StringList& iceP_listObjectID, const ::ZG6000::StringMap& iceP_params, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_createSpecialTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_createSpecialTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_createSpecialTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskTypeID);
        ostr->write(iceP_listObjectID);
        ostr->write(iceP_params);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_createSpecialTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_createSpecialTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_createSpecialTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_createSpecialTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_createSpecialTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_createCustomTask(const ::ZG6000::ListStringMap& iceP_listItem, const ::ZG6000::StringMap& iceP_params, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_createCustomTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_createCustomTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_createCustomTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listItem);
        ostr->write(iceP_params);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_createCustomTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_createCustomTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_createCustomTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_createCustomTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_createCustomTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_createUAVTask(const ::ZG6000::StringMap& iceP_task, const ::ZG6000::ListStringMap& iceP_listPreset, const ::ZG6000::ListStringMap& iceP_listItem, const ::ZG6000::ListStringMap& iceP_listAction, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_createUAVTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_createUAVTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_createUAVTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_task);
        ostr->write(iceP_listPreset);
        ostr->write(iceP_listItem);
        ostr->write(iceP_listAction);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_createUAVTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_createUAVTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_createUAVTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_createUAVTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_createUAVTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_editTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_task, const ::ZG6000::ListStringMap& iceP_items, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_editTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_editTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_editTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_task);
        ostr->write(iceP_items);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_editTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_editTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_editTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_editTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_editTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_convertTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_params, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_convertTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_convertTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_convertTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_params);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_convertTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_convertTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_convertTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_convertTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_convertTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_skipItem(const ::std::string& iceP_itemID, const ::ZG6000::StringMap& iceP_params, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_skipItem_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_skipItem_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_skipItem_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_itemID);
        ostr->write(iceP_params);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_skipItem_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_skipItem(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_skipItem_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_skipItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_skipItem_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_updateTask(const ::ZG6000::ListStringMap& iceP_listTask, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_updateTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_updateTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_updateTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listTask);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_updateTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_updateTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_updateTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_updateTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_updateTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_updateItem(const ::ZG6000::ListStringMap& iceP_listItem, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_updateItem_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_updateItem_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_updateItem_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listItem);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_updateItem_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_updateItem(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_updateItem_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_updateItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_updateItem_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_updateAction(const ::ZG6000::ListStringMap& iceP_listAction, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_updateAction_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_updateAction_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_updateAction_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listAction);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_updateAction_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_updateAction(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_updateAction_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_updateAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_updateAction_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIT::_iceI_begin_downloadTask(const ::ZG6000::StringList& iceP_listTaskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIT_downloadTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIT_downloadTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIT_downloadTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listTaskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIT_downloadTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIT::end_downloadTask(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ListStringMap& iceP_listAction, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_downloadTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTask);
    istr->read(iceP_listItem);
    istr->read(iceP_listAction);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIT::_iceI_end_downloadTask(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ListStringMap& iceP_listAction, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIT_downloadTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTask);
    istr->read(iceP_listItem);
    istr->read(iceP_listAction);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGOPTaskIT::_newInstance() const
{
    return new ZGOPTaskIT;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGOPTaskIT::ice_staticId()
{
    return ::ZG6000::ZGOPTaskIT::ice_staticId();
}

ZG6000::ZGOPTaskIT::~ZGOPTaskIT()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGOPTaskIT* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPTaskIT_ids[4] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPTaskBase",
    "::ZG6000::ZGOPTaskIT",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGOPTaskIT::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPTaskIT_ids, iceC_ZG6000_ZGOPTaskIT_ids + 4, s);
}

::std::vector< ::std::string>
ZG6000::ZGOPTaskIT::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGOPTaskIT_ids[0], &iceC_ZG6000_ZGOPTaskIT_ids[4]);
}

const ::std::string&
ZG6000::ZGOPTaskIT::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPTaskIT::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGOPTaskIT";
    return typeId;
#else
    return iceC_ZG6000_ZGOPTaskIT_ids[2];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_getTaskItems(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    StringMap iceP_task;
    ListStringMap iceP_items;
    ErrorInfo iceP_e;
    bool ret = this->getTaskItems(iceP_taskID, iceP_task, iceP_items, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_task);
    ostr->write(iceP_items);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_getItemActions(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_itemID;
    istr->read(iceP_itemID);
    inS.endReadParams();
    StringMap iceP_item;
    ListStringMap iceP_actions;
    ErrorInfo iceP_e;
    bool ret = this->getItemActions(iceP_itemID, iceP_item, iceP_actions, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_item);
    ostr->write(iceP_actions);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_createTypicalTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_params;
    istr->read(iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->createTypicalTask(iceP_params, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_getTaskTypeObjects(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_params;
    istr->read(iceP_params);
    inS.endReadParams();
    ListStringMap iceP_listObject;
    ErrorInfo iceP_e;
    bool ret = this->getTaskTypeObjects(iceP_params, iceP_listObject, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listObject);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_createSpecialTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskTypeID;
    StringList iceP_listObjectID;
    StringMap iceP_params;
    istr->read(iceP_taskTypeID);
    istr->read(iceP_listObjectID);
    istr->read(iceP_params);
    inS.endReadParams();
    ::std::string iceP_taskID;
    ErrorInfo iceP_e;
    bool ret = this->createSpecialTask(iceP_taskTypeID, iceP_listObjectID, iceP_params, iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_taskID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_createCustomTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    StringMap iceP_params;
    istr->read(iceP_listItem);
    istr->read(iceP_params);
    inS.endReadParams();
    ::std::string iceP_taskID;
    ErrorInfo iceP_e;
    bool ret = this->createCustomTask(iceP_listItem, iceP_params, iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_taskID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_createUAVTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_task;
    ListStringMap iceP_listPreset;
    ListStringMap iceP_listItem;
    ListStringMap iceP_listAction;
    istr->read(iceP_task);
    istr->read(iceP_listPreset);
    istr->read(iceP_listItem);
    istr->read(iceP_listAction);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->createUAVTask(iceP_task, iceP_listPreset, iceP_listItem, iceP_listAction, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_editTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_task;
    ListStringMap iceP_items;
    istr->read(iceP_taskID);
    istr->read(iceP_task);
    istr->read(iceP_items);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->editTask(iceP_taskID, iceP_task, iceP_items, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_convertTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_params;
    istr->read(iceP_taskID);
    istr->read(iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->convertTask(iceP_taskID, iceP_params, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_skipItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_itemID;
    StringMap iceP_params;
    istr->read(iceP_itemID);
    istr->read(iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->skipItem(iceP_itemID, iceP_params, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_updateTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listTask;
    istr->read(iceP_listTask);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateTask(iceP_listTask, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_updateItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->read(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateItem(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_updateAction(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listAction;
    istr->read(iceP_listAction);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateAction(iceP_listAction, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceD_downloadTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listTaskID;
    istr->read(iceP_listTaskID);
    inS.endReadParams();
    ListStringMap iceP_listTask;
    ListStringMap iceP_listItem;
    ListStringMap iceP_listAction;
    ErrorInfo iceP_e;
    bool ret = this->downloadTask(iceP_listTaskID, iceP_listTask, iceP_listItem, iceP_listAction, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listTask);
    ostr->write(iceP_listItem);
    ostr->write(iceP_listAction);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPTaskIT_all[] =
{
    "abolishTask",
    "checkState",
    "confirmTask",
    "convertTask",
    "createCustomTask",
    "createSpecialTask",
    "createTypicalTask",
    "createUAVTask",
    "deleteTask",
    "dispatchData",
    "downloadTask",
    "editTask",
    "exitApp",
    "getItemActions",
    "getTaskItems",
    "getTaskList",
    "getTaskTypeObjects",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "pauseTask",
    "resumeDebug",
    "resumeTask",
    "retryTask",
    "skipItem",
    "startDebug",
    "startTask",
    "stopDebug",
    "test",
    "updateAction",
    "updateItem",
    "updateTask"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIT::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPTaskIT_all, iceC_ZG6000_ZGOPTaskIT_all + 37, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPTaskIT_all)
    {
        case 0:
        {
            return _iceD_abolishTask(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_confirmTask(in, current);
        }
        case 3:
        {
            return _iceD_convertTask(in, current);
        }
        case 4:
        {
            return _iceD_createCustomTask(in, current);
        }
        case 5:
        {
            return _iceD_createSpecialTask(in, current);
        }
        case 6:
        {
            return _iceD_createTypicalTask(in, current);
        }
        case 7:
        {
            return _iceD_createUAVTask(in, current);
        }
        case 8:
        {
            return _iceD_deleteTask(in, current);
        }
        case 9:
        {
            return _iceD_dispatchData(in, current);
        }
        case 10:
        {
            return _iceD_downloadTask(in, current);
        }
        case 11:
        {
            return _iceD_editTask(in, current);
        }
        case 12:
        {
            return _iceD_exitApp(in, current);
        }
        case 13:
        {
            return _iceD_getItemActions(in, current);
        }
        case 14:
        {
            return _iceD_getTaskItems(in, current);
        }
        case 15:
        {
            return _iceD_getTaskList(in, current);
        }
        case 16:
        {
            return _iceD_getTaskTypeObjects(in, current);
        }
        case 17:
        {
            return _iceD_getVersion(in, current);
        }
        case 18:
        {
            return _iceD_heartDebug(in, current);
        }
        case 19:
        {
            return _iceD_ice_id(in, current);
        }
        case 20:
        {
            return _iceD_ice_ids(in, current);
        }
        case 21:
        {
            return _iceD_ice_isA(in, current);
        }
        case 22:
        {
            return _iceD_ice_ping(in, current);
        }
        case 23:
        {
            return _iceD_isDebugging(in, current);
        }
        case 24:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 25:
        {
            return _iceD_pauseTask(in, current);
        }
        case 26:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 27:
        {
            return _iceD_resumeTask(in, current);
        }
        case 28:
        {
            return _iceD_retryTask(in, current);
        }
        case 29:
        {
            return _iceD_skipItem(in, current);
        }
        case 30:
        {
            return _iceD_startDebug(in, current);
        }
        case 31:
        {
            return _iceD_startTask(in, current);
        }
        case 32:
        {
            return _iceD_stopDebug(in, current);
        }
        case 33:
        {
            return _iceD_test(in, current);
        }
        case 34:
        {
            return _iceD_updateAction(in, current);
        }
        case 35:
        {
            return _iceD_updateItem(in, current);
        }
        case 36:
        {
            return _iceD_updateTask(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGOPTaskIT::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGOPTaskIT, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGOPTaskIT::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGOPTaskIT, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGOPTaskITPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGOPTaskITPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGOPTaskIT::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPTaskManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGMPTaskManager_h__
#define __ZGMPTaskManager_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGMPTaskManager;
class ZGMPTaskManagerPrx;

}

namespace ZG6000
{

class ZGMPTaskManager : public virtual ZGServerBase
{
public:

    using ProxyType = ZGMPTaskManagerPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getTask.
     */
    struct GetTaskResult
    {
        bool returnValue;
        StringMap task;
        ErrorInfo e;
    };

    virtual bool getTask(::std::string taskID, StringMap& task, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getTaskList.
     */
    struct GetTaskListResult
    {
        bool returnValue;
        ListStringMap taskList;
        ErrorInfo e;
    };

    virtual bool getTaskList(::std::string appNodeID, ::std::string subsystemID, ::std::string majorID, ::std::string taskTypeID, ListStringMap& taskList, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getTaskItems.
     */
    struct GetTaskItemsResult
    {
        bool returnValue;
        ListStringMap itemList;
        ErrorInfo e;
    };

    virtual bool getTaskItems(::std::string taskID, ListStringMap& itemList, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskItems(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to startTask.
     */
    struct StartTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool startTask(::std::string clientID, ::std::string _cpp_operator, ::std::string monitor, ::std::string taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_startTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to stopTask.
     */
    struct StopTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool stopTask(::std::string clientID, ::std::string _cpp_operator, ::std::string monitor, ::std::string taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_stopTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to pauseTask.
     */
    struct PauseTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool pauseTask(::std::string clientID, ::std::string _cpp_operator, ::std::string monitor, ::std::string taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_pauseTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to resumeTask.
     */
    struct ResumeTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool resumeTask(::std::string clientID, ::std::string _cpp_operator, ::std::string monitor, ::std::string taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_resumeTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getExecCondition.
     */
    struct GetExecConditionResult
    {
        bool returnValue;
        ListStringMap lstMapData;
        ErrorInfo e;
    };

    virtual bool getExecCondition(::std::string taskID, ListStringMap& lstMapData, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getExecCondition(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getConfirmCondition.
     */
    struct GetConfirmConditionResult
    {
        bool returnValue;
        ListStringMap lstMapData;
        ErrorInfo e;
    };

    virtual bool getConfirmCondition(::std::string taskID, ListStringMap& lstMapData, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getConfirmCondition(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getErrorCondition.
     */
    struct GetErrorConditionResult
    {
        bool returnValue;
        ListStringMap lstMapData;
        ErrorInfo e;
    };

    virtual bool getErrorCondition(::std::string taskID, ListStringMap& lstMapData, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getErrorCondition(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGMPTaskManagerPrx : public virtual ::Ice::Proxy<ZGMPTaskManagerPrx, ZGServerBasePrx>
{
public:

    bool getTask(const ::std::string& taskID, StringMap& task, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPTaskManager::GetTaskResult>(true, this, &ZGMPTaskManagerPrx::_iceI_getTask, taskID, context).get();
        task = ::std::move(_result.task);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTaskAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPTaskManager::GetTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPTaskManager::GetTaskResult, P>(false, this, &ZGMPTaskManagerPrx::_iceI_getTask, taskID, context);
    }

    ::std::function<void()>
    getTaskAsync(const ::std::string& taskID,
                 ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                 ::std::function<void(::std::exception_ptr)> ex = nullptr,
                 ::std::function<void(bool)> sent = nullptr,
                 const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPTaskManager::GetTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.task), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPTaskManager::GetTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPTaskManagerPrx::_iceI_getTask, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetTaskResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getTaskList(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& majorID, const ::std::string& taskTypeID, ListStringMap& taskList, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPTaskManager::GetTaskListResult>(true, this, &ZGMPTaskManagerPrx::_iceI_getTaskList, appNodeID, subsystemID, majorID, taskTypeID, context).get();
        taskList = ::std::move(_result.taskList);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTaskListAsync(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& majorID, const ::std::string& taskTypeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPTaskManager::GetTaskListResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPTaskManager::GetTaskListResult, P>(false, this, &ZGMPTaskManagerPrx::_iceI_getTaskList, appNodeID, subsystemID, majorID, taskTypeID, context);
    }

    ::std::function<void()>
    getTaskListAsync(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& majorID, const ::std::string& taskTypeID,
                     ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPTaskManager::GetTaskListResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.taskList), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPTaskManager::GetTaskListResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPTaskManagerPrx::_iceI_getTaskList, appNodeID, subsystemID, majorID, taskTypeID, context);
    }

    /// \cond INTERNAL
    void _iceI_getTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetTaskListResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getTaskItems(const ::std::string& taskID, ListStringMap& itemList, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPTaskManager::GetTaskItemsResult>(true, this, &ZGMPTaskManagerPrx::_iceI_getTaskItems, taskID, context).get();
        itemList = ::std::move(_result.itemList);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTaskItemsAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPTaskManager::GetTaskItemsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPTaskManager::GetTaskItemsResult, P>(false, this, &ZGMPTaskManagerPrx::_iceI_getTaskItems, taskID, context);
    }

    ::std::function<void()>
    getTaskItemsAsync(const ::std::string& taskID,
                      ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPTaskManager::GetTaskItemsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.itemList), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPTaskManager::GetTaskItemsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPTaskManagerPrx::_iceI_getTaskItems, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getTaskItems(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetTaskItemsResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool startTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPTaskManager::StartTaskResult>(true, this, &ZGMPTaskManagerPrx::_iceI_startTask, clientID, _cpp_operator, monitor, taskID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto startTaskAsync(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPTaskManager::StartTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPTaskManager::StartTaskResult, P>(false, this, &ZGMPTaskManagerPrx::_iceI_startTask, clientID, _cpp_operator, monitor, taskID, context);
    }

    ::std::function<void()>
    startTaskAsync(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPTaskManager::StartTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPTaskManager::StartTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPTaskManagerPrx::_iceI_startTask, clientID, _cpp_operator, monitor, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_startTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::StartTaskResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool stopTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPTaskManager::StopTaskResult>(true, this, &ZGMPTaskManagerPrx::_iceI_stopTask, clientID, _cpp_operator, monitor, taskID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto stopTaskAsync(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPTaskManager::StopTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPTaskManager::StopTaskResult, P>(false, this, &ZGMPTaskManagerPrx::_iceI_stopTask, clientID, _cpp_operator, monitor, taskID, context);
    }

    ::std::function<void()>
    stopTaskAsync(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPTaskManager::StopTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPTaskManager::StopTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPTaskManagerPrx::_iceI_stopTask, clientID, _cpp_operator, monitor, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_stopTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::StopTaskResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool pauseTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPTaskManager::PauseTaskResult>(true, this, &ZGMPTaskManagerPrx::_iceI_pauseTask, clientID, _cpp_operator, monitor, taskID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto pauseTaskAsync(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPTaskManager::PauseTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPTaskManager::PauseTaskResult, P>(false, this, &ZGMPTaskManagerPrx::_iceI_pauseTask, clientID, _cpp_operator, monitor, taskID, context);
    }

    ::std::function<void()>
    pauseTaskAsync(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPTaskManager::PauseTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPTaskManager::PauseTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPTaskManagerPrx::_iceI_pauseTask, clientID, _cpp_operator, monitor, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_pauseTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::PauseTaskResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool resumeTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPTaskManager::ResumeTaskResult>(true, this, &ZGMPTaskManagerPrx::_iceI_resumeTask, clientID, _cpp_operator, monitor, taskID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto resumeTaskAsync(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPTaskManager::ResumeTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPTaskManager::ResumeTaskResult, P>(false, this, &ZGMPTaskManagerPrx::_iceI_resumeTask, clientID, _cpp_operator, monitor, taskID, context);
    }

    ::std::function<void()>
    resumeTaskAsync(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPTaskManager::ResumeTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPTaskManager::ResumeTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPTaskManagerPrx::_iceI_resumeTask, clientID, _cpp_operator, monitor, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_resumeTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::ResumeTaskResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getExecCondition(const ::std::string& taskID, ListStringMap& lstMapData, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPTaskManager::GetExecConditionResult>(true, this, &ZGMPTaskManagerPrx::_iceI_getExecCondition, taskID, context).get();
        lstMapData = ::std::move(_result.lstMapData);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getExecConditionAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPTaskManager::GetExecConditionResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPTaskManager::GetExecConditionResult, P>(false, this, &ZGMPTaskManagerPrx::_iceI_getExecCondition, taskID, context);
    }

    ::std::function<void()>
    getExecConditionAsync(const ::std::string& taskID,
                          ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPTaskManager::GetExecConditionResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.lstMapData), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPTaskManager::GetExecConditionResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPTaskManagerPrx::_iceI_getExecCondition, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getExecCondition(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetExecConditionResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getConfirmCondition(const ::std::string& taskID, ListStringMap& lstMapData, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPTaskManager::GetConfirmConditionResult>(true, this, &ZGMPTaskManagerPrx::_iceI_getConfirmCondition, taskID, context).get();
        lstMapData = ::std::move(_result.lstMapData);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getConfirmConditionAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPTaskManager::GetConfirmConditionResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPTaskManager::GetConfirmConditionResult, P>(false, this, &ZGMPTaskManagerPrx::_iceI_getConfirmCondition, taskID, context);
    }

    ::std::function<void()>
    getConfirmConditionAsync(const ::std::string& taskID,
                             ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPTaskManager::GetConfirmConditionResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.lstMapData), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPTaskManager::GetConfirmConditionResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPTaskManagerPrx::_iceI_getConfirmCondition, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getConfirmCondition(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetConfirmConditionResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getErrorCondition(const ::std::string& taskID, ListStringMap& lstMapData, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPTaskManager::GetErrorConditionResult>(true, this, &ZGMPTaskManagerPrx::_iceI_getErrorCondition, taskID, context).get();
        lstMapData = ::std::move(_result.lstMapData);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getErrorConditionAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPTaskManager::GetErrorConditionResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPTaskManager::GetErrorConditionResult, P>(false, this, &ZGMPTaskManagerPrx::_iceI_getErrorCondition, taskID, context);
    }

    ::std::function<void()>
    getErrorConditionAsync(const ::std::string& taskID,
                           ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPTaskManager::GetErrorConditionResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.lstMapData), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPTaskManager::GetErrorConditionResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPTaskManagerPrx::_iceI_getErrorCondition, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getErrorCondition(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPTaskManager::GetErrorConditionResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGMPTaskManagerPrx() = default;
    friend ::std::shared_ptr<ZGMPTaskManagerPrx> IceInternal::createProxy<ZGMPTaskManagerPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGMPTaskManagerPtr = ::std::shared_ptr<ZGMPTaskManager>;
using ZGMPTaskManagerPrxPtr = ::std::shared_ptr<ZGMPTaskManagerPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGMPTaskManager;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGMPTaskManager>&);
::IceProxy::Ice::Object* upCast(ZGMPTaskManager*);
/// \endcond

}

}

namespace ZG6000
{

class ZGMPTaskManager;
/// \cond INTERNAL
::Ice::Object* upCast(ZGMPTaskManager*);
/// \endcond
typedef ::IceInternal::Handle< ZGMPTaskManager> ZGMPTaskManagerPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGMPTaskManager> ZGMPTaskManagerPrx;
typedef ZGMPTaskManagerPrx ZGMPTaskManagerPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGMPTaskManagerPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getTask.
 */
class Callback_ZGMPTaskManager_getTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPTaskManager_getTask_Base> Callback_ZGMPTaskManager_getTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getTaskList.
 */
class Callback_ZGMPTaskManager_getTaskList_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPTaskManager_getTaskList_Base> Callback_ZGMPTaskManager_getTaskListPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskItems.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getTaskItems.
 */
class Callback_ZGMPTaskManager_getTaskItems_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPTaskManager_getTaskItems_Base> Callback_ZGMPTaskManager_getTaskItemsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_startTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_startTask.
 */
class Callback_ZGMPTaskManager_startTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPTaskManager_startTask_Base> Callback_ZGMPTaskManager_startTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_stopTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_stopTask.
 */
class Callback_ZGMPTaskManager_stopTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPTaskManager_stopTask_Base> Callback_ZGMPTaskManager_stopTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_pauseTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_pauseTask.
 */
class Callback_ZGMPTaskManager_pauseTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPTaskManager_pauseTask_Base> Callback_ZGMPTaskManager_pauseTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_resumeTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_resumeTask.
 */
class Callback_ZGMPTaskManager_resumeTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPTaskManager_resumeTask_Base> Callback_ZGMPTaskManager_resumeTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getExecCondition.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getExecCondition.
 */
class Callback_ZGMPTaskManager_getExecCondition_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPTaskManager_getExecCondition_Base> Callback_ZGMPTaskManager_getExecConditionPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getConfirmCondition.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getConfirmCondition.
 */
class Callback_ZGMPTaskManager_getConfirmCondition_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPTaskManager_getConfirmCondition_Base> Callback_ZGMPTaskManager_getConfirmConditionPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getErrorCondition.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getErrorCondition.
 */
class Callback_ZGMPTaskManager_getErrorCondition_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPTaskManager_getErrorCondition_Base> Callback_ZGMPTaskManager_getErrorConditionPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGMPTaskManager : public virtual ::Ice::Proxy<ZGMPTaskManager, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool getTask(const ::std::string& taskID, ::ZG6000::StringMap& task, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTask(task, e, _iceI_begin_getTask(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTask(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTask(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTask(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTask(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTask(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTask(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTask(const ::std::string& taskID, const ::ZG6000::Callback_ZGMPTaskManager_getTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTask(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTask(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPTaskManager_getTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTask(taskID, context, cb, cookie);
    }

    bool end_getTask(::ZG6000::StringMap& task, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTask(::ZG6000::StringMap& iceP_task, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTask(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getTaskList(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& majorID, const ::std::string& taskTypeID, ::ZG6000::ListStringMap& taskList, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTaskList(taskList, e, _iceI_begin_getTaskList(appNodeID, subsystemID, majorID, taskTypeID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTaskList(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& majorID, const ::std::string& taskTypeID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTaskList(appNodeID, subsystemID, majorID, taskTypeID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTaskList(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& majorID, const ::std::string& taskTypeID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(appNodeID, subsystemID, majorID, taskTypeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskList(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& majorID, const ::std::string& taskTypeID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(appNodeID, subsystemID, majorID, taskTypeID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskList(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& majorID, const ::std::string& taskTypeID, const ::ZG6000::Callback_ZGMPTaskManager_getTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(appNodeID, subsystemID, majorID, taskTypeID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskList(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& majorID, const ::std::string& taskTypeID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPTaskManager_getTaskListPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskList(appNodeID, subsystemID, majorID, taskTypeID, context, cb, cookie);
    }

    bool end_getTaskList(::ZG6000::ListStringMap& taskList, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTaskList(::ZG6000::ListStringMap& iceP_taskList, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTaskList(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getTaskItems(const ::std::string& taskID, ::ZG6000::ListStringMap& itemList, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTaskItems(itemList, e, _iceI_begin_getTaskItems(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTaskItems(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::ZG6000::Callback_ZGMPTaskManager_getTaskItemsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPTaskManager_getTaskItemsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, context, cb, cookie);
    }

    bool end_getTaskItems(::ZG6000::ListStringMap& itemList, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTaskItems(::ZG6000::ListStringMap& iceP_itemList, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTaskItems(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool startTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_startTask(e, _iceI_begin_startTask(clientID, _cpp_operator, monitor, taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_startTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_startTask(clientID, _cpp_operator, monitor, taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_startTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startTask(clientID, _cpp_operator, monitor, taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_startTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startTask(clientID, _cpp_operator, monitor, taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_startTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::ZG6000::Callback_ZGMPTaskManager_startTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startTask(clientID, _cpp_operator, monitor, taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_startTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPTaskManager_startTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startTask(clientID, _cpp_operator, monitor, taskID, context, cb, cookie);
    }

    bool end_startTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_startTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_startTask(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool stopTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_stopTask(e, _iceI_begin_stopTask(clientID, _cpp_operator, monitor, taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_stopTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_stopTask(clientID, _cpp_operator, monitor, taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_stopTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopTask(clientID, _cpp_operator, monitor, taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_stopTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopTask(clientID, _cpp_operator, monitor, taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_stopTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::ZG6000::Callback_ZGMPTaskManager_stopTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopTask(clientID, _cpp_operator, monitor, taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_stopTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPTaskManager_stopTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopTask(clientID, _cpp_operator, monitor, taskID, context, cb, cookie);
    }

    bool end_stopTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_stopTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_stopTask(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool pauseTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_pauseTask(e, _iceI_begin_pauseTask(clientID, _cpp_operator, monitor, taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_pauseTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_pauseTask(clientID, _cpp_operator, monitor, taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_pauseTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseTask(clientID, _cpp_operator, monitor, taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_pauseTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseTask(clientID, _cpp_operator, monitor, taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_pauseTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::ZG6000::Callback_ZGMPTaskManager_pauseTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseTask(clientID, _cpp_operator, monitor, taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_pauseTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPTaskManager_pauseTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_pauseTask(clientID, _cpp_operator, monitor, taskID, context, cb, cookie);
    }

    bool end_pauseTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_pauseTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_pauseTask(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool resumeTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_resumeTask(e, _iceI_begin_resumeTask(clientID, _cpp_operator, monitor, taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_resumeTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_resumeTask(clientID, _cpp_operator, monitor, taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_resumeTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeTask(clientID, _cpp_operator, monitor, taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resumeTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeTask(clientID, _cpp_operator, monitor, taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resumeTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::ZG6000::Callback_ZGMPTaskManager_resumeTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeTask(clientID, _cpp_operator, monitor, taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resumeTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPTaskManager_resumeTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resumeTask(clientID, _cpp_operator, monitor, taskID, context, cb, cookie);
    }

    bool end_resumeTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_resumeTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_resumeTask(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getExecCondition(const ::std::string& taskID, ::ZG6000::ListStringMap& lstMapData, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getExecCondition(lstMapData, e, _iceI_begin_getExecCondition(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getExecCondition(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getExecCondition(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getExecCondition(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getExecCondition(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getExecCondition(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getExecCondition(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getExecCondition(const ::std::string& taskID, const ::ZG6000::Callback_ZGMPTaskManager_getExecConditionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getExecCondition(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getExecCondition(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPTaskManager_getExecConditionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getExecCondition(taskID, context, cb, cookie);
    }

    bool end_getExecCondition(::ZG6000::ListStringMap& lstMapData, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getExecCondition(::ZG6000::ListStringMap& iceP_lstMapData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getExecCondition(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getConfirmCondition(const ::std::string& taskID, ::ZG6000::ListStringMap& lstMapData, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getConfirmCondition(lstMapData, e, _iceI_begin_getConfirmCondition(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getConfirmCondition(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getConfirmCondition(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getConfirmCondition(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getConfirmCondition(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getConfirmCondition(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getConfirmCondition(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getConfirmCondition(const ::std::string& taskID, const ::ZG6000::Callback_ZGMPTaskManager_getConfirmConditionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getConfirmCondition(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getConfirmCondition(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPTaskManager_getConfirmConditionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getConfirmCondition(taskID, context, cb, cookie);
    }

    bool end_getConfirmCondition(::ZG6000::ListStringMap& lstMapData, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getConfirmCondition(::ZG6000::ListStringMap& iceP_lstMapData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getConfirmCondition(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getErrorCondition(const ::std::string& taskID, ::ZG6000::ListStringMap& lstMapData, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getErrorCondition(lstMapData, e, _iceI_begin_getErrorCondition(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getErrorCondition(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getErrorCondition(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getErrorCondition(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getErrorCondition(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getErrorCondition(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getErrorCondition(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getErrorCondition(const ::std::string& taskID, const ::ZG6000::Callback_ZGMPTaskManager_getErrorConditionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getErrorCondition(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getErrorCondition(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPTaskManager_getErrorConditionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getErrorCondition(taskID, context, cb, cookie);
    }

    bool end_getErrorCondition(::ZG6000::ListStringMap& lstMapData, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getErrorCondition(::ZG6000::ListStringMap& iceP_lstMapData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getErrorCondition(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGMPTaskManager : virtual public ZGServerBase
{
public:

    typedef ZGMPTaskManagerPrx ProxyType;
    typedef ZGMPTaskManagerPtr PointerType;

    virtual ~ZGMPTaskManager();

#ifdef ICE_CPP11_COMPILER
    ZGMPTaskManager() = default;
    ZGMPTaskManager(const ZGMPTaskManager&) = default;
    ZGMPTaskManager& operator=(const ZGMPTaskManager&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool getTask(const ::std::string& taskID, StringMap& task, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getTaskList(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::std::string& majorID, const ::std::string& taskTypeID, ListStringMap& taskList, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskList(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getTaskItems(const ::std::string& taskID, ListStringMap& itemList, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskItems(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool startTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_startTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool stopTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_stopTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool pauseTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_pauseTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool resumeTask(const ::std::string& clientID, const ::std::string& _cpp_operator, const ::std::string& monitor, const ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_resumeTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getExecCondition(const ::std::string& taskID, ListStringMap& lstMapData, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getExecCondition(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getConfirmCondition(const ::std::string& taskID, ListStringMap& lstMapData, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getConfirmCondition(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getErrorCondition(const ::std::string& taskID, ListStringMap& lstMapData, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getErrorCondition(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGMPTaskManager& lhs, const ZGMPTaskManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGMPTaskManager& lhs, const ZGMPTaskManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getTask.
 */
template<class T>
class CallbackNC_ZGMPTaskManager_getTask : public Callback_ZGMPTaskManager_getTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGMPTaskManager_getTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        StringMap iceP_task;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTask(iceP_task, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_task, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTask.
 */
template<class T> Callback_ZGMPTaskManager_getTaskPtr
newCallback_ZGMPTaskManager_getTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTask.
 */
template<class T> Callback_ZGMPTaskManager_getTaskPtr
newCallback_ZGMPTaskManager_getTask(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getTask.
 */
template<class T, typename CT>
class Callback_ZGMPTaskManager_getTask : public Callback_ZGMPTaskManager_getTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPTaskManager_getTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        StringMap iceP_task;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTask(iceP_task, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_task, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTask.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getTaskPtr
newCallback_ZGMPTaskManager_getTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTask.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getTaskPtr
newCallback_ZGMPTaskManager_getTask(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getTaskList.
 */
template<class T>
class CallbackNC_ZGMPTaskManager_getTaskList : public Callback_ZGMPTaskManager_getTaskList_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPTaskManager_getTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_taskList;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskList(iceP_taskList, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_taskList, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskList.
 */
template<class T> Callback_ZGMPTaskManager_getTaskListPtr
newCallback_ZGMPTaskManager_getTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskList.
 */
template<class T> Callback_ZGMPTaskManager_getTaskListPtr
newCallback_ZGMPTaskManager_getTaskList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getTaskList<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskList.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getTaskList.
 */
template<class T, typename CT>
class Callback_ZGMPTaskManager_getTaskList : public Callback_ZGMPTaskManager_getTaskList_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPTaskManager_getTaskList(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_taskList;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskList(iceP_taskList, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_taskList, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskList.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getTaskListPtr
newCallback_ZGMPTaskManager_getTaskList(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskList.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getTaskListPtr
newCallback_ZGMPTaskManager_getTaskList(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getTaskList<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskItems.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getTaskItems.
 */
template<class T>
class CallbackNC_ZGMPTaskManager_getTaskItems : public Callback_ZGMPTaskManager_getTaskItems_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPTaskManager_getTaskItems(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_itemList;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskItems(iceP_itemList, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_itemList, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskItems.
 */
template<class T> Callback_ZGMPTaskManager_getTaskItemsPtr
newCallback_ZGMPTaskManager_getTaskItems(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getTaskItems<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskItems.
 */
template<class T> Callback_ZGMPTaskManager_getTaskItemsPtr
newCallback_ZGMPTaskManager_getTaskItems(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getTaskItems<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskItems.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getTaskItems.
 */
template<class T, typename CT>
class Callback_ZGMPTaskManager_getTaskItems : public Callback_ZGMPTaskManager_getTaskItems_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPTaskManager_getTaskItems(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_itemList;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskItems(iceP_itemList, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_itemList, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskItems.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getTaskItemsPtr
newCallback_ZGMPTaskManager_getTaskItems(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getTaskItems<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getTaskItems.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getTaskItemsPtr
newCallback_ZGMPTaskManager_getTaskItems(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getTaskItems<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_startTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_startTask.
 */
template<class T>
class CallbackNC_ZGMPTaskManager_startTask : public Callback_ZGMPTaskManager_startTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPTaskManager_startTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_startTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_startTask.
 */
template<class T> Callback_ZGMPTaskManager_startTaskPtr
newCallback_ZGMPTaskManager_startTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_startTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_startTask.
 */
template<class T> Callback_ZGMPTaskManager_startTaskPtr
newCallback_ZGMPTaskManager_startTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_startTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_startTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_startTask.
 */
template<class T, typename CT>
class Callback_ZGMPTaskManager_startTask : public Callback_ZGMPTaskManager_startTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPTaskManager_startTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_startTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_startTask.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_startTaskPtr
newCallback_ZGMPTaskManager_startTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_startTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_startTask.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_startTaskPtr
newCallback_ZGMPTaskManager_startTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_startTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_stopTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_stopTask.
 */
template<class T>
class CallbackNC_ZGMPTaskManager_stopTask : public Callback_ZGMPTaskManager_stopTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPTaskManager_stopTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_stopTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_stopTask.
 */
template<class T> Callback_ZGMPTaskManager_stopTaskPtr
newCallback_ZGMPTaskManager_stopTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_stopTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_stopTask.
 */
template<class T> Callback_ZGMPTaskManager_stopTaskPtr
newCallback_ZGMPTaskManager_stopTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_stopTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_stopTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_stopTask.
 */
template<class T, typename CT>
class Callback_ZGMPTaskManager_stopTask : public Callback_ZGMPTaskManager_stopTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPTaskManager_stopTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_stopTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_stopTask.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_stopTaskPtr
newCallback_ZGMPTaskManager_stopTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_stopTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_stopTask.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_stopTaskPtr
newCallback_ZGMPTaskManager_stopTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_stopTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_pauseTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_pauseTask.
 */
template<class T>
class CallbackNC_ZGMPTaskManager_pauseTask : public Callback_ZGMPTaskManager_pauseTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPTaskManager_pauseTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_pauseTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_pauseTask.
 */
template<class T> Callback_ZGMPTaskManager_pauseTaskPtr
newCallback_ZGMPTaskManager_pauseTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_pauseTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_pauseTask.
 */
template<class T> Callback_ZGMPTaskManager_pauseTaskPtr
newCallback_ZGMPTaskManager_pauseTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_pauseTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_pauseTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_pauseTask.
 */
template<class T, typename CT>
class Callback_ZGMPTaskManager_pauseTask : public Callback_ZGMPTaskManager_pauseTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPTaskManager_pauseTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_pauseTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_pauseTask.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_pauseTaskPtr
newCallback_ZGMPTaskManager_pauseTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_pauseTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_pauseTask.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_pauseTaskPtr
newCallback_ZGMPTaskManager_pauseTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_pauseTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_resumeTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_resumeTask.
 */
template<class T>
class CallbackNC_ZGMPTaskManager_resumeTask : public Callback_ZGMPTaskManager_resumeTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGMPTaskManager_resumeTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_resumeTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_resumeTask.
 */
template<class T> Callback_ZGMPTaskManager_resumeTaskPtr
newCallback_ZGMPTaskManager_resumeTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_resumeTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_resumeTask.
 */
template<class T> Callback_ZGMPTaskManager_resumeTaskPtr
newCallback_ZGMPTaskManager_resumeTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_resumeTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_resumeTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_resumeTask.
 */
template<class T, typename CT>
class Callback_ZGMPTaskManager_resumeTask : public Callback_ZGMPTaskManager_resumeTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGMPTaskManager_resumeTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_resumeTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_resumeTask.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_resumeTaskPtr
newCallback_ZGMPTaskManager_resumeTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_resumeTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_resumeTask.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_resumeTaskPtr
newCallback_ZGMPTaskManager_resumeTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_resumeTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getExecCondition.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getExecCondition.
 */
template<class T>
class CallbackNC_ZGMPTaskManager_getExecCondition : public Callback_ZGMPTaskManager_getExecCondition_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPTaskManager_getExecCondition(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_lstMapData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getExecCondition(iceP_lstMapData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_lstMapData, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getExecCondition.
 */
template<class T> Callback_ZGMPTaskManager_getExecConditionPtr
newCallback_ZGMPTaskManager_getExecCondition(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getExecCondition<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getExecCondition.
 */
template<class T> Callback_ZGMPTaskManager_getExecConditionPtr
newCallback_ZGMPTaskManager_getExecCondition(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getExecCondition<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getExecCondition.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getExecCondition.
 */
template<class T, typename CT>
class Callback_ZGMPTaskManager_getExecCondition : public Callback_ZGMPTaskManager_getExecCondition_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPTaskManager_getExecCondition(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_lstMapData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getExecCondition(iceP_lstMapData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_lstMapData, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getExecCondition.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getExecConditionPtr
newCallback_ZGMPTaskManager_getExecCondition(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getExecCondition<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getExecCondition.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getExecConditionPtr
newCallback_ZGMPTaskManager_getExecCondition(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getExecCondition<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getConfirmCondition.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getConfirmCondition.
 */
template<class T>
class CallbackNC_ZGMPTaskManager_getConfirmCondition : public Callback_ZGMPTaskManager_getConfirmCondition_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPTaskManager_getConfirmCondition(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_lstMapData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getConfirmCondition(iceP_lstMapData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_lstMapData, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getConfirmCondition.
 */
template<class T> Callback_ZGMPTaskManager_getConfirmConditionPtr
newCallback_ZGMPTaskManager_getConfirmCondition(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getConfirmCondition<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getConfirmCondition.
 */
template<class T> Callback_ZGMPTaskManager_getConfirmConditionPtr
newCallback_ZGMPTaskManager_getConfirmCondition(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getConfirmCondition<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getConfirmCondition.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getConfirmCondition.
 */
template<class T, typename CT>
class Callback_ZGMPTaskManager_getConfirmCondition : public Callback_ZGMPTaskManager_getConfirmCondition_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPTaskManager_getConfirmCondition(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_lstMapData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getConfirmCondition(iceP_lstMapData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_lstMapData, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getConfirmCondition.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getConfirmConditionPtr
newCallback_ZGMPTaskManager_getConfirmCondition(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getConfirmCondition<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getConfirmCondition.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getConfirmConditionPtr
newCallback_ZGMPTaskManager_getConfirmCondition(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getConfirmCondition<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getErrorCondition.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getErrorCondition.
 */
template<class T>
class CallbackNC_ZGMPTaskManager_getErrorCondition : public Callback_ZGMPTaskManager_getErrorCondition_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPTaskManager_getErrorCondition(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_lstMapData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getErrorCondition(iceP_lstMapData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_lstMapData, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getErrorCondition.
 */
template<class T> Callback_ZGMPTaskManager_getErrorConditionPtr
newCallback_ZGMPTaskManager_getErrorCondition(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getErrorCondition<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getErrorCondition.
 */
template<class T> Callback_ZGMPTaskManager_getErrorConditionPtr
newCallback_ZGMPTaskManager_getErrorCondition(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPTaskManager_getErrorCondition<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPTaskManager::begin_getErrorCondition.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPTaskManager_getErrorCondition.
 */
template<class T, typename CT>
class Callback_ZGMPTaskManager_getErrorCondition : public Callback_ZGMPTaskManager_getErrorCondition_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPTaskManager_getErrorCondition(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPTaskManagerPrx proxy = ZGMPTaskManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_lstMapData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getErrorCondition(iceP_lstMapData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_lstMapData, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getErrorCondition.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getErrorConditionPtr
newCallback_ZGMPTaskManager_getErrorCondition(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getErrorCondition<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPTaskManager::begin_getErrorCondition.
 */
template<class T, typename CT> Callback_ZGMPTaskManager_getErrorConditionPtr
newCallback_ZGMPTaskManager_getErrorCondition(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPTaskManager_getErrorCondition<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

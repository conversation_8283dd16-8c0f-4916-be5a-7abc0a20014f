//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPExamManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPExamManager_h__
#define __ZGSPExamManager_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPExamManager;
class ZGSPExamManagerPrx;

}

namespace ZG6000
{

class ZGSPExamManager : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPExamManagerPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to createExam.
     */
    struct CreateExamResult
    {
        bool returnValue;
        ::std::string examID;
        ErrorInfo e;
    };

    virtual bool createExam(::std::string paramExamID, ::std::string& examID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createExam(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getExamInfo.
     */
    struct GetExamInfoResult
    {
        bool returnValue;
        ::std::string examInfo;
        ErrorInfo e;
    };

    virtual bool getExamInfo(::std::string examID, ::std::string& examInfo, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getExamInfo(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to execStep.
     */
    struct ExecStepResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool execStep(::std::string appNodeID, ::std::string stepID, StringMap params, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_execStep(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deleteExam.
     */
    struct DeleteExamResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool deleteExam(::std::string examID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteExam(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to finishExam.
     */
    struct FinishExamResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool finishExam(::std::string examID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_finishExam(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPExamManagerPrx : public virtual ::Ice::Proxy<ZGSPExamManagerPrx, ZGServerBasePrx>
{
public:

    bool createExam(const ::std::string& paramExamID, ::std::string& examID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPExamManager::CreateExamResult>(true, this, &ZGSPExamManagerPrx::_iceI_createExam, paramExamID, context).get();
        examID = ::std::move(_result.examID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto createExamAsync(const ::std::string& paramExamID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPExamManager::CreateExamResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPExamManager::CreateExamResult, P>(false, this, &ZGSPExamManagerPrx::_iceI_createExam, paramExamID, context);
    }

    ::std::function<void()>
    createExamAsync(const ::std::string& paramExamID,
                    ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPExamManager::CreateExamResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.examID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPExamManager::CreateExamResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPExamManagerPrx::_iceI_createExam, paramExamID, context);
    }

    /// \cond INTERNAL
    void _iceI_createExam(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPExamManager::CreateExamResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getExamInfo(const ::std::string& examID, ::std::string& examInfo, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPExamManager::GetExamInfoResult>(true, this, &ZGSPExamManagerPrx::_iceI_getExamInfo, examID, context).get();
        examInfo = ::std::move(_result.examInfo);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getExamInfoAsync(const ::std::string& examID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPExamManager::GetExamInfoResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPExamManager::GetExamInfoResult, P>(false, this, &ZGSPExamManagerPrx::_iceI_getExamInfo, examID, context);
    }

    ::std::function<void()>
    getExamInfoAsync(const ::std::string& examID,
                     ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPExamManager::GetExamInfoResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.examInfo), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPExamManager::GetExamInfoResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPExamManagerPrx::_iceI_getExamInfo, examID, context);
    }

    /// \cond INTERNAL
    void _iceI_getExamInfo(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPExamManager::GetExamInfoResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool execStep(const ::std::string& appNodeID, const ::std::string& stepID, const StringMap& params, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPExamManager::ExecStepResult>(true, this, &ZGSPExamManagerPrx::_iceI_execStep, appNodeID, stepID, params, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto execStepAsync(const ::std::string& appNodeID, const ::std::string& stepID, const StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPExamManager::ExecStepResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPExamManager::ExecStepResult, P>(false, this, &ZGSPExamManagerPrx::_iceI_execStep, appNodeID, stepID, params, context);
    }

    ::std::function<void()>
    execStepAsync(const ::std::string& appNodeID, const ::std::string& stepID, const StringMap& params,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPExamManager::ExecStepResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPExamManager::ExecStepResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPExamManagerPrx::_iceI_execStep, appNodeID, stepID, params, context);
    }

    /// \cond INTERNAL
    void _iceI_execStep(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPExamManager::ExecStepResult>>&, const ::std::string&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool deleteExam(const ::std::string& examID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPExamManager::DeleteExamResult>(true, this, &ZGSPExamManagerPrx::_iceI_deleteExam, examID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto deleteExamAsync(const ::std::string& examID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPExamManager::DeleteExamResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPExamManager::DeleteExamResult, P>(false, this, &ZGSPExamManagerPrx::_iceI_deleteExam, examID, context);
    }

    ::std::function<void()>
    deleteExamAsync(const ::std::string& examID,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPExamManager::DeleteExamResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPExamManager::DeleteExamResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPExamManagerPrx::_iceI_deleteExam, examID, context);
    }

    /// \cond INTERNAL
    void _iceI_deleteExam(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPExamManager::DeleteExamResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool finishExam(const ::std::string& examID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPExamManager::FinishExamResult>(true, this, &ZGSPExamManagerPrx::_iceI_finishExam, examID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto finishExamAsync(const ::std::string& examID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPExamManager::FinishExamResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPExamManager::FinishExamResult, P>(false, this, &ZGSPExamManagerPrx::_iceI_finishExam, examID, context);
    }

    ::std::function<void()>
    finishExamAsync(const ::std::string& examID,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPExamManager::FinishExamResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPExamManager::FinishExamResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPExamManagerPrx::_iceI_finishExam, examID, context);
    }

    /// \cond INTERNAL
    void _iceI_finishExam(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPExamManager::FinishExamResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPExamManagerPrx() = default;
    friend ::std::shared_ptr<ZGSPExamManagerPrx> IceInternal::createProxy<ZGSPExamManagerPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPExamManagerPtr = ::std::shared_ptr<ZGSPExamManager>;
using ZGSPExamManagerPrxPtr = ::std::shared_ptr<ZGSPExamManagerPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPExamManager;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPExamManager>&);
::IceProxy::Ice::Object* upCast(ZGSPExamManager*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPExamManager;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPExamManager*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPExamManager> ZGSPExamManagerPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPExamManager> ZGSPExamManagerPrx;
typedef ZGSPExamManagerPrx ZGSPExamManagerPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPExamManagerPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_createExam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_createExam.
 */
class Callback_ZGSPExamManager_createExam_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPExamManager_createExam_Base> Callback_ZGSPExamManager_createExamPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_getExamInfo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_getExamInfo.
 */
class Callback_ZGSPExamManager_getExamInfo_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPExamManager_getExamInfo_Base> Callback_ZGSPExamManager_getExamInfoPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_execStep.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_execStep.
 */
class Callback_ZGSPExamManager_execStep_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPExamManager_execStep_Base> Callback_ZGSPExamManager_execStepPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_deleteExam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_deleteExam.
 */
class Callback_ZGSPExamManager_deleteExam_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPExamManager_deleteExam_Base> Callback_ZGSPExamManager_deleteExamPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_finishExam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_finishExam.
 */
class Callback_ZGSPExamManager_finishExam_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPExamManager_finishExam_Base> Callback_ZGSPExamManager_finishExamPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPExamManager : public virtual ::Ice::Proxy<ZGSPExamManager, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool createExam(const ::std::string& paramExamID, ::std::string& examID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createExam(examID, e, _iceI_begin_createExam(paramExamID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_createExam(const ::std::string& paramExamID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createExam(paramExamID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_createExam(const ::std::string& paramExamID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createExam(paramExamID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createExam(const ::std::string& paramExamID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createExam(paramExamID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createExam(const ::std::string& paramExamID, const ::ZG6000::Callback_ZGSPExamManager_createExamPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createExam(paramExamID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createExam(const ::std::string& paramExamID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPExamManager_createExamPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createExam(paramExamID, context, cb, cookie);
    }

    bool end_createExam(::std::string& examID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createExam(::std::string& iceP_examID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createExam(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getExamInfo(const ::std::string& examID, ::std::string& examInfo, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getExamInfo(examInfo, e, _iceI_begin_getExamInfo(examID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getExamInfo(const ::std::string& examID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getExamInfo(examID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getExamInfo(const ::std::string& examID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getExamInfo(examID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getExamInfo(const ::std::string& examID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getExamInfo(examID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getExamInfo(const ::std::string& examID, const ::ZG6000::Callback_ZGSPExamManager_getExamInfoPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getExamInfo(examID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getExamInfo(const ::std::string& examID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPExamManager_getExamInfoPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getExamInfo(examID, context, cb, cookie);
    }

    bool end_getExamInfo(::std::string& examInfo, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getExamInfo(::std::string& iceP_examInfo, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getExamInfo(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool execStep(const ::std::string& appNodeID, const ::std::string& stepID, const ::ZG6000::StringMap& params, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_execStep(e, _iceI_begin_execStep(appNodeID, stepID, params, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_execStep(const ::std::string& appNodeID, const ::std::string& stepID, const ::ZG6000::StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_execStep(appNodeID, stepID, params, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_execStep(const ::std::string& appNodeID, const ::std::string& stepID, const ::ZG6000::StringMap& params, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_execStep(appNodeID, stepID, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_execStep(const ::std::string& appNodeID, const ::std::string& stepID, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_execStep(appNodeID, stepID, params, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_execStep(const ::std::string& appNodeID, const ::std::string& stepID, const ::ZG6000::StringMap& params, const ::ZG6000::Callback_ZGSPExamManager_execStepPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_execStep(appNodeID, stepID, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_execStep(const ::std::string& appNodeID, const ::std::string& stepID, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPExamManager_execStepPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_execStep(appNodeID, stepID, params, context, cb, cookie);
    }

    bool end_execStep(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_execStep(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_execStep(const ::std::string&, const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool deleteExam(const ::std::string& examID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deleteExam(e, _iceI_begin_deleteExam(examID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_deleteExam(const ::std::string& examID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deleteExam(examID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_deleteExam(const ::std::string& examID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteExam(examID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteExam(const ::std::string& examID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteExam(examID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteExam(const ::std::string& examID, const ::ZG6000::Callback_ZGSPExamManager_deleteExamPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteExam(examID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteExam(const ::std::string& examID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPExamManager_deleteExamPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteExam(examID, context, cb, cookie);
    }

    bool end_deleteExam(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deleteExam(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deleteExam(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool finishExam(const ::std::string& examID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_finishExam(e, _iceI_begin_finishExam(examID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_finishExam(const ::std::string& examID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_finishExam(examID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_finishExam(const ::std::string& examID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_finishExam(examID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_finishExam(const ::std::string& examID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_finishExam(examID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_finishExam(const ::std::string& examID, const ::ZG6000::Callback_ZGSPExamManager_finishExamPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_finishExam(examID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_finishExam(const ::std::string& examID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPExamManager_finishExamPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_finishExam(examID, context, cb, cookie);
    }

    bool end_finishExam(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_finishExam(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_finishExam(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPExamManager : virtual public ZGServerBase
{
public:

    typedef ZGSPExamManagerPrx ProxyType;
    typedef ZGSPExamManagerPtr PointerType;

    virtual ~ZGSPExamManager();

#ifdef ICE_CPP11_COMPILER
    ZGSPExamManager() = default;
    ZGSPExamManager(const ZGSPExamManager&) = default;
    ZGSPExamManager& operator=(const ZGSPExamManager&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool createExam(const ::std::string& paramExamID, ::std::string& examID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createExam(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getExamInfo(const ::std::string& examID, ::std::string& examInfo, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getExamInfo(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool execStep(const ::std::string& appNodeID, const ::std::string& stepID, const StringMap& params, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_execStep(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool deleteExam(const ::std::string& examID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteExam(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool finishExam(const ::std::string& examID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_finishExam(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPExamManager& lhs, const ZGSPExamManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPExamManager& lhs, const ZGSPExamManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_createExam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_createExam.
 */
template<class T>
class CallbackNC_ZGSPExamManager_createExam : public Callback_ZGSPExamManager_createExam_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPExamManager_createExam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPExamManagerPrx proxy = ZGSPExamManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_examID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createExam(iceP_examID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_examID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_createExam.
 */
template<class T> Callback_ZGSPExamManager_createExamPtr
newCallback_ZGSPExamManager_createExam(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPExamManager_createExam<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_createExam.
 */
template<class T> Callback_ZGSPExamManager_createExamPtr
newCallback_ZGSPExamManager_createExam(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPExamManager_createExam<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_createExam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_createExam.
 */
template<class T, typename CT>
class Callback_ZGSPExamManager_createExam : public Callback_ZGSPExamManager_createExam_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPExamManager_createExam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPExamManagerPrx proxy = ZGSPExamManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_examID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createExam(iceP_examID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_examID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_createExam.
 */
template<class T, typename CT> Callback_ZGSPExamManager_createExamPtr
newCallback_ZGSPExamManager_createExam(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPExamManager_createExam<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_createExam.
 */
template<class T, typename CT> Callback_ZGSPExamManager_createExamPtr
newCallback_ZGSPExamManager_createExam(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPExamManager_createExam<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_getExamInfo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_getExamInfo.
 */
template<class T>
class CallbackNC_ZGSPExamManager_getExamInfo : public Callback_ZGSPExamManager_getExamInfo_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPExamManager_getExamInfo(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPExamManagerPrx proxy = ZGSPExamManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_examInfo;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getExamInfo(iceP_examInfo, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_examInfo, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_getExamInfo.
 */
template<class T> Callback_ZGSPExamManager_getExamInfoPtr
newCallback_ZGSPExamManager_getExamInfo(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPExamManager_getExamInfo<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_getExamInfo.
 */
template<class T> Callback_ZGSPExamManager_getExamInfoPtr
newCallback_ZGSPExamManager_getExamInfo(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPExamManager_getExamInfo<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_getExamInfo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_getExamInfo.
 */
template<class T, typename CT>
class Callback_ZGSPExamManager_getExamInfo : public Callback_ZGSPExamManager_getExamInfo_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPExamManager_getExamInfo(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPExamManagerPrx proxy = ZGSPExamManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_examInfo;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getExamInfo(iceP_examInfo, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_examInfo, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_getExamInfo.
 */
template<class T, typename CT> Callback_ZGSPExamManager_getExamInfoPtr
newCallback_ZGSPExamManager_getExamInfo(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPExamManager_getExamInfo<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_getExamInfo.
 */
template<class T, typename CT> Callback_ZGSPExamManager_getExamInfoPtr
newCallback_ZGSPExamManager_getExamInfo(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPExamManager_getExamInfo<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_execStep.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_execStep.
 */
template<class T>
class CallbackNC_ZGSPExamManager_execStep : public Callback_ZGSPExamManager_execStep_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPExamManager_execStep(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPExamManagerPrx proxy = ZGSPExamManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_execStep(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_execStep.
 */
template<class T> Callback_ZGSPExamManager_execStepPtr
newCallback_ZGSPExamManager_execStep(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPExamManager_execStep<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_execStep.
 */
template<class T> Callback_ZGSPExamManager_execStepPtr
newCallback_ZGSPExamManager_execStep(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPExamManager_execStep<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_execStep.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_execStep.
 */
template<class T, typename CT>
class Callback_ZGSPExamManager_execStep : public Callback_ZGSPExamManager_execStep_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPExamManager_execStep(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPExamManagerPrx proxy = ZGSPExamManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_execStep(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_execStep.
 */
template<class T, typename CT> Callback_ZGSPExamManager_execStepPtr
newCallback_ZGSPExamManager_execStep(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPExamManager_execStep<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_execStep.
 */
template<class T, typename CT> Callback_ZGSPExamManager_execStepPtr
newCallback_ZGSPExamManager_execStep(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPExamManager_execStep<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_deleteExam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_deleteExam.
 */
template<class T>
class CallbackNC_ZGSPExamManager_deleteExam : public Callback_ZGSPExamManager_deleteExam_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPExamManager_deleteExam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPExamManagerPrx proxy = ZGSPExamManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteExam(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_deleteExam.
 */
template<class T> Callback_ZGSPExamManager_deleteExamPtr
newCallback_ZGSPExamManager_deleteExam(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPExamManager_deleteExam<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_deleteExam.
 */
template<class T> Callback_ZGSPExamManager_deleteExamPtr
newCallback_ZGSPExamManager_deleteExam(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPExamManager_deleteExam<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_deleteExam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_deleteExam.
 */
template<class T, typename CT>
class Callback_ZGSPExamManager_deleteExam : public Callback_ZGSPExamManager_deleteExam_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPExamManager_deleteExam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPExamManagerPrx proxy = ZGSPExamManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteExam(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_deleteExam.
 */
template<class T, typename CT> Callback_ZGSPExamManager_deleteExamPtr
newCallback_ZGSPExamManager_deleteExam(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPExamManager_deleteExam<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_deleteExam.
 */
template<class T, typename CT> Callback_ZGSPExamManager_deleteExamPtr
newCallback_ZGSPExamManager_deleteExam(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPExamManager_deleteExam<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_finishExam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_finishExam.
 */
template<class T>
class CallbackNC_ZGSPExamManager_finishExam : public Callback_ZGSPExamManager_finishExam_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPExamManager_finishExam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPExamManagerPrx proxy = ZGSPExamManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_finishExam(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_finishExam.
 */
template<class T> Callback_ZGSPExamManager_finishExamPtr
newCallback_ZGSPExamManager_finishExam(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPExamManager_finishExam<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_finishExam.
 */
template<class T> Callback_ZGSPExamManager_finishExamPtr
newCallback_ZGSPExamManager_finishExam(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPExamManager_finishExam<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPExamManager::begin_finishExam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPExamManager_finishExam.
 */
template<class T, typename CT>
class Callback_ZGSPExamManager_finishExam : public Callback_ZGSPExamManager_finishExam_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPExamManager_finishExam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPExamManagerPrx proxy = ZGSPExamManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_finishExam(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_finishExam.
 */
template<class T, typename CT> Callback_ZGSPExamManager_finishExamPtr
newCallback_ZGSPExamManager_finishExam(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPExamManager_finishExam<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPExamManager::begin_finishExam.
 */
template<class T, typename CT> Callback_ZGSPExamManager_finishExamPtr
newCallback_ZGSPExamManager_finishExam(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPExamManager_finishExam<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPTaskIU.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGOPTaskIU.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPTaskIU_ids[4] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPTaskBase",
    "::ZG6000::ZGOPTaskIU",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGOPTaskIU_ops[] =
{
    "abolishTask",
    "checkState",
    "confirmTask",
    "createTemplateTask",
    "createTemporaryTask",
    "deleteTask",
    "dispatchData",
    "downloadTask",
    "exitApp",
    "getTaskItems",
    "getTaskList",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "pauseTask",
    "resumeDebug",
    "resumeTask",
    "retryTask",
    "startDebug",
    "startTask",
    "stopDebug",
    "test",
    "updateItem",
    "updateTask"
};
const ::std::string iceC_ZG6000_ZGOPTaskIU_getTaskItems_name = "getTaskItems";
const ::std::string iceC_ZG6000_ZGOPTaskIU_createTemplateTask_name = "createTemplateTask";
const ::std::string iceC_ZG6000_ZGOPTaskIU_createTemporaryTask_name = "createTemporaryTask";
const ::std::string iceC_ZG6000_ZGOPTaskIU_downloadTask_name = "downloadTask";
const ::std::string iceC_ZG6000_ZGOPTaskIU_updateTask_name = "updateTask";
const ::std::string iceC_ZG6000_ZGOPTaskIU_updateItem_name = "updateItem";

}

bool
ZG6000::ZGOPTaskIU::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPTaskIU_ids, iceC_ZG6000_ZGOPTaskIU_ids + 4, s);
}

::std::vector<::std::string>
ZG6000::ZGOPTaskIU::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGOPTaskIU_ids[0], &iceC_ZG6000_ZGOPTaskIU_ids[4]);
}

::std::string
ZG6000::ZGOPTaskIU::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPTaskIU::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGOPTaskIU";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_getTaskItems(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    StringMap iceP_task;
    ListStringMap iceP_items;
    ErrorInfo iceP_e;
    bool ret = this->getTaskItems(::std::move(iceP_taskID), iceP_task, iceP_items, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_task, iceP_items, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_createTemplateTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_templateID;
    StringList iceP_listAppNodeID;
    StringMap iceP_param;
    istr->readAll(iceP_templateID, iceP_listAppNodeID, iceP_param);
    inS.endReadParams();
    StringList iceP_listTaskID;
    ErrorInfo iceP_e;
    bool ret = this->createTemplateTask(::std::move(iceP_templateID), ::std::move(iceP_listAppNodeID), ::std::move(iceP_param), iceP_listTaskID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listTaskID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_createTemporaryTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listUnlockTemplate;
    StringMap iceP_param;
    istr->readAll(iceP_listUnlockTemplate, iceP_param);
    inS.endReadParams();
    ::std::string iceP_taskID;
    ErrorInfo iceP_e;
    bool ret = this->createTemporaryTask(::std::move(iceP_listUnlockTemplate), ::std::move(iceP_param), iceP_taskID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_taskID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_downloadTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringList iceP_listTaskID;
    istr->readAll(iceP_listTaskID);
    inS.endReadParams();
    ListStringMap iceP_listTask;
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->downloadTask(::std::move(iceP_listTaskID), iceP_listTask, iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listTask, iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_updateTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listTask;
    istr->readAll(iceP_listTask);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateTask(::std::move(iceP_listTask), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_updateItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->readAll(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateItem(::std::move(iceP_listItem), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPTaskIU_ops, iceC_ZG6000_ZGOPTaskIU_ops + 29, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPTaskIU_ops)
    {
        case 0:
        {
            return _iceD_abolishTask(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_confirmTask(in, current);
        }
        case 3:
        {
            return _iceD_createTemplateTask(in, current);
        }
        case 4:
        {
            return _iceD_createTemporaryTask(in, current);
        }
        case 5:
        {
            return _iceD_deleteTask(in, current);
        }
        case 6:
        {
            return _iceD_dispatchData(in, current);
        }
        case 7:
        {
            return _iceD_downloadTask(in, current);
        }
        case 8:
        {
            return _iceD_exitApp(in, current);
        }
        case 9:
        {
            return _iceD_getTaskItems(in, current);
        }
        case 10:
        {
            return _iceD_getTaskList(in, current);
        }
        case 11:
        {
            return _iceD_getVersion(in, current);
        }
        case 12:
        {
            return _iceD_heartDebug(in, current);
        }
        case 13:
        {
            return _iceD_ice_id(in, current);
        }
        case 14:
        {
            return _iceD_ice_ids(in, current);
        }
        case 15:
        {
            return _iceD_ice_isA(in, current);
        }
        case 16:
        {
            return _iceD_ice_ping(in, current);
        }
        case 17:
        {
            return _iceD_isDebugging(in, current);
        }
        case 18:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 19:
        {
            return _iceD_pauseTask(in, current);
        }
        case 20:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 21:
        {
            return _iceD_resumeTask(in, current);
        }
        case 22:
        {
            return _iceD_retryTask(in, current);
        }
        case 23:
        {
            return _iceD_startDebug(in, current);
        }
        case 24:
        {
            return _iceD_startTask(in, current);
        }
        case 25:
        {
            return _iceD_stopDebug(in, current);
        }
        case 26:
        {
            return _iceD_test(in, current);
        }
        case 27:
        {
            return _iceD_updateItem(in, current);
        }
        case 28:
        {
            return _iceD_updateTask(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskIUPrx::_iceI_getTaskItems(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::GetTaskItemsResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_getTaskItems_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIU_getTaskItems_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIU::GetTaskItemsResult v;
            istr->readAll(v.task, v.items, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskIUPrx::_iceI_createTemplateTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::CreateTemplateTaskResult>>& outAsync, const ::std::string& iceP_templateID, const StringList& iceP_listAppNodeID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_createTemplateTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIU_createTemplateTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_templateID, iceP_listAppNodeID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIU::CreateTemplateTaskResult v;
            istr->readAll(v.listTaskID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskIUPrx::_iceI_createTemporaryTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::CreateTemporaryTaskResult>>& outAsync, const ListStringMap& iceP_listUnlockTemplate, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_createTemporaryTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIU_createTemporaryTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listUnlockTemplate, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIU::CreateTemporaryTaskResult v;
            istr->readAll(v.taskID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskIUPrx::_iceI_downloadTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::DownloadTaskResult>>& outAsync, const StringList& iceP_listTaskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_downloadTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIU_downloadTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listTaskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIU::DownloadTaskResult v;
            istr->readAll(v.listTask, v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskIUPrx::_iceI_updateTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::UpdateTaskResult>>& outAsync, const ListStringMap& iceP_listTask, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_updateTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIU_updateTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listTask);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIU::UpdateTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskIUPrx::_iceI_updateItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIU::UpdateItemResult>>& outAsync, const ListStringMap& iceP_listItem, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_updateItem_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskIU_updateItem_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listItem);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskIU::UpdateItemResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGOPTaskIUPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGOPTaskIUPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGOPTaskIUPrx::ice_staticId()
{
    return ZGOPTaskIU::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPTaskIU_getTaskItems_name = "getTaskItems";

const ::std::string iceC_ZG6000_ZGOPTaskIU_createTemplateTask_name = "createTemplateTask";

const ::std::string iceC_ZG6000_ZGOPTaskIU_createTemporaryTask_name = "createTemporaryTask";

const ::std::string iceC_ZG6000_ZGOPTaskIU_downloadTask_name = "downloadTask";

const ::std::string iceC_ZG6000_ZGOPTaskIU_updateTask_name = "updateTask";

const ::std::string iceC_ZG6000_ZGOPTaskIU_updateItem_name = "updateItem";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGOPTaskIU* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGOPTaskIU>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGOPTaskIU;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIU::_iceI_begin_getTaskItems(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_getTaskItems_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIU_getTaskItems_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIU_getTaskItems_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIU_getTaskItems_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIU::end_getTaskItems(::ZG6000::StringMap& iceP_task, ::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_getTaskItems_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_task);
    istr->read(iceP_items);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIU::_iceI_end_getTaskItems(::ZG6000::StringMap& iceP_task, ::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_getTaskItems_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_task);
    istr->read(iceP_items);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIU::_iceI_begin_createTemplateTask(const ::std::string& iceP_templateID, const ::ZG6000::StringList& iceP_listAppNodeID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_createTemplateTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIU_createTemplateTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIU_createTemplateTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_templateID);
        ostr->write(iceP_listAppNodeID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIU_createTemplateTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIU::end_createTemplateTask(::ZG6000::StringList& iceP_listTaskID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_createTemplateTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTaskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIU::_iceI_end_createTemplateTask(::ZG6000::StringList& iceP_listTaskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_createTemplateTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTaskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIU::_iceI_begin_createTemporaryTask(const ::ZG6000::ListStringMap& iceP_listUnlockTemplate, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_createTemporaryTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIU_createTemporaryTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIU_createTemporaryTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listUnlockTemplate);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIU_createTemporaryTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIU::end_createTemporaryTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_createTemporaryTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIU::_iceI_end_createTemporaryTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_createTemporaryTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIU::_iceI_begin_downloadTask(const ::ZG6000::StringList& iceP_listTaskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_downloadTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIU_downloadTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIU_downloadTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listTaskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIU_downloadTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIU::end_downloadTask(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_downloadTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTask);
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIU::_iceI_end_downloadTask(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_downloadTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTask);
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIU::_iceI_begin_updateTask(const ::ZG6000::ListStringMap& iceP_listTask, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_updateTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIU_updateTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIU_updateTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listTask);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIU_updateTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIU::end_updateTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_updateTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIU::_iceI_end_updateTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_updateTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskIU::_iceI_begin_updateItem(const ::ZG6000::ListStringMap& iceP_listItem, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskIU_updateItem_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskIU_updateItem_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskIU_updateItem_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listItem);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskIU_updateItem_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskIU::end_updateItem(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_updateItem_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskIU::_iceI_end_updateItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskIU_updateItem_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGOPTaskIU::_newInstance() const
{
    return new ZGOPTaskIU;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGOPTaskIU::ice_staticId()
{
    return ::ZG6000::ZGOPTaskIU::ice_staticId();
}

ZG6000::ZGOPTaskIU::~ZGOPTaskIU()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGOPTaskIU* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPTaskIU_ids[4] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPTaskBase",
    "::ZG6000::ZGOPTaskIU",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGOPTaskIU::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPTaskIU_ids, iceC_ZG6000_ZGOPTaskIU_ids + 4, s);
}

::std::vector< ::std::string>
ZG6000::ZGOPTaskIU::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGOPTaskIU_ids[0], &iceC_ZG6000_ZGOPTaskIU_ids[4]);
}

const ::std::string&
ZG6000::ZGOPTaskIU::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPTaskIU::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGOPTaskIU";
    return typeId;
#else
    return iceC_ZG6000_ZGOPTaskIU_ids[2];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_getTaskItems(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    StringMap iceP_task;
    ListStringMap iceP_items;
    ErrorInfo iceP_e;
    bool ret = this->getTaskItems(iceP_taskID, iceP_task, iceP_items, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_task);
    ostr->write(iceP_items);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_createTemplateTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_templateID;
    StringList iceP_listAppNodeID;
    StringMap iceP_param;
    istr->read(iceP_templateID);
    istr->read(iceP_listAppNodeID);
    istr->read(iceP_param);
    inS.endReadParams();
    StringList iceP_listTaskID;
    ErrorInfo iceP_e;
    bool ret = this->createTemplateTask(iceP_templateID, iceP_listAppNodeID, iceP_param, iceP_listTaskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listTaskID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_createTemporaryTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listUnlockTemplate;
    StringMap iceP_param;
    istr->read(iceP_listUnlockTemplate);
    istr->read(iceP_param);
    inS.endReadParams();
    ::std::string iceP_taskID;
    ErrorInfo iceP_e;
    bool ret = this->createTemporaryTask(iceP_listUnlockTemplate, iceP_param, iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_taskID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_downloadTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringList iceP_listTaskID;
    istr->read(iceP_listTaskID);
    inS.endReadParams();
    ListStringMap iceP_listTask;
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->downloadTask(iceP_listTaskID, iceP_listTask, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listTask);
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_updateTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listTask;
    istr->read(iceP_listTask);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateTask(iceP_listTask, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceD_updateItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->read(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateItem(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPTaskIU_all[] =
{
    "abolishTask",
    "checkState",
    "confirmTask",
    "createTemplateTask",
    "createTemporaryTask",
    "deleteTask",
    "dispatchData",
    "downloadTask",
    "exitApp",
    "getTaskItems",
    "getTaskList",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "pauseTask",
    "resumeDebug",
    "resumeTask",
    "retryTask",
    "startDebug",
    "startTask",
    "stopDebug",
    "test",
    "updateItem",
    "updateTask"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskIU::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPTaskIU_all, iceC_ZG6000_ZGOPTaskIU_all + 29, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPTaskIU_all)
    {
        case 0:
        {
            return _iceD_abolishTask(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_confirmTask(in, current);
        }
        case 3:
        {
            return _iceD_createTemplateTask(in, current);
        }
        case 4:
        {
            return _iceD_createTemporaryTask(in, current);
        }
        case 5:
        {
            return _iceD_deleteTask(in, current);
        }
        case 6:
        {
            return _iceD_dispatchData(in, current);
        }
        case 7:
        {
            return _iceD_downloadTask(in, current);
        }
        case 8:
        {
            return _iceD_exitApp(in, current);
        }
        case 9:
        {
            return _iceD_getTaskItems(in, current);
        }
        case 10:
        {
            return _iceD_getTaskList(in, current);
        }
        case 11:
        {
            return _iceD_getVersion(in, current);
        }
        case 12:
        {
            return _iceD_heartDebug(in, current);
        }
        case 13:
        {
            return _iceD_ice_id(in, current);
        }
        case 14:
        {
            return _iceD_ice_ids(in, current);
        }
        case 15:
        {
            return _iceD_ice_isA(in, current);
        }
        case 16:
        {
            return _iceD_ice_ping(in, current);
        }
        case 17:
        {
            return _iceD_isDebugging(in, current);
        }
        case 18:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 19:
        {
            return _iceD_pauseTask(in, current);
        }
        case 20:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 21:
        {
            return _iceD_resumeTask(in, current);
        }
        case 22:
        {
            return _iceD_retryTask(in, current);
        }
        case 23:
        {
            return _iceD_startDebug(in, current);
        }
        case 24:
        {
            return _iceD_startTask(in, current);
        }
        case 25:
        {
            return _iceD_stopDebug(in, current);
        }
        case 26:
        {
            return _iceD_test(in, current);
        }
        case 27:
        {
            return _iceD_updateItem(in, current);
        }
        case 28:
        {
            return _iceD_updateTask(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGOPTaskIU::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGOPTaskIU, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGOPTaskIU::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGOPTaskIU, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGOPTaskIUPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGOPTaskIUPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGOPTaskIU::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

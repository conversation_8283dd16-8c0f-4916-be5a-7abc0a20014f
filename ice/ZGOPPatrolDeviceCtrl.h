//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPPatrolDeviceCtrl.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGOPPatrolDeviceCtrl_h__
#define __ZGOPPatrolDeviceCtrl_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGOPPatrolDeviceCtrl;
class ZGOPPatrolDeviceCtrlPrx;

}

namespace ZG6000
{

class ZGOPPatrolDeviceCtrl : public virtual ZGServerBase
{
public:

    using ProxyType = ZGOPPatrolDeviceCtrlPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to presetPointCtrl.
     */
    struct PresetPointCtrlResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool presetPointCtrl(::std::string devicePresetID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_presetPointCtrl(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to devicePresetCtrl.
     */
    struct DevicePresetCtrlResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool devicePresetCtrl(::std::string deviceID, ::std::string presetNo, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_devicePresetCtrl(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deviceYk.
     */
    struct DeviceYkResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool deviceYk(::std::string deviceID, ::std::string propertyName, ::std::string propertyValue, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deviceYk(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deviceYs.
     */
    struct DeviceYsResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool deviceYs(::std::string deviceID, ::std::string propertyName, ::std::string propertyValue, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deviceYs(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to captureImage.
     */
    struct CaptureImageResult
    {
        bool returnValue;
        ::std::string url;
        ErrorInfo e;
    };

    virtual bool captureImage(::std::string yvID, ::std::string& url, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_captureImage(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to recordAudio.
     */
    struct RecordAudioResult
    {
        bool returnValue;
        ::std::string url;
        ErrorInfo e;
    };

    virtual bool recordAudio(::std::string yvID, int duration, ::std::string& url, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_recordAudio(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to recordVideo.
     */
    struct RecordVideoResult
    {
        bool returnValue;
        ::std::string url;
        ErrorInfo e;
    };

    virtual bool recordVideo(::std::string yvID, int duration, ::std::string& url, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_recordVideo(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGOPPatrolDeviceCtrlPrx : public virtual ::Ice::Proxy<ZGOPPatrolDeviceCtrlPrx, ZGServerBasePrx>
{
public:

    bool presetPointCtrl(const ::std::string& devicePresetID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::PresetPointCtrlResult>(true, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_presetPointCtrl, devicePresetID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto presetPointCtrlAsync(const ::std::string& devicePresetID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPPatrolDeviceCtrl::PresetPointCtrlResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::PresetPointCtrlResult, P>(false, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_presetPointCtrl, devicePresetID, context);
    }

    ::std::function<void()>
    presetPointCtrlAsync(const ::std::string& devicePresetID,
                         ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPPatrolDeviceCtrl::PresetPointCtrlResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPPatrolDeviceCtrl::PresetPointCtrlResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_presetPointCtrl, devicePresetID, context);
    }

    /// \cond INTERNAL
    void _iceI_presetPointCtrl(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::PresetPointCtrlResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool devicePresetCtrl(const ::std::string& deviceID, const ::std::string& presetNo, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::DevicePresetCtrlResult>(true, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_devicePresetCtrl, deviceID, presetNo, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto devicePresetCtrlAsync(const ::std::string& deviceID, const ::std::string& presetNo, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPPatrolDeviceCtrl::DevicePresetCtrlResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::DevicePresetCtrlResult, P>(false, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_devicePresetCtrl, deviceID, presetNo, context);
    }

    ::std::function<void()>
    devicePresetCtrlAsync(const ::std::string& deviceID, const ::std::string& presetNo,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPPatrolDeviceCtrl::DevicePresetCtrlResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPPatrolDeviceCtrl::DevicePresetCtrlResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_devicePresetCtrl, deviceID, presetNo, context);
    }

    /// \cond INTERNAL
    void _iceI_devicePresetCtrl(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::DevicePresetCtrlResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool deviceYk(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::DeviceYkResult>(true, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_deviceYk, deviceID, propertyName, propertyValue, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto deviceYkAsync(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPPatrolDeviceCtrl::DeviceYkResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::DeviceYkResult, P>(false, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_deviceYk, deviceID, propertyName, propertyValue, context);
    }

    ::std::function<void()>
    deviceYkAsync(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPPatrolDeviceCtrl::DeviceYkResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPPatrolDeviceCtrl::DeviceYkResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_deviceYk, deviceID, propertyName, propertyValue, context);
    }

    /// \cond INTERNAL
    void _iceI_deviceYk(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::DeviceYkResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool deviceYs(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::DeviceYsResult>(true, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_deviceYs, deviceID, propertyName, propertyValue, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto deviceYsAsync(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPPatrolDeviceCtrl::DeviceYsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::DeviceYsResult, P>(false, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_deviceYs, deviceID, propertyName, propertyValue, context);
    }

    ::std::function<void()>
    deviceYsAsync(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPPatrolDeviceCtrl::DeviceYsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPPatrolDeviceCtrl::DeviceYsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_deviceYs, deviceID, propertyName, propertyValue, context);
    }

    /// \cond INTERNAL
    void _iceI_deviceYs(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::DeviceYsResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool captureImage(const ::std::string& yvID, ::std::string& url, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::CaptureImageResult>(true, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_captureImage, yvID, context).get();
        url = ::std::move(_result.url);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto captureImageAsync(const ::std::string& yvID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPPatrolDeviceCtrl::CaptureImageResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::CaptureImageResult, P>(false, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_captureImage, yvID, context);
    }

    ::std::function<void()>
    captureImageAsync(const ::std::string& yvID,
                      ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPPatrolDeviceCtrl::CaptureImageResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.url), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPPatrolDeviceCtrl::CaptureImageResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_captureImage, yvID, context);
    }

    /// \cond INTERNAL
    void _iceI_captureImage(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::CaptureImageResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool recordAudio(const ::std::string& yvID, int duration, ::std::string& url, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::RecordAudioResult>(true, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_recordAudio, yvID, duration, context).get();
        url = ::std::move(_result.url);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto recordAudioAsync(const ::std::string& yvID, int duration, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPPatrolDeviceCtrl::RecordAudioResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::RecordAudioResult, P>(false, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_recordAudio, yvID, duration, context);
    }

    ::std::function<void()>
    recordAudioAsync(const ::std::string& yvID, int duration,
                     ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPPatrolDeviceCtrl::RecordAudioResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.url), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPPatrolDeviceCtrl::RecordAudioResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_recordAudio, yvID, duration, context);
    }

    /// \cond INTERNAL
    void _iceI_recordAudio(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::RecordAudioResult>>&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    bool recordVideo(const ::std::string& yvID, int duration, ::std::string& url, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::RecordVideoResult>(true, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_recordVideo, yvID, duration, context).get();
        url = ::std::move(_result.url);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto recordVideoAsync(const ::std::string& yvID, int duration, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPPatrolDeviceCtrl::RecordVideoResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPPatrolDeviceCtrl::RecordVideoResult, P>(false, this, &ZGOPPatrolDeviceCtrlPrx::_iceI_recordVideo, yvID, duration, context);
    }

    ::std::function<void()>
    recordVideoAsync(const ::std::string& yvID, int duration,
                     ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPPatrolDeviceCtrl::RecordVideoResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.url), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPPatrolDeviceCtrl::RecordVideoResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPPatrolDeviceCtrlPrx::_iceI_recordVideo, yvID, duration, context);
    }

    /// \cond INTERNAL
    void _iceI_recordVideo(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPPatrolDeviceCtrl::RecordVideoResult>>&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGOPPatrolDeviceCtrlPrx() = default;
    friend ::std::shared_ptr<ZGOPPatrolDeviceCtrlPrx> IceInternal::createProxy<ZGOPPatrolDeviceCtrlPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGOPPatrolDeviceCtrlPtr = ::std::shared_ptr<ZGOPPatrolDeviceCtrl>;
using ZGOPPatrolDeviceCtrlPrxPtr = ::std::shared_ptr<ZGOPPatrolDeviceCtrlPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGOPPatrolDeviceCtrl;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGOPPatrolDeviceCtrl>&);
::IceProxy::Ice::Object* upCast(ZGOPPatrolDeviceCtrl*);
/// \endcond

}

}

namespace ZG6000
{

class ZGOPPatrolDeviceCtrl;
/// \cond INTERNAL
::Ice::Object* upCast(ZGOPPatrolDeviceCtrl*);
/// \endcond
typedef ::IceInternal::Handle< ZGOPPatrolDeviceCtrl> ZGOPPatrolDeviceCtrlPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGOPPatrolDeviceCtrl> ZGOPPatrolDeviceCtrlPrx;
typedef ZGOPPatrolDeviceCtrlPrx ZGOPPatrolDeviceCtrlPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGOPPatrolDeviceCtrlPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_presetPointCtrl.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_presetPointCtrl.
 */
class Callback_ZGOPPatrolDeviceCtrl_presetPointCtrl_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPPatrolDeviceCtrl_presetPointCtrl_Base> Callback_ZGOPPatrolDeviceCtrl_presetPointCtrlPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_devicePresetCtrl.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_devicePresetCtrl.
 */
class Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrl_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrl_Base> Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrlPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYk.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_deviceYk.
 */
class Callback_ZGOPPatrolDeviceCtrl_deviceYk_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPPatrolDeviceCtrl_deviceYk_Base> Callback_ZGOPPatrolDeviceCtrl_deviceYkPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYs.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_deviceYs.
 */
class Callback_ZGOPPatrolDeviceCtrl_deviceYs_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPPatrolDeviceCtrl_deviceYs_Base> Callback_ZGOPPatrolDeviceCtrl_deviceYsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_captureImage.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_captureImage.
 */
class Callback_ZGOPPatrolDeviceCtrl_captureImage_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPPatrolDeviceCtrl_captureImage_Base> Callback_ZGOPPatrolDeviceCtrl_captureImagePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordAudio.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_recordAudio.
 */
class Callback_ZGOPPatrolDeviceCtrl_recordAudio_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPPatrolDeviceCtrl_recordAudio_Base> Callback_ZGOPPatrolDeviceCtrl_recordAudioPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordVideo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_recordVideo.
 */
class Callback_ZGOPPatrolDeviceCtrl_recordVideo_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPPatrolDeviceCtrl_recordVideo_Base> Callback_ZGOPPatrolDeviceCtrl_recordVideoPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGOPPatrolDeviceCtrl : public virtual ::Ice::Proxy<ZGOPPatrolDeviceCtrl, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool presetPointCtrl(const ::std::string& devicePresetID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_presetPointCtrl(e, _iceI_begin_presetPointCtrl(devicePresetID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_presetPointCtrl(const ::std::string& devicePresetID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_presetPointCtrl(devicePresetID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_presetPointCtrl(const ::std::string& devicePresetID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_presetPointCtrl(devicePresetID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_presetPointCtrl(const ::std::string& devicePresetID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_presetPointCtrl(devicePresetID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_presetPointCtrl(const ::std::string& devicePresetID, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_presetPointCtrlPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_presetPointCtrl(devicePresetID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_presetPointCtrl(const ::std::string& devicePresetID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_presetPointCtrlPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_presetPointCtrl(devicePresetID, context, cb, cookie);
    }

    bool end_presetPointCtrl(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_presetPointCtrl(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_presetPointCtrl(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool devicePresetCtrl(const ::std::string& deviceID, const ::std::string& presetNo, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_devicePresetCtrl(e, _iceI_begin_devicePresetCtrl(deviceID, presetNo, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_devicePresetCtrl(const ::std::string& deviceID, const ::std::string& presetNo, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_devicePresetCtrl(deviceID, presetNo, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_devicePresetCtrl(const ::std::string& deviceID, const ::std::string& presetNo, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_devicePresetCtrl(deviceID, presetNo, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_devicePresetCtrl(const ::std::string& deviceID, const ::std::string& presetNo, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_devicePresetCtrl(deviceID, presetNo, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_devicePresetCtrl(const ::std::string& deviceID, const ::std::string& presetNo, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrlPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_devicePresetCtrl(deviceID, presetNo, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_devicePresetCtrl(const ::std::string& deviceID, const ::std::string& presetNo, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrlPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_devicePresetCtrl(deviceID, presetNo, context, cb, cookie);
    }

    bool end_devicePresetCtrl(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_devicePresetCtrl(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_devicePresetCtrl(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool deviceYk(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deviceYk(e, _iceI_begin_deviceYk(deviceID, propertyName, propertyValue, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_deviceYk(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deviceYk(deviceID, propertyName, propertyValue, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_deviceYk(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deviceYk(deviceID, propertyName, propertyValue, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deviceYk(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deviceYk(deviceID, propertyName, propertyValue, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deviceYk(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_deviceYkPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deviceYk(deviceID, propertyName, propertyValue, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deviceYk(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_deviceYkPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deviceYk(deviceID, propertyName, propertyValue, context, cb, cookie);
    }

    bool end_deviceYk(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deviceYk(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deviceYk(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool deviceYs(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deviceYs(e, _iceI_begin_deviceYs(deviceID, propertyName, propertyValue, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_deviceYs(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deviceYs(deviceID, propertyName, propertyValue, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_deviceYs(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deviceYs(deviceID, propertyName, propertyValue, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deviceYs(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deviceYs(deviceID, propertyName, propertyValue, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deviceYs(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_deviceYsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deviceYs(deviceID, propertyName, propertyValue, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deviceYs(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_deviceYsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deviceYs(deviceID, propertyName, propertyValue, context, cb, cookie);
    }

    bool end_deviceYs(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deviceYs(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deviceYs(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool captureImage(const ::std::string& yvID, ::std::string& url, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_captureImage(url, e, _iceI_begin_captureImage(yvID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_captureImage(const ::std::string& yvID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_captureImage(yvID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_captureImage(const ::std::string& yvID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_captureImage(yvID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_captureImage(const ::std::string& yvID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_captureImage(yvID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_captureImage(const ::std::string& yvID, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_captureImagePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_captureImage(yvID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_captureImage(const ::std::string& yvID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_captureImagePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_captureImage(yvID, context, cb, cookie);
    }

    bool end_captureImage(::std::string& url, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_captureImage(::std::string& iceP_url, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_captureImage(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool recordAudio(const ::std::string& yvID, ::Ice::Int duration, ::std::string& url, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_recordAudio(url, e, _iceI_begin_recordAudio(yvID, duration, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_recordAudio(const ::std::string& yvID, ::Ice::Int duration, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_recordAudio(yvID, duration, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_recordAudio(const ::std::string& yvID, ::Ice::Int duration, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_recordAudio(yvID, duration, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_recordAudio(const ::std::string& yvID, ::Ice::Int duration, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_recordAudio(yvID, duration, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_recordAudio(const ::std::string& yvID, ::Ice::Int duration, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_recordAudioPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_recordAudio(yvID, duration, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_recordAudio(const ::std::string& yvID, ::Ice::Int duration, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_recordAudioPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_recordAudio(yvID, duration, context, cb, cookie);
    }

    bool end_recordAudio(::std::string& url, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_recordAudio(::std::string& iceP_url, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_recordAudio(const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool recordVideo(const ::std::string& yvID, ::Ice::Int duration, ::std::string& url, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_recordVideo(url, e, _iceI_begin_recordVideo(yvID, duration, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_recordVideo(const ::std::string& yvID, ::Ice::Int duration, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_recordVideo(yvID, duration, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_recordVideo(const ::std::string& yvID, ::Ice::Int duration, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_recordVideo(yvID, duration, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_recordVideo(const ::std::string& yvID, ::Ice::Int duration, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_recordVideo(yvID, duration, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_recordVideo(const ::std::string& yvID, ::Ice::Int duration, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_recordVideoPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_recordVideo(yvID, duration, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_recordVideo(const ::std::string& yvID, ::Ice::Int duration, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPPatrolDeviceCtrl_recordVideoPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_recordVideo(yvID, duration, context, cb, cookie);
    }

    bool end_recordVideo(::std::string& url, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_recordVideo(::std::string& iceP_url, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_recordVideo(const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGOPPatrolDeviceCtrl : virtual public ZGServerBase
{
public:

    typedef ZGOPPatrolDeviceCtrlPrx ProxyType;
    typedef ZGOPPatrolDeviceCtrlPtr PointerType;

    virtual ~ZGOPPatrolDeviceCtrl();

#ifdef ICE_CPP11_COMPILER
    ZGOPPatrolDeviceCtrl() = default;
    ZGOPPatrolDeviceCtrl(const ZGOPPatrolDeviceCtrl&) = default;
    ZGOPPatrolDeviceCtrl& operator=(const ZGOPPatrolDeviceCtrl&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool presetPointCtrl(const ::std::string& devicePresetID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_presetPointCtrl(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool devicePresetCtrl(const ::std::string& deviceID, const ::std::string& presetNo, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_devicePresetCtrl(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool deviceYk(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deviceYk(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool deviceYs(const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deviceYs(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool captureImage(const ::std::string& yvID, ::std::string& url, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_captureImage(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool recordAudio(const ::std::string& yvID, ::Ice::Int duration, ::std::string& url, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_recordAudio(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool recordVideo(const ::std::string& yvID, ::Ice::Int duration, ::std::string& url, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_recordVideo(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGOPPatrolDeviceCtrl& lhs, const ZGOPPatrolDeviceCtrl& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGOPPatrolDeviceCtrl& lhs, const ZGOPPatrolDeviceCtrl& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_presetPointCtrl.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_presetPointCtrl.
 */
template<class T>
class CallbackNC_ZGOPPatrolDeviceCtrl_presetPointCtrl : public Callback_ZGOPPatrolDeviceCtrl_presetPointCtrl_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPPatrolDeviceCtrl_presetPointCtrl(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_presetPointCtrl(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_presetPointCtrl.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_presetPointCtrlPtr
newCallback_ZGOPPatrolDeviceCtrl_presetPointCtrl(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_presetPointCtrl<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_presetPointCtrl.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_presetPointCtrlPtr
newCallback_ZGOPPatrolDeviceCtrl_presetPointCtrl(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_presetPointCtrl<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_presetPointCtrl.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_presetPointCtrl.
 */
template<class T, typename CT>
class Callback_ZGOPPatrolDeviceCtrl_presetPointCtrl : public Callback_ZGOPPatrolDeviceCtrl_presetPointCtrl_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPPatrolDeviceCtrl_presetPointCtrl(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_presetPointCtrl(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_presetPointCtrl.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_presetPointCtrlPtr
newCallback_ZGOPPatrolDeviceCtrl_presetPointCtrl(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_presetPointCtrl<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_presetPointCtrl.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_presetPointCtrlPtr
newCallback_ZGOPPatrolDeviceCtrl_presetPointCtrl(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_presetPointCtrl<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_devicePresetCtrl.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_devicePresetCtrl.
 */
template<class T>
class CallbackNC_ZGOPPatrolDeviceCtrl_devicePresetCtrl : public Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrl_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPPatrolDeviceCtrl_devicePresetCtrl(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_devicePresetCtrl(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_devicePresetCtrl.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrlPtr
newCallback_ZGOPPatrolDeviceCtrl_devicePresetCtrl(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_devicePresetCtrl<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_devicePresetCtrl.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrlPtr
newCallback_ZGOPPatrolDeviceCtrl_devicePresetCtrl(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_devicePresetCtrl<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_devicePresetCtrl.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_devicePresetCtrl.
 */
template<class T, typename CT>
class Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrl : public Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrl_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrl(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_devicePresetCtrl(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_devicePresetCtrl.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrlPtr
newCallback_ZGOPPatrolDeviceCtrl_devicePresetCtrl(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrl<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_devicePresetCtrl.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrlPtr
newCallback_ZGOPPatrolDeviceCtrl_devicePresetCtrl(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_devicePresetCtrl<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYk.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_deviceYk.
 */
template<class T>
class CallbackNC_ZGOPPatrolDeviceCtrl_deviceYk : public Callback_ZGOPPatrolDeviceCtrl_deviceYk_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPPatrolDeviceCtrl_deviceYk(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deviceYk(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYk.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_deviceYkPtr
newCallback_ZGOPPatrolDeviceCtrl_deviceYk(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_deviceYk<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYk.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_deviceYkPtr
newCallback_ZGOPPatrolDeviceCtrl_deviceYk(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_deviceYk<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYk.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_deviceYk.
 */
template<class T, typename CT>
class Callback_ZGOPPatrolDeviceCtrl_deviceYk : public Callback_ZGOPPatrolDeviceCtrl_deviceYk_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPPatrolDeviceCtrl_deviceYk(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deviceYk(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYk.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_deviceYkPtr
newCallback_ZGOPPatrolDeviceCtrl_deviceYk(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_deviceYk<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYk.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_deviceYkPtr
newCallback_ZGOPPatrolDeviceCtrl_deviceYk(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_deviceYk<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYs.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_deviceYs.
 */
template<class T>
class CallbackNC_ZGOPPatrolDeviceCtrl_deviceYs : public Callback_ZGOPPatrolDeviceCtrl_deviceYs_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPPatrolDeviceCtrl_deviceYs(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deviceYs(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYs.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_deviceYsPtr
newCallback_ZGOPPatrolDeviceCtrl_deviceYs(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_deviceYs<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYs.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_deviceYsPtr
newCallback_ZGOPPatrolDeviceCtrl_deviceYs(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_deviceYs<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYs.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_deviceYs.
 */
template<class T, typename CT>
class Callback_ZGOPPatrolDeviceCtrl_deviceYs : public Callback_ZGOPPatrolDeviceCtrl_deviceYs_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPPatrolDeviceCtrl_deviceYs(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deviceYs(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYs.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_deviceYsPtr
newCallback_ZGOPPatrolDeviceCtrl_deviceYs(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_deviceYs<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_deviceYs.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_deviceYsPtr
newCallback_ZGOPPatrolDeviceCtrl_deviceYs(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_deviceYs<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_captureImage.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_captureImage.
 */
template<class T>
class CallbackNC_ZGOPPatrolDeviceCtrl_captureImage : public Callback_ZGOPPatrolDeviceCtrl_captureImage_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGOPPatrolDeviceCtrl_captureImage(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_url;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_captureImage(iceP_url, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_url, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_captureImage.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_captureImagePtr
newCallback_ZGOPPatrolDeviceCtrl_captureImage(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_captureImage<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_captureImage.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_captureImagePtr
newCallback_ZGOPPatrolDeviceCtrl_captureImage(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_captureImage<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_captureImage.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_captureImage.
 */
template<class T, typename CT>
class Callback_ZGOPPatrolDeviceCtrl_captureImage : public Callback_ZGOPPatrolDeviceCtrl_captureImage_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGOPPatrolDeviceCtrl_captureImage(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_url;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_captureImage(iceP_url, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_url, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_captureImage.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_captureImagePtr
newCallback_ZGOPPatrolDeviceCtrl_captureImage(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_captureImage<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_captureImage.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_captureImagePtr
newCallback_ZGOPPatrolDeviceCtrl_captureImage(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_captureImage<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordAudio.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_recordAudio.
 */
template<class T>
class CallbackNC_ZGOPPatrolDeviceCtrl_recordAudio : public Callback_ZGOPPatrolDeviceCtrl_recordAudio_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGOPPatrolDeviceCtrl_recordAudio(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_url;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_recordAudio(iceP_url, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_url, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordAudio.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_recordAudioPtr
newCallback_ZGOPPatrolDeviceCtrl_recordAudio(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_recordAudio<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordAudio.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_recordAudioPtr
newCallback_ZGOPPatrolDeviceCtrl_recordAudio(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_recordAudio<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordAudio.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_recordAudio.
 */
template<class T, typename CT>
class Callback_ZGOPPatrolDeviceCtrl_recordAudio : public Callback_ZGOPPatrolDeviceCtrl_recordAudio_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGOPPatrolDeviceCtrl_recordAudio(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_url;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_recordAudio(iceP_url, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_url, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordAudio.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_recordAudioPtr
newCallback_ZGOPPatrolDeviceCtrl_recordAudio(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_recordAudio<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordAudio.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_recordAudioPtr
newCallback_ZGOPPatrolDeviceCtrl_recordAudio(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_recordAudio<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordVideo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_recordVideo.
 */
template<class T>
class CallbackNC_ZGOPPatrolDeviceCtrl_recordVideo : public Callback_ZGOPPatrolDeviceCtrl_recordVideo_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGOPPatrolDeviceCtrl_recordVideo(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_url;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_recordVideo(iceP_url, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_url, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordVideo.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_recordVideoPtr
newCallback_ZGOPPatrolDeviceCtrl_recordVideo(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_recordVideo<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordVideo.
 */
template<class T> Callback_ZGOPPatrolDeviceCtrl_recordVideoPtr
newCallback_ZGOPPatrolDeviceCtrl_recordVideo(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPPatrolDeviceCtrl_recordVideo<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordVideo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPPatrolDeviceCtrl_recordVideo.
 */
template<class T, typename CT>
class Callback_ZGOPPatrolDeviceCtrl_recordVideo : public Callback_ZGOPPatrolDeviceCtrl_recordVideo_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGOPPatrolDeviceCtrl_recordVideo(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPPatrolDeviceCtrlPrx proxy = ZGOPPatrolDeviceCtrlPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_url;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_recordVideo(iceP_url, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_url, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordVideo.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_recordVideoPtr
newCallback_ZGOPPatrolDeviceCtrl_recordVideo(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_recordVideo<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPPatrolDeviceCtrl::begin_recordVideo.
 */
template<class T, typename CT> Callback_ZGOPPatrolDeviceCtrl_recordVideoPtr
newCallback_ZGOPPatrolDeviceCtrl_recordVideo(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPPatrolDeviceCtrl_recordVideo<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

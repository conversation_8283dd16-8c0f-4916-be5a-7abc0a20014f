//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPHisDataManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPHisDataManager_h__
#define __ZGSPHisDataManager_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPHisDataManager;
class ZGSPHisDataManagerPrx;

}

namespace ZG6000
{

enum class StoreType : unsigned char
{
    stDay,
    stWeek,
    stHour,
    stMinute,
    stSecond
};

}

namespace ZG6000
{

class ZGSPHisDataManager : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPHisDataManagerPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to queryTableData.
     */
    struct QueryTableDataResult
    {
        bool returnValue;
        StringList listTitle;
        ListStringList listValues;
        ErrorInfo e;
    };

    virtual bool queryTableData(::std::string tableName, ::std::string condition, int offset, int limit, ::std::string orderField, ::std::string orderType, StringList& listTitle, ListStringList& listValues, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_queryTableData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to queryTableCount.
     */
    struct QueryTableCountResult
    {
        bool returnValue;
        int count;
        ErrorInfo e;
    };

    virtual bool queryTableCount(::std::string tableName, ::std::string condition, int& count, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_queryTableCount(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to queryStoreYc.
     */
    struct QueryStoreYcResult
    {
        bool returnValue;
        ListStringMap listResult;
        ErrorInfo e;
    };

    virtual bool queryStoreYc(StringList listDevice, StringList listProperty, ::std::string startTime, ::std::string endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_queryStoreYc(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to queryStoreYx.
     */
    struct QueryStoreYxResult
    {
        bool returnValue;
        ListStringMap listResult;
        ErrorInfo e;
    };

    virtual bool queryStoreYx(StringList listDevice, StringList listProperty, ::std::string startTime, ::std::string endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_queryStoreYx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to queryStoreText.
     */
    struct QueryStoreTextResult
    {
        bool returnValue;
        ListStringMap listResult;
        ErrorInfo e;
    };

    virtual bool queryStoreText(StringList listDevice, StringList listProperty, ::std::string startTime, ::std::string endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_queryStoreText(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to queryStoreYm.
     */
    struct QueryStoreYmResult
    {
        bool returnValue;
        ListStringMap listResult;
        ErrorInfo e;
    };

    virtual bool queryStoreYm(StringList listDevice, StringList listProperty, ::std::string startTime, ::std::string endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_queryStoreYm(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPHisDataManagerPrx : public virtual ::Ice::Proxy<ZGSPHisDataManagerPrx, ZGServerBasePrx>
{
public:

    bool queryTableData(const ::std::string& tableName, const ::std::string& condition, int offset, int limit, const ::std::string& orderField, const ::std::string& orderType, StringList& listTitle, ListStringList& listValues, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPHisDataManager::QueryTableDataResult>(true, this, &ZGSPHisDataManagerPrx::_iceI_queryTableData, tableName, condition, offset, limit, orderField, orderType, context).get();
        listTitle = ::std::move(_result.listTitle);
        listValues = ::std::move(_result.listValues);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto queryTableDataAsync(const ::std::string& tableName, const ::std::string& condition, int offset, int limit, const ::std::string& orderField, const ::std::string& orderType, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPHisDataManager::QueryTableDataResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPHisDataManager::QueryTableDataResult, P>(false, this, &ZGSPHisDataManagerPrx::_iceI_queryTableData, tableName, condition, offset, limit, orderField, orderType, context);
    }

    ::std::function<void()>
    queryTableDataAsync(const ::std::string& tableName, const ::std::string& condition, int offset, int limit, const ::std::string& orderField, const ::std::string& orderType,
                        ::std::function<void(bool, ::ZG6000::StringList, ::ZG6000::ListStringList, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPHisDataManager::QueryTableDataResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listTitle), ::std::move(_result.listValues), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPHisDataManager::QueryTableDataResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPHisDataManagerPrx::_iceI_queryTableData, tableName, condition, offset, limit, orderField, orderType, context);
    }

    /// \cond INTERNAL
    void _iceI_queryTableData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryTableDataResult>>&, const ::std::string&, const ::std::string&, int, int, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool queryTableCount(const ::std::string& tableName, const ::std::string& condition, int& count, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPHisDataManager::QueryTableCountResult>(true, this, &ZGSPHisDataManagerPrx::_iceI_queryTableCount, tableName, condition, context).get();
        count = _result.count;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto queryTableCountAsync(const ::std::string& tableName, const ::std::string& condition, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPHisDataManager::QueryTableCountResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPHisDataManager::QueryTableCountResult, P>(false, this, &ZGSPHisDataManagerPrx::_iceI_queryTableCount, tableName, condition, context);
    }

    ::std::function<void()>
    queryTableCountAsync(const ::std::string& tableName, const ::std::string& condition,
                         ::std::function<void(bool, int, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPHisDataManager::QueryTableCountResult&& _result)
        {
            response(_result.returnValue, _result.count, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPHisDataManager::QueryTableCountResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPHisDataManagerPrx::_iceI_queryTableCount, tableName, condition, context);
    }

    /// \cond INTERNAL
    void _iceI_queryTableCount(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryTableCountResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool queryStoreYc(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPHisDataManager::QueryStoreYcResult>(true, this, &ZGSPHisDataManagerPrx::_iceI_queryStoreYc, listDevice, listProperty, startTime, endTime, context).get();
        listResult = ::std::move(_result.listResult);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto queryStoreYcAsync(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPHisDataManager::QueryStoreYcResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPHisDataManager::QueryStoreYcResult, P>(false, this, &ZGSPHisDataManagerPrx::_iceI_queryStoreYc, listDevice, listProperty, startTime, endTime, context);
    }

    ::std::function<void()>
    queryStoreYcAsync(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime,
                      ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPHisDataManager::QueryStoreYcResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listResult), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPHisDataManager::QueryStoreYcResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPHisDataManagerPrx::_iceI_queryStoreYc, listDevice, listProperty, startTime, endTime, context);
    }

    /// \cond INTERNAL
    void _iceI_queryStoreYc(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryStoreYcResult>>&, const StringList&, const StringList&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool queryStoreYx(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPHisDataManager::QueryStoreYxResult>(true, this, &ZGSPHisDataManagerPrx::_iceI_queryStoreYx, listDevice, listProperty, startTime, endTime, context).get();
        listResult = ::std::move(_result.listResult);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto queryStoreYxAsync(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPHisDataManager::QueryStoreYxResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPHisDataManager::QueryStoreYxResult, P>(false, this, &ZGSPHisDataManagerPrx::_iceI_queryStoreYx, listDevice, listProperty, startTime, endTime, context);
    }

    ::std::function<void()>
    queryStoreYxAsync(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime,
                      ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPHisDataManager::QueryStoreYxResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listResult), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPHisDataManager::QueryStoreYxResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPHisDataManagerPrx::_iceI_queryStoreYx, listDevice, listProperty, startTime, endTime, context);
    }

    /// \cond INTERNAL
    void _iceI_queryStoreYx(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryStoreYxResult>>&, const StringList&, const StringList&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool queryStoreText(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPHisDataManager::QueryStoreTextResult>(true, this, &ZGSPHisDataManagerPrx::_iceI_queryStoreText, listDevice, listProperty, startTime, endTime, context).get();
        listResult = ::std::move(_result.listResult);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto queryStoreTextAsync(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPHisDataManager::QueryStoreTextResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPHisDataManager::QueryStoreTextResult, P>(false, this, &ZGSPHisDataManagerPrx::_iceI_queryStoreText, listDevice, listProperty, startTime, endTime, context);
    }

    ::std::function<void()>
    queryStoreTextAsync(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime,
                        ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPHisDataManager::QueryStoreTextResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listResult), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPHisDataManager::QueryStoreTextResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPHisDataManagerPrx::_iceI_queryStoreText, listDevice, listProperty, startTime, endTime, context);
    }

    /// \cond INTERNAL
    void _iceI_queryStoreText(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryStoreTextResult>>&, const StringList&, const StringList&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool queryStoreYm(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPHisDataManager::QueryStoreYmResult>(true, this, &ZGSPHisDataManagerPrx::_iceI_queryStoreYm, listDevice, listProperty, startTime, endTime, context).get();
        listResult = ::std::move(_result.listResult);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto queryStoreYmAsync(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPHisDataManager::QueryStoreYmResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPHisDataManager::QueryStoreYmResult, P>(false, this, &ZGSPHisDataManagerPrx::_iceI_queryStoreYm, listDevice, listProperty, startTime, endTime, context);
    }

    ::std::function<void()>
    queryStoreYmAsync(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime,
                      ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPHisDataManager::QueryStoreYmResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listResult), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPHisDataManager::QueryStoreYmResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPHisDataManagerPrx::_iceI_queryStoreYm, listDevice, listProperty, startTime, endTime, context);
    }

    /// \cond INTERNAL
    void _iceI_queryStoreYm(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPHisDataManager::QueryStoreYmResult>>&, const StringList&, const StringList&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPHisDataManagerPrx() = default;
    friend ::std::shared_ptr<ZGSPHisDataManagerPrx> IceInternal::createProxy<ZGSPHisDataManagerPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

template<>
struct StreamableTraits< ::ZG6000::StoreType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 4;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPHisDataManagerPtr = ::std::shared_ptr<ZGSPHisDataManager>;
using ZGSPHisDataManagerPrxPtr = ::std::shared_ptr<ZGSPHisDataManagerPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPHisDataManager;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPHisDataManager>&);
::IceProxy::Ice::Object* upCast(ZGSPHisDataManager*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPHisDataManager;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPHisDataManager*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPHisDataManager> ZGSPHisDataManagerPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPHisDataManager> ZGSPHisDataManagerPrx;
typedef ZGSPHisDataManagerPrx ZGSPHisDataManagerPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPHisDataManagerPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

enum StoreType
{
    stDay,
    stWeek,
    stHour,
    stMinute,
    stSecond
};

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryTableData.
 */
class Callback_ZGSPHisDataManager_queryTableData_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPHisDataManager_queryTableData_Base> Callback_ZGSPHisDataManager_queryTableDataPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableCount.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryTableCount.
 */
class Callback_ZGSPHisDataManager_queryTableCount_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPHisDataManager_queryTableCount_Base> Callback_ZGSPHisDataManager_queryTableCountPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYc.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreYc.
 */
class Callback_ZGSPHisDataManager_queryStoreYc_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPHisDataManager_queryStoreYc_Base> Callback_ZGSPHisDataManager_queryStoreYcPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreYx.
 */
class Callback_ZGSPHisDataManager_queryStoreYx_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPHisDataManager_queryStoreYx_Base> Callback_ZGSPHisDataManager_queryStoreYxPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreText.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreText.
 */
class Callback_ZGSPHisDataManager_queryStoreText_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPHisDataManager_queryStoreText_Base> Callback_ZGSPHisDataManager_queryStoreTextPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreYm.
 */
class Callback_ZGSPHisDataManager_queryStoreYm_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPHisDataManager_queryStoreYm_Base> Callback_ZGSPHisDataManager_queryStoreYmPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPHisDataManager : public virtual ::Ice::Proxy<ZGSPHisDataManager, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool queryTableData(const ::std::string& tableName, const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, ::ZG6000::StringList& listTitle, ::ZG6000::ListStringList& listValues, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_queryTableData(listTitle, listValues, e, _iceI_begin_queryTableData(tableName, condition, offset, limit, orderField, orderType, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_queryTableData(const ::std::string& tableName, const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_queryTableData(tableName, condition, offset, limit, orderField, orderType, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_queryTableData(const ::std::string& tableName, const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryTableData(tableName, condition, offset, limit, orderField, orderType, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryTableData(const ::std::string& tableName, const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryTableData(tableName, condition, offset, limit, orderField, orderType, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryTableData(const ::std::string& tableName, const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, const ::ZG6000::Callback_ZGSPHisDataManager_queryTableDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryTableData(tableName, condition, offset, limit, orderField, orderType, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryTableData(const ::std::string& tableName, const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPHisDataManager_queryTableDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryTableData(tableName, condition, offset, limit, orderField, orderType, context, cb, cookie);
    }

    bool end_queryTableData(::ZG6000::StringList& listTitle, ::ZG6000::ListStringList& listValues, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_queryTableData(::ZG6000::StringList& iceP_listTitle, ::ZG6000::ListStringList& iceP_listValues, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_queryTableData(const ::std::string&, const ::std::string&, ::Ice::Int, ::Ice::Int, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool queryTableCount(const ::std::string& tableName, const ::std::string& condition, ::Ice::Int& count, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_queryTableCount(count, e, _iceI_begin_queryTableCount(tableName, condition, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_queryTableCount(const ::std::string& tableName, const ::std::string& condition, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_queryTableCount(tableName, condition, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_queryTableCount(const ::std::string& tableName, const ::std::string& condition, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryTableCount(tableName, condition, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryTableCount(const ::std::string& tableName, const ::std::string& condition, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryTableCount(tableName, condition, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryTableCount(const ::std::string& tableName, const ::std::string& condition, const ::ZG6000::Callback_ZGSPHisDataManager_queryTableCountPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryTableCount(tableName, condition, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryTableCount(const ::std::string& tableName, const ::std::string& condition, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPHisDataManager_queryTableCountPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryTableCount(tableName, condition, context, cb, cookie);
    }

    bool end_queryTableCount(::Ice::Int& count, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_queryTableCount(::Ice::Int& iceP_count, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_queryTableCount(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool queryStoreYc(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ::ZG6000::ListStringMap& listResult, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_queryStoreYc(listResult, e, _iceI_begin_queryStoreYc(listDevice, listProperty, startTime, endTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_queryStoreYc(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_queryStoreYc(listDevice, listProperty, startTime, endTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYc(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYc(listDevice, listProperty, startTime, endTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYc(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYc(listDevice, listProperty, startTime, endTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYc(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::ZG6000::Callback_ZGSPHisDataManager_queryStoreYcPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYc(listDevice, listProperty, startTime, endTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYc(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPHisDataManager_queryStoreYcPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYc(listDevice, listProperty, startTime, endTime, context, cb, cookie);
    }

    bool end_queryStoreYc(::ZG6000::ListStringMap& listResult, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_queryStoreYc(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_queryStoreYc(const ::ZG6000::StringList&, const ::ZG6000::StringList&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool queryStoreYx(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ::ZG6000::ListStringMap& listResult, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_queryStoreYx(listResult, e, _iceI_begin_queryStoreYx(listDevice, listProperty, startTime, endTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_queryStoreYx(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_queryStoreYx(listDevice, listProperty, startTime, endTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYx(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYx(listDevice, listProperty, startTime, endTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYx(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYx(listDevice, listProperty, startTime, endTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYx(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::ZG6000::Callback_ZGSPHisDataManager_queryStoreYxPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYx(listDevice, listProperty, startTime, endTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYx(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPHisDataManager_queryStoreYxPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYx(listDevice, listProperty, startTime, endTime, context, cb, cookie);
    }

    bool end_queryStoreYx(::ZG6000::ListStringMap& listResult, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_queryStoreYx(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_queryStoreYx(const ::ZG6000::StringList&, const ::ZG6000::StringList&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool queryStoreText(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ::ZG6000::ListStringMap& listResult, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_queryStoreText(listResult, e, _iceI_begin_queryStoreText(listDevice, listProperty, startTime, endTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_queryStoreText(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_queryStoreText(listDevice, listProperty, startTime, endTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_queryStoreText(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreText(listDevice, listProperty, startTime, endTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreText(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreText(listDevice, listProperty, startTime, endTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreText(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::ZG6000::Callback_ZGSPHisDataManager_queryStoreTextPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreText(listDevice, listProperty, startTime, endTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreText(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPHisDataManager_queryStoreTextPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreText(listDevice, listProperty, startTime, endTime, context, cb, cookie);
    }

    bool end_queryStoreText(::ZG6000::ListStringMap& listResult, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_queryStoreText(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_queryStoreText(const ::ZG6000::StringList&, const ::ZG6000::StringList&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool queryStoreYm(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ::ZG6000::ListStringMap& listResult, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_queryStoreYm(listResult, e, _iceI_begin_queryStoreYm(listDevice, listProperty, startTime, endTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_queryStoreYm(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_queryStoreYm(listDevice, listProperty, startTime, endTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYm(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYm(listDevice, listProperty, startTime, endTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYm(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYm(listDevice, listProperty, startTime, endTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYm(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::ZG6000::Callback_ZGSPHisDataManager_queryStoreYmPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYm(listDevice, listProperty, startTime, endTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_queryStoreYm(const ::ZG6000::StringList& listDevice, const ::ZG6000::StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPHisDataManager_queryStoreYmPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_queryStoreYm(listDevice, listProperty, startTime, endTime, context, cb, cookie);
    }

    bool end_queryStoreYm(::ZG6000::ListStringMap& listResult, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_queryStoreYm(::ZG6000::ListStringMap& iceP_listResult, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_queryStoreYm(const ::ZG6000::StringList&, const ::ZG6000::StringList&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPHisDataManager : virtual public ZGServerBase
{
public:

    typedef ZGSPHisDataManagerPrx ProxyType;
    typedef ZGSPHisDataManagerPtr PointerType;

    virtual ~ZGSPHisDataManager();

#ifdef ICE_CPP11_COMPILER
    ZGSPHisDataManager() = default;
    ZGSPHisDataManager(const ZGSPHisDataManager&) = default;
    ZGSPHisDataManager& operator=(const ZGSPHisDataManager&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool queryTableData(const ::std::string& tableName, const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, StringList& listTitle, ListStringList& listValues, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_queryTableData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool queryTableCount(const ::std::string& tableName, const ::std::string& condition, ::Ice::Int& count, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_queryTableCount(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool queryStoreYc(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_queryStoreYc(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool queryStoreYx(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_queryStoreYx(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool queryStoreText(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_queryStoreText(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool queryStoreYm(const StringList& listDevice, const StringList& listProperty, const ::std::string& startTime, const ::std::string& endTime, ListStringMap& listResult, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_queryStoreYm(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPHisDataManager& lhs, const ZGSPHisDataManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPHisDataManager& lhs, const ZGSPHisDataManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

template<>
struct StreamableTraits< ::ZG6000::StoreType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 4;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryTableData.
 */
template<class T>
class CallbackNC_ZGSPHisDataManager_queryTableData : public Callback_ZGSPHisDataManager_queryTableData_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringList&, const ListStringList&, const ErrorInfo&);

    CallbackNC_ZGSPHisDataManager_queryTableData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        StringList iceP_listTitle;
        ListStringList iceP_listValues;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryTableData(iceP_listTitle, iceP_listValues, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listTitle, iceP_listValues, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableData.
 */
template<class T> Callback_ZGSPHisDataManager_queryTableDataPtr
newCallback_ZGSPHisDataManager_queryTableData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringList&, const ListStringList&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryTableData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableData.
 */
template<class T> Callback_ZGSPHisDataManager_queryTableDataPtr
newCallback_ZGSPHisDataManager_queryTableData(T* instance, void (T::*cb)(bool, const StringList&, const ListStringList&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryTableData<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableData.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryTableData.
 */
template<class T, typename CT>
class Callback_ZGSPHisDataManager_queryTableData : public Callback_ZGSPHisDataManager_queryTableData_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringList&, const ListStringList&, const ErrorInfo&, const CT&);

    Callback_ZGSPHisDataManager_queryTableData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        StringList iceP_listTitle;
        ListStringList iceP_listValues;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryTableData(iceP_listTitle, iceP_listValues, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listTitle, iceP_listValues, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableData.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryTableDataPtr
newCallback_ZGSPHisDataManager_queryTableData(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringList&, const ListStringList&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryTableData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableData.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryTableDataPtr
newCallback_ZGSPHisDataManager_queryTableData(T* instance, void (T::*cb)(bool, const StringList&, const ListStringList&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryTableData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableCount.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryTableCount.
 */
template<class T>
class CallbackNC_ZGSPHisDataManager_queryTableCount : public Callback_ZGSPHisDataManager_queryTableCount_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&);

    CallbackNC_ZGSPHisDataManager_queryTableCount(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_count;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryTableCount(iceP_count, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_count, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableCount.
 */
template<class T> Callback_ZGSPHisDataManager_queryTableCountPtr
newCallback_ZGSPHisDataManager_queryTableCount(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryTableCount<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableCount.
 */
template<class T> Callback_ZGSPHisDataManager_queryTableCountPtr
newCallback_ZGSPHisDataManager_queryTableCount(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryTableCount<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableCount.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryTableCount.
 */
template<class T, typename CT>
class Callback_ZGSPHisDataManager_queryTableCount : public Callback_ZGSPHisDataManager_queryTableCount_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, ::Ice::Int, const ErrorInfo&, const CT&);

    Callback_ZGSPHisDataManager_queryTableCount(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        ::Ice::Int iceP_count;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryTableCount(iceP_count, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_count, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableCount.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryTableCountPtr
newCallback_ZGSPHisDataManager_queryTableCount(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryTableCount<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryTableCount.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryTableCountPtr
newCallback_ZGSPHisDataManager_queryTableCount(T* instance, void (T::*cb)(bool, ::Ice::Int, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryTableCount<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYc.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreYc.
 */
template<class T>
class CallbackNC_ZGSPHisDataManager_queryStoreYc : public Callback_ZGSPHisDataManager_queryStoreYc_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPHisDataManager_queryStoreYc(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listResult;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryStoreYc(iceP_listResult, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listResult, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYc.
 */
template<class T> Callback_ZGSPHisDataManager_queryStoreYcPtr
newCallback_ZGSPHisDataManager_queryStoreYc(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryStoreYc<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYc.
 */
template<class T> Callback_ZGSPHisDataManager_queryStoreYcPtr
newCallback_ZGSPHisDataManager_queryStoreYc(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryStoreYc<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYc.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreYc.
 */
template<class T, typename CT>
class Callback_ZGSPHisDataManager_queryStoreYc : public Callback_ZGSPHisDataManager_queryStoreYc_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPHisDataManager_queryStoreYc(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listResult;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryStoreYc(iceP_listResult, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listResult, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYc.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryStoreYcPtr
newCallback_ZGSPHisDataManager_queryStoreYc(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryStoreYc<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYc.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryStoreYcPtr
newCallback_ZGSPHisDataManager_queryStoreYc(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryStoreYc<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreYx.
 */
template<class T>
class CallbackNC_ZGSPHisDataManager_queryStoreYx : public Callback_ZGSPHisDataManager_queryStoreYx_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPHisDataManager_queryStoreYx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listResult;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryStoreYx(iceP_listResult, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listResult, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYx.
 */
template<class T> Callback_ZGSPHisDataManager_queryStoreYxPtr
newCallback_ZGSPHisDataManager_queryStoreYx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryStoreYx<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYx.
 */
template<class T> Callback_ZGSPHisDataManager_queryStoreYxPtr
newCallback_ZGSPHisDataManager_queryStoreYx(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryStoreYx<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYx.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreYx.
 */
template<class T, typename CT>
class Callback_ZGSPHisDataManager_queryStoreYx : public Callback_ZGSPHisDataManager_queryStoreYx_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPHisDataManager_queryStoreYx(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listResult;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryStoreYx(iceP_listResult, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listResult, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYx.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryStoreYxPtr
newCallback_ZGSPHisDataManager_queryStoreYx(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryStoreYx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYx.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryStoreYxPtr
newCallback_ZGSPHisDataManager_queryStoreYx(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryStoreYx<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreText.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreText.
 */
template<class T>
class CallbackNC_ZGSPHisDataManager_queryStoreText : public Callback_ZGSPHisDataManager_queryStoreText_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPHisDataManager_queryStoreText(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listResult;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryStoreText(iceP_listResult, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listResult, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreText.
 */
template<class T> Callback_ZGSPHisDataManager_queryStoreTextPtr
newCallback_ZGSPHisDataManager_queryStoreText(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryStoreText<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreText.
 */
template<class T> Callback_ZGSPHisDataManager_queryStoreTextPtr
newCallback_ZGSPHisDataManager_queryStoreText(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryStoreText<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreText.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreText.
 */
template<class T, typename CT>
class Callback_ZGSPHisDataManager_queryStoreText : public Callback_ZGSPHisDataManager_queryStoreText_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPHisDataManager_queryStoreText(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listResult;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryStoreText(iceP_listResult, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listResult, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreText.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryStoreTextPtr
newCallback_ZGSPHisDataManager_queryStoreText(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryStoreText<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreText.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryStoreTextPtr
newCallback_ZGSPHisDataManager_queryStoreText(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryStoreText<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreYm.
 */
template<class T>
class CallbackNC_ZGSPHisDataManager_queryStoreYm : public Callback_ZGSPHisDataManager_queryStoreYm_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPHisDataManager_queryStoreYm(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listResult;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryStoreYm(iceP_listResult, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listResult, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYm.
 */
template<class T> Callback_ZGSPHisDataManager_queryStoreYmPtr
newCallback_ZGSPHisDataManager_queryStoreYm(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryStoreYm<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYm.
 */
template<class T> Callback_ZGSPHisDataManager_queryStoreYmPtr
newCallback_ZGSPHisDataManager_queryStoreYm(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPHisDataManager_queryStoreYm<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYm.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPHisDataManager_queryStoreYm.
 */
template<class T, typename CT>
class Callback_ZGSPHisDataManager_queryStoreYm : public Callback_ZGSPHisDataManager_queryStoreYm_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPHisDataManager_queryStoreYm(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPHisDataManagerPrx proxy = ZGSPHisDataManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listResult;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_queryStoreYm(iceP_listResult, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listResult, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYm.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryStoreYmPtr
newCallback_ZGSPHisDataManager_queryStoreYm(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryStoreYm<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPHisDataManager::begin_queryStoreYm.
 */
template<class T, typename CT> Callback_ZGSPHisDataManager_queryStoreYmPtr
newCallback_ZGSPHisDataManager_queryStoreYm(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPHisDataManager_queryStoreYm<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

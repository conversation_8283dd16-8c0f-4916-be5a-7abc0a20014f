//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSTStraySystem.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSTStraySystem_h__
#define __ZGSTStraySystem_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSTStraySystem;
class ZGSTStraySystemPrx;

}

namespace ZG6000
{

class ZGSTStraySystem : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSTStraySystemPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to setMeasureStation.
     */
    struct SetMeasureStationResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	选择测量站点
     */
    virtual bool setMeasureStation(::std::string clientID, ::std::string inStationID, ::std::string outStationID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setMeasureStation(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to calculateOffset.
     */
    struct CalculateOffsetResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	计算电压与电流偏移数据
     */
    virtual bool calculateOffset(ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_calculateOffset(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to startCalculate.
     */
    struct StartCalculateResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	开始执行计算
     */
    virtual bool startCalculate(ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_startCalculate(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to stopCalculate.
     */
    struct StopCalculateResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	停止执行计算
     */
    virtual bool stopCalculate(ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_stopCalculate(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getValidStations.
     */
    struct GetValidStationsResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ListStringMap listMapStation;
        ErrorInfo e;
    };

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    virtual bool getValidStations(ListStringMap& listMapStation, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getValidStations(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getSystemParam.
     */
    struct GetSystemParamResult
    {
        bool returnValue;
        StringMap systemParam;
        ErrorInfo e;
    };

    virtual bool getSystemParam(StringMap& systemParam, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getSystemParam(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setSystemParam.
     */
    struct SetSystemParamResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setSystemParam(StringMap systemParam, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setSystemParam(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSTStraySystemPrx : public virtual ::Ice::Proxy<ZGSTStraySystemPrx, ZGServerBasePrx>
{
public:

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	选择测量站点
     */
    bool setMeasureStation(const ::std::string& clientID, const ::std::string& inStationID, const ::std::string& outStationID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStraySystem::SetMeasureStationResult>(true, this, &ZGSTStraySystemPrx::_iceI_setMeasureStation, clientID, inStationID, outStationID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	选择测量站点
     */
    template<template<typename> class P = ::std::promise>
    auto setMeasureStationAsync(const ::std::string& clientID, const ::std::string& inStationID, const ::std::string& outStationID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStraySystem::SetMeasureStationResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStraySystem::SetMeasureStationResult, P>(false, this, &ZGSTStraySystemPrx::_iceI_setMeasureStation, clientID, inStationID, outStationID, context);
    }

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	选择测量站点
     */
    ::std::function<void()>
    setMeasureStationAsync(const ::std::string& clientID, const ::std::string& inStationID, const ::std::string& outStationID,
                           ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStraySystem::SetMeasureStationResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStraySystem::SetMeasureStationResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStraySystemPrx::_iceI_setMeasureStation, clientID, inStationID, outStationID, context);
    }

    /// \cond INTERNAL
    void _iceI_setMeasureStation(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::SetMeasureStationResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	计算电压与电流偏移数据
     */
    bool calculateOffset(ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStraySystem::CalculateOffsetResult>(true, this, &ZGSTStraySystemPrx::_iceI_calculateOffset, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	计算电压与电流偏移数据
     */
    template<template<typename> class P = ::std::promise>
    auto calculateOffsetAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStraySystem::CalculateOffsetResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStraySystem::CalculateOffsetResult, P>(false, this, &ZGSTStraySystemPrx::_iceI_calculateOffset, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	计算电压与电流偏移数据
     */
    ::std::function<void()>
    calculateOffsetAsync(::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStraySystem::CalculateOffsetResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStraySystem::CalculateOffsetResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStraySystemPrx::_iceI_calculateOffset, context);
    }

    /// \cond INTERNAL
    void _iceI_calculateOffset(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::CalculateOffsetResult>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	开始执行计算
     */
    bool startCalculate(ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStraySystem::StartCalculateResult>(true, this, &ZGSTStraySystemPrx::_iceI_startCalculate, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	开始执行计算
     */
    template<template<typename> class P = ::std::promise>
    auto startCalculateAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStraySystem::StartCalculateResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStraySystem::StartCalculateResult, P>(false, this, &ZGSTStraySystemPrx::_iceI_startCalculate, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	开始执行计算
     */
    ::std::function<void()>
    startCalculateAsync(::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStraySystem::StartCalculateResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStraySystem::StartCalculateResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStraySystemPrx::_iceI_startCalculate, context);
    }

    /// \cond INTERNAL
    void _iceI_startCalculate(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::StartCalculateResult>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	停止执行计算
     */
    bool stopCalculate(ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStraySystem::StopCalculateResult>(true, this, &ZGSTStraySystemPrx::_iceI_stopCalculate, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	停止执行计算
     */
    template<template<typename> class P = ::std::promise>
    auto stopCalculateAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStraySystem::StopCalculateResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStraySystem::StopCalculateResult, P>(false, this, &ZGSTStraySystemPrx::_iceI_stopCalculate, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	停止执行计算
     */
    ::std::function<void()>
    stopCalculateAsync(::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStraySystem::StopCalculateResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStraySystem::StopCalculateResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStraySystemPrx::_iceI_stopCalculate, context);
    }

    /// \cond INTERNAL
    void _iceI_stopCalculate(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::StopCalculateResult>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    bool getValidStations(ListStringMap& listMapStation, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStraySystem::GetValidStationsResult>(true, this, &ZGSTStraySystemPrx::_iceI_getValidStations, context).get();
        listMapStation = ::std::move(_result.listMapStation);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    template<template<typename> class P = ::std::promise>
    auto getValidStationsAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStraySystem::GetValidStationsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStraySystem::GetValidStationsResult, P>(false, this, &ZGSTStraySystemPrx::_iceI_getValidStations, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    ::std::function<void()>
    getValidStationsAsync(::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStraySystem::GetValidStationsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listMapStation), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStraySystem::GetValidStationsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStraySystemPrx::_iceI_getValidStations, context);
    }

    /// \cond INTERNAL
    void _iceI_getValidStations(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::GetValidStationsResult>>&, const ::Ice::Context&);
    /// \endcond

    bool getSystemParam(StringMap& systemParam, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStraySystem::GetSystemParamResult>(true, this, &ZGSTStraySystemPrx::_iceI_getSystemParam, context).get();
        systemParam = ::std::move(_result.systemParam);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getSystemParamAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStraySystem::GetSystemParamResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStraySystem::GetSystemParamResult, P>(false, this, &ZGSTStraySystemPrx::_iceI_getSystemParam, context);
    }

    ::std::function<void()>
    getSystemParamAsync(::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStraySystem::GetSystemParamResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.systemParam), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStraySystem::GetSystemParamResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStraySystemPrx::_iceI_getSystemParam, context);
    }

    /// \cond INTERNAL
    void _iceI_getSystemParam(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::GetSystemParamResult>>&, const ::Ice::Context&);
    /// \endcond

    bool setSystemParam(const StringMap& systemParam, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStraySystem::SetSystemParamResult>(true, this, &ZGSTStraySystemPrx::_iceI_setSystemParam, systemParam, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setSystemParamAsync(const StringMap& systemParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStraySystem::SetSystemParamResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStraySystem::SetSystemParamResult, P>(false, this, &ZGSTStraySystemPrx::_iceI_setSystemParam, systemParam, context);
    }

    ::std::function<void()>
    setSystemParamAsync(const StringMap& systemParam,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStraySystem::SetSystemParamResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStraySystem::SetSystemParamResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStraySystemPrx::_iceI_setSystemParam, systemParam, context);
    }

    /// \cond INTERNAL
    void _iceI_setSystemParam(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStraySystem::SetSystemParamResult>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSTStraySystemPrx() = default;
    friend ::std::shared_ptr<ZGSTStraySystemPrx> IceInternal::createProxy<ZGSTStraySystemPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSTStraySystemPtr = ::std::shared_ptr<ZGSTStraySystem>;
using ZGSTStraySystemPrxPtr = ::std::shared_ptr<ZGSTStraySystemPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSTStraySystem;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSTStraySystem>&);
::IceProxy::Ice::Object* upCast(ZGSTStraySystem*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSTStraySystem;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSTStraySystem*);
/// \endcond
typedef ::IceInternal::Handle< ZGSTStraySystem> ZGSTStraySystemPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSTStraySystem> ZGSTStraySystemPrx;
typedef ZGSTStraySystemPrx ZGSTStraySystemPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSTStraySystemPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_setMeasureStation.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_setMeasureStation.
 */
class Callback_ZGSTStraySystem_setMeasureStation_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStraySystem_setMeasureStation_Base> Callback_ZGSTStraySystem_setMeasureStationPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_calculateOffset.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_calculateOffset.
 */
class Callback_ZGSTStraySystem_calculateOffset_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStraySystem_calculateOffset_Base> Callback_ZGSTStraySystem_calculateOffsetPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_startCalculate.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_startCalculate.
 */
class Callback_ZGSTStraySystem_startCalculate_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStraySystem_startCalculate_Base> Callback_ZGSTStraySystem_startCalculatePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_stopCalculate.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_stopCalculate.
 */
class Callback_ZGSTStraySystem_stopCalculate_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStraySystem_stopCalculate_Base> Callback_ZGSTStraySystem_stopCalculatePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_getValidStations.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_getValidStations.
 */
class Callback_ZGSTStraySystem_getValidStations_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStraySystem_getValidStations_Base> Callback_ZGSTStraySystem_getValidStationsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_getSystemParam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_getSystemParam.
 */
class Callback_ZGSTStraySystem_getSystemParam_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStraySystem_getSystemParam_Base> Callback_ZGSTStraySystem_getSystemParamPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_setSystemParam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_setSystemParam.
 */
class Callback_ZGSTStraySystem_setSystemParam_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStraySystem_setSystemParam_Base> Callback_ZGSTStraySystem_setSystemParamPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSTStraySystem : public virtual ::Ice::Proxy<ZGSTStraySystem, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	选择测量站点
     */
    bool setMeasureStation(const ::std::string& clientID, const ::std::string& inStationID, const ::std::string& outStationID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setMeasureStation(e, _iceI_begin_setMeasureStation(clientID, inStationID, outStationID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	选择测量站点
     */
    ::Ice::AsyncResultPtr begin_setMeasureStation(const ::std::string& clientID, const ::std::string& inStationID, const ::std::string& outStationID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setMeasureStation(clientID, inStationID, outStationID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	选择测量站点
     */
    ::Ice::AsyncResultPtr begin_setMeasureStation(const ::std::string& clientID, const ::std::string& inStationID, const ::std::string& outStationID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setMeasureStation(clientID, inStationID, outStationID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	选择测量站点
     */
    ::Ice::AsyncResultPtr begin_setMeasureStation(const ::std::string& clientID, const ::std::string& inStationID, const ::std::string& outStationID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setMeasureStation(clientID, inStationID, outStationID, context, cb, cookie);
    }

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	选择测量站点
     */
    ::Ice::AsyncResultPtr begin_setMeasureStation(const ::std::string& clientID, const ::std::string& inStationID, const ::std::string& outStationID, const ::ZG6000::Callback_ZGSTStraySystem_setMeasureStationPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setMeasureStation(clientID, inStationID, outStationID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	选择测量站点
     */
    ::Ice::AsyncResultPtr begin_setMeasureStation(const ::std::string& clientID, const ::std::string& inStationID, const ::std::string& outStationID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStraySystem_setMeasureStationPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setMeasureStation(clientID, inStationID, outStationID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_setMeasureStation.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_setMeasureStation(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setMeasureStation(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setMeasureStation(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	计算电压与电流偏移数据
     */
    bool calculateOffset(::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_calculateOffset(e, _iceI_begin_calculateOffset(context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	计算电压与电流偏移数据
     */
    ::Ice::AsyncResultPtr begin_calculateOffset(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_calculateOffset(context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	计算电压与电流偏移数据
     */
    ::Ice::AsyncResultPtr begin_calculateOffset(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_calculateOffset(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	计算电压与电流偏移数据
     */
    ::Ice::AsyncResultPtr begin_calculateOffset(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_calculateOffset(context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	计算电压与电流偏移数据
     */
    ::Ice::AsyncResultPtr begin_calculateOffset(const ::ZG6000::Callback_ZGSTStraySystem_calculateOffsetPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_calculateOffset(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	计算电压与电流偏移数据
     */
    ::Ice::AsyncResultPtr begin_calculateOffset(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStraySystem_calculateOffsetPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_calculateOffset(context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_calculateOffset.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_calculateOffset(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_calculateOffset(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_calculateOffset(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	开始执行计算
     */
    bool startCalculate(::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_startCalculate(e, _iceI_begin_startCalculate(context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	开始执行计算
     */
    ::Ice::AsyncResultPtr begin_startCalculate(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_startCalculate(context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	开始执行计算
     */
    ::Ice::AsyncResultPtr begin_startCalculate(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startCalculate(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	开始执行计算
     */
    ::Ice::AsyncResultPtr begin_startCalculate(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startCalculate(context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	开始执行计算
     */
    ::Ice::AsyncResultPtr begin_startCalculate(const ::ZG6000::Callback_ZGSTStraySystem_startCalculatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startCalculate(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	开始执行计算
     */
    ::Ice::AsyncResultPtr begin_startCalculate(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStraySystem_startCalculatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_startCalculate(context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_startCalculate.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_startCalculate(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_startCalculate(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_startCalculate(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	停止执行计算
     */
    bool stopCalculate(::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_stopCalculate(e, _iceI_begin_stopCalculate(context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	停止执行计算
     */
    ::Ice::AsyncResultPtr begin_stopCalculate(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_stopCalculate(context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	停止执行计算
     */
    ::Ice::AsyncResultPtr begin_stopCalculate(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopCalculate(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	停止执行计算
     */
    ::Ice::AsyncResultPtr begin_stopCalculate(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopCalculate(context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	停止执行计算
     */
    ::Ice::AsyncResultPtr begin_stopCalculate(const ::ZG6000::Callback_ZGSTStraySystem_stopCalculatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopCalculate(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	停止执行计算
     */
    ::Ice::AsyncResultPtr begin_stopCalculate(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStraySystem_stopCalculatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_stopCalculate(context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_stopCalculate.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_stopCalculate(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_stopCalculate(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_stopCalculate(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    bool getValidStations(::ZG6000::ListStringMap& listMapStation, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getValidStations(listMapStation, e, _iceI_begin_getValidStations(context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    ::Ice::AsyncResultPtr begin_getValidStations(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getValidStations(context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    ::Ice::AsyncResultPtr begin_getValidStations(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getValidStations(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    ::Ice::AsyncResultPtr begin_getValidStations(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getValidStations(context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    ::Ice::AsyncResultPtr begin_getValidStations(const ::ZG6000::Callback_ZGSTStraySystem_getValidStationsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getValidStations(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    ::Ice::AsyncResultPtr begin_getValidStations(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStraySystem_getValidStationsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getValidStations(context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getValidStations.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_getValidStations(::ZG6000::ListStringMap& listMapStation, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getValidStations(::ZG6000::ListStringMap& iceP_listMapStation, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getValidStations(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getSystemParam(::ZG6000::StringMap& systemParam, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getSystemParam(systemParam, e, _iceI_begin_getSystemParam(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getSystemParam(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getSystemParam(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getSystemParam(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSystemParam(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getSystemParam(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSystemParam(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getSystemParam(const ::ZG6000::Callback_ZGSTStraySystem_getSystemParamPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSystemParam(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getSystemParam(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStraySystem_getSystemParamPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSystemParam(context, cb, cookie);
    }

    bool end_getSystemParam(::ZG6000::StringMap& systemParam, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getSystemParam(::ZG6000::StringMap& iceP_systemParam, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getSystemParam(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setSystemParam(const ::ZG6000::StringMap& systemParam, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setSystemParam(e, _iceI_begin_setSystemParam(systemParam, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setSystemParam(const ::ZG6000::StringMap& systemParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setSystemParam(systemParam, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setSystemParam(const ::ZG6000::StringMap& systemParam, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setSystemParam(systemParam, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setSystemParam(const ::ZG6000::StringMap& systemParam, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setSystemParam(systemParam, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setSystemParam(const ::ZG6000::StringMap& systemParam, const ::ZG6000::Callback_ZGSTStraySystem_setSystemParamPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setSystemParam(systemParam, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setSystemParam(const ::ZG6000::StringMap& systemParam, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStraySystem_setSystemParamPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setSystemParam(systemParam, context, cb, cookie);
    }

    bool end_setSystemParam(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setSystemParam(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setSystemParam(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSTStraySystem : virtual public ZGServerBase
{
public:

    typedef ZGSTStraySystemPrx ProxyType;
    typedef ZGSTStraySystemPtr PointerType;

    virtual ~ZGSTStraySystem();

#ifdef ICE_CPP11_COMPILER
    ZGSTStraySystem() = default;
    ZGSTStraySystem(const ZGSTStraySystem&) = default;
    ZGSTStraySystem& operator=(const ZGSTStraySystem&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param clientID 客户端ID
     * @param inStationID 流入站ID
     * @param outStationID 流出站ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	选择测量站点
     */
    virtual bool setMeasureStation(const ::std::string& clientID, const ::std::string& inStationID, const ::std::string& outStationID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setMeasureStation(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	计算电压与电流偏移数据
     */
    virtual bool calculateOffset(ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_calculateOffset(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	开始执行计算
     */
    virtual bool startCalculate(ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_startCalculate(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	停止执行计算
     */
    virtual bool stopCalculate(ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_stopCalculate(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	后台获取有效的站点（包含测流传感器的站点）
     */
    virtual bool getValidStations(ListStringMap& listMapStation, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getValidStations(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getSystemParam(StringMap& systemParam, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getSystemParam(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setSystemParam(const StringMap& systemParam, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setSystemParam(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSTStraySystem& lhs, const ZGSTStraySystem& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSTStraySystem& lhs, const ZGSTStraySystem& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_setMeasureStation.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_setMeasureStation.
 */
template<class T>
class CallbackNC_ZGSTStraySystem_setMeasureStation : public Callback_ZGSTStraySystem_setMeasureStation_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSTStraySystem_setMeasureStation(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setMeasureStation(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_setMeasureStation.
 */
template<class T> Callback_ZGSTStraySystem_setMeasureStationPtr
newCallback_ZGSTStraySystem_setMeasureStation(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_setMeasureStation<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_setMeasureStation.
 */
template<class T> Callback_ZGSTStraySystem_setMeasureStationPtr
newCallback_ZGSTStraySystem_setMeasureStation(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_setMeasureStation<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_setMeasureStation.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_setMeasureStation.
 */
template<class T, typename CT>
class Callback_ZGSTStraySystem_setMeasureStation : public Callback_ZGSTStraySystem_setMeasureStation_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSTStraySystem_setMeasureStation(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setMeasureStation(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_setMeasureStation.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_setMeasureStationPtr
newCallback_ZGSTStraySystem_setMeasureStation(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_setMeasureStation<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_setMeasureStation.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_setMeasureStationPtr
newCallback_ZGSTStraySystem_setMeasureStation(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_setMeasureStation<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_calculateOffset.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_calculateOffset.
 */
template<class T>
class CallbackNC_ZGSTStraySystem_calculateOffset : public Callback_ZGSTStraySystem_calculateOffset_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSTStraySystem_calculateOffset(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_calculateOffset(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_calculateOffset.
 */
template<class T> Callback_ZGSTStraySystem_calculateOffsetPtr
newCallback_ZGSTStraySystem_calculateOffset(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_calculateOffset<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_calculateOffset.
 */
template<class T> Callback_ZGSTStraySystem_calculateOffsetPtr
newCallback_ZGSTStraySystem_calculateOffset(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_calculateOffset<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_calculateOffset.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_calculateOffset.
 */
template<class T, typename CT>
class Callback_ZGSTStraySystem_calculateOffset : public Callback_ZGSTStraySystem_calculateOffset_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSTStraySystem_calculateOffset(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_calculateOffset(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_calculateOffset.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_calculateOffsetPtr
newCallback_ZGSTStraySystem_calculateOffset(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_calculateOffset<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_calculateOffset.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_calculateOffsetPtr
newCallback_ZGSTStraySystem_calculateOffset(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_calculateOffset<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_startCalculate.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_startCalculate.
 */
template<class T>
class CallbackNC_ZGSTStraySystem_startCalculate : public Callback_ZGSTStraySystem_startCalculate_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSTStraySystem_startCalculate(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_startCalculate(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_startCalculate.
 */
template<class T> Callback_ZGSTStraySystem_startCalculatePtr
newCallback_ZGSTStraySystem_startCalculate(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_startCalculate<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_startCalculate.
 */
template<class T> Callback_ZGSTStraySystem_startCalculatePtr
newCallback_ZGSTStraySystem_startCalculate(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_startCalculate<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_startCalculate.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_startCalculate.
 */
template<class T, typename CT>
class Callback_ZGSTStraySystem_startCalculate : public Callback_ZGSTStraySystem_startCalculate_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSTStraySystem_startCalculate(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_startCalculate(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_startCalculate.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_startCalculatePtr
newCallback_ZGSTStraySystem_startCalculate(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_startCalculate<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_startCalculate.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_startCalculatePtr
newCallback_ZGSTStraySystem_startCalculate(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_startCalculate<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_stopCalculate.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_stopCalculate.
 */
template<class T>
class CallbackNC_ZGSTStraySystem_stopCalculate : public Callback_ZGSTStraySystem_stopCalculate_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSTStraySystem_stopCalculate(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_stopCalculate(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_stopCalculate.
 */
template<class T> Callback_ZGSTStraySystem_stopCalculatePtr
newCallback_ZGSTStraySystem_stopCalculate(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_stopCalculate<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_stopCalculate.
 */
template<class T> Callback_ZGSTStraySystem_stopCalculatePtr
newCallback_ZGSTStraySystem_stopCalculate(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_stopCalculate<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_stopCalculate.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_stopCalculate.
 */
template<class T, typename CT>
class Callback_ZGSTStraySystem_stopCalculate : public Callback_ZGSTStraySystem_stopCalculate_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSTStraySystem_stopCalculate(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_stopCalculate(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_stopCalculate.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_stopCalculatePtr
newCallback_ZGSTStraySystem_stopCalculate(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_stopCalculate<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_stopCalculate.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_stopCalculatePtr
newCallback_ZGSTStraySystem_stopCalculate(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_stopCalculate<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_getValidStations.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_getValidStations.
 */
template<class T>
class CallbackNC_ZGSTStraySystem_getValidStations : public Callback_ZGSTStraySystem_getValidStations_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSTStraySystem_getValidStations(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listMapStation;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getValidStations(iceP_listMapStation, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listMapStation, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_getValidStations.
 */
template<class T> Callback_ZGSTStraySystem_getValidStationsPtr
newCallback_ZGSTStraySystem_getValidStations(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_getValidStations<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_getValidStations.
 */
template<class T> Callback_ZGSTStraySystem_getValidStationsPtr
newCallback_ZGSTStraySystem_getValidStations(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_getValidStations<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_getValidStations.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_getValidStations.
 */
template<class T, typename CT>
class Callback_ZGSTStraySystem_getValidStations : public Callback_ZGSTStraySystem_getValidStations_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSTStraySystem_getValidStations(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listMapStation;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getValidStations(iceP_listMapStation, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listMapStation, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_getValidStations.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_getValidStationsPtr
newCallback_ZGSTStraySystem_getValidStations(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_getValidStations<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_getValidStations.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_getValidStationsPtr
newCallback_ZGSTStraySystem_getValidStations(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_getValidStations<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_getSystemParam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_getSystemParam.
 */
template<class T>
class CallbackNC_ZGSTStraySystem_getSystemParam : public Callback_ZGSTStraySystem_getSystemParam_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGSTStraySystem_getSystemParam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        StringMap iceP_systemParam;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getSystemParam(iceP_systemParam, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_systemParam, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_getSystemParam.
 */
template<class T> Callback_ZGSTStraySystem_getSystemParamPtr
newCallback_ZGSTStraySystem_getSystemParam(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_getSystemParam<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_getSystemParam.
 */
template<class T> Callback_ZGSTStraySystem_getSystemParamPtr
newCallback_ZGSTStraySystem_getSystemParam(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_getSystemParam<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_getSystemParam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_getSystemParam.
 */
template<class T, typename CT>
class Callback_ZGSTStraySystem_getSystemParam : public Callback_ZGSTStraySystem_getSystemParam_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSTStraySystem_getSystemParam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        StringMap iceP_systemParam;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getSystemParam(iceP_systemParam, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_systemParam, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_getSystemParam.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_getSystemParamPtr
newCallback_ZGSTStraySystem_getSystemParam(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_getSystemParam<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_getSystemParam.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_getSystemParamPtr
newCallback_ZGSTStraySystem_getSystemParam(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_getSystemParam<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_setSystemParam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_setSystemParam.
 */
template<class T>
class CallbackNC_ZGSTStraySystem_setSystemParam : public Callback_ZGSTStraySystem_setSystemParam_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSTStraySystem_setSystemParam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setSystemParam(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_setSystemParam.
 */
template<class T> Callback_ZGSTStraySystem_setSystemParamPtr
newCallback_ZGSTStraySystem_setSystemParam(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_setSystemParam<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_setSystemParam.
 */
template<class T> Callback_ZGSTStraySystem_setSystemParamPtr
newCallback_ZGSTStraySystem_setSystemParam(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStraySystem_setSystemParam<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStraySystem::begin_setSystemParam.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStraySystem_setSystemParam.
 */
template<class T, typename CT>
class Callback_ZGSTStraySystem_setSystemParam : public Callback_ZGSTStraySystem_setSystemParam_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSTStraySystem_setSystemParam(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStraySystemPrx proxy = ZGSTStraySystemPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setSystemParam(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_setSystemParam.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_setSystemParamPtr
newCallback_ZGSTStraySystem_setSystemParam(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_setSystemParam<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStraySystem::begin_setSystemParam.
 */
template<class T, typename CT> Callback_ZGSTStraySystem_setSystemParamPtr
newCallback_ZGSTStraySystem_setSystemParam(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStraySystem_setSystemParam<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

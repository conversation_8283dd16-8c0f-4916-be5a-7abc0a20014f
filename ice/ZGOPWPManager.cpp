//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPWPManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGOPWPManager.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPWPManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPWPManager",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGOPWPManager_ops[] =
{
    "abolishWPTask",
    "backTask",
    "checkState",
    "confirmTask",
    "createWPTask",
    "deleteWPTask",
    "dispatchData",
    "editWPTask",
    "editWPUser",
    "exitApp",
    "getVersion",
    "getWPTaskList",
    "getWPUser",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGOPWPManager_getWPTaskList_name = "getWPTaskList";
const ::std::string iceC_ZG6000_ZGOPWPManager_createWPTask_name = "createWPTask";
const ::std::string iceC_ZG6000_ZGOPWPManager_editWPTask_name = "editWPTask";
const ::std::string iceC_ZG6000_ZGOPWPManager_editWPUser_name = "editWPUser";
const ::std::string iceC_ZG6000_ZGOPWPManager_deleteWPTask_name = "deleteWPTask";
const ::std::string iceC_ZG6000_ZGOPWPManager_abolishWPTask_name = "abolishWPTask";
const ::std::string iceC_ZG6000_ZGOPWPManager_confirmTask_name = "confirmTask";
const ::std::string iceC_ZG6000_ZGOPWPManager_backTask_name = "backTask";
const ::std::string iceC_ZG6000_ZGOPWPManager_getWPUser_name = "getWPUser";

}

bool
ZG6000::ZGOPWPManager::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPWPManager_ids, iceC_ZG6000_ZGOPWPManager_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGOPWPManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGOPWPManager_ids[0], &iceC_ZG6000_ZGOPWPManager_ids[3]);
}

::std::string
ZG6000::ZGOPWPManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPWPManager::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGOPWPManager";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_getWPTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_param;
    istr->readAll(iceP_param);
    inS.endReadParams();
    ListStringMap iceP_listTask;
    ErrorInfo iceP_e;
    bool ret = this->getWPTaskList(::std::move(iceP_param), iceP_listTask, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listTask, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_createWPTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_param;
    StringList iceP_listUserID;
    istr->readAll(iceP_param, iceP_listUserID);
    inS.endReadParams();
    ::std::string iceP_taskID;
    ErrorInfo iceP_e;
    bool ret = this->createWPTask(::std::move(iceP_param), ::std::move(iceP_listUserID), iceP_taskID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_taskID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_editWPTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->editWPTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_editWPUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringList iceP_listUserID;
    istr->readAll(iceP_taskID, iceP_listUserID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->editWPUser(::std::move(iceP_taskID), ::std::move(iceP_listUserID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_deleteWPTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteWPTask(::std::move(iceP_taskID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_abolishWPTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->abolishWPTask(::std::move(iceP_taskID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_confirmTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_backTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->backTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_getWPUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_listUser;
    ErrorInfo iceP_e;
    bool ret = this->getWPUser(::std::move(iceP_taskID), iceP_listUser, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listUser, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPWPManager_ops, iceC_ZG6000_ZGOPWPManager_ops + 24, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPWPManager_ops)
    {
        case 0:
        {
            return _iceD_abolishWPTask(in, current);
        }
        case 1:
        {
            return _iceD_backTask(in, current);
        }
        case 2:
        {
            return _iceD_checkState(in, current);
        }
        case 3:
        {
            return _iceD_confirmTask(in, current);
        }
        case 4:
        {
            return _iceD_createWPTask(in, current);
        }
        case 5:
        {
            return _iceD_deleteWPTask(in, current);
        }
        case 6:
        {
            return _iceD_dispatchData(in, current);
        }
        case 7:
        {
            return _iceD_editWPTask(in, current);
        }
        case 8:
        {
            return _iceD_editWPUser(in, current);
        }
        case 9:
        {
            return _iceD_exitApp(in, current);
        }
        case 10:
        {
            return _iceD_getVersion(in, current);
        }
        case 11:
        {
            return _iceD_getWPTaskList(in, current);
        }
        case 12:
        {
            return _iceD_getWPUser(in, current);
        }
        case 13:
        {
            return _iceD_heartDebug(in, current);
        }
        case 14:
        {
            return _iceD_ice_id(in, current);
        }
        case 15:
        {
            return _iceD_ice_ids(in, current);
        }
        case 16:
        {
            return _iceD_ice_isA(in, current);
        }
        case 17:
        {
            return _iceD_ice_ping(in, current);
        }
        case 18:
        {
            return _iceD_isDebugging(in, current);
        }
        case 19:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 20:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 21:
        {
            return _iceD_startDebug(in, current);
        }
        case 22:
        {
            return _iceD_stopDebug(in, current);
        }
        case 23:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPWPManagerPrx::_iceI_getWPTaskList(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::GetWPTaskListResult>>& outAsync, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_getWPTaskList_name);
    outAsync->invoke(iceC_ZG6000_ZGOPWPManager_getWPTaskList_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPWPManager::GetWPTaskListResult v;
            istr->readAll(v.listTask, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPWPManagerPrx::_iceI_createWPTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::CreateWPTaskResult>>& outAsync, const StringMap& iceP_param, const StringList& iceP_listUserID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_createWPTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPWPManager_createWPTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_param, iceP_listUserID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPWPManager::CreateWPTaskResult v;
            istr->readAll(v.taskID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPWPManagerPrx::_iceI_editWPTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::EditWPTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_editWPTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPWPManager_editWPTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPWPManager::EditWPTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPWPManagerPrx::_iceI_editWPUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::EditWPUserResult>>& outAsync, const ::std::string& iceP_taskID, const StringList& iceP_listUserID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_editWPUser_name);
    outAsync->invoke(iceC_ZG6000_ZGOPWPManager_editWPUser_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_listUserID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPWPManager::EditWPUserResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPWPManagerPrx::_iceI_deleteWPTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::DeleteWPTaskResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_deleteWPTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPWPManager_deleteWPTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPWPManager::DeleteWPTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPWPManagerPrx::_iceI_abolishWPTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::AbolishWPTaskResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_abolishWPTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPWPManager_abolishWPTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPWPManager::AbolishWPTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPWPManagerPrx::_iceI_confirmTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::ConfirmTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_confirmTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPWPManager_confirmTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPWPManager::ConfirmTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPWPManagerPrx::_iceI_backTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::BackTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_backTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPWPManager_backTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPWPManager::BackTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPWPManagerPrx::_iceI_getWPUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPWPManager::GetWPUserResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_getWPUser_name);
    outAsync->invoke(iceC_ZG6000_ZGOPWPManager_getWPUser_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPWPManager::GetWPUserResult v;
            istr->readAll(v.listUser, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGOPWPManagerPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGOPWPManagerPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGOPWPManagerPrx::ice_staticId()
{
    return ZGOPWPManager::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPWPManager_getWPTaskList_name = "getWPTaskList";

const ::std::string iceC_ZG6000_ZGOPWPManager_createWPTask_name = "createWPTask";

const ::std::string iceC_ZG6000_ZGOPWPManager_editWPTask_name = "editWPTask";

const ::std::string iceC_ZG6000_ZGOPWPManager_editWPUser_name = "editWPUser";

const ::std::string iceC_ZG6000_ZGOPWPManager_deleteWPTask_name = "deleteWPTask";

const ::std::string iceC_ZG6000_ZGOPWPManager_abolishWPTask_name = "abolishWPTask";

const ::std::string iceC_ZG6000_ZGOPWPManager_confirmTask_name = "confirmTask";

const ::std::string iceC_ZG6000_ZGOPWPManager_backTask_name = "backTask";

const ::std::string iceC_ZG6000_ZGOPWPManager_getWPUser_name = "getWPUser";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGOPWPManager* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGOPWPManager>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGOPWPManager;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPWPManager::_iceI_begin_getWPTaskList(const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_getWPTaskList_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPWPManager_getWPTaskList_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPWPManager_getWPTaskList_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPWPManager_getWPTaskList_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPWPManager::end_getWPTaskList(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_getWPTaskList_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTask);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPWPManager::_iceI_end_getWPTaskList(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_getWPTaskList_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTask);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPWPManager::_iceI_begin_createWPTask(const ::ZG6000::StringMap& iceP_param, const ::ZG6000::StringList& iceP_listUserID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_createWPTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPWPManager_createWPTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPWPManager_createWPTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_param);
        ostr->write(iceP_listUserID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPWPManager_createWPTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPWPManager::end_createWPTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_createWPTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPWPManager::_iceI_end_createWPTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_createWPTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPWPManager::_iceI_begin_editWPTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_editWPTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPWPManager_editWPTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPWPManager_editWPTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPWPManager_editWPTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPWPManager::end_editWPTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_editWPTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPWPManager::_iceI_end_editWPTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_editWPTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPWPManager::_iceI_begin_editWPUser(const ::std::string& iceP_taskID, const ::ZG6000::StringList& iceP_listUserID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_editWPUser_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPWPManager_editWPUser_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPWPManager_editWPUser_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_listUserID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPWPManager_editWPUser_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPWPManager::end_editWPUser(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_editWPUser_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPWPManager::_iceI_end_editWPUser(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_editWPUser_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPWPManager::_iceI_begin_deleteWPTask(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_deleteWPTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPWPManager_deleteWPTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPWPManager_deleteWPTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPWPManager_deleteWPTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPWPManager::end_deleteWPTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_deleteWPTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPWPManager::_iceI_end_deleteWPTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_deleteWPTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPWPManager::_iceI_begin_abolishWPTask(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_abolishWPTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPWPManager_abolishWPTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPWPManager_abolishWPTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPWPManager_abolishWPTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPWPManager::end_abolishWPTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_abolishWPTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPWPManager::_iceI_end_abolishWPTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_abolishWPTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPWPManager::_iceI_begin_confirmTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_confirmTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPWPManager_confirmTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPWPManager_confirmTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPWPManager_confirmTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPWPManager::end_confirmTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_confirmTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPWPManager::_iceI_end_confirmTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_confirmTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPWPManager::_iceI_begin_backTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_backTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPWPManager_backTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPWPManager_backTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPWPManager_backTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPWPManager::end_backTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_backTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPWPManager::_iceI_end_backTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_backTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPWPManager::_iceI_begin_getWPUser(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPWPManager_getWPUser_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPWPManager_getWPUser_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPWPManager_getWPUser_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPWPManager_getWPUser_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPWPManager::end_getWPUser(::ZG6000::ListStringMap& iceP_listUser, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_getWPUser_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listUser);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPWPManager::_iceI_end_getWPUser(::ZG6000::ListStringMap& iceP_listUser, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPWPManager_getWPUser_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listUser);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGOPWPManager::_newInstance() const
{
    return new ZGOPWPManager;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGOPWPManager::ice_staticId()
{
    return ::ZG6000::ZGOPWPManager::ice_staticId();
}

ZG6000::ZGOPWPManager::~ZGOPWPManager()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGOPWPManager* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPWPManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPWPManager",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGOPWPManager::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPWPManager_ids, iceC_ZG6000_ZGOPWPManager_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGOPWPManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGOPWPManager_ids[0], &iceC_ZG6000_ZGOPWPManager_ids[3]);
}

const ::std::string&
ZG6000::ZGOPWPManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPWPManager::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGOPWPManager";
    return typeId;
#else
    return iceC_ZG6000_ZGOPWPManager_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_getWPTaskList(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_param;
    istr->read(iceP_param);
    inS.endReadParams();
    ListStringMap iceP_listTask;
    ErrorInfo iceP_e;
    bool ret = this->getWPTaskList(iceP_param, iceP_listTask, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listTask);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_createWPTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_param;
    StringList iceP_listUserID;
    istr->read(iceP_param);
    istr->read(iceP_listUserID);
    inS.endReadParams();
    ::std::string iceP_taskID;
    ErrorInfo iceP_e;
    bool ret = this->createWPTask(iceP_param, iceP_listUserID, iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_taskID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_editWPTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->editWPTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_editWPUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringList iceP_listUserID;
    istr->read(iceP_taskID);
    istr->read(iceP_listUserID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->editWPUser(iceP_taskID, iceP_listUserID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_deleteWPTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteWPTask(iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_abolishWPTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->abolishWPTask(iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_confirmTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_backTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->backTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceD_getWPUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_listUser;
    ErrorInfo iceP_e;
    bool ret = this->getWPUser(iceP_taskID, iceP_listUser, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listUser);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPWPManager_all[] =
{
    "abolishWPTask",
    "backTask",
    "checkState",
    "confirmTask",
    "createWPTask",
    "deleteWPTask",
    "dispatchData",
    "editWPTask",
    "editWPUser",
    "exitApp",
    "getVersion",
    "getWPTaskList",
    "getWPUser",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGOPWPManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPWPManager_all, iceC_ZG6000_ZGOPWPManager_all + 24, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPWPManager_all)
    {
        case 0:
        {
            return _iceD_abolishWPTask(in, current);
        }
        case 1:
        {
            return _iceD_backTask(in, current);
        }
        case 2:
        {
            return _iceD_checkState(in, current);
        }
        case 3:
        {
            return _iceD_confirmTask(in, current);
        }
        case 4:
        {
            return _iceD_createWPTask(in, current);
        }
        case 5:
        {
            return _iceD_deleteWPTask(in, current);
        }
        case 6:
        {
            return _iceD_dispatchData(in, current);
        }
        case 7:
        {
            return _iceD_editWPTask(in, current);
        }
        case 8:
        {
            return _iceD_editWPUser(in, current);
        }
        case 9:
        {
            return _iceD_exitApp(in, current);
        }
        case 10:
        {
            return _iceD_getVersion(in, current);
        }
        case 11:
        {
            return _iceD_getWPTaskList(in, current);
        }
        case 12:
        {
            return _iceD_getWPUser(in, current);
        }
        case 13:
        {
            return _iceD_heartDebug(in, current);
        }
        case 14:
        {
            return _iceD_ice_id(in, current);
        }
        case 15:
        {
            return _iceD_ice_ids(in, current);
        }
        case 16:
        {
            return _iceD_ice_isA(in, current);
        }
        case 17:
        {
            return _iceD_ice_ping(in, current);
        }
        case 18:
        {
            return _iceD_isDebugging(in, current);
        }
        case 19:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 20:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 21:
        {
            return _iceD_startDebug(in, current);
        }
        case 22:
        {
            return _iceD_stopDebug(in, current);
        }
        case 23:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGOPWPManager::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGOPWPManager, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGOPWPManager::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGOPWPManager, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGOPWPManagerPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGOPWPManagerPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGOPWPManager::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

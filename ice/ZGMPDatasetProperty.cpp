//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPDatasetProperty.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGMPDatasetProperty.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPDatasetProperty",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getDataIDByProperty",
    "getProperties",
    "getPropertiesAll",
    "getProperty",
    "getPropertyByDataID",
    "getPropertyValue",
    "getPropertyValues",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "isPropertyExists",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test",
    "updateProperties",
    "updateProperty",
    "updatePropertyValue",
    "updatePropertyValues"
};
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getPropertiesAll_name = "getPropertiesAll";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getProperties_name = "getProperties";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getProperty_name = "getProperty";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getPropertyValues_name = "getPropertyValues";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getPropertyValue_name = "getPropertyValue";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_updateProperties_name = "updateProperties";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_updateProperty_name = "updateProperty";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValues_name = "updatePropertyValues";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValue_name = "updatePropertyValue";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getDataIDByProperty_name = "getDataIDByProperty";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getPropertyByDataID_name = "getPropertyByDataID";
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_isPropertyExists_name = "isPropertyExists";

}

bool
ZG6000::ZGMPDatasetProperty::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPDatasetProperty_ids, iceC_ZG6000_ZGMPDatasetProperty_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGMPDatasetProperty::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGMPDatasetProperty_ids[0], &iceC_ZG6000_ZGMPDatasetProperty_ids[3]);
}

::std::string
ZG6000::ZGMPDatasetProperty::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPDatasetProperty::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGMPDatasetProperty";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getPropertiesAll(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    istr->readAll(iceP_datasetID);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getPropertiesAll(::std::move(iceP_datasetID), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    StringList iceP_listName;
    istr->readAll(iceP_datasetID, iceP_listName);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getProperties(::std::move(iceP_datasetID), ::std::move(iceP_listName), iceP_properties, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_properties, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    istr->readAll(iceP_datasetID, iceP_name);
    inS.endReadParams();
    StringMap iceP_property;
    ErrorInfo iceP_e;
    bool ret = this->getProperty(::std::move(iceP_datasetID), ::std::move(iceP_name), iceP_property, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_property, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getPropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    StringList iceP_listName;
    istr->readAll(iceP_datasetID, iceP_listName);
    inS.endReadParams();
    StringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValues(::std::move(iceP_datasetID), ::std::move(iceP_listName), iceP_values, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_values, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getPropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    istr->readAll(iceP_datasetID, iceP_name);
    inS.endReadParams();
    ::std::string iceP_value;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValue(::std::move(iceP_datasetID), ::std::move(iceP_name), iceP_value, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_value, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_updateProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    MapStringMap iceP_properties;
    bool iceP_saveToDB;
    istr->readAll(iceP_datasetID, iceP_properties, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateProperties(::std::move(iceP_datasetID), ::std::move(iceP_properties), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_updateProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    StringMap iceP_property;
    bool iceP_saveToDB;
    istr->readAll(iceP_datasetID, iceP_name, iceP_property, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateProperty(::std::move(iceP_datasetID), ::std::move(iceP_name), ::std::move(iceP_property), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_updatePropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    StringMap iceP_values;
    bool iceP_saveToDB;
    istr->readAll(iceP_datasetID, iceP_values, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValues(::std::move(iceP_datasetID), ::std::move(iceP_values), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_updatePropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    ::std::string iceP_value;
    bool iceP_saveToDB;
    istr->readAll(iceP_datasetID, iceP_name, iceP_value, iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValue(::std::move(iceP_datasetID), ::std::move(iceP_name), ::std::move(iceP_value), iceP_saveToDB, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getDataIDByProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    istr->readAll(iceP_datasetID, iceP_name);
    inS.endReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_dataID;
    ErrorInfo iceP_e;
    bool ret = this->getDataIDByProperty(::std::move(iceP_datasetID), ::std::move(iceP_name), iceP_tableName, iceP_dataID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_tableName, iceP_dataID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getPropertyByDataID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_dataID;
    istr->readAll(iceP_dataID);
    inS.endReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyByDataID(::std::move(iceP_dataID), iceP_datasetID, iceP_name, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_datasetID, iceP_name, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_isPropertyExists(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    istr->readAll(iceP_datasetID, iceP_name);
    inS.endReadParams();
    bool iceP_exists;
    ErrorInfo iceP_e;
    bool ret = this->isPropertyExists(::std::move(iceP_datasetID), ::std::move(iceP_name), iceP_exists, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_exists, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPDatasetProperty_ops, iceC_ZG6000_ZGMPDatasetProperty_ops + 27, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPDatasetProperty_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getDataIDByProperty(in, current);
        }
        case 4:
        {
            return _iceD_getProperties(in, current);
        }
        case 5:
        {
            return _iceD_getPropertiesAll(in, current);
        }
        case 6:
        {
            return _iceD_getProperty(in, current);
        }
        case 7:
        {
            return _iceD_getPropertyByDataID(in, current);
        }
        case 8:
        {
            return _iceD_getPropertyValue(in, current);
        }
        case 9:
        {
            return _iceD_getPropertyValues(in, current);
        }
        case 10:
        {
            return _iceD_getVersion(in, current);
        }
        case 11:
        {
            return _iceD_heartDebug(in, current);
        }
        case 12:
        {
            return _iceD_ice_id(in, current);
        }
        case 13:
        {
            return _iceD_ice_ids(in, current);
        }
        case 14:
        {
            return _iceD_ice_isA(in, current);
        }
        case 15:
        {
            return _iceD_ice_ping(in, current);
        }
        case 16:
        {
            return _iceD_isDebugging(in, current);
        }
        case 17:
        {
            return _iceD_isPropertyExists(in, current);
        }
        case 18:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 19:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 20:
        {
            return _iceD_startDebug(in, current);
        }
        case 21:
        {
            return _iceD_stopDebug(in, current);
        }
        case 22:
        {
            return _iceD_test(in, current);
        }
        case 23:
        {
            return _iceD_updateProperties(in, current);
        }
        case 24:
        {
            return _iceD_updateProperty(in, current);
        }
        case 25:
        {
            return _iceD_updatePropertyValue(in, current);
        }
        case 26:
        {
            return _iceD_updatePropertyValues(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_getPropertiesAll(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertiesAllResult>>& outAsync, const ::std::string& iceP_datasetID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getPropertiesAll_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_getPropertiesAll_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::GetPropertiesAllResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_getProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertiesResult>>& outAsync, const ::std::string& iceP_datasetID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_getProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::GetPropertiesResult v;
            istr->readAll(v.properties, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_getProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertyResult>>& outAsync, const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getProperty_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_getProperty_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID, iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::GetPropertyResult v;
            istr->readAll(v.property, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_getPropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertyValuesResult>>& outAsync, const ::std::string& iceP_datasetID, const StringList& iceP_listName, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getPropertyValues_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_getPropertyValues_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID, iceP_listName);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::GetPropertyValuesResult v;
            istr->readAll(v.values, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_getPropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertyValueResult>>& outAsync, const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getPropertyValue_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_getPropertyValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID, iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::GetPropertyValueResult v;
            istr->readAll(v.value, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_updateProperties(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::UpdatePropertiesResult>>& outAsync, const ::std::string& iceP_datasetID, const MapStringMap& iceP_properties, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_updateProperties_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_updateProperties_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID, iceP_properties, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::UpdatePropertiesResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_updateProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::UpdatePropertyResult>>& outAsync, const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const StringMap& iceP_property, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_updateProperty_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_updateProperty_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID, iceP_name, iceP_property, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::UpdatePropertyResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_updatePropertyValues(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::UpdatePropertyValuesResult>>& outAsync, const ::std::string& iceP_datasetID, const StringMap& iceP_values, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValues_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValues_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID, iceP_values, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::UpdatePropertyValuesResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_updatePropertyValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::UpdatePropertyValueResult>>& outAsync, const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::std::string& iceP_value, bool iceP_saveToDB, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValue_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID, iceP_name, iceP_value, iceP_saveToDB);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::UpdatePropertyValueResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_getDataIDByProperty(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetDataIDByPropertyResult>>& outAsync, const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getDataIDByProperty_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_getDataIDByProperty_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID, iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::GetDataIDByPropertyResult v;
            istr->readAll(v.tableName, v.dataID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_getPropertyByDataID(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::GetPropertyByDataIDResult>>& outAsync, const ::std::string& iceP_dataID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getPropertyByDataID_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_getPropertyByDataID_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::GetPropertyByDataIDResult v;
            istr->readAll(v.datasetID, v.name, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPDatasetPropertyPrx::_iceI_isPropertyExists(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPDatasetProperty::IsPropertyExistsResult>>& outAsync, const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_isPropertyExists_name);
    outAsync->invoke(iceC_ZG6000_ZGMPDatasetProperty_isPropertyExists_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_datasetID, iceP_name);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPDatasetProperty::IsPropertyExistsResult v;
            istr->readAll(v.exists, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGMPDatasetPropertyPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGMPDatasetPropertyPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGMPDatasetPropertyPrx::ice_staticId()
{
    return ZGMPDatasetProperty::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getPropertiesAll_name = "getPropertiesAll";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getProperties_name = "getProperties";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getProperty_name = "getProperty";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getPropertyValues_name = "getPropertyValues";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getPropertyValue_name = "getPropertyValue";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_updateProperties_name = "updateProperties";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_updateProperty_name = "updateProperty";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValues_name = "updatePropertyValues";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValue_name = "updatePropertyValue";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getDataIDByProperty_name = "getDataIDByProperty";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_getPropertyByDataID_name = "getPropertyByDataID";

const ::std::string iceC_ZG6000_ZGMPDatasetProperty_isPropertyExists_name = "isPropertyExists";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGMPDatasetProperty* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGMPDatasetProperty>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGMPDatasetProperty;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_getPropertiesAll(const ::std::string& iceP_datasetID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getPropertiesAll_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_getPropertiesAll_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_getPropertiesAll_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_getPropertiesAll_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_getPropertiesAll(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getPropertiesAll_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_getPropertiesAll(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getPropertiesAll_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_getProperties(const ::std::string& iceP_datasetID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_getProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_getProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_getProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_getProperties(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_getProperties(::ZG6000::MapStringMap& iceP_properties, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_properties);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_getProperty(const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getProperty_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_getProperty_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_getProperty_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_getProperty_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_getProperty(::ZG6000::StringMap& iceP_property, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getProperty_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_property);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_getProperty(::ZG6000::StringMap& iceP_property, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getProperty_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_property);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_getPropertyValues(const ::std::string& iceP_datasetID, const ::ZG6000::StringList& iceP_listName, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getPropertyValues_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_getPropertyValues_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_getPropertyValues_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_listName);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_getPropertyValues_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_getPropertyValues(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getPropertyValues_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_getPropertyValues(::ZG6000::StringMap& iceP_values, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getPropertyValues_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_values);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_getPropertyValue(const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getPropertyValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_getPropertyValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_getPropertyValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_getPropertyValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_getPropertyValue(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getPropertyValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_value);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_getPropertyValue(::std::string& iceP_value, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getPropertyValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_value);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_updateProperties(const ::std::string& iceP_datasetID, const ::ZG6000::MapStringMap& iceP_properties, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_updateProperties_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_updateProperties_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_updateProperties_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_properties);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_updateProperties_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_updateProperties(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_updateProperties_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_updateProperties(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_updateProperties_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_updateProperty(const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::ZG6000::StringMap& iceP_property, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_updateProperty_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_updateProperty_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_updateProperty_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_name);
        ostr->write(iceP_property);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_updateProperty_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_updateProperty(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_updateProperty_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_updateProperty(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_updateProperty_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_updatePropertyValues(const ::std::string& iceP_datasetID, const ::ZG6000::StringMap& iceP_values, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValues_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValues_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValues_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_values);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValues_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_updatePropertyValues(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValues_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_updatePropertyValues(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValues_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_updatePropertyValue(const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::std::string& iceP_value, bool iceP_saveToDB, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_name);
        ostr->write(iceP_value);
        ostr->write(iceP_saveToDB);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_updatePropertyValue(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_updatePropertyValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_updatePropertyValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_getDataIDByProperty(const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getDataIDByProperty_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_getDataIDByProperty_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_getDataIDByProperty_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_getDataIDByProperty_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_getDataIDByProperty(::std::string& iceP_tableName, ::std::string& iceP_dataID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getDataIDByProperty_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_tableName);
    istr->read(iceP_dataID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_getDataIDByProperty(::std::string& iceP_tableName, ::std::string& iceP_dataID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getDataIDByProperty_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_tableName);
    istr->read(iceP_dataID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_getPropertyByDataID(const ::std::string& iceP_dataID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_getPropertyByDataID_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_getPropertyByDataID_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_getPropertyByDataID_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_getPropertyByDataID_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_getPropertyByDataID(::std::string& iceP_datasetID, ::std::string& iceP_name, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getPropertyByDataID_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_datasetID);
    istr->read(iceP_name);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_getPropertyByDataID(::std::string& iceP_datasetID, ::std::string& iceP_name, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_getPropertyByDataID_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_datasetID);
    istr->read(iceP_name);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_begin_isPropertyExists(const ::std::string& iceP_datasetID, const ::std::string& iceP_name, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPDatasetProperty_isPropertyExists_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPDatasetProperty_isPropertyExists_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPDatasetProperty_isPropertyExists_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_datasetID);
        ostr->write(iceP_name);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPDatasetProperty_isPropertyExists_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPDatasetProperty::end_isPropertyExists(bool& iceP_exists, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_isPropertyExists_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_exists);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPDatasetProperty::_iceI_end_isPropertyExists(bool& iceP_exists, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPDatasetProperty_isPropertyExists_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_exists);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGMPDatasetProperty::_newInstance() const
{
    return new ZGMPDatasetProperty;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGMPDatasetProperty::ice_staticId()
{
    return ::ZG6000::ZGMPDatasetProperty::ice_staticId();
}

ZG6000::ZGMPDatasetProperty::~ZGMPDatasetProperty()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGMPDatasetProperty* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPDatasetProperty",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGMPDatasetProperty::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPDatasetProperty_ids, iceC_ZG6000_ZGMPDatasetProperty_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGMPDatasetProperty::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGMPDatasetProperty_ids[0], &iceC_ZG6000_ZGMPDatasetProperty_ids[3]);
}

const ::std::string&
ZG6000::ZGMPDatasetProperty::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPDatasetProperty::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGMPDatasetProperty";
    return typeId;
#else
    return iceC_ZG6000_ZGMPDatasetProperty_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getPropertiesAll(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    istr->read(iceP_datasetID);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getPropertiesAll(iceP_datasetID, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    StringList iceP_listName;
    istr->read(iceP_datasetID);
    istr->read(iceP_listName);
    inS.endReadParams();
    MapStringMap iceP_properties;
    ErrorInfo iceP_e;
    bool ret = this->getProperties(iceP_datasetID, iceP_listName, iceP_properties, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_properties);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    istr->read(iceP_datasetID);
    istr->read(iceP_name);
    inS.endReadParams();
    StringMap iceP_property;
    ErrorInfo iceP_e;
    bool ret = this->getProperty(iceP_datasetID, iceP_name, iceP_property, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_property);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getPropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    StringList iceP_listName;
    istr->read(iceP_datasetID);
    istr->read(iceP_listName);
    inS.endReadParams();
    StringMap iceP_values;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValues(iceP_datasetID, iceP_listName, iceP_values, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_values);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getPropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    istr->read(iceP_datasetID);
    istr->read(iceP_name);
    inS.endReadParams();
    ::std::string iceP_value;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyValue(iceP_datasetID, iceP_name, iceP_value, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_value);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_updateProperties(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    MapStringMap iceP_properties;
    bool iceP_saveToDB;
    istr->read(iceP_datasetID);
    istr->read(iceP_properties);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateProperties(iceP_datasetID, iceP_properties, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_updateProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    StringMap iceP_property;
    bool iceP_saveToDB;
    istr->read(iceP_datasetID);
    istr->read(iceP_name);
    istr->read(iceP_property);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateProperty(iceP_datasetID, iceP_name, iceP_property, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_updatePropertyValues(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    StringMap iceP_values;
    bool iceP_saveToDB;
    istr->read(iceP_datasetID);
    istr->read(iceP_values);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValues(iceP_datasetID, iceP_values, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_updatePropertyValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    ::std::string iceP_value;
    bool iceP_saveToDB;
    istr->read(iceP_datasetID);
    istr->read(iceP_name);
    istr->read(iceP_value);
    istr->read(iceP_saveToDB);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updatePropertyValue(iceP_datasetID, iceP_name, iceP_value, iceP_saveToDB, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getDataIDByProperty(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    istr->read(iceP_datasetID);
    istr->read(iceP_name);
    inS.endReadParams();
    ::std::string iceP_tableName;
    ::std::string iceP_dataID;
    ErrorInfo iceP_e;
    bool ret = this->getDataIDByProperty(iceP_datasetID, iceP_name, iceP_tableName, iceP_dataID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_tableName);
    ostr->write(iceP_dataID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_getPropertyByDataID(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_dataID;
    istr->read(iceP_dataID);
    inS.endReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    ErrorInfo iceP_e;
    bool ret = this->getPropertyByDataID(iceP_dataID, iceP_datasetID, iceP_name, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_datasetID);
    ostr->write(iceP_name);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceD_isPropertyExists(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_datasetID;
    ::std::string iceP_name;
    istr->read(iceP_datasetID);
    istr->read(iceP_name);
    inS.endReadParams();
    bool iceP_exists;
    ErrorInfo iceP_e;
    bool ret = this->isPropertyExists(iceP_datasetID, iceP_name, iceP_exists, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_exists);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPDatasetProperty_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getDataIDByProperty",
    "getProperties",
    "getPropertiesAll",
    "getProperty",
    "getPropertyByDataID",
    "getPropertyValue",
    "getPropertyValues",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "isPropertyExists",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test",
    "updateProperties",
    "updateProperty",
    "updatePropertyValue",
    "updatePropertyValues"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGMPDatasetProperty::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPDatasetProperty_all, iceC_ZG6000_ZGMPDatasetProperty_all + 27, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPDatasetProperty_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getDataIDByProperty(in, current);
        }
        case 4:
        {
            return _iceD_getProperties(in, current);
        }
        case 5:
        {
            return _iceD_getPropertiesAll(in, current);
        }
        case 6:
        {
            return _iceD_getProperty(in, current);
        }
        case 7:
        {
            return _iceD_getPropertyByDataID(in, current);
        }
        case 8:
        {
            return _iceD_getPropertyValue(in, current);
        }
        case 9:
        {
            return _iceD_getPropertyValues(in, current);
        }
        case 10:
        {
            return _iceD_getVersion(in, current);
        }
        case 11:
        {
            return _iceD_heartDebug(in, current);
        }
        case 12:
        {
            return _iceD_ice_id(in, current);
        }
        case 13:
        {
            return _iceD_ice_ids(in, current);
        }
        case 14:
        {
            return _iceD_ice_isA(in, current);
        }
        case 15:
        {
            return _iceD_ice_ping(in, current);
        }
        case 16:
        {
            return _iceD_isDebugging(in, current);
        }
        case 17:
        {
            return _iceD_isPropertyExists(in, current);
        }
        case 18:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 19:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 20:
        {
            return _iceD_startDebug(in, current);
        }
        case 21:
        {
            return _iceD_stopDebug(in, current);
        }
        case 22:
        {
            return _iceD_test(in, current);
        }
        case 23:
        {
            return _iceD_updateProperties(in, current);
        }
        case 24:
        {
            return _iceD_updateProperty(in, current);
        }
        case 25:
        {
            return _iceD_updatePropertyValue(in, current);
        }
        case 26:
        {
            return _iceD_updatePropertyValues(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGMPDatasetProperty::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGMPDatasetProperty, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGMPDatasetProperty::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGMPDatasetProperty, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGMPDatasetPropertyPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGMPDatasetPropertyPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGMPDatasetProperty::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPRealWarn.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGMPRealWarn.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPRealWarn_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPRealWarn",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGMPRealWarn_ops[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getRealWarnByAppNode",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};
const ::std::string iceC_ZG6000_ZGMPRealWarn_getRealWarnByAppNode_name = "getRealWarnByAppNode";

}

bool
ZG6000::ZGMPRealWarn::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPRealWarn_ids, iceC_ZG6000_ZGMPRealWarn_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGMPRealWarn::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGMPRealWarn_ids[0], &iceC_ZG6000_ZGMPRealWarn_ids[3]);
}

::std::string
ZG6000::ZGMPRealWarn::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPRealWarn::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGMPRealWarn";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGMPRealWarn::_iceD_getRealWarnByAppNode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    ::std::string iceP_subsystemID;
    istr->readAll(iceP_appNodeID, iceP_subsystemID);
    inS.endReadParams();
    ListStringMap iceP_listRealWarn;
    ErrorInfo iceP_e;
    bool ret = this->getRealWarnByAppNode(::std::move(iceP_appNodeID), ::std::move(iceP_subsystemID), iceP_listRealWarn, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listRealWarn, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGMPRealWarn::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPRealWarn_ops, iceC_ZG6000_ZGMPRealWarn_ops + 16, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPRealWarn_ops)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getRealWarnByAppNode(in, current);
        }
        case 4:
        {
            return _iceD_getVersion(in, current);
        }
        case 5:
        {
            return _iceD_heartDebug(in, current);
        }
        case 6:
        {
            return _iceD_ice_id(in, current);
        }
        case 7:
        {
            return _iceD_ice_ids(in, current);
        }
        case 8:
        {
            return _iceD_ice_isA(in, current);
        }
        case 9:
        {
            return _iceD_ice_ping(in, current);
        }
        case 10:
        {
            return _iceD_isDebugging(in, current);
        }
        case 11:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 12:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 13:
        {
            return _iceD_startDebug(in, current);
        }
        case 14:
        {
            return _iceD_stopDebug(in, current);
        }
        case 15:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGMPRealWarnPrx::_iceI_getRealWarnByAppNode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRealWarn::GetRealWarnByAppNodeResult>>& outAsync, const ::std::string& iceP_appNodeID, const ::std::string& iceP_subsystemID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRealWarn_getRealWarnByAppNode_name);
    outAsync->invoke(iceC_ZG6000_ZGMPRealWarn_getRealWarnByAppNode_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_appNodeID, iceP_subsystemID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGMPRealWarn::GetRealWarnByAppNodeResult v;
            istr->readAll(v.listRealWarn, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGMPRealWarnPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGMPRealWarnPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGMPRealWarnPrx::ice_staticId()
{
    return ZGMPRealWarn::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGMPRealWarn_getRealWarnByAppNode_name = "getRealWarnByAppNode";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGMPRealWarn* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGMPRealWarn>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGMPRealWarn;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGMPRealWarn::_iceI_begin_getRealWarnByAppNode(const ::std::string& iceP_appNodeID, const ::std::string& iceP_subsystemID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGMPRealWarn_getRealWarnByAppNode_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGMPRealWarn_getRealWarnByAppNode_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGMPRealWarn_getRealWarnByAppNode_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_subsystemID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGMPRealWarn_getRealWarnByAppNode_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGMPRealWarn::end_getRealWarnByAppNode(::ZG6000::ListStringMap& iceP_listRealWarn, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRealWarn_getRealWarnByAppNode_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listRealWarn);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGMPRealWarn::_iceI_end_getRealWarnByAppNode(::ZG6000::ListStringMap& iceP_listRealWarn, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGMPRealWarn_getRealWarnByAppNode_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listRealWarn);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGMPRealWarn::_newInstance() const
{
    return new ZGMPRealWarn;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGMPRealWarn::ice_staticId()
{
    return ::ZG6000::ZGMPRealWarn::ice_staticId();
}

ZG6000::ZGMPRealWarn::~ZGMPRealWarn()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGMPRealWarn* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPRealWarn_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGMPRealWarn",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGMPRealWarn::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGMPRealWarn_ids, iceC_ZG6000_ZGMPRealWarn_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGMPRealWarn::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGMPRealWarn_ids[0], &iceC_ZG6000_ZGMPRealWarn_ids[3]);
}

const ::std::string&
ZG6000::ZGMPRealWarn::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGMPRealWarn::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGMPRealWarn";
    return typeId;
#else
    return iceC_ZG6000_ZGMPRealWarn_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGMPRealWarn::_iceD_getRealWarnByAppNode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_appNodeID;
    ::std::string iceP_subsystemID;
    istr->read(iceP_appNodeID);
    istr->read(iceP_subsystemID);
    inS.endReadParams();
    ListStringMap iceP_listRealWarn;
    ErrorInfo iceP_e;
    bool ret = this->getRealWarnByAppNode(iceP_appNodeID, iceP_subsystemID, iceP_listRealWarn, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listRealWarn);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGMPRealWarn_all[] =
{
    "checkState",
    "dispatchData",
    "exitApp",
    "getRealWarnByAppNode",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "resumeDebug",
    "startDebug",
    "stopDebug",
    "test"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGMPRealWarn::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGMPRealWarn_all, iceC_ZG6000_ZGMPRealWarn_all + 16, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGMPRealWarn_all)
    {
        case 0:
        {
            return _iceD_checkState(in, current);
        }
        case 1:
        {
            return _iceD_dispatchData(in, current);
        }
        case 2:
        {
            return _iceD_exitApp(in, current);
        }
        case 3:
        {
            return _iceD_getRealWarnByAppNode(in, current);
        }
        case 4:
        {
            return _iceD_getVersion(in, current);
        }
        case 5:
        {
            return _iceD_heartDebug(in, current);
        }
        case 6:
        {
            return _iceD_ice_id(in, current);
        }
        case 7:
        {
            return _iceD_ice_ids(in, current);
        }
        case 8:
        {
            return _iceD_ice_isA(in, current);
        }
        case 9:
        {
            return _iceD_ice_ping(in, current);
        }
        case 10:
        {
            return _iceD_isDebugging(in, current);
        }
        case 11:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 12:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 13:
        {
            return _iceD_startDebug(in, current);
        }
        case 14:
        {
            return _iceD_stopDebug(in, current);
        }
        case 15:
        {
            return _iceD_test(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGMPRealWarn::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGMPRealWarn, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGMPRealWarn::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGMPRealWarn, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGMPRealWarnPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGMPRealWarnPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGMPRealWarn::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

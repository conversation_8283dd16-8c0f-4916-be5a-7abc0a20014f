//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPTaskOT.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGOPTaskOT.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPTaskOT_ids[4] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPTaskBase",
    "::ZG6000::ZGOPTaskOT",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGOPTaskOT_ops[] =
{
    "abolishTask",
    "checkState",
    "clearOtSimulateValue",
    "confirmPreview",
    "confirmTask",
    "convertOT",
    "createOT",
    "createOtItem",
    "deleteOtItem",
    "deleteTask",
    "dispatchData",
    "downloadTask",
    "editOT",
    "exitApp",
    "getCommonTerm",
    "getDeviceTerm",
    "getOT",
    "getTaskList",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "pausePreview",
    "pauseTask",
    "resumeDebug",
    "resumePreview",
    "resumeTask",
    "retryPreview",
    "retryTask",
    "setOtSimulateValue",
    "skipItem",
    "startDebug",
    "startPreview",
    "startTask",
    "stopDebug",
    "stopPreview",
    "test",
    "updateItem",
    "updateTask"
};
const ::std::string iceC_ZG6000_ZGOPTaskOT_getOT_name = "getOT";
const ::std::string iceC_ZG6000_ZGOPTaskOT_createOT_name = "createOT";
const ::std::string iceC_ZG6000_ZGOPTaskOT_editOT_name = "editOT";
const ::std::string iceC_ZG6000_ZGOPTaskOT_convertOT_name = "convertOT";
const ::std::string iceC_ZG6000_ZGOPTaskOT_skipItem_name = "skipItem";
const ::std::string iceC_ZG6000_ZGOPTaskOT_startPreview_name = "startPreview";
const ::std::string iceC_ZG6000_ZGOPTaskOT_stopPreview_name = "stopPreview";
const ::std::string iceC_ZG6000_ZGOPTaskOT_pausePreview_name = "pausePreview";
const ::std::string iceC_ZG6000_ZGOPTaskOT_resumePreview_name = "resumePreview";
const ::std::string iceC_ZG6000_ZGOPTaskOT_retryPreview_name = "retryPreview";
const ::std::string iceC_ZG6000_ZGOPTaskOT_confirmPreview_name = "confirmPreview";
const ::std::string iceC_ZG6000_ZGOPTaskOT_getDeviceTerm_name = "getDeviceTerm";
const ::std::string iceC_ZG6000_ZGOPTaskOT_getCommonTerm_name = "getCommonTerm";
const ::std::string iceC_ZG6000_ZGOPTaskOT_createOtItem_name = "createOtItem";
const ::std::string iceC_ZG6000_ZGOPTaskOT_deleteOtItem_name = "deleteOtItem";
const ::std::string iceC_ZG6000_ZGOPTaskOT_setOtSimulateValue_name = "setOtSimulateValue";
const ::std::string iceC_ZG6000_ZGOPTaskOT_clearOtSimulateValue_name = "clearOtSimulateValue";
const ::std::string iceC_ZG6000_ZGOPTaskOT_downloadTask_name = "downloadTask";
const ::std::string iceC_ZG6000_ZGOPTaskOT_updateTask_name = "updateTask";
const ::std::string iceC_ZG6000_ZGOPTaskOT_updateItem_name = "updateItem";

}

bool
ZG6000::ZGOPTaskOT::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPTaskOT_ids, iceC_ZG6000_ZGOPTaskOT_ids + 4, s);
}

::std::vector<::std::string>
ZG6000::ZGOPTaskOT::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGOPTaskOT_ids[0], &iceC_ZG6000_ZGOPTaskOT_ids[4]);
}

::std::string
ZG6000::ZGOPTaskOT::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPTaskOT::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGOPTaskOT";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_getOT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    istr->readAll(iceP_otID);
    inS.endReadParams();
    StringMap iceP_head;
    ListStringMap iceP_items;
    ErrorInfo iceP_e;
    bool ret = this->getOT(::std::move(iceP_otID), iceP_head, iceP_items, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_head, iceP_items, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_createOT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskTypeID;
    StringMap iceP_param;
    istr->readAll(iceP_taskTypeID, iceP_param);
    inS.endReadParams();
    ::std::string iceP_otID;
    ErrorInfo iceP_e;
    bool ret = this->createOT(::std::move(iceP_taskTypeID), ::std::move(iceP_param), iceP_otID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_otID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_editOT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_head;
    ListStringMap iceP_items;
    istr->readAll(iceP_otID, iceP_head, iceP_items);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->editOT(::std::move(iceP_otID), ::std::move(iceP_head), ::std::move(iceP_items), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_convertOT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->convertOT(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_skipItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->skipItem(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_startPreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->startPreview(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_stopPreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->stopPreview(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_pausePreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->pausePreview(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_resumePreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->resumePreview(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_retryPreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->retryPreview(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_confirmPreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmPreview(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_getDeviceTerm(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_param;
    istr->readAll(iceP_deviceID, iceP_param);
    inS.endReadParams();
    ListStringMap iceP_terms;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceTerm(::std::move(iceP_deviceID), ::std::move(iceP_param), iceP_terms, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_terms, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_getCommonTerm(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_param;
    istr->readAll(iceP_param);
    inS.endReadParams();
    ListStringMap iceP_terms;
    ErrorInfo iceP_e;
    bool ret = this->getCommonTerm(::std::move(iceP_param), iceP_terms, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_terms, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_createOtItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ListStringMap iceP_items;
    ErrorInfo iceP_e;
    bool ret = this->createOtItem(::std::move(iceP_otID), ::std::move(iceP_param), iceP_items, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_items, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_deleteOtItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteOtItem(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_setOtSimulateValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setOtSimulateValue(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_clearOtSimulateValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->readAll(iceP_otID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->clearOtSimulateValue(::std::move(iceP_otID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_downloadTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    StringList iceP_listTaskID;
    istr->readAll(iceP_clientID, iceP_listTaskID);
    inS.endReadParams();
    ListStringMap iceP_listTask;
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->downloadTask(::std::move(iceP_clientID), ::std::move(iceP_listTaskID), iceP_listTask, iceP_listItem, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listTask, iceP_listItem, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_updateTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listTask;
    istr->readAll(iceP_listTask);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateTask(::std::move(iceP_listTask), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_updateItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->readAll(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateItem(::std::move(iceP_listItem), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPTaskOT_ops, iceC_ZG6000_ZGOPTaskOT_ops + 43, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPTaskOT_ops)
    {
        case 0:
        {
            return _iceD_abolishTask(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_clearOtSimulateValue(in, current);
        }
        case 3:
        {
            return _iceD_confirmPreview(in, current);
        }
        case 4:
        {
            return _iceD_confirmTask(in, current);
        }
        case 5:
        {
            return _iceD_convertOT(in, current);
        }
        case 6:
        {
            return _iceD_createOT(in, current);
        }
        case 7:
        {
            return _iceD_createOtItem(in, current);
        }
        case 8:
        {
            return _iceD_deleteOtItem(in, current);
        }
        case 9:
        {
            return _iceD_deleteTask(in, current);
        }
        case 10:
        {
            return _iceD_dispatchData(in, current);
        }
        case 11:
        {
            return _iceD_downloadTask(in, current);
        }
        case 12:
        {
            return _iceD_editOT(in, current);
        }
        case 13:
        {
            return _iceD_exitApp(in, current);
        }
        case 14:
        {
            return _iceD_getCommonTerm(in, current);
        }
        case 15:
        {
            return _iceD_getDeviceTerm(in, current);
        }
        case 16:
        {
            return _iceD_getOT(in, current);
        }
        case 17:
        {
            return _iceD_getTaskList(in, current);
        }
        case 18:
        {
            return _iceD_getVersion(in, current);
        }
        case 19:
        {
            return _iceD_heartDebug(in, current);
        }
        case 20:
        {
            return _iceD_ice_id(in, current);
        }
        case 21:
        {
            return _iceD_ice_ids(in, current);
        }
        case 22:
        {
            return _iceD_ice_isA(in, current);
        }
        case 23:
        {
            return _iceD_ice_ping(in, current);
        }
        case 24:
        {
            return _iceD_isDebugging(in, current);
        }
        case 25:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 26:
        {
            return _iceD_pausePreview(in, current);
        }
        case 27:
        {
            return _iceD_pauseTask(in, current);
        }
        case 28:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 29:
        {
            return _iceD_resumePreview(in, current);
        }
        case 30:
        {
            return _iceD_resumeTask(in, current);
        }
        case 31:
        {
            return _iceD_retryPreview(in, current);
        }
        case 32:
        {
            return _iceD_retryTask(in, current);
        }
        case 33:
        {
            return _iceD_setOtSimulateValue(in, current);
        }
        case 34:
        {
            return _iceD_skipItem(in, current);
        }
        case 35:
        {
            return _iceD_startDebug(in, current);
        }
        case 36:
        {
            return _iceD_startPreview(in, current);
        }
        case 37:
        {
            return _iceD_startTask(in, current);
        }
        case 38:
        {
            return _iceD_stopDebug(in, current);
        }
        case 39:
        {
            return _iceD_stopPreview(in, current);
        }
        case 40:
        {
            return _iceD_test(in, current);
        }
        case 41:
        {
            return _iceD_updateItem(in, current);
        }
        case 42:
        {
            return _iceD_updateTask(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_getOT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::GetOTResult>>& outAsync, const ::std::string& iceP_otID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_getOT_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_getOT_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::GetOTResult v;
            istr->readAll(v.head, v.items, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_createOT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::CreateOTResult>>& outAsync, const ::std::string& iceP_taskTypeID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_createOT_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_createOT_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskTypeID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::CreateOTResult v;
            istr->readAll(v.otID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_editOT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::EditOTResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_head, const ListStringMap& iceP_items, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_editOT_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_editOT_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_head, iceP_items);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::EditOTResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_convertOT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::ConvertOTResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_convertOT_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_convertOT_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::ConvertOTResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_skipItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::SkipItemResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_skipItem_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_skipItem_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::SkipItemResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_startPreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::StartPreviewResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_startPreview_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_startPreview_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::StartPreviewResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_stopPreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::StopPreviewResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_stopPreview_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_stopPreview_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::StopPreviewResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_pausePreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::PausePreviewResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_pausePreview_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_pausePreview_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::PausePreviewResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_resumePreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::ResumePreviewResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_resumePreview_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_resumePreview_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::ResumePreviewResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_retryPreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::RetryPreviewResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_retryPreview_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_retryPreview_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::RetryPreviewResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_confirmPreview(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::ConfirmPreviewResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_confirmPreview_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_confirmPreview_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::ConfirmPreviewResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_getDeviceTerm(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::GetDeviceTermResult>>& outAsync, const ::std::string& iceP_deviceID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_getDeviceTerm_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_getDeviceTerm_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_deviceID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::GetDeviceTermResult v;
            istr->readAll(v.terms, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_getCommonTerm(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::GetCommonTermResult>>& outAsync, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_getCommonTerm_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_getCommonTerm_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::GetCommonTermResult v;
            istr->readAll(v.terms, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_createOtItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::CreateOtItemResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_createOtItem_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_createOtItem_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::CreateOtItemResult v;
            istr->readAll(v.items, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_deleteOtItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::DeleteOtItemResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_deleteOtItem_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_deleteOtItem_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::DeleteOtItemResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_setOtSimulateValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::SetOtSimulateValueResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_setOtSimulateValue_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_setOtSimulateValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::SetOtSimulateValueResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_clearOtSimulateValue(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::ClearOtSimulateValueResult>>& outAsync, const ::std::string& iceP_otID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_clearOtSimulateValue_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_clearOtSimulateValue_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_otID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::ClearOtSimulateValueResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_downloadTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::DownloadTaskResult>>& outAsync, const ::std::string& iceP_clientID, const StringList& iceP_listTaskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_downloadTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_downloadTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_listTaskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::DownloadTaskResult v;
            istr->readAll(v.listTask, v.listItem, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_updateTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::UpdateTaskResult>>& outAsync, const ListStringMap& iceP_listTask, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_updateTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_updateTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listTask);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::UpdateTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOTPrx::_iceI_updateItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOT::UpdateItemResult>>& outAsync, const ListStringMap& iceP_listItem, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_updateItem_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOT_updateItem_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_listItem);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOT::UpdateItemResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGOPTaskOTPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGOPTaskOTPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGOPTaskOTPrx::ice_staticId()
{
    return ZGOPTaskOT::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPTaskOT_getOT_name = "getOT";

const ::std::string iceC_ZG6000_ZGOPTaskOT_createOT_name = "createOT";

const ::std::string iceC_ZG6000_ZGOPTaskOT_editOT_name = "editOT";

const ::std::string iceC_ZG6000_ZGOPTaskOT_convertOT_name = "convertOT";

const ::std::string iceC_ZG6000_ZGOPTaskOT_skipItem_name = "skipItem";

const ::std::string iceC_ZG6000_ZGOPTaskOT_startPreview_name = "startPreview";

const ::std::string iceC_ZG6000_ZGOPTaskOT_stopPreview_name = "stopPreview";

const ::std::string iceC_ZG6000_ZGOPTaskOT_pausePreview_name = "pausePreview";

const ::std::string iceC_ZG6000_ZGOPTaskOT_resumePreview_name = "resumePreview";

const ::std::string iceC_ZG6000_ZGOPTaskOT_retryPreview_name = "retryPreview";

const ::std::string iceC_ZG6000_ZGOPTaskOT_confirmPreview_name = "confirmPreview";

const ::std::string iceC_ZG6000_ZGOPTaskOT_getDeviceTerm_name = "getDeviceTerm";

const ::std::string iceC_ZG6000_ZGOPTaskOT_getCommonTerm_name = "getCommonTerm";

const ::std::string iceC_ZG6000_ZGOPTaskOT_createOtItem_name = "createOtItem";

const ::std::string iceC_ZG6000_ZGOPTaskOT_deleteOtItem_name = "deleteOtItem";

const ::std::string iceC_ZG6000_ZGOPTaskOT_setOtSimulateValue_name = "setOtSimulateValue";

const ::std::string iceC_ZG6000_ZGOPTaskOT_clearOtSimulateValue_name = "clearOtSimulateValue";

const ::std::string iceC_ZG6000_ZGOPTaskOT_downloadTask_name = "downloadTask";

const ::std::string iceC_ZG6000_ZGOPTaskOT_updateTask_name = "updateTask";

const ::std::string iceC_ZG6000_ZGOPTaskOT_updateItem_name = "updateItem";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGOPTaskOT* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGOPTaskOT>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGOPTaskOT;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_getOT(const ::std::string& iceP_otID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_getOT_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_getOT_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_getOT_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_getOT_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_getOT(::ZG6000::StringMap& iceP_head, ::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_getOT_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_head);
    istr->read(iceP_items);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_getOT(::ZG6000::StringMap& iceP_head, ::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_getOT_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_head);
    istr->read(iceP_items);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_createOT(const ::std::string& iceP_taskTypeID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_createOT_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_createOT_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_createOT_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskTypeID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_createOT_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_createOT(::std::string& iceP_otID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_createOT_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_otID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_createOT(::std::string& iceP_otID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_createOT_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_otID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_editOT(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_head, const ::ZG6000::ListStringMap& iceP_items, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_editOT_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_editOT_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_editOT_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_head);
        ostr->write(iceP_items);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_editOT_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_editOT(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_editOT_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_editOT(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_editOT_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_convertOT(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_convertOT_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_convertOT_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_convertOT_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_convertOT_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_convertOT(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_convertOT_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_convertOT(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_convertOT_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_skipItem(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_skipItem_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_skipItem_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_skipItem_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_skipItem_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_skipItem(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_skipItem_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_skipItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_skipItem_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_startPreview(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_startPreview_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_startPreview_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_startPreview_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_startPreview_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_startPreview(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_startPreview_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_startPreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_startPreview_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_stopPreview(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_stopPreview_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_stopPreview_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_stopPreview_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_stopPreview_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_stopPreview(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_stopPreview_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_stopPreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_stopPreview_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_pausePreview(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_pausePreview_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_pausePreview_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_pausePreview_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_pausePreview_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_pausePreview(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_pausePreview_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_pausePreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_pausePreview_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_resumePreview(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_resumePreview_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_resumePreview_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_resumePreview_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_resumePreview_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_resumePreview(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_resumePreview_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_resumePreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_resumePreview_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_retryPreview(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_retryPreview_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_retryPreview_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_retryPreview_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_retryPreview_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_retryPreview(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_retryPreview_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_retryPreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_retryPreview_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_confirmPreview(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_confirmPreview_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_confirmPreview_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_confirmPreview_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_confirmPreview_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_confirmPreview(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_confirmPreview_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_confirmPreview(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_confirmPreview_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_getDeviceTerm(const ::std::string& iceP_deviceID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_getDeviceTerm_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_getDeviceTerm_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_getDeviceTerm_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_getDeviceTerm_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_getDeviceTerm(::ZG6000::ListStringMap& iceP_terms, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_getDeviceTerm_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_terms);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_getDeviceTerm(::ZG6000::ListStringMap& iceP_terms, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_getDeviceTerm_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_terms);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_getCommonTerm(const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_getCommonTerm_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_getCommonTerm_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_getCommonTerm_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_getCommonTerm_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_getCommonTerm(::ZG6000::ListStringMap& iceP_terms, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_getCommonTerm_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_terms);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_getCommonTerm(::ZG6000::ListStringMap& iceP_terms, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_getCommonTerm_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_terms);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_createOtItem(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_createOtItem_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_createOtItem_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_createOtItem_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_createOtItem_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_createOtItem(::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_createOtItem_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_items);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_createOtItem(::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_createOtItem_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_items);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_deleteOtItem(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_deleteOtItem_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_deleteOtItem_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_deleteOtItem_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_deleteOtItem_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_deleteOtItem(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_deleteOtItem_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_deleteOtItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_deleteOtItem_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_setOtSimulateValue(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_setOtSimulateValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_setOtSimulateValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_setOtSimulateValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_setOtSimulateValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_setOtSimulateValue(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_setOtSimulateValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_setOtSimulateValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_setOtSimulateValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_clearOtSimulateValue(const ::std::string& iceP_otID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_clearOtSimulateValue_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_clearOtSimulateValue_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_clearOtSimulateValue_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_otID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_clearOtSimulateValue_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_clearOtSimulateValue(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_clearOtSimulateValue_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_clearOtSimulateValue(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_clearOtSimulateValue_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_downloadTask(const ::std::string& iceP_clientID, const ::ZG6000::StringList& iceP_listTaskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_downloadTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_downloadTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_downloadTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_listTaskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_downloadTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_downloadTask(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_downloadTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTask);
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_downloadTask(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_downloadTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listTask);
    istr->read(iceP_listItem);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_updateTask(const ::ZG6000::ListStringMap& iceP_listTask, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_updateTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_updateTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_updateTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listTask);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_updateTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_updateTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_updateTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_updateTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_updateTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOT::_iceI_begin_updateItem(const ::ZG6000::ListStringMap& iceP_listItem, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOT_updateItem_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOT_updateItem_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOT_updateItem_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_listItem);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOT_updateItem_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOT::end_updateItem(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_updateItem_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOT::_iceI_end_updateItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOT_updateItem_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGOPTaskOT::_newInstance() const
{
    return new ZGOPTaskOT;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGOPTaskOT::ice_staticId()
{
    return ::ZG6000::ZGOPTaskOT::ice_staticId();
}

ZG6000::ZGOPTaskOT::~ZGOPTaskOT()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGOPTaskOT* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPTaskOT_ids[4] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPTaskBase",
    "::ZG6000::ZGOPTaskOT",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGOPTaskOT::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPTaskOT_ids, iceC_ZG6000_ZGOPTaskOT_ids + 4, s);
}

::std::vector< ::std::string>
ZG6000::ZGOPTaskOT::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGOPTaskOT_ids[0], &iceC_ZG6000_ZGOPTaskOT_ids[4]);
}

const ::std::string&
ZG6000::ZGOPTaskOT::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPTaskOT::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGOPTaskOT";
    return typeId;
#else
    return iceC_ZG6000_ZGOPTaskOT_ids[2];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_getOT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    istr->read(iceP_otID);
    inS.endReadParams();
    StringMap iceP_head;
    ListStringMap iceP_items;
    ErrorInfo iceP_e;
    bool ret = this->getOT(iceP_otID, iceP_head, iceP_items, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_head);
    ostr->write(iceP_items);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_createOT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskTypeID;
    StringMap iceP_param;
    istr->read(iceP_taskTypeID);
    istr->read(iceP_param);
    inS.endReadParams();
    ::std::string iceP_otID;
    ErrorInfo iceP_e;
    bool ret = this->createOT(iceP_taskTypeID, iceP_param, iceP_otID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_otID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_editOT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_head;
    ListStringMap iceP_items;
    istr->read(iceP_otID);
    istr->read(iceP_head);
    istr->read(iceP_items);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->editOT(iceP_otID, iceP_head, iceP_items, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_convertOT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->convertOT(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_skipItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->skipItem(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_startPreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->startPreview(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_stopPreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->stopPreview(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_pausePreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->pausePreview(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_resumePreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->resumePreview(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_retryPreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->retryPreview(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_confirmPreview(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmPreview(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_getDeviceTerm(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_deviceID;
    StringMap iceP_param;
    istr->read(iceP_deviceID);
    istr->read(iceP_param);
    inS.endReadParams();
    ListStringMap iceP_terms;
    ErrorInfo iceP_e;
    bool ret = this->getDeviceTerm(iceP_deviceID, iceP_param, iceP_terms, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_terms);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_getCommonTerm(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_param;
    istr->read(iceP_param);
    inS.endReadParams();
    ListStringMap iceP_terms;
    ErrorInfo iceP_e;
    bool ret = this->getCommonTerm(iceP_param, iceP_terms, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_terms);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_createOtItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ListStringMap iceP_items;
    ErrorInfo iceP_e;
    bool ret = this->createOtItem(iceP_otID, iceP_param, iceP_items, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_items);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_deleteOtItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteOtItem(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_setOtSimulateValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setOtSimulateValue(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_clearOtSimulateValue(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_otID;
    StringMap iceP_param;
    istr->read(iceP_otID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->clearOtSimulateValue(iceP_otID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_downloadTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    StringList iceP_listTaskID;
    istr->read(iceP_clientID);
    istr->read(iceP_listTaskID);
    inS.endReadParams();
    ListStringMap iceP_listTask;
    ListStringMap iceP_listItem;
    ErrorInfo iceP_e;
    bool ret = this->downloadTask(iceP_clientID, iceP_listTaskID, iceP_listTask, iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listTask);
    ostr->write(iceP_listItem);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_updateTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listTask;
    istr->read(iceP_listTask);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateTask(iceP_listTask, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceD_updateItem(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ListStringMap iceP_listItem;
    istr->read(iceP_listItem);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateItem(iceP_listItem, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPTaskOT_all[] =
{
    "abolishTask",
    "checkState",
    "clearOtSimulateValue",
    "confirmPreview",
    "confirmTask",
    "convertOT",
    "createOT",
    "createOtItem",
    "deleteOtItem",
    "deleteTask",
    "dispatchData",
    "downloadTask",
    "editOT",
    "exitApp",
    "getCommonTerm",
    "getDeviceTerm",
    "getOT",
    "getTaskList",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "pauseDebug",
    "pausePreview",
    "pauseTask",
    "resumeDebug",
    "resumePreview",
    "resumeTask",
    "retryPreview",
    "retryTask",
    "setOtSimulateValue",
    "skipItem",
    "startDebug",
    "startPreview",
    "startTask",
    "stopDebug",
    "stopPreview",
    "test",
    "updateItem",
    "updateTask"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOT::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPTaskOT_all, iceC_ZG6000_ZGOPTaskOT_all + 43, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPTaskOT_all)
    {
        case 0:
        {
            return _iceD_abolishTask(in, current);
        }
        case 1:
        {
            return _iceD_checkState(in, current);
        }
        case 2:
        {
            return _iceD_clearOtSimulateValue(in, current);
        }
        case 3:
        {
            return _iceD_confirmPreview(in, current);
        }
        case 4:
        {
            return _iceD_confirmTask(in, current);
        }
        case 5:
        {
            return _iceD_convertOT(in, current);
        }
        case 6:
        {
            return _iceD_createOT(in, current);
        }
        case 7:
        {
            return _iceD_createOtItem(in, current);
        }
        case 8:
        {
            return _iceD_deleteOtItem(in, current);
        }
        case 9:
        {
            return _iceD_deleteTask(in, current);
        }
        case 10:
        {
            return _iceD_dispatchData(in, current);
        }
        case 11:
        {
            return _iceD_downloadTask(in, current);
        }
        case 12:
        {
            return _iceD_editOT(in, current);
        }
        case 13:
        {
            return _iceD_exitApp(in, current);
        }
        case 14:
        {
            return _iceD_getCommonTerm(in, current);
        }
        case 15:
        {
            return _iceD_getDeviceTerm(in, current);
        }
        case 16:
        {
            return _iceD_getOT(in, current);
        }
        case 17:
        {
            return _iceD_getTaskList(in, current);
        }
        case 18:
        {
            return _iceD_getVersion(in, current);
        }
        case 19:
        {
            return _iceD_heartDebug(in, current);
        }
        case 20:
        {
            return _iceD_ice_id(in, current);
        }
        case 21:
        {
            return _iceD_ice_ids(in, current);
        }
        case 22:
        {
            return _iceD_ice_isA(in, current);
        }
        case 23:
        {
            return _iceD_ice_ping(in, current);
        }
        case 24:
        {
            return _iceD_isDebugging(in, current);
        }
        case 25:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 26:
        {
            return _iceD_pausePreview(in, current);
        }
        case 27:
        {
            return _iceD_pauseTask(in, current);
        }
        case 28:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 29:
        {
            return _iceD_resumePreview(in, current);
        }
        case 30:
        {
            return _iceD_resumeTask(in, current);
        }
        case 31:
        {
            return _iceD_retryPreview(in, current);
        }
        case 32:
        {
            return _iceD_retryTask(in, current);
        }
        case 33:
        {
            return _iceD_setOtSimulateValue(in, current);
        }
        case 34:
        {
            return _iceD_skipItem(in, current);
        }
        case 35:
        {
            return _iceD_startDebug(in, current);
        }
        case 36:
        {
            return _iceD_startPreview(in, current);
        }
        case 37:
        {
            return _iceD_startTask(in, current);
        }
        case 38:
        {
            return _iceD_stopDebug(in, current);
        }
        case 39:
        {
            return _iceD_stopPreview(in, current);
        }
        case 40:
        {
            return _iceD_test(in, current);
        }
        case 41:
        {
            return _iceD_updateItem(in, current);
        }
        case 42:
        {
            return _iceD_updateTask(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGOPTaskOT::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGOPTaskOT, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGOPTaskOT::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGOPTaskOT, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGOPTaskOTPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGOPTaskOTPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGOPTaskOT::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

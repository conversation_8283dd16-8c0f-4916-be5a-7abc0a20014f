//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPRedisMaster.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPRedisMaster_h__
#define __ZGSPRedisMaster_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPRedisMaster;
class ZGSPRedisMasterPrx;

}

namespace ZG6000
{

class ZGSPRedisMaster : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPRedisMasterPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getLocalState.
     */
    struct GetLocalStateResult
    {
        bool returnValue;
        bool isMaster;
        ErrorInfo e;
    };

    virtual bool getLocalState(bool& isMaster, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getLocalState(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPRedisMasterPrx : public virtual ::Ice::Proxy<ZGSPRedisMasterPrx, ZGServerBasePrx>
{
public:

    bool getLocalState(bool& isMaster, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPRedisMaster::GetLocalStateResult>(true, this, &ZGSPRedisMasterPrx::_iceI_getLocalState, context).get();
        isMaster = _result.isMaster;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getLocalStateAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPRedisMaster::GetLocalStateResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPRedisMaster::GetLocalStateResult, P>(false, this, &ZGSPRedisMasterPrx::_iceI_getLocalState, context);
    }

    ::std::function<void()>
    getLocalStateAsync(::std::function<void(bool, bool, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPRedisMaster::GetLocalStateResult&& _result)
        {
            response(_result.returnValue, _result.isMaster, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPRedisMaster::GetLocalStateResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPRedisMasterPrx::_iceI_getLocalState, context);
    }

    /// \cond INTERNAL
    void _iceI_getLocalState(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPRedisMaster::GetLocalStateResult>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPRedisMasterPrx() = default;
    friend ::std::shared_ptr<ZGSPRedisMasterPrx> IceInternal::createProxy<ZGSPRedisMasterPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPRedisMasterPtr = ::std::shared_ptr<ZGSPRedisMaster>;
using ZGSPRedisMasterPrxPtr = ::std::shared_ptr<ZGSPRedisMasterPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPRedisMaster;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPRedisMaster>&);
::IceProxy::Ice::Object* upCast(ZGSPRedisMaster*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPRedisMaster;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPRedisMaster*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPRedisMaster> ZGSPRedisMasterPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPRedisMaster> ZGSPRedisMasterPrx;
typedef ZGSPRedisMasterPrx ZGSPRedisMasterPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPRedisMasterPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPRedisMaster::begin_getLocalState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPRedisMaster_getLocalState.
 */
class Callback_ZGSPRedisMaster_getLocalState_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPRedisMaster_getLocalState_Base> Callback_ZGSPRedisMaster_getLocalStatePtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPRedisMaster : public virtual ::Ice::Proxy<ZGSPRedisMaster, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool getLocalState(bool& isMaster, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getLocalState(isMaster, e, _iceI_begin_getLocalState(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getLocalState(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getLocalState(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getLocalState(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getLocalState(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getLocalState(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getLocalState(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getLocalState(const ::ZG6000::Callback_ZGSPRedisMaster_getLocalStatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getLocalState(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getLocalState(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPRedisMaster_getLocalStatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getLocalState(context, cb, cookie);
    }

    bool end_getLocalState(bool& isMaster, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getLocalState(bool& iceP_isMaster, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getLocalState(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPRedisMaster : virtual public ZGServerBase
{
public:

    typedef ZGSPRedisMasterPrx ProxyType;
    typedef ZGSPRedisMasterPtr PointerType;

    virtual ~ZGSPRedisMaster();

#ifdef ICE_CPP11_COMPILER
    ZGSPRedisMaster() = default;
    ZGSPRedisMaster(const ZGSPRedisMaster&) = default;
    ZGSPRedisMaster& operator=(const ZGSPRedisMaster&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool getLocalState(bool& isMaster, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getLocalState(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPRedisMaster& lhs, const ZGSPRedisMaster& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPRedisMaster& lhs, const ZGSPRedisMaster& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPRedisMaster::begin_getLocalState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPRedisMaster_getLocalState.
 */
template<class T>
class CallbackNC_ZGSPRedisMaster_getLocalState : public Callback_ZGSPRedisMaster_getLocalState_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&);

    CallbackNC_ZGSPRedisMaster_getLocalState(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPRedisMasterPrx proxy = ZGSPRedisMasterPrx::uncheckedCast(result->getProxy());
        bool iceP_isMaster;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getLocalState(iceP_isMaster, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_isMaster, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPRedisMaster::begin_getLocalState.
 */
template<class T> Callback_ZGSPRedisMaster_getLocalStatePtr
newCallback_ZGSPRedisMaster_getLocalState(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPRedisMaster_getLocalState<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPRedisMaster::begin_getLocalState.
 */
template<class T> Callback_ZGSPRedisMaster_getLocalStatePtr
newCallback_ZGSPRedisMaster_getLocalState(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPRedisMaster_getLocalState<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPRedisMaster::begin_getLocalState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPRedisMaster_getLocalState.
 */
template<class T, typename CT>
class Callback_ZGSPRedisMaster_getLocalState : public Callback_ZGSPRedisMaster_getLocalState_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&, const CT&);

    Callback_ZGSPRedisMaster_getLocalState(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPRedisMasterPrx proxy = ZGSPRedisMasterPrx::uncheckedCast(result->getProxy());
        bool iceP_isMaster;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getLocalState(iceP_isMaster, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_isMaster, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPRedisMaster::begin_getLocalState.
 */
template<class T, typename CT> Callback_ZGSPRedisMaster_getLocalStatePtr
newCallback_ZGSPRedisMaster_getLocalState(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPRedisMaster_getLocalState<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPRedisMaster::begin_getLocalState.
 */
template<class T, typename CT> Callback_ZGSPRedisMaster_getLocalStatePtr
newCallback_ZGSPRedisMaster_getLocalState(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPRedisMaster_getLocalState<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

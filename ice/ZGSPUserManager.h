//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPUserManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSPUserManager_h__
#define __ZGSPUserManager_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSPUserManager;
class ZGSPUserManagerPrx;

}

namespace ZG6000
{

class ZGSPUserManager : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSPUserManagerPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getUserInfo.
     */
    struct GetUserInfoResult
    {
        bool returnValue;
        StringMap user;
        ListStringMap listRole;
        ListStringMap listCard;
        ListStringMap listAuth;
        ListStringMap listAppNode;
        ErrorInfo e;
    };

    virtual bool getUserInfo(::std::string userID, StringMap& user, ListStringMap& listRole, ListStringMap& listCard, ListStringMap& listAuth, ListStringMap& listAppNode, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserInfo(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getUserFingers.
     */
    struct GetUserFingersResult
    {
        bool returnValue;
        ListStringMap listFinger;
        ErrorInfo e;
    };

    virtual bool getUserFingers(::std::string userID, ListStringMap& listFinger, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserFingers(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getUserFace.
     */
    struct GetUserFaceResult
    {
        bool returnValue;
        ::std::string faceData;
        ErrorInfo e;
    };

    virtual bool getUserFace(::std::string userID, ::std::string& faceData, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserFace(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to addUser.
     */
    struct AddUserResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool addUser(StringMap user, ListStringMap listRole, ListStringMap listAuth, ListStringMap listAppNode, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_addUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateUser.
     */
    struct UpdateUserResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateUser(StringMap user, ListStringMap listRole, ListStringMap listAuth, ListStringMap listAppNode, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deleteUser.
     */
    struct DeleteUserResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool deleteUser(::std::string userID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to addUserCard.
     */
    struct AddUserCardResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool addUserCard(::std::string userID, ::std::string cardNo, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_addUserCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deleteUserCard.
     */
    struct DeleteUserCardResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool deleteUserCard(::std::string cardNo, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteUserCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateUserFace.
     */
    struct UpdateUserFaceResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateUserFace(::std::string userID, ::std::string faceData, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateUserFace(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deleteUserFace.
     */
    struct DeleteUserFaceResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool deleteUserFace(::std::string userID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteUserFace(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateUserFinger.
     */
    struct UpdateUserFingerResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateUserFinger(::std::string userID, int fingerNo, ::std::string fingerData, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateUserFinger(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deleteUserFinger.
     */
    struct DeleteUserFingerResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool deleteUserFinger(::std::string userID, int fingerNo, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteUserFinger(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to changePassword.
     */
    struct ChangePasswordResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool changePassword(::std::string userID, ::std::string oldPassword, ::std::string newPassword, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_changePassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to resetPassword.
     */
    struct ResetPasswordResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool resetPassword(::std::string userID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_resetPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to isUserHasPower.
     */
    struct IsUserHasPowerResult
    {
        bool returnValue;
        bool hasPower;
        ErrorInfo e;
    };

    virtual bool isUserHasPower(::std::string userID, ::std::string powerID, bool& hasPower, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_isUserHasPower(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to isCardBindUser.
     */
    struct IsCardBindUserResult
    {
        bool returnValue;
        bool isBind;
        ::std::string userID;
        ErrorInfo e;
    };

    virtual bool isCardBindUser(::std::string cardID, bool& isBind, ::std::string& userID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_isCardBindUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to loginByPassword.
     */
    struct LoginByPasswordResult
    {
        bool returnValue;
        ::std::string outClientID;
        ErrorInfo e;
    };

    virtual bool loginByPassword(::std::string clientID, ::std::string userID, ::std::string password, int keepTime, ::std::string& outClientID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to loginByCard.
     */
    struct LoginByCardResult
    {
        bool returnValue;
        ::std::string outClientID;
        ErrorInfo e;
    };

    virtual bool loginByCard(::std::string clientID, ::std::string userID, ::std::string authModeID, ::std::string cardID, int keepTime, ::std::string& outClientID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to loginByVerifyCode.
     */
    struct LoginByVerifyCodeResult
    {
        bool returnValue;
        ::std::string outClientID;
        ErrorInfo e;
    };

    virtual bool loginByVerifyCode(::std::string clientID, ::std::string phoneNumber, ::std::string code, int keepTime, ::std::string& outClientID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to loginByAuthDev.
     */
    struct LoginByAuthDevResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool loginByAuthDev(::std::string clientID, ::std::string userID, ::std::string authModeID, int keepTime, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByAuthDev(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to logout.
     */
    struct LogoutResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool logout(::std::string clientID, ::std::string userID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_logout(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to sendVerifyCode.
     */
    struct SendVerifyCodeResult
    {
        bool returnValue;
        ::std::string seqNo;
        ErrorInfo e;
    };

    virtual bool sendVerifyCode(::std::string phoneNumber, ::std::string& seqNo, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_sendVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getAvaiableUser.
     */
    struct GetAvaiableUserResult
    {
        bool returnValue;
        ListStringMap lstUser;
        ErrorInfo e;
    };

    virtual bool getAvaiableUser(::std::string clientID, ::std::string appNodeID, ::std::string powerID, ListStringMap& lstUser, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getAvaiableUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByPassword.
     */
    struct VerifyByPasswordResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool verifyByPassword(::std::string clientID, ::std::string userID, ::std::string password, ::std::string appNodeID, ::std::string powerID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByCard.
     */
    struct VerifyByCardResult
    {
        bool returnValue;
        ::std::string realUserID;
        ErrorInfo e;
    };

    virtual bool verifyByCard(::std::string clientID, ::std::string userID, ::std::string authModeID, ::std::string cardID, ::std::string appNodeID, ::std::string powerID, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByVerifyCode.
     */
    struct VerifyByVerifyCodeResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool verifyByVerifyCode(::std::string clientID, ::std::string phoneNumber, ::std::string code, ::std::string appNodeID, ::std::string powerID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByAuthDev.
     */
    struct VerifyByAuthDevResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool verifyByAuthDev(::std::string clientID, ::std::string userID, ::std::string authModeID, ::std::string appNodeID, ::std::string powerID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByAuthDev(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByPasswordNoClient.
     */
    struct VerifyByPasswordNoClientResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool verifyByPasswordNoClient(::std::string userID, ::std::string password, ::std::string appNodeID, ::std::string powerID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByPasswordNoClient(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByCardNoClient.
     */
    struct VerifyByCardNoClientResult
    {
        bool returnValue;
        ::std::string realUserID;
        ErrorInfo e;
    };

    virtual bool verifyByCardNoClient(::std::string userID, ::std::string authModeID, ::std::string cardID, ::std::string appNodeID, ::std::string powerID, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByCardNoClient(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to verifyByAuthDevNoClient.
     */
    struct VerifyByAuthDevNoClientResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool verifyByAuthDevNoClient(::std::string userID, ::std::string authModeID, ::std::string appNodeID, ::std::string powerID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByAuthDevNoClient(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void cancelAuth(::std::string clientID, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_cancelAuth(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to sendRandomPassword.
     */
    struct SendRandomPasswordResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool sendRandomPassword(::std::string userID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_sendRandomPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSPUserManagerPrx : public virtual ::Ice::Proxy<ZGSPUserManagerPrx, ZGServerBasePrx>
{
public:

    bool getUserInfo(const ::std::string& userID, StringMap& user, ListStringMap& listRole, ListStringMap& listCard, ListStringMap& listAuth, ListStringMap& listAppNode, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::GetUserInfoResult>(true, this, &ZGSPUserManagerPrx::_iceI_getUserInfo, userID, context).get();
        user = ::std::move(_result.user);
        listRole = ::std::move(_result.listRole);
        listCard = ::std::move(_result.listCard);
        listAuth = ::std::move(_result.listAuth);
        listAppNode = ::std::move(_result.listAppNode);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getUserInfoAsync(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::GetUserInfoResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::GetUserInfoResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_getUserInfo, userID, context);
    }

    ::std::function<void()>
    getUserInfoAsync(const ::std::string& userID,
                     ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ListStringMap, ::ZG6000::ListStringMap, ::ZG6000::ListStringMap, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::GetUserInfoResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.user), ::std::move(_result.listRole), ::std::move(_result.listCard), ::std::move(_result.listAuth), ::std::move(_result.listAppNode), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::GetUserInfoResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_getUserInfo, userID, context);
    }

    /// \cond INTERNAL
    void _iceI_getUserInfo(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::GetUserInfoResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getUserFingers(const ::std::string& userID, ListStringMap& listFinger, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::GetUserFingersResult>(true, this, &ZGSPUserManagerPrx::_iceI_getUserFingers, userID, context).get();
        listFinger = ::std::move(_result.listFinger);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getUserFingersAsync(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::GetUserFingersResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::GetUserFingersResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_getUserFingers, userID, context);
    }

    ::std::function<void()>
    getUserFingersAsync(const ::std::string& userID,
                        ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::GetUserFingersResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listFinger), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::GetUserFingersResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_getUserFingers, userID, context);
    }

    /// \cond INTERNAL
    void _iceI_getUserFingers(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::GetUserFingersResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getUserFace(const ::std::string& userID, ::std::string& faceData, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::GetUserFaceResult>(true, this, &ZGSPUserManagerPrx::_iceI_getUserFace, userID, context).get();
        faceData = ::std::move(_result.faceData);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getUserFaceAsync(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::GetUserFaceResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::GetUserFaceResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_getUserFace, userID, context);
    }

    ::std::function<void()>
    getUserFaceAsync(const ::std::string& userID,
                     ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::GetUserFaceResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.faceData), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::GetUserFaceResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_getUserFace, userID, context);
    }

    /// \cond INTERNAL
    void _iceI_getUserFace(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::GetUserFaceResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool addUser(const StringMap& user, const ListStringMap& listRole, const ListStringMap& listAuth, const ListStringMap& listAppNode, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::AddUserResult>(true, this, &ZGSPUserManagerPrx::_iceI_addUser, user, listRole, listAuth, listAppNode, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto addUserAsync(const StringMap& user, const ListStringMap& listRole, const ListStringMap& listAuth, const ListStringMap& listAppNode, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::AddUserResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::AddUserResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_addUser, user, listRole, listAuth, listAppNode, context);
    }

    ::std::function<void()>
    addUserAsync(const StringMap& user, const ListStringMap& listRole, const ListStringMap& listAuth, const ListStringMap& listAppNode,
                 ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                 ::std::function<void(::std::exception_ptr)> ex = nullptr,
                 ::std::function<void(bool)> sent = nullptr,
                 const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::AddUserResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::AddUserResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_addUser, user, listRole, listAuth, listAppNode, context);
    }

    /// \cond INTERNAL
    void _iceI_addUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::AddUserResult>>&, const StringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool updateUser(const StringMap& user, const ListStringMap& listRole, const ListStringMap& listAuth, const ListStringMap& listAppNode, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::UpdateUserResult>(true, this, &ZGSPUserManagerPrx::_iceI_updateUser, user, listRole, listAuth, listAppNode, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateUserAsync(const StringMap& user, const ListStringMap& listRole, const ListStringMap& listAuth, const ListStringMap& listAppNode, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::UpdateUserResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::UpdateUserResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_updateUser, user, listRole, listAuth, listAppNode, context);
    }

    ::std::function<void()>
    updateUserAsync(const StringMap& user, const ListStringMap& listRole, const ListStringMap& listAuth, const ListStringMap& listAppNode,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::UpdateUserResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::UpdateUserResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_updateUser, user, listRole, listAuth, listAppNode, context);
    }

    /// \cond INTERNAL
    void _iceI_updateUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::UpdateUserResult>>&, const StringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool deleteUser(const ::std::string& userID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::DeleteUserResult>(true, this, &ZGSPUserManagerPrx::_iceI_deleteUser, userID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto deleteUserAsync(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::DeleteUserResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::DeleteUserResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_deleteUser, userID, context);
    }

    ::std::function<void()>
    deleteUserAsync(const ::std::string& userID,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::DeleteUserResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::DeleteUserResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_deleteUser, userID, context);
    }

    /// \cond INTERNAL
    void _iceI_deleteUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::DeleteUserResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool addUserCard(const ::std::string& userID, const ::std::string& cardNo, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::AddUserCardResult>(true, this, &ZGSPUserManagerPrx::_iceI_addUserCard, userID, cardNo, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto addUserCardAsync(const ::std::string& userID, const ::std::string& cardNo, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::AddUserCardResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::AddUserCardResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_addUserCard, userID, cardNo, context);
    }

    ::std::function<void()>
    addUserCardAsync(const ::std::string& userID, const ::std::string& cardNo,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::AddUserCardResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::AddUserCardResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_addUserCard, userID, cardNo, context);
    }

    /// \cond INTERNAL
    void _iceI_addUserCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::AddUserCardResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool deleteUserCard(const ::std::string& cardNo, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::DeleteUserCardResult>(true, this, &ZGSPUserManagerPrx::_iceI_deleteUserCard, cardNo, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto deleteUserCardAsync(const ::std::string& cardNo, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::DeleteUserCardResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::DeleteUserCardResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_deleteUserCard, cardNo, context);
    }

    ::std::function<void()>
    deleteUserCardAsync(const ::std::string& cardNo,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::DeleteUserCardResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::DeleteUserCardResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_deleteUserCard, cardNo, context);
    }

    /// \cond INTERNAL
    void _iceI_deleteUserCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::DeleteUserCardResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool updateUserFace(const ::std::string& userID, const ::std::string& faceData, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::UpdateUserFaceResult>(true, this, &ZGSPUserManagerPrx::_iceI_updateUserFace, userID, faceData, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateUserFaceAsync(const ::std::string& userID, const ::std::string& faceData, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::UpdateUserFaceResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::UpdateUserFaceResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_updateUserFace, userID, faceData, context);
    }

    ::std::function<void()>
    updateUserFaceAsync(const ::std::string& userID, const ::std::string& faceData,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::UpdateUserFaceResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::UpdateUserFaceResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_updateUserFace, userID, faceData, context);
    }

    /// \cond INTERNAL
    void _iceI_updateUserFace(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::UpdateUserFaceResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool deleteUserFace(const ::std::string& userID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::DeleteUserFaceResult>(true, this, &ZGSPUserManagerPrx::_iceI_deleteUserFace, userID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto deleteUserFaceAsync(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::DeleteUserFaceResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::DeleteUserFaceResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_deleteUserFace, userID, context);
    }

    ::std::function<void()>
    deleteUserFaceAsync(const ::std::string& userID,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::DeleteUserFaceResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::DeleteUserFaceResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_deleteUserFace, userID, context);
    }

    /// \cond INTERNAL
    void _iceI_deleteUserFace(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::DeleteUserFaceResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool updateUserFinger(const ::std::string& userID, int fingerNo, const ::std::string& fingerData, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::UpdateUserFingerResult>(true, this, &ZGSPUserManagerPrx::_iceI_updateUserFinger, userID, fingerNo, fingerData, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateUserFingerAsync(const ::std::string& userID, int fingerNo, const ::std::string& fingerData, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::UpdateUserFingerResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::UpdateUserFingerResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_updateUserFinger, userID, fingerNo, fingerData, context);
    }

    ::std::function<void()>
    updateUserFingerAsync(const ::std::string& userID, int fingerNo, const ::std::string& fingerData,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::UpdateUserFingerResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::UpdateUserFingerResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_updateUserFinger, userID, fingerNo, fingerData, context);
    }

    /// \cond INTERNAL
    void _iceI_updateUserFinger(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::UpdateUserFingerResult>>&, const ::std::string&, int, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool deleteUserFinger(const ::std::string& userID, int fingerNo, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::DeleteUserFingerResult>(true, this, &ZGSPUserManagerPrx::_iceI_deleteUserFinger, userID, fingerNo, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto deleteUserFingerAsync(const ::std::string& userID, int fingerNo, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::DeleteUserFingerResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::DeleteUserFingerResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_deleteUserFinger, userID, fingerNo, context);
    }

    ::std::function<void()>
    deleteUserFingerAsync(const ::std::string& userID, int fingerNo,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::DeleteUserFingerResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::DeleteUserFingerResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_deleteUserFinger, userID, fingerNo, context);
    }

    /// \cond INTERNAL
    void _iceI_deleteUserFinger(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::DeleteUserFingerResult>>&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    bool changePassword(const ::std::string& userID, const ::std::string& oldPassword, const ::std::string& newPassword, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::ChangePasswordResult>(true, this, &ZGSPUserManagerPrx::_iceI_changePassword, userID, oldPassword, newPassword, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto changePasswordAsync(const ::std::string& userID, const ::std::string& oldPassword, const ::std::string& newPassword, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::ChangePasswordResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::ChangePasswordResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_changePassword, userID, oldPassword, newPassword, context);
    }

    ::std::function<void()>
    changePasswordAsync(const ::std::string& userID, const ::std::string& oldPassword, const ::std::string& newPassword,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::ChangePasswordResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::ChangePasswordResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_changePassword, userID, oldPassword, newPassword, context);
    }

    /// \cond INTERNAL
    void _iceI_changePassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::ChangePasswordResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool resetPassword(const ::std::string& userID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::ResetPasswordResult>(true, this, &ZGSPUserManagerPrx::_iceI_resetPassword, userID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto resetPasswordAsync(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::ResetPasswordResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::ResetPasswordResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_resetPassword, userID, context);
    }

    ::std::function<void()>
    resetPasswordAsync(const ::std::string& userID,
                       ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::ResetPasswordResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::ResetPasswordResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_resetPassword, userID, context);
    }

    /// \cond INTERNAL
    void _iceI_resetPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::ResetPasswordResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool isUserHasPower(const ::std::string& userID, const ::std::string& powerID, bool& hasPower, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::IsUserHasPowerResult>(true, this, &ZGSPUserManagerPrx::_iceI_isUserHasPower, userID, powerID, context).get();
        hasPower = _result.hasPower;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto isUserHasPowerAsync(const ::std::string& userID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::IsUserHasPowerResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::IsUserHasPowerResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_isUserHasPower, userID, powerID, context);
    }

    ::std::function<void()>
    isUserHasPowerAsync(const ::std::string& userID, const ::std::string& powerID,
                        ::std::function<void(bool, bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::IsUserHasPowerResult&& _result)
        {
            response(_result.returnValue, _result.hasPower, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::IsUserHasPowerResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_isUserHasPower, userID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_isUserHasPower(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::IsUserHasPowerResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool isCardBindUser(const ::std::string& cardID, bool& isBind, ::std::string& userID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::IsCardBindUserResult>(true, this, &ZGSPUserManagerPrx::_iceI_isCardBindUser, cardID, context).get();
        isBind = _result.isBind;
        userID = ::std::move(_result.userID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto isCardBindUserAsync(const ::std::string& cardID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::IsCardBindUserResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::IsCardBindUserResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_isCardBindUser, cardID, context);
    }

    ::std::function<void()>
    isCardBindUserAsync(const ::std::string& cardID,
                        ::std::function<void(bool, bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::IsCardBindUserResult&& _result)
        {
            response(_result.returnValue, _result.isBind, ::std::move(_result.userID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::IsCardBindUserResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_isCardBindUser, cardID, context);
    }

    /// \cond INTERNAL
    void _iceI_isCardBindUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::IsCardBindUserResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, int keepTime, ::std::string& outClientID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::LoginByPasswordResult>(true, this, &ZGSPUserManagerPrx::_iceI_loginByPassword, clientID, userID, password, keepTime, context).get();
        outClientID = ::std::move(_result.outClientID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto loginByPasswordAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::LoginByPasswordResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::LoginByPasswordResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_loginByPassword, clientID, userID, password, keepTime, context);
    }

    ::std::function<void()>
    loginByPasswordAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, int keepTime,
                         ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::LoginByPasswordResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.outClientID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::LoginByPasswordResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_loginByPassword, clientID, userID, password, keepTime, context);
    }

    /// \cond INTERNAL
    void _iceI_loginByPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::LoginByPasswordResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    bool loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, int keepTime, ::std::string& outClientID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::LoginByCardResult>(true, this, &ZGSPUserManagerPrx::_iceI_loginByCard, clientID, userID, authModeID, cardID, keepTime, context).get();
        outClientID = ::std::move(_result.outClientID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto loginByCardAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::LoginByCardResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::LoginByCardResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_loginByCard, clientID, userID, authModeID, cardID, keepTime, context);
    }

    ::std::function<void()>
    loginByCardAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, int keepTime,
                     ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::LoginByCardResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.outClientID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::LoginByCardResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_loginByCard, clientID, userID, authModeID, cardID, keepTime, context);
    }

    /// \cond INTERNAL
    void _iceI_loginByCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::LoginByCardResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    bool loginByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, int keepTime, ::std::string& outClientID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::LoginByVerifyCodeResult>(true, this, &ZGSPUserManagerPrx::_iceI_loginByVerifyCode, clientID, phoneNumber, code, keepTime, context).get();
        outClientID = ::std::move(_result.outClientID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto loginByVerifyCodeAsync(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::LoginByVerifyCodeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::LoginByVerifyCodeResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_loginByVerifyCode, clientID, phoneNumber, code, keepTime, context);
    }

    ::std::function<void()>
    loginByVerifyCodeAsync(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, int keepTime,
                           ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::LoginByVerifyCodeResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.outClientID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::LoginByVerifyCodeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_loginByVerifyCode, clientID, phoneNumber, code, keepTime, context);
    }

    /// \cond INTERNAL
    void _iceI_loginByVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::LoginByVerifyCodeResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    bool loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, int keepTime, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::LoginByAuthDevResult>(true, this, &ZGSPUserManagerPrx::_iceI_loginByAuthDev, clientID, userID, authModeID, keepTime, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto loginByAuthDevAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::LoginByAuthDevResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::LoginByAuthDevResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_loginByAuthDev, clientID, userID, authModeID, keepTime, context);
    }

    ::std::function<void()>
    loginByAuthDevAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, int keepTime,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::LoginByAuthDevResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::LoginByAuthDevResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_loginByAuthDev, clientID, userID, authModeID, keepTime, context);
    }

    /// \cond INTERNAL
    void _iceI_loginByAuthDev(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::LoginByAuthDevResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, int, const ::Ice::Context&);
    /// \endcond

    bool logout(const ::std::string& clientID, const ::std::string& userID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::LogoutResult>(true, this, &ZGSPUserManagerPrx::_iceI_logout, clientID, userID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto logoutAsync(const ::std::string& clientID, const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::LogoutResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::LogoutResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_logout, clientID, userID, context);
    }

    ::std::function<void()>
    logoutAsync(const ::std::string& clientID, const ::std::string& userID,
                ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                ::std::function<void(::std::exception_ptr)> ex = nullptr,
                ::std::function<void(bool)> sent = nullptr,
                const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::LogoutResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::LogoutResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_logout, clientID, userID, context);
    }

    /// \cond INTERNAL
    void _iceI_logout(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::LogoutResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool sendVerifyCode(const ::std::string& phoneNumber, ::std::string& seqNo, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::SendVerifyCodeResult>(true, this, &ZGSPUserManagerPrx::_iceI_sendVerifyCode, phoneNumber, context).get();
        seqNo = ::std::move(_result.seqNo);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto sendVerifyCodeAsync(const ::std::string& phoneNumber, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::SendVerifyCodeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::SendVerifyCodeResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_sendVerifyCode, phoneNumber, context);
    }

    ::std::function<void()>
    sendVerifyCodeAsync(const ::std::string& phoneNumber,
                        ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::SendVerifyCodeResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.seqNo), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::SendVerifyCodeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_sendVerifyCode, phoneNumber, context);
    }

    /// \cond INTERNAL
    void _iceI_sendVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::SendVerifyCodeResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, ListStringMap& lstUser, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::GetAvaiableUserResult>(true, this, &ZGSPUserManagerPrx::_iceI_getAvaiableUser, clientID, appNodeID, powerID, context).get();
        lstUser = ::std::move(_result.lstUser);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getAvaiableUserAsync(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::GetAvaiableUserResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::GetAvaiableUserResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_getAvaiableUser, clientID, appNodeID, powerID, context);
    }

    ::std::function<void()>
    getAvaiableUserAsync(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID,
                         ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::GetAvaiableUserResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.lstUser), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::GetAvaiableUserResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_getAvaiableUser, clientID, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_getAvaiableUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::GetAvaiableUserResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::VerifyByPasswordResult>(true, this, &ZGSPUserManagerPrx::_iceI_verifyByPassword, clientID, userID, password, appNodeID, powerID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByPasswordAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::VerifyByPasswordResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::VerifyByPasswordResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_verifyByPassword, clientID, userID, password, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByPasswordAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::VerifyByPasswordResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::VerifyByPasswordResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_verifyByPassword, clientID, userID, password, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByPasswordResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::VerifyByCardResult>(true, this, &ZGSPUserManagerPrx::_iceI_verifyByCard, clientID, userID, authModeID, cardID, appNodeID, powerID, context).get();
        realUserID = ::std::move(_result.realUserID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByCardAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::VerifyByCardResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::VerifyByCardResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_verifyByCard, clientID, userID, authModeID, cardID, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByCardAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID,
                      ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::VerifyByCardResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.realUserID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::VerifyByCardResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_verifyByCard, clientID, userID, authModeID, cardID, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByCardResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::VerifyByVerifyCodeResult>(true, this, &ZGSPUserManagerPrx::_iceI_verifyByVerifyCode, clientID, phoneNumber, code, appNodeID, powerID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByVerifyCodeAsync(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::VerifyByVerifyCodeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::VerifyByVerifyCodeResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_verifyByVerifyCode, clientID, phoneNumber, code, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByVerifyCodeAsync(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, const ::std::string& appNodeID, const ::std::string& powerID,
                            ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::VerifyByVerifyCodeResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::VerifyByVerifyCodeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_verifyByVerifyCode, clientID, phoneNumber, code, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByVerifyCodeResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::VerifyByAuthDevResult>(true, this, &ZGSPUserManagerPrx::_iceI_verifyByAuthDev, clientID, userID, authModeID, appNodeID, powerID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByAuthDevAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::VerifyByAuthDevResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::VerifyByAuthDevResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_verifyByAuthDev, clientID, userID, authModeID, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByAuthDevAsync(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID,
                         ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::VerifyByAuthDevResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::VerifyByAuthDevResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_verifyByAuthDev, clientID, userID, authModeID, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByAuthDev(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByAuthDevResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByPasswordNoClient(const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::VerifyByPasswordNoClientResult>(true, this, &ZGSPUserManagerPrx::_iceI_verifyByPasswordNoClient, userID, password, appNodeID, powerID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByPasswordNoClientAsync(const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::VerifyByPasswordNoClientResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::VerifyByPasswordNoClientResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_verifyByPasswordNoClient, userID, password, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByPasswordNoClientAsync(const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID,
                                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                  ::std::function<void(bool)> sent = nullptr,
                                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::VerifyByPasswordNoClientResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::VerifyByPasswordNoClientResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_verifyByPasswordNoClient, userID, password, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByPasswordNoClient(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByPasswordNoClientResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByCardNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::VerifyByCardNoClientResult>(true, this, &ZGSPUserManagerPrx::_iceI_verifyByCardNoClient, userID, authModeID, cardID, appNodeID, powerID, context).get();
        realUserID = ::std::move(_result.realUserID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByCardNoClientAsync(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::VerifyByCardNoClientResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::VerifyByCardNoClientResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_verifyByCardNoClient, userID, authModeID, cardID, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByCardNoClientAsync(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID,
                              ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                              ::std::function<void(::std::exception_ptr)> ex = nullptr,
                              ::std::function<void(bool)> sent = nullptr,
                              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::VerifyByCardNoClientResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.realUserID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::VerifyByCardNoClientResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_verifyByCardNoClient, userID, authModeID, cardID, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByCardNoClient(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByCardNoClientResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool verifyByAuthDevNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::VerifyByAuthDevNoClientResult>(true, this, &ZGSPUserManagerPrx::_iceI_verifyByAuthDevNoClient, userID, authModeID, appNodeID, powerID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto verifyByAuthDevNoClientAsync(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::VerifyByAuthDevNoClientResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::VerifyByAuthDevNoClientResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_verifyByAuthDevNoClient, userID, authModeID, appNodeID, powerID, context);
    }

    ::std::function<void()>
    verifyByAuthDevNoClientAsync(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID,
                                 ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                                 ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                 ::std::function<void(bool)> sent = nullptr,
                                 const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::VerifyByAuthDevNoClientResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::VerifyByAuthDevNoClientResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_verifyByAuthDevNoClient, userID, authModeID, appNodeID, powerID, context);
    }

    /// \cond INTERNAL
    void _iceI_verifyByAuthDevNoClient(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByAuthDevNoClientResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    void cancelAuth(const ::std::string& clientID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSPUserManagerPrx::_iceI_cancelAuth, clientID, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto cancelAuthAsync(const ::std::string& clientID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSPUserManagerPrx::_iceI_cancelAuth, clientID, context);
    }

    ::std::function<void()>
    cancelAuthAsync(const ::std::string& clientID,
                    ::std::function<void()> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_cancelAuth, clientID, context);
    }

    /// \cond INTERNAL
    void _iceI_cancelAuth(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool sendRandomPassword(const ::std::string& userID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSPUserManager::SendRandomPasswordResult>(true, this, &ZGSPUserManagerPrx::_iceI_sendRandomPassword, userID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto sendRandomPasswordAsync(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSPUserManager::SendRandomPasswordResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSPUserManager::SendRandomPasswordResult, P>(false, this, &ZGSPUserManagerPrx::_iceI_sendRandomPassword, userID, context);
    }

    ::std::function<void()>
    sendRandomPasswordAsync(const ::std::string& userID,
                            ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSPUserManager::SendRandomPasswordResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSPUserManager::SendRandomPasswordResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSPUserManagerPrx::_iceI_sendRandomPassword, userID, context);
    }

    /// \cond INTERNAL
    void _iceI_sendRandomPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::SendRandomPasswordResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSPUserManagerPrx() = default;
    friend ::std::shared_ptr<ZGSPUserManagerPrx> IceInternal::createProxy<ZGSPUserManagerPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSPUserManagerPtr = ::std::shared_ptr<ZGSPUserManager>;
using ZGSPUserManagerPrxPtr = ::std::shared_ptr<ZGSPUserManagerPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSPUserManager;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSPUserManager>&);
::IceProxy::Ice::Object* upCast(ZGSPUserManager*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSPUserManager;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSPUserManager*);
/// \endcond
typedef ::IceInternal::Handle< ZGSPUserManager> ZGSPUserManagerPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSPUserManager> ZGSPUserManagerPrx;
typedef ZGSPUserManagerPrx ZGSPUserManagerPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSPUserManagerPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getUserInfo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getUserInfo.
 */
class Callback_ZGSPUserManager_getUserInfo_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_getUserInfo_Base> Callback_ZGSPUserManager_getUserInfoPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getUserFingers.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getUserFingers.
 */
class Callback_ZGSPUserManager_getUserFingers_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_getUserFingers_Base> Callback_ZGSPUserManager_getUserFingersPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getUserFace.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getUserFace.
 */
class Callback_ZGSPUserManager_getUserFace_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_getUserFace_Base> Callback_ZGSPUserManager_getUserFacePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_addUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_addUser.
 */
class Callback_ZGSPUserManager_addUser_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_addUser_Base> Callback_ZGSPUserManager_addUserPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_updateUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_updateUser.
 */
class Callback_ZGSPUserManager_updateUser_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_updateUser_Base> Callback_ZGSPUserManager_updateUserPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUser.
 */
class Callback_ZGSPUserManager_deleteUser_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_deleteUser_Base> Callback_ZGSPUserManager_deleteUserPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_addUserCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_addUserCard.
 */
class Callback_ZGSPUserManager_addUserCard_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_addUserCard_Base> Callback_ZGSPUserManager_addUserCardPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUserCard.
 */
class Callback_ZGSPUserManager_deleteUserCard_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_deleteUserCard_Base> Callback_ZGSPUserManager_deleteUserCardPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFace.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_updateUserFace.
 */
class Callback_ZGSPUserManager_updateUserFace_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_updateUserFace_Base> Callback_ZGSPUserManager_updateUserFacePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFace.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUserFace.
 */
class Callback_ZGSPUserManager_deleteUserFace_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_deleteUserFace_Base> Callback_ZGSPUserManager_deleteUserFacePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFinger.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_updateUserFinger.
 */
class Callback_ZGSPUserManager_updateUserFinger_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_updateUserFinger_Base> Callback_ZGSPUserManager_updateUserFingerPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFinger.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUserFinger.
 */
class Callback_ZGSPUserManager_deleteUserFinger_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_deleteUserFinger_Base> Callback_ZGSPUserManager_deleteUserFingerPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_changePassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_changePassword.
 */
class Callback_ZGSPUserManager_changePassword_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_changePassword_Base> Callback_ZGSPUserManager_changePasswordPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_resetPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_resetPassword.
 */
class Callback_ZGSPUserManager_resetPassword_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_resetPassword_Base> Callback_ZGSPUserManager_resetPasswordPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_isUserHasPower.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_isUserHasPower.
 */
class Callback_ZGSPUserManager_isUserHasPower_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_isUserHasPower_Base> Callback_ZGSPUserManager_isUserHasPowerPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_isCardBindUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_isCardBindUser.
 */
class Callback_ZGSPUserManager_isCardBindUser_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_isCardBindUser_Base> Callback_ZGSPUserManager_isCardBindUserPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByPassword.
 */
class Callback_ZGSPUserManager_loginByPassword_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_loginByPassword_Base> Callback_ZGSPUserManager_loginByPasswordPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByCard.
 */
class Callback_ZGSPUserManager_loginByCard_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_loginByCard_Base> Callback_ZGSPUserManager_loginByCardPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByVerifyCode.
 */
class Callback_ZGSPUserManager_loginByVerifyCode_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_loginByVerifyCode_Base> Callback_ZGSPUserManager_loginByVerifyCodePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByAuthDev.
 */
class Callback_ZGSPUserManager_loginByAuthDev_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_loginByAuthDev_Base> Callback_ZGSPUserManager_loginByAuthDevPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_logout.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_logout.
 */
class Callback_ZGSPUserManager_logout_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_logout_Base> Callback_ZGSPUserManager_logoutPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_sendVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_sendVerifyCode.
 */
class Callback_ZGSPUserManager_sendVerifyCode_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_sendVerifyCode_Base> Callback_ZGSPUserManager_sendVerifyCodePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getAvaiableUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getAvaiableUser.
 */
class Callback_ZGSPUserManager_getAvaiableUser_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_getAvaiableUser_Base> Callback_ZGSPUserManager_getAvaiableUserPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByPassword.
 */
class Callback_ZGSPUserManager_verifyByPassword_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_verifyByPassword_Base> Callback_ZGSPUserManager_verifyByPasswordPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByCard.
 */
class Callback_ZGSPUserManager_verifyByCard_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_verifyByCard_Base> Callback_ZGSPUserManager_verifyByCardPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByVerifyCode.
 */
class Callback_ZGSPUserManager_verifyByVerifyCode_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_verifyByVerifyCode_Base> Callback_ZGSPUserManager_verifyByVerifyCodePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByAuthDev.
 */
class Callback_ZGSPUserManager_verifyByAuthDev_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_verifyByAuthDev_Base> Callback_ZGSPUserManager_verifyByAuthDevPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPasswordNoClient.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByPasswordNoClient.
 */
class Callback_ZGSPUserManager_verifyByPasswordNoClient_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_verifyByPasswordNoClient_Base> Callback_ZGSPUserManager_verifyByPasswordNoClientPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCardNoClient.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByCardNoClient.
 */
class Callback_ZGSPUserManager_verifyByCardNoClient_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_verifyByCardNoClient_Base> Callback_ZGSPUserManager_verifyByCardNoClientPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDevNoClient.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByAuthDevNoClient.
 */
class Callback_ZGSPUserManager_verifyByAuthDevNoClient_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_verifyByAuthDevNoClient_Base> Callback_ZGSPUserManager_verifyByAuthDevNoClientPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_cancelAuth.
 */
class Callback_ZGSPUserManager_cancelAuth_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_cancelAuth_Base> Callback_ZGSPUserManager_cancelAuthPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_sendRandomPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_sendRandomPassword.
 */
class Callback_ZGSPUserManager_sendRandomPassword_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSPUserManager_sendRandomPassword_Base> Callback_ZGSPUserManager_sendRandomPasswordPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSPUserManager : public virtual ::Ice::Proxy<ZGSPUserManager, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool getUserInfo(const ::std::string& userID, ::ZG6000::StringMap& user, ::ZG6000::ListStringMap& listRole, ::ZG6000::ListStringMap& listCard, ::ZG6000::ListStringMap& listAuth, ::ZG6000::ListStringMap& listAppNode, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getUserInfo(user, listRole, listCard, listAuth, listAppNode, e, _iceI_begin_getUserInfo(userID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getUserInfo(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getUserInfo(userID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getUserInfo(const ::std::string& userID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserInfo(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserInfo(const ::std::string& userID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserInfo(userID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserInfo(const ::std::string& userID, const ::ZG6000::Callback_ZGSPUserManager_getUserInfoPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserInfo(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserInfo(const ::std::string& userID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_getUserInfoPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserInfo(userID, context, cb, cookie);
    }

    bool end_getUserInfo(::ZG6000::StringMap& user, ::ZG6000::ListStringMap& listRole, ::ZG6000::ListStringMap& listCard, ::ZG6000::ListStringMap& listAuth, ::ZG6000::ListStringMap& listAppNode, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getUserInfo(::ZG6000::StringMap& iceP_user, ::ZG6000::ListStringMap& iceP_listRole, ::ZG6000::ListStringMap& iceP_listCard, ::ZG6000::ListStringMap& iceP_listAuth, ::ZG6000::ListStringMap& iceP_listAppNode, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getUserInfo(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getUserFingers(const ::std::string& userID, ::ZG6000::ListStringMap& listFinger, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getUserFingers(listFinger, e, _iceI_begin_getUserFingers(userID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getUserFingers(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getUserFingers(userID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getUserFingers(const ::std::string& userID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserFingers(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserFingers(const ::std::string& userID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserFingers(userID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserFingers(const ::std::string& userID, const ::ZG6000::Callback_ZGSPUserManager_getUserFingersPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserFingers(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserFingers(const ::std::string& userID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_getUserFingersPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserFingers(userID, context, cb, cookie);
    }

    bool end_getUserFingers(::ZG6000::ListStringMap& listFinger, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getUserFingers(::ZG6000::ListStringMap& iceP_listFinger, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getUserFingers(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getUserFace(const ::std::string& userID, ::std::string& faceData, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getUserFace(faceData, e, _iceI_begin_getUserFace(userID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getUserFace(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getUserFace(userID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getUserFace(const ::std::string& userID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserFace(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserFace(const ::std::string& userID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserFace(userID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserFace(const ::std::string& userID, const ::ZG6000::Callback_ZGSPUserManager_getUserFacePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserFace(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getUserFace(const ::std::string& userID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_getUserFacePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getUserFace(userID, context, cb, cookie);
    }

    bool end_getUserFace(::std::string& faceData, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getUserFace(::std::string& iceP_faceData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getUserFace(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool addUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_addUser(e, _iceI_begin_addUser(user, listRole, listAuth, listAppNode, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_addUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_addUser(user, listRole, listAuth, listAppNode, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_addUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addUser(user, listRole, listAuth, listAppNode, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addUser(user, listRole, listAuth, listAppNode, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, const ::ZG6000::Callback_ZGSPUserManager_addUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addUser(user, listRole, listAuth, listAppNode, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_addUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addUser(user, listRole, listAuth, listAppNode, context, cb, cookie);
    }

    bool end_addUser(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_addUser(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_addUser(const ::ZG6000::StringMap&, const ::ZG6000::ListStringMap&, const ::ZG6000::ListStringMap&, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateUser(e, _iceI_begin_updateUser(user, listRole, listAuth, listAppNode, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateUser(user, listRole, listAuth, listAppNode, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUser(user, listRole, listAuth, listAppNode, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUser(user, listRole, listAuth, listAppNode, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, const ::ZG6000::Callback_ZGSPUserManager_updateUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUser(user, listRole, listAuth, listAppNode, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateUser(const ::ZG6000::StringMap& user, const ::ZG6000::ListStringMap& listRole, const ::ZG6000::ListStringMap& listAuth, const ::ZG6000::ListStringMap& listAppNode, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_updateUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUser(user, listRole, listAuth, listAppNode, context, cb, cookie);
    }

    bool end_updateUser(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateUser(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateUser(const ::ZG6000::StringMap&, const ::ZG6000::ListStringMap&, const ::ZG6000::ListStringMap&, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool deleteUser(const ::std::string& userID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deleteUser(e, _iceI_begin_deleteUser(userID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_deleteUser(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deleteUser(userID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_deleteUser(const ::std::string& userID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUser(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUser(const ::std::string& userID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUser(userID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUser(const ::std::string& userID, const ::ZG6000::Callback_ZGSPUserManager_deleteUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUser(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUser(const ::std::string& userID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_deleteUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUser(userID, context, cb, cookie);
    }

    bool end_deleteUser(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deleteUser(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deleteUser(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool addUserCard(const ::std::string& userID, const ::std::string& cardNo, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_addUserCard(e, _iceI_begin_addUserCard(userID, cardNo, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_addUserCard(const ::std::string& userID, const ::std::string& cardNo, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_addUserCard(userID, cardNo, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_addUserCard(const ::std::string& userID, const ::std::string& cardNo, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addUserCard(userID, cardNo, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addUserCard(const ::std::string& userID, const ::std::string& cardNo, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addUserCard(userID, cardNo, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addUserCard(const ::std::string& userID, const ::std::string& cardNo, const ::ZG6000::Callback_ZGSPUserManager_addUserCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addUserCard(userID, cardNo, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addUserCard(const ::std::string& userID, const ::std::string& cardNo, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_addUserCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addUserCard(userID, cardNo, context, cb, cookie);
    }

    bool end_addUserCard(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_addUserCard(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_addUserCard(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool deleteUserCard(const ::std::string& cardNo, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deleteUserCard(e, _iceI_begin_deleteUserCard(cardNo, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_deleteUserCard(const ::std::string& cardNo, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deleteUserCard(cardNo, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_deleteUserCard(const ::std::string& cardNo, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserCard(cardNo, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUserCard(const ::std::string& cardNo, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserCard(cardNo, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUserCard(const ::std::string& cardNo, const ::ZG6000::Callback_ZGSPUserManager_deleteUserCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserCard(cardNo, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUserCard(const ::std::string& cardNo, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_deleteUserCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserCard(cardNo, context, cb, cookie);
    }

    bool end_deleteUserCard(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deleteUserCard(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deleteUserCard(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateUserFace(const ::std::string& userID, const ::std::string& faceData, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateUserFace(e, _iceI_begin_updateUserFace(userID, faceData, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateUserFace(const ::std::string& userID, const ::std::string& faceData, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateUserFace(userID, faceData, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateUserFace(const ::std::string& userID, const ::std::string& faceData, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUserFace(userID, faceData, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateUserFace(const ::std::string& userID, const ::std::string& faceData, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUserFace(userID, faceData, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateUserFace(const ::std::string& userID, const ::std::string& faceData, const ::ZG6000::Callback_ZGSPUserManager_updateUserFacePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUserFace(userID, faceData, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateUserFace(const ::std::string& userID, const ::std::string& faceData, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_updateUserFacePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUserFace(userID, faceData, context, cb, cookie);
    }

    bool end_updateUserFace(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateUserFace(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateUserFace(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool deleteUserFace(const ::std::string& userID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deleteUserFace(e, _iceI_begin_deleteUserFace(userID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_deleteUserFace(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deleteUserFace(userID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_deleteUserFace(const ::std::string& userID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserFace(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUserFace(const ::std::string& userID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserFace(userID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUserFace(const ::std::string& userID, const ::ZG6000::Callback_ZGSPUserManager_deleteUserFacePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserFace(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUserFace(const ::std::string& userID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_deleteUserFacePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserFace(userID, context, cb, cookie);
    }

    bool end_deleteUserFace(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deleteUserFace(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deleteUserFace(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::std::string& fingerData, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateUserFinger(e, _iceI_begin_updateUserFinger(userID, fingerNo, fingerData, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::std::string& fingerData, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateUserFinger(userID, fingerNo, fingerData, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::std::string& fingerData, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUserFinger(userID, fingerNo, fingerData, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::std::string& fingerData, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUserFinger(userID, fingerNo, fingerData, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::std::string& fingerData, const ::ZG6000::Callback_ZGSPUserManager_updateUserFingerPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUserFinger(userID, fingerNo, fingerData, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::std::string& fingerData, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_updateUserFingerPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateUserFinger(userID, fingerNo, fingerData, context, cb, cookie);
    }

    bool end_updateUserFinger(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateUserFinger(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateUserFinger(const ::std::string&, ::Ice::Int, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool deleteUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deleteUserFinger(e, _iceI_begin_deleteUserFinger(userID, fingerNo, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_deleteUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deleteUserFinger(userID, fingerNo, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_deleteUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserFinger(userID, fingerNo, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserFinger(userID, fingerNo, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::ZG6000::Callback_ZGSPUserManager_deleteUserFingerPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserFinger(userID, fingerNo, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_deleteUserFingerPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteUserFinger(userID, fingerNo, context, cb, cookie);
    }

    bool end_deleteUserFinger(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deleteUserFinger(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deleteUserFinger(const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool changePassword(const ::std::string& userID, const ::std::string& oldPassword, const ::std::string& newPassword, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_changePassword(e, _iceI_begin_changePassword(userID, oldPassword, newPassword, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_changePassword(const ::std::string& userID, const ::std::string& oldPassword, const ::std::string& newPassword, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_changePassword(userID, oldPassword, newPassword, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_changePassword(const ::std::string& userID, const ::std::string& oldPassword, const ::std::string& newPassword, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePassword(userID, oldPassword, newPassword, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changePassword(const ::std::string& userID, const ::std::string& oldPassword, const ::std::string& newPassword, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePassword(userID, oldPassword, newPassword, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changePassword(const ::std::string& userID, const ::std::string& oldPassword, const ::std::string& newPassword, const ::ZG6000::Callback_ZGSPUserManager_changePasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePassword(userID, oldPassword, newPassword, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changePassword(const ::std::string& userID, const ::std::string& oldPassword, const ::std::string& newPassword, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_changePasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePassword(userID, oldPassword, newPassword, context, cb, cookie);
    }

    bool end_changePassword(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_changePassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_changePassword(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool resetPassword(const ::std::string& userID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_resetPassword(e, _iceI_begin_resetPassword(userID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_resetPassword(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_resetPassword(userID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_resetPassword(const ::std::string& userID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetPassword(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resetPassword(const ::std::string& userID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetPassword(userID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resetPassword(const ::std::string& userID, const ::ZG6000::Callback_ZGSPUserManager_resetPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetPassword(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resetPassword(const ::std::string& userID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_resetPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetPassword(userID, context, cb, cookie);
    }

    bool end_resetPassword(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_resetPassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_resetPassword(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool isUserHasPower(const ::std::string& userID, const ::std::string& powerID, bool& hasPower, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_isUserHasPower(hasPower, e, _iceI_begin_isUserHasPower(userID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_isUserHasPower(const ::std::string& userID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_isUserHasPower(userID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_isUserHasPower(const ::std::string& userID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isUserHasPower(userID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isUserHasPower(const ::std::string& userID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isUserHasPower(userID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isUserHasPower(const ::std::string& userID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPUserManager_isUserHasPowerPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isUserHasPower(userID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isUserHasPower(const ::std::string& userID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_isUserHasPowerPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isUserHasPower(userID, powerID, context, cb, cookie);
    }

    bool end_isUserHasPower(bool& hasPower, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_isUserHasPower(bool& iceP_hasPower, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_isUserHasPower(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool isCardBindUser(const ::std::string& cardID, bool& isBind, ::std::string& userID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_isCardBindUser(isBind, userID, e, _iceI_begin_isCardBindUser(cardID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_isCardBindUser(const ::std::string& cardID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_isCardBindUser(cardID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_isCardBindUser(const ::std::string& cardID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isCardBindUser(cardID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isCardBindUser(const ::std::string& cardID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isCardBindUser(cardID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isCardBindUser(const ::std::string& cardID, const ::ZG6000::Callback_ZGSPUserManager_isCardBindUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isCardBindUser(cardID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isCardBindUser(const ::std::string& cardID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_isCardBindUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isCardBindUser(cardID, context, cb, cookie);
    }

    bool end_isCardBindUser(bool& isBind, ::std::string& userID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_isCardBindUser(bool& iceP_isBind, ::std::string& iceP_userID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_isCardBindUser(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, ::std::string& outClientID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_loginByPassword(outClientID, e, _iceI_begin_loginByPassword(clientID, userID, password, keepTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_loginByPassword(clientID, userID, password, keepTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPassword(clientID, userID, password, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPassword(clientID, userID, password, keepTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, const ::ZG6000::Callback_ZGSPUserManager_loginByPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPassword(clientID, userID, password, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_loginByPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByPassword(clientID, userID, password, keepTime, context, cb, cookie);
    }

    bool end_loginByPassword(::std::string& outClientID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_loginByPassword(::std::string& iceP_outClientID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_loginByPassword(const ::std::string&, const ::std::string&, const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, ::std::string& outClientID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_loginByCard(outClientID, e, _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, const ::ZG6000::Callback_ZGSPUserManager_loginByCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_loginByCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByCard(clientID, userID, authModeID, cardID, keepTime, context, cb, cookie);
    }

    bool end_loginByCard(::std::string& outClientID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_loginByCard(::std::string& iceP_outClientID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_loginByCard(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool loginByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, ::Ice::Int keepTime, ::std::string& outClientID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_loginByVerifyCode(outClientID, e, _iceI_begin_loginByVerifyCode(clientID, phoneNumber, code, keepTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_loginByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, ::Ice::Int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_loginByVerifyCode(clientID, phoneNumber, code, keepTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_loginByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, ::Ice::Int keepTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByVerifyCode(clientID, phoneNumber, code, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, ::Ice::Int keepTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByVerifyCode(clientID, phoneNumber, code, keepTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, ::Ice::Int keepTime, const ::ZG6000::Callback_ZGSPUserManager_loginByVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByVerifyCode(clientID, phoneNumber, code, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, ::Ice::Int keepTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_loginByVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByVerifyCode(clientID, phoneNumber, code, keepTime, context, cb, cookie);
    }

    bool end_loginByVerifyCode(::std::string& outClientID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_loginByVerifyCode(::std::string& iceP_outClientID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_loginByVerifyCode(const ::std::string&, const ::std::string&, const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_loginByAuthDev(e, _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, const ::ZG6000::Callback_ZGSPUserManager_loginByAuthDevPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_loginByAuthDevPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_loginByAuthDev(clientID, userID, authModeID, keepTime, context, cb, cookie);
    }

    bool end_loginByAuthDev(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_loginByAuthDev(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_loginByAuthDev(const ::std::string&, const ::std::string&, const ::std::string&, ::Ice::Int, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool logout(const ::std::string& clientID, const ::std::string& userID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_logout(e, _iceI_begin_logout(clientID, userID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_logout(const ::std::string& clientID, const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_logout(clientID, userID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_logout(const ::std::string& clientID, const ::std::string& userID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_logout(clientID, userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_logout(const ::std::string& clientID, const ::std::string& userID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_logout(clientID, userID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_logout(const ::std::string& clientID, const ::std::string& userID, const ::ZG6000::Callback_ZGSPUserManager_logoutPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_logout(clientID, userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_logout(const ::std::string& clientID, const ::std::string& userID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_logoutPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_logout(clientID, userID, context, cb, cookie);
    }

    bool end_logout(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_logout(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_logout(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool sendVerifyCode(const ::std::string& phoneNumber, ::std::string& seqNo, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_sendVerifyCode(seqNo, e, _iceI_begin_sendVerifyCode(phoneNumber, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_sendVerifyCode(const ::std::string& phoneNumber, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_sendVerifyCode(phoneNumber, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_sendVerifyCode(const ::std::string& phoneNumber, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendVerifyCode(phoneNumber, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendVerifyCode(const ::std::string& phoneNumber, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendVerifyCode(phoneNumber, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendVerifyCode(const ::std::string& phoneNumber, const ::ZG6000::Callback_ZGSPUserManager_sendVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendVerifyCode(phoneNumber, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendVerifyCode(const ::std::string& phoneNumber, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_sendVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendVerifyCode(phoneNumber, context, cb, cookie);
    }

    bool end_sendVerifyCode(::std::string& seqNo, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_sendVerifyCode(::std::string& iceP_seqNo, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_sendVerifyCode(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, ::ZG6000::ListStringMap& lstUser, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getAvaiableUser(lstUser, e, _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPUserManager_getAvaiableUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_getAvaiableUserPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAvaiableUser(clientID, appNodeID, powerID, context, cb, cookie);
    }

    bool end_getAvaiableUser(::ZG6000::ListStringMap& lstUser, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getAvaiableUser(::ZG6000::ListStringMap& iceP_lstUser, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getAvaiableUser(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByPassword(e, _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPUserManager_verifyByPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_verifyByPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPassword(clientID, userID, password, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByPassword(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByPassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByPassword(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, ::std::string& realUserID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByCard(realUserID, e, _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPUserManager_verifyByCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_verifyByCardPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByCard(::std::string& realUserID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByCard(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByCard(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, const ::std::string& appNodeID, const ::std::string& powerID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByVerifyCode(e, _iceI_begin_verifyByVerifyCode(clientID, phoneNumber, code, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByVerifyCode(clientID, phoneNumber, code, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByVerifyCode(clientID, phoneNumber, code, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByVerifyCode(clientID, phoneNumber, code, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPUserManager_verifyByVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByVerifyCode(clientID, phoneNumber, code, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_verifyByVerifyCodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByVerifyCode(clientID, phoneNumber, code, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByVerifyCode(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByVerifyCode(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByVerifyCode(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByAuthDev(e, _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPUserManager_verifyByAuthDevPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_verifyByAuthDevPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByAuthDev(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByAuthDev(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByAuthDev(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByPasswordNoClient(const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByPasswordNoClient(e, _iceI_begin_verifyByPasswordNoClient(userID, password, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByPasswordNoClient(const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByPasswordNoClient(userID, password, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByPasswordNoClient(const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPasswordNoClient(userID, password, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPasswordNoClient(const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPasswordNoClient(userID, password, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPasswordNoClient(const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPUserManager_verifyByPasswordNoClientPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPasswordNoClient(userID, password, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByPasswordNoClient(const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_verifyByPasswordNoClientPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByPasswordNoClient(userID, password, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByPasswordNoClient(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByPasswordNoClient(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByPasswordNoClient(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByCardNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, ::std::string& realUserID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByCardNoClient(realUserID, e, _iceI_begin_verifyByCardNoClient(userID, authModeID, cardID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByCardNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByCardNoClient(userID, authModeID, cardID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByCardNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCardNoClient(userID, authModeID, cardID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByCardNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCardNoClient(userID, authModeID, cardID, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByCardNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPUserManager_verifyByCardNoClientPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCardNoClient(userID, authModeID, cardID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByCardNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_verifyByCardNoClientPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByCardNoClient(userID, authModeID, cardID, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByCardNoClient(::std::string& realUserID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByCardNoClient(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByCardNoClient(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool verifyByAuthDevNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_verifyByAuthDevNoClient(e, _iceI_begin_verifyByAuthDevNoClient(userID, authModeID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDevNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_verifyByAuthDevNoClient(userID, authModeID, appNodeID, powerID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDevNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDevNoClient(userID, authModeID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDevNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDevNoClient(userID, authModeID, appNodeID, powerID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDevNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::ZG6000::Callback_ZGSPUserManager_verifyByAuthDevNoClientPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDevNoClient(userID, authModeID, appNodeID, powerID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_verifyByAuthDevNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_verifyByAuthDevNoClientPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_verifyByAuthDevNoClient(userID, authModeID, appNodeID, powerID, context, cb, cookie);
    }

    bool end_verifyByAuthDevNoClient(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_verifyByAuthDevNoClient(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_verifyByAuthDevNoClient(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    void cancelAuth(const ::std::string& clientID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_cancelAuth(_iceI_begin_cancelAuth(clientID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_cancelAuth(const ::std::string& clientID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_cancelAuth(clientID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_cancelAuth(const ::std::string& clientID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelAuth(clientID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelAuth(const ::std::string& clientID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelAuth(clientID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelAuth(const ::std::string& clientID, const ::ZG6000::Callback_ZGSPUserManager_cancelAuthPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelAuth(clientID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelAuth(const ::std::string& clientID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_cancelAuthPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelAuth(clientID, context, cb, cookie);
    }

    void end_cancelAuth(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_cancelAuth(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool sendRandomPassword(const ::std::string& userID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_sendRandomPassword(e, _iceI_begin_sendRandomPassword(userID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_sendRandomPassword(const ::std::string& userID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_sendRandomPassword(userID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_sendRandomPassword(const ::std::string& userID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendRandomPassword(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendRandomPassword(const ::std::string& userID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendRandomPassword(userID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendRandomPassword(const ::std::string& userID, const ::ZG6000::Callback_ZGSPUserManager_sendRandomPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendRandomPassword(userID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendRandomPassword(const ::std::string& userID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSPUserManager_sendRandomPasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendRandomPassword(userID, context, cb, cookie);
    }

    bool end_sendRandomPassword(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_sendRandomPassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_sendRandomPassword(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSPUserManager : virtual public ZGServerBase
{
public:

    typedef ZGSPUserManagerPrx ProxyType;
    typedef ZGSPUserManagerPtr PointerType;

    virtual ~ZGSPUserManager();

#ifdef ICE_CPP11_COMPILER
    ZGSPUserManager() = default;
    ZGSPUserManager(const ZGSPUserManager&) = default;
    ZGSPUserManager& operator=(const ZGSPUserManager&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool getUserInfo(const ::std::string& userID, StringMap& user, ListStringMap& listRole, ListStringMap& listCard, ListStringMap& listAuth, ListStringMap& listAppNode, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserInfo(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getUserFingers(const ::std::string& userID, ListStringMap& listFinger, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserFingers(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getUserFace(const ::std::string& userID, ::std::string& faceData, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getUserFace(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool addUser(const StringMap& user, const ListStringMap& listRole, const ListStringMap& listAuth, const ListStringMap& listAppNode, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_addUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateUser(const StringMap& user, const ListStringMap& listRole, const ListStringMap& listAuth, const ListStringMap& listAppNode, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool deleteUser(const ::std::string& userID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool addUserCard(const ::std::string& userID, const ::std::string& cardNo, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_addUserCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool deleteUserCard(const ::std::string& cardNo, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteUserCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateUserFace(const ::std::string& userID, const ::std::string& faceData, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateUserFace(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool deleteUserFace(const ::std::string& userID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteUserFace(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, const ::std::string& fingerData, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateUserFinger(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool deleteUserFinger(const ::std::string& userID, ::Ice::Int fingerNo, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteUserFinger(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool changePassword(const ::std::string& userID, const ::std::string& oldPassword, const ::std::string& newPassword, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_changePassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool resetPassword(const ::std::string& userID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_resetPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool isUserHasPower(const ::std::string& userID, const ::std::string& powerID, bool& hasPower, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_isUserHasPower(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool isCardBindUser(const ::std::string& cardID, bool& isBind, ::std::string& userID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_isCardBindUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool loginByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, ::Ice::Int keepTime, ::std::string& outClientID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool loginByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, ::Ice::Int keepTime, ::std::string& outClientID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool loginByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, ::Ice::Int keepTime, ::std::string& outClientID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool loginByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, ::Ice::Int keepTime, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_loginByAuthDev(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool logout(const ::std::string& clientID, const ::std::string& userID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_logout(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool sendVerifyCode(const ::std::string& phoneNumber, ::std::string& seqNo, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_sendVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getAvaiableUser(const ::std::string& clientID, const ::std::string& appNodeID, const ::std::string& powerID, ListStringMap& lstUser, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getAvaiableUser(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByPassword(const ::std::string& clientID, const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByCard(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByCard(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByVerifyCode(const ::std::string& clientID, const ::std::string& phoneNumber, const ::std::string& code, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByVerifyCode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByAuthDev(const ::std::string& clientID, const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByAuthDev(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByPasswordNoClient(const ::std::string& userID, const ::std::string& password, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByPasswordNoClient(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByCardNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& cardID, const ::std::string& appNodeID, const ::std::string& powerID, ::std::string& realUserID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByCardNoClient(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool verifyByAuthDevNoClient(const ::std::string& userID, const ::std::string& authModeID, const ::std::string& appNodeID, const ::std::string& powerID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_verifyByAuthDevNoClient(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void cancelAuth(const ::std::string& clientID, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_cancelAuth(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool sendRandomPassword(const ::std::string& userID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_sendRandomPassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSPUserManager& lhs, const ZGSPUserManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSPUserManager& lhs, const ZGSPUserManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getUserInfo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getUserInfo.
 */
template<class T>
class CallbackNC_ZGSPUserManager_getUserInfo : public Callback_ZGSPUserManager_getUserInfo_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_getUserInfo(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        StringMap iceP_user;
        ListStringMap iceP_listRole;
        ListStringMap iceP_listCard;
        ListStringMap iceP_listAuth;
        ListStringMap iceP_listAppNode;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserInfo(iceP_user, iceP_listRole, iceP_listCard, iceP_listAuth, iceP_listAppNode, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_user, iceP_listRole, iceP_listCard, iceP_listAuth, iceP_listAppNode, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserInfo.
 */
template<class T> Callback_ZGSPUserManager_getUserInfoPtr
newCallback_ZGSPUserManager_getUserInfo(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_getUserInfo<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserInfo.
 */
template<class T> Callback_ZGSPUserManager_getUserInfoPtr
newCallback_ZGSPUserManager_getUserInfo(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_getUserInfo<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getUserInfo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getUserInfo.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_getUserInfo : public Callback_ZGSPUserManager_getUserInfo_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_getUserInfo(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        StringMap iceP_user;
        ListStringMap iceP_listRole;
        ListStringMap iceP_listCard;
        ListStringMap iceP_listAuth;
        ListStringMap iceP_listAppNode;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserInfo(iceP_user, iceP_listRole, iceP_listCard, iceP_listAuth, iceP_listAppNode, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_user, iceP_listRole, iceP_listCard, iceP_listAuth, iceP_listAppNode, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserInfo.
 */
template<class T, typename CT> Callback_ZGSPUserManager_getUserInfoPtr
newCallback_ZGSPUserManager_getUserInfo(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_getUserInfo<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserInfo.
 */
template<class T, typename CT> Callback_ZGSPUserManager_getUserInfoPtr
newCallback_ZGSPUserManager_getUserInfo(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_getUserInfo<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getUserFingers.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getUserFingers.
 */
template<class T>
class CallbackNC_ZGSPUserManager_getUserFingers : public Callback_ZGSPUserManager_getUserFingers_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_getUserFingers(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listFinger;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserFingers(iceP_listFinger, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listFinger, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserFingers.
 */
template<class T> Callback_ZGSPUserManager_getUserFingersPtr
newCallback_ZGSPUserManager_getUserFingers(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_getUserFingers<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserFingers.
 */
template<class T> Callback_ZGSPUserManager_getUserFingersPtr
newCallback_ZGSPUserManager_getUserFingers(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_getUserFingers<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getUserFingers.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getUserFingers.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_getUserFingers : public Callback_ZGSPUserManager_getUserFingers_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_getUserFingers(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listFinger;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserFingers(iceP_listFinger, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listFinger, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserFingers.
 */
template<class T, typename CT> Callback_ZGSPUserManager_getUserFingersPtr
newCallback_ZGSPUserManager_getUserFingers(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_getUserFingers<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserFingers.
 */
template<class T, typename CT> Callback_ZGSPUserManager_getUserFingersPtr
newCallback_ZGSPUserManager_getUserFingers(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_getUserFingers<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getUserFace.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getUserFace.
 */
template<class T>
class CallbackNC_ZGSPUserManager_getUserFace : public Callback_ZGSPUserManager_getUserFace_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_getUserFace(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_faceData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserFace(iceP_faceData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_faceData, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserFace.
 */
template<class T> Callback_ZGSPUserManager_getUserFacePtr
newCallback_ZGSPUserManager_getUserFace(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_getUserFace<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserFace.
 */
template<class T> Callback_ZGSPUserManager_getUserFacePtr
newCallback_ZGSPUserManager_getUserFace(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_getUserFace<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getUserFace.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getUserFace.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_getUserFace : public Callback_ZGSPUserManager_getUserFace_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_getUserFace(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_faceData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getUserFace(iceP_faceData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_faceData, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserFace.
 */
template<class T, typename CT> Callback_ZGSPUserManager_getUserFacePtr
newCallback_ZGSPUserManager_getUserFace(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_getUserFace<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getUserFace.
 */
template<class T, typename CT> Callback_ZGSPUserManager_getUserFacePtr
newCallback_ZGSPUserManager_getUserFace(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_getUserFace<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_addUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_addUser.
 */
template<class T>
class CallbackNC_ZGSPUserManager_addUser : public Callback_ZGSPUserManager_addUser_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_addUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addUser(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_addUser.
 */
template<class T> Callback_ZGSPUserManager_addUserPtr
newCallback_ZGSPUserManager_addUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_addUser<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_addUser.
 */
template<class T> Callback_ZGSPUserManager_addUserPtr
newCallback_ZGSPUserManager_addUser(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_addUser<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_addUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_addUser.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_addUser : public Callback_ZGSPUserManager_addUser_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_addUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addUser(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_addUser.
 */
template<class T, typename CT> Callback_ZGSPUserManager_addUserPtr
newCallback_ZGSPUserManager_addUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_addUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_addUser.
 */
template<class T, typename CT> Callback_ZGSPUserManager_addUserPtr
newCallback_ZGSPUserManager_addUser(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_addUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_updateUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_updateUser.
 */
template<class T>
class CallbackNC_ZGSPUserManager_updateUser : public Callback_ZGSPUserManager_updateUser_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_updateUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateUser(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUser.
 */
template<class T> Callback_ZGSPUserManager_updateUserPtr
newCallback_ZGSPUserManager_updateUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_updateUser<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUser.
 */
template<class T> Callback_ZGSPUserManager_updateUserPtr
newCallback_ZGSPUserManager_updateUser(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_updateUser<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_updateUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_updateUser.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_updateUser : public Callback_ZGSPUserManager_updateUser_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_updateUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateUser(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUser.
 */
template<class T, typename CT> Callback_ZGSPUserManager_updateUserPtr
newCallback_ZGSPUserManager_updateUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_updateUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUser.
 */
template<class T, typename CT> Callback_ZGSPUserManager_updateUserPtr
newCallback_ZGSPUserManager_updateUser(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_updateUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUser.
 */
template<class T>
class CallbackNC_ZGSPUserManager_deleteUser : public Callback_ZGSPUserManager_deleteUser_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_deleteUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteUser(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUser.
 */
template<class T> Callback_ZGSPUserManager_deleteUserPtr
newCallback_ZGSPUserManager_deleteUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_deleteUser<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUser.
 */
template<class T> Callback_ZGSPUserManager_deleteUserPtr
newCallback_ZGSPUserManager_deleteUser(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_deleteUser<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUser.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_deleteUser : public Callback_ZGSPUserManager_deleteUser_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_deleteUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteUser(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUser.
 */
template<class T, typename CT> Callback_ZGSPUserManager_deleteUserPtr
newCallback_ZGSPUserManager_deleteUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_deleteUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUser.
 */
template<class T, typename CT> Callback_ZGSPUserManager_deleteUserPtr
newCallback_ZGSPUserManager_deleteUser(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_deleteUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_addUserCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_addUserCard.
 */
template<class T>
class CallbackNC_ZGSPUserManager_addUserCard : public Callback_ZGSPUserManager_addUserCard_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_addUserCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addUserCard(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_addUserCard.
 */
template<class T> Callback_ZGSPUserManager_addUserCardPtr
newCallback_ZGSPUserManager_addUserCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_addUserCard<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_addUserCard.
 */
template<class T> Callback_ZGSPUserManager_addUserCardPtr
newCallback_ZGSPUserManager_addUserCard(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_addUserCard<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_addUserCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_addUserCard.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_addUserCard : public Callback_ZGSPUserManager_addUserCard_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_addUserCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addUserCard(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_addUserCard.
 */
template<class T, typename CT> Callback_ZGSPUserManager_addUserCardPtr
newCallback_ZGSPUserManager_addUserCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_addUserCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_addUserCard.
 */
template<class T, typename CT> Callback_ZGSPUserManager_addUserCardPtr
newCallback_ZGSPUserManager_addUserCard(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_addUserCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUserCard.
 */
template<class T>
class CallbackNC_ZGSPUserManager_deleteUserCard : public Callback_ZGSPUserManager_deleteUserCard_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_deleteUserCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteUserCard(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserCard.
 */
template<class T> Callback_ZGSPUserManager_deleteUserCardPtr
newCallback_ZGSPUserManager_deleteUserCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_deleteUserCard<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserCard.
 */
template<class T> Callback_ZGSPUserManager_deleteUserCardPtr
newCallback_ZGSPUserManager_deleteUserCard(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_deleteUserCard<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUserCard.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_deleteUserCard : public Callback_ZGSPUserManager_deleteUserCard_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_deleteUserCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteUserCard(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserCard.
 */
template<class T, typename CT> Callback_ZGSPUserManager_deleteUserCardPtr
newCallback_ZGSPUserManager_deleteUserCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_deleteUserCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserCard.
 */
template<class T, typename CT> Callback_ZGSPUserManager_deleteUserCardPtr
newCallback_ZGSPUserManager_deleteUserCard(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_deleteUserCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFace.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_updateUserFace.
 */
template<class T>
class CallbackNC_ZGSPUserManager_updateUserFace : public Callback_ZGSPUserManager_updateUserFace_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_updateUserFace(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateUserFace(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFace.
 */
template<class T> Callback_ZGSPUserManager_updateUserFacePtr
newCallback_ZGSPUserManager_updateUserFace(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_updateUserFace<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFace.
 */
template<class T> Callback_ZGSPUserManager_updateUserFacePtr
newCallback_ZGSPUserManager_updateUserFace(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_updateUserFace<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFace.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_updateUserFace.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_updateUserFace : public Callback_ZGSPUserManager_updateUserFace_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_updateUserFace(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateUserFace(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFace.
 */
template<class T, typename CT> Callback_ZGSPUserManager_updateUserFacePtr
newCallback_ZGSPUserManager_updateUserFace(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_updateUserFace<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFace.
 */
template<class T, typename CT> Callback_ZGSPUserManager_updateUserFacePtr
newCallback_ZGSPUserManager_updateUserFace(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_updateUserFace<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFace.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUserFace.
 */
template<class T>
class CallbackNC_ZGSPUserManager_deleteUserFace : public Callback_ZGSPUserManager_deleteUserFace_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_deleteUserFace(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteUserFace(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFace.
 */
template<class T> Callback_ZGSPUserManager_deleteUserFacePtr
newCallback_ZGSPUserManager_deleteUserFace(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_deleteUserFace<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFace.
 */
template<class T> Callback_ZGSPUserManager_deleteUserFacePtr
newCallback_ZGSPUserManager_deleteUserFace(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_deleteUserFace<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFace.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUserFace.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_deleteUserFace : public Callback_ZGSPUserManager_deleteUserFace_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_deleteUserFace(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteUserFace(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFace.
 */
template<class T, typename CT> Callback_ZGSPUserManager_deleteUserFacePtr
newCallback_ZGSPUserManager_deleteUserFace(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_deleteUserFace<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFace.
 */
template<class T, typename CT> Callback_ZGSPUserManager_deleteUserFacePtr
newCallback_ZGSPUserManager_deleteUserFace(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_deleteUserFace<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFinger.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_updateUserFinger.
 */
template<class T>
class CallbackNC_ZGSPUserManager_updateUserFinger : public Callback_ZGSPUserManager_updateUserFinger_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_updateUserFinger(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateUserFinger(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFinger.
 */
template<class T> Callback_ZGSPUserManager_updateUserFingerPtr
newCallback_ZGSPUserManager_updateUserFinger(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_updateUserFinger<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFinger.
 */
template<class T> Callback_ZGSPUserManager_updateUserFingerPtr
newCallback_ZGSPUserManager_updateUserFinger(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_updateUserFinger<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFinger.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_updateUserFinger.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_updateUserFinger : public Callback_ZGSPUserManager_updateUserFinger_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_updateUserFinger(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateUserFinger(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFinger.
 */
template<class T, typename CT> Callback_ZGSPUserManager_updateUserFingerPtr
newCallback_ZGSPUserManager_updateUserFinger(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_updateUserFinger<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_updateUserFinger.
 */
template<class T, typename CT> Callback_ZGSPUserManager_updateUserFingerPtr
newCallback_ZGSPUserManager_updateUserFinger(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_updateUserFinger<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFinger.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUserFinger.
 */
template<class T>
class CallbackNC_ZGSPUserManager_deleteUserFinger : public Callback_ZGSPUserManager_deleteUserFinger_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_deleteUserFinger(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteUserFinger(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFinger.
 */
template<class T> Callback_ZGSPUserManager_deleteUserFingerPtr
newCallback_ZGSPUserManager_deleteUserFinger(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_deleteUserFinger<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFinger.
 */
template<class T> Callback_ZGSPUserManager_deleteUserFingerPtr
newCallback_ZGSPUserManager_deleteUserFinger(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_deleteUserFinger<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFinger.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_deleteUserFinger.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_deleteUserFinger : public Callback_ZGSPUserManager_deleteUserFinger_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_deleteUserFinger(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteUserFinger(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFinger.
 */
template<class T, typename CT> Callback_ZGSPUserManager_deleteUserFingerPtr
newCallback_ZGSPUserManager_deleteUserFinger(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_deleteUserFinger<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_deleteUserFinger.
 */
template<class T, typename CT> Callback_ZGSPUserManager_deleteUserFingerPtr
newCallback_ZGSPUserManager_deleteUserFinger(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_deleteUserFinger<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_changePassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_changePassword.
 */
template<class T>
class CallbackNC_ZGSPUserManager_changePassword : public Callback_ZGSPUserManager_changePassword_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_changePassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_changePassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_changePassword.
 */
template<class T> Callback_ZGSPUserManager_changePasswordPtr
newCallback_ZGSPUserManager_changePassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_changePassword<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_changePassword.
 */
template<class T> Callback_ZGSPUserManager_changePasswordPtr
newCallback_ZGSPUserManager_changePassword(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_changePassword<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_changePassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_changePassword.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_changePassword : public Callback_ZGSPUserManager_changePassword_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_changePassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_changePassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_changePassword.
 */
template<class T, typename CT> Callback_ZGSPUserManager_changePasswordPtr
newCallback_ZGSPUserManager_changePassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_changePassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_changePassword.
 */
template<class T, typename CT> Callback_ZGSPUserManager_changePasswordPtr
newCallback_ZGSPUserManager_changePassword(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_changePassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_resetPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_resetPassword.
 */
template<class T>
class CallbackNC_ZGSPUserManager_resetPassword : public Callback_ZGSPUserManager_resetPassword_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_resetPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_resetPassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_resetPassword.
 */
template<class T> Callback_ZGSPUserManager_resetPasswordPtr
newCallback_ZGSPUserManager_resetPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_resetPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_resetPassword.
 */
template<class T> Callback_ZGSPUserManager_resetPasswordPtr
newCallback_ZGSPUserManager_resetPassword(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_resetPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_resetPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_resetPassword.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_resetPassword : public Callback_ZGSPUserManager_resetPassword_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_resetPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_resetPassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_resetPassword.
 */
template<class T, typename CT> Callback_ZGSPUserManager_resetPasswordPtr
newCallback_ZGSPUserManager_resetPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_resetPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_resetPassword.
 */
template<class T, typename CT> Callback_ZGSPUserManager_resetPasswordPtr
newCallback_ZGSPUserManager_resetPassword(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_resetPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_isUserHasPower.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_isUserHasPower.
 */
template<class T>
class CallbackNC_ZGSPUserManager_isUserHasPower : public Callback_ZGSPUserManager_isUserHasPower_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_isUserHasPower(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        bool iceP_hasPower;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isUserHasPower(iceP_hasPower, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_hasPower, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_isUserHasPower.
 */
template<class T> Callback_ZGSPUserManager_isUserHasPowerPtr
newCallback_ZGSPUserManager_isUserHasPower(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_isUserHasPower<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_isUserHasPower.
 */
template<class T> Callback_ZGSPUserManager_isUserHasPowerPtr
newCallback_ZGSPUserManager_isUserHasPower(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_isUserHasPower<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_isUserHasPower.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_isUserHasPower.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_isUserHasPower : public Callback_ZGSPUserManager_isUserHasPower_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_isUserHasPower(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        bool iceP_hasPower;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isUserHasPower(iceP_hasPower, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_hasPower, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_isUserHasPower.
 */
template<class T, typename CT> Callback_ZGSPUserManager_isUserHasPowerPtr
newCallback_ZGSPUserManager_isUserHasPower(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_isUserHasPower<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_isUserHasPower.
 */
template<class T, typename CT> Callback_ZGSPUserManager_isUserHasPowerPtr
newCallback_ZGSPUserManager_isUserHasPower(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_isUserHasPower<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_isCardBindUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_isCardBindUser.
 */
template<class T>
class CallbackNC_ZGSPUserManager_isCardBindUser : public Callback_ZGSPUserManager_isCardBindUser_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_isCardBindUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        bool iceP_isBind;
        ::std::string iceP_userID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isCardBindUser(iceP_isBind, iceP_userID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_isBind, iceP_userID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_isCardBindUser.
 */
template<class T> Callback_ZGSPUserManager_isCardBindUserPtr
newCallback_ZGSPUserManager_isCardBindUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_isCardBindUser<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_isCardBindUser.
 */
template<class T> Callback_ZGSPUserManager_isCardBindUserPtr
newCallback_ZGSPUserManager_isCardBindUser(T* instance, void (T::*cb)(bool, bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_isCardBindUser<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_isCardBindUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_isCardBindUser.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_isCardBindUser : public Callback_ZGSPUserManager_isCardBindUser_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_isCardBindUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        bool iceP_isBind;
        ::std::string iceP_userID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isCardBindUser(iceP_isBind, iceP_userID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_isBind, iceP_userID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_isCardBindUser.
 */
template<class T, typename CT> Callback_ZGSPUserManager_isCardBindUserPtr
newCallback_ZGSPUserManager_isCardBindUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_isCardBindUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_isCardBindUser.
 */
template<class T, typename CT> Callback_ZGSPUserManager_isCardBindUserPtr
newCallback_ZGSPUserManager_isCardBindUser(T* instance, void (T::*cb)(bool, bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_isCardBindUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByPassword.
 */
template<class T>
class CallbackNC_ZGSPUserManager_loginByPassword : public Callback_ZGSPUserManager_loginByPassword_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_loginByPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_outClientID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByPassword(iceP_outClientID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_outClientID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByPassword.
 */
template<class T> Callback_ZGSPUserManager_loginByPasswordPtr
newCallback_ZGSPUserManager_loginByPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_loginByPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByPassword.
 */
template<class T> Callback_ZGSPUserManager_loginByPasswordPtr
newCallback_ZGSPUserManager_loginByPassword(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_loginByPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByPassword.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_loginByPassword : public Callback_ZGSPUserManager_loginByPassword_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_loginByPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_outClientID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByPassword(iceP_outClientID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_outClientID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByPassword.
 */
template<class T, typename CT> Callback_ZGSPUserManager_loginByPasswordPtr
newCallback_ZGSPUserManager_loginByPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_loginByPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByPassword.
 */
template<class T, typename CT> Callback_ZGSPUserManager_loginByPasswordPtr
newCallback_ZGSPUserManager_loginByPassword(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_loginByPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByCard.
 */
template<class T>
class CallbackNC_ZGSPUserManager_loginByCard : public Callback_ZGSPUserManager_loginByCard_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_loginByCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_outClientID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByCard(iceP_outClientID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_outClientID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByCard.
 */
template<class T> Callback_ZGSPUserManager_loginByCardPtr
newCallback_ZGSPUserManager_loginByCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_loginByCard<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByCard.
 */
template<class T> Callback_ZGSPUserManager_loginByCardPtr
newCallback_ZGSPUserManager_loginByCard(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_loginByCard<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByCard.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_loginByCard : public Callback_ZGSPUserManager_loginByCard_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_loginByCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_outClientID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByCard(iceP_outClientID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_outClientID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByCard.
 */
template<class T, typename CT> Callback_ZGSPUserManager_loginByCardPtr
newCallback_ZGSPUserManager_loginByCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_loginByCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByCard.
 */
template<class T, typename CT> Callback_ZGSPUserManager_loginByCardPtr
newCallback_ZGSPUserManager_loginByCard(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_loginByCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByVerifyCode.
 */
template<class T>
class CallbackNC_ZGSPUserManager_loginByVerifyCode : public Callback_ZGSPUserManager_loginByVerifyCode_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_loginByVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_outClientID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByVerifyCode(iceP_outClientID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_outClientID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByVerifyCode.
 */
template<class T> Callback_ZGSPUserManager_loginByVerifyCodePtr
newCallback_ZGSPUserManager_loginByVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_loginByVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByVerifyCode.
 */
template<class T> Callback_ZGSPUserManager_loginByVerifyCodePtr
newCallback_ZGSPUserManager_loginByVerifyCode(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_loginByVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByVerifyCode.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_loginByVerifyCode : public Callback_ZGSPUserManager_loginByVerifyCode_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_loginByVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_outClientID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByVerifyCode(iceP_outClientID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_outClientID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPUserManager_loginByVerifyCodePtr
newCallback_ZGSPUserManager_loginByVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_loginByVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPUserManager_loginByVerifyCodePtr
newCallback_ZGSPUserManager_loginByVerifyCode(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_loginByVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByAuthDev.
 */
template<class T>
class CallbackNC_ZGSPUserManager_loginByAuthDev : public Callback_ZGSPUserManager_loginByAuthDev_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_loginByAuthDev(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByAuthDev(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByAuthDev.
 */
template<class T> Callback_ZGSPUserManager_loginByAuthDevPtr
newCallback_ZGSPUserManager_loginByAuthDev(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_loginByAuthDev<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByAuthDev.
 */
template<class T> Callback_ZGSPUserManager_loginByAuthDevPtr
newCallback_ZGSPUserManager_loginByAuthDev(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_loginByAuthDev<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_loginByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_loginByAuthDev.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_loginByAuthDev : public Callback_ZGSPUserManager_loginByAuthDev_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_loginByAuthDev(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_loginByAuthDev(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByAuthDev.
 */
template<class T, typename CT> Callback_ZGSPUserManager_loginByAuthDevPtr
newCallback_ZGSPUserManager_loginByAuthDev(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_loginByAuthDev<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_loginByAuthDev.
 */
template<class T, typename CT> Callback_ZGSPUserManager_loginByAuthDevPtr
newCallback_ZGSPUserManager_loginByAuthDev(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_loginByAuthDev<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_logout.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_logout.
 */
template<class T>
class CallbackNC_ZGSPUserManager_logout : public Callback_ZGSPUserManager_logout_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_logout(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_logout(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_logout.
 */
template<class T> Callback_ZGSPUserManager_logoutPtr
newCallback_ZGSPUserManager_logout(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_logout<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_logout.
 */
template<class T> Callback_ZGSPUserManager_logoutPtr
newCallback_ZGSPUserManager_logout(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_logout<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_logout.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_logout.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_logout : public Callback_ZGSPUserManager_logout_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_logout(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_logout(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_logout.
 */
template<class T, typename CT> Callback_ZGSPUserManager_logoutPtr
newCallback_ZGSPUserManager_logout(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_logout<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_logout.
 */
template<class T, typename CT> Callback_ZGSPUserManager_logoutPtr
newCallback_ZGSPUserManager_logout(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_logout<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_sendVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_sendVerifyCode.
 */
template<class T>
class CallbackNC_ZGSPUserManager_sendVerifyCode : public Callback_ZGSPUserManager_sendVerifyCode_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_sendVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_seqNo;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_sendVerifyCode(iceP_seqNo, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_seqNo, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_sendVerifyCode.
 */
template<class T> Callback_ZGSPUserManager_sendVerifyCodePtr
newCallback_ZGSPUserManager_sendVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_sendVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_sendVerifyCode.
 */
template<class T> Callback_ZGSPUserManager_sendVerifyCodePtr
newCallback_ZGSPUserManager_sendVerifyCode(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_sendVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_sendVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_sendVerifyCode.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_sendVerifyCode : public Callback_ZGSPUserManager_sendVerifyCode_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_sendVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_seqNo;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_sendVerifyCode(iceP_seqNo, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_seqNo, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_sendVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPUserManager_sendVerifyCodePtr
newCallback_ZGSPUserManager_sendVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_sendVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_sendVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPUserManager_sendVerifyCodePtr
newCallback_ZGSPUserManager_sendVerifyCode(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_sendVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getAvaiableUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getAvaiableUser.
 */
template<class T>
class CallbackNC_ZGSPUserManager_getAvaiableUser : public Callback_ZGSPUserManager_getAvaiableUser_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_getAvaiableUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_lstUser;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getAvaiableUser(iceP_lstUser, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_lstUser, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getAvaiableUser.
 */
template<class T> Callback_ZGSPUserManager_getAvaiableUserPtr
newCallback_ZGSPUserManager_getAvaiableUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_getAvaiableUser<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getAvaiableUser.
 */
template<class T> Callback_ZGSPUserManager_getAvaiableUserPtr
newCallback_ZGSPUserManager_getAvaiableUser(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_getAvaiableUser<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_getAvaiableUser.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_getAvaiableUser.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_getAvaiableUser : public Callback_ZGSPUserManager_getAvaiableUser_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_getAvaiableUser(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_lstUser;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getAvaiableUser(iceP_lstUser, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_lstUser, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getAvaiableUser.
 */
template<class T, typename CT> Callback_ZGSPUserManager_getAvaiableUserPtr
newCallback_ZGSPUserManager_getAvaiableUser(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_getAvaiableUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_getAvaiableUser.
 */
template<class T, typename CT> Callback_ZGSPUserManager_getAvaiableUserPtr
newCallback_ZGSPUserManager_getAvaiableUser(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_getAvaiableUser<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByPassword.
 */
template<class T>
class CallbackNC_ZGSPUserManager_verifyByPassword : public Callback_ZGSPUserManager_verifyByPassword_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_verifyByPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByPassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPassword.
 */
template<class T> Callback_ZGSPUserManager_verifyByPasswordPtr
newCallback_ZGSPUserManager_verifyByPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPassword.
 */
template<class T> Callback_ZGSPUserManager_verifyByPasswordPtr
newCallback_ZGSPUserManager_verifyByPassword(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByPassword.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_verifyByPassword : public Callback_ZGSPUserManager_verifyByPassword_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_verifyByPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByPassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPassword.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByPasswordPtr
newCallback_ZGSPUserManager_verifyByPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPassword.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByPasswordPtr
newCallback_ZGSPUserManager_verifyByPassword(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByCard.
 */
template<class T>
class CallbackNC_ZGSPUserManager_verifyByCard : public Callback_ZGSPUserManager_verifyByCard_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_verifyByCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_realUserID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByCard(iceP_realUserID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_realUserID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCard.
 */
template<class T> Callback_ZGSPUserManager_verifyByCardPtr
newCallback_ZGSPUserManager_verifyByCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByCard<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCard.
 */
template<class T> Callback_ZGSPUserManager_verifyByCardPtr
newCallback_ZGSPUserManager_verifyByCard(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByCard<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCard.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByCard.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_verifyByCard : public Callback_ZGSPUserManager_verifyByCard_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_verifyByCard(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_realUserID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByCard(iceP_realUserID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_realUserID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCard.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByCardPtr
newCallback_ZGSPUserManager_verifyByCard(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCard.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByCardPtr
newCallback_ZGSPUserManager_verifyByCard(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByCard<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByVerifyCode.
 */
template<class T>
class CallbackNC_ZGSPUserManager_verifyByVerifyCode : public Callback_ZGSPUserManager_verifyByVerifyCode_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_verifyByVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByVerifyCode(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByVerifyCode.
 */
template<class T> Callback_ZGSPUserManager_verifyByVerifyCodePtr
newCallback_ZGSPUserManager_verifyByVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByVerifyCode.
 */
template<class T> Callback_ZGSPUserManager_verifyByVerifyCodePtr
newCallback_ZGSPUserManager_verifyByVerifyCode(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByVerifyCode<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByVerifyCode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByVerifyCode.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_verifyByVerifyCode : public Callback_ZGSPUserManager_verifyByVerifyCode_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_verifyByVerifyCode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByVerifyCode(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByVerifyCodePtr
newCallback_ZGSPUserManager_verifyByVerifyCode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByVerifyCode.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByVerifyCodePtr
newCallback_ZGSPUserManager_verifyByVerifyCode(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByVerifyCode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByAuthDev.
 */
template<class T>
class CallbackNC_ZGSPUserManager_verifyByAuthDev : public Callback_ZGSPUserManager_verifyByAuthDev_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_verifyByAuthDev(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByAuthDev(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDev.
 */
template<class T> Callback_ZGSPUserManager_verifyByAuthDevPtr
newCallback_ZGSPUserManager_verifyByAuthDev(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByAuthDev<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDev.
 */
template<class T> Callback_ZGSPUserManager_verifyByAuthDevPtr
newCallback_ZGSPUserManager_verifyByAuthDev(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByAuthDev<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDev.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByAuthDev.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_verifyByAuthDev : public Callback_ZGSPUserManager_verifyByAuthDev_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_verifyByAuthDev(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByAuthDev(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDev.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByAuthDevPtr
newCallback_ZGSPUserManager_verifyByAuthDev(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByAuthDev<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDev.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByAuthDevPtr
newCallback_ZGSPUserManager_verifyByAuthDev(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByAuthDev<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPasswordNoClient.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByPasswordNoClient.
 */
template<class T>
class CallbackNC_ZGSPUserManager_verifyByPasswordNoClient : public Callback_ZGSPUserManager_verifyByPasswordNoClient_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_verifyByPasswordNoClient(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByPasswordNoClient(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPasswordNoClient.
 */
template<class T> Callback_ZGSPUserManager_verifyByPasswordNoClientPtr
newCallback_ZGSPUserManager_verifyByPasswordNoClient(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByPasswordNoClient<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPasswordNoClient.
 */
template<class T> Callback_ZGSPUserManager_verifyByPasswordNoClientPtr
newCallback_ZGSPUserManager_verifyByPasswordNoClient(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByPasswordNoClient<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPasswordNoClient.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByPasswordNoClient.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_verifyByPasswordNoClient : public Callback_ZGSPUserManager_verifyByPasswordNoClient_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_verifyByPasswordNoClient(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByPasswordNoClient(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPasswordNoClient.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByPasswordNoClientPtr
newCallback_ZGSPUserManager_verifyByPasswordNoClient(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByPasswordNoClient<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByPasswordNoClient.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByPasswordNoClientPtr
newCallback_ZGSPUserManager_verifyByPasswordNoClient(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByPasswordNoClient<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCardNoClient.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByCardNoClient.
 */
template<class T>
class CallbackNC_ZGSPUserManager_verifyByCardNoClient : public Callback_ZGSPUserManager_verifyByCardNoClient_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_verifyByCardNoClient(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_realUserID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByCardNoClient(iceP_realUserID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_realUserID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCardNoClient.
 */
template<class T> Callback_ZGSPUserManager_verifyByCardNoClientPtr
newCallback_ZGSPUserManager_verifyByCardNoClient(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByCardNoClient<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCardNoClient.
 */
template<class T> Callback_ZGSPUserManager_verifyByCardNoClientPtr
newCallback_ZGSPUserManager_verifyByCardNoClient(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByCardNoClient<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCardNoClient.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByCardNoClient.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_verifyByCardNoClient : public Callback_ZGSPUserManager_verifyByCardNoClient_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_verifyByCardNoClient(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_realUserID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByCardNoClient(iceP_realUserID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_realUserID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCardNoClient.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByCardNoClientPtr
newCallback_ZGSPUserManager_verifyByCardNoClient(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByCardNoClient<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByCardNoClient.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByCardNoClientPtr
newCallback_ZGSPUserManager_verifyByCardNoClient(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByCardNoClient<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDevNoClient.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByAuthDevNoClient.
 */
template<class T>
class CallbackNC_ZGSPUserManager_verifyByAuthDevNoClient : public Callback_ZGSPUserManager_verifyByAuthDevNoClient_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_verifyByAuthDevNoClient(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByAuthDevNoClient(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDevNoClient.
 */
template<class T> Callback_ZGSPUserManager_verifyByAuthDevNoClientPtr
newCallback_ZGSPUserManager_verifyByAuthDevNoClient(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByAuthDevNoClient<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDevNoClient.
 */
template<class T> Callback_ZGSPUserManager_verifyByAuthDevNoClientPtr
newCallback_ZGSPUserManager_verifyByAuthDevNoClient(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_verifyByAuthDevNoClient<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDevNoClient.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_verifyByAuthDevNoClient.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_verifyByAuthDevNoClient : public Callback_ZGSPUserManager_verifyByAuthDevNoClient_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_verifyByAuthDevNoClient(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_verifyByAuthDevNoClient(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDevNoClient.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByAuthDevNoClientPtr
newCallback_ZGSPUserManager_verifyByAuthDevNoClient(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByAuthDevNoClient<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_verifyByAuthDevNoClient.
 */
template<class T, typename CT> Callback_ZGSPUserManager_verifyByAuthDevNoClientPtr
newCallback_ZGSPUserManager_verifyByAuthDevNoClient(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_verifyByAuthDevNoClient<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_cancelAuth.
 */
template<class T>
class CallbackNC_ZGSPUserManager_cancelAuth : public Callback_ZGSPUserManager_cancelAuth_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSPUserManager_cancelAuth(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 */
template<class T> Callback_ZGSPUserManager_cancelAuthPtr
newCallback_ZGSPUserManager_cancelAuth(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_cancelAuth<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 */
template<class T> Callback_ZGSPUserManager_cancelAuthPtr
newCallback_ZGSPUserManager_cancelAuth(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_cancelAuth<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 */
template<class T> Callback_ZGSPUserManager_cancelAuthPtr
newCallback_ZGSPUserManager_cancelAuth(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_cancelAuth<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 */
template<class T> Callback_ZGSPUserManager_cancelAuthPtr
newCallback_ZGSPUserManager_cancelAuth(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_cancelAuth<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_cancelAuth.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_cancelAuth : public Callback_ZGSPUserManager_cancelAuth_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSPUserManager_cancelAuth(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 */
template<class T, typename CT> Callback_ZGSPUserManager_cancelAuthPtr
newCallback_ZGSPUserManager_cancelAuth(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_cancelAuth<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 */
template<class T, typename CT> Callback_ZGSPUserManager_cancelAuthPtr
newCallback_ZGSPUserManager_cancelAuth(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_cancelAuth<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 */
template<class T, typename CT> Callback_ZGSPUserManager_cancelAuthPtr
newCallback_ZGSPUserManager_cancelAuth(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_cancelAuth<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_cancelAuth.
 */
template<class T, typename CT> Callback_ZGSPUserManager_cancelAuthPtr
newCallback_ZGSPUserManager_cancelAuth(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_cancelAuth<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_sendRandomPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_sendRandomPassword.
 */
template<class T>
class CallbackNC_ZGSPUserManager_sendRandomPassword : public Callback_ZGSPUserManager_sendRandomPassword_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSPUserManager_sendRandomPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_sendRandomPassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_sendRandomPassword.
 */
template<class T> Callback_ZGSPUserManager_sendRandomPasswordPtr
newCallback_ZGSPUserManager_sendRandomPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_sendRandomPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_sendRandomPassword.
 */
template<class T> Callback_ZGSPUserManager_sendRandomPasswordPtr
newCallback_ZGSPUserManager_sendRandomPassword(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSPUserManager_sendRandomPassword<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSPUserManager::begin_sendRandomPassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSPUserManager_sendRandomPassword.
 */
template<class T, typename CT>
class Callback_ZGSPUserManager_sendRandomPassword : public Callback_ZGSPUserManager_sendRandomPassword_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSPUserManager_sendRandomPassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSPUserManagerPrx proxy = ZGSPUserManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_sendRandomPassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_sendRandomPassword.
 */
template<class T, typename CT> Callback_ZGSPUserManager_sendRandomPasswordPtr
newCallback_ZGSPUserManager_sendRandomPassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_sendRandomPassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSPUserManager::begin_sendRandomPassword.
 */
template<class T, typename CT> Callback_ZGSPUserManager_sendRandomPasswordPtr
newCallback_ZGSPUserManager_sendRandomPassword(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSPUserManager_sendRandomPassword<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

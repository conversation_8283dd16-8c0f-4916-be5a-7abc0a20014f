//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSPUserManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGSPUserManager.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPUserManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPUserManager",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGSPUserManager_ops[] =
{
    "addUser",
    "addUserCard",
    "cancelAuth",
    "changePassword",
    "checkState",
    "deleteUser",
    "deleteUserCard",
    "deleteUserFace",
    "deleteUserFinger",
    "dispatchData",
    "exitApp",
    "getAvaiableUser",
    "getUserFace",
    "getUserFingers",
    "getUserInfo",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isCardBindUser",
    "isDebugging",
    "isUserHasPower",
    "loginByAuthDev",
    "loginByCard",
    "loginByPassword",
    "loginByVerifyCode",
    "logout",
    "pauseDebug",
    "resetPassword",
    "resumeDebug",
    "sendRandomPassword",
    "sendVerifyCode",
    "startDebug",
    "stopDebug",
    "test",
    "updateUser",
    "updateUserFace",
    "updateUserFinger",
    "verifyByAuthDev",
    "verifyByAuthDevNoClient",
    "verifyByCard",
    "verifyByCardNoClient",
    "verifyByPassword",
    "verifyByPasswordNoClient",
    "verifyByVerifyCode"
};
const ::std::string iceC_ZG6000_ZGSPUserManager_getUserInfo_name = "getUserInfo";
const ::std::string iceC_ZG6000_ZGSPUserManager_getUserFingers_name = "getUserFingers";
const ::std::string iceC_ZG6000_ZGSPUserManager_getUserFace_name = "getUserFace";
const ::std::string iceC_ZG6000_ZGSPUserManager_addUser_name = "addUser";
const ::std::string iceC_ZG6000_ZGSPUserManager_updateUser_name = "updateUser";
const ::std::string iceC_ZG6000_ZGSPUserManager_deleteUser_name = "deleteUser";
const ::std::string iceC_ZG6000_ZGSPUserManager_addUserCard_name = "addUserCard";
const ::std::string iceC_ZG6000_ZGSPUserManager_deleteUserCard_name = "deleteUserCard";
const ::std::string iceC_ZG6000_ZGSPUserManager_updateUserFace_name = "updateUserFace";
const ::std::string iceC_ZG6000_ZGSPUserManager_deleteUserFace_name = "deleteUserFace";
const ::std::string iceC_ZG6000_ZGSPUserManager_updateUserFinger_name = "updateUserFinger";
const ::std::string iceC_ZG6000_ZGSPUserManager_deleteUserFinger_name = "deleteUserFinger";
const ::std::string iceC_ZG6000_ZGSPUserManager_changePassword_name = "changePassword";
const ::std::string iceC_ZG6000_ZGSPUserManager_resetPassword_name = "resetPassword";
const ::std::string iceC_ZG6000_ZGSPUserManager_isUserHasPower_name = "isUserHasPower";
const ::std::string iceC_ZG6000_ZGSPUserManager_isCardBindUser_name = "isCardBindUser";
const ::std::string iceC_ZG6000_ZGSPUserManager_loginByPassword_name = "loginByPassword";
const ::std::string iceC_ZG6000_ZGSPUserManager_loginByCard_name = "loginByCard";
const ::std::string iceC_ZG6000_ZGSPUserManager_loginByVerifyCode_name = "loginByVerifyCode";
const ::std::string iceC_ZG6000_ZGSPUserManager_loginByAuthDev_name = "loginByAuthDev";
const ::std::string iceC_ZG6000_ZGSPUserManager_logout_name = "logout";
const ::std::string iceC_ZG6000_ZGSPUserManager_sendVerifyCode_name = "sendVerifyCode";
const ::std::string iceC_ZG6000_ZGSPUserManager_getAvaiableUser_name = "getAvaiableUser";
const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByPassword_name = "verifyByPassword";
const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByCard_name = "verifyByCard";
const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByVerifyCode_name = "verifyByVerifyCode";
const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByAuthDev_name = "verifyByAuthDev";
const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByPasswordNoClient_name = "verifyByPasswordNoClient";
const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByCardNoClient_name = "verifyByCardNoClient";
const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByAuthDevNoClient_name = "verifyByAuthDevNoClient";
const ::std::string iceC_ZG6000_ZGSPUserManager_cancelAuth_name = "cancelAuth";
const ::std::string iceC_ZG6000_ZGSPUserManager_sendRandomPassword_name = "sendRandomPassword";

}

bool
ZG6000::ZGSPUserManager::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPUserManager_ids, iceC_ZG6000_ZGSPUserManager_ids + 3, s);
}

::std::vector<::std::string>
ZG6000::ZGSPUserManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGSPUserManager_ids[0], &iceC_ZG6000_ZGSPUserManager_ids[3]);
}

::std::string
ZG6000::ZGSPUserManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPUserManager::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGSPUserManager";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_getUserInfo(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->readAll(iceP_userID);
    inS.endReadParams();
    StringMap iceP_user;
    ListStringMap iceP_listRole;
    ListStringMap iceP_listCard;
    ListStringMap iceP_listAuth;
    ListStringMap iceP_listAppNode;
    ErrorInfo iceP_e;
    bool ret = this->getUserInfo(::std::move(iceP_userID), iceP_user, iceP_listRole, iceP_listCard, iceP_listAuth, iceP_listAppNode, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_user, iceP_listRole, iceP_listCard, iceP_listAuth, iceP_listAppNode, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_getUserFingers(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->readAll(iceP_userID);
    inS.endReadParams();
    ListStringMap iceP_listFinger;
    ErrorInfo iceP_e;
    bool ret = this->getUserFingers(::std::move(iceP_userID), iceP_listFinger, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listFinger, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_getUserFace(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->readAll(iceP_userID);
    inS.endReadParams();
    ::std::string iceP_faceData;
    ErrorInfo iceP_e;
    bool ret = this->getUserFace(::std::move(iceP_userID), iceP_faceData, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_faceData, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_addUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_user;
    ListStringMap iceP_listRole;
    ListStringMap iceP_listAuth;
    ListStringMap iceP_listAppNode;
    istr->readAll(iceP_user, iceP_listRole, iceP_listAuth, iceP_listAppNode);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addUser(::std::move(iceP_user), ::std::move(iceP_listRole), ::std::move(iceP_listAuth), ::std::move(iceP_listAppNode), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_updateUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_user;
    ListStringMap iceP_listRole;
    ListStringMap iceP_listAuth;
    ListStringMap iceP_listAppNode;
    istr->readAll(iceP_user, iceP_listRole, iceP_listAuth, iceP_listAppNode);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateUser(::std::move(iceP_user), ::std::move(iceP_listRole), ::std::move(iceP_listAuth), ::std::move(iceP_listAppNode), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_deleteUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->readAll(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteUser(::std::move(iceP_userID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_addUserCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_cardNo;
    istr->readAll(iceP_userID, iceP_cardNo);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addUserCard(::std::move(iceP_userID), ::std::move(iceP_cardNo), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_deleteUserCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_cardNo;
    istr->readAll(iceP_cardNo);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteUserCard(::std::move(iceP_cardNo), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_updateUserFace(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_faceData;
    istr->readAll(iceP_userID, iceP_faceData);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateUserFace(::std::move(iceP_userID), ::std::move(iceP_faceData), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_deleteUserFace(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->readAll(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteUserFace(::std::move(iceP_userID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_updateUserFinger(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    int iceP_fingerNo;
    ::std::string iceP_fingerData;
    istr->readAll(iceP_userID, iceP_fingerNo, iceP_fingerData);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateUserFinger(::std::move(iceP_userID), iceP_fingerNo, ::std::move(iceP_fingerData), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_deleteUserFinger(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    int iceP_fingerNo;
    istr->readAll(iceP_userID, iceP_fingerNo);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteUserFinger(::std::move(iceP_userID), iceP_fingerNo, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_changePassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_oldPassword;
    ::std::string iceP_newPassword;
    istr->readAll(iceP_userID, iceP_oldPassword, iceP_newPassword);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->changePassword(::std::move(iceP_userID), ::std::move(iceP_oldPassword), ::std::move(iceP_newPassword), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_resetPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->readAll(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->resetPassword(::std::move(iceP_userID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_isUserHasPower(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_userID, iceP_powerID);
    inS.endReadParams();
    bool iceP_hasPower;
    ErrorInfo iceP_e;
    bool ret = this->isUserHasPower(::std::move(iceP_userID), ::std::move(iceP_powerID), iceP_hasPower, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_hasPower, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_isCardBindUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_cardID;
    istr->readAll(iceP_cardID);
    inS.endReadParams();
    bool iceP_isBind;
    ::std::string iceP_userID;
    ErrorInfo iceP_e;
    bool ret = this->isCardBindUser(::std::move(iceP_cardID), iceP_isBind, iceP_userID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_isBind, iceP_userID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_loginByPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    int iceP_keepTime;
    istr->readAll(iceP_clientID, iceP_userID, iceP_password, iceP_keepTime);
    inS.endReadParams();
    ::std::string iceP_outClientID;
    ErrorInfo iceP_e;
    bool ret = this->loginByPassword(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_password), iceP_keepTime, iceP_outClientID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_outClientID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_loginByCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_cardID;
    int iceP_keepTime;
    istr->readAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_keepTime);
    inS.endReadParams();
    ::std::string iceP_outClientID;
    ErrorInfo iceP_e;
    bool ret = this->loginByCard(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_authModeID), ::std::move(iceP_cardID), iceP_keepTime, iceP_outClientID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_outClientID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_loginByVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_phoneNumber;
    ::std::string iceP_code;
    int iceP_keepTime;
    istr->readAll(iceP_clientID, iceP_phoneNumber, iceP_code, iceP_keepTime);
    inS.endReadParams();
    ::std::string iceP_outClientID;
    ErrorInfo iceP_e;
    bool ret = this->loginByVerifyCode(::std::move(iceP_clientID), ::std::move(iceP_phoneNumber), ::std::move(iceP_code), iceP_keepTime, iceP_outClientID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_outClientID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_loginByAuthDev(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    int iceP_keepTime;
    istr->readAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_keepTime);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->loginByAuthDev(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_authModeID), iceP_keepTime, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_logout(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    istr->readAll(iceP_clientID, iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->logout(::std::move(iceP_clientID), ::std::move(iceP_userID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_sendVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_phoneNumber;
    istr->readAll(iceP_phoneNumber);
    inS.endReadParams();
    ::std::string iceP_seqNo;
    ErrorInfo iceP_e;
    bool ret = this->sendVerifyCode(::std::move(iceP_phoneNumber), iceP_seqNo, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_seqNo, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_getAvaiableUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_clientID, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ListStringMap iceP_lstUser;
    ErrorInfo iceP_e;
    bool ret = this->getAvaiableUser(::std::move(iceP_clientID), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_lstUser, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_lstUser, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_clientID, iceP_userID, iceP_password, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByPassword(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_password), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_cardID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ::std::string iceP_realUserID;
    ErrorInfo iceP_e;
    bool ret = this->verifyByCard(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_authModeID), ::std::move(iceP_cardID), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_realUserID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_realUserID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_phoneNumber;
    ::std::string iceP_code;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_clientID, iceP_phoneNumber, iceP_code, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByVerifyCode(::std::move(iceP_clientID), ::std::move(iceP_phoneNumber), ::std::move(iceP_code), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByAuthDev(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByAuthDev(::std::move(iceP_clientID), ::std::move(iceP_userID), ::std::move(iceP_authModeID), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByPasswordNoClient(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_userID, iceP_password, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByPasswordNoClient(::std::move(iceP_userID), ::std::move(iceP_password), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByCardNoClient(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_cardID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_userID, iceP_authModeID, iceP_cardID, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ::std::string iceP_realUserID;
    ErrorInfo iceP_e;
    bool ret = this->verifyByCardNoClient(::std::move(iceP_userID), ::std::move(iceP_authModeID), ::std::move(iceP_cardID), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_realUserID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_realUserID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByAuthDevNoClient(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->readAll(iceP_userID, iceP_authModeID, iceP_appNodeID, iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByAuthDevNoClient(::std::move(iceP_userID), ::std::move(iceP_authModeID), ::std::move(iceP_appNodeID), ::std::move(iceP_powerID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_cancelAuth(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    istr->readAll(iceP_clientID);
    inS.endReadParams();
    this->cancelAuth(::std::move(iceP_clientID), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_sendRandomPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->readAll(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->sendRandomPassword(::std::move(iceP_userID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPUserManager_ops, iceC_ZG6000_ZGSPUserManager_ops + 47, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPUserManager_ops)
    {
        case 0:
        {
            return _iceD_addUser(in, current);
        }
        case 1:
        {
            return _iceD_addUserCard(in, current);
        }
        case 2:
        {
            return _iceD_cancelAuth(in, current);
        }
        case 3:
        {
            return _iceD_changePassword(in, current);
        }
        case 4:
        {
            return _iceD_checkState(in, current);
        }
        case 5:
        {
            return _iceD_deleteUser(in, current);
        }
        case 6:
        {
            return _iceD_deleteUserCard(in, current);
        }
        case 7:
        {
            return _iceD_deleteUserFace(in, current);
        }
        case 8:
        {
            return _iceD_deleteUserFinger(in, current);
        }
        case 9:
        {
            return _iceD_dispatchData(in, current);
        }
        case 10:
        {
            return _iceD_exitApp(in, current);
        }
        case 11:
        {
            return _iceD_getAvaiableUser(in, current);
        }
        case 12:
        {
            return _iceD_getUserFace(in, current);
        }
        case 13:
        {
            return _iceD_getUserFingers(in, current);
        }
        case 14:
        {
            return _iceD_getUserInfo(in, current);
        }
        case 15:
        {
            return _iceD_getVersion(in, current);
        }
        case 16:
        {
            return _iceD_heartDebug(in, current);
        }
        case 17:
        {
            return _iceD_ice_id(in, current);
        }
        case 18:
        {
            return _iceD_ice_ids(in, current);
        }
        case 19:
        {
            return _iceD_ice_isA(in, current);
        }
        case 20:
        {
            return _iceD_ice_ping(in, current);
        }
        case 21:
        {
            return _iceD_isCardBindUser(in, current);
        }
        case 22:
        {
            return _iceD_isDebugging(in, current);
        }
        case 23:
        {
            return _iceD_isUserHasPower(in, current);
        }
        case 24:
        {
            return _iceD_loginByAuthDev(in, current);
        }
        case 25:
        {
            return _iceD_loginByCard(in, current);
        }
        case 26:
        {
            return _iceD_loginByPassword(in, current);
        }
        case 27:
        {
            return _iceD_loginByVerifyCode(in, current);
        }
        case 28:
        {
            return _iceD_logout(in, current);
        }
        case 29:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 30:
        {
            return _iceD_resetPassword(in, current);
        }
        case 31:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 32:
        {
            return _iceD_sendRandomPassword(in, current);
        }
        case 33:
        {
            return _iceD_sendVerifyCode(in, current);
        }
        case 34:
        {
            return _iceD_startDebug(in, current);
        }
        case 35:
        {
            return _iceD_stopDebug(in, current);
        }
        case 36:
        {
            return _iceD_test(in, current);
        }
        case 37:
        {
            return _iceD_updateUser(in, current);
        }
        case 38:
        {
            return _iceD_updateUserFace(in, current);
        }
        case 39:
        {
            return _iceD_updateUserFinger(in, current);
        }
        case 40:
        {
            return _iceD_verifyByAuthDev(in, current);
        }
        case 41:
        {
            return _iceD_verifyByAuthDevNoClient(in, current);
        }
        case 42:
        {
            return _iceD_verifyByCard(in, current);
        }
        case 43:
        {
            return _iceD_verifyByCardNoClient(in, current);
        }
        case 44:
        {
            return _iceD_verifyByPassword(in, current);
        }
        case 45:
        {
            return _iceD_verifyByPasswordNoClient(in, current);
        }
        case 46:
        {
            return _iceD_verifyByVerifyCode(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_getUserInfo(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::GetUserInfoResult>>& outAsync, const ::std::string& iceP_userID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_getUserInfo_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_getUserInfo_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::GetUserInfoResult v;
            istr->readAll(v.user, v.listRole, v.listCard, v.listAuth, v.listAppNode, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_getUserFingers(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::GetUserFingersResult>>& outAsync, const ::std::string& iceP_userID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_getUserFingers_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_getUserFingers_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::GetUserFingersResult v;
            istr->readAll(v.listFinger, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_getUserFace(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::GetUserFaceResult>>& outAsync, const ::std::string& iceP_userID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_getUserFace_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_getUserFace_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::GetUserFaceResult v;
            istr->readAll(v.faceData, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_addUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::AddUserResult>>& outAsync, const StringMap& iceP_user, const ListStringMap& iceP_listRole, const ListStringMap& iceP_listAuth, const ListStringMap& iceP_listAppNode, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_addUser_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_addUser_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_user, iceP_listRole, iceP_listAuth, iceP_listAppNode);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::AddUserResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_updateUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::UpdateUserResult>>& outAsync, const StringMap& iceP_user, const ListStringMap& iceP_listRole, const ListStringMap& iceP_listAuth, const ListStringMap& iceP_listAppNode, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_updateUser_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_updateUser_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_user, iceP_listRole, iceP_listAuth, iceP_listAppNode);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::UpdateUserResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_deleteUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::DeleteUserResult>>& outAsync, const ::std::string& iceP_userID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_deleteUser_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_deleteUser_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::DeleteUserResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_addUserCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::AddUserCardResult>>& outAsync, const ::std::string& iceP_userID, const ::std::string& iceP_cardNo, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_addUserCard_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_addUserCard_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID, iceP_cardNo);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::AddUserCardResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_deleteUserCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::DeleteUserCardResult>>& outAsync, const ::std::string& iceP_cardNo, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_deleteUserCard_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_deleteUserCard_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_cardNo);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::DeleteUserCardResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_updateUserFace(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::UpdateUserFaceResult>>& outAsync, const ::std::string& iceP_userID, const ::std::string& iceP_faceData, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_updateUserFace_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_updateUserFace_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID, iceP_faceData);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::UpdateUserFaceResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_deleteUserFace(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::DeleteUserFaceResult>>& outAsync, const ::std::string& iceP_userID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_deleteUserFace_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_deleteUserFace_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::DeleteUserFaceResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_updateUserFinger(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::UpdateUserFingerResult>>& outAsync, const ::std::string& iceP_userID, int iceP_fingerNo, const ::std::string& iceP_fingerData, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_updateUserFinger_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_updateUserFinger_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID, iceP_fingerNo, iceP_fingerData);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::UpdateUserFingerResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_deleteUserFinger(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::DeleteUserFingerResult>>& outAsync, const ::std::string& iceP_userID, int iceP_fingerNo, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_deleteUserFinger_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_deleteUserFinger_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID, iceP_fingerNo);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::DeleteUserFingerResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_changePassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::ChangePasswordResult>>& outAsync, const ::std::string& iceP_userID, const ::std::string& iceP_oldPassword, const ::std::string& iceP_newPassword, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_changePassword_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_changePassword_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID, iceP_oldPassword, iceP_newPassword);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::ChangePasswordResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_resetPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::ResetPasswordResult>>& outAsync, const ::std::string& iceP_userID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_resetPassword_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_resetPassword_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::ResetPasswordResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_isUserHasPower(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::IsUserHasPowerResult>>& outAsync, const ::std::string& iceP_userID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_isUserHasPower_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_isUserHasPower_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::IsUserHasPowerResult v;
            istr->readAll(v.hasPower, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_isCardBindUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::IsCardBindUserResult>>& outAsync, const ::std::string& iceP_cardID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_isCardBindUser_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_isCardBindUser_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_cardID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::IsCardBindUserResult v;
            istr->readAll(v.isBind, v.userID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_loginByPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::LoginByPasswordResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, int iceP_keepTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_loginByPassword_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_loginByPassword_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_password, iceP_keepTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::LoginByPasswordResult v;
            istr->readAll(v.outClientID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_loginByCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::LoginByCardResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_cardID, int iceP_keepTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_loginByCard_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_loginByCard_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_keepTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::LoginByCardResult v;
            istr->readAll(v.outClientID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_loginByVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::LoginByVerifyCodeResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_phoneNumber, const ::std::string& iceP_code, int iceP_keepTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_loginByVerifyCode_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_loginByVerifyCode_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_phoneNumber, iceP_code, iceP_keepTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::LoginByVerifyCodeResult v;
            istr->readAll(v.outClientID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_loginByAuthDev(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::LoginByAuthDevResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, int iceP_keepTime, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_loginByAuthDev_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_loginByAuthDev_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_keepTime);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::LoginByAuthDevResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_logout(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::LogoutResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_logout_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_logout_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::LogoutResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_sendVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::SendVerifyCodeResult>>& outAsync, const ::std::string& iceP_phoneNumber, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_sendVerifyCode_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_sendVerifyCode_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_phoneNumber);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::SendVerifyCodeResult v;
            istr->readAll(v.seqNo, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_getAvaiableUser(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::GetAvaiableUserResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_getAvaiableUser_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_getAvaiableUser_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::GetAvaiableUserResult v;
            istr->readAll(v.lstUser, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_verifyByPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByPasswordResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByPassword_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_verifyByPassword_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_password, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::VerifyByPasswordResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_verifyByCard(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByCardResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_cardID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByCard_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_verifyByCard_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::VerifyByCardResult v;
            istr->readAll(v.realUserID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_verifyByVerifyCode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByVerifyCodeResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_phoneNumber, const ::std::string& iceP_code, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByVerifyCode_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_verifyByVerifyCode_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_phoneNumber, iceP_code, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::VerifyByVerifyCodeResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_verifyByAuthDev(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByAuthDevResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByAuthDev_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_verifyByAuthDev_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_userID, iceP_authModeID, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::VerifyByAuthDevResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_verifyByPasswordNoClient(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByPasswordNoClientResult>>& outAsync, const ::std::string& iceP_userID, const ::std::string& iceP_password, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByPasswordNoClient_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_verifyByPasswordNoClient_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID, iceP_password, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::VerifyByPasswordNoClientResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_verifyByCardNoClient(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByCardNoClientResult>>& outAsync, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_cardID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByCardNoClient_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_verifyByCardNoClient_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID, iceP_authModeID, iceP_cardID, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::VerifyByCardNoClientResult v;
            istr->readAll(v.realUserID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_verifyByAuthDevNoClient(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::VerifyByAuthDevNoClientResult>>& outAsync, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByAuthDevNoClient_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_verifyByAuthDevNoClient_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID, iceP_authModeID, iceP_appNodeID, iceP_powerID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::VerifyByAuthDevNoClientResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_cancelAuth(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const ::std::string& iceP_clientID, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_cancelAuth_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGSPUserManagerPrx::_iceI_sendRandomPassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSPUserManager::SendRandomPasswordResult>>& outAsync, const ::std::string& iceP_userID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_sendRandomPassword_name);
    outAsync->invoke(iceC_ZG6000_ZGSPUserManager_sendRandomPassword_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_userID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGSPUserManager::SendRandomPasswordResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGSPUserManagerPrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGSPUserManagerPrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGSPUserManagerPrx::ice_staticId()
{
    return ZGSPUserManager::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGSPUserManager_getUserInfo_name = "getUserInfo";

const ::std::string iceC_ZG6000_ZGSPUserManager_getUserFingers_name = "getUserFingers";

const ::std::string iceC_ZG6000_ZGSPUserManager_getUserFace_name = "getUserFace";

const ::std::string iceC_ZG6000_ZGSPUserManager_addUser_name = "addUser";

const ::std::string iceC_ZG6000_ZGSPUserManager_updateUser_name = "updateUser";

const ::std::string iceC_ZG6000_ZGSPUserManager_deleteUser_name = "deleteUser";

const ::std::string iceC_ZG6000_ZGSPUserManager_addUserCard_name = "addUserCard";

const ::std::string iceC_ZG6000_ZGSPUserManager_deleteUserCard_name = "deleteUserCard";

const ::std::string iceC_ZG6000_ZGSPUserManager_updateUserFace_name = "updateUserFace";

const ::std::string iceC_ZG6000_ZGSPUserManager_deleteUserFace_name = "deleteUserFace";

const ::std::string iceC_ZG6000_ZGSPUserManager_updateUserFinger_name = "updateUserFinger";

const ::std::string iceC_ZG6000_ZGSPUserManager_deleteUserFinger_name = "deleteUserFinger";

const ::std::string iceC_ZG6000_ZGSPUserManager_changePassword_name = "changePassword";

const ::std::string iceC_ZG6000_ZGSPUserManager_resetPassword_name = "resetPassword";

const ::std::string iceC_ZG6000_ZGSPUserManager_isUserHasPower_name = "isUserHasPower";

const ::std::string iceC_ZG6000_ZGSPUserManager_isCardBindUser_name = "isCardBindUser";

const ::std::string iceC_ZG6000_ZGSPUserManager_loginByPassword_name = "loginByPassword";

const ::std::string iceC_ZG6000_ZGSPUserManager_loginByCard_name = "loginByCard";

const ::std::string iceC_ZG6000_ZGSPUserManager_loginByVerifyCode_name = "loginByVerifyCode";

const ::std::string iceC_ZG6000_ZGSPUserManager_loginByAuthDev_name = "loginByAuthDev";

const ::std::string iceC_ZG6000_ZGSPUserManager_logout_name = "logout";

const ::std::string iceC_ZG6000_ZGSPUserManager_sendVerifyCode_name = "sendVerifyCode";

const ::std::string iceC_ZG6000_ZGSPUserManager_getAvaiableUser_name = "getAvaiableUser";

const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByPassword_name = "verifyByPassword";

const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByCard_name = "verifyByCard";

const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByVerifyCode_name = "verifyByVerifyCode";

const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByAuthDev_name = "verifyByAuthDev";

const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByPasswordNoClient_name = "verifyByPasswordNoClient";

const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByCardNoClient_name = "verifyByCardNoClient";

const ::std::string iceC_ZG6000_ZGSPUserManager_verifyByAuthDevNoClient_name = "verifyByAuthDevNoClient";

const ::std::string iceC_ZG6000_ZGSPUserManager_cancelAuth_name = "cancelAuth";

const ::std::string iceC_ZG6000_ZGSPUserManager_sendRandomPassword_name = "sendRandomPassword";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGSPUserManager* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGSPUserManager>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGSPUserManager;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_getUserInfo(const ::std::string& iceP_userID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_getUserInfo_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_getUserInfo_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_getUserInfo_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_getUserInfo_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_getUserInfo(::ZG6000::StringMap& iceP_user, ::ZG6000::ListStringMap& iceP_listRole, ::ZG6000::ListStringMap& iceP_listCard, ::ZG6000::ListStringMap& iceP_listAuth, ::ZG6000::ListStringMap& iceP_listAppNode, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_getUserInfo_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_user);
    istr->read(iceP_listRole);
    istr->read(iceP_listCard);
    istr->read(iceP_listAuth);
    istr->read(iceP_listAppNode);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_getUserInfo(::ZG6000::StringMap& iceP_user, ::ZG6000::ListStringMap& iceP_listRole, ::ZG6000::ListStringMap& iceP_listCard, ::ZG6000::ListStringMap& iceP_listAuth, ::ZG6000::ListStringMap& iceP_listAppNode, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_getUserInfo_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_user);
    istr->read(iceP_listRole);
    istr->read(iceP_listCard);
    istr->read(iceP_listAuth);
    istr->read(iceP_listAppNode);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_getUserFingers(const ::std::string& iceP_userID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_getUserFingers_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_getUserFingers_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_getUserFingers_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_getUserFingers_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_getUserFingers(::ZG6000::ListStringMap& iceP_listFinger, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_getUserFingers_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFinger);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_getUserFingers(::ZG6000::ListStringMap& iceP_listFinger, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_getUserFingers_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listFinger);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_getUserFace(const ::std::string& iceP_userID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_getUserFace_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_getUserFace_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_getUserFace_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_getUserFace_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_getUserFace(::std::string& iceP_faceData, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_getUserFace_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_faceData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_getUserFace(::std::string& iceP_faceData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_getUserFace_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_faceData);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_addUser(const ::ZG6000::StringMap& iceP_user, const ::ZG6000::ListStringMap& iceP_listRole, const ::ZG6000::ListStringMap& iceP_listAuth, const ::ZG6000::ListStringMap& iceP_listAppNode, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_addUser_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_addUser_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_addUser_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_user);
        ostr->write(iceP_listRole);
        ostr->write(iceP_listAuth);
        ostr->write(iceP_listAppNode);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_addUser_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_addUser(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_addUser_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_addUser(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_addUser_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_updateUser(const ::ZG6000::StringMap& iceP_user, const ::ZG6000::ListStringMap& iceP_listRole, const ::ZG6000::ListStringMap& iceP_listAuth, const ::ZG6000::ListStringMap& iceP_listAppNode, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_updateUser_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_updateUser_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_updateUser_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_user);
        ostr->write(iceP_listRole);
        ostr->write(iceP_listAuth);
        ostr->write(iceP_listAppNode);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_updateUser_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_updateUser(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_updateUser_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_updateUser(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_updateUser_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_deleteUser(const ::std::string& iceP_userID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_deleteUser_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_deleteUser_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_deleteUser_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_deleteUser_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_deleteUser(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_deleteUser_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_deleteUser(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_deleteUser_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_addUserCard(const ::std::string& iceP_userID, const ::std::string& iceP_cardNo, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_addUserCard_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_addUserCard_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_addUserCard_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        ostr->write(iceP_cardNo);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_addUserCard_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_addUserCard(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_addUserCard_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_addUserCard(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_addUserCard_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_deleteUserCard(const ::std::string& iceP_cardNo, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_deleteUserCard_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_deleteUserCard_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_deleteUserCard_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_cardNo);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_deleteUserCard_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_deleteUserCard(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_deleteUserCard_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_deleteUserCard(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_deleteUserCard_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_updateUserFace(const ::std::string& iceP_userID, const ::std::string& iceP_faceData, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_updateUserFace_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_updateUserFace_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_updateUserFace_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        ostr->write(iceP_faceData);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_updateUserFace_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_updateUserFace(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_updateUserFace_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_updateUserFace(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_updateUserFace_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_deleteUserFace(const ::std::string& iceP_userID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_deleteUserFace_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_deleteUserFace_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_deleteUserFace_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_deleteUserFace_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_deleteUserFace(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_deleteUserFace_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_deleteUserFace(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_deleteUserFace_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_updateUserFinger(const ::std::string& iceP_userID, ::Ice::Int iceP_fingerNo, const ::std::string& iceP_fingerData, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_updateUserFinger_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_updateUserFinger_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_updateUserFinger_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        ostr->write(iceP_fingerNo);
        ostr->write(iceP_fingerData);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_updateUserFinger_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_updateUserFinger(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_updateUserFinger_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_updateUserFinger(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_updateUserFinger_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_deleteUserFinger(const ::std::string& iceP_userID, ::Ice::Int iceP_fingerNo, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_deleteUserFinger_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_deleteUserFinger_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_deleteUserFinger_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        ostr->write(iceP_fingerNo);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_deleteUserFinger_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_deleteUserFinger(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_deleteUserFinger_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_deleteUserFinger(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_deleteUserFinger_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_changePassword(const ::std::string& iceP_userID, const ::std::string& iceP_oldPassword, const ::std::string& iceP_newPassword, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_changePassword_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_changePassword_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_changePassword_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        ostr->write(iceP_oldPassword);
        ostr->write(iceP_newPassword);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_changePassword_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_changePassword(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_changePassword_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_changePassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_changePassword_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_resetPassword(const ::std::string& iceP_userID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_resetPassword_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_resetPassword_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_resetPassword_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_resetPassword_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_resetPassword(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_resetPassword_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_resetPassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_resetPassword_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_isUserHasPower(const ::std::string& iceP_userID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_isUserHasPower_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_isUserHasPower_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_isUserHasPower_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_isUserHasPower_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_isUserHasPower(bool& iceP_hasPower, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_isUserHasPower_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_hasPower);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_isUserHasPower(bool& iceP_hasPower, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_isUserHasPower_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_hasPower);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_isCardBindUser(const ::std::string& iceP_cardID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_isCardBindUser_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_isCardBindUser_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_isCardBindUser_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_cardID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_isCardBindUser_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_isCardBindUser(bool& iceP_isBind, ::std::string& iceP_userID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_isCardBindUser_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_isBind);
    istr->read(iceP_userID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_isCardBindUser(bool& iceP_isBind, ::std::string& iceP_userID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_isCardBindUser_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_isBind);
    istr->read(iceP_userID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_loginByPassword(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, ::Ice::Int iceP_keepTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_loginByPassword_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_loginByPassword_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_loginByPassword_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_password);
        ostr->write(iceP_keepTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_loginByPassword_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_loginByPassword(::std::string& iceP_outClientID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_loginByPassword_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_outClientID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_loginByPassword(::std::string& iceP_outClientID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_loginByPassword_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_outClientID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_loginByCard(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_cardID, ::Ice::Int iceP_keepTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_loginByCard_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_loginByCard_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_loginByCard_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_authModeID);
        ostr->write(iceP_cardID);
        ostr->write(iceP_keepTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_loginByCard_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_loginByCard(::std::string& iceP_outClientID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_loginByCard_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_outClientID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_loginByCard(::std::string& iceP_outClientID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_loginByCard_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_outClientID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_loginByVerifyCode(const ::std::string& iceP_clientID, const ::std::string& iceP_phoneNumber, const ::std::string& iceP_code, ::Ice::Int iceP_keepTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_loginByVerifyCode_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_loginByVerifyCode_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_loginByVerifyCode_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_phoneNumber);
        ostr->write(iceP_code);
        ostr->write(iceP_keepTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_loginByVerifyCode_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_loginByVerifyCode(::std::string& iceP_outClientID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_loginByVerifyCode_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_outClientID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_loginByVerifyCode(::std::string& iceP_outClientID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_loginByVerifyCode_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_outClientID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_loginByAuthDev(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, ::Ice::Int iceP_keepTime, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_loginByAuthDev_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_loginByAuthDev_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_loginByAuthDev_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_authModeID);
        ostr->write(iceP_keepTime);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_loginByAuthDev_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_loginByAuthDev(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_loginByAuthDev_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_loginByAuthDev(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_loginByAuthDev_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_logout(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_logout_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_logout_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_logout_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_logout_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_logout(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_logout_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_logout(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_logout_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_sendVerifyCode(const ::std::string& iceP_phoneNumber, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_sendVerifyCode_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_sendVerifyCode_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_sendVerifyCode_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_phoneNumber);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_sendVerifyCode_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_sendVerifyCode(::std::string& iceP_seqNo, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_sendVerifyCode_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_seqNo);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_sendVerifyCode(::std::string& iceP_seqNo, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_sendVerifyCode_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_seqNo);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_getAvaiableUser(const ::std::string& iceP_clientID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_getAvaiableUser_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_getAvaiableUser_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_getAvaiableUser_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_getAvaiableUser_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_getAvaiableUser(::ZG6000::ListStringMap& iceP_lstUser, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_getAvaiableUser_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_lstUser);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_getAvaiableUser(::ZG6000::ListStringMap& iceP_lstUser, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_getAvaiableUser_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_lstUser);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_verifyByPassword(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_password, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByPassword_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_verifyByPassword_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_verifyByPassword_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_password);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_verifyByPassword_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_verifyByPassword(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByPassword_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_verifyByPassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByPassword_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_verifyByCard(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_cardID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByCard_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_verifyByCard_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_verifyByCard_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_authModeID);
        ostr->write(iceP_cardID);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_verifyByCard_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_verifyByCard(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByCard_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_realUserID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_verifyByCard(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByCard_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_realUserID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_verifyByVerifyCode(const ::std::string& iceP_clientID, const ::std::string& iceP_phoneNumber, const ::std::string& iceP_code, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByVerifyCode_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_verifyByVerifyCode_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_verifyByVerifyCode_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_phoneNumber);
        ostr->write(iceP_code);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_verifyByVerifyCode_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_verifyByVerifyCode(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByVerifyCode_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_verifyByVerifyCode(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByVerifyCode_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_verifyByAuthDev(const ::std::string& iceP_clientID, const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByAuthDev_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_verifyByAuthDev_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_verifyByAuthDev_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_userID);
        ostr->write(iceP_authModeID);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_verifyByAuthDev_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_verifyByAuthDev(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByAuthDev_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_verifyByAuthDev(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByAuthDev_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_verifyByPasswordNoClient(const ::std::string& iceP_userID, const ::std::string& iceP_password, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByPasswordNoClient_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_verifyByPasswordNoClient_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_verifyByPasswordNoClient_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        ostr->write(iceP_password);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_verifyByPasswordNoClient_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_verifyByPasswordNoClient(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByPasswordNoClient_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_verifyByPasswordNoClient(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByPasswordNoClient_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_verifyByCardNoClient(const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_cardID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByCardNoClient_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_verifyByCardNoClient_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_verifyByCardNoClient_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        ostr->write(iceP_authModeID);
        ostr->write(iceP_cardID);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_verifyByCardNoClient_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_verifyByCardNoClient(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByCardNoClient_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_realUserID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_verifyByCardNoClient(::std::string& iceP_realUserID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByCardNoClient_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_realUserID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_verifyByAuthDevNoClient(const ::std::string& iceP_userID, const ::std::string& iceP_authModeID, const ::std::string& iceP_appNodeID, const ::std::string& iceP_powerID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_verifyByAuthDevNoClient_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_verifyByAuthDevNoClient_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_verifyByAuthDevNoClient_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        ostr->write(iceP_authModeID);
        ostr->write(iceP_appNodeID);
        ostr->write(iceP_powerID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_verifyByAuthDevNoClient_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_verifyByAuthDevNoClient(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByAuthDevNoClient_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_verifyByAuthDevNoClient(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_verifyByAuthDevNoClient_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_cancelAuth(const ::std::string& iceP_clientID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_cancelAuth_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_cancelAuth_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_cancelAuth_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::ZG6000::ZGSPUserManager::end_cancelAuth(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_ZG6000_ZGSPUserManager_cancelAuth_name);
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGSPUserManager::_iceI_begin_sendRandomPassword(const ::std::string& iceP_userID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGSPUserManager_sendRandomPassword_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGSPUserManager_sendRandomPassword_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGSPUserManager_sendRandomPassword_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_userID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGSPUserManager_sendRandomPassword_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGSPUserManager::end_sendRandomPassword(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_sendRandomPassword_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGSPUserManager::_iceI_end_sendRandomPassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGSPUserManager_sendRandomPassword_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGSPUserManager::_newInstance() const
{
    return new ZGSPUserManager;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGSPUserManager::ice_staticId()
{
    return ::ZG6000::ZGSPUserManager::ice_staticId();
}

ZG6000::ZGSPUserManager::~ZGSPUserManager()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGSPUserManager* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPUserManager_ids[3] =
{
    "::Ice::Object",
    "::ZG6000::ZGSPUserManager",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGSPUserManager::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGSPUserManager_ids, iceC_ZG6000_ZGSPUserManager_ids + 3, s);
}

::std::vector< ::std::string>
ZG6000::ZGSPUserManager::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGSPUserManager_ids[0], &iceC_ZG6000_ZGSPUserManager_ids[3]);
}

const ::std::string&
ZG6000::ZGSPUserManager::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGSPUserManager::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGSPUserManager";
    return typeId;
#else
    return iceC_ZG6000_ZGSPUserManager_ids[1];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_getUserInfo(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->read(iceP_userID);
    inS.endReadParams();
    StringMap iceP_user;
    ListStringMap iceP_listRole;
    ListStringMap iceP_listCard;
    ListStringMap iceP_listAuth;
    ListStringMap iceP_listAppNode;
    ErrorInfo iceP_e;
    bool ret = this->getUserInfo(iceP_userID, iceP_user, iceP_listRole, iceP_listCard, iceP_listAuth, iceP_listAppNode, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_user);
    ostr->write(iceP_listRole);
    ostr->write(iceP_listCard);
    ostr->write(iceP_listAuth);
    ostr->write(iceP_listAppNode);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_getUserFingers(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->read(iceP_userID);
    inS.endReadParams();
    ListStringMap iceP_listFinger;
    ErrorInfo iceP_e;
    bool ret = this->getUserFingers(iceP_userID, iceP_listFinger, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listFinger);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_getUserFace(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->read(iceP_userID);
    inS.endReadParams();
    ::std::string iceP_faceData;
    ErrorInfo iceP_e;
    bool ret = this->getUserFace(iceP_userID, iceP_faceData, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_faceData);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_addUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_user;
    ListStringMap iceP_listRole;
    ListStringMap iceP_listAuth;
    ListStringMap iceP_listAppNode;
    istr->read(iceP_user);
    istr->read(iceP_listRole);
    istr->read(iceP_listAuth);
    istr->read(iceP_listAppNode);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addUser(iceP_user, iceP_listRole, iceP_listAuth, iceP_listAppNode, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_updateUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_user;
    ListStringMap iceP_listRole;
    ListStringMap iceP_listAuth;
    ListStringMap iceP_listAppNode;
    istr->read(iceP_user);
    istr->read(iceP_listRole);
    istr->read(iceP_listAuth);
    istr->read(iceP_listAppNode);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateUser(iceP_user, iceP_listRole, iceP_listAuth, iceP_listAppNode, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_deleteUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->read(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteUser(iceP_userID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_addUserCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_cardNo;
    istr->read(iceP_userID);
    istr->read(iceP_cardNo);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->addUserCard(iceP_userID, iceP_cardNo, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_deleteUserCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_cardNo;
    istr->read(iceP_cardNo);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteUserCard(iceP_cardNo, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_updateUserFace(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_faceData;
    istr->read(iceP_userID);
    istr->read(iceP_faceData);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateUserFace(iceP_userID, iceP_faceData, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_deleteUserFace(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->read(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteUserFace(iceP_userID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_updateUserFinger(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::Ice::Int iceP_fingerNo;
    ::std::string iceP_fingerData;
    istr->read(iceP_userID);
    istr->read(iceP_fingerNo);
    istr->read(iceP_fingerData);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->updateUserFinger(iceP_userID, iceP_fingerNo, iceP_fingerData, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_deleteUserFinger(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::Ice::Int iceP_fingerNo;
    istr->read(iceP_userID);
    istr->read(iceP_fingerNo);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteUserFinger(iceP_userID, iceP_fingerNo, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_changePassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_oldPassword;
    ::std::string iceP_newPassword;
    istr->read(iceP_userID);
    istr->read(iceP_oldPassword);
    istr->read(iceP_newPassword);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->changePassword(iceP_userID, iceP_oldPassword, iceP_newPassword, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_resetPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->read(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->resetPassword(iceP_userID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_isUserHasPower(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_powerID;
    istr->read(iceP_userID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    bool iceP_hasPower;
    ErrorInfo iceP_e;
    bool ret = this->isUserHasPower(iceP_userID, iceP_powerID, iceP_hasPower, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_hasPower);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_isCardBindUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_cardID;
    istr->read(iceP_cardID);
    inS.endReadParams();
    bool iceP_isBind;
    ::std::string iceP_userID;
    ErrorInfo iceP_e;
    bool ret = this->isCardBindUser(iceP_cardID, iceP_isBind, iceP_userID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_isBind);
    ostr->write(iceP_userID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_loginByPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::Ice::Int iceP_keepTime;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_password);
    istr->read(iceP_keepTime);
    inS.endReadParams();
    ::std::string iceP_outClientID;
    ErrorInfo iceP_e;
    bool ret = this->loginByPassword(iceP_clientID, iceP_userID, iceP_password, iceP_keepTime, iceP_outClientID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_outClientID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_loginByCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_cardID;
    ::Ice::Int iceP_keepTime;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_authModeID);
    istr->read(iceP_cardID);
    istr->read(iceP_keepTime);
    inS.endReadParams();
    ::std::string iceP_outClientID;
    ErrorInfo iceP_e;
    bool ret = this->loginByCard(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_keepTime, iceP_outClientID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_outClientID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_loginByVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_phoneNumber;
    ::std::string iceP_code;
    ::Ice::Int iceP_keepTime;
    istr->read(iceP_clientID);
    istr->read(iceP_phoneNumber);
    istr->read(iceP_code);
    istr->read(iceP_keepTime);
    inS.endReadParams();
    ::std::string iceP_outClientID;
    ErrorInfo iceP_e;
    bool ret = this->loginByVerifyCode(iceP_clientID, iceP_phoneNumber, iceP_code, iceP_keepTime, iceP_outClientID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_outClientID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_loginByAuthDev(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::Ice::Int iceP_keepTime;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_authModeID);
    istr->read(iceP_keepTime);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->loginByAuthDev(iceP_clientID, iceP_userID, iceP_authModeID, iceP_keepTime, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_logout(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->logout(iceP_clientID, iceP_userID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_sendVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_phoneNumber;
    istr->read(iceP_phoneNumber);
    inS.endReadParams();
    ::std::string iceP_seqNo;
    ErrorInfo iceP_e;
    bool ret = this->sendVerifyCode(iceP_phoneNumber, iceP_seqNo, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_seqNo);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_getAvaiableUser(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_clientID);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ListStringMap iceP_lstUser;
    ErrorInfo iceP_e;
    bool ret = this->getAvaiableUser(iceP_clientID, iceP_appNodeID, iceP_powerID, iceP_lstUser, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_lstUser);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_password);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByPassword(iceP_clientID, iceP_userID, iceP_password, iceP_appNodeID, iceP_powerID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByCard(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_cardID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_authModeID);
    istr->read(iceP_cardID);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ::std::string iceP_realUserID;
    ErrorInfo iceP_e;
    bool ret = this->verifyByCard(iceP_clientID, iceP_userID, iceP_authModeID, iceP_cardID, iceP_appNodeID, iceP_powerID, iceP_realUserID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_realUserID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByVerifyCode(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_phoneNumber;
    ::std::string iceP_code;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_clientID);
    istr->read(iceP_phoneNumber);
    istr->read(iceP_code);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByVerifyCode(iceP_clientID, iceP_phoneNumber, iceP_code, iceP_appNodeID, iceP_powerID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByAuthDev(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_clientID);
    istr->read(iceP_userID);
    istr->read(iceP_authModeID);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByAuthDev(iceP_clientID, iceP_userID, iceP_authModeID, iceP_appNodeID, iceP_powerID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByPasswordNoClient(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_password;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_userID);
    istr->read(iceP_password);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByPasswordNoClient(iceP_userID, iceP_password, iceP_appNodeID, iceP_powerID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByCardNoClient(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_cardID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_userID);
    istr->read(iceP_authModeID);
    istr->read(iceP_cardID);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ::std::string iceP_realUserID;
    ErrorInfo iceP_e;
    bool ret = this->verifyByCardNoClient(iceP_userID, iceP_authModeID, iceP_cardID, iceP_appNodeID, iceP_powerID, iceP_realUserID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_realUserID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_verifyByAuthDevNoClient(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    ::std::string iceP_authModeID;
    ::std::string iceP_appNodeID;
    ::std::string iceP_powerID;
    istr->read(iceP_userID);
    istr->read(iceP_authModeID);
    istr->read(iceP_appNodeID);
    istr->read(iceP_powerID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->verifyByAuthDevNoClient(iceP_userID, iceP_authModeID, iceP_appNodeID, iceP_powerID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_cancelAuth(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    istr->read(iceP_clientID);
    inS.endReadParams();
    this->cancelAuth(iceP_clientID, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceD_sendRandomPassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_userID;
    istr->read(iceP_userID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->sendRandomPassword(iceP_userID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGSPUserManager_all[] =
{
    "addUser",
    "addUserCard",
    "cancelAuth",
    "changePassword",
    "checkState",
    "deleteUser",
    "deleteUserCard",
    "deleteUserFace",
    "deleteUserFinger",
    "dispatchData",
    "exitApp",
    "getAvaiableUser",
    "getUserFace",
    "getUserFingers",
    "getUserInfo",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isCardBindUser",
    "isDebugging",
    "isUserHasPower",
    "loginByAuthDev",
    "loginByCard",
    "loginByPassword",
    "loginByVerifyCode",
    "logout",
    "pauseDebug",
    "resetPassword",
    "resumeDebug",
    "sendRandomPassword",
    "sendVerifyCode",
    "startDebug",
    "stopDebug",
    "test",
    "updateUser",
    "updateUserFace",
    "updateUserFinger",
    "verifyByAuthDev",
    "verifyByAuthDevNoClient",
    "verifyByCard",
    "verifyByCardNoClient",
    "verifyByPassword",
    "verifyByPasswordNoClient",
    "verifyByVerifyCode"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGSPUserManager::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGSPUserManager_all, iceC_ZG6000_ZGSPUserManager_all + 47, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGSPUserManager_all)
    {
        case 0:
        {
            return _iceD_addUser(in, current);
        }
        case 1:
        {
            return _iceD_addUserCard(in, current);
        }
        case 2:
        {
            return _iceD_cancelAuth(in, current);
        }
        case 3:
        {
            return _iceD_changePassword(in, current);
        }
        case 4:
        {
            return _iceD_checkState(in, current);
        }
        case 5:
        {
            return _iceD_deleteUser(in, current);
        }
        case 6:
        {
            return _iceD_deleteUserCard(in, current);
        }
        case 7:
        {
            return _iceD_deleteUserFace(in, current);
        }
        case 8:
        {
            return _iceD_deleteUserFinger(in, current);
        }
        case 9:
        {
            return _iceD_dispatchData(in, current);
        }
        case 10:
        {
            return _iceD_exitApp(in, current);
        }
        case 11:
        {
            return _iceD_getAvaiableUser(in, current);
        }
        case 12:
        {
            return _iceD_getUserFace(in, current);
        }
        case 13:
        {
            return _iceD_getUserFingers(in, current);
        }
        case 14:
        {
            return _iceD_getUserInfo(in, current);
        }
        case 15:
        {
            return _iceD_getVersion(in, current);
        }
        case 16:
        {
            return _iceD_heartDebug(in, current);
        }
        case 17:
        {
            return _iceD_ice_id(in, current);
        }
        case 18:
        {
            return _iceD_ice_ids(in, current);
        }
        case 19:
        {
            return _iceD_ice_isA(in, current);
        }
        case 20:
        {
            return _iceD_ice_ping(in, current);
        }
        case 21:
        {
            return _iceD_isCardBindUser(in, current);
        }
        case 22:
        {
            return _iceD_isDebugging(in, current);
        }
        case 23:
        {
            return _iceD_isUserHasPower(in, current);
        }
        case 24:
        {
            return _iceD_loginByAuthDev(in, current);
        }
        case 25:
        {
            return _iceD_loginByCard(in, current);
        }
        case 26:
        {
            return _iceD_loginByPassword(in, current);
        }
        case 27:
        {
            return _iceD_loginByVerifyCode(in, current);
        }
        case 28:
        {
            return _iceD_logout(in, current);
        }
        case 29:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 30:
        {
            return _iceD_resetPassword(in, current);
        }
        case 31:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 32:
        {
            return _iceD_sendRandomPassword(in, current);
        }
        case 33:
        {
            return _iceD_sendVerifyCode(in, current);
        }
        case 34:
        {
            return _iceD_startDebug(in, current);
        }
        case 35:
        {
            return _iceD_stopDebug(in, current);
        }
        case 36:
        {
            return _iceD_test(in, current);
        }
        case 37:
        {
            return _iceD_updateUser(in, current);
        }
        case 38:
        {
            return _iceD_updateUserFace(in, current);
        }
        case 39:
        {
            return _iceD_updateUserFinger(in, current);
        }
        case 40:
        {
            return _iceD_verifyByAuthDev(in, current);
        }
        case 41:
        {
            return _iceD_verifyByAuthDevNoClient(in, current);
        }
        case 42:
        {
            return _iceD_verifyByCard(in, current);
        }
        case 43:
        {
            return _iceD_verifyByCardNoClient(in, current);
        }
        case 44:
        {
            return _iceD_verifyByPassword(in, current);
        }
        case 45:
        {
            return _iceD_verifyByPasswordNoClient(in, current);
        }
        case 46:
        {
            return _iceD_verifyByVerifyCode(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGSPUserManager::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGSPUserManager, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGSPUserManager::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGSPUserManager, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGSPUserManagerPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGSPUserManagerPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGSPUserManager::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

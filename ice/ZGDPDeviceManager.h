//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGDPDeviceManager.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGDPDeviceManager_h__
#define __ZGDPDeviceManager_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGDPDeviceManager;
class ZGDPDeviceManagerPrx;

}

namespace ZG6000
{

class ZGDPDeviceManager : public virtual ZGServerBase
{
public:

    using ProxyType = ZGDPDeviceManagerPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getDevices.
     */
    struct GetDevicesResult
    {
        bool returnValue;
        ListStringMap devices;
        ErrorInfo e;
    };

    virtual bool getDevices(::std::string condition, int offset, int limit, ::std::string orderField, ::std::string orderType, ListStringMap& devices, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDevices(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to addDevice.
     */
    struct AddDeviceResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool addDevice(::std::string deviceID, StringMap properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_addDevice(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateDevice.
     */
    struct UpdateDeviceResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateDevice(::std::string deviceID, StringMap properties, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateDevice(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to removeDevice.
     */
    struct RemoveDeviceResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool removeDevice(::std::string deviceID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_removeDevice(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to changeDeviceState.
     */
    struct ChangeDeviceStateResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool changeDeviceState(::std::string deviceID, StringMap params, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_changeDeviceState(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to changePart.
     */
    struct ChangePartResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool changePart(::std::string parentPartID, StringMap partParam, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_changePart(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDeviceHistory.
     */
    struct GetDeviceHistoryResult
    {
        bool returnValue;
        ListStringMap listDeviceHistory;
        ErrorInfo e;
    };

    virtual bool getDeviceHistory(::std::string deviceID, ListStringMap& listDeviceHistory, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceHistory(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDevicePartHistory.
     */
    struct GetDevicePartHistoryResult
    {
        bool returnValue;
        ListStringMap listPartHistory;
        ErrorInfo e;
    };

    virtual bool getDevicePartHistory(::std::string deviceID, ListStringMap& listPartHistory, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDevicePartHistory(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to addDeviceResume.
     */
    struct AddDeviceResumeResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool addDeviceResume(::std::string deviceID, StringMap deviceResume, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_addDeviceResume(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDeviceResume.
     */
    struct GetDeviceResumeResult
    {
        bool returnValue;
        ListStringMap listDeviceResume;
        ErrorInfo e;
    };

    virtual bool getDeviceResume(::std::string deviceID, ListStringMap& listDeviceResume, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceResume(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGDPDeviceManagerPrx : public virtual ::Ice::Proxy<ZGDPDeviceManagerPrx, ZGServerBasePrx>
{
public:

    bool getDevices(const ::std::string& condition, int offset, int limit, const ::std::string& orderField, const ::std::string& orderType, ListStringMap& devices, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceManager::GetDevicesResult>(true, this, &ZGDPDeviceManagerPrx::_iceI_getDevices, condition, offset, limit, orderField, orderType, context).get();
        devices = ::std::move(_result.devices);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDevicesAsync(const ::std::string& condition, int offset, int limit, const ::std::string& orderField, const ::std::string& orderType, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceManager::GetDevicesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceManager::GetDevicesResult, P>(false, this, &ZGDPDeviceManagerPrx::_iceI_getDevices, condition, offset, limit, orderField, orderType, context);
    }

    ::std::function<void()>
    getDevicesAsync(const ::std::string& condition, int offset, int limit, const ::std::string& orderField, const ::std::string& orderType,
                    ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceManager::GetDevicesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.devices), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceManager::GetDevicesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDeviceManagerPrx::_iceI_getDevices, condition, offset, limit, orderField, orderType, context);
    }

    /// \cond INTERNAL
    void _iceI_getDevices(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::GetDevicesResult>>&, const ::std::string&, int, int, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool addDevice(const ::std::string& deviceID, const StringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceManager::AddDeviceResult>(true, this, &ZGDPDeviceManagerPrx::_iceI_addDevice, deviceID, properties, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto addDeviceAsync(const ::std::string& deviceID, const StringMap& properties, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceManager::AddDeviceResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceManager::AddDeviceResult, P>(false, this, &ZGDPDeviceManagerPrx::_iceI_addDevice, deviceID, properties, context);
    }

    ::std::function<void()>
    addDeviceAsync(const ::std::string& deviceID, const StringMap& properties,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceManager::AddDeviceResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceManager::AddDeviceResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDeviceManagerPrx::_iceI_addDevice, deviceID, properties, context);
    }

    /// \cond INTERNAL
    void _iceI_addDevice(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::AddDeviceResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool updateDevice(const ::std::string& deviceID, const StringMap& properties, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceManager::UpdateDeviceResult>(true, this, &ZGDPDeviceManagerPrx::_iceI_updateDevice, deviceID, properties, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateDeviceAsync(const ::std::string& deviceID, const StringMap& properties, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceManager::UpdateDeviceResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceManager::UpdateDeviceResult, P>(false, this, &ZGDPDeviceManagerPrx::_iceI_updateDevice, deviceID, properties, context);
    }

    ::std::function<void()>
    updateDeviceAsync(const ::std::string& deviceID, const StringMap& properties,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceManager::UpdateDeviceResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceManager::UpdateDeviceResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDeviceManagerPrx::_iceI_updateDevice, deviceID, properties, context);
    }

    /// \cond INTERNAL
    void _iceI_updateDevice(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::UpdateDeviceResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool removeDevice(const ::std::string& deviceID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceManager::RemoveDeviceResult>(true, this, &ZGDPDeviceManagerPrx::_iceI_removeDevice, deviceID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto removeDeviceAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceManager::RemoveDeviceResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceManager::RemoveDeviceResult, P>(false, this, &ZGDPDeviceManagerPrx::_iceI_removeDevice, deviceID, context);
    }

    ::std::function<void()>
    removeDeviceAsync(const ::std::string& deviceID,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceManager::RemoveDeviceResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceManager::RemoveDeviceResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDeviceManagerPrx::_iceI_removeDevice, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_removeDevice(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::RemoveDeviceResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool changeDeviceState(const ::std::string& deviceID, const StringMap& params, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceManager::ChangeDeviceStateResult>(true, this, &ZGDPDeviceManagerPrx::_iceI_changeDeviceState, deviceID, params, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto changeDeviceStateAsync(const ::std::string& deviceID, const StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceManager::ChangeDeviceStateResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceManager::ChangeDeviceStateResult, P>(false, this, &ZGDPDeviceManagerPrx::_iceI_changeDeviceState, deviceID, params, context);
    }

    ::std::function<void()>
    changeDeviceStateAsync(const ::std::string& deviceID, const StringMap& params,
                           ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceManager::ChangeDeviceStateResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceManager::ChangeDeviceStateResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDeviceManagerPrx::_iceI_changeDeviceState, deviceID, params, context);
    }

    /// \cond INTERNAL
    void _iceI_changeDeviceState(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::ChangeDeviceStateResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool changePart(const ::std::string& parentPartID, const StringMap& partParam, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceManager::ChangePartResult>(true, this, &ZGDPDeviceManagerPrx::_iceI_changePart, parentPartID, partParam, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto changePartAsync(const ::std::string& parentPartID, const StringMap& partParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceManager::ChangePartResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceManager::ChangePartResult, P>(false, this, &ZGDPDeviceManagerPrx::_iceI_changePart, parentPartID, partParam, context);
    }

    ::std::function<void()>
    changePartAsync(const ::std::string& parentPartID, const StringMap& partParam,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceManager::ChangePartResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceManager::ChangePartResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDeviceManagerPrx::_iceI_changePart, parentPartID, partParam, context);
    }

    /// \cond INTERNAL
    void _iceI_changePart(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::ChangePartResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool getDeviceHistory(const ::std::string& deviceID, ListStringMap& listDeviceHistory, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceManager::GetDeviceHistoryResult>(true, this, &ZGDPDeviceManagerPrx::_iceI_getDeviceHistory, deviceID, context).get();
        listDeviceHistory = ::std::move(_result.listDeviceHistory);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDeviceHistoryAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceManager::GetDeviceHistoryResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceManager::GetDeviceHistoryResult, P>(false, this, &ZGDPDeviceManagerPrx::_iceI_getDeviceHistory, deviceID, context);
    }

    ::std::function<void()>
    getDeviceHistoryAsync(const ::std::string& deviceID,
                          ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceManager::GetDeviceHistoryResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listDeviceHistory), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceManager::GetDeviceHistoryResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDeviceManagerPrx::_iceI_getDeviceHistory, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getDeviceHistory(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::GetDeviceHistoryResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getDevicePartHistory(const ::std::string& deviceID, ListStringMap& listPartHistory, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceManager::GetDevicePartHistoryResult>(true, this, &ZGDPDeviceManagerPrx::_iceI_getDevicePartHistory, deviceID, context).get();
        listPartHistory = ::std::move(_result.listPartHistory);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDevicePartHistoryAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceManager::GetDevicePartHistoryResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceManager::GetDevicePartHistoryResult, P>(false, this, &ZGDPDeviceManagerPrx::_iceI_getDevicePartHistory, deviceID, context);
    }

    ::std::function<void()>
    getDevicePartHistoryAsync(const ::std::string& deviceID,
                              ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                              ::std::function<void(::std::exception_ptr)> ex = nullptr,
                              ::std::function<void(bool)> sent = nullptr,
                              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceManager::GetDevicePartHistoryResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listPartHistory), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceManager::GetDevicePartHistoryResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDeviceManagerPrx::_iceI_getDevicePartHistory, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getDevicePartHistory(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::GetDevicePartHistoryResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool addDeviceResume(const ::std::string& deviceID, const StringMap& deviceResume, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceManager::AddDeviceResumeResult>(true, this, &ZGDPDeviceManagerPrx::_iceI_addDeviceResume, deviceID, deviceResume, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto addDeviceResumeAsync(const ::std::string& deviceID, const StringMap& deviceResume, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceManager::AddDeviceResumeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceManager::AddDeviceResumeResult, P>(false, this, &ZGDPDeviceManagerPrx::_iceI_addDeviceResume, deviceID, deviceResume, context);
    }

    ::std::function<void()>
    addDeviceResumeAsync(const ::std::string& deviceID, const StringMap& deviceResume,
                         ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceManager::AddDeviceResumeResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceManager::AddDeviceResumeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDeviceManagerPrx::_iceI_addDeviceResume, deviceID, deviceResume, context);
    }

    /// \cond INTERNAL
    void _iceI_addDeviceResume(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::AddDeviceResumeResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool getDeviceResume(const ::std::string& deviceID, ListStringMap& listDeviceResume, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGDPDeviceManager::GetDeviceResumeResult>(true, this, &ZGDPDeviceManagerPrx::_iceI_getDeviceResume, deviceID, context).get();
        listDeviceResume = ::std::move(_result.listDeviceResume);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDeviceResumeAsync(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGDPDeviceManager::GetDeviceResumeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGDPDeviceManager::GetDeviceResumeResult, P>(false, this, &ZGDPDeviceManagerPrx::_iceI_getDeviceResume, deviceID, context);
    }

    ::std::function<void()>
    getDeviceResumeAsync(const ::std::string& deviceID,
                         ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGDPDeviceManager::GetDeviceResumeResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listDeviceResume), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGDPDeviceManager::GetDeviceResumeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGDPDeviceManagerPrx::_iceI_getDeviceResume, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getDeviceResume(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGDPDeviceManager::GetDeviceResumeResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGDPDeviceManagerPrx() = default;
    friend ::std::shared_ptr<ZGDPDeviceManagerPrx> IceInternal::createProxy<ZGDPDeviceManagerPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGDPDeviceManagerPtr = ::std::shared_ptr<ZGDPDeviceManager>;
using ZGDPDeviceManagerPrxPtr = ::std::shared_ptr<ZGDPDeviceManagerPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGDPDeviceManager;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGDPDeviceManager>&);
::IceProxy::Ice::Object* upCast(ZGDPDeviceManager*);
/// \endcond

}

}

namespace ZG6000
{

class ZGDPDeviceManager;
/// \cond INTERNAL
::Ice::Object* upCast(ZGDPDeviceManager*);
/// \endcond
typedef ::IceInternal::Handle< ZGDPDeviceManager> ZGDPDeviceManagerPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGDPDeviceManager> ZGDPDeviceManagerPrx;
typedef ZGDPDeviceManagerPrx ZGDPDeviceManagerPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGDPDeviceManagerPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevices.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDevices.
 */
class Callback_ZGDPDeviceManager_getDevices_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceManager_getDevices_Base> Callback_ZGDPDeviceManager_getDevicesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_addDevice.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_addDevice.
 */
class Callback_ZGDPDeviceManager_addDevice_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceManager_addDevice_Base> Callback_ZGDPDeviceManager_addDevicePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_updateDevice.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_updateDevice.
 */
class Callback_ZGDPDeviceManager_updateDevice_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceManager_updateDevice_Base> Callback_ZGDPDeviceManager_updateDevicePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_removeDevice.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_removeDevice.
 */
class Callback_ZGDPDeviceManager_removeDevice_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceManager_removeDevice_Base> Callback_ZGDPDeviceManager_removeDevicePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_changeDeviceState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_changeDeviceState.
 */
class Callback_ZGDPDeviceManager_changeDeviceState_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceManager_changeDeviceState_Base> Callback_ZGDPDeviceManager_changeDeviceStatePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_changePart.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_changePart.
 */
class Callback_ZGDPDeviceManager_changePart_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceManager_changePart_Base> Callback_ZGDPDeviceManager_changePartPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceHistory.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDeviceHistory.
 */
class Callback_ZGDPDeviceManager_getDeviceHistory_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceManager_getDeviceHistory_Base> Callback_ZGDPDeviceManager_getDeviceHistoryPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevicePartHistory.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDevicePartHistory.
 */
class Callback_ZGDPDeviceManager_getDevicePartHistory_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceManager_getDevicePartHistory_Base> Callback_ZGDPDeviceManager_getDevicePartHistoryPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_addDeviceResume.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_addDeviceResume.
 */
class Callback_ZGDPDeviceManager_addDeviceResume_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceManager_addDeviceResume_Base> Callback_ZGDPDeviceManager_addDeviceResumePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceResume.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDeviceResume.
 */
class Callback_ZGDPDeviceManager_getDeviceResume_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGDPDeviceManager_getDeviceResume_Base> Callback_ZGDPDeviceManager_getDeviceResumePtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGDPDeviceManager : public virtual ::Ice::Proxy<ZGDPDeviceManager, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool getDevices(const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, ::ZG6000::ListStringMap& devices, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDevices(devices, e, _iceI_begin_getDevices(condition, offset, limit, orderField, orderType, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDevices(const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDevices(condition, offset, limit, orderField, orderType, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDevices(const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevices(condition, offset, limit, orderField, orderType, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDevices(const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevices(condition, offset, limit, orderField, orderType, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDevices(const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, const ::ZG6000::Callback_ZGDPDeviceManager_getDevicesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevices(condition, offset, limit, orderField, orderType, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDevices(const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceManager_getDevicesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevices(condition, offset, limit, orderField, orderType, context, cb, cookie);
    }

    bool end_getDevices(::ZG6000::ListStringMap& devices, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDevices(::ZG6000::ListStringMap& iceP_devices, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDevices(const ::std::string&, ::Ice::Int, ::Ice::Int, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool addDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_addDevice(e, _iceI_begin_addDevice(deviceID, properties, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_addDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_addDevice(deviceID, properties, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_addDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addDevice(deviceID, properties, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addDevice(deviceID, properties, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, const ::ZG6000::Callback_ZGDPDeviceManager_addDevicePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addDevice(deviceID, properties, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceManager_addDevicePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addDevice(deviceID, properties, context, cb, cookie);
    }

    bool end_addDevice(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_addDevice(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_addDevice(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateDevice(e, _iceI_begin_updateDevice(deviceID, properties, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateDevice(deviceID, properties, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateDevice(deviceID, properties, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateDevice(deviceID, properties, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, const ::ZG6000::Callback_ZGDPDeviceManager_updateDevicePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateDevice(deviceID, properties, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateDevice(const ::std::string& deviceID, const ::ZG6000::StringMap& properties, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceManager_updateDevicePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateDevice(deviceID, properties, context, cb, cookie);
    }

    bool end_updateDevice(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateDevice(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateDevice(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool removeDevice(const ::std::string& deviceID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_removeDevice(e, _iceI_begin_removeDevice(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_removeDevice(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_removeDevice(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_removeDevice(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeDevice(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_removeDevice(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeDevice(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_removeDevice(const ::std::string& deviceID, const ::ZG6000::Callback_ZGDPDeviceManager_removeDevicePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeDevice(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_removeDevice(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceManager_removeDevicePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_removeDevice(deviceID, context, cb, cookie);
    }

    bool end_removeDevice(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_removeDevice(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_removeDevice(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool changeDeviceState(const ::std::string& deviceID, const ::ZG6000::StringMap& params, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_changeDeviceState(e, _iceI_begin_changeDeviceState(deviceID, params, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_changeDeviceState(const ::std::string& deviceID, const ::ZG6000::StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_changeDeviceState(deviceID, params, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_changeDeviceState(const ::std::string& deviceID, const ::ZG6000::StringMap& params, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changeDeviceState(deviceID, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changeDeviceState(const ::std::string& deviceID, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changeDeviceState(deviceID, params, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changeDeviceState(const ::std::string& deviceID, const ::ZG6000::StringMap& params, const ::ZG6000::Callback_ZGDPDeviceManager_changeDeviceStatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changeDeviceState(deviceID, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changeDeviceState(const ::std::string& deviceID, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceManager_changeDeviceStatePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changeDeviceState(deviceID, params, context, cb, cookie);
    }

    bool end_changeDeviceState(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_changeDeviceState(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_changeDeviceState(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool changePart(const ::std::string& parentPartID, const ::ZG6000::StringMap& partParam, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_changePart(e, _iceI_begin_changePart(parentPartID, partParam, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_changePart(const ::std::string& parentPartID, const ::ZG6000::StringMap& partParam, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_changePart(parentPartID, partParam, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_changePart(const ::std::string& parentPartID, const ::ZG6000::StringMap& partParam, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePart(parentPartID, partParam, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changePart(const ::std::string& parentPartID, const ::ZG6000::StringMap& partParam, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePart(parentPartID, partParam, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changePart(const ::std::string& parentPartID, const ::ZG6000::StringMap& partParam, const ::ZG6000::Callback_ZGDPDeviceManager_changePartPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePart(parentPartID, partParam, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changePart(const ::std::string& parentPartID, const ::ZG6000::StringMap& partParam, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceManager_changePartPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePart(parentPartID, partParam, context, cb, cookie);
    }

    bool end_changePart(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_changePart(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_changePart(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDeviceHistory(const ::std::string& deviceID, ::ZG6000::ListStringMap& listDeviceHistory, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDeviceHistory(listDeviceHistory, e, _iceI_begin_getDeviceHistory(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDeviceHistory(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDeviceHistory(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDeviceHistory(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceHistory(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceHistory(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceHistory(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceHistory(const ::std::string& deviceID, const ::ZG6000::Callback_ZGDPDeviceManager_getDeviceHistoryPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceHistory(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceHistory(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceManager_getDeviceHistoryPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceHistory(deviceID, context, cb, cookie);
    }

    bool end_getDeviceHistory(::ZG6000::ListStringMap& listDeviceHistory, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDeviceHistory(::ZG6000::ListStringMap& iceP_listDeviceHistory, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDeviceHistory(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDevicePartHistory(const ::std::string& deviceID, ::ZG6000::ListStringMap& listPartHistory, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDevicePartHistory(listPartHistory, e, _iceI_begin_getDevicePartHistory(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDevicePartHistory(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDevicePartHistory(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDevicePartHistory(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevicePartHistory(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDevicePartHistory(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevicePartHistory(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDevicePartHistory(const ::std::string& deviceID, const ::ZG6000::Callback_ZGDPDeviceManager_getDevicePartHistoryPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevicePartHistory(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDevicePartHistory(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceManager_getDevicePartHistoryPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevicePartHistory(deviceID, context, cb, cookie);
    }

    bool end_getDevicePartHistory(::ZG6000::ListStringMap& listPartHistory, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDevicePartHistory(::ZG6000::ListStringMap& iceP_listPartHistory, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDevicePartHistory(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool addDeviceResume(const ::std::string& deviceID, const ::ZG6000::StringMap& deviceResume, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_addDeviceResume(e, _iceI_begin_addDeviceResume(deviceID, deviceResume, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_addDeviceResume(const ::std::string& deviceID, const ::ZG6000::StringMap& deviceResume, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_addDeviceResume(deviceID, deviceResume, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_addDeviceResume(const ::std::string& deviceID, const ::ZG6000::StringMap& deviceResume, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addDeviceResume(deviceID, deviceResume, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addDeviceResume(const ::std::string& deviceID, const ::ZG6000::StringMap& deviceResume, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addDeviceResume(deviceID, deviceResume, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addDeviceResume(const ::std::string& deviceID, const ::ZG6000::StringMap& deviceResume, const ::ZG6000::Callback_ZGDPDeviceManager_addDeviceResumePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addDeviceResume(deviceID, deviceResume, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_addDeviceResume(const ::std::string& deviceID, const ::ZG6000::StringMap& deviceResume, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceManager_addDeviceResumePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_addDeviceResume(deviceID, deviceResume, context, cb, cookie);
    }

    bool end_addDeviceResume(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_addDeviceResume(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_addDeviceResume(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDeviceResume(const ::std::string& deviceID, ::ZG6000::ListStringMap& listDeviceResume, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDeviceResume(listDeviceResume, e, _iceI_begin_getDeviceResume(deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDeviceResume(const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDeviceResume(deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDeviceResume(const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceResume(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceResume(const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceResume(deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceResume(const ::std::string& deviceID, const ::ZG6000::Callback_ZGDPDeviceManager_getDeviceResumePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceResume(deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDeviceResume(const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGDPDeviceManager_getDeviceResumePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDeviceResume(deviceID, context, cb, cookie);
    }

    bool end_getDeviceResume(::ZG6000::ListStringMap& listDeviceResume, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDeviceResume(::ZG6000::ListStringMap& iceP_listDeviceResume, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDeviceResume(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGDPDeviceManager : virtual public ZGServerBase
{
public:

    typedef ZGDPDeviceManagerPrx ProxyType;
    typedef ZGDPDeviceManagerPtr PointerType;

    virtual ~ZGDPDeviceManager();

#ifdef ICE_CPP11_COMPILER
    ZGDPDeviceManager() = default;
    ZGDPDeviceManager(const ZGDPDeviceManager&) = default;
    ZGDPDeviceManager& operator=(const ZGDPDeviceManager&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool getDevices(const ::std::string& condition, ::Ice::Int offset, ::Ice::Int limit, const ::std::string& orderField, const ::std::string& orderType, ListStringMap& devices, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDevices(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool addDevice(const ::std::string& deviceID, const StringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_addDevice(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateDevice(const ::std::string& deviceID, const StringMap& properties, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateDevice(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool removeDevice(const ::std::string& deviceID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_removeDevice(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool changeDeviceState(const ::std::string& deviceID, const StringMap& params, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_changeDeviceState(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool changePart(const ::std::string& parentPartID, const StringMap& partParam, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_changePart(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDeviceHistory(const ::std::string& deviceID, ListStringMap& listDeviceHistory, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceHistory(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDevicePartHistory(const ::std::string& deviceID, ListStringMap& listPartHistory, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDevicePartHistory(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool addDeviceResume(const ::std::string& deviceID, const StringMap& deviceResume, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_addDeviceResume(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDeviceResume(const ::std::string& deviceID, ListStringMap& listDeviceResume, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDeviceResume(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGDPDeviceManager& lhs, const ZGDPDeviceManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGDPDeviceManager& lhs, const ZGDPDeviceManager& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevices.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDevices.
 */
template<class T>
class CallbackNC_ZGDPDeviceManager_getDevices : public Callback_ZGDPDeviceManager_getDevices_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceManager_getDevices(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_devices;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDevices(iceP_devices, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_devices, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevices.
 */
template<class T> Callback_ZGDPDeviceManager_getDevicesPtr
newCallback_ZGDPDeviceManager_getDevices(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_getDevices<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevices.
 */
template<class T> Callback_ZGDPDeviceManager_getDevicesPtr
newCallback_ZGDPDeviceManager_getDevices(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_getDevices<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevices.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDevices.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceManager_getDevices : public Callback_ZGDPDeviceManager_getDevices_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceManager_getDevices(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_devices;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDevices(iceP_devices, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_devices, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevices.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_getDevicesPtr
newCallback_ZGDPDeviceManager_getDevices(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_getDevices<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevices.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_getDevicesPtr
newCallback_ZGDPDeviceManager_getDevices(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_getDevices<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_addDevice.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_addDevice.
 */
template<class T>
class CallbackNC_ZGDPDeviceManager_addDevice : public Callback_ZGDPDeviceManager_addDevice_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGDPDeviceManager_addDevice(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addDevice(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_addDevice.
 */
template<class T> Callback_ZGDPDeviceManager_addDevicePtr
newCallback_ZGDPDeviceManager_addDevice(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_addDevice<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_addDevice.
 */
template<class T> Callback_ZGDPDeviceManager_addDevicePtr
newCallback_ZGDPDeviceManager_addDevice(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_addDevice<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_addDevice.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_addDevice.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceManager_addDevice : public Callback_ZGDPDeviceManager_addDevice_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceManager_addDevice(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addDevice(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_addDevice.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_addDevicePtr
newCallback_ZGDPDeviceManager_addDevice(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_addDevice<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_addDevice.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_addDevicePtr
newCallback_ZGDPDeviceManager_addDevice(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_addDevice<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_updateDevice.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_updateDevice.
 */
template<class T>
class CallbackNC_ZGDPDeviceManager_updateDevice : public Callback_ZGDPDeviceManager_updateDevice_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGDPDeviceManager_updateDevice(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateDevice(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_updateDevice.
 */
template<class T> Callback_ZGDPDeviceManager_updateDevicePtr
newCallback_ZGDPDeviceManager_updateDevice(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_updateDevice<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_updateDevice.
 */
template<class T> Callback_ZGDPDeviceManager_updateDevicePtr
newCallback_ZGDPDeviceManager_updateDevice(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_updateDevice<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_updateDevice.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_updateDevice.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceManager_updateDevice : public Callback_ZGDPDeviceManager_updateDevice_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceManager_updateDevice(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateDevice(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_updateDevice.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_updateDevicePtr
newCallback_ZGDPDeviceManager_updateDevice(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_updateDevice<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_updateDevice.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_updateDevicePtr
newCallback_ZGDPDeviceManager_updateDevice(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_updateDevice<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_removeDevice.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_removeDevice.
 */
template<class T>
class CallbackNC_ZGDPDeviceManager_removeDevice : public Callback_ZGDPDeviceManager_removeDevice_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGDPDeviceManager_removeDevice(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_removeDevice(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_removeDevice.
 */
template<class T> Callback_ZGDPDeviceManager_removeDevicePtr
newCallback_ZGDPDeviceManager_removeDevice(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_removeDevice<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_removeDevice.
 */
template<class T> Callback_ZGDPDeviceManager_removeDevicePtr
newCallback_ZGDPDeviceManager_removeDevice(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_removeDevice<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_removeDevice.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_removeDevice.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceManager_removeDevice : public Callback_ZGDPDeviceManager_removeDevice_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceManager_removeDevice(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_removeDevice(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_removeDevice.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_removeDevicePtr
newCallback_ZGDPDeviceManager_removeDevice(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_removeDevice<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_removeDevice.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_removeDevicePtr
newCallback_ZGDPDeviceManager_removeDevice(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_removeDevice<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_changeDeviceState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_changeDeviceState.
 */
template<class T>
class CallbackNC_ZGDPDeviceManager_changeDeviceState : public Callback_ZGDPDeviceManager_changeDeviceState_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGDPDeviceManager_changeDeviceState(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_changeDeviceState(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_changeDeviceState.
 */
template<class T> Callback_ZGDPDeviceManager_changeDeviceStatePtr
newCallback_ZGDPDeviceManager_changeDeviceState(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_changeDeviceState<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_changeDeviceState.
 */
template<class T> Callback_ZGDPDeviceManager_changeDeviceStatePtr
newCallback_ZGDPDeviceManager_changeDeviceState(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_changeDeviceState<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_changeDeviceState.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_changeDeviceState.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceManager_changeDeviceState : public Callback_ZGDPDeviceManager_changeDeviceState_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceManager_changeDeviceState(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_changeDeviceState(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_changeDeviceState.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_changeDeviceStatePtr
newCallback_ZGDPDeviceManager_changeDeviceState(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_changeDeviceState<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_changeDeviceState.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_changeDeviceStatePtr
newCallback_ZGDPDeviceManager_changeDeviceState(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_changeDeviceState<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_changePart.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_changePart.
 */
template<class T>
class CallbackNC_ZGDPDeviceManager_changePart : public Callback_ZGDPDeviceManager_changePart_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGDPDeviceManager_changePart(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_changePart(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_changePart.
 */
template<class T> Callback_ZGDPDeviceManager_changePartPtr
newCallback_ZGDPDeviceManager_changePart(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_changePart<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_changePart.
 */
template<class T> Callback_ZGDPDeviceManager_changePartPtr
newCallback_ZGDPDeviceManager_changePart(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_changePart<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_changePart.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_changePart.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceManager_changePart : public Callback_ZGDPDeviceManager_changePart_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceManager_changePart(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_changePart(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_changePart.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_changePartPtr
newCallback_ZGDPDeviceManager_changePart(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_changePart<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_changePart.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_changePartPtr
newCallback_ZGDPDeviceManager_changePart(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_changePart<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceHistory.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDeviceHistory.
 */
template<class T>
class CallbackNC_ZGDPDeviceManager_getDeviceHistory : public Callback_ZGDPDeviceManager_getDeviceHistory_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceManager_getDeviceHistory(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listDeviceHistory;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceHistory(iceP_listDeviceHistory, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listDeviceHistory, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceHistory.
 */
template<class T> Callback_ZGDPDeviceManager_getDeviceHistoryPtr
newCallback_ZGDPDeviceManager_getDeviceHistory(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_getDeviceHistory<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceHistory.
 */
template<class T> Callback_ZGDPDeviceManager_getDeviceHistoryPtr
newCallback_ZGDPDeviceManager_getDeviceHistory(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_getDeviceHistory<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceHistory.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDeviceHistory.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceManager_getDeviceHistory : public Callback_ZGDPDeviceManager_getDeviceHistory_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceManager_getDeviceHistory(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listDeviceHistory;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceHistory(iceP_listDeviceHistory, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listDeviceHistory, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceHistory.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_getDeviceHistoryPtr
newCallback_ZGDPDeviceManager_getDeviceHistory(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_getDeviceHistory<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceHistory.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_getDeviceHistoryPtr
newCallback_ZGDPDeviceManager_getDeviceHistory(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_getDeviceHistory<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevicePartHistory.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDevicePartHistory.
 */
template<class T>
class CallbackNC_ZGDPDeviceManager_getDevicePartHistory : public Callback_ZGDPDeviceManager_getDevicePartHistory_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceManager_getDevicePartHistory(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listPartHistory;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDevicePartHistory(iceP_listPartHistory, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listPartHistory, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevicePartHistory.
 */
template<class T> Callback_ZGDPDeviceManager_getDevicePartHistoryPtr
newCallback_ZGDPDeviceManager_getDevicePartHistory(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_getDevicePartHistory<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevicePartHistory.
 */
template<class T> Callback_ZGDPDeviceManager_getDevicePartHistoryPtr
newCallback_ZGDPDeviceManager_getDevicePartHistory(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_getDevicePartHistory<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevicePartHistory.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDevicePartHistory.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceManager_getDevicePartHistory : public Callback_ZGDPDeviceManager_getDevicePartHistory_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceManager_getDevicePartHistory(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listPartHistory;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDevicePartHistory(iceP_listPartHistory, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listPartHistory, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevicePartHistory.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_getDevicePartHistoryPtr
newCallback_ZGDPDeviceManager_getDevicePartHistory(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_getDevicePartHistory<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDevicePartHistory.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_getDevicePartHistoryPtr
newCallback_ZGDPDeviceManager_getDevicePartHistory(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_getDevicePartHistory<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_addDeviceResume.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_addDeviceResume.
 */
template<class T>
class CallbackNC_ZGDPDeviceManager_addDeviceResume : public Callback_ZGDPDeviceManager_addDeviceResume_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGDPDeviceManager_addDeviceResume(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addDeviceResume(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_addDeviceResume.
 */
template<class T> Callback_ZGDPDeviceManager_addDeviceResumePtr
newCallback_ZGDPDeviceManager_addDeviceResume(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_addDeviceResume<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_addDeviceResume.
 */
template<class T> Callback_ZGDPDeviceManager_addDeviceResumePtr
newCallback_ZGDPDeviceManager_addDeviceResume(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_addDeviceResume<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_addDeviceResume.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_addDeviceResume.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceManager_addDeviceResume : public Callback_ZGDPDeviceManager_addDeviceResume_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceManager_addDeviceResume(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_addDeviceResume(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_addDeviceResume.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_addDeviceResumePtr
newCallback_ZGDPDeviceManager_addDeviceResume(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_addDeviceResume<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_addDeviceResume.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_addDeviceResumePtr
newCallback_ZGDPDeviceManager_addDeviceResume(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_addDeviceResume<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceResume.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDeviceResume.
 */
template<class T>
class CallbackNC_ZGDPDeviceManager_getDeviceResume : public Callback_ZGDPDeviceManager_getDeviceResume_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGDPDeviceManager_getDeviceResume(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listDeviceResume;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceResume(iceP_listDeviceResume, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listDeviceResume, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceResume.
 */
template<class T> Callback_ZGDPDeviceManager_getDeviceResumePtr
newCallback_ZGDPDeviceManager_getDeviceResume(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_getDeviceResume<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceResume.
 */
template<class T> Callback_ZGDPDeviceManager_getDeviceResumePtr
newCallback_ZGDPDeviceManager_getDeviceResume(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGDPDeviceManager_getDeviceResume<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceResume.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGDPDeviceManager_getDeviceResume.
 */
template<class T, typename CT>
class Callback_ZGDPDeviceManager_getDeviceResume : public Callback_ZGDPDeviceManager_getDeviceResume_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGDPDeviceManager_getDeviceResume(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGDPDeviceManagerPrx proxy = ZGDPDeviceManagerPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listDeviceResume;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDeviceResume(iceP_listDeviceResume, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listDeviceResume, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceResume.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_getDeviceResumePtr
newCallback_ZGDPDeviceManager_getDeviceResume(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_getDeviceResume<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGDPDeviceManager::begin_getDeviceResume.
 */
template<class T, typename CT> Callback_ZGDPDeviceManager_getDeviceResumePtr
newCallback_ZGDPDeviceManager_getDeviceResume(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGDPDeviceManager_getDeviceResume<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

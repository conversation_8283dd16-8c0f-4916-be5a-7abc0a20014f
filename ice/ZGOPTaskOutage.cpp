//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPTaskOutage.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include <ZGOPTaskOutage.h>
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPTaskOutage_ids[4] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPTaskBase",
    "::ZG6000::ZGOPTaskOutage",
    "::ZG6000::ZGServerBase"
};
const ::std::string iceC_ZG6000_ZGOPTaskOutage_ops[] =
{
    "abolishTask",
    "applyPTW",
    "applySFT",
    "cancelPTW",
    "cancelSFT",
    "cancelTask",
    "changePhone",
    "checkState",
    "checkSwitchCloseConditions",
    "closeSwitch",
    "closeSwitchBatch",
    "confirmOutage",
    "confirmTask",
    "convertTask",
    "createTask",
    "deleteTask",
    "deleteTypicalTask",
    "dispatchData",
    "editTask",
    "exitApp",
    "getDevicesBoundaryType",
    "getMonitorDevices",
    "getSwitchCloseConditions",
    "getTaskInfo",
    "getTaskList",
    "getTaskUsers",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "isExternalLockEnable",
    "lockIsolator",
    "lockIsolatorBatch",
    "lockSwitch",
    "lockSwitchBatch",
    "lockTask",
    "moveTask",
    "openSwitch",
    "openSwitchBatch",
    "pauseDebug",
    "pauseTask",
    "resumeDebug",
    "resumeTask",
    "retryTask",
    "saveEvent",
    "saveOTP",
    "sendSMS",
    "setLockDevicePassword",
    "startDebug",
    "startTask",
    "stopDebug",
    "test",
    "unlockExternalLock",
    "unlockIsolator",
    "unlockIsolatorBatch",
    "unlockSwitch",
    "unlockSwitchBatch",
    "unlockTask"
};
const ::std::string iceC_ZG6000_ZGOPTaskOutage_getTaskInfo_name = "getTaskInfo";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_getTaskUsers_name = "getTaskUsers";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_createTask_name = "createTask";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_editTask_name = "editTask";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_moveTask_name = "moveTask";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_cancelTask_name = "cancelTask";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_convertTask_name = "convertTask";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_sendSMS_name = "sendSMS";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_changePhone_name = "changePhone";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_lockIsolator_name = "lockIsolator";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockIsolator_name = "unlockIsolator";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_lockIsolatorBatch_name = "lockIsolatorBatch";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockIsolatorBatch_name = "unlockIsolatorBatch";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_lockSwitch_name = "lockSwitch";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockSwitch_name = "unlockSwitch";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_lockSwitchBatch_name = "lockSwitchBatch";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockSwitchBatch_name = "unlockSwitchBatch";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_closeSwitch_name = "closeSwitch";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_openSwitch_name = "openSwitch";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_closeSwitchBatch_name = "closeSwitchBatch";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_openSwitchBatch_name = "openSwitchBatch";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_lockTask_name = "lockTask";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockTask_name = "unlockTask";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_applyPTW_name = "applyPTW";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_cancelPTW_name = "cancelPTW";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_applySFT_name = "applySFT";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_cancelSFT_name = "cancelSFT";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_saveOTP_name = "saveOTP";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_confirmOutage_name = "confirmOutage";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_saveEvent_name = "saveEvent";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_getMonitorDevices_name = "getMonitorDevices";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_setLockDevicePassword_name = "setLockDevicePassword";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockExternalLock_name = "unlockExternalLock";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_isExternalLockEnable_name = "isExternalLockEnable";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_getSwitchCloseConditions_name = "getSwitchCloseConditions";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_checkSwitchCloseConditions_name = "checkSwitchCloseConditions";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_deleteTypicalTask_name = "deleteTypicalTask";
const ::std::string iceC_ZG6000_ZGOPTaskOutage_getDevicesBoundaryType_name = "getDevicesBoundaryType";

}

bool
ZG6000::ZGOPTaskOutage::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPTaskOutage_ids, iceC_ZG6000_ZGOPTaskOutage_ids + 4, s);
}

::std::vector<::std::string>
ZG6000::ZGOPTaskOutage::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_ZG6000_ZGOPTaskOutage_ids[0], &iceC_ZG6000_ZGOPTaskOutage_ids[4]);
}

::std::string
ZG6000::ZGOPTaskOutage::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPTaskOutage::ice_staticId()
{
    static const ::std::string typeId = "::ZG6000::ZGOPTaskOutage";
    return typeId;
}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_getTaskInfo(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    StringMap iceP_head;
    ListStringMap iceP_devices;
    ListStringMap iceP_users;
    ErrorInfo iceP_e;
    bool ret = this->getTaskInfo(::std::move(iceP_taskID), iceP_head, iceP_devices, iceP_users, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_head, iceP_devices, iceP_users, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_getTaskUsers(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_users;
    ErrorInfo iceP_e;
    bool ret = this->getTaskUsers(::std::move(iceP_taskID), iceP_users, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_users, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_createTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_head;
    ListStringMap iceP_devices;
    ListStringMap iceP_users;
    istr->readAll(iceP_head, iceP_devices, iceP_users);
    inS.endReadParams();
    ::std::string iceP_taskID;
    ErrorInfo iceP_e;
    bool ret = this->createTask(::std::move(iceP_head), ::std::move(iceP_devices), ::std::move(iceP_users), iceP_taskID, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_taskID, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_editTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_head;
    ListStringMap iceP_devices;
    ListStringMap iceP_users;
    istr->readAll(iceP_taskID, iceP_head, iceP_devices, iceP_users);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->editTask(::std::move(iceP_taskID), ::std::move(iceP_head), ::std::move(iceP_devices), ::std::move(iceP_users), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_moveTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    ListStringMap iceP_oldUsers;
    ListStringMap iceP_newUsers;
    istr->readAll(iceP_taskID, iceP_oldUsers, iceP_newUsers);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->moveTask(::std::move(iceP_taskID), ::std::move(iceP_oldUsers), ::std::move(iceP_newUsers), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_cancelTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->cancelTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_convertTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->readAll(iceP_taskID, iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->convertTask(::std::move(iceP_taskID), ::std::move(iceP_param), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_sendSMS(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringList iceP_listPhone;
    istr->readAll(iceP_taskID, iceP_listPhone);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->sendSMS(::std::move(iceP_taskID), ::std::move(iceP_listPhone), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_changePhone(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    ::std::string iceP_oldPhoneNumber;
    ::std::string iceP_newPhoneNumber;
    istr->readAll(iceP_taskID, iceP_oldPhoneNumber, iceP_newPhoneNumber);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->changePhone(::std::move(iceP_taskID), ::std::move(iceP_oldPhoneNumber), ::std::move(iceP_newPhoneNumber), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_lockIsolator(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->lockIsolator(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceID), ::std::move(iceP_OTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockIsolator(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockIsolator(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceID), ::std::move(iceP_OTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_lockIsolatorBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->lockIsolatorBatch(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceOTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockIsolatorBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockIsolatorBatch(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceOTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_lockSwitch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->lockSwitch(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceID), ::std::move(iceP_OTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockSwitch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockSwitch(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceID), ::std::move(iceP_OTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_lockSwitchBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->lockSwitchBatch(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceOTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockSwitchBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockSwitchBatch(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceOTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_closeSwitch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->closeSwitch(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceID), ::std::move(iceP_OTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_openSwitch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->openSwitch(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceID), ::std::move(iceP_OTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_closeSwitchBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->closeSwitchBatch(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceOTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_openSwitchBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->openSwitchBatch(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceOTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_lockTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->readAll(iceP_taskID, iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->lockTask(::std::move(iceP_taskID), ::std::move(iceP_deviceOTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->readAll(iceP_taskID, iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockTask(::std::move(iceP_taskID), ::std::move(iceP_deviceOTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_applyPTW(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->applyPTW(::std::move(iceP_taskID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_cancelPTW(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_OTP;
    istr->readAll(iceP_taskID, iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->cancelPTW(::std::move(iceP_taskID), ::std::move(iceP_OTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_applySFT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->applySFT(::std::move(iceP_taskID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_cancelSFT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_OTP;
    istr->readAll(iceP_taskID, iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->cancelSFT(::std::move(iceP_taskID), ::std::move(iceP_OTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_saveOTP(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_OTP;
    istr->readAll(iceP_taskID, iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->saveOTP(::std::move(iceP_taskID), ::std::move(iceP_OTP), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_confirmOutage(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmOutage(::std::move(iceP_taskID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_saveEvent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_event;
    istr->readAll(iceP_taskID, iceP_deviceID, iceP_event);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->saveEvent(::std::move(iceP_taskID), ::std::move(iceP_deviceID), ::std::move(iceP_event), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_getMonitorDevices(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_listDevices;
    ErrorInfo iceP_e;
    bool ret = this->getMonitorDevices(::std::move(iceP_taskID), iceP_listDevices, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_listDevices, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_setLockDevicePassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_params;
    istr->readAll(iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setLockDevicePassword(::std::move(iceP_params), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockExternalLock(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    istr->readAll(iceP_clientID, iceP_taskID, iceP_deviceID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockExternalLock(::std::move(iceP_clientID), ::std::move(iceP_taskID), ::std::move(iceP_deviceID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_isExternalLockEnable(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    StringMap iceP_deviceEnable;
    ErrorInfo iceP_e;
    bool ret = this->isExternalLockEnable(::std::move(iceP_taskID), iceP_deviceEnable, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_deviceEnable, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_getSwitchCloseConditions(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    istr->readAll(iceP_taskID, iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_conditions;
    ErrorInfo iceP_e;
    bool ret = this->getSwitchCloseConditions(::std::move(iceP_taskID), ::std::move(iceP_deviceID), iceP_conditions, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_conditions, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_checkSwitchCloseConditions(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    istr->readAll(iceP_taskID, iceP_deviceID);
    inS.endReadParams();
    bool iceP_success;
    ErrorInfo iceP_e;
    bool ret = this->checkSwitchCloseConditions(::std::move(iceP_taskID), ::std::move(iceP_deviceID), iceP_success, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_success, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_deleteTypicalTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->readAll(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteTypicalTask(::std::move(iceP_taskID), iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_getDevicesBoundaryType(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    StringMap iceP_inputDevice;
    istr->readAll(iceP_inputDevice);
    inS.endReadParams();
    bool iceP_isValidRegion;
    ListStringMap iceP_listOutputDevice;
    ErrorInfo iceP_e;
    bool ret = this->getDevicesBoundaryType(::std::move(iceP_inputDevice), iceP_isValidRegion, iceP_listOutputDevice, iceP_e, current);
    auto ostr = inS.startWriteParams();
    ostr->writeAll(iceP_isValidRegion, iceP_listOutputDevice, iceP_e, ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPTaskOutage_ops, iceC_ZG6000_ZGOPTaskOutage_ops + 61, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPTaskOutage_ops)
    {
        case 0:
        {
            return _iceD_abolishTask(in, current);
        }
        case 1:
        {
            return _iceD_applyPTW(in, current);
        }
        case 2:
        {
            return _iceD_applySFT(in, current);
        }
        case 3:
        {
            return _iceD_cancelPTW(in, current);
        }
        case 4:
        {
            return _iceD_cancelSFT(in, current);
        }
        case 5:
        {
            return _iceD_cancelTask(in, current);
        }
        case 6:
        {
            return _iceD_changePhone(in, current);
        }
        case 7:
        {
            return _iceD_checkState(in, current);
        }
        case 8:
        {
            return _iceD_checkSwitchCloseConditions(in, current);
        }
        case 9:
        {
            return _iceD_closeSwitch(in, current);
        }
        case 10:
        {
            return _iceD_closeSwitchBatch(in, current);
        }
        case 11:
        {
            return _iceD_confirmOutage(in, current);
        }
        case 12:
        {
            return _iceD_confirmTask(in, current);
        }
        case 13:
        {
            return _iceD_convertTask(in, current);
        }
        case 14:
        {
            return _iceD_createTask(in, current);
        }
        case 15:
        {
            return _iceD_deleteTask(in, current);
        }
        case 16:
        {
            return _iceD_deleteTypicalTask(in, current);
        }
        case 17:
        {
            return _iceD_dispatchData(in, current);
        }
        case 18:
        {
            return _iceD_editTask(in, current);
        }
        case 19:
        {
            return _iceD_exitApp(in, current);
        }
        case 20:
        {
            return _iceD_getDevicesBoundaryType(in, current);
        }
        case 21:
        {
            return _iceD_getMonitorDevices(in, current);
        }
        case 22:
        {
            return _iceD_getSwitchCloseConditions(in, current);
        }
        case 23:
        {
            return _iceD_getTaskInfo(in, current);
        }
        case 24:
        {
            return _iceD_getTaskList(in, current);
        }
        case 25:
        {
            return _iceD_getTaskUsers(in, current);
        }
        case 26:
        {
            return _iceD_getVersion(in, current);
        }
        case 27:
        {
            return _iceD_heartDebug(in, current);
        }
        case 28:
        {
            return _iceD_ice_id(in, current);
        }
        case 29:
        {
            return _iceD_ice_ids(in, current);
        }
        case 30:
        {
            return _iceD_ice_isA(in, current);
        }
        case 31:
        {
            return _iceD_ice_ping(in, current);
        }
        case 32:
        {
            return _iceD_isDebugging(in, current);
        }
        case 33:
        {
            return _iceD_isExternalLockEnable(in, current);
        }
        case 34:
        {
            return _iceD_lockIsolator(in, current);
        }
        case 35:
        {
            return _iceD_lockIsolatorBatch(in, current);
        }
        case 36:
        {
            return _iceD_lockSwitch(in, current);
        }
        case 37:
        {
            return _iceD_lockSwitchBatch(in, current);
        }
        case 38:
        {
            return _iceD_lockTask(in, current);
        }
        case 39:
        {
            return _iceD_moveTask(in, current);
        }
        case 40:
        {
            return _iceD_openSwitch(in, current);
        }
        case 41:
        {
            return _iceD_openSwitchBatch(in, current);
        }
        case 42:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 43:
        {
            return _iceD_pauseTask(in, current);
        }
        case 44:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 45:
        {
            return _iceD_resumeTask(in, current);
        }
        case 46:
        {
            return _iceD_retryTask(in, current);
        }
        case 47:
        {
            return _iceD_saveEvent(in, current);
        }
        case 48:
        {
            return _iceD_saveOTP(in, current);
        }
        case 49:
        {
            return _iceD_sendSMS(in, current);
        }
        case 50:
        {
            return _iceD_setLockDevicePassword(in, current);
        }
        case 51:
        {
            return _iceD_startDebug(in, current);
        }
        case 52:
        {
            return _iceD_startTask(in, current);
        }
        case 53:
        {
            return _iceD_stopDebug(in, current);
        }
        case 54:
        {
            return _iceD_test(in, current);
        }
        case 55:
        {
            return _iceD_unlockExternalLock(in, current);
        }
        case 56:
        {
            return _iceD_unlockIsolator(in, current);
        }
        case 57:
        {
            return _iceD_unlockIsolatorBatch(in, current);
        }
        case 58:
        {
            return _iceD_unlockSwitch(in, current);
        }
        case 59:
        {
            return _iceD_unlockSwitchBatch(in, current);
        }
        case 60:
        {
            return _iceD_unlockTask(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_getTaskInfo(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::GetTaskInfoResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_getTaskInfo_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_getTaskInfo_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::GetTaskInfoResult v;
            istr->readAll(v.head, v.devices, v.users, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_getTaskUsers(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::GetTaskUsersResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_getTaskUsers_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_getTaskUsers_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::GetTaskUsersResult v;
            istr->readAll(v.users, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_createTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CreateTaskResult>>& outAsync, const StringMap& iceP_head, const ListStringMap& iceP_devices, const ListStringMap& iceP_users, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_createTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_createTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_head, iceP_devices, iceP_users);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::CreateTaskResult v;
            istr->readAll(v.taskID, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_editTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::EditTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_head, const ListStringMap& iceP_devices, const ListStringMap& iceP_users, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_editTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_editTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_head, iceP_devices, iceP_users);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::EditTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_moveTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::MoveTaskResult>>& outAsync, const ::std::string& iceP_taskID, const ListStringMap& iceP_oldUsers, const ListStringMap& iceP_newUsers, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_moveTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_moveTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_oldUsers, iceP_newUsers);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::MoveTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_cancelTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CancelTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_cancelTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_cancelTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::CancelTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_convertTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::ConvertTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_param, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_convertTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_convertTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_param);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::ConvertTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_sendSMS(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::SendSMSResult>>& outAsync, const ::std::string& iceP_taskID, const StringList& iceP_listPhone, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_sendSMS_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_sendSMS_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_listPhone);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::SendSMSResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_changePhone(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::ChangePhoneResult>>& outAsync, const ::std::string& iceP_taskID, const ::std::string& iceP_oldPhoneNumber, const ::std::string& iceP_newPhoneNumber, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_changePhone_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_changePhone_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_oldPhoneNumber, iceP_newPhoneNumber);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::ChangePhoneResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_lockIsolator(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::LockIsolatorResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_lockIsolator_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_lockIsolator_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::LockIsolatorResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_unlockIsolator(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockIsolatorResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockIsolator_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockIsolator_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::UnlockIsolatorResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_lockIsolatorBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::LockIsolatorBatchResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const StringMap& iceP_deviceOTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_lockIsolatorBatch_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_lockIsolatorBatch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::LockIsolatorBatchResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_unlockIsolatorBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockIsolatorBatchResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const StringMap& iceP_deviceOTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockIsolatorBatch_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockIsolatorBatch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::UnlockIsolatorBatchResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_lockSwitch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::LockSwitchResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_lockSwitch_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_lockSwitch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::LockSwitchResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_unlockSwitch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockSwitchResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockSwitch_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockSwitch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::UnlockSwitchResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_lockSwitchBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::LockSwitchBatchResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const StringMap& iceP_deviceOTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_lockSwitchBatch_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_lockSwitchBatch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::LockSwitchBatchResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_unlockSwitchBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockSwitchBatchResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const StringMap& iceP_deviceOTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockSwitchBatch_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockSwitchBatch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::UnlockSwitchBatchResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_closeSwitch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CloseSwitchResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_closeSwitch_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_closeSwitch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::CloseSwitchResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_openSwitch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::OpenSwitchResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_openSwitch_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_openSwitch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::OpenSwitchResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_closeSwitchBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CloseSwitchBatchResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const StringMap& iceP_deviceOTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_closeSwitchBatch_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_closeSwitchBatch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::CloseSwitchBatchResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_openSwitchBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::OpenSwitchBatchResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const StringMap& iceP_deviceOTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_openSwitchBatch_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_openSwitchBatch_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceOTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::OpenSwitchBatchResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_lockTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::LockTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_deviceOTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_lockTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_lockTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_deviceOTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::LockTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_unlockTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockTaskResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_deviceOTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_deviceOTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::UnlockTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_applyPTW(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::ApplyPTWResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_applyPTW_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_applyPTW_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::ApplyPTWResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_cancelPTW(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CancelPTWResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_OTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_cancelPTW_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_cancelPTW_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_OTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::CancelPTWResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_applySFT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::ApplySFTResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_applySFT_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_applySFT_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::ApplySFTResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_cancelSFT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CancelSFTResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_OTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_cancelSFT_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_cancelSFT_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_OTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::CancelSFTResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_saveOTP(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::SaveOTPResult>>& outAsync, const ::std::string& iceP_taskID, const StringMap& iceP_OTP, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_saveOTP_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_saveOTP_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_OTP);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::SaveOTPResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_confirmOutage(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::ConfirmOutageResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_confirmOutage_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_confirmOutage_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::ConfirmOutageResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_saveEvent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::SaveEventResult>>& outAsync, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_event, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_saveEvent_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_saveEvent_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_deviceID, iceP_event);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::SaveEventResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_getMonitorDevices(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::GetMonitorDevicesResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_getMonitorDevices_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_getMonitorDevices_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::GetMonitorDevicesResult v;
            istr->readAll(v.listDevices, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_setLockDevicePassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::SetLockDevicePasswordResult>>& outAsync, const StringMap& iceP_params, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_setLockDevicePassword_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_setLockDevicePassword_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_params);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::SetLockDevicePasswordResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_unlockExternalLock(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockExternalLockResult>>& outAsync, const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockExternalLock_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockExternalLock_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_clientID, iceP_taskID, iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::UnlockExternalLockResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_isExternalLockEnable(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::IsExternalLockEnableResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_isExternalLockEnable_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_isExternalLockEnable_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::IsExternalLockEnableResult v;
            istr->readAll(v.deviceEnable, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_getSwitchCloseConditions(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::GetSwitchCloseConditionsResult>>& outAsync, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_getSwitchCloseConditions_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_getSwitchCloseConditions_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::GetSwitchCloseConditionsResult v;
            istr->readAll(v.conditions, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_checkSwitchCloseConditions(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CheckSwitchCloseConditionsResult>>& outAsync, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_checkSwitchCloseConditions_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_checkSwitchCloseConditions_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID, iceP_deviceID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::CheckSwitchCloseConditionsResult v;
            istr->readAll(v.success, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_deleteTypicalTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::DeleteTypicalTaskResult>>& outAsync, const ::std::string& iceP_taskID, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_deleteTypicalTask_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_deleteTypicalTask_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_taskID);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::DeleteTypicalTaskResult v;
            istr->readAll(v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::ZGOPTaskOutagePrx::_iceI_getDevicesBoundaryType(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::GetDevicesBoundaryTypeResult>>& outAsync, const StringMap& iceP_inputDevice, const ::Ice::Context& context)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_getDevicesBoundaryType_name);
    outAsync->invoke(iceC_ZG6000_ZGOPTaskOutage_getDevicesBoundaryType_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_inputDevice);
        },
        nullptr,
        [](::Ice::InputStream* istr)
        {
            ZGOPTaskOutage::GetDevicesBoundaryTypeResult v;
            istr->readAll(v.isValidRegion, v.listOutputDevice, v.e, v.returnValue);
            return v;
        });
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
ZG6000::ZGOPTaskOutagePrx::_newInstance() const
{
    return ::IceInternal::createProxy<ZGOPTaskOutagePrx>();
}
/// \endcond

const ::std::string&
ZG6000::ZGOPTaskOutagePrx::ice_staticId()
{
    return ZGOPTaskOutage::ice_staticId();
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_ZG6000_ZGOPTaskOutage_getTaskInfo_name = "getTaskInfo";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_getTaskUsers_name = "getTaskUsers";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_createTask_name = "createTask";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_editTask_name = "editTask";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_moveTask_name = "moveTask";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_cancelTask_name = "cancelTask";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_convertTask_name = "convertTask";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_sendSMS_name = "sendSMS";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_changePhone_name = "changePhone";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_lockIsolator_name = "lockIsolator";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockIsolator_name = "unlockIsolator";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_lockIsolatorBatch_name = "lockIsolatorBatch";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockIsolatorBatch_name = "unlockIsolatorBatch";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_lockSwitch_name = "lockSwitch";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockSwitch_name = "unlockSwitch";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_lockSwitchBatch_name = "lockSwitchBatch";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockSwitchBatch_name = "unlockSwitchBatch";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_closeSwitch_name = "closeSwitch";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_openSwitch_name = "openSwitch";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_closeSwitchBatch_name = "closeSwitchBatch";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_openSwitchBatch_name = "openSwitchBatch";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_lockTask_name = "lockTask";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockTask_name = "unlockTask";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_applyPTW_name = "applyPTW";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_cancelPTW_name = "cancelPTW";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_applySFT_name = "applySFT";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_cancelSFT_name = "cancelSFT";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_saveOTP_name = "saveOTP";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_confirmOutage_name = "confirmOutage";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_saveEvent_name = "saveEvent";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_getMonitorDevices_name = "getMonitorDevices";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_setLockDevicePassword_name = "setLockDevicePassword";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_unlockExternalLock_name = "unlockExternalLock";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_isExternalLockEnable_name = "isExternalLockEnable";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_getSwitchCloseConditions_name = "getSwitchCloseConditions";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_checkSwitchCloseConditions_name = "checkSwitchCloseConditions";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_deleteTypicalTask_name = "deleteTypicalTask";

const ::std::string iceC_ZG6000_ZGOPTaskOutage_getDevicesBoundaryType_name = "getDevicesBoundaryType";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::ZG6000::upCast(ZGOPTaskOutage* p) { return p; }

void
::IceProxy::ZG6000::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< ZGOPTaskOutage>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new ZGOPTaskOutage;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_getTaskInfo(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_getTaskInfo_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_getTaskInfo_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_getTaskInfo_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_getTaskInfo_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_getTaskInfo(::ZG6000::StringMap& iceP_head, ::ZG6000::ListStringMap& iceP_devices, ::ZG6000::ListStringMap& iceP_users, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_getTaskInfo_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_head);
    istr->read(iceP_devices);
    istr->read(iceP_users);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_getTaskInfo(::ZG6000::StringMap& iceP_head, ::ZG6000::ListStringMap& iceP_devices, ::ZG6000::ListStringMap& iceP_users, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_getTaskInfo_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_head);
    istr->read(iceP_devices);
    istr->read(iceP_users);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_getTaskUsers(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_getTaskUsers_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_getTaskUsers_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_getTaskUsers_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_getTaskUsers_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_getTaskUsers(::ZG6000::ListStringMap& iceP_users, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_getTaskUsers_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_users);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_getTaskUsers(::ZG6000::ListStringMap& iceP_users, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_getTaskUsers_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_users);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_createTask(const ::ZG6000::StringMap& iceP_head, const ::ZG6000::ListStringMap& iceP_devices, const ::ZG6000::ListStringMap& iceP_users, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_createTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_createTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_createTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_head);
        ostr->write(iceP_devices);
        ostr->write(iceP_users);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_createTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_createTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_createTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_createTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_createTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_taskID);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_editTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_head, const ::ZG6000::ListStringMap& iceP_devices, const ::ZG6000::ListStringMap& iceP_users, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_editTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_editTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_editTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_head);
        ostr->write(iceP_devices);
        ostr->write(iceP_users);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_editTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_editTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_editTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_editTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_editTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_moveTask(const ::std::string& iceP_taskID, const ::ZG6000::ListStringMap& iceP_oldUsers, const ::ZG6000::ListStringMap& iceP_newUsers, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_moveTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_moveTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_moveTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_oldUsers);
        ostr->write(iceP_newUsers);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_moveTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_moveTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_moveTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_moveTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_moveTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_cancelTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_cancelTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_cancelTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_cancelTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_cancelTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_cancelTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_cancelTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_cancelTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_cancelTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_convertTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_param, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_convertTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_convertTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_convertTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_param);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_convertTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_convertTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_convertTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_convertTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_convertTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_sendSMS(const ::std::string& iceP_taskID, const ::ZG6000::StringList& iceP_listPhone, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_sendSMS_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_sendSMS_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_sendSMS_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_listPhone);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_sendSMS_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_sendSMS(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_sendSMS_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_sendSMS(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_sendSMS_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_changePhone(const ::std::string& iceP_taskID, const ::std::string& iceP_oldPhoneNumber, const ::std::string& iceP_newPhoneNumber, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_changePhone_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_changePhone_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_changePhone_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_oldPhoneNumber);
        ostr->write(iceP_newPhoneNumber);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_changePhone_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_changePhone(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_changePhone_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_changePhone(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_changePhone_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_lockIsolator(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_lockIsolator_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_lockIsolator_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_lockIsolator_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_OTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_lockIsolator_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_lockIsolator(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_lockIsolator_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_lockIsolator(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_lockIsolator_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_unlockIsolator(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockIsolator_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_unlockIsolator_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_unlockIsolator_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_OTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockIsolator_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_unlockIsolator(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockIsolator_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_unlockIsolator(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockIsolator_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_lockIsolatorBatch(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_deviceOTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_lockIsolatorBatch_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_lockIsolatorBatch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_lockIsolatorBatch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceOTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_lockIsolatorBatch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_lockIsolatorBatch(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_lockIsolatorBatch_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_lockIsolatorBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_lockIsolatorBatch_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_unlockIsolatorBatch(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_deviceOTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockIsolatorBatch_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_unlockIsolatorBatch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_unlockIsolatorBatch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceOTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockIsolatorBatch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_unlockIsolatorBatch(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockIsolatorBatch_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_unlockIsolatorBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockIsolatorBatch_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_lockSwitch(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_lockSwitch_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_lockSwitch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_lockSwitch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_OTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_lockSwitch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_lockSwitch(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_lockSwitch_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_lockSwitch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_lockSwitch_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_unlockSwitch(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockSwitch_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_unlockSwitch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_unlockSwitch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_OTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockSwitch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_unlockSwitch(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockSwitch_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_unlockSwitch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockSwitch_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_lockSwitchBatch(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_deviceOTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_lockSwitchBatch_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_lockSwitchBatch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_lockSwitchBatch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceOTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_lockSwitchBatch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_lockSwitchBatch(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_lockSwitchBatch_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_lockSwitchBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_lockSwitchBatch_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_unlockSwitchBatch(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_deviceOTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockSwitchBatch_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_unlockSwitchBatch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_unlockSwitchBatch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceOTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockSwitchBatch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_unlockSwitchBatch(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockSwitchBatch_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_unlockSwitchBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockSwitchBatch_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_closeSwitch(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_closeSwitch_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_closeSwitch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_closeSwitch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_OTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_closeSwitch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_closeSwitch(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_closeSwitch_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_closeSwitch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_closeSwitch_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_openSwitch(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_OTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_openSwitch_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_openSwitch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_openSwitch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_OTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_openSwitch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_openSwitch(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_openSwitch_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_openSwitch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_openSwitch_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_closeSwitchBatch(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_deviceOTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_closeSwitchBatch_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_closeSwitchBatch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_closeSwitchBatch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceOTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_closeSwitchBatch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_closeSwitchBatch(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_closeSwitchBatch_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_closeSwitchBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_closeSwitchBatch_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_openSwitchBatch(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_deviceOTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_openSwitchBatch_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_openSwitchBatch_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_openSwitchBatch_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceOTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_openSwitchBatch_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_openSwitchBatch(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_openSwitchBatch_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_openSwitchBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_openSwitchBatch_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_lockTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_deviceOTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_lockTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_lockTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_lockTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceOTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_lockTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_lockTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_lockTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_lockTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_lockTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_unlockTask(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_deviceOTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_unlockTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_unlockTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceOTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_unlockTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_unlockTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_applyPTW(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_applyPTW_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_applyPTW_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_applyPTW_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_applyPTW_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_applyPTW(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_applyPTW_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_applyPTW(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_applyPTW_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_cancelPTW(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_OTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_cancelPTW_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_cancelPTW_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_cancelPTW_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_OTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_cancelPTW_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_cancelPTW(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_cancelPTW_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_cancelPTW(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_cancelPTW_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_applySFT(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_applySFT_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_applySFT_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_applySFT_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_applySFT_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_applySFT(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_applySFT_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_applySFT(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_applySFT_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_cancelSFT(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_OTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_cancelSFT_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_cancelSFT_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_cancelSFT_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_OTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_cancelSFT_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_cancelSFT(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_cancelSFT_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_cancelSFT(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_cancelSFT_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_saveOTP(const ::std::string& iceP_taskID, const ::ZG6000::StringMap& iceP_OTP, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_saveOTP_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_saveOTP_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_saveOTP_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_OTP);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_saveOTP_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_saveOTP(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_saveOTP_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_saveOTP(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_saveOTP_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_confirmOutage(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_confirmOutage_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_confirmOutage_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_confirmOutage_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_confirmOutage_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_confirmOutage(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_confirmOutage_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_confirmOutage(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_confirmOutage_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_saveEvent(const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::std::string& iceP_event, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_saveEvent_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_saveEvent_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_saveEvent_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceID);
        ostr->write(iceP_event);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_saveEvent_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_saveEvent(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_saveEvent_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_saveEvent(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_saveEvent_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_getMonitorDevices(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_getMonitorDevices_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_getMonitorDevices_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_getMonitorDevices_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_getMonitorDevices_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_getMonitorDevices(::ZG6000::ListStringMap& iceP_listDevices, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_getMonitorDevices_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listDevices);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_getMonitorDevices(::ZG6000::ListStringMap& iceP_listDevices, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_getMonitorDevices_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_listDevices);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_setLockDevicePassword(const ::ZG6000::StringMap& iceP_params, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_setLockDevicePassword_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_setLockDevicePassword_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_setLockDevicePassword_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_params);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_setLockDevicePassword_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_setLockDevicePassword(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_setLockDevicePassword_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_setLockDevicePassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_setLockDevicePassword_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_unlockExternalLock(const ::std::string& iceP_clientID, const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_unlockExternalLock_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_unlockExternalLock_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_unlockExternalLock_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_clientID);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_unlockExternalLock_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_unlockExternalLock(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockExternalLock_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_unlockExternalLock(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_unlockExternalLock_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_isExternalLockEnable(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_isExternalLockEnable_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_isExternalLockEnable_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_isExternalLockEnable_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_isExternalLockEnable_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_isExternalLockEnable(::ZG6000::StringMap& iceP_deviceEnable, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_isExternalLockEnable_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_deviceEnable);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_isExternalLockEnable(::ZG6000::StringMap& iceP_deviceEnable, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_isExternalLockEnable_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_deviceEnable);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_getSwitchCloseConditions(const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_getSwitchCloseConditions_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_getSwitchCloseConditions_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_getSwitchCloseConditions_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_getSwitchCloseConditions_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_getSwitchCloseConditions(::ZG6000::ListStringMap& iceP_conditions, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_getSwitchCloseConditions_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_conditions);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_getSwitchCloseConditions(::ZG6000::ListStringMap& iceP_conditions, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_getSwitchCloseConditions_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_conditions);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_checkSwitchCloseConditions(const ::std::string& iceP_taskID, const ::std::string& iceP_deviceID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_checkSwitchCloseConditions_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_checkSwitchCloseConditions_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_checkSwitchCloseConditions_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        ostr->write(iceP_deviceID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_checkSwitchCloseConditions_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_checkSwitchCloseConditions(bool& iceP_success, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_checkSwitchCloseConditions_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_success);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_checkSwitchCloseConditions(bool& iceP_success, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_checkSwitchCloseConditions_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_success);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_deleteTypicalTask(const ::std::string& iceP_taskID, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_deleteTypicalTask_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_deleteTypicalTask_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_deleteTypicalTask_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_taskID);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_deleteTypicalTask_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_deleteTypicalTask(::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_deleteTypicalTask_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_deleteTypicalTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_deleteTypicalTask_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

::Ice::AsyncResultPtr
IceProxy::ZG6000::ZGOPTaskOutage::_iceI_begin_getDevicesBoundaryType(const ::ZG6000::StringMap& iceP_inputDevice, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    _checkTwowayOnly(iceC_ZG6000_ZGOPTaskOutage_getDevicesBoundaryType_name, sync);
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_ZG6000_ZGOPTaskOutage_getDevicesBoundaryType_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_ZG6000_ZGOPTaskOutage_getDevicesBoundaryType_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_inputDevice);
        result->endWriteParams();
        result->invoke(iceC_ZG6000_ZGOPTaskOutage_getDevicesBoundaryType_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

bool
IceProxy::ZG6000::ZGOPTaskOutage::end_getDevicesBoundaryType(bool& iceP_isValidRegion, ::ZG6000::ListStringMap& iceP_listOutputDevice, ::ZG6000::ErrorInfo& iceP_e, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_getDevicesBoundaryType_name);
    bool ret;
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_isValidRegion);
    istr->read(iceP_listOutputDevice);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
    return ret;
}

void IceProxy::ZG6000::ZGOPTaskOutage::_iceI_end_getDevicesBoundaryType(bool& iceP_isValidRegion, ::ZG6000::ListStringMap& iceP_listOutputDevice, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr& result)
{
    ::Ice::AsyncResult::_check(result, this, iceC_ZG6000_ZGOPTaskOutage_getDevicesBoundaryType_name);
    if(!result->_waitForResponse())
    {
        try
        {
            result->_throwUserException();
        }
        catch(const ::Ice::UserException& ex)
        {
            throw ::Ice::UnknownUserException(__FILE__, __LINE__, ex.ice_id());
        }
    }
    ::Ice::InputStream* istr = result->_startReadParams();
    istr->read(iceP_isValidRegion);
    istr->read(iceP_listOutputDevice);
    istr->read(iceP_e);
    istr->read(ret);
    result->_endReadParams();
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::ZG6000::ZGOPTaskOutage::_newInstance() const
{
    return new ZGOPTaskOutage;
}
/// \endcond

const ::std::string&
IceProxy::ZG6000::ZGOPTaskOutage::ice_staticId()
{
    return ::ZG6000::ZGOPTaskOutage::ice_staticId();
}

ZG6000::ZGOPTaskOutage::~ZGOPTaskOutage()
{
}

/// \cond INTERNAL
::Ice::Object* ZG6000::upCast(ZGOPTaskOutage* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPTaskOutage_ids[4] =
{
    "::Ice::Object",
    "::ZG6000::ZGOPTaskBase",
    "::ZG6000::ZGOPTaskOutage",
    "::ZG6000::ZGServerBase"
};

}

bool
ZG6000::ZGOPTaskOutage::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_ZG6000_ZGOPTaskOutage_ids, iceC_ZG6000_ZGOPTaskOutage_ids + 4, s);
}

::std::vector< ::std::string>
ZG6000::ZGOPTaskOutage::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_ZG6000_ZGOPTaskOutage_ids[0], &iceC_ZG6000_ZGOPTaskOutage_ids[4]);
}

const ::std::string&
ZG6000::ZGOPTaskOutage::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
ZG6000::ZGOPTaskOutage::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::ZG6000::ZGOPTaskOutage";
    return typeId;
#else
    return iceC_ZG6000_ZGOPTaskOutage_ids[2];
#endif
}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_getTaskInfo(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    StringMap iceP_head;
    ListStringMap iceP_devices;
    ListStringMap iceP_users;
    ErrorInfo iceP_e;
    bool ret = this->getTaskInfo(iceP_taskID, iceP_head, iceP_devices, iceP_users, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_head);
    ostr->write(iceP_devices);
    ostr->write(iceP_users);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_getTaskUsers(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_users;
    ErrorInfo iceP_e;
    bool ret = this->getTaskUsers(iceP_taskID, iceP_users, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_users);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_createTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_head;
    ListStringMap iceP_devices;
    ListStringMap iceP_users;
    istr->read(iceP_head);
    istr->read(iceP_devices);
    istr->read(iceP_users);
    inS.endReadParams();
    ::std::string iceP_taskID;
    ErrorInfo iceP_e;
    bool ret = this->createTask(iceP_head, iceP_devices, iceP_users, iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_taskID);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_editTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_head;
    ListStringMap iceP_devices;
    ListStringMap iceP_users;
    istr->read(iceP_taskID);
    istr->read(iceP_head);
    istr->read(iceP_devices);
    istr->read(iceP_users);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->editTask(iceP_taskID, iceP_head, iceP_devices, iceP_users, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_moveTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    ListStringMap iceP_oldUsers;
    ListStringMap iceP_newUsers;
    istr->read(iceP_taskID);
    istr->read(iceP_oldUsers);
    istr->read(iceP_newUsers);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->moveTask(iceP_taskID, iceP_oldUsers, iceP_newUsers, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_cancelTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->cancelTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_convertTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_param;
    istr->read(iceP_taskID);
    istr->read(iceP_param);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->convertTask(iceP_taskID, iceP_param, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_sendSMS(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringList iceP_listPhone;
    istr->read(iceP_taskID);
    istr->read(iceP_listPhone);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->sendSMS(iceP_taskID, iceP_listPhone, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_changePhone(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    ::std::string iceP_oldPhoneNumber;
    ::std::string iceP_newPhoneNumber;
    istr->read(iceP_taskID);
    istr->read(iceP_oldPhoneNumber);
    istr->read(iceP_newPhoneNumber);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->changePhone(iceP_taskID, iceP_oldPhoneNumber, iceP_newPhoneNumber, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_lockIsolator(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceID);
    istr->read(iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->lockIsolator(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockIsolator(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceID);
    istr->read(iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockIsolator(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_lockIsolatorBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->lockIsolatorBatch(iceP_clientID, iceP_taskID, iceP_deviceOTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockIsolatorBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockIsolatorBatch(iceP_clientID, iceP_taskID, iceP_deviceOTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_lockSwitch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceID);
    istr->read(iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->lockSwitch(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockSwitch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceID);
    istr->read(iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockSwitch(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_lockSwitchBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->lockSwitchBatch(iceP_clientID, iceP_taskID, iceP_deviceOTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockSwitchBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockSwitchBatch(iceP_clientID, iceP_taskID, iceP_deviceOTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_closeSwitch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceID);
    istr->read(iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->closeSwitch(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_openSwitch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_OTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceID);
    istr->read(iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->openSwitch(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_OTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_closeSwitchBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->closeSwitchBatch(iceP_clientID, iceP_taskID, iceP_deviceOTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_openSwitchBatch(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->openSwitchBatch(iceP_clientID, iceP_taskID, iceP_deviceOTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_lockTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->read(iceP_taskID);
    istr->read(iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->lockTask(iceP_taskID, iceP_deviceOTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_deviceOTP;
    istr->read(iceP_taskID);
    istr->read(iceP_deviceOTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockTask(iceP_taskID, iceP_deviceOTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_applyPTW(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->applyPTW(iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_cancelPTW(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_OTP;
    istr->read(iceP_taskID);
    istr->read(iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->cancelPTW(iceP_taskID, iceP_OTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_applySFT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->applySFT(iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_cancelSFT(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_OTP;
    istr->read(iceP_taskID);
    istr->read(iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->cancelSFT(iceP_taskID, iceP_OTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_saveOTP(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    StringMap iceP_OTP;
    istr->read(iceP_taskID);
    istr->read(iceP_OTP);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->saveOTP(iceP_taskID, iceP_OTP, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_confirmOutage(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->confirmOutage(iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_saveEvent(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    ::std::string iceP_event;
    istr->read(iceP_taskID);
    istr->read(iceP_deviceID);
    istr->read(iceP_event);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->saveEvent(iceP_taskID, iceP_deviceID, iceP_event, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_getMonitorDevices(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ListStringMap iceP_listDevices;
    ErrorInfo iceP_e;
    bool ret = this->getMonitorDevices(iceP_taskID, iceP_listDevices, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_listDevices);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_setLockDevicePassword(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_params;
    istr->read(iceP_params);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->setLockDevicePassword(iceP_params, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_unlockExternalLock(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_clientID;
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    istr->read(iceP_clientID);
    istr->read(iceP_taskID);
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->unlockExternalLock(iceP_clientID, iceP_taskID, iceP_deviceID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_isExternalLockEnable(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    StringMap iceP_deviceEnable;
    ErrorInfo iceP_e;
    bool ret = this->isExternalLockEnable(iceP_taskID, iceP_deviceEnable, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_deviceEnable);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_getSwitchCloseConditions(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    istr->read(iceP_taskID);
    istr->read(iceP_deviceID);
    inS.endReadParams();
    ListStringMap iceP_conditions;
    ErrorInfo iceP_e;
    bool ret = this->getSwitchCloseConditions(iceP_taskID, iceP_deviceID, iceP_conditions, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_conditions);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_checkSwitchCloseConditions(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    ::std::string iceP_deviceID;
    istr->read(iceP_taskID);
    istr->read(iceP_deviceID);
    inS.endReadParams();
    bool iceP_success;
    ErrorInfo iceP_e;
    bool ret = this->checkSwitchCloseConditions(iceP_taskID, iceP_deviceID, iceP_success, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_success);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_deleteTypicalTask(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    ::std::string iceP_taskID;
    istr->read(iceP_taskID);
    inS.endReadParams();
    ErrorInfo iceP_e;
    bool ret = this->deleteTypicalTask(iceP_taskID, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceD_getDevicesBoundaryType(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    StringMap iceP_inputDevice;
    istr->read(iceP_inputDevice);
    inS.endReadParams();
    bool iceP_isValidRegion;
    ListStringMap iceP_listOutputDevice;
    ErrorInfo iceP_e;
    bool ret = this->getDevicesBoundaryType(iceP_inputDevice, iceP_isValidRegion, iceP_listOutputDevice, iceP_e, current);
    ::Ice::OutputStream* ostr = inS.startWriteParams();
    ostr->write(iceP_isValidRegion);
    ostr->write(iceP_listOutputDevice);
    ostr->write(iceP_e);
    ostr->write(ret);
    inS.endWriteParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_ZG6000_ZGOPTaskOutage_all[] =
{
    "abolishTask",
    "applyPTW",
    "applySFT",
    "cancelPTW",
    "cancelSFT",
    "cancelTask",
    "changePhone",
    "checkState",
    "checkSwitchCloseConditions",
    "closeSwitch",
    "closeSwitchBatch",
    "confirmOutage",
    "confirmTask",
    "convertTask",
    "createTask",
    "deleteTask",
    "deleteTypicalTask",
    "dispatchData",
    "editTask",
    "exitApp",
    "getDevicesBoundaryType",
    "getMonitorDevices",
    "getSwitchCloseConditions",
    "getTaskInfo",
    "getTaskList",
    "getTaskUsers",
    "getVersion",
    "heartDebug",
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "isDebugging",
    "isExternalLockEnable",
    "lockIsolator",
    "lockIsolatorBatch",
    "lockSwitch",
    "lockSwitchBatch",
    "lockTask",
    "moveTask",
    "openSwitch",
    "openSwitchBatch",
    "pauseDebug",
    "pauseTask",
    "resumeDebug",
    "resumeTask",
    "retryTask",
    "saveEvent",
    "saveOTP",
    "sendSMS",
    "setLockDevicePassword",
    "startDebug",
    "startTask",
    "stopDebug",
    "test",
    "unlockExternalLock",
    "unlockIsolator",
    "unlockIsolatorBatch",
    "unlockSwitch",
    "unlockSwitchBatch",
    "unlockTask"
};

}

/// \cond INTERNAL
bool
ZG6000::ZGOPTaskOutage::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_ZG6000_ZGOPTaskOutage_all, iceC_ZG6000_ZGOPTaskOutage_all + 61, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_ZG6000_ZGOPTaskOutage_all)
    {
        case 0:
        {
            return _iceD_abolishTask(in, current);
        }
        case 1:
        {
            return _iceD_applyPTW(in, current);
        }
        case 2:
        {
            return _iceD_applySFT(in, current);
        }
        case 3:
        {
            return _iceD_cancelPTW(in, current);
        }
        case 4:
        {
            return _iceD_cancelSFT(in, current);
        }
        case 5:
        {
            return _iceD_cancelTask(in, current);
        }
        case 6:
        {
            return _iceD_changePhone(in, current);
        }
        case 7:
        {
            return _iceD_checkState(in, current);
        }
        case 8:
        {
            return _iceD_checkSwitchCloseConditions(in, current);
        }
        case 9:
        {
            return _iceD_closeSwitch(in, current);
        }
        case 10:
        {
            return _iceD_closeSwitchBatch(in, current);
        }
        case 11:
        {
            return _iceD_confirmOutage(in, current);
        }
        case 12:
        {
            return _iceD_confirmTask(in, current);
        }
        case 13:
        {
            return _iceD_convertTask(in, current);
        }
        case 14:
        {
            return _iceD_createTask(in, current);
        }
        case 15:
        {
            return _iceD_deleteTask(in, current);
        }
        case 16:
        {
            return _iceD_deleteTypicalTask(in, current);
        }
        case 17:
        {
            return _iceD_dispatchData(in, current);
        }
        case 18:
        {
            return _iceD_editTask(in, current);
        }
        case 19:
        {
            return _iceD_exitApp(in, current);
        }
        case 20:
        {
            return _iceD_getDevicesBoundaryType(in, current);
        }
        case 21:
        {
            return _iceD_getMonitorDevices(in, current);
        }
        case 22:
        {
            return _iceD_getSwitchCloseConditions(in, current);
        }
        case 23:
        {
            return _iceD_getTaskInfo(in, current);
        }
        case 24:
        {
            return _iceD_getTaskList(in, current);
        }
        case 25:
        {
            return _iceD_getTaskUsers(in, current);
        }
        case 26:
        {
            return _iceD_getVersion(in, current);
        }
        case 27:
        {
            return _iceD_heartDebug(in, current);
        }
        case 28:
        {
            return _iceD_ice_id(in, current);
        }
        case 29:
        {
            return _iceD_ice_ids(in, current);
        }
        case 30:
        {
            return _iceD_ice_isA(in, current);
        }
        case 31:
        {
            return _iceD_ice_ping(in, current);
        }
        case 32:
        {
            return _iceD_isDebugging(in, current);
        }
        case 33:
        {
            return _iceD_isExternalLockEnable(in, current);
        }
        case 34:
        {
            return _iceD_lockIsolator(in, current);
        }
        case 35:
        {
            return _iceD_lockIsolatorBatch(in, current);
        }
        case 36:
        {
            return _iceD_lockSwitch(in, current);
        }
        case 37:
        {
            return _iceD_lockSwitchBatch(in, current);
        }
        case 38:
        {
            return _iceD_lockTask(in, current);
        }
        case 39:
        {
            return _iceD_moveTask(in, current);
        }
        case 40:
        {
            return _iceD_openSwitch(in, current);
        }
        case 41:
        {
            return _iceD_openSwitchBatch(in, current);
        }
        case 42:
        {
            return _iceD_pauseDebug(in, current);
        }
        case 43:
        {
            return _iceD_pauseTask(in, current);
        }
        case 44:
        {
            return _iceD_resumeDebug(in, current);
        }
        case 45:
        {
            return _iceD_resumeTask(in, current);
        }
        case 46:
        {
            return _iceD_retryTask(in, current);
        }
        case 47:
        {
            return _iceD_saveEvent(in, current);
        }
        case 48:
        {
            return _iceD_saveOTP(in, current);
        }
        case 49:
        {
            return _iceD_sendSMS(in, current);
        }
        case 50:
        {
            return _iceD_setLockDevicePassword(in, current);
        }
        case 51:
        {
            return _iceD_startDebug(in, current);
        }
        case 52:
        {
            return _iceD_startTask(in, current);
        }
        case 53:
        {
            return _iceD_stopDebug(in, current);
        }
        case 54:
        {
            return _iceD_test(in, current);
        }
        case 55:
        {
            return _iceD_unlockExternalLock(in, current);
        }
        case 56:
        {
            return _iceD_unlockIsolator(in, current);
        }
        case 57:
        {
            return _iceD_unlockIsolatorBatch(in, current);
        }
        case 58:
        {
            return _iceD_unlockSwitch(in, current);
        }
        case 59:
        {
            return _iceD_unlockSwitchBatch(in, current);
        }
        case 60:
        {
            return _iceD_unlockTask(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
ZG6000::ZGOPTaskOutage::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< ZGOPTaskOutage, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
ZG6000::ZGOPTaskOutage::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< ZGOPTaskOutage, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
ZG6000::_icePatchObjectPtr(ZGOPTaskOutagePtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = ZGOPTaskOutagePtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(ZGOPTaskOutage::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif

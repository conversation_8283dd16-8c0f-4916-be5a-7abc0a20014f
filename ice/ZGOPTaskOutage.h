//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPTaskOutage.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGOPTaskOutage_h__
#define __ZGOPTaskOutage_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGOPTaskBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGOPTaskOutage;
class ZGOPTaskOutagePrx;

}

namespace ZG6000
{

class ZGOPTaskOutage : public virtual ZGOPTaskBase
{
public:

    using ProxyType = ZGOPTaskOutagePrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getTaskInfo.
     */
    struct GetTaskInfoResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        StringMap head;
        ListStringMap devices;
        ListStringMap users;
        ErrorInfo e;
    };

    /**
     * @param taskID 任务D
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取断电任务信息
     */
    virtual bool getTaskInfo(::std::string taskID, StringMap& head, ListStringMap& devices, ListStringMap& users, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskInfo(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getTaskUsers.
     */
    struct GetTaskUsersResult
    {
        bool returnValue;
        ListStringMap users;
        ErrorInfo e;
    };

    virtual bool getTaskUsers(::std::string taskID, ListStringMap& users, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskUsers(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to createTask.
     */
    struct CreateTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 创建成功后的任务ID */
        ::std::string taskID;
        /** 执行出错时的错误信息 */
        ErrorInfo e;
    };

    /**
     * @param taskID 创建成功后的任务ID
     * @param e 执行出错时的错误信息
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建断电任务
     */
    virtual bool createTask(StringMap head, ListStringMap devices, ListStringMap users, ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to editTask.
     */
    struct EditTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        /** 执行出错时的错误信息 */
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param e 执行出错时的错误信息
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	编辑断电任务
     */
    virtual bool editTask(::std::string taskID, StringMap head, ListStringMap devices, ListStringMap users, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_editTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to moveTask.
     */
    struct MoveTaskResult
    {
        /** 执行成功返回true，否则返回false */
        bool returnValue;
        /** 错误信息 */
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param e 错误信息
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，否则返回false
     * @brief	转移断电任务
     */
    virtual bool moveTask(::std::string taskID, ListStringMap oldUsers, ListStringMap newUsers, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_moveTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to cancelTask.
     */
    struct CancelTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool cancelTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_cancelTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to convertTask.
     */
    struct ConvertTaskResult
    {
        /** 执行成功返回true，失败返回false */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	转换任务类型
     */
    virtual bool convertTask(::std::string taskID, StringMap param, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_convertTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to sendSMS.
     */
    struct SendSMSResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool sendSMS(::std::string taskID, StringList listPhone, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_sendSMS(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to changePhone.
     */
    struct ChangePhoneResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool changePhone(::std::string taskID, ::std::string oldPhoneNumber, ::std::string newPhoneNumber, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_changePhone(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to lockIsolator.
     */
    struct LockIsolatorResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool lockIsolator(::std::string clientID, ::std::string taskID, ::std::string deviceID, ::std::string OTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_lockIsolator(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to unlockIsolator.
     */
    struct UnlockIsolatorResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool unlockIsolator(::std::string clientID, ::std::string taskID, ::std::string deviceID, ::std::string OTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockIsolator(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to lockIsolatorBatch.
     */
    struct LockIsolatorBatchResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool lockIsolatorBatch(::std::string clientID, ::std::string taskID, StringMap deviceOTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_lockIsolatorBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to unlockIsolatorBatch.
     */
    struct UnlockIsolatorBatchResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool unlockIsolatorBatch(::std::string clientID, ::std::string taskID, StringMap deviceOTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockIsolatorBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to lockSwitch.
     */
    struct LockSwitchResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool lockSwitch(::std::string clientID, ::std::string taskID, ::std::string deviceID, ::std::string OTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_lockSwitch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to unlockSwitch.
     */
    struct UnlockSwitchResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool unlockSwitch(::std::string clientID, ::std::string taskID, ::std::string deviceID, ::std::string OTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockSwitch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to lockSwitchBatch.
     */
    struct LockSwitchBatchResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool lockSwitchBatch(::std::string clientID, ::std::string taskID, StringMap deviceOTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_lockSwitchBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to unlockSwitchBatch.
     */
    struct UnlockSwitchBatchResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool unlockSwitchBatch(::std::string clientID, ::std::string taskID, StringMap deviceOTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockSwitchBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to closeSwitch.
     */
    struct CloseSwitchResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool closeSwitch(::std::string clientID, ::std::string taskID, ::std::string deviceID, ::std::string OTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_closeSwitch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to openSwitch.
     */
    struct OpenSwitchResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool openSwitch(::std::string clientID, ::std::string taskID, ::std::string deviceID, ::std::string OTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_openSwitch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to closeSwitchBatch.
     */
    struct CloseSwitchBatchResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool closeSwitchBatch(::std::string clientID, ::std::string taskID, StringMap deviceOTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_closeSwitchBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to openSwitchBatch.
     */
    struct OpenSwitchBatchResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool openSwitchBatch(::std::string clientID, ::std::string taskID, StringMap deviceOTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_openSwitchBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to lockTask.
     */
    struct LockTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool lockTask(::std::string taskID, StringMap deviceOTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_lockTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to unlockTask.
     */
    struct UnlockTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool unlockTask(::std::string taskID, StringMap deviceOTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to applyPTW.
     */
    struct ApplyPTWResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool applyPTW(::std::string taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_applyPTW(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to cancelPTW.
     */
    struct CancelPTWResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool cancelPTW(::std::string taskID, StringMap OTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_cancelPTW(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to applySFT.
     */
    struct ApplySFTResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool applySFT(::std::string taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_applySFT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to cancelSFT.
     */
    struct CancelSFTResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool cancelSFT(::std::string taskID, StringMap OTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_cancelSFT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to saveOTP.
     */
    struct SaveOTPResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool saveOTP(::std::string taskID, StringMap OTP, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_saveOTP(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to confirmOutage.
     */
    struct ConfirmOutageResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool confirmOutage(::std::string taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmOutage(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to saveEvent.
     */
    struct SaveEventResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool saveEvent(::std::string taskID, ::std::string deviceID, ::std::string event, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_saveEvent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getMonitorDevices.
     */
    struct GetMonitorDevicesResult
    {
        bool returnValue;
        ListStringMap listDevices;
        ErrorInfo e;
    };

    virtual bool getMonitorDevices(::std::string taskID, ListStringMap& listDevices, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getMonitorDevices(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to setLockDevicePassword.
     */
    struct SetLockDevicePasswordResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool setLockDevicePassword(StringMap params, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_setLockDevicePassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to unlockExternalLock.
     */
    struct UnlockExternalLockResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool unlockExternalLock(::std::string clientID, ::std::string taskID, ::std::string deviceID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockExternalLock(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to isExternalLockEnable.
     */
    struct IsExternalLockEnableResult
    {
        bool returnValue;
        StringMap deviceEnable;
        ErrorInfo e;
    };

    virtual bool isExternalLockEnable(::std::string taskID, StringMap& deviceEnable, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_isExternalLockEnable(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getSwitchCloseConditions.
     */
    struct GetSwitchCloseConditionsResult
    {
        bool returnValue;
        ListStringMap conditions;
        ErrorInfo e;
    };

    virtual bool getSwitchCloseConditions(::std::string taskID, ::std::string deviceID, ListStringMap& conditions, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getSwitchCloseConditions(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to checkSwitchCloseConditions.
     */
    struct CheckSwitchCloseConditionsResult
    {
        bool returnValue;
        bool success;
        ErrorInfo e;
    };

    virtual bool checkSwitchCloseConditions(::std::string taskID, ::std::string deviceID, bool& success, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_checkSwitchCloseConditions(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to deleteTypicalTask.
     */
    struct DeleteTypicalTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool deleteTypicalTask(::std::string taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteTypicalTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDevicesBoundaryType.
     */
    struct GetDevicesBoundaryTypeResult
    {
        bool returnValue;
        bool isValidRegion;
        ListStringMap listOutputDevice;
        ErrorInfo e;
    };

    virtual bool getDevicesBoundaryType(StringMap inputDevice, bool& isValidRegion, ListStringMap& listOutputDevice, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDevicesBoundaryType(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGOPTaskOutagePrx : public virtual ::Ice::Proxy<ZGOPTaskOutagePrx, ZGOPTaskBasePrx>
{
public:

    /**
     * @param taskID 任务D
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取断电任务信息
     */
    bool getTaskInfo(const ::std::string& taskID, StringMap& head, ListStringMap& devices, ListStringMap& users, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::GetTaskInfoResult>(true, this, &ZGOPTaskOutagePrx::_iceI_getTaskInfo, taskID, context).get();
        head = ::std::move(_result.head);
        devices = ::std::move(_result.devices);
        users = ::std::move(_result.users);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务D
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	获取断电任务信息
     */
    template<template<typename> class P = ::std::promise>
    auto getTaskInfoAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::GetTaskInfoResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::GetTaskInfoResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_getTaskInfo, taskID, context);
    }

    /**
     * @param taskID 任务D
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	获取断电任务信息
     */
    ::std::function<void()>
    getTaskInfoAsync(const ::std::string& taskID,
                     ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ListStringMap, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::GetTaskInfoResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.head), ::std::move(_result.devices), ::std::move(_result.users), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::GetTaskInfoResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_getTaskInfo, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getTaskInfo(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::GetTaskInfoResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getTaskUsers(const ::std::string& taskID, ListStringMap& users, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::GetTaskUsersResult>(true, this, &ZGOPTaskOutagePrx::_iceI_getTaskUsers, taskID, context).get();
        users = ::std::move(_result.users);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTaskUsersAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::GetTaskUsersResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::GetTaskUsersResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_getTaskUsers, taskID, context);
    }

    ::std::function<void()>
    getTaskUsersAsync(const ::std::string& taskID,
                      ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::GetTaskUsersResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.users), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::GetTaskUsersResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_getTaskUsers, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getTaskUsers(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::GetTaskUsersResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 创建成功后的任务ID
     * @param e 执行出错时的错误信息
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建断电任务
     */
    bool createTask(const StringMap& head, const ListStringMap& devices, const ListStringMap& users, ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::CreateTaskResult>(true, this, &ZGOPTaskOutagePrx::_iceI_createTask, head, devices, users, context).get();
        taskID = ::std::move(_result.taskID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	创建断电任务
     */
    template<template<typename> class P = ::std::promise>
    auto createTaskAsync(const StringMap& head, const ListStringMap& devices, const ListStringMap& users, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::CreateTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::CreateTaskResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_createTask, head, devices, users, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	创建断电任务
     */
    ::std::function<void()>
    createTaskAsync(const StringMap& head, const ListStringMap& devices, const ListStringMap& users,
                    ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::CreateTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.taskID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::CreateTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_createTask, head, devices, users, context);
    }

    /// \cond INTERNAL
    void _iceI_createTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CreateTaskResult>>&, const StringMap&, const ListStringMap&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param e 执行出错时的错误信息
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	编辑断电任务
     */
    bool editTask(const ::std::string& taskID, const StringMap& head, const ListStringMap& devices, const ListStringMap& users, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::EditTaskResult>(true, this, &ZGOPTaskOutagePrx::_iceI_editTask, taskID, head, devices, users, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	编辑断电任务
     */
    template<template<typename> class P = ::std::promise>
    auto editTaskAsync(const ::std::string& taskID, const StringMap& head, const ListStringMap& devices, const ListStringMap& users, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::EditTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::EditTaskResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_editTask, taskID, head, devices, users, context);
    }

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	编辑断电任务
     */
    ::std::function<void()>
    editTaskAsync(const ::std::string& taskID, const StringMap& head, const ListStringMap& devices, const ListStringMap& users,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::EditTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::EditTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_editTask, taskID, head, devices, users, context);
    }

    /// \cond INTERNAL
    void _iceI_editTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::EditTaskResult>>&, const ::std::string&, const StringMap&, const ListStringMap&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param e 错误信息
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，否则返回false
     * @brief	转移断电任务
     */
    bool moveTask(const ::std::string& taskID, const ListStringMap& oldUsers, const ListStringMap& newUsers, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::MoveTaskResult>(true, this, &ZGOPTaskOutagePrx::_iceI_moveTask, taskID, oldUsers, newUsers, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	转移断电任务
     */
    template<template<typename> class P = ::std::promise>
    auto moveTaskAsync(const ::std::string& taskID, const ListStringMap& oldUsers, const ListStringMap& newUsers, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::MoveTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::MoveTaskResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_moveTask, taskID, oldUsers, newUsers, context);
    }

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	转移断电任务
     */
    ::std::function<void()>
    moveTaskAsync(const ::std::string& taskID, const ListStringMap& oldUsers, const ListStringMap& newUsers,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::MoveTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::MoveTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_moveTask, taskID, oldUsers, newUsers, context);
    }

    /// \cond INTERNAL
    void _iceI_moveTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::MoveTaskResult>>&, const ::std::string&, const ListStringMap&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool cancelTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::CancelTaskResult>(true, this, &ZGOPTaskOutagePrx::_iceI_cancelTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto cancelTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::CancelTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::CancelTaskResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_cancelTask, taskID, param, context);
    }

    ::std::function<void()>
    cancelTaskAsync(const ::std::string& taskID, const StringMap& param,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::CancelTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::CancelTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_cancelTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_cancelTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CancelTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	转换任务类型
     */
    bool convertTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::ConvertTaskResult>(true, this, &ZGOPTaskOutagePrx::_iceI_convertTask, taskID, param, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	转换任务类型
     */
    template<template<typename> class P = ::std::promise>
    auto convertTaskAsync(const ::std::string& taskID, const StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::ConvertTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::ConvertTaskResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_convertTask, taskID, param, context);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	转换任务类型
     */
    ::std::function<void()>
    convertTaskAsync(const ::std::string& taskID, const StringMap& param,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::ConvertTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::ConvertTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_convertTask, taskID, param, context);
    }

    /// \cond INTERNAL
    void _iceI_convertTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::ConvertTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool sendSMS(const ::std::string& taskID, const StringList& listPhone, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::SendSMSResult>(true, this, &ZGOPTaskOutagePrx::_iceI_sendSMS, taskID, listPhone, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto sendSMSAsync(const ::std::string& taskID, const StringList& listPhone, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::SendSMSResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::SendSMSResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_sendSMS, taskID, listPhone, context);
    }

    ::std::function<void()>
    sendSMSAsync(const ::std::string& taskID, const StringList& listPhone,
                 ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                 ::std::function<void(::std::exception_ptr)> ex = nullptr,
                 ::std::function<void(bool)> sent = nullptr,
                 const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::SendSMSResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::SendSMSResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_sendSMS, taskID, listPhone, context);
    }

    /// \cond INTERNAL
    void _iceI_sendSMS(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::SendSMSResult>>&, const ::std::string&, const StringList&, const ::Ice::Context&);
    /// \endcond

    bool changePhone(const ::std::string& taskID, const ::std::string& oldPhoneNumber, const ::std::string& newPhoneNumber, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::ChangePhoneResult>(true, this, &ZGOPTaskOutagePrx::_iceI_changePhone, taskID, oldPhoneNumber, newPhoneNumber, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto changePhoneAsync(const ::std::string& taskID, const ::std::string& oldPhoneNumber, const ::std::string& newPhoneNumber, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::ChangePhoneResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::ChangePhoneResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_changePhone, taskID, oldPhoneNumber, newPhoneNumber, context);
    }

    ::std::function<void()>
    changePhoneAsync(const ::std::string& taskID, const ::std::string& oldPhoneNumber, const ::std::string& newPhoneNumber,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::ChangePhoneResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::ChangePhoneResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_changePhone, taskID, oldPhoneNumber, newPhoneNumber, context);
    }

    /// \cond INTERNAL
    void _iceI_changePhone(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::ChangePhoneResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool lockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::LockIsolatorResult>(true, this, &ZGOPTaskOutagePrx::_iceI_lockIsolator, clientID, taskID, deviceID, OTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto lockIsolatorAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::LockIsolatorResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::LockIsolatorResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_lockIsolator, clientID, taskID, deviceID, OTP, context);
    }

    ::std::function<void()>
    lockIsolatorAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::LockIsolatorResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::LockIsolatorResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_lockIsolator, clientID, taskID, deviceID, OTP, context);
    }

    /// \cond INTERNAL
    void _iceI_lockIsolator(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::LockIsolatorResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool unlockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::UnlockIsolatorResult>(true, this, &ZGOPTaskOutagePrx::_iceI_unlockIsolator, clientID, taskID, deviceID, OTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto unlockIsolatorAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::UnlockIsolatorResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::UnlockIsolatorResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_unlockIsolator, clientID, taskID, deviceID, OTP, context);
    }

    ::std::function<void()>
    unlockIsolatorAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP,
                        ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::UnlockIsolatorResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::UnlockIsolatorResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_unlockIsolator, clientID, taskID, deviceID, OTP, context);
    }

    /// \cond INTERNAL
    void _iceI_unlockIsolator(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockIsolatorResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool lockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::LockIsolatorBatchResult>(true, this, &ZGOPTaskOutagePrx::_iceI_lockIsolatorBatch, clientID, taskID, deviceOTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto lockIsolatorBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::LockIsolatorBatchResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::LockIsolatorBatchResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_lockIsolatorBatch, clientID, taskID, deviceOTP, context);
    }

    ::std::function<void()>
    lockIsolatorBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP,
                           ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::LockIsolatorBatchResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::LockIsolatorBatchResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_lockIsolatorBatch, clientID, taskID, deviceOTP, context);
    }

    /// \cond INTERNAL
    void _iceI_lockIsolatorBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::LockIsolatorBatchResult>>&, const ::std::string&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool unlockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::UnlockIsolatorBatchResult>(true, this, &ZGOPTaskOutagePrx::_iceI_unlockIsolatorBatch, clientID, taskID, deviceOTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto unlockIsolatorBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::UnlockIsolatorBatchResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::UnlockIsolatorBatchResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_unlockIsolatorBatch, clientID, taskID, deviceOTP, context);
    }

    ::std::function<void()>
    unlockIsolatorBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP,
                             ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::UnlockIsolatorBatchResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::UnlockIsolatorBatchResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_unlockIsolatorBatch, clientID, taskID, deviceOTP, context);
    }

    /// \cond INTERNAL
    void _iceI_unlockIsolatorBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockIsolatorBatchResult>>&, const ::std::string&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool lockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::LockSwitchResult>(true, this, &ZGOPTaskOutagePrx::_iceI_lockSwitch, clientID, taskID, deviceID, OTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto lockSwitchAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::LockSwitchResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::LockSwitchResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_lockSwitch, clientID, taskID, deviceID, OTP, context);
    }

    ::std::function<void()>
    lockSwitchAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::LockSwitchResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::LockSwitchResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_lockSwitch, clientID, taskID, deviceID, OTP, context);
    }

    /// \cond INTERNAL
    void _iceI_lockSwitch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::LockSwitchResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool unlockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::UnlockSwitchResult>(true, this, &ZGOPTaskOutagePrx::_iceI_unlockSwitch, clientID, taskID, deviceID, OTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto unlockSwitchAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::UnlockSwitchResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::UnlockSwitchResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_unlockSwitch, clientID, taskID, deviceID, OTP, context);
    }

    ::std::function<void()>
    unlockSwitchAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::UnlockSwitchResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::UnlockSwitchResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_unlockSwitch, clientID, taskID, deviceID, OTP, context);
    }

    /// \cond INTERNAL
    void _iceI_unlockSwitch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockSwitchResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool lockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::LockSwitchBatchResult>(true, this, &ZGOPTaskOutagePrx::_iceI_lockSwitchBatch, clientID, taskID, deviceOTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto lockSwitchBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::LockSwitchBatchResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::LockSwitchBatchResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_lockSwitchBatch, clientID, taskID, deviceOTP, context);
    }

    ::std::function<void()>
    lockSwitchBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP,
                         ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::LockSwitchBatchResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::LockSwitchBatchResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_lockSwitchBatch, clientID, taskID, deviceOTP, context);
    }

    /// \cond INTERNAL
    void _iceI_lockSwitchBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::LockSwitchBatchResult>>&, const ::std::string&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool unlockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::UnlockSwitchBatchResult>(true, this, &ZGOPTaskOutagePrx::_iceI_unlockSwitchBatch, clientID, taskID, deviceOTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto unlockSwitchBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::UnlockSwitchBatchResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::UnlockSwitchBatchResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_unlockSwitchBatch, clientID, taskID, deviceOTP, context);
    }

    ::std::function<void()>
    unlockSwitchBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP,
                           ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::UnlockSwitchBatchResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::UnlockSwitchBatchResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_unlockSwitchBatch, clientID, taskID, deviceOTP, context);
    }

    /// \cond INTERNAL
    void _iceI_unlockSwitchBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockSwitchBatchResult>>&, const ::std::string&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool closeSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::CloseSwitchResult>(true, this, &ZGOPTaskOutagePrx::_iceI_closeSwitch, clientID, taskID, deviceID, OTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto closeSwitchAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::CloseSwitchResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::CloseSwitchResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_closeSwitch, clientID, taskID, deviceID, OTP, context);
    }

    ::std::function<void()>
    closeSwitchAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::CloseSwitchResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::CloseSwitchResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_closeSwitch, clientID, taskID, deviceID, OTP, context);
    }

    /// \cond INTERNAL
    void _iceI_closeSwitch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CloseSwitchResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool openSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::OpenSwitchResult>(true, this, &ZGOPTaskOutagePrx::_iceI_openSwitch, clientID, taskID, deviceID, OTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto openSwitchAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::OpenSwitchResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::OpenSwitchResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_openSwitch, clientID, taskID, deviceID, OTP, context);
    }

    ::std::function<void()>
    openSwitchAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::OpenSwitchResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::OpenSwitchResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_openSwitch, clientID, taskID, deviceID, OTP, context);
    }

    /// \cond INTERNAL
    void _iceI_openSwitch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::OpenSwitchResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool closeSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::CloseSwitchBatchResult>(true, this, &ZGOPTaskOutagePrx::_iceI_closeSwitchBatch, clientID, taskID, deviceOTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto closeSwitchBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::CloseSwitchBatchResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::CloseSwitchBatchResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_closeSwitchBatch, clientID, taskID, deviceOTP, context);
    }

    ::std::function<void()>
    closeSwitchBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP,
                          ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::CloseSwitchBatchResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::CloseSwitchBatchResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_closeSwitchBatch, clientID, taskID, deviceOTP, context);
    }

    /// \cond INTERNAL
    void _iceI_closeSwitchBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CloseSwitchBatchResult>>&, const ::std::string&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool openSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::OpenSwitchBatchResult>(true, this, &ZGOPTaskOutagePrx::_iceI_openSwitchBatch, clientID, taskID, deviceOTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto openSwitchBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::OpenSwitchBatchResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::OpenSwitchBatchResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_openSwitchBatch, clientID, taskID, deviceOTP, context);
    }

    ::std::function<void()>
    openSwitchBatchAsync(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP,
                         ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::OpenSwitchBatchResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::OpenSwitchBatchResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_openSwitchBatch, clientID, taskID, deviceOTP, context);
    }

    /// \cond INTERNAL
    void _iceI_openSwitchBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::OpenSwitchBatchResult>>&, const ::std::string&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool lockTask(const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::LockTaskResult>(true, this, &ZGOPTaskOutagePrx::_iceI_lockTask, taskID, deviceOTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto lockTaskAsync(const ::std::string& taskID, const StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::LockTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::LockTaskResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_lockTask, taskID, deviceOTP, context);
    }

    ::std::function<void()>
    lockTaskAsync(const ::std::string& taskID, const StringMap& deviceOTP,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::LockTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::LockTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_lockTask, taskID, deviceOTP, context);
    }

    /// \cond INTERNAL
    void _iceI_lockTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::LockTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool unlockTask(const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::UnlockTaskResult>(true, this, &ZGOPTaskOutagePrx::_iceI_unlockTask, taskID, deviceOTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto unlockTaskAsync(const ::std::string& taskID, const StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::UnlockTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::UnlockTaskResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_unlockTask, taskID, deviceOTP, context);
    }

    ::std::function<void()>
    unlockTaskAsync(const ::std::string& taskID, const StringMap& deviceOTP,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::UnlockTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::UnlockTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_unlockTask, taskID, deviceOTP, context);
    }

    /// \cond INTERNAL
    void _iceI_unlockTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool applyPTW(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::ApplyPTWResult>(true, this, &ZGOPTaskOutagePrx::_iceI_applyPTW, taskID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto applyPTWAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::ApplyPTWResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::ApplyPTWResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_applyPTW, taskID, context);
    }

    ::std::function<void()>
    applyPTWAsync(const ::std::string& taskID,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::ApplyPTWResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::ApplyPTWResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_applyPTW, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_applyPTW(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::ApplyPTWResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool cancelPTW(const ::std::string& taskID, const StringMap& OTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::CancelPTWResult>(true, this, &ZGOPTaskOutagePrx::_iceI_cancelPTW, taskID, OTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto cancelPTWAsync(const ::std::string& taskID, const StringMap& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::CancelPTWResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::CancelPTWResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_cancelPTW, taskID, OTP, context);
    }

    ::std::function<void()>
    cancelPTWAsync(const ::std::string& taskID, const StringMap& OTP,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::CancelPTWResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::CancelPTWResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_cancelPTW, taskID, OTP, context);
    }

    /// \cond INTERNAL
    void _iceI_cancelPTW(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CancelPTWResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool applySFT(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::ApplySFTResult>(true, this, &ZGOPTaskOutagePrx::_iceI_applySFT, taskID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto applySFTAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::ApplySFTResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::ApplySFTResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_applySFT, taskID, context);
    }

    ::std::function<void()>
    applySFTAsync(const ::std::string& taskID,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::ApplySFTResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::ApplySFTResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_applySFT, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_applySFT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::ApplySFTResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool cancelSFT(const ::std::string& taskID, const StringMap& OTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::CancelSFTResult>(true, this, &ZGOPTaskOutagePrx::_iceI_cancelSFT, taskID, OTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto cancelSFTAsync(const ::std::string& taskID, const StringMap& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::CancelSFTResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::CancelSFTResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_cancelSFT, taskID, OTP, context);
    }

    ::std::function<void()>
    cancelSFTAsync(const ::std::string& taskID, const StringMap& OTP,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::CancelSFTResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::CancelSFTResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_cancelSFT, taskID, OTP, context);
    }

    /// \cond INTERNAL
    void _iceI_cancelSFT(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CancelSFTResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool saveOTP(const ::std::string& taskID, const StringMap& OTP, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::SaveOTPResult>(true, this, &ZGOPTaskOutagePrx::_iceI_saveOTP, taskID, OTP, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto saveOTPAsync(const ::std::string& taskID, const StringMap& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::SaveOTPResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::SaveOTPResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_saveOTP, taskID, OTP, context);
    }

    ::std::function<void()>
    saveOTPAsync(const ::std::string& taskID, const StringMap& OTP,
                 ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                 ::std::function<void(::std::exception_ptr)> ex = nullptr,
                 ::std::function<void(bool)> sent = nullptr,
                 const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::SaveOTPResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::SaveOTPResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_saveOTP, taskID, OTP, context);
    }

    /// \cond INTERNAL
    void _iceI_saveOTP(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::SaveOTPResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool confirmOutage(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::ConfirmOutageResult>(true, this, &ZGOPTaskOutagePrx::_iceI_confirmOutage, taskID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto confirmOutageAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::ConfirmOutageResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::ConfirmOutageResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_confirmOutage, taskID, context);
    }

    ::std::function<void()>
    confirmOutageAsync(const ::std::string& taskID,
                       ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::ConfirmOutageResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::ConfirmOutageResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_confirmOutage, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_confirmOutage(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::ConfirmOutageResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool saveEvent(const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& event, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::SaveEventResult>(true, this, &ZGOPTaskOutagePrx::_iceI_saveEvent, taskID, deviceID, event, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto saveEventAsync(const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& event, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::SaveEventResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::SaveEventResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_saveEvent, taskID, deviceID, event, context);
    }

    ::std::function<void()>
    saveEventAsync(const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& event,
                   ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::SaveEventResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::SaveEventResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_saveEvent, taskID, deviceID, event, context);
    }

    /// \cond INTERNAL
    void _iceI_saveEvent(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::SaveEventResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getMonitorDevices(const ::std::string& taskID, ListStringMap& listDevices, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::GetMonitorDevicesResult>(true, this, &ZGOPTaskOutagePrx::_iceI_getMonitorDevices, taskID, context).get();
        listDevices = ::std::move(_result.listDevices);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getMonitorDevicesAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::GetMonitorDevicesResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::GetMonitorDevicesResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_getMonitorDevices, taskID, context);
    }

    ::std::function<void()>
    getMonitorDevicesAsync(const ::std::string& taskID,
                           ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::GetMonitorDevicesResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listDevices), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::GetMonitorDevicesResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_getMonitorDevices, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getMonitorDevices(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::GetMonitorDevicesResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool setLockDevicePassword(const StringMap& params, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::SetLockDevicePasswordResult>(true, this, &ZGOPTaskOutagePrx::_iceI_setLockDevicePassword, params, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto setLockDevicePasswordAsync(const StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::SetLockDevicePasswordResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::SetLockDevicePasswordResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_setLockDevicePassword, params, context);
    }

    ::std::function<void()>
    setLockDevicePasswordAsync(const StringMap& params,
                               ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                               ::std::function<void(::std::exception_ptr)> ex = nullptr,
                               ::std::function<void(bool)> sent = nullptr,
                               const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::SetLockDevicePasswordResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::SetLockDevicePasswordResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_setLockDevicePassword, params, context);
    }

    /// \cond INTERNAL
    void _iceI_setLockDevicePassword(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::SetLockDevicePasswordResult>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool unlockExternalLock(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::UnlockExternalLockResult>(true, this, &ZGOPTaskOutagePrx::_iceI_unlockExternalLock, clientID, taskID, deviceID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto unlockExternalLockAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::UnlockExternalLockResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::UnlockExternalLockResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_unlockExternalLock, clientID, taskID, deviceID, context);
    }

    ::std::function<void()>
    unlockExternalLockAsync(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID,
                            ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::UnlockExternalLockResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::UnlockExternalLockResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_unlockExternalLock, clientID, taskID, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_unlockExternalLock(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::UnlockExternalLockResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool isExternalLockEnable(const ::std::string& taskID, StringMap& deviceEnable, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::IsExternalLockEnableResult>(true, this, &ZGOPTaskOutagePrx::_iceI_isExternalLockEnable, taskID, context).get();
        deviceEnable = ::std::move(_result.deviceEnable);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto isExternalLockEnableAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::IsExternalLockEnableResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::IsExternalLockEnableResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_isExternalLockEnable, taskID, context);
    }

    ::std::function<void()>
    isExternalLockEnableAsync(const ::std::string& taskID,
                              ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ErrorInfo)> response,
                              ::std::function<void(::std::exception_ptr)> ex = nullptr,
                              ::std::function<void(bool)> sent = nullptr,
                              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::IsExternalLockEnableResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.deviceEnable), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::IsExternalLockEnableResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_isExternalLockEnable, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_isExternalLockEnable(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::IsExternalLockEnableResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, ListStringMap& conditions, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::GetSwitchCloseConditionsResult>(true, this, &ZGOPTaskOutagePrx::_iceI_getSwitchCloseConditions, taskID, deviceID, context).get();
        conditions = ::std::move(_result.conditions);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getSwitchCloseConditionsAsync(const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::GetSwitchCloseConditionsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::GetSwitchCloseConditionsResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_getSwitchCloseConditions, taskID, deviceID, context);
    }

    ::std::function<void()>
    getSwitchCloseConditionsAsync(const ::std::string& taskID, const ::std::string& deviceID,
                                  ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                  ::std::function<void(bool)> sent = nullptr,
                                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::GetSwitchCloseConditionsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.conditions), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::GetSwitchCloseConditionsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_getSwitchCloseConditions, taskID, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_getSwitchCloseConditions(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::GetSwitchCloseConditionsResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool checkSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, bool& success, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::CheckSwitchCloseConditionsResult>(true, this, &ZGOPTaskOutagePrx::_iceI_checkSwitchCloseConditions, taskID, deviceID, context).get();
        success = _result.success;
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto checkSwitchCloseConditionsAsync(const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::CheckSwitchCloseConditionsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::CheckSwitchCloseConditionsResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_checkSwitchCloseConditions, taskID, deviceID, context);
    }

    ::std::function<void()>
    checkSwitchCloseConditionsAsync(const ::std::string& taskID, const ::std::string& deviceID,
                                    ::std::function<void(bool, bool, ::ZG6000::ErrorInfo)> response,
                                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                    ::std::function<void(bool)> sent = nullptr,
                                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::CheckSwitchCloseConditionsResult&& _result)
        {
            response(_result.returnValue, _result.success, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::CheckSwitchCloseConditionsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_checkSwitchCloseConditions, taskID, deviceID, context);
    }

    /// \cond INTERNAL
    void _iceI_checkSwitchCloseConditions(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::CheckSwitchCloseConditionsResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool deleteTypicalTask(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::DeleteTypicalTaskResult>(true, this, &ZGOPTaskOutagePrx::_iceI_deleteTypicalTask, taskID, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto deleteTypicalTaskAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::DeleteTypicalTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::DeleteTypicalTaskResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_deleteTypicalTask, taskID, context);
    }

    ::std::function<void()>
    deleteTypicalTaskAsync(const ::std::string& taskID,
                           ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::DeleteTypicalTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::DeleteTypicalTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_deleteTypicalTask, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_deleteTypicalTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::DeleteTypicalTaskResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getDevicesBoundaryType(const StringMap& inputDevice, bool& isValidRegion, ListStringMap& listOutputDevice, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskOutage::GetDevicesBoundaryTypeResult>(true, this, &ZGOPTaskOutagePrx::_iceI_getDevicesBoundaryType, inputDevice, context).get();
        isValidRegion = _result.isValidRegion;
        listOutputDevice = ::std::move(_result.listOutputDevice);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getDevicesBoundaryTypeAsync(const StringMap& inputDevice, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskOutage::GetDevicesBoundaryTypeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskOutage::GetDevicesBoundaryTypeResult, P>(false, this, &ZGOPTaskOutagePrx::_iceI_getDevicesBoundaryType, inputDevice, context);
    }

    ::std::function<void()>
    getDevicesBoundaryTypeAsync(const StringMap& inputDevice,
                                ::std::function<void(bool, bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                                ::std::function<void(::std::exception_ptr)> ex = nullptr,
                                ::std::function<void(bool)> sent = nullptr,
                                const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskOutage::GetDevicesBoundaryTypeResult&& _result)
        {
            response(_result.returnValue, _result.isValidRegion, ::std::move(_result.listOutputDevice), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskOutage::GetDevicesBoundaryTypeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskOutagePrx::_iceI_getDevicesBoundaryType, inputDevice, context);
    }

    /// \cond INTERNAL
    void _iceI_getDevicesBoundaryType(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskOutage::GetDevicesBoundaryTypeResult>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGOPTaskOutagePrx() = default;
    friend ::std::shared_ptr<ZGOPTaskOutagePrx> IceInternal::createProxy<ZGOPTaskOutagePrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGOPTaskOutagePtr = ::std::shared_ptr<ZGOPTaskOutage>;
using ZGOPTaskOutagePrxPtr = ::std::shared_ptr<ZGOPTaskOutagePrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGOPTaskOutage;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGOPTaskOutage>&);
::IceProxy::Ice::Object* upCast(ZGOPTaskOutage*);
/// \endcond

}

}

namespace ZG6000
{

class ZGOPTaskOutage;
/// \cond INTERNAL
::Ice::Object* upCast(ZGOPTaskOutage*);
/// \endcond
typedef ::IceInternal::Handle< ZGOPTaskOutage> ZGOPTaskOutagePtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGOPTaskOutage> ZGOPTaskOutagePrx;
typedef ZGOPTaskOutagePrx ZGOPTaskOutagePrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGOPTaskOutagePtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskInfo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getTaskInfo.
 */
class Callback_ZGOPTaskOutage_getTaskInfo_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_getTaskInfo_Base> Callback_ZGOPTaskOutage_getTaskInfoPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskUsers.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getTaskUsers.
 */
class Callback_ZGOPTaskOutage_getTaskUsers_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_getTaskUsers_Base> Callback_ZGOPTaskOutage_getTaskUsersPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_createTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_createTask.
 */
class Callback_ZGOPTaskOutage_createTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_createTask_Base> Callback_ZGOPTaskOutage_createTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_editTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_editTask.
 */
class Callback_ZGOPTaskOutage_editTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_editTask_Base> Callback_ZGOPTaskOutage_editTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_moveTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_moveTask.
 */
class Callback_ZGOPTaskOutage_moveTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_moveTask_Base> Callback_ZGOPTaskOutage_moveTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_cancelTask.
 */
class Callback_ZGOPTaskOutage_cancelTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_cancelTask_Base> Callback_ZGOPTaskOutage_cancelTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_convertTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_convertTask.
 */
class Callback_ZGOPTaskOutage_convertTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_convertTask_Base> Callback_ZGOPTaskOutage_convertTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_sendSMS.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_sendSMS.
 */
class Callback_ZGOPTaskOutage_sendSMS_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_sendSMS_Base> Callback_ZGOPTaskOutage_sendSMSPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_changePhone.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_changePhone.
 */
class Callback_ZGOPTaskOutage_changePhone_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_changePhone_Base> Callback_ZGOPTaskOutage_changePhonePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolator.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockIsolator.
 */
class Callback_ZGOPTaskOutage_lockIsolator_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_lockIsolator_Base> Callback_ZGOPTaskOutage_lockIsolatorPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolator.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockIsolator.
 */
class Callback_ZGOPTaskOutage_unlockIsolator_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_unlockIsolator_Base> Callback_ZGOPTaskOutage_unlockIsolatorPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolatorBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockIsolatorBatch.
 */
class Callback_ZGOPTaskOutage_lockIsolatorBatch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_lockIsolatorBatch_Base> Callback_ZGOPTaskOutage_lockIsolatorBatchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolatorBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockIsolatorBatch.
 */
class Callback_ZGOPTaskOutage_unlockIsolatorBatch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_unlockIsolatorBatch_Base> Callback_ZGOPTaskOutage_unlockIsolatorBatchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockSwitch.
 */
class Callback_ZGOPTaskOutage_lockSwitch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_lockSwitch_Base> Callback_ZGOPTaskOutage_lockSwitchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockSwitch.
 */
class Callback_ZGOPTaskOutage_unlockSwitch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_unlockSwitch_Base> Callback_ZGOPTaskOutage_unlockSwitchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockSwitchBatch.
 */
class Callback_ZGOPTaskOutage_lockSwitchBatch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_lockSwitchBatch_Base> Callback_ZGOPTaskOutage_lockSwitchBatchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockSwitchBatch.
 */
class Callback_ZGOPTaskOutage_unlockSwitchBatch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_unlockSwitchBatch_Base> Callback_ZGOPTaskOutage_unlockSwitchBatchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_closeSwitch.
 */
class Callback_ZGOPTaskOutage_closeSwitch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_closeSwitch_Base> Callback_ZGOPTaskOutage_closeSwitchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_openSwitch.
 */
class Callback_ZGOPTaskOutage_openSwitch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_openSwitch_Base> Callback_ZGOPTaskOutage_openSwitchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_closeSwitchBatch.
 */
class Callback_ZGOPTaskOutage_closeSwitchBatch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_closeSwitchBatch_Base> Callback_ZGOPTaskOutage_closeSwitchBatchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_openSwitchBatch.
 */
class Callback_ZGOPTaskOutage_openSwitchBatch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_openSwitchBatch_Base> Callback_ZGOPTaskOutage_openSwitchBatchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockTask.
 */
class Callback_ZGOPTaskOutage_lockTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_lockTask_Base> Callback_ZGOPTaskOutage_lockTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockTask.
 */
class Callback_ZGOPTaskOutage_unlockTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_unlockTask_Base> Callback_ZGOPTaskOutage_unlockTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_applyPTW.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_applyPTW.
 */
class Callback_ZGOPTaskOutage_applyPTW_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_applyPTW_Base> Callback_ZGOPTaskOutage_applyPTWPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelPTW.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_cancelPTW.
 */
class Callback_ZGOPTaskOutage_cancelPTW_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_cancelPTW_Base> Callback_ZGOPTaskOutage_cancelPTWPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_applySFT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_applySFT.
 */
class Callback_ZGOPTaskOutage_applySFT_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_applySFT_Base> Callback_ZGOPTaskOutage_applySFTPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelSFT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_cancelSFT.
 */
class Callback_ZGOPTaskOutage_cancelSFT_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_cancelSFT_Base> Callback_ZGOPTaskOutage_cancelSFTPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_saveOTP.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_saveOTP.
 */
class Callback_ZGOPTaskOutage_saveOTP_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_saveOTP_Base> Callback_ZGOPTaskOutage_saveOTPPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_confirmOutage.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_confirmOutage.
 */
class Callback_ZGOPTaskOutage_confirmOutage_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_confirmOutage_Base> Callback_ZGOPTaskOutage_confirmOutagePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_saveEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_saveEvent.
 */
class Callback_ZGOPTaskOutage_saveEvent_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_saveEvent_Base> Callback_ZGOPTaskOutage_saveEventPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getMonitorDevices.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getMonitorDevices.
 */
class Callback_ZGOPTaskOutage_getMonitorDevices_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_getMonitorDevices_Base> Callback_ZGOPTaskOutage_getMonitorDevicesPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_setLockDevicePassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_setLockDevicePassword.
 */
class Callback_ZGOPTaskOutage_setLockDevicePassword_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_setLockDevicePassword_Base> Callback_ZGOPTaskOutage_setLockDevicePasswordPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockExternalLock.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockExternalLock.
 */
class Callback_ZGOPTaskOutage_unlockExternalLock_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_unlockExternalLock_Base> Callback_ZGOPTaskOutage_unlockExternalLockPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_isExternalLockEnable.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_isExternalLockEnable.
 */
class Callback_ZGOPTaskOutage_isExternalLockEnable_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_isExternalLockEnable_Base> Callback_ZGOPTaskOutage_isExternalLockEnablePtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getSwitchCloseConditions.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getSwitchCloseConditions.
 */
class Callback_ZGOPTaskOutage_getSwitchCloseConditions_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_getSwitchCloseConditions_Base> Callback_ZGOPTaskOutage_getSwitchCloseConditionsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_checkSwitchCloseConditions.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_checkSwitchCloseConditions.
 */
class Callback_ZGOPTaskOutage_checkSwitchCloseConditions_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_checkSwitchCloseConditions_Base> Callback_ZGOPTaskOutage_checkSwitchCloseConditionsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_deleteTypicalTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_deleteTypicalTask.
 */
class Callback_ZGOPTaskOutage_deleteTypicalTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_deleteTypicalTask_Base> Callback_ZGOPTaskOutage_deleteTypicalTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getDevicesBoundaryType.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getDevicesBoundaryType.
 */
class Callback_ZGOPTaskOutage_getDevicesBoundaryType_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskOutage_getDevicesBoundaryType_Base> Callback_ZGOPTaskOutage_getDevicesBoundaryTypePtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGOPTaskOutage : public virtual ::Ice::Proxy<ZGOPTaskOutage, ::IceProxy::ZG6000::ZGOPTaskBase>
{
public:

    /**
     * @param taskID 任务D
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取断电任务信息
     */
    bool getTaskInfo(const ::std::string& taskID, ::ZG6000::StringMap& head, ::ZG6000::ListStringMap& devices, ::ZG6000::ListStringMap& users, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTaskInfo(head, devices, users, e, _iceI_begin_getTaskInfo(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务D
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取断电任务信息
     */
    ::Ice::AsyncResultPtr begin_getTaskInfo(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTaskInfo(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务D
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取断电任务信息
     */
    ::Ice::AsyncResultPtr begin_getTaskInfo(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskInfo(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务D
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取断电任务信息
     */
    ::Ice::AsyncResultPtr begin_getTaskInfo(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskInfo(taskID, context, cb, cookie);
    }

    /**
     * @param taskID 任务D
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取断电任务信息
     */
    ::Ice::AsyncResultPtr begin_getTaskInfo(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPTaskOutage_getTaskInfoPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskInfo(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务D
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取断电任务信息
     */
    ::Ice::AsyncResultPtr begin_getTaskInfo(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_getTaskInfoPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskInfo(taskID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getTaskInfo.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_getTaskInfo(::ZG6000::StringMap& head, ::ZG6000::ListStringMap& devices, ::ZG6000::ListStringMap& users, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTaskInfo(::ZG6000::StringMap& iceP_head, ::ZG6000::ListStringMap& iceP_devices, ::ZG6000::ListStringMap& iceP_users, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTaskInfo(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getTaskUsers(const ::std::string& taskID, ::ZG6000::ListStringMap& users, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTaskUsers(users, e, _iceI_begin_getTaskUsers(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTaskUsers(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTaskUsers(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTaskUsers(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskUsers(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskUsers(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskUsers(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskUsers(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPTaskOutage_getTaskUsersPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskUsers(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskUsers(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_getTaskUsersPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskUsers(taskID, context, cb, cookie);
    }

    bool end_getTaskUsers(::ZG6000::ListStringMap& users, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTaskUsers(::ZG6000::ListStringMap& iceP_users, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTaskUsers(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 创建成功后的任务ID
     * @param e 执行出错时的错误信息
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建断电任务
     */
    bool createTask(const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createTask(taskID, e, _iceI_begin_createTask(head, devices, users, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建断电任务
     */
    ::Ice::AsyncResultPtr begin_createTask(const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createTask(head, devices, users, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建断电任务
     */
    ::Ice::AsyncResultPtr begin_createTask(const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTask(head, devices, users, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建断电任务
     */
    ::Ice::AsyncResultPtr begin_createTask(const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTask(head, devices, users, context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建断电任务
     */
    ::Ice::AsyncResultPtr begin_createTask(const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, const ::ZG6000::Callback_ZGOPTaskOutage_createTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTask(head, devices, users, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	创建断电任务
     */
    ::Ice::AsyncResultPtr begin_createTask(const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_createTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTask(head, devices, users, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_createTask.
     * @param taskID 创建成功后的任务ID
     * @param e 执行出错时的错误信息
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_createTask(::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createTask(const ::ZG6000::StringMap&, const ::ZG6000::ListStringMap&, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param e 执行出错时的错误信息
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	编辑断电任务
     */
    bool editTask(const ::std::string& taskID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_editTask(e, _iceI_begin_editTask(taskID, head, devices, users, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	编辑断电任务
     */
    ::Ice::AsyncResultPtr begin_editTask(const ::std::string& taskID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_editTask(taskID, head, devices, users, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	编辑断电任务
     */
    ::Ice::AsyncResultPtr begin_editTask(const ::std::string& taskID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editTask(taskID, head, devices, users, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	编辑断电任务
     */
    ::Ice::AsyncResultPtr begin_editTask(const ::std::string& taskID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editTask(taskID, head, devices, users, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	编辑断电任务
     */
    ::Ice::AsyncResultPtr begin_editTask(const ::std::string& taskID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, const ::ZG6000::Callback_ZGOPTaskOutage_editTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editTask(taskID, head, devices, users, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	编辑断电任务
     */
    ::Ice::AsyncResultPtr begin_editTask(const ::std::string& taskID, const ::ZG6000::StringMap& head, const ::ZG6000::ListStringMap& devices, const ::ZG6000::ListStringMap& users, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_editTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editTask(taskID, head, devices, users, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_editTask.
     * @param e 执行出错时的错误信息
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_editTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_editTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_editTask(const ::std::string&, const ::ZG6000::StringMap&, const ::ZG6000::ListStringMap&, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param e 错误信息
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，否则返回false
     * @brief	转移断电任务
     */
    bool moveTask(const ::std::string& taskID, const ::ZG6000::ListStringMap& oldUsers, const ::ZG6000::ListStringMap& newUsers, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_moveTask(e, _iceI_begin_moveTask(taskID, oldUsers, newUsers, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转移断电任务
     */
    ::Ice::AsyncResultPtr begin_moveTask(const ::std::string& taskID, const ::ZG6000::ListStringMap& oldUsers, const ::ZG6000::ListStringMap& newUsers, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_moveTask(taskID, oldUsers, newUsers, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转移断电任务
     */
    ::Ice::AsyncResultPtr begin_moveTask(const ::std::string& taskID, const ::ZG6000::ListStringMap& oldUsers, const ::ZG6000::ListStringMap& newUsers, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_moveTask(taskID, oldUsers, newUsers, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转移断电任务
     */
    ::Ice::AsyncResultPtr begin_moveTask(const ::std::string& taskID, const ::ZG6000::ListStringMap& oldUsers, const ::ZG6000::ListStringMap& newUsers, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_moveTask(taskID, oldUsers, newUsers, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转移断电任务
     */
    ::Ice::AsyncResultPtr begin_moveTask(const ::std::string& taskID, const ::ZG6000::ListStringMap& oldUsers, const ::ZG6000::ListStringMap& newUsers, const ::ZG6000::Callback_ZGOPTaskOutage_moveTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_moveTask(taskID, oldUsers, newUsers, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转移断电任务
     */
    ::Ice::AsyncResultPtr begin_moveTask(const ::std::string& taskID, const ::ZG6000::ListStringMap& oldUsers, const ::ZG6000::ListStringMap& newUsers, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_moveTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_moveTask(taskID, oldUsers, newUsers, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_moveTask.
     * @param e 错误信息
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，否则返回false
     */
    bool end_moveTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_moveTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_moveTask(const ::std::string&, const ::ZG6000::ListStringMap&, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool cancelTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_cancelTask(e, _iceI_begin_cancelTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_cancelTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_cancelTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_cancelTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelTask(taskID, param, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOutage_cancelTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_cancelTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelTask(taskID, param, context, cb, cookie);
    }

    bool end_cancelTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_cancelTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_cancelTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	转换任务类型
     */
    bool convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_convertTask(e, _iceI_begin_convertTask(taskID, param, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转换任务类型
     */
    ::Ice::AsyncResultPtr begin_convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_convertTask(taskID, param, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转换任务类型
     */
    ::Ice::AsyncResultPtr begin_convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转换任务类型
     */
    ::Ice::AsyncResultPtr begin_convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertTask(taskID, param, context, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转换任务类型
     */
    ::Ice::AsyncResultPtr begin_convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::ZG6000::Callback_ZGOPTaskOutage_convertTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertTask(taskID, param, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	转换任务类型
     */
    ::Ice::AsyncResultPtr begin_convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& param, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_convertTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertTask(taskID, param, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_convertTask.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false
     */
    bool end_convertTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_convertTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_convertTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool sendSMS(const ::std::string& taskID, const ::ZG6000::StringList& listPhone, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_sendSMS(e, _iceI_begin_sendSMS(taskID, listPhone, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_sendSMS(const ::std::string& taskID, const ::ZG6000::StringList& listPhone, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_sendSMS(taskID, listPhone, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_sendSMS(const ::std::string& taskID, const ::ZG6000::StringList& listPhone, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendSMS(taskID, listPhone, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendSMS(const ::std::string& taskID, const ::ZG6000::StringList& listPhone, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendSMS(taskID, listPhone, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendSMS(const ::std::string& taskID, const ::ZG6000::StringList& listPhone, const ::ZG6000::Callback_ZGOPTaskOutage_sendSMSPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendSMS(taskID, listPhone, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendSMS(const ::std::string& taskID, const ::ZG6000::StringList& listPhone, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_sendSMSPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendSMS(taskID, listPhone, context, cb, cookie);
    }

    bool end_sendSMS(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_sendSMS(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_sendSMS(const ::std::string&, const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool changePhone(const ::std::string& taskID, const ::std::string& oldPhoneNumber, const ::std::string& newPhoneNumber, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_changePhone(e, _iceI_begin_changePhone(taskID, oldPhoneNumber, newPhoneNumber, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_changePhone(const ::std::string& taskID, const ::std::string& oldPhoneNumber, const ::std::string& newPhoneNumber, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_changePhone(taskID, oldPhoneNumber, newPhoneNumber, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_changePhone(const ::std::string& taskID, const ::std::string& oldPhoneNumber, const ::std::string& newPhoneNumber, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePhone(taskID, oldPhoneNumber, newPhoneNumber, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changePhone(const ::std::string& taskID, const ::std::string& oldPhoneNumber, const ::std::string& newPhoneNumber, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePhone(taskID, oldPhoneNumber, newPhoneNumber, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changePhone(const ::std::string& taskID, const ::std::string& oldPhoneNumber, const ::std::string& newPhoneNumber, const ::ZG6000::Callback_ZGOPTaskOutage_changePhonePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePhone(taskID, oldPhoneNumber, newPhoneNumber, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_changePhone(const ::std::string& taskID, const ::std::string& oldPhoneNumber, const ::std::string& newPhoneNumber, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_changePhonePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_changePhone(taskID, oldPhoneNumber, newPhoneNumber, context, cb, cookie);
    }

    bool end_changePhone(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_changePhone(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_changePhone(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool lockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_lockIsolator(e, _iceI_begin_lockIsolator(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_lockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_lockIsolator(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_lockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockIsolator(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockIsolator(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::ZG6000::Callback_ZGOPTaskOutage_lockIsolatorPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockIsolator(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_lockIsolatorPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockIsolator(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    bool end_lockIsolator(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_lockIsolator(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_lockIsolator(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool unlockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_unlockIsolator(e, _iceI_begin_unlockIsolator(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_unlockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_unlockIsolator(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_unlockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockIsolator(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockIsolator(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::ZG6000::Callback_ZGOPTaskOutage_unlockIsolatorPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockIsolator(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_unlockIsolatorPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockIsolator(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    bool end_unlockIsolator(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_unlockIsolator(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_unlockIsolator(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool lockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_lockIsolatorBatch(e, _iceI_begin_lockIsolatorBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_lockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_lockIsolatorBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_lockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockIsolatorBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockIsolatorBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::ZG6000::Callback_ZGOPTaskOutage_lockIsolatorBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockIsolatorBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_lockIsolatorBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockIsolatorBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    bool end_lockIsolatorBatch(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_lockIsolatorBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_lockIsolatorBatch(const ::std::string&, const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool unlockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_unlockIsolatorBatch(e, _iceI_begin_unlockIsolatorBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_unlockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_unlockIsolatorBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_unlockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockIsolatorBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockIsolatorBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::ZG6000::Callback_ZGOPTaskOutage_unlockIsolatorBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockIsolatorBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_unlockIsolatorBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockIsolatorBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    bool end_unlockIsolatorBatch(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_unlockIsolatorBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_unlockIsolatorBatch(const ::std::string&, const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool lockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_lockSwitch(e, _iceI_begin_lockSwitch(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_lockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_lockSwitch(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_lockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockSwitch(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockSwitch(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::ZG6000::Callback_ZGOPTaskOutage_lockSwitchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockSwitch(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_lockSwitchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockSwitch(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    bool end_lockSwitch(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_lockSwitch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_lockSwitch(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool unlockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_unlockSwitch(e, _iceI_begin_unlockSwitch(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_unlockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_unlockSwitch(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_unlockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockSwitch(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockSwitch(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::ZG6000::Callback_ZGOPTaskOutage_unlockSwitchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockSwitch(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_unlockSwitchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockSwitch(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    bool end_unlockSwitch(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_unlockSwitch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_unlockSwitch(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool lockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_lockSwitchBatch(e, _iceI_begin_lockSwitchBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_lockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_lockSwitchBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_lockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockSwitchBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockSwitchBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::ZG6000::Callback_ZGOPTaskOutage_lockSwitchBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockSwitchBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_lockSwitchBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockSwitchBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    bool end_lockSwitchBatch(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_lockSwitchBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_lockSwitchBatch(const ::std::string&, const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool unlockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_unlockSwitchBatch(e, _iceI_begin_unlockSwitchBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_unlockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_unlockSwitchBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_unlockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockSwitchBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockSwitchBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::ZG6000::Callback_ZGOPTaskOutage_unlockSwitchBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockSwitchBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_unlockSwitchBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockSwitchBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    bool end_unlockSwitchBatch(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_unlockSwitchBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_unlockSwitchBatch(const ::std::string&, const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool closeSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_closeSwitch(e, _iceI_begin_closeSwitch(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_closeSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_closeSwitch(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_closeSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeSwitch(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_closeSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeSwitch(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_closeSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::ZG6000::Callback_ZGOPTaskOutage_closeSwitchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeSwitch(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_closeSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_closeSwitchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeSwitch(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    bool end_closeSwitch(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_closeSwitch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_closeSwitch(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool openSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_openSwitch(e, _iceI_begin_openSwitch(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_openSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_openSwitch(clientID, taskID, deviceID, OTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_openSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_openSwitch(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_openSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_openSwitch(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_openSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::ZG6000::Callback_ZGOPTaskOutage_openSwitchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_openSwitch(clientID, taskID, deviceID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_openSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_openSwitchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_openSwitch(clientID, taskID, deviceID, OTP, context, cb, cookie);
    }

    bool end_openSwitch(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_openSwitch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_openSwitch(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool closeSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_closeSwitchBatch(e, _iceI_begin_closeSwitchBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_closeSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_closeSwitchBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_closeSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeSwitchBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_closeSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeSwitchBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_closeSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::ZG6000::Callback_ZGOPTaskOutage_closeSwitchBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeSwitchBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_closeSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_closeSwitchBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_closeSwitchBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    bool end_closeSwitchBatch(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_closeSwitchBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_closeSwitchBatch(const ::std::string&, const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool openSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_openSwitchBatch(e, _iceI_begin_openSwitchBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_openSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_openSwitchBatch(clientID, taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_openSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_openSwitchBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_openSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_openSwitchBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_openSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::ZG6000::Callback_ZGOPTaskOutage_openSwitchBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_openSwitchBatch(clientID, taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_openSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_openSwitchBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_openSwitchBatch(clientID, taskID, deviceOTP, context, cb, cookie);
    }

    bool end_openSwitchBatch(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_openSwitchBatch(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_openSwitchBatch(const ::std::string&, const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool lockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_lockTask(e, _iceI_begin_lockTask(taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_lockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_lockTask(taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_lockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockTask(taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockTask(taskID, deviceOTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::ZG6000::Callback_ZGOPTaskOutage_lockTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockTask(taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_lockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_lockTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_lockTask(taskID, deviceOTP, context, cb, cookie);
    }

    bool end_lockTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_lockTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_lockTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool unlockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_unlockTask(e, _iceI_begin_unlockTask(taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_unlockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_unlockTask(taskID, deviceOTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_unlockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockTask(taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockTask(taskID, deviceOTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::ZG6000::Callback_ZGOPTaskOutage_unlockTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockTask(taskID, deviceOTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockTask(const ::std::string& taskID, const ::ZG6000::StringMap& deviceOTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_unlockTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockTask(taskID, deviceOTP, context, cb, cookie);
    }

    bool end_unlockTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_unlockTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_unlockTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool applyPTW(const ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_applyPTW(e, _iceI_begin_applyPTW(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_applyPTW(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_applyPTW(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_applyPTW(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_applyPTW(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_applyPTW(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_applyPTW(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_applyPTW(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPTaskOutage_applyPTWPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_applyPTW(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_applyPTW(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_applyPTWPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_applyPTW(taskID, context, cb, cookie);
    }

    bool end_applyPTW(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_applyPTW(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_applyPTW(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool cancelPTW(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_cancelPTW(e, _iceI_begin_cancelPTW(taskID, OTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_cancelPTW(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_cancelPTW(taskID, OTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_cancelPTW(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelPTW(taskID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelPTW(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelPTW(taskID, OTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelPTW(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::ZG6000::Callback_ZGOPTaskOutage_cancelPTWPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelPTW(taskID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelPTW(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_cancelPTWPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelPTW(taskID, OTP, context, cb, cookie);
    }

    bool end_cancelPTW(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_cancelPTW(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_cancelPTW(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool applySFT(const ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_applySFT(e, _iceI_begin_applySFT(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_applySFT(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_applySFT(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_applySFT(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_applySFT(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_applySFT(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_applySFT(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_applySFT(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPTaskOutage_applySFTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_applySFT(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_applySFT(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_applySFTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_applySFT(taskID, context, cb, cookie);
    }

    bool end_applySFT(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_applySFT(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_applySFT(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool cancelSFT(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_cancelSFT(e, _iceI_begin_cancelSFT(taskID, OTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_cancelSFT(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_cancelSFT(taskID, OTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_cancelSFT(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelSFT(taskID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelSFT(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelSFT(taskID, OTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelSFT(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::ZG6000::Callback_ZGOPTaskOutage_cancelSFTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelSFT(taskID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_cancelSFT(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_cancelSFTPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_cancelSFT(taskID, OTP, context, cb, cookie);
    }

    bool end_cancelSFT(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_cancelSFT(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_cancelSFT(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool saveOTP(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_saveOTP(e, _iceI_begin_saveOTP(taskID, OTP, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_saveOTP(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_saveOTP(taskID, OTP, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_saveOTP(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_saveOTP(taskID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_saveOTP(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_saveOTP(taskID, OTP, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_saveOTP(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::ZG6000::Callback_ZGOPTaskOutage_saveOTPPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_saveOTP(taskID, OTP, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_saveOTP(const ::std::string& taskID, const ::ZG6000::StringMap& OTP, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_saveOTPPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_saveOTP(taskID, OTP, context, cb, cookie);
    }

    bool end_saveOTP(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_saveOTP(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_saveOTP(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool confirmOutage(const ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_confirmOutage(e, _iceI_begin_confirmOutage(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_confirmOutage(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_confirmOutage(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_confirmOutage(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmOutage(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_confirmOutage(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmOutage(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_confirmOutage(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPTaskOutage_confirmOutagePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmOutage(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_confirmOutage(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_confirmOutagePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_confirmOutage(taskID, context, cb, cookie);
    }

    bool end_confirmOutage(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_confirmOutage(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_confirmOutage(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool saveEvent(const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& event, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_saveEvent(e, _iceI_begin_saveEvent(taskID, deviceID, event, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_saveEvent(const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& event, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_saveEvent(taskID, deviceID, event, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_saveEvent(const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& event, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_saveEvent(taskID, deviceID, event, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_saveEvent(const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& event, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_saveEvent(taskID, deviceID, event, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_saveEvent(const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& event, const ::ZG6000::Callback_ZGOPTaskOutage_saveEventPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_saveEvent(taskID, deviceID, event, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_saveEvent(const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& event, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_saveEventPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_saveEvent(taskID, deviceID, event, context, cb, cookie);
    }

    bool end_saveEvent(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_saveEvent(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_saveEvent(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getMonitorDevices(const ::std::string& taskID, ::ZG6000::ListStringMap& listDevices, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getMonitorDevices(listDevices, e, _iceI_begin_getMonitorDevices(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getMonitorDevices(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getMonitorDevices(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getMonitorDevices(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getMonitorDevices(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getMonitorDevices(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getMonitorDevices(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getMonitorDevices(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPTaskOutage_getMonitorDevicesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getMonitorDevices(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getMonitorDevices(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_getMonitorDevicesPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getMonitorDevices(taskID, context, cb, cookie);
    }

    bool end_getMonitorDevices(::ZG6000::ListStringMap& listDevices, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getMonitorDevices(::ZG6000::ListStringMap& iceP_listDevices, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getMonitorDevices(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool setLockDevicePassword(const ::ZG6000::StringMap& params, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_setLockDevicePassword(e, _iceI_begin_setLockDevicePassword(params, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_setLockDevicePassword(const ::ZG6000::StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_setLockDevicePassword(params, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_setLockDevicePassword(const ::ZG6000::StringMap& params, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setLockDevicePassword(params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setLockDevicePassword(const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setLockDevicePassword(params, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setLockDevicePassword(const ::ZG6000::StringMap& params, const ::ZG6000::Callback_ZGOPTaskOutage_setLockDevicePasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setLockDevicePassword(params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_setLockDevicePassword(const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_setLockDevicePasswordPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_setLockDevicePassword(params, context, cb, cookie);
    }

    bool end_setLockDevicePassword(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_setLockDevicePassword(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_setLockDevicePassword(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool unlockExternalLock(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_unlockExternalLock(e, _iceI_begin_unlockExternalLock(clientID, taskID, deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_unlockExternalLock(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_unlockExternalLock(clientID, taskID, deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_unlockExternalLock(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockExternalLock(clientID, taskID, deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockExternalLock(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockExternalLock(clientID, taskID, deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockExternalLock(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::ZG6000::Callback_ZGOPTaskOutage_unlockExternalLockPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockExternalLock(clientID, taskID, deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_unlockExternalLock(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_unlockExternalLockPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_unlockExternalLock(clientID, taskID, deviceID, context, cb, cookie);
    }

    bool end_unlockExternalLock(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_unlockExternalLock(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_unlockExternalLock(const ::std::string&, const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool isExternalLockEnable(const ::std::string& taskID, ::ZG6000::StringMap& deviceEnable, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_isExternalLockEnable(deviceEnable, e, _iceI_begin_isExternalLockEnable(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_isExternalLockEnable(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_isExternalLockEnable(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_isExternalLockEnable(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isExternalLockEnable(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isExternalLockEnable(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isExternalLockEnable(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isExternalLockEnable(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPTaskOutage_isExternalLockEnablePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isExternalLockEnable(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_isExternalLockEnable(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_isExternalLockEnablePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_isExternalLockEnable(taskID, context, cb, cookie);
    }

    bool end_isExternalLockEnable(::ZG6000::StringMap& deviceEnable, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_isExternalLockEnable(::ZG6000::StringMap& iceP_deviceEnable, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_isExternalLockEnable(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, ::ZG6000::ListStringMap& conditions, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getSwitchCloseConditions(conditions, e, _iceI_begin_getSwitchCloseConditions(taskID, deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getSwitchCloseConditions(taskID, deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSwitchCloseConditions(taskID, deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSwitchCloseConditions(taskID, deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, const ::ZG6000::Callback_ZGOPTaskOutage_getSwitchCloseConditionsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSwitchCloseConditions(taskID, deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_getSwitchCloseConditionsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSwitchCloseConditions(taskID, deviceID, context, cb, cookie);
    }

    bool end_getSwitchCloseConditions(::ZG6000::ListStringMap& conditions, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getSwitchCloseConditions(::ZG6000::ListStringMap& iceP_conditions, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getSwitchCloseConditions(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool checkSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, bool& success, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_checkSwitchCloseConditions(success, e, _iceI_begin_checkSwitchCloseConditions(taskID, deviceID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_checkSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_checkSwitchCloseConditions(taskID, deviceID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_checkSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_checkSwitchCloseConditions(taskID, deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_checkSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_checkSwitchCloseConditions(taskID, deviceID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_checkSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, const ::ZG6000::Callback_ZGOPTaskOutage_checkSwitchCloseConditionsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_checkSwitchCloseConditions(taskID, deviceID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_checkSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_checkSwitchCloseConditionsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_checkSwitchCloseConditions(taskID, deviceID, context, cb, cookie);
    }

    bool end_checkSwitchCloseConditions(bool& success, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_checkSwitchCloseConditions(bool& iceP_success, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_checkSwitchCloseConditions(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool deleteTypicalTask(const ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_deleteTypicalTask(e, _iceI_begin_deleteTypicalTask(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_deleteTypicalTask(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_deleteTypicalTask(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_deleteTypicalTask(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTypicalTask(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteTypicalTask(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTypicalTask(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteTypicalTask(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPTaskOutage_deleteTypicalTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTypicalTask(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_deleteTypicalTask(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_deleteTypicalTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_deleteTypicalTask(taskID, context, cb, cookie);
    }

    bool end_deleteTypicalTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_deleteTypicalTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_deleteTypicalTask(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getDevicesBoundaryType(const ::ZG6000::StringMap& inputDevice, bool& isValidRegion, ::ZG6000::ListStringMap& listOutputDevice, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDevicesBoundaryType(isValidRegion, listOutputDevice, e, _iceI_begin_getDevicesBoundaryType(inputDevice, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getDevicesBoundaryType(const ::ZG6000::StringMap& inputDevice, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDevicesBoundaryType(inputDevice, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getDevicesBoundaryType(const ::ZG6000::StringMap& inputDevice, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevicesBoundaryType(inputDevice, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDevicesBoundaryType(const ::ZG6000::StringMap& inputDevice, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevicesBoundaryType(inputDevice, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDevicesBoundaryType(const ::ZG6000::StringMap& inputDevice, const ::ZG6000::Callback_ZGOPTaskOutage_getDevicesBoundaryTypePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevicesBoundaryType(inputDevice, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getDevicesBoundaryType(const ::ZG6000::StringMap& inputDevice, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskOutage_getDevicesBoundaryTypePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDevicesBoundaryType(inputDevice, context, cb, cookie);
    }

    bool end_getDevicesBoundaryType(bool& isValidRegion, ::ZG6000::ListStringMap& listOutputDevice, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDevicesBoundaryType(bool& iceP_isValidRegion, ::ZG6000::ListStringMap& iceP_listOutputDevice, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDevicesBoundaryType(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGOPTaskOutage : virtual public ZGOPTaskBase
{
public:

    typedef ZGOPTaskOutagePrx ProxyType;
    typedef ZGOPTaskOutagePtr PointerType;

    virtual ~ZGOPTaskOutage();

#ifdef ICE_CPP11_COMPILER
    ZGOPTaskOutage() = default;
    ZGOPTaskOutage(const ZGOPTaskOutage&) = default;
    ZGOPTaskOutage& operator=(const ZGOPTaskOutage&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param taskID 任务D
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	获取断电任务信息
     */
    virtual bool getTaskInfo(const ::std::string& taskID, StringMap& head, ListStringMap& devices, ListStringMap& users, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskInfo(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getTaskUsers(const ::std::string& taskID, ListStringMap& users, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskUsers(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 创建成功后的任务ID
     * @param e 执行出错时的错误信息
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	创建断电任务
     */
    virtual bool createTask(const StringMap& head, const ListStringMap& devices, const ListStringMap& users, ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param head 任务头信息
     * @param devices 设备信息
     * @param e 执行出错时的错误信息
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	编辑断电任务
     */
    virtual bool editTask(const ::std::string& taskID, const StringMap& head, const ListStringMap& devices, const ListStringMap& users, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_editTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param oldUsers 原CP
     * @param newUsers 新CP
     * @param e 错误信息
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，否则返回false
     * @brief	转移断电任务
     */
    virtual bool moveTask(const ::std::string& taskID, const ListStringMap& oldUsers, const ListStringMap& newUsers, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_moveTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool cancelTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_cancelTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param taskID 任务ID
     * @param param 任务参数
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false
     * @brief	转换任务类型
     */
    virtual bool convertTask(const ::std::string& taskID, const StringMap& param, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_convertTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool sendSMS(const ::std::string& taskID, const StringList& listPhone, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_sendSMS(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool changePhone(const ::std::string& taskID, const ::std::string& oldPhoneNumber, const ::std::string& newPhoneNumber, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_changePhone(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool lockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_lockIsolator(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool unlockIsolator(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockIsolator(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool lockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_lockIsolatorBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool unlockIsolatorBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockIsolatorBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool lockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_lockSwitch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool unlockSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockSwitch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool lockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_lockSwitchBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool unlockSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockSwitchBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool closeSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_closeSwitch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool openSwitch(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& OTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_openSwitch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool closeSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_closeSwitchBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool openSwitchBatch(const ::std::string& clientID, const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_openSwitchBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool lockTask(const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_lockTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool unlockTask(const ::std::string& taskID, const StringMap& deviceOTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool applyPTW(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_applyPTW(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool cancelPTW(const ::std::string& taskID, const StringMap& OTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_cancelPTW(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool applySFT(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_applySFT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool cancelSFT(const ::std::string& taskID, const StringMap& OTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_cancelSFT(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool saveOTP(const ::std::string& taskID, const StringMap& OTP, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_saveOTP(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool confirmOutage(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_confirmOutage(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool saveEvent(const ::std::string& taskID, const ::std::string& deviceID, const ::std::string& event, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_saveEvent(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getMonitorDevices(const ::std::string& taskID, ListStringMap& listDevices, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getMonitorDevices(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool setLockDevicePassword(const StringMap& params, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_setLockDevicePassword(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool unlockExternalLock(const ::std::string& clientID, const ::std::string& taskID, const ::std::string& deviceID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_unlockExternalLock(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool isExternalLockEnable(const ::std::string& taskID, StringMap& deviceEnable, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_isExternalLockEnable(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, ListStringMap& conditions, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getSwitchCloseConditions(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool checkSwitchCloseConditions(const ::std::string& taskID, const ::std::string& deviceID, bool& success, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_checkSwitchCloseConditions(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool deleteTypicalTask(const ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_deleteTypicalTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getDevicesBoundaryType(const StringMap& inputDevice, bool& isValidRegion, ListStringMap& listOutputDevice, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDevicesBoundaryType(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGOPTaskOutage& lhs, const ZGOPTaskOutage& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGOPTaskOutage& lhs, const ZGOPTaskOutage& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskInfo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getTaskInfo.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_getTaskInfo : public Callback_ZGOPTaskOutage_getTaskInfo_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_getTaskInfo(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        StringMap iceP_head;
        ListStringMap iceP_devices;
        ListStringMap iceP_users;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskInfo(iceP_head, iceP_devices, iceP_users, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_head, iceP_devices, iceP_users, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskInfo.
 */
template<class T> Callback_ZGOPTaskOutage_getTaskInfoPtr
newCallback_ZGOPTaskOutage_getTaskInfo(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_getTaskInfo<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskInfo.
 */
template<class T> Callback_ZGOPTaskOutage_getTaskInfoPtr
newCallback_ZGOPTaskOutage_getTaskInfo(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_getTaskInfo<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskInfo.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getTaskInfo.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_getTaskInfo : public Callback_ZGOPTaskOutage_getTaskInfo_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_getTaskInfo(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        StringMap iceP_head;
        ListStringMap iceP_devices;
        ListStringMap iceP_users;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskInfo(iceP_head, iceP_devices, iceP_users, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_head, iceP_devices, iceP_users, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskInfo.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_getTaskInfoPtr
newCallback_ZGOPTaskOutage_getTaskInfo(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_getTaskInfo<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskInfo.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_getTaskInfoPtr
newCallback_ZGOPTaskOutage_getTaskInfo(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_getTaskInfo<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskUsers.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getTaskUsers.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_getTaskUsers : public Callback_ZGOPTaskOutage_getTaskUsers_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_getTaskUsers(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_users;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskUsers(iceP_users, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_users, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskUsers.
 */
template<class T> Callback_ZGOPTaskOutage_getTaskUsersPtr
newCallback_ZGOPTaskOutage_getTaskUsers(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_getTaskUsers<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskUsers.
 */
template<class T> Callback_ZGOPTaskOutage_getTaskUsersPtr
newCallback_ZGOPTaskOutage_getTaskUsers(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_getTaskUsers<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskUsers.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getTaskUsers.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_getTaskUsers : public Callback_ZGOPTaskOutage_getTaskUsers_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_getTaskUsers(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_users;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskUsers(iceP_users, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_users, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskUsers.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_getTaskUsersPtr
newCallback_ZGOPTaskOutage_getTaskUsers(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_getTaskUsers<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getTaskUsers.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_getTaskUsersPtr
newCallback_ZGOPTaskOutage_getTaskUsers(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_getTaskUsers<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_createTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_createTask.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_createTask : public Callback_ZGOPTaskOutage_createTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_createTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ::std::string iceP_taskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createTask(iceP_taskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_taskID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_createTask.
 */
template<class T> Callback_ZGOPTaskOutage_createTaskPtr
newCallback_ZGOPTaskOutage_createTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_createTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_createTask.
 */
template<class T> Callback_ZGOPTaskOutage_createTaskPtr
newCallback_ZGOPTaskOutage_createTask(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_createTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_createTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_createTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_createTask : public Callback_ZGOPTaskOutage_createTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_createTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ::std::string iceP_taskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createTask(iceP_taskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_taskID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_createTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_createTaskPtr
newCallback_ZGOPTaskOutage_createTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_createTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_createTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_createTaskPtr
newCallback_ZGOPTaskOutage_createTask(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_createTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_editTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_editTask.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_editTask : public Callback_ZGOPTaskOutage_editTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_editTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_editTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_editTask.
 */
template<class T> Callback_ZGOPTaskOutage_editTaskPtr
newCallback_ZGOPTaskOutage_editTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_editTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_editTask.
 */
template<class T> Callback_ZGOPTaskOutage_editTaskPtr
newCallback_ZGOPTaskOutage_editTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_editTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_editTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_editTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_editTask : public Callback_ZGOPTaskOutage_editTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_editTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_editTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_editTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_editTaskPtr
newCallback_ZGOPTaskOutage_editTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_editTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_editTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_editTaskPtr
newCallback_ZGOPTaskOutage_editTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_editTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_moveTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_moveTask.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_moveTask : public Callback_ZGOPTaskOutage_moveTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_moveTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_moveTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_moveTask.
 */
template<class T> Callback_ZGOPTaskOutage_moveTaskPtr
newCallback_ZGOPTaskOutage_moveTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_moveTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_moveTask.
 */
template<class T> Callback_ZGOPTaskOutage_moveTaskPtr
newCallback_ZGOPTaskOutage_moveTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_moveTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_moveTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_moveTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_moveTask : public Callback_ZGOPTaskOutage_moveTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_moveTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_moveTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_moveTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_moveTaskPtr
newCallback_ZGOPTaskOutage_moveTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_moveTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_moveTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_moveTaskPtr
newCallback_ZGOPTaskOutage_moveTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_moveTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_cancelTask.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_cancelTask : public Callback_ZGOPTaskOutage_cancelTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_cancelTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_cancelTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelTask.
 */
template<class T> Callback_ZGOPTaskOutage_cancelTaskPtr
newCallback_ZGOPTaskOutage_cancelTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_cancelTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelTask.
 */
template<class T> Callback_ZGOPTaskOutage_cancelTaskPtr
newCallback_ZGOPTaskOutage_cancelTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_cancelTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_cancelTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_cancelTask : public Callback_ZGOPTaskOutage_cancelTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_cancelTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_cancelTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_cancelTaskPtr
newCallback_ZGOPTaskOutage_cancelTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_cancelTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_cancelTaskPtr
newCallback_ZGOPTaskOutage_cancelTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_cancelTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_convertTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_convertTask.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_convertTask : public Callback_ZGOPTaskOutage_convertTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_convertTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_convertTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_convertTask.
 */
template<class T> Callback_ZGOPTaskOutage_convertTaskPtr
newCallback_ZGOPTaskOutage_convertTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_convertTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_convertTask.
 */
template<class T> Callback_ZGOPTaskOutage_convertTaskPtr
newCallback_ZGOPTaskOutage_convertTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_convertTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_convertTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_convertTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_convertTask : public Callback_ZGOPTaskOutage_convertTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_convertTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_convertTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_convertTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_convertTaskPtr
newCallback_ZGOPTaskOutage_convertTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_convertTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_convertTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_convertTaskPtr
newCallback_ZGOPTaskOutage_convertTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_convertTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_sendSMS.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_sendSMS.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_sendSMS : public Callback_ZGOPTaskOutage_sendSMS_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_sendSMS(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_sendSMS(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_sendSMS.
 */
template<class T> Callback_ZGOPTaskOutage_sendSMSPtr
newCallback_ZGOPTaskOutage_sendSMS(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_sendSMS<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_sendSMS.
 */
template<class T> Callback_ZGOPTaskOutage_sendSMSPtr
newCallback_ZGOPTaskOutage_sendSMS(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_sendSMS<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_sendSMS.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_sendSMS.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_sendSMS : public Callback_ZGOPTaskOutage_sendSMS_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_sendSMS(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_sendSMS(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_sendSMS.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_sendSMSPtr
newCallback_ZGOPTaskOutage_sendSMS(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_sendSMS<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_sendSMS.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_sendSMSPtr
newCallback_ZGOPTaskOutage_sendSMS(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_sendSMS<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_changePhone.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_changePhone.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_changePhone : public Callback_ZGOPTaskOutage_changePhone_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_changePhone(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_changePhone(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_changePhone.
 */
template<class T> Callback_ZGOPTaskOutage_changePhonePtr
newCallback_ZGOPTaskOutage_changePhone(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_changePhone<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_changePhone.
 */
template<class T> Callback_ZGOPTaskOutage_changePhonePtr
newCallback_ZGOPTaskOutage_changePhone(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_changePhone<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_changePhone.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_changePhone.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_changePhone : public Callback_ZGOPTaskOutage_changePhone_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_changePhone(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_changePhone(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_changePhone.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_changePhonePtr
newCallback_ZGOPTaskOutage_changePhone(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_changePhone<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_changePhone.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_changePhonePtr
newCallback_ZGOPTaskOutage_changePhone(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_changePhone<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolator.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockIsolator.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_lockIsolator : public Callback_ZGOPTaskOutage_lockIsolator_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_lockIsolator(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_lockIsolator(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolator.
 */
template<class T> Callback_ZGOPTaskOutage_lockIsolatorPtr
newCallback_ZGOPTaskOutage_lockIsolator(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_lockIsolator<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolator.
 */
template<class T> Callback_ZGOPTaskOutage_lockIsolatorPtr
newCallback_ZGOPTaskOutage_lockIsolator(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_lockIsolator<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolator.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockIsolator.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_lockIsolator : public Callback_ZGOPTaskOutage_lockIsolator_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_lockIsolator(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_lockIsolator(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolator.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_lockIsolatorPtr
newCallback_ZGOPTaskOutage_lockIsolator(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_lockIsolator<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolator.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_lockIsolatorPtr
newCallback_ZGOPTaskOutage_lockIsolator(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_lockIsolator<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolator.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockIsolator.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_unlockIsolator : public Callback_ZGOPTaskOutage_unlockIsolator_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_unlockIsolator(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockIsolator(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolator.
 */
template<class T> Callback_ZGOPTaskOutage_unlockIsolatorPtr
newCallback_ZGOPTaskOutage_unlockIsolator(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockIsolator<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolator.
 */
template<class T> Callback_ZGOPTaskOutage_unlockIsolatorPtr
newCallback_ZGOPTaskOutage_unlockIsolator(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockIsolator<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolator.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockIsolator.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_unlockIsolator : public Callback_ZGOPTaskOutage_unlockIsolator_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_unlockIsolator(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockIsolator(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolator.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockIsolatorPtr
newCallback_ZGOPTaskOutage_unlockIsolator(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockIsolator<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolator.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockIsolatorPtr
newCallback_ZGOPTaskOutage_unlockIsolator(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockIsolator<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolatorBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockIsolatorBatch.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_lockIsolatorBatch : public Callback_ZGOPTaskOutage_lockIsolatorBatch_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_lockIsolatorBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_lockIsolatorBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolatorBatch.
 */
template<class T> Callback_ZGOPTaskOutage_lockIsolatorBatchPtr
newCallback_ZGOPTaskOutage_lockIsolatorBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_lockIsolatorBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolatorBatch.
 */
template<class T> Callback_ZGOPTaskOutage_lockIsolatorBatchPtr
newCallback_ZGOPTaskOutage_lockIsolatorBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_lockIsolatorBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolatorBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockIsolatorBatch.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_lockIsolatorBatch : public Callback_ZGOPTaskOutage_lockIsolatorBatch_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_lockIsolatorBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_lockIsolatorBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolatorBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_lockIsolatorBatchPtr
newCallback_ZGOPTaskOutage_lockIsolatorBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_lockIsolatorBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockIsolatorBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_lockIsolatorBatchPtr
newCallback_ZGOPTaskOutage_lockIsolatorBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_lockIsolatorBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolatorBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockIsolatorBatch.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_unlockIsolatorBatch : public Callback_ZGOPTaskOutage_unlockIsolatorBatch_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_unlockIsolatorBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockIsolatorBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolatorBatch.
 */
template<class T> Callback_ZGOPTaskOutage_unlockIsolatorBatchPtr
newCallback_ZGOPTaskOutage_unlockIsolatorBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockIsolatorBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolatorBatch.
 */
template<class T> Callback_ZGOPTaskOutage_unlockIsolatorBatchPtr
newCallback_ZGOPTaskOutage_unlockIsolatorBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockIsolatorBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolatorBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockIsolatorBatch.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_unlockIsolatorBatch : public Callback_ZGOPTaskOutage_unlockIsolatorBatch_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_unlockIsolatorBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockIsolatorBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolatorBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockIsolatorBatchPtr
newCallback_ZGOPTaskOutage_unlockIsolatorBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockIsolatorBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockIsolatorBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockIsolatorBatchPtr
newCallback_ZGOPTaskOutage_unlockIsolatorBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockIsolatorBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockSwitch.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_lockSwitch : public Callback_ZGOPTaskOutage_lockSwitch_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_lockSwitch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_lockSwitch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitch.
 */
template<class T> Callback_ZGOPTaskOutage_lockSwitchPtr
newCallback_ZGOPTaskOutage_lockSwitch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_lockSwitch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitch.
 */
template<class T> Callback_ZGOPTaskOutage_lockSwitchPtr
newCallback_ZGOPTaskOutage_lockSwitch(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_lockSwitch<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockSwitch.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_lockSwitch : public Callback_ZGOPTaskOutage_lockSwitch_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_lockSwitch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_lockSwitch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_lockSwitchPtr
newCallback_ZGOPTaskOutage_lockSwitch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_lockSwitch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_lockSwitchPtr
newCallback_ZGOPTaskOutage_lockSwitch(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_lockSwitch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockSwitch.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_unlockSwitch : public Callback_ZGOPTaskOutage_unlockSwitch_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_unlockSwitch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockSwitch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitch.
 */
template<class T> Callback_ZGOPTaskOutage_unlockSwitchPtr
newCallback_ZGOPTaskOutage_unlockSwitch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockSwitch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitch.
 */
template<class T> Callback_ZGOPTaskOutage_unlockSwitchPtr
newCallback_ZGOPTaskOutage_unlockSwitch(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockSwitch<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockSwitch.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_unlockSwitch : public Callback_ZGOPTaskOutage_unlockSwitch_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_unlockSwitch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockSwitch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockSwitchPtr
newCallback_ZGOPTaskOutage_unlockSwitch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockSwitch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockSwitchPtr
newCallback_ZGOPTaskOutage_unlockSwitch(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockSwitch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockSwitchBatch.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_lockSwitchBatch : public Callback_ZGOPTaskOutage_lockSwitchBatch_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_lockSwitchBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_lockSwitchBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitchBatch.
 */
template<class T> Callback_ZGOPTaskOutage_lockSwitchBatchPtr
newCallback_ZGOPTaskOutage_lockSwitchBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_lockSwitchBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitchBatch.
 */
template<class T> Callback_ZGOPTaskOutage_lockSwitchBatchPtr
newCallback_ZGOPTaskOutage_lockSwitchBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_lockSwitchBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockSwitchBatch.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_lockSwitchBatch : public Callback_ZGOPTaskOutage_lockSwitchBatch_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_lockSwitchBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_lockSwitchBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitchBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_lockSwitchBatchPtr
newCallback_ZGOPTaskOutage_lockSwitchBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_lockSwitchBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockSwitchBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_lockSwitchBatchPtr
newCallback_ZGOPTaskOutage_lockSwitchBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_lockSwitchBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockSwitchBatch.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_unlockSwitchBatch : public Callback_ZGOPTaskOutage_unlockSwitchBatch_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_unlockSwitchBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockSwitchBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitchBatch.
 */
template<class T> Callback_ZGOPTaskOutage_unlockSwitchBatchPtr
newCallback_ZGOPTaskOutage_unlockSwitchBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockSwitchBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitchBatch.
 */
template<class T> Callback_ZGOPTaskOutage_unlockSwitchBatchPtr
newCallback_ZGOPTaskOutage_unlockSwitchBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockSwitchBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockSwitchBatch.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_unlockSwitchBatch : public Callback_ZGOPTaskOutage_unlockSwitchBatch_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_unlockSwitchBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockSwitchBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitchBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockSwitchBatchPtr
newCallback_ZGOPTaskOutage_unlockSwitchBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockSwitchBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockSwitchBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockSwitchBatchPtr
newCallback_ZGOPTaskOutage_unlockSwitchBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockSwitchBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_closeSwitch.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_closeSwitch : public Callback_ZGOPTaskOutage_closeSwitch_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_closeSwitch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_closeSwitch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitch.
 */
template<class T> Callback_ZGOPTaskOutage_closeSwitchPtr
newCallback_ZGOPTaskOutage_closeSwitch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_closeSwitch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitch.
 */
template<class T> Callback_ZGOPTaskOutage_closeSwitchPtr
newCallback_ZGOPTaskOutage_closeSwitch(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_closeSwitch<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_closeSwitch.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_closeSwitch : public Callback_ZGOPTaskOutage_closeSwitch_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_closeSwitch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_closeSwitch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_closeSwitchPtr
newCallback_ZGOPTaskOutage_closeSwitch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_closeSwitch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_closeSwitchPtr
newCallback_ZGOPTaskOutage_closeSwitch(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_closeSwitch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_openSwitch.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_openSwitch : public Callback_ZGOPTaskOutage_openSwitch_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_openSwitch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_openSwitch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitch.
 */
template<class T> Callback_ZGOPTaskOutage_openSwitchPtr
newCallback_ZGOPTaskOutage_openSwitch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_openSwitch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitch.
 */
template<class T> Callback_ZGOPTaskOutage_openSwitchPtr
newCallback_ZGOPTaskOutage_openSwitch(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_openSwitch<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_openSwitch.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_openSwitch : public Callback_ZGOPTaskOutage_openSwitch_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_openSwitch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_openSwitch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_openSwitchPtr
newCallback_ZGOPTaskOutage_openSwitch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_openSwitch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_openSwitchPtr
newCallback_ZGOPTaskOutage_openSwitch(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_openSwitch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_closeSwitchBatch.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_closeSwitchBatch : public Callback_ZGOPTaskOutage_closeSwitchBatch_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_closeSwitchBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_closeSwitchBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitchBatch.
 */
template<class T> Callback_ZGOPTaskOutage_closeSwitchBatchPtr
newCallback_ZGOPTaskOutage_closeSwitchBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_closeSwitchBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitchBatch.
 */
template<class T> Callback_ZGOPTaskOutage_closeSwitchBatchPtr
newCallback_ZGOPTaskOutage_closeSwitchBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_closeSwitchBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_closeSwitchBatch.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_closeSwitchBatch : public Callback_ZGOPTaskOutage_closeSwitchBatch_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_closeSwitchBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_closeSwitchBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitchBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_closeSwitchBatchPtr
newCallback_ZGOPTaskOutage_closeSwitchBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_closeSwitchBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_closeSwitchBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_closeSwitchBatchPtr
newCallback_ZGOPTaskOutage_closeSwitchBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_closeSwitchBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_openSwitchBatch.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_openSwitchBatch : public Callback_ZGOPTaskOutage_openSwitchBatch_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_openSwitchBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_openSwitchBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitchBatch.
 */
template<class T> Callback_ZGOPTaskOutage_openSwitchBatchPtr
newCallback_ZGOPTaskOutage_openSwitchBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_openSwitchBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitchBatch.
 */
template<class T> Callback_ZGOPTaskOutage_openSwitchBatchPtr
newCallback_ZGOPTaskOutage_openSwitchBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_openSwitchBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitchBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_openSwitchBatch.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_openSwitchBatch : public Callback_ZGOPTaskOutage_openSwitchBatch_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_openSwitchBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_openSwitchBatch(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitchBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_openSwitchBatchPtr
newCallback_ZGOPTaskOutage_openSwitchBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_openSwitchBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_openSwitchBatch.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_openSwitchBatchPtr
newCallback_ZGOPTaskOutage_openSwitchBatch(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_openSwitchBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockTask.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_lockTask : public Callback_ZGOPTaskOutage_lockTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_lockTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_lockTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockTask.
 */
template<class T> Callback_ZGOPTaskOutage_lockTaskPtr
newCallback_ZGOPTaskOutage_lockTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_lockTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockTask.
 */
template<class T> Callback_ZGOPTaskOutage_lockTaskPtr
newCallback_ZGOPTaskOutage_lockTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_lockTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_lockTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_lockTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_lockTask : public Callback_ZGOPTaskOutage_lockTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_lockTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_lockTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_lockTaskPtr
newCallback_ZGOPTaskOutage_lockTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_lockTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_lockTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_lockTaskPtr
newCallback_ZGOPTaskOutage_lockTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_lockTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockTask.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_unlockTask : public Callback_ZGOPTaskOutage_unlockTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_unlockTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockTask.
 */
template<class T> Callback_ZGOPTaskOutage_unlockTaskPtr
newCallback_ZGOPTaskOutage_unlockTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockTask.
 */
template<class T> Callback_ZGOPTaskOutage_unlockTaskPtr
newCallback_ZGOPTaskOutage_unlockTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_unlockTask : public Callback_ZGOPTaskOutage_unlockTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_unlockTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockTaskPtr
newCallback_ZGOPTaskOutage_unlockTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockTaskPtr
newCallback_ZGOPTaskOutage_unlockTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_applyPTW.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_applyPTW.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_applyPTW : public Callback_ZGOPTaskOutage_applyPTW_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_applyPTW(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_applyPTW(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_applyPTW.
 */
template<class T> Callback_ZGOPTaskOutage_applyPTWPtr
newCallback_ZGOPTaskOutage_applyPTW(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_applyPTW<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_applyPTW.
 */
template<class T> Callback_ZGOPTaskOutage_applyPTWPtr
newCallback_ZGOPTaskOutage_applyPTW(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_applyPTW<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_applyPTW.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_applyPTW.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_applyPTW : public Callback_ZGOPTaskOutage_applyPTW_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_applyPTW(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_applyPTW(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_applyPTW.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_applyPTWPtr
newCallback_ZGOPTaskOutage_applyPTW(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_applyPTW<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_applyPTW.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_applyPTWPtr
newCallback_ZGOPTaskOutage_applyPTW(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_applyPTW<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelPTW.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_cancelPTW.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_cancelPTW : public Callback_ZGOPTaskOutage_cancelPTW_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_cancelPTW(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_cancelPTW(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelPTW.
 */
template<class T> Callback_ZGOPTaskOutage_cancelPTWPtr
newCallback_ZGOPTaskOutage_cancelPTW(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_cancelPTW<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelPTW.
 */
template<class T> Callback_ZGOPTaskOutage_cancelPTWPtr
newCallback_ZGOPTaskOutage_cancelPTW(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_cancelPTW<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelPTW.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_cancelPTW.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_cancelPTW : public Callback_ZGOPTaskOutage_cancelPTW_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_cancelPTW(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_cancelPTW(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelPTW.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_cancelPTWPtr
newCallback_ZGOPTaskOutage_cancelPTW(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_cancelPTW<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelPTW.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_cancelPTWPtr
newCallback_ZGOPTaskOutage_cancelPTW(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_cancelPTW<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_applySFT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_applySFT.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_applySFT : public Callback_ZGOPTaskOutage_applySFT_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_applySFT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_applySFT(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_applySFT.
 */
template<class T> Callback_ZGOPTaskOutage_applySFTPtr
newCallback_ZGOPTaskOutage_applySFT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_applySFT<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_applySFT.
 */
template<class T> Callback_ZGOPTaskOutage_applySFTPtr
newCallback_ZGOPTaskOutage_applySFT(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_applySFT<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_applySFT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_applySFT.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_applySFT : public Callback_ZGOPTaskOutage_applySFT_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_applySFT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_applySFT(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_applySFT.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_applySFTPtr
newCallback_ZGOPTaskOutage_applySFT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_applySFT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_applySFT.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_applySFTPtr
newCallback_ZGOPTaskOutage_applySFT(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_applySFT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelSFT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_cancelSFT.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_cancelSFT : public Callback_ZGOPTaskOutage_cancelSFT_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_cancelSFT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_cancelSFT(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelSFT.
 */
template<class T> Callback_ZGOPTaskOutage_cancelSFTPtr
newCallback_ZGOPTaskOutage_cancelSFT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_cancelSFT<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelSFT.
 */
template<class T> Callback_ZGOPTaskOutage_cancelSFTPtr
newCallback_ZGOPTaskOutage_cancelSFT(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_cancelSFT<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelSFT.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_cancelSFT.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_cancelSFT : public Callback_ZGOPTaskOutage_cancelSFT_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_cancelSFT(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_cancelSFT(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelSFT.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_cancelSFTPtr
newCallback_ZGOPTaskOutage_cancelSFT(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_cancelSFT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_cancelSFT.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_cancelSFTPtr
newCallback_ZGOPTaskOutage_cancelSFT(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_cancelSFT<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_saveOTP.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_saveOTP.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_saveOTP : public Callback_ZGOPTaskOutage_saveOTP_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_saveOTP(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_saveOTP(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_saveOTP.
 */
template<class T> Callback_ZGOPTaskOutage_saveOTPPtr
newCallback_ZGOPTaskOutage_saveOTP(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_saveOTP<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_saveOTP.
 */
template<class T> Callback_ZGOPTaskOutage_saveOTPPtr
newCallback_ZGOPTaskOutage_saveOTP(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_saveOTP<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_saveOTP.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_saveOTP.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_saveOTP : public Callback_ZGOPTaskOutage_saveOTP_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_saveOTP(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_saveOTP(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_saveOTP.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_saveOTPPtr
newCallback_ZGOPTaskOutage_saveOTP(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_saveOTP<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_saveOTP.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_saveOTPPtr
newCallback_ZGOPTaskOutage_saveOTP(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_saveOTP<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_confirmOutage.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_confirmOutage.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_confirmOutage : public Callback_ZGOPTaskOutage_confirmOutage_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_confirmOutage(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmOutage(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_confirmOutage.
 */
template<class T> Callback_ZGOPTaskOutage_confirmOutagePtr
newCallback_ZGOPTaskOutage_confirmOutage(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_confirmOutage<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_confirmOutage.
 */
template<class T> Callback_ZGOPTaskOutage_confirmOutagePtr
newCallback_ZGOPTaskOutage_confirmOutage(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_confirmOutage<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_confirmOutage.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_confirmOutage.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_confirmOutage : public Callback_ZGOPTaskOutage_confirmOutage_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_confirmOutage(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_confirmOutage(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_confirmOutage.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_confirmOutagePtr
newCallback_ZGOPTaskOutage_confirmOutage(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_confirmOutage<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_confirmOutage.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_confirmOutagePtr
newCallback_ZGOPTaskOutage_confirmOutage(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_confirmOutage<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_saveEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_saveEvent.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_saveEvent : public Callback_ZGOPTaskOutage_saveEvent_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_saveEvent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_saveEvent(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_saveEvent.
 */
template<class T> Callback_ZGOPTaskOutage_saveEventPtr
newCallback_ZGOPTaskOutage_saveEvent(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_saveEvent<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_saveEvent.
 */
template<class T> Callback_ZGOPTaskOutage_saveEventPtr
newCallback_ZGOPTaskOutage_saveEvent(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_saveEvent<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_saveEvent.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_saveEvent.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_saveEvent : public Callback_ZGOPTaskOutage_saveEvent_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_saveEvent(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_saveEvent(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_saveEvent.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_saveEventPtr
newCallback_ZGOPTaskOutage_saveEvent(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_saveEvent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_saveEvent.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_saveEventPtr
newCallback_ZGOPTaskOutage_saveEvent(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_saveEvent<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getMonitorDevices.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getMonitorDevices.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_getMonitorDevices : public Callback_ZGOPTaskOutage_getMonitorDevices_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_getMonitorDevices(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listDevices;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getMonitorDevices(iceP_listDevices, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listDevices, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getMonitorDevices.
 */
template<class T> Callback_ZGOPTaskOutage_getMonitorDevicesPtr
newCallback_ZGOPTaskOutage_getMonitorDevices(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_getMonitorDevices<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getMonitorDevices.
 */
template<class T> Callback_ZGOPTaskOutage_getMonitorDevicesPtr
newCallback_ZGOPTaskOutage_getMonitorDevices(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_getMonitorDevices<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getMonitorDevices.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getMonitorDevices.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_getMonitorDevices : public Callback_ZGOPTaskOutage_getMonitorDevices_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_getMonitorDevices(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listDevices;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getMonitorDevices(iceP_listDevices, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listDevices, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getMonitorDevices.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_getMonitorDevicesPtr
newCallback_ZGOPTaskOutage_getMonitorDevices(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_getMonitorDevices<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getMonitorDevices.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_getMonitorDevicesPtr
newCallback_ZGOPTaskOutage_getMonitorDevices(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_getMonitorDevices<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_setLockDevicePassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_setLockDevicePassword.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_setLockDevicePassword : public Callback_ZGOPTaskOutage_setLockDevicePassword_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_setLockDevicePassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setLockDevicePassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_setLockDevicePassword.
 */
template<class T> Callback_ZGOPTaskOutage_setLockDevicePasswordPtr
newCallback_ZGOPTaskOutage_setLockDevicePassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_setLockDevicePassword<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_setLockDevicePassword.
 */
template<class T> Callback_ZGOPTaskOutage_setLockDevicePasswordPtr
newCallback_ZGOPTaskOutage_setLockDevicePassword(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_setLockDevicePassword<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_setLockDevicePassword.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_setLockDevicePassword.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_setLockDevicePassword : public Callback_ZGOPTaskOutage_setLockDevicePassword_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_setLockDevicePassword(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_setLockDevicePassword(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_setLockDevicePassword.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_setLockDevicePasswordPtr
newCallback_ZGOPTaskOutage_setLockDevicePassword(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_setLockDevicePassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_setLockDevicePassword.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_setLockDevicePasswordPtr
newCallback_ZGOPTaskOutage_setLockDevicePassword(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_setLockDevicePassword<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockExternalLock.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockExternalLock.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_unlockExternalLock : public Callback_ZGOPTaskOutage_unlockExternalLock_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_unlockExternalLock(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockExternalLock(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockExternalLock.
 */
template<class T> Callback_ZGOPTaskOutage_unlockExternalLockPtr
newCallback_ZGOPTaskOutage_unlockExternalLock(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockExternalLock<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockExternalLock.
 */
template<class T> Callback_ZGOPTaskOutage_unlockExternalLockPtr
newCallback_ZGOPTaskOutage_unlockExternalLock(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_unlockExternalLock<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockExternalLock.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_unlockExternalLock.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_unlockExternalLock : public Callback_ZGOPTaskOutage_unlockExternalLock_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_unlockExternalLock(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_unlockExternalLock(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockExternalLock.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockExternalLockPtr
newCallback_ZGOPTaskOutage_unlockExternalLock(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockExternalLock<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_unlockExternalLock.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_unlockExternalLockPtr
newCallback_ZGOPTaskOutage_unlockExternalLock(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_unlockExternalLock<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_isExternalLockEnable.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_isExternalLockEnable.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_isExternalLockEnable : public Callback_ZGOPTaskOutage_isExternalLockEnable_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_isExternalLockEnable(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        StringMap iceP_deviceEnable;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isExternalLockEnable(iceP_deviceEnable, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_deviceEnable, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_isExternalLockEnable.
 */
template<class T> Callback_ZGOPTaskOutage_isExternalLockEnablePtr
newCallback_ZGOPTaskOutage_isExternalLockEnable(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_isExternalLockEnable<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_isExternalLockEnable.
 */
template<class T> Callback_ZGOPTaskOutage_isExternalLockEnablePtr
newCallback_ZGOPTaskOutage_isExternalLockEnable(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_isExternalLockEnable<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_isExternalLockEnable.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_isExternalLockEnable.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_isExternalLockEnable : public Callback_ZGOPTaskOutage_isExternalLockEnable_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_isExternalLockEnable(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        StringMap iceP_deviceEnable;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_isExternalLockEnable(iceP_deviceEnable, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_deviceEnable, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_isExternalLockEnable.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_isExternalLockEnablePtr
newCallback_ZGOPTaskOutage_isExternalLockEnable(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_isExternalLockEnable<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_isExternalLockEnable.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_isExternalLockEnablePtr
newCallback_ZGOPTaskOutage_isExternalLockEnable(T* instance, void (T::*cb)(bool, const StringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_isExternalLockEnable<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getSwitchCloseConditions.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getSwitchCloseConditions.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_getSwitchCloseConditions : public Callback_ZGOPTaskOutage_getSwitchCloseConditions_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_getSwitchCloseConditions(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_conditions;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getSwitchCloseConditions(iceP_conditions, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_conditions, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getSwitchCloseConditions.
 */
template<class T> Callback_ZGOPTaskOutage_getSwitchCloseConditionsPtr
newCallback_ZGOPTaskOutage_getSwitchCloseConditions(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_getSwitchCloseConditions<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getSwitchCloseConditions.
 */
template<class T> Callback_ZGOPTaskOutage_getSwitchCloseConditionsPtr
newCallback_ZGOPTaskOutage_getSwitchCloseConditions(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_getSwitchCloseConditions<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getSwitchCloseConditions.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getSwitchCloseConditions.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_getSwitchCloseConditions : public Callback_ZGOPTaskOutage_getSwitchCloseConditions_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_getSwitchCloseConditions(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_conditions;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getSwitchCloseConditions(iceP_conditions, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_conditions, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getSwitchCloseConditions.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_getSwitchCloseConditionsPtr
newCallback_ZGOPTaskOutage_getSwitchCloseConditions(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_getSwitchCloseConditions<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getSwitchCloseConditions.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_getSwitchCloseConditionsPtr
newCallback_ZGOPTaskOutage_getSwitchCloseConditions(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_getSwitchCloseConditions<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_checkSwitchCloseConditions.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_checkSwitchCloseConditions.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_checkSwitchCloseConditions : public Callback_ZGOPTaskOutage_checkSwitchCloseConditions_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_checkSwitchCloseConditions(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        bool iceP_success;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_checkSwitchCloseConditions(iceP_success, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_success, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_checkSwitchCloseConditions.
 */
template<class T> Callback_ZGOPTaskOutage_checkSwitchCloseConditionsPtr
newCallback_ZGOPTaskOutage_checkSwitchCloseConditions(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_checkSwitchCloseConditions<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_checkSwitchCloseConditions.
 */
template<class T> Callback_ZGOPTaskOutage_checkSwitchCloseConditionsPtr
newCallback_ZGOPTaskOutage_checkSwitchCloseConditions(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_checkSwitchCloseConditions<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_checkSwitchCloseConditions.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_checkSwitchCloseConditions.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_checkSwitchCloseConditions : public Callback_ZGOPTaskOutage_checkSwitchCloseConditions_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_checkSwitchCloseConditions(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        bool iceP_success;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_checkSwitchCloseConditions(iceP_success, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_success, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_checkSwitchCloseConditions.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_checkSwitchCloseConditionsPtr
newCallback_ZGOPTaskOutage_checkSwitchCloseConditions(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_checkSwitchCloseConditions<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_checkSwitchCloseConditions.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_checkSwitchCloseConditionsPtr
newCallback_ZGOPTaskOutage_checkSwitchCloseConditions(T* instance, void (T::*cb)(bool, bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_checkSwitchCloseConditions<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_deleteTypicalTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_deleteTypicalTask.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_deleteTypicalTask : public Callback_ZGOPTaskOutage_deleteTypicalTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_deleteTypicalTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteTypicalTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_deleteTypicalTask.
 */
template<class T> Callback_ZGOPTaskOutage_deleteTypicalTaskPtr
newCallback_ZGOPTaskOutage_deleteTypicalTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_deleteTypicalTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_deleteTypicalTask.
 */
template<class T> Callback_ZGOPTaskOutage_deleteTypicalTaskPtr
newCallback_ZGOPTaskOutage_deleteTypicalTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_deleteTypicalTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_deleteTypicalTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_deleteTypicalTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_deleteTypicalTask : public Callback_ZGOPTaskOutage_deleteTypicalTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_deleteTypicalTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_deleteTypicalTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_deleteTypicalTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_deleteTypicalTaskPtr
newCallback_ZGOPTaskOutage_deleteTypicalTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_deleteTypicalTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_deleteTypicalTask.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_deleteTypicalTaskPtr
newCallback_ZGOPTaskOutage_deleteTypicalTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_deleteTypicalTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getDevicesBoundaryType.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getDevicesBoundaryType.
 */
template<class T>
class CallbackNC_ZGOPTaskOutage_getDevicesBoundaryType : public Callback_ZGOPTaskOutage_getDevicesBoundaryType_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskOutage_getDevicesBoundaryType(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        bool iceP_isValidRegion;
        ListStringMap iceP_listOutputDevice;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDevicesBoundaryType(iceP_isValidRegion, iceP_listOutputDevice, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_isValidRegion, iceP_listOutputDevice, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getDevicesBoundaryType.
 */
template<class T> Callback_ZGOPTaskOutage_getDevicesBoundaryTypePtr
newCallback_ZGOPTaskOutage_getDevicesBoundaryType(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_getDevicesBoundaryType<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getDevicesBoundaryType.
 */
template<class T> Callback_ZGOPTaskOutage_getDevicesBoundaryTypePtr
newCallback_ZGOPTaskOutage_getDevicesBoundaryType(T* instance, void (T::*cb)(bool, bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskOutage_getDevicesBoundaryType<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskOutage::begin_getDevicesBoundaryType.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskOutage_getDevicesBoundaryType.
 */
template<class T, typename CT>
class Callback_ZGOPTaskOutage_getDevicesBoundaryType : public Callback_ZGOPTaskOutage_getDevicesBoundaryType_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskOutage_getDevicesBoundaryType(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskOutagePrx proxy = ZGOPTaskOutagePrx::uncheckedCast(result->getProxy());
        bool iceP_isValidRegion;
        ListStringMap iceP_listOutputDevice;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDevicesBoundaryType(iceP_isValidRegion, iceP_listOutputDevice, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_isValidRegion, iceP_listOutputDevice, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getDevicesBoundaryType.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_getDevicesBoundaryTypePtr
newCallback_ZGOPTaskOutage_getDevicesBoundaryType(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_getDevicesBoundaryType<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskOutage::begin_getDevicesBoundaryType.
 */
template<class T, typename CT> Callback_ZGOPTaskOutage_getDevicesBoundaryTypePtr
newCallback_ZGOPTaskOutage_getDevicesBoundaryType(T* instance, void (T::*cb)(bool, bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskOutage_getDevicesBoundaryType<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

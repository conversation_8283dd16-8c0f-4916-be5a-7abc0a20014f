//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGSTStrayDevice.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGSTStrayDevice_h__
#define __ZGSTStrayDevice_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGSTStrayDevice;
class ZGSTStrayDevicePrx;

}

namespace ZG6000
{

class ZGSTStrayDevice : public virtual ZGServerBase
{
public:

    using ProxyType = ZGSTStrayDevicePrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getAllStations.
     */
    struct GetAllStationsResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ListStringMap listMapStation;
        ErrorInfo e;
    };

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	装置获取所有站点
     */
    virtual bool getAllStations(ListStringMap& listMapStation, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getAllStations(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getSensorByStation.
     */
    struct GetSensorByStationResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ListStringMap listMapSensor;
        ErrorInfo e;
    };

    /**
     * @param stationID 站点ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取站点的传感器信息
     */
    virtual bool getSensorByStation(::std::string stationID, ListStringMap& listMapSensor, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getSensorByStation(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDataBySensor.
     */
    struct GetDataBySensorResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ListStringMap listData;
        ErrorInfo e;
    };

    /**
     * @param sensorID 传感器ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取传感器的数据
     */
    virtual bool getDataBySensor(::std::string sensorID, ListStringMap& listData, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDataBySensor(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getDataBySensors.
     */
    struct GetDataBySensorsResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ListStringMap listData;
        ErrorInfo e;
    };

    /**
     * @param listSensor 传感器ID列表
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取多个传感器的数据
     */
    virtual bool getDataBySensors(StringList listSensor, ListStringMap& listData, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getDataBySensors(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to sendYs.
     */
    struct SendYsResult
    {
        /** 执行成功返回true，失败返回false。 */
        bool returnValue;
        ErrorInfo e;
    };

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	发送遥设命令
     */
    virtual bool sendYs(::std::string clientID, ::std::string deviceID, ::std::string propertyName, ::std::string propertyValue, bool automatic, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_sendYs(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void resetCalculation(const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_resetCalculation(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGSTStrayDevicePrx : public virtual ::Ice::Proxy<ZGSTStrayDevicePrx, ZGServerBasePrx>
{
public:

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	装置获取所有站点
     */
    bool getAllStations(ListStringMap& listMapStation, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStrayDevice::GetAllStationsResult>(true, this, &ZGSTStrayDevicePrx::_iceI_getAllStations, context).get();
        listMapStation = ::std::move(_result.listMapStation);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	装置获取所有站点
     */
    template<template<typename> class P = ::std::promise>
    auto getAllStationsAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStrayDevice::GetAllStationsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStrayDevice::GetAllStationsResult, P>(false, this, &ZGSTStrayDevicePrx::_iceI_getAllStations, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	装置获取所有站点
     */
    ::std::function<void()>
    getAllStationsAsync(::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStrayDevice::GetAllStationsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listMapStation), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStrayDevice::GetAllStationsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStrayDevicePrx::_iceI_getAllStations, context);
    }

    /// \cond INTERNAL
    void _iceI_getAllStations(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStrayDevice::GetAllStationsResult>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param stationID 站点ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取站点的传感器信息
     */
    bool getSensorByStation(const ::std::string& stationID, ListStringMap& listMapSensor, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStrayDevice::GetSensorByStationResult>(true, this, &ZGSTStrayDevicePrx::_iceI_getSensorByStation, stationID, context).get();
        listMapSensor = ::std::move(_result.listMapSensor);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param stationID 站点ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	获取站点的传感器信息
     */
    template<template<typename> class P = ::std::promise>
    auto getSensorByStationAsync(const ::std::string& stationID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStrayDevice::GetSensorByStationResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStrayDevice::GetSensorByStationResult, P>(false, this, &ZGSTStrayDevicePrx::_iceI_getSensorByStation, stationID, context);
    }

    /**
     * @param stationID 站点ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	获取站点的传感器信息
     */
    ::std::function<void()>
    getSensorByStationAsync(const ::std::string& stationID,
                            ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStrayDevice::GetSensorByStationResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listMapSensor), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStrayDevice::GetSensorByStationResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStrayDevicePrx::_iceI_getSensorByStation, stationID, context);
    }

    /// \cond INTERNAL
    void _iceI_getSensorByStation(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStrayDevice::GetSensorByStationResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param sensorID 传感器ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取传感器的数据
     */
    bool getDataBySensor(const ::std::string& sensorID, ListStringMap& listData, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStrayDevice::GetDataBySensorResult>(true, this, &ZGSTStrayDevicePrx::_iceI_getDataBySensor, sensorID, context).get();
        listData = ::std::move(_result.listData);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param sensorID 传感器ID
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	获取传感器的数据
     */
    template<template<typename> class P = ::std::promise>
    auto getDataBySensorAsync(const ::std::string& sensorID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStrayDevice::GetDataBySensorResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStrayDevice::GetDataBySensorResult, P>(false, this, &ZGSTStrayDevicePrx::_iceI_getDataBySensor, sensorID, context);
    }

    /**
     * @param sensorID 传感器ID
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	获取传感器的数据
     */
    ::std::function<void()>
    getDataBySensorAsync(const ::std::string& sensorID,
                         ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                         ::std::function<void(::std::exception_ptr)> ex = nullptr,
                         ::std::function<void(bool)> sent = nullptr,
                         const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStrayDevice::GetDataBySensorResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listData), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStrayDevice::GetDataBySensorResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStrayDevicePrx::_iceI_getDataBySensor, sensorID, context);
    }

    /// \cond INTERNAL
    void _iceI_getDataBySensor(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStrayDevice::GetDataBySensorResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param listSensor 传感器ID列表
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取多个传感器的数据
     */
    bool getDataBySensors(const StringList& listSensor, ListStringMap& listData, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStrayDevice::GetDataBySensorsResult>(true, this, &ZGSTStrayDevicePrx::_iceI_getDataBySensors, listSensor, context).get();
        listData = ::std::move(_result.listData);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param listSensor 传感器ID列表
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	获取多个传感器的数据
     */
    template<template<typename> class P = ::std::promise>
    auto getDataBySensorsAsync(const StringList& listSensor, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStrayDevice::GetDataBySensorsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStrayDevice::GetDataBySensorsResult, P>(false, this, &ZGSTStrayDevicePrx::_iceI_getDataBySensors, listSensor, context);
    }

    /**
     * @param listSensor 传感器ID列表
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	获取多个传感器的数据
     */
    ::std::function<void()>
    getDataBySensorsAsync(const StringList& listSensor,
                          ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStrayDevice::GetDataBySensorsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listData), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStrayDevice::GetDataBySensorsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStrayDevicePrx::_iceI_getDataBySensors, listSensor, context);
    }

    /// \cond INTERNAL
    void _iceI_getDataBySensors(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStrayDevice::GetDataBySensorsResult>>&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	发送遥设命令
     */
    bool sendYs(const ::std::string& clientID, const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, bool automatic, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGSTStrayDevice::SendYsResult>(true, this, &ZGSTStrayDevicePrx::_iceI_sendYs, clientID, deviceID, propertyName, propertyValue, automatic, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The future object for the invocation.
     * @brief	发送遥设命令
     */
    template<template<typename> class P = ::std::promise>
    auto sendYsAsync(const ::std::string& clientID, const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, bool automatic, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGSTStrayDevice::SendYsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGSTStrayDevice::SendYsResult, P>(false, this, &ZGSTStrayDevicePrx::_iceI_sendYs, clientID, deviceID, propertyName, propertyValue, automatic, context);
    }

    /**
     * @param response The response callback.
     * @param ex The exception callback.
     * @param sent The sent callback.
     * @param context The Context map to send with the invocation.
     * @return A function that can be called to cancel the invocation locally.
     * @brief	发送遥设命令
     */
    ::std::function<void()>
    sendYsAsync(const ::std::string& clientID, const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, bool automatic,
                ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                ::std::function<void(::std::exception_ptr)> ex = nullptr,
                ::std::function<void(bool)> sent = nullptr,
                const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGSTStrayDevice::SendYsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGSTStrayDevice::SendYsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStrayDevicePrx::_iceI_sendYs, clientID, deviceID, propertyName, propertyValue, automatic, context);
    }

    /// \cond INTERNAL
    void _iceI_sendYs(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGSTStrayDevice::SendYsResult>>&, const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, bool, const ::Ice::Context&);
    /// \endcond

    void resetCalculation(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGSTStrayDevicePrx::_iceI_resetCalculation, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto resetCalculationAsync(const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGSTStrayDevicePrx::_iceI_resetCalculation, context);
    }

    ::std::function<void()>
    resetCalculationAsync(::std::function<void()> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGSTStrayDevicePrx::_iceI_resetCalculation, context);
    }

    /// \cond INTERNAL
    void _iceI_resetCalculation(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGSTStrayDevicePrx() = default;
    friend ::std::shared_ptr<ZGSTStrayDevicePrx> IceInternal::createProxy<ZGSTStrayDevicePrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGSTStrayDevicePtr = ::std::shared_ptr<ZGSTStrayDevice>;
using ZGSTStrayDevicePrxPtr = ::std::shared_ptr<ZGSTStrayDevicePrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGSTStrayDevice;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGSTStrayDevice>&);
::IceProxy::Ice::Object* upCast(ZGSTStrayDevice*);
/// \endcond

}

}

namespace ZG6000
{

class ZGSTStrayDevice;
/// \cond INTERNAL
::Ice::Object* upCast(ZGSTStrayDevice*);
/// \endcond
typedef ::IceInternal::Handle< ZGSTStrayDevice> ZGSTStrayDevicePtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGSTStrayDevice> ZGSTStrayDevicePrx;
typedef ZGSTStrayDevicePrx ZGSTStrayDevicePrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGSTStrayDevicePtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getAllStations.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getAllStations.
 */
class Callback_ZGSTStrayDevice_getAllStations_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStrayDevice_getAllStations_Base> Callback_ZGSTStrayDevice_getAllStationsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getSensorByStation.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getSensorByStation.
 */
class Callback_ZGSTStrayDevice_getSensorByStation_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStrayDevice_getSensorByStation_Base> Callback_ZGSTStrayDevice_getSensorByStationPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensor.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getDataBySensor.
 */
class Callback_ZGSTStrayDevice_getDataBySensor_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStrayDevice_getDataBySensor_Base> Callback_ZGSTStrayDevice_getDataBySensorPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensors.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getDataBySensors.
 */
class Callback_ZGSTStrayDevice_getDataBySensors_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStrayDevice_getDataBySensors_Base> Callback_ZGSTStrayDevice_getDataBySensorsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_sendYs.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_sendYs.
 */
class Callback_ZGSTStrayDevice_sendYs_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStrayDevice_sendYs_Base> Callback_ZGSTStrayDevice_sendYsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_resetCalculation.
 */
class Callback_ZGSTStrayDevice_resetCalculation_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGSTStrayDevice_resetCalculation_Base> Callback_ZGSTStrayDevice_resetCalculationPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGSTStrayDevice : public virtual ::Ice::Proxy<ZGSTStrayDevice, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	装置获取所有站点
     */
    bool getAllStations(::ZG6000::ListStringMap& listMapStation, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getAllStations(listMapStation, e, _iceI_begin_getAllStations(context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	装置获取所有站点
     */
    ::Ice::AsyncResultPtr begin_getAllStations(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getAllStations(context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	装置获取所有站点
     */
    ::Ice::AsyncResultPtr begin_getAllStations(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAllStations(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	装置获取所有站点
     */
    ::Ice::AsyncResultPtr begin_getAllStations(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAllStations(context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	装置获取所有站点
     */
    ::Ice::AsyncResultPtr begin_getAllStations(const ::ZG6000::Callback_ZGSTStrayDevice_getAllStationsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAllStations(::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	装置获取所有站点
     */
    ::Ice::AsyncResultPtr begin_getAllStations(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStrayDevice_getAllStationsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getAllStations(context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getAllStations.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_getAllStations(::ZG6000::ListStringMap& listMapStation, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getAllStations(::ZG6000::ListStringMap& iceP_listMapStation, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getAllStations(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param stationID 站点ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取站点的传感器信息
     */
    bool getSensorByStation(const ::std::string& stationID, ::ZG6000::ListStringMap& listMapSensor, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getSensorByStation(listMapSensor, e, _iceI_begin_getSensorByStation(stationID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param stationID 站点ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取站点的传感器信息
     */
    ::Ice::AsyncResultPtr begin_getSensorByStation(const ::std::string& stationID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getSensorByStation(stationID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param stationID 站点ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取站点的传感器信息
     */
    ::Ice::AsyncResultPtr begin_getSensorByStation(const ::std::string& stationID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSensorByStation(stationID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param stationID 站点ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取站点的传感器信息
     */
    ::Ice::AsyncResultPtr begin_getSensorByStation(const ::std::string& stationID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSensorByStation(stationID, context, cb, cookie);
    }

    /**
     * @param stationID 站点ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取站点的传感器信息
     */
    ::Ice::AsyncResultPtr begin_getSensorByStation(const ::std::string& stationID, const ::ZG6000::Callback_ZGSTStrayDevice_getSensorByStationPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSensorByStation(stationID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param stationID 站点ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取站点的传感器信息
     */
    ::Ice::AsyncResultPtr begin_getSensorByStation(const ::std::string& stationID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStrayDevice_getSensorByStationPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getSensorByStation(stationID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getSensorByStation.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_getSensorByStation(::ZG6000::ListStringMap& listMapSensor, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getSensorByStation(::ZG6000::ListStringMap& iceP_listMapSensor, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getSensorByStation(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param sensorID 传感器ID
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取传感器的数据
     */
    bool getDataBySensor(const ::std::string& sensorID, ::ZG6000::ListStringMap& listData, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDataBySensor(listData, e, _iceI_begin_getDataBySensor(sensorID, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param sensorID 传感器ID
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取传感器的数据
     */
    ::Ice::AsyncResultPtr begin_getDataBySensor(const ::std::string& sensorID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDataBySensor(sensorID, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param sensorID 传感器ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取传感器的数据
     */
    ::Ice::AsyncResultPtr begin_getDataBySensor(const ::std::string& sensorID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataBySensor(sensorID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param sensorID 传感器ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取传感器的数据
     */
    ::Ice::AsyncResultPtr begin_getDataBySensor(const ::std::string& sensorID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataBySensor(sensorID, context, cb, cookie);
    }

    /**
     * @param sensorID 传感器ID
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取传感器的数据
     */
    ::Ice::AsyncResultPtr begin_getDataBySensor(const ::std::string& sensorID, const ::ZG6000::Callback_ZGSTStrayDevice_getDataBySensorPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataBySensor(sensorID, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param sensorID 传感器ID
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取传感器的数据
     */
    ::Ice::AsyncResultPtr begin_getDataBySensor(const ::std::string& sensorID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStrayDevice_getDataBySensorPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataBySensor(sensorID, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getDataBySensor.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_getDataBySensor(::ZG6000::ListStringMap& listData, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDataBySensor(::ZG6000::ListStringMap& iceP_listData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDataBySensor(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param listSensor 传感器ID列表
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取多个传感器的数据
     */
    bool getDataBySensors(const ::ZG6000::StringList& listSensor, ::ZG6000::ListStringMap& listData, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getDataBySensors(listData, e, _iceI_begin_getDataBySensors(listSensor, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param listSensor 传感器ID列表
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取多个传感器的数据
     */
    ::Ice::AsyncResultPtr begin_getDataBySensors(const ::ZG6000::StringList& listSensor, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getDataBySensors(listSensor, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param listSensor 传感器ID列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取多个传感器的数据
     */
    ::Ice::AsyncResultPtr begin_getDataBySensors(const ::ZG6000::StringList& listSensor, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataBySensors(listSensor, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listSensor 传感器ID列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取多个传感器的数据
     */
    ::Ice::AsyncResultPtr begin_getDataBySensors(const ::ZG6000::StringList& listSensor, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataBySensors(listSensor, context, cb, cookie);
    }

    /**
     * @param listSensor 传感器ID列表
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取多个传感器的数据
     */
    ::Ice::AsyncResultPtr begin_getDataBySensors(const ::ZG6000::StringList& listSensor, const ::ZG6000::Callback_ZGSTStrayDevice_getDataBySensorsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataBySensors(listSensor, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param listSensor 传感器ID列表
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	获取多个传感器的数据
     */
    ::Ice::AsyncResultPtr begin_getDataBySensors(const ::ZG6000::StringList& listSensor, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStrayDevice_getDataBySensorsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getDataBySensors(listSensor, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_getDataBySensors.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_getDataBySensors(::ZG6000::ListStringMap& listData, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getDataBySensors(::ZG6000::ListStringMap& iceP_listData, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getDataBySensors(const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * @param context The Context map to send with the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	发送遥设命令
     */
    bool sendYs(const ::std::string& clientID, const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, bool automatic, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_sendYs(e, _iceI_begin_sendYs(clientID, deviceID, propertyName, propertyValue, automatic, context, ::IceInternal::dummyCallback, 0, true));
    }

    /**
     * @param context The Context map to send with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	发送遥设命令
     */
    ::Ice::AsyncResultPtr begin_sendYs(const ::std::string& clientID, const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, bool automatic, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_sendYs(clientID, deviceID, propertyName, propertyValue, automatic, context, ::IceInternal::dummyCallback, 0);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	发送遥设命令
     */
    ::Ice::AsyncResultPtr begin_sendYs(const ::std::string& clientID, const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, bool automatic, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendYs(clientID, deviceID, propertyName, propertyValue, automatic, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	发送遥设命令
     */
    ::Ice::AsyncResultPtr begin_sendYs(const ::std::string& clientID, const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, bool automatic, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendYs(clientID, deviceID, propertyName, propertyValue, automatic, context, cb, cookie);
    }

    /**
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	发送遥设命令
     */
    ::Ice::AsyncResultPtr begin_sendYs(const ::std::string& clientID, const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, bool automatic, const ::ZG6000::Callback_ZGSTStrayDevice_sendYsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendYs(clientID, deviceID, propertyName, propertyValue, automatic, ::Ice::noExplicitContext, cb, cookie);
    }

    /**
     * @param context The Context map to send with the invocation.
     * @param cb Asynchronous callback object.
     * @param cookie User-defined data to associate with the invocation.
     * @return The asynchronous result object for the invocation.
     * @brief	发送遥设命令
     */
    ::Ice::AsyncResultPtr begin_sendYs(const ::std::string& clientID, const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, bool automatic, const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStrayDevice_sendYsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendYs(clientID, deviceID, propertyName, propertyValue, automatic, context, cb, cookie);
    }

    /**
     * Completes an invocation of begin_sendYs.
     * @param result The asynchronous result object for the invocation.
     * @return 执行成功返回true，失败返回false。
     */
    bool end_sendYs(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_sendYs(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_sendYs(const ::std::string&, const ::std::string&, const ::std::string&, const ::std::string&, bool, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    void resetCalculation(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_resetCalculation(_iceI_begin_resetCalculation(context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_resetCalculation(const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_resetCalculation(context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_resetCalculation(const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetCalculation(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resetCalculation(const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetCalculation(context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resetCalculation(const ::ZG6000::Callback_ZGSTStrayDevice_resetCalculationPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetCalculation(::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_resetCalculation(const ::Ice::Context& context, const ::ZG6000::Callback_ZGSTStrayDevice_resetCalculationPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_resetCalculation(context, cb, cookie);
    }

    void end_resetCalculation(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_resetCalculation(const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGSTStrayDevice : virtual public ZGServerBase
{
public:

    typedef ZGSTStrayDevicePrx ProxyType;
    typedef ZGSTStrayDevicePtr PointerType;

    virtual ~ZGSTStrayDevice();

#ifdef ICE_CPP11_COMPILER
    ZGSTStrayDevice() = default;
    ZGSTStrayDevice(const ZGSTStrayDevice&) = default;
    ZGSTStrayDevice& operator=(const ZGSTStrayDevice&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	装置获取所有站点
     */
    virtual bool getAllStations(ListStringMap& listMapStation, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getAllStations(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param stationID 站点ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取站点的传感器信息
     */
    virtual bool getSensorByStation(const ::std::string& stationID, ListStringMap& listMapSensor, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getSensorByStation(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param sensorID 传感器ID
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取传感器的数据
     */
    virtual bool getDataBySensor(const ::std::string& sensorID, ListStringMap& listData, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDataBySensor(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param listSensor 传感器ID列表
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	获取多个传感器的数据
     */
    virtual bool getDataBySensors(const StringList& listSensor, ListStringMap& listData, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getDataBySensors(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * @param current The Current object for the invocation.
     * @return 执行成功返回true，失败返回false。
     * @brief	发送遥设命令
     */
    virtual bool sendYs(const ::std::string& clientID, const ::std::string& deviceID, const ::std::string& propertyName, const ::std::string& propertyValue, bool automatic, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_sendYs(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void resetCalculation(const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_resetCalculation(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGSTStrayDevice& lhs, const ZGSTStrayDevice& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGSTStrayDevice& lhs, const ZGSTStrayDevice& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getAllStations.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getAllStations.
 */
template<class T>
class CallbackNC_ZGSTStrayDevice_getAllStations : public Callback_ZGSTStrayDevice_getAllStations_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSTStrayDevice_getAllStations(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStrayDevicePrx proxy = ZGSTStrayDevicePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listMapStation;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getAllStations(iceP_listMapStation, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listMapStation, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getAllStations.
 */
template<class T> Callback_ZGSTStrayDevice_getAllStationsPtr
newCallback_ZGSTStrayDevice_getAllStations(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_getAllStations<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getAllStations.
 */
template<class T> Callback_ZGSTStrayDevice_getAllStationsPtr
newCallback_ZGSTStrayDevice_getAllStations(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_getAllStations<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getAllStations.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getAllStations.
 */
template<class T, typename CT>
class Callback_ZGSTStrayDevice_getAllStations : public Callback_ZGSTStrayDevice_getAllStations_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSTStrayDevice_getAllStations(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStrayDevicePrx proxy = ZGSTStrayDevicePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listMapStation;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getAllStations(iceP_listMapStation, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listMapStation, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getAllStations.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_getAllStationsPtr
newCallback_ZGSTStrayDevice_getAllStations(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_getAllStations<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getAllStations.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_getAllStationsPtr
newCallback_ZGSTStrayDevice_getAllStations(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_getAllStations<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getSensorByStation.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getSensorByStation.
 */
template<class T>
class CallbackNC_ZGSTStrayDevice_getSensorByStation : public Callback_ZGSTStrayDevice_getSensorByStation_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSTStrayDevice_getSensorByStation(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStrayDevicePrx proxy = ZGSTStrayDevicePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listMapSensor;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getSensorByStation(iceP_listMapSensor, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listMapSensor, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getSensorByStation.
 */
template<class T> Callback_ZGSTStrayDevice_getSensorByStationPtr
newCallback_ZGSTStrayDevice_getSensorByStation(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_getSensorByStation<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getSensorByStation.
 */
template<class T> Callback_ZGSTStrayDevice_getSensorByStationPtr
newCallback_ZGSTStrayDevice_getSensorByStation(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_getSensorByStation<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getSensorByStation.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getSensorByStation.
 */
template<class T, typename CT>
class Callback_ZGSTStrayDevice_getSensorByStation : public Callback_ZGSTStrayDevice_getSensorByStation_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSTStrayDevice_getSensorByStation(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStrayDevicePrx proxy = ZGSTStrayDevicePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listMapSensor;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getSensorByStation(iceP_listMapSensor, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listMapSensor, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getSensorByStation.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_getSensorByStationPtr
newCallback_ZGSTStrayDevice_getSensorByStation(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_getSensorByStation<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getSensorByStation.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_getSensorByStationPtr
newCallback_ZGSTStrayDevice_getSensorByStation(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_getSensorByStation<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensor.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getDataBySensor.
 */
template<class T>
class CallbackNC_ZGSTStrayDevice_getDataBySensor : public Callback_ZGSTStrayDevice_getDataBySensor_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSTStrayDevice_getDataBySensor(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStrayDevicePrx proxy = ZGSTStrayDevicePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDataBySensor(iceP_listData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listData, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensor.
 */
template<class T> Callback_ZGSTStrayDevice_getDataBySensorPtr
newCallback_ZGSTStrayDevice_getDataBySensor(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_getDataBySensor<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensor.
 */
template<class T> Callback_ZGSTStrayDevice_getDataBySensorPtr
newCallback_ZGSTStrayDevice_getDataBySensor(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_getDataBySensor<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensor.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getDataBySensor.
 */
template<class T, typename CT>
class Callback_ZGSTStrayDevice_getDataBySensor : public Callback_ZGSTStrayDevice_getDataBySensor_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSTStrayDevice_getDataBySensor(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStrayDevicePrx proxy = ZGSTStrayDevicePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDataBySensor(iceP_listData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listData, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensor.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_getDataBySensorPtr
newCallback_ZGSTStrayDevice_getDataBySensor(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_getDataBySensor<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensor.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_getDataBySensorPtr
newCallback_ZGSTStrayDevice_getDataBySensor(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_getDataBySensor<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensors.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getDataBySensors.
 */
template<class T>
class CallbackNC_ZGSTStrayDevice_getDataBySensors : public Callback_ZGSTStrayDevice_getDataBySensors_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGSTStrayDevice_getDataBySensors(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStrayDevicePrx proxy = ZGSTStrayDevicePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDataBySensors(iceP_listData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listData, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensors.
 */
template<class T> Callback_ZGSTStrayDevice_getDataBySensorsPtr
newCallback_ZGSTStrayDevice_getDataBySensors(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_getDataBySensors<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensors.
 */
template<class T> Callback_ZGSTStrayDevice_getDataBySensorsPtr
newCallback_ZGSTStrayDevice_getDataBySensors(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_getDataBySensors<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensors.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_getDataBySensors.
 */
template<class T, typename CT>
class Callback_ZGSTStrayDevice_getDataBySensors : public Callback_ZGSTStrayDevice_getDataBySensors_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGSTStrayDevice_getDataBySensors(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStrayDevicePrx proxy = ZGSTStrayDevicePrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listData;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getDataBySensors(iceP_listData, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listData, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensors.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_getDataBySensorsPtr
newCallback_ZGSTStrayDevice_getDataBySensors(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_getDataBySensors<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_getDataBySensors.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_getDataBySensorsPtr
newCallback_ZGSTStrayDevice_getDataBySensors(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_getDataBySensors<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_sendYs.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_sendYs.
 */
template<class T>
class CallbackNC_ZGSTStrayDevice_sendYs : public Callback_ZGSTStrayDevice_sendYs_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGSTStrayDevice_sendYs(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStrayDevicePrx proxy = ZGSTStrayDevicePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_sendYs(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_sendYs.
 */
template<class T> Callback_ZGSTStrayDevice_sendYsPtr
newCallback_ZGSTStrayDevice_sendYs(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_sendYs<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_sendYs.
 */
template<class T> Callback_ZGSTStrayDevice_sendYsPtr
newCallback_ZGSTStrayDevice_sendYs(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_sendYs<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_sendYs.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_sendYs.
 */
template<class T, typename CT>
class Callback_ZGSTStrayDevice_sendYs : public Callback_ZGSTStrayDevice_sendYs_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGSTStrayDevice_sendYs(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGSTStrayDevicePrx proxy = ZGSTStrayDevicePrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_sendYs(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_sendYs.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_sendYsPtr
newCallback_ZGSTStrayDevice_sendYs(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_sendYs<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_sendYs.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_sendYsPtr
newCallback_ZGSTStrayDevice_sendYs(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_sendYs<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_resetCalculation.
 */
template<class T>
class CallbackNC_ZGSTStrayDevice_resetCalculation : public Callback_ZGSTStrayDevice_resetCalculation_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGSTStrayDevice_resetCalculation(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 */
template<class T> Callback_ZGSTStrayDevice_resetCalculationPtr
newCallback_ZGSTStrayDevice_resetCalculation(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_resetCalculation<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 */
template<class T> Callback_ZGSTStrayDevice_resetCalculationPtr
newCallback_ZGSTStrayDevice_resetCalculation(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_resetCalculation<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 */
template<class T> Callback_ZGSTStrayDevice_resetCalculationPtr
newCallback_ZGSTStrayDevice_resetCalculation(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_resetCalculation<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 */
template<class T> Callback_ZGSTStrayDevice_resetCalculationPtr
newCallback_ZGSTStrayDevice_resetCalculation(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGSTStrayDevice_resetCalculation<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGSTStrayDevice_resetCalculation.
 */
template<class T, typename CT>
class Callback_ZGSTStrayDevice_resetCalculation : public Callback_ZGSTStrayDevice_resetCalculation_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGSTStrayDevice_resetCalculation(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_resetCalculationPtr
newCallback_ZGSTStrayDevice_resetCalculation(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_resetCalculation<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_resetCalculationPtr
newCallback_ZGSTStrayDevice_resetCalculation(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_resetCalculation<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_resetCalculationPtr
newCallback_ZGSTStrayDevice_resetCalculation(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_resetCalculation<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGSTStrayDevice::begin_resetCalculation.
 */
template<class T, typename CT> Callback_ZGSTStrayDevice_resetCalculationPtr
newCallback_ZGSTStrayDevice_resetCalculation(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGSTStrayDevice_resetCalculation<T, CT>(instance, 0, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

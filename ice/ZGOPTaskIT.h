//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGOPTaskIT.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGOPTaskIT_h__
#define __ZGOPTaskIT_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGOPTaskBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGOPTaskIT;
class ZGOPTaskITPrx;

}

namespace ZG6000
{

class ZGOPTaskIT : public virtual ZGOPTaskBase
{
public:

    using ProxyType = ZGOPTaskITPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getTaskItems.
     */
    struct GetTaskItemsResult
    {
        bool returnValue;
        StringMap task;
        ListStringMap items;
        ErrorInfo e;
    };

    virtual bool getTaskItems(::std::string taskID, StringMap& task, ListStringMap& items, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskItems(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getItemActions.
     */
    struct GetItemActionsResult
    {
        bool returnValue;
        StringMap item;
        ListStringMap actions;
        ErrorInfo e;
    };

    virtual bool getItemActions(::std::string itemID, StringMap& item, ListStringMap& actions, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getItemActions(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to createTypicalTask.
     */
    struct CreateTypicalTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool createTypicalTask(StringMap params, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createTypicalTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to getTaskTypeObjects.
     */
    struct GetTaskTypeObjectsResult
    {
        bool returnValue;
        ListStringMap listObject;
        ErrorInfo e;
    };

    virtual bool getTaskTypeObjects(StringMap params, ListStringMap& listObject, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskTypeObjects(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to createSpecialTask.
     */
    struct CreateSpecialTaskResult
    {
        bool returnValue;
        ::std::string taskID;
        ErrorInfo e;
    };

    virtual bool createSpecialTask(::std::string taskTypeID, StringList listObjectID, StringMap params, ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createSpecialTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to createCustomTask.
     */
    struct CreateCustomTaskResult
    {
        bool returnValue;
        ::std::string taskID;
        ErrorInfo e;
    };

    virtual bool createCustomTask(ListStringMap listItem, StringMap params, ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createCustomTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to createUAVTask.
     */
    struct CreateUAVTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool createUAVTask(StringMap task, ListStringMap listPreset, ListStringMap listItem, ListStringMap listAction, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_createUAVTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to editTask.
     */
    struct EditTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool editTask(::std::string taskID, StringMap task, ListStringMap items, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_editTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to convertTask.
     */
    struct ConvertTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool convertTask(::std::string taskID, StringMap params, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_convertTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to skipItem.
     */
    struct SkipItemResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool skipItem(::std::string itemID, StringMap params, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_skipItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateTask.
     */
    struct UpdateTaskResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateTask(ListStringMap listTask, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateItem.
     */
    struct UpdateItemResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateItem(ListStringMap listItem, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to updateAction.
     */
    struct UpdateActionResult
    {
        bool returnValue;
        ErrorInfo e;
    };

    virtual bool updateAction(ListStringMap listAction, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_updateAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /**
     * Encapsulates the results of a call to downloadTask.
     */
    struct DownloadTaskResult
    {
        bool returnValue;
        ListStringMap listTask;
        ListStringMap listItem;
        ListStringMap listAction;
        ErrorInfo e;
    };

    virtual bool downloadTask(StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ListStringMap& listAction, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_downloadTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGOPTaskITPrx : public virtual ::Ice::Proxy<ZGOPTaskITPrx, ZGOPTaskBasePrx>
{
public:

    bool getTaskItems(const ::std::string& taskID, StringMap& task, ListStringMap& items, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::GetTaskItemsResult>(true, this, &ZGOPTaskITPrx::_iceI_getTaskItems, taskID, context).get();
        task = ::std::move(_result.task);
        items = ::std::move(_result.items);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTaskItemsAsync(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::GetTaskItemsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::GetTaskItemsResult, P>(false, this, &ZGOPTaskITPrx::_iceI_getTaskItems, taskID, context);
    }

    ::std::function<void()>
    getTaskItemsAsync(const ::std::string& taskID,
                      ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::GetTaskItemsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.task), ::std::move(_result.items), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::GetTaskItemsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_getTaskItems, taskID, context);
    }

    /// \cond INTERNAL
    void _iceI_getTaskItems(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::GetTaskItemsResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool getItemActions(const ::std::string& itemID, StringMap& item, ListStringMap& actions, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::GetItemActionsResult>(true, this, &ZGOPTaskITPrx::_iceI_getItemActions, itemID, context).get();
        item = ::std::move(_result.item);
        actions = ::std::move(_result.actions);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getItemActionsAsync(const ::std::string& itemID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::GetItemActionsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::GetItemActionsResult, P>(false, this, &ZGOPTaskITPrx::_iceI_getItemActions, itemID, context);
    }

    ::std::function<void()>
    getItemActionsAsync(const ::std::string& itemID,
                        ::std::function<void(bool, ::ZG6000::StringMap, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::GetItemActionsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.item), ::std::move(_result.actions), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::GetItemActionsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_getItemActions, itemID, context);
    }

    /// \cond INTERNAL
    void _iceI_getItemActions(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::GetItemActionsResult>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    bool createTypicalTask(const StringMap& params, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::CreateTypicalTaskResult>(true, this, &ZGOPTaskITPrx::_iceI_createTypicalTask, params, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto createTypicalTaskAsync(const StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::CreateTypicalTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::CreateTypicalTaskResult, P>(false, this, &ZGOPTaskITPrx::_iceI_createTypicalTask, params, context);
    }

    ::std::function<void()>
    createTypicalTaskAsync(const StringMap& params,
                           ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::CreateTypicalTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::CreateTypicalTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_createTypicalTask, params, context);
    }

    /// \cond INTERNAL
    void _iceI_createTypicalTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::CreateTypicalTaskResult>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool getTaskTypeObjects(const StringMap& params, ListStringMap& listObject, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::GetTaskTypeObjectsResult>(true, this, &ZGOPTaskITPrx::_iceI_getTaskTypeObjects, params, context).get();
        listObject = ::std::move(_result.listObject);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getTaskTypeObjectsAsync(const StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::GetTaskTypeObjectsResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::GetTaskTypeObjectsResult, P>(false, this, &ZGOPTaskITPrx::_iceI_getTaskTypeObjects, params, context);
    }

    ::std::function<void()>
    getTaskTypeObjectsAsync(const StringMap& params,
                            ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                            ::std::function<void(::std::exception_ptr)> ex = nullptr,
                            ::std::function<void(bool)> sent = nullptr,
                            const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::GetTaskTypeObjectsResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listObject), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::GetTaskTypeObjectsResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_getTaskTypeObjects, params, context);
    }

    /// \cond INTERNAL
    void _iceI_getTaskTypeObjects(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::GetTaskTypeObjectsResult>>&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool createSpecialTask(const ::std::string& taskTypeID, const StringList& listObjectID, const StringMap& params, ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::CreateSpecialTaskResult>(true, this, &ZGOPTaskITPrx::_iceI_createSpecialTask, taskTypeID, listObjectID, params, context).get();
        taskID = ::std::move(_result.taskID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto createSpecialTaskAsync(const ::std::string& taskTypeID, const StringList& listObjectID, const StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::CreateSpecialTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::CreateSpecialTaskResult, P>(false, this, &ZGOPTaskITPrx::_iceI_createSpecialTask, taskTypeID, listObjectID, params, context);
    }

    ::std::function<void()>
    createSpecialTaskAsync(const ::std::string& taskTypeID, const StringList& listObjectID, const StringMap& params,
                           ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                           ::std::function<void(::std::exception_ptr)> ex = nullptr,
                           ::std::function<void(bool)> sent = nullptr,
                           const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::CreateSpecialTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.taskID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::CreateSpecialTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_createSpecialTask, taskTypeID, listObjectID, params, context);
    }

    /// \cond INTERNAL
    void _iceI_createSpecialTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::CreateSpecialTaskResult>>&, const ::std::string&, const StringList&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool createCustomTask(const ListStringMap& listItem, const StringMap& params, ::std::string& taskID, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::CreateCustomTaskResult>(true, this, &ZGOPTaskITPrx::_iceI_createCustomTask, listItem, params, context).get();
        taskID = ::std::move(_result.taskID);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto createCustomTaskAsync(const ListStringMap& listItem, const StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::CreateCustomTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::CreateCustomTaskResult, P>(false, this, &ZGOPTaskITPrx::_iceI_createCustomTask, listItem, params, context);
    }

    ::std::function<void()>
    createCustomTaskAsync(const ListStringMap& listItem, const StringMap& params,
                          ::std::function<void(bool, ::std::string, ::ZG6000::ErrorInfo)> response,
                          ::std::function<void(::std::exception_ptr)> ex = nullptr,
                          ::std::function<void(bool)> sent = nullptr,
                          const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::CreateCustomTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.taskID), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::CreateCustomTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_createCustomTask, listItem, params, context);
    }

    /// \cond INTERNAL
    void _iceI_createCustomTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::CreateCustomTaskResult>>&, const ListStringMap&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool createUAVTask(const StringMap& task, const ListStringMap& listPreset, const ListStringMap& listItem, const ListStringMap& listAction, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::CreateUAVTaskResult>(true, this, &ZGOPTaskITPrx::_iceI_createUAVTask, task, listPreset, listItem, listAction, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto createUAVTaskAsync(const StringMap& task, const ListStringMap& listPreset, const ListStringMap& listItem, const ListStringMap& listAction, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::CreateUAVTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::CreateUAVTaskResult, P>(false, this, &ZGOPTaskITPrx::_iceI_createUAVTask, task, listPreset, listItem, listAction, context);
    }

    ::std::function<void()>
    createUAVTaskAsync(const StringMap& task, const ListStringMap& listPreset, const ListStringMap& listItem, const ListStringMap& listAction,
                       ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                       ::std::function<void(::std::exception_ptr)> ex = nullptr,
                       ::std::function<void(bool)> sent = nullptr,
                       const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::CreateUAVTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::CreateUAVTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_createUAVTask, task, listPreset, listItem, listAction, context);
    }

    /// \cond INTERNAL
    void _iceI_createUAVTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::CreateUAVTaskResult>>&, const StringMap&, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool editTask(const ::std::string& taskID, const StringMap& task, const ListStringMap& items, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::EditTaskResult>(true, this, &ZGOPTaskITPrx::_iceI_editTask, taskID, task, items, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto editTaskAsync(const ::std::string& taskID, const StringMap& task, const ListStringMap& items, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::EditTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::EditTaskResult, P>(false, this, &ZGOPTaskITPrx::_iceI_editTask, taskID, task, items, context);
    }

    ::std::function<void()>
    editTaskAsync(const ::std::string& taskID, const StringMap& task, const ListStringMap& items,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::EditTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::EditTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_editTask, taskID, task, items, context);
    }

    /// \cond INTERNAL
    void _iceI_editTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::EditTaskResult>>&, const ::std::string&, const StringMap&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool convertTask(const ::std::string& taskID, const StringMap& params, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::ConvertTaskResult>(true, this, &ZGOPTaskITPrx::_iceI_convertTask, taskID, params, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto convertTaskAsync(const ::std::string& taskID, const StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::ConvertTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::ConvertTaskResult, P>(false, this, &ZGOPTaskITPrx::_iceI_convertTask, taskID, params, context);
    }

    ::std::function<void()>
    convertTaskAsync(const ::std::string& taskID, const StringMap& params,
                     ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                     ::std::function<void(::std::exception_ptr)> ex = nullptr,
                     ::std::function<void(bool)> sent = nullptr,
                     const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::ConvertTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::ConvertTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_convertTask, taskID, params, context);
    }

    /// \cond INTERNAL
    void _iceI_convertTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::ConvertTaskResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool skipItem(const ::std::string& itemID, const StringMap& params, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::SkipItemResult>(true, this, &ZGOPTaskITPrx::_iceI_skipItem, itemID, params, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto skipItemAsync(const ::std::string& itemID, const StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::SkipItemResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::SkipItemResult, P>(false, this, &ZGOPTaskITPrx::_iceI_skipItem, itemID, params, context);
    }

    ::std::function<void()>
    skipItemAsync(const ::std::string& itemID, const StringMap& params,
                  ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::SkipItemResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::SkipItemResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_skipItem, itemID, params, context);
    }

    /// \cond INTERNAL
    void _iceI_skipItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::SkipItemResult>>&, const ::std::string&, const StringMap&, const ::Ice::Context&);
    /// \endcond

    bool updateTask(const ListStringMap& listTask, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::UpdateTaskResult>(true, this, &ZGOPTaskITPrx::_iceI_updateTask, listTask, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateTaskAsync(const ListStringMap& listTask, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::UpdateTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::UpdateTaskResult, P>(false, this, &ZGOPTaskITPrx::_iceI_updateTask, listTask, context);
    }

    ::std::function<void()>
    updateTaskAsync(const ListStringMap& listTask,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::UpdateTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::UpdateTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_updateTask, listTask, context);
    }

    /// \cond INTERNAL
    void _iceI_updateTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::UpdateTaskResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool updateItem(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::UpdateItemResult>(true, this, &ZGOPTaskITPrx::_iceI_updateItem, listItem, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateItemAsync(const ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::UpdateItemResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::UpdateItemResult, P>(false, this, &ZGOPTaskITPrx::_iceI_updateItem, listItem, context);
    }

    ::std::function<void()>
    updateItemAsync(const ListStringMap& listItem,
                    ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                    ::std::function<void(::std::exception_ptr)> ex = nullptr,
                    ::std::function<void(bool)> sent = nullptr,
                    const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::UpdateItemResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::UpdateItemResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_updateItem, listItem, context);
    }

    /// \cond INTERNAL
    void _iceI_updateItem(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::UpdateItemResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool updateAction(const ListStringMap& listAction, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::UpdateActionResult>(true, this, &ZGOPTaskITPrx::_iceI_updateAction, listAction, context).get();
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto updateActionAsync(const ListStringMap& listAction, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::UpdateActionResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::UpdateActionResult, P>(false, this, &ZGOPTaskITPrx::_iceI_updateAction, listAction, context);
    }

    ::std::function<void()>
    updateActionAsync(const ListStringMap& listAction,
                      ::std::function<void(bool, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::UpdateActionResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::UpdateActionResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_updateAction, listAction, context);
    }

    /// \cond INTERNAL
    void _iceI_updateAction(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::UpdateActionResult>>&, const ListStringMap&, const ::Ice::Context&);
    /// \endcond

    bool downloadTask(const StringList& listTaskID, ListStringMap& listTask, ListStringMap& listItem, ListStringMap& listAction, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGOPTaskIT::DownloadTaskResult>(true, this, &ZGOPTaskITPrx::_iceI_downloadTask, listTaskID, context).get();
        listTask = ::std::move(_result.listTask);
        listItem = ::std::move(_result.listItem);
        listAction = ::std::move(_result.listAction);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto downloadTaskAsync(const StringList& listTaskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGOPTaskIT::DownloadTaskResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGOPTaskIT::DownloadTaskResult, P>(false, this, &ZGOPTaskITPrx::_iceI_downloadTask, listTaskID, context);
    }

    ::std::function<void()>
    downloadTaskAsync(const StringList& listTaskID,
                      ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ListStringMap, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                      ::std::function<void(::std::exception_ptr)> ex = nullptr,
                      ::std::function<void(bool)> sent = nullptr,
                      const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGOPTaskIT::DownloadTaskResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listTask), ::std::move(_result.listItem), ::std::move(_result.listAction), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGOPTaskIT::DownloadTaskResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGOPTaskITPrx::_iceI_downloadTask, listTaskID, context);
    }

    /// \cond INTERNAL
    void _iceI_downloadTask(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGOPTaskIT::DownloadTaskResult>>&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGOPTaskITPrx() = default;
    friend ::std::shared_ptr<ZGOPTaskITPrx> IceInternal::createProxy<ZGOPTaskITPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGOPTaskITPtr = ::std::shared_ptr<ZGOPTaskIT>;
using ZGOPTaskITPrxPtr = ::std::shared_ptr<ZGOPTaskITPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGOPTaskIT;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGOPTaskIT>&);
::IceProxy::Ice::Object* upCast(ZGOPTaskIT*);
/// \endcond

}

}

namespace ZG6000
{

class ZGOPTaskIT;
/// \cond INTERNAL
::Ice::Object* upCast(ZGOPTaskIT*);
/// \endcond
typedef ::IceInternal::Handle< ZGOPTaskIT> ZGOPTaskITPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGOPTaskIT> ZGOPTaskITPrx;
typedef ZGOPTaskITPrx ZGOPTaskITPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGOPTaskITPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskItems.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_getTaskItems.
 */
class Callback_ZGOPTaskIT_getTaskItems_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_getTaskItems_Base> Callback_ZGOPTaskIT_getTaskItemsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_getItemActions.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_getItemActions.
 */
class Callback_ZGOPTaskIT_getItemActions_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_getItemActions_Base> Callback_ZGOPTaskIT_getItemActionsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createTypicalTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createTypicalTask.
 */
class Callback_ZGOPTaskIT_createTypicalTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_createTypicalTask_Base> Callback_ZGOPTaskIT_createTypicalTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskTypeObjects.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_getTaskTypeObjects.
 */
class Callback_ZGOPTaskIT_getTaskTypeObjects_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_getTaskTypeObjects_Base> Callback_ZGOPTaskIT_getTaskTypeObjectsPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createSpecialTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createSpecialTask.
 */
class Callback_ZGOPTaskIT_createSpecialTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_createSpecialTask_Base> Callback_ZGOPTaskIT_createSpecialTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createCustomTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createCustomTask.
 */
class Callback_ZGOPTaskIT_createCustomTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_createCustomTask_Base> Callback_ZGOPTaskIT_createCustomTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createUAVTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createUAVTask.
 */
class Callback_ZGOPTaskIT_createUAVTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_createUAVTask_Base> Callback_ZGOPTaskIT_createUAVTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_editTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_editTask.
 */
class Callback_ZGOPTaskIT_editTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_editTask_Base> Callback_ZGOPTaskIT_editTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_convertTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_convertTask.
 */
class Callback_ZGOPTaskIT_convertTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_convertTask_Base> Callback_ZGOPTaskIT_convertTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_skipItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_skipItem.
 */
class Callback_ZGOPTaskIT_skipItem_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_skipItem_Base> Callback_ZGOPTaskIT_skipItemPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_updateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_updateTask.
 */
class Callback_ZGOPTaskIT_updateTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_updateTask_Base> Callback_ZGOPTaskIT_updateTaskPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_updateItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_updateItem.
 */
class Callback_ZGOPTaskIT_updateItem_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_updateItem_Base> Callback_ZGOPTaskIT_updateItemPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_updateAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_updateAction.
 */
class Callback_ZGOPTaskIT_updateAction_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_updateAction_Base> Callback_ZGOPTaskIT_updateActionPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_downloadTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_downloadTask.
 */
class Callback_ZGOPTaskIT_downloadTask_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGOPTaskIT_downloadTask_Base> Callback_ZGOPTaskIT_downloadTaskPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGOPTaskIT : public virtual ::Ice::Proxy<ZGOPTaskIT, ::IceProxy::ZG6000::ZGOPTaskBase>
{
public:

    bool getTaskItems(const ::std::string& taskID, ::ZG6000::StringMap& task, ::ZG6000::ListStringMap& items, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTaskItems(task, items, e, _iceI_begin_getTaskItems(taskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTaskItems(taskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::ZG6000::Callback_ZGOPTaskIT_getTaskItemsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskItems(const ::std::string& taskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_getTaskItemsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskItems(taskID, context, cb, cookie);
    }

    bool end_getTaskItems(::ZG6000::StringMap& task, ::ZG6000::ListStringMap& items, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTaskItems(::ZG6000::StringMap& iceP_task, ::ZG6000::ListStringMap& iceP_items, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTaskItems(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getItemActions(const ::std::string& itemID, ::ZG6000::StringMap& item, ::ZG6000::ListStringMap& actions, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getItemActions(item, actions, e, _iceI_begin_getItemActions(itemID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getItemActions(const ::std::string& itemID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getItemActions(itemID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getItemActions(const ::std::string& itemID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getItemActions(itemID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getItemActions(const ::std::string& itemID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getItemActions(itemID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getItemActions(const ::std::string& itemID, const ::ZG6000::Callback_ZGOPTaskIT_getItemActionsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getItemActions(itemID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getItemActions(const ::std::string& itemID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_getItemActionsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getItemActions(itemID, context, cb, cookie);
    }

    bool end_getItemActions(::ZG6000::StringMap& item, ::ZG6000::ListStringMap& actions, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getItemActions(::ZG6000::StringMap& iceP_item, ::ZG6000::ListStringMap& iceP_actions, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getItemActions(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool createTypicalTask(const ::ZG6000::StringMap& params, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createTypicalTask(e, _iceI_begin_createTypicalTask(params, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_createTypicalTask(const ::ZG6000::StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createTypicalTask(params, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_createTypicalTask(const ::ZG6000::StringMap& params, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTypicalTask(params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createTypicalTask(const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTypicalTask(params, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createTypicalTask(const ::ZG6000::StringMap& params, const ::ZG6000::Callback_ZGOPTaskIT_createTypicalTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTypicalTask(params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createTypicalTask(const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_createTypicalTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createTypicalTask(params, context, cb, cookie);
    }

    bool end_createTypicalTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createTypicalTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createTypicalTask(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool getTaskTypeObjects(const ::ZG6000::StringMap& params, ::ZG6000::ListStringMap& listObject, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getTaskTypeObjects(listObject, e, _iceI_begin_getTaskTypeObjects(params, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getTaskTypeObjects(const ::ZG6000::StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getTaskTypeObjects(params, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getTaskTypeObjects(const ::ZG6000::StringMap& params, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskTypeObjects(params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskTypeObjects(const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskTypeObjects(params, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskTypeObjects(const ::ZG6000::StringMap& params, const ::ZG6000::Callback_ZGOPTaskIT_getTaskTypeObjectsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskTypeObjects(params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getTaskTypeObjects(const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_getTaskTypeObjectsPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getTaskTypeObjects(params, context, cb, cookie);
    }

    bool end_getTaskTypeObjects(::ZG6000::ListStringMap& listObject, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getTaskTypeObjects(::ZG6000::ListStringMap& iceP_listObject, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getTaskTypeObjects(const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool createSpecialTask(const ::std::string& taskTypeID, const ::ZG6000::StringList& listObjectID, const ::ZG6000::StringMap& params, ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createSpecialTask(taskID, e, _iceI_begin_createSpecialTask(taskTypeID, listObjectID, params, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_createSpecialTask(const ::std::string& taskTypeID, const ::ZG6000::StringList& listObjectID, const ::ZG6000::StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createSpecialTask(taskTypeID, listObjectID, params, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_createSpecialTask(const ::std::string& taskTypeID, const ::ZG6000::StringList& listObjectID, const ::ZG6000::StringMap& params, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createSpecialTask(taskTypeID, listObjectID, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createSpecialTask(const ::std::string& taskTypeID, const ::ZG6000::StringList& listObjectID, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createSpecialTask(taskTypeID, listObjectID, params, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createSpecialTask(const ::std::string& taskTypeID, const ::ZG6000::StringList& listObjectID, const ::ZG6000::StringMap& params, const ::ZG6000::Callback_ZGOPTaskIT_createSpecialTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createSpecialTask(taskTypeID, listObjectID, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createSpecialTask(const ::std::string& taskTypeID, const ::ZG6000::StringList& listObjectID, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_createSpecialTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createSpecialTask(taskTypeID, listObjectID, params, context, cb, cookie);
    }

    bool end_createSpecialTask(::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createSpecialTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createSpecialTask(const ::std::string&, const ::ZG6000::StringList&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool createCustomTask(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::StringMap& params, ::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createCustomTask(taskID, e, _iceI_begin_createCustomTask(listItem, params, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_createCustomTask(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createCustomTask(listItem, params, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_createCustomTask(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::StringMap& params, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createCustomTask(listItem, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createCustomTask(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createCustomTask(listItem, params, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createCustomTask(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::StringMap& params, const ::ZG6000::Callback_ZGOPTaskIT_createCustomTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createCustomTask(listItem, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createCustomTask(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_createCustomTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createCustomTask(listItem, params, context, cb, cookie);
    }

    bool end_createCustomTask(::std::string& taskID, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createCustomTask(::std::string& iceP_taskID, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createCustomTask(const ::ZG6000::ListStringMap&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool createUAVTask(const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& listPreset, const ::ZG6000::ListStringMap& listItem, const ::ZG6000::ListStringMap& listAction, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_createUAVTask(e, _iceI_begin_createUAVTask(task, listPreset, listItem, listAction, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_createUAVTask(const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& listPreset, const ::ZG6000::ListStringMap& listItem, const ::ZG6000::ListStringMap& listAction, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_createUAVTask(task, listPreset, listItem, listAction, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_createUAVTask(const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& listPreset, const ::ZG6000::ListStringMap& listItem, const ::ZG6000::ListStringMap& listAction, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createUAVTask(task, listPreset, listItem, listAction, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createUAVTask(const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& listPreset, const ::ZG6000::ListStringMap& listItem, const ::ZG6000::ListStringMap& listAction, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createUAVTask(task, listPreset, listItem, listAction, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createUAVTask(const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& listPreset, const ::ZG6000::ListStringMap& listItem, const ::ZG6000::ListStringMap& listAction, const ::ZG6000::Callback_ZGOPTaskIT_createUAVTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createUAVTask(task, listPreset, listItem, listAction, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_createUAVTask(const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& listPreset, const ::ZG6000::ListStringMap& listItem, const ::ZG6000::ListStringMap& listAction, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_createUAVTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_createUAVTask(task, listPreset, listItem, listAction, context, cb, cookie);
    }

    bool end_createUAVTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_createUAVTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_createUAVTask(const ::ZG6000::StringMap&, const ::ZG6000::ListStringMap&, const ::ZG6000::ListStringMap&, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool editTask(const ::std::string& taskID, const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& items, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_editTask(e, _iceI_begin_editTask(taskID, task, items, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_editTask(const ::std::string& taskID, const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& items, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_editTask(taskID, task, items, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_editTask(const ::std::string& taskID, const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& items, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editTask(taskID, task, items, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_editTask(const ::std::string& taskID, const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& items, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editTask(taskID, task, items, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_editTask(const ::std::string& taskID, const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& items, const ::ZG6000::Callback_ZGOPTaskIT_editTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editTask(taskID, task, items, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_editTask(const ::std::string& taskID, const ::ZG6000::StringMap& task, const ::ZG6000::ListStringMap& items, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_editTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_editTask(taskID, task, items, context, cb, cookie);
    }

    bool end_editTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_editTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_editTask(const ::std::string&, const ::ZG6000::StringMap&, const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& params, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_convertTask(e, _iceI_begin_convertTask(taskID, params, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_convertTask(taskID, params, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& params, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertTask(taskID, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertTask(taskID, params, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& params, const ::ZG6000::Callback_ZGOPTaskIT_convertTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertTask(taskID, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_convertTask(const ::std::string& taskID, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_convertTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_convertTask(taskID, params, context, cb, cookie);
    }

    bool end_convertTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_convertTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_convertTask(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool skipItem(const ::std::string& itemID, const ::ZG6000::StringMap& params, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_skipItem(e, _iceI_begin_skipItem(itemID, params, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_skipItem(const ::std::string& itemID, const ::ZG6000::StringMap& params, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_skipItem(itemID, params, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_skipItem(const ::std::string& itemID, const ::ZG6000::StringMap& params, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_skipItem(itemID, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_skipItem(const ::std::string& itemID, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_skipItem(itemID, params, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_skipItem(const ::std::string& itemID, const ::ZG6000::StringMap& params, const ::ZG6000::Callback_ZGOPTaskIT_skipItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_skipItem(itemID, params, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_skipItem(const ::std::string& itemID, const ::ZG6000::StringMap& params, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_skipItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_skipItem(itemID, params, context, cb, cookie);
    }

    bool end_skipItem(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_skipItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_skipItem(const ::std::string&, const ::ZG6000::StringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateTask(const ::ZG6000::ListStringMap& listTask, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateTask(e, _iceI_begin_updateTask(listTask, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateTask(listTask, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::ZG6000::Callback_ZGOPTaskIT_updateTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateTask(const ::ZG6000::ListStringMap& listTask, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_updateTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateTask(listTask, context, cb, cookie);
    }

    bool end_updateTask(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateTask(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateTask(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateItem(const ::ZG6000::ListStringMap& listItem, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateItem(e, _iceI_begin_updateItem(listItem, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateItem(listItem, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::ZG6000::Callback_ZGOPTaskIT_updateItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateItem(const ::ZG6000::ListStringMap& listItem, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_updateItemPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateItem(listItem, context, cb, cookie);
    }

    bool end_updateItem(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateItem(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateItem(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool updateAction(const ::ZG6000::ListStringMap& listAction, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_updateAction(e, _iceI_begin_updateAction(listAction, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_updateAction(const ::ZG6000::ListStringMap& listAction, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_updateAction(listAction, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_updateAction(const ::ZG6000::ListStringMap& listAction, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateAction(listAction, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateAction(const ::ZG6000::ListStringMap& listAction, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateAction(listAction, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateAction(const ::ZG6000::ListStringMap& listAction, const ::ZG6000::Callback_ZGOPTaskIT_updateActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateAction(listAction, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_updateAction(const ::ZG6000::ListStringMap& listAction, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_updateActionPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_updateAction(listAction, context, cb, cookie);
    }

    bool end_updateAction(::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_updateAction(::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_updateAction(const ::ZG6000::ListStringMap&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    bool downloadTask(const ::ZG6000::StringList& listTaskID, ::ZG6000::ListStringMap& listTask, ::ZG6000::ListStringMap& listItem, ::ZG6000::ListStringMap& listAction, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_downloadTask(listTask, listItem, listAction, e, _iceI_begin_downloadTask(listTaskID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::ZG6000::StringList& listTaskID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_downloadTask(listTaskID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::ZG6000::StringList& listTaskID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(listTaskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::ZG6000::StringList& listTaskID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(listTaskID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::ZG6000::StringList& listTaskID, const ::ZG6000::Callback_ZGOPTaskIT_downloadTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(listTaskID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_downloadTask(const ::ZG6000::StringList& listTaskID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGOPTaskIT_downloadTaskPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_downloadTask(listTaskID, context, cb, cookie);
    }

    bool end_downloadTask(::ZG6000::ListStringMap& listTask, ::ZG6000::ListStringMap& listItem, ::ZG6000::ListStringMap& listAction, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_downloadTask(::ZG6000::ListStringMap& iceP_listTask, ::ZG6000::ListStringMap& iceP_listItem, ::ZG6000::ListStringMap& iceP_listAction, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_downloadTask(const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGOPTaskIT : virtual public ZGOPTaskBase
{
public:

    typedef ZGOPTaskITPrx ProxyType;
    typedef ZGOPTaskITPtr PointerType;

    virtual ~ZGOPTaskIT();

#ifdef ICE_CPP11_COMPILER
    ZGOPTaskIT() = default;
    ZGOPTaskIT(const ZGOPTaskIT&) = default;
    ZGOPTaskIT& operator=(const ZGOPTaskIT&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool getTaskItems(const ::std::string& taskID, StringMap& task, ListStringMap& items, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskItems(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getItemActions(const ::std::string& itemID, StringMap& item, ListStringMap& actions, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getItemActions(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool createTypicalTask(const StringMap& params, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createTypicalTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool getTaskTypeObjects(const StringMap& params, ListStringMap& listObject, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getTaskTypeObjects(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool createSpecialTask(const ::std::string& taskTypeID, const StringList& listObjectID, const StringMap& params, ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createSpecialTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool createCustomTask(const ListStringMap& listItem, const StringMap& params, ::std::string& taskID, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createCustomTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool createUAVTask(const StringMap& task, const ListStringMap& listPreset, const ListStringMap& listItem, const ListStringMap& listAction, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_createUAVTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool editTask(const ::std::string& taskID, const StringMap& task, const ListStringMap& items, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_editTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool convertTask(const ::std::string& taskID, const StringMap& params, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_convertTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool skipItem(const ::std::string& itemID, const StringMap& params, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_skipItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateTask(const ListStringMap& listTask, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateItem(const ListStringMap& listItem, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateItem(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool updateAction(const ListStringMap& listAction, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_updateAction(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual bool downloadTask(const StringList& listTaskID, ListStringMap& listTask, ListStringMap& listItem, ListStringMap& listAction, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_downloadTask(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGOPTaskIT& lhs, const ZGOPTaskIT& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGOPTaskIT& lhs, const ZGOPTaskIT& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskItems.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_getTaskItems.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_getTaskItems : public Callback_ZGOPTaskIT_getTaskItems_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_getTaskItems(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        StringMap iceP_task;
        ListStringMap iceP_items;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskItems(iceP_task, iceP_items, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_task, iceP_items, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskItems.
 */
template<class T> Callback_ZGOPTaskIT_getTaskItemsPtr
newCallback_ZGOPTaskIT_getTaskItems(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_getTaskItems<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskItems.
 */
template<class T> Callback_ZGOPTaskIT_getTaskItemsPtr
newCallback_ZGOPTaskIT_getTaskItems(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_getTaskItems<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskItems.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_getTaskItems.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_getTaskItems : public Callback_ZGOPTaskIT_getTaskItems_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_getTaskItems(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        StringMap iceP_task;
        ListStringMap iceP_items;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskItems(iceP_task, iceP_items, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_task, iceP_items, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskItems.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_getTaskItemsPtr
newCallback_ZGOPTaskIT_getTaskItems(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_getTaskItems<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskItems.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_getTaskItemsPtr
newCallback_ZGOPTaskIT_getTaskItems(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_getTaskItems<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_getItemActions.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_getItemActions.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_getItemActions : public Callback_ZGOPTaskIT_getItemActions_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_getItemActions(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        StringMap iceP_item;
        ListStringMap iceP_actions;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getItemActions(iceP_item, iceP_actions, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_item, iceP_actions, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getItemActions.
 */
template<class T> Callback_ZGOPTaskIT_getItemActionsPtr
newCallback_ZGOPTaskIT_getItemActions(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_getItemActions<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getItemActions.
 */
template<class T> Callback_ZGOPTaskIT_getItemActionsPtr
newCallback_ZGOPTaskIT_getItemActions(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_getItemActions<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_getItemActions.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_getItemActions.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_getItemActions : public Callback_ZGOPTaskIT_getItemActions_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_getItemActions(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        StringMap iceP_item;
        ListStringMap iceP_actions;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getItemActions(iceP_item, iceP_actions, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_item, iceP_actions, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getItemActions.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_getItemActionsPtr
newCallback_ZGOPTaskIT_getItemActions(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_getItemActions<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getItemActions.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_getItemActionsPtr
newCallback_ZGOPTaskIT_getItemActions(T* instance, void (T::*cb)(bool, const StringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_getItemActions<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createTypicalTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createTypicalTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_createTypicalTask : public Callback_ZGOPTaskIT_createTypicalTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_createTypicalTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createTypicalTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createTypicalTask.
 */
template<class T> Callback_ZGOPTaskIT_createTypicalTaskPtr
newCallback_ZGOPTaskIT_createTypicalTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_createTypicalTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createTypicalTask.
 */
template<class T> Callback_ZGOPTaskIT_createTypicalTaskPtr
newCallback_ZGOPTaskIT_createTypicalTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_createTypicalTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createTypicalTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createTypicalTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_createTypicalTask : public Callback_ZGOPTaskIT_createTypicalTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_createTypicalTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createTypicalTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createTypicalTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_createTypicalTaskPtr
newCallback_ZGOPTaskIT_createTypicalTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_createTypicalTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createTypicalTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_createTypicalTaskPtr
newCallback_ZGOPTaskIT_createTypicalTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_createTypicalTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskTypeObjects.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_getTaskTypeObjects.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_getTaskTypeObjects : public Callback_ZGOPTaskIT_getTaskTypeObjects_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_getTaskTypeObjects(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listObject;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskTypeObjects(iceP_listObject, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listObject, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskTypeObjects.
 */
template<class T> Callback_ZGOPTaskIT_getTaskTypeObjectsPtr
newCallback_ZGOPTaskIT_getTaskTypeObjects(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_getTaskTypeObjects<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskTypeObjects.
 */
template<class T> Callback_ZGOPTaskIT_getTaskTypeObjectsPtr
newCallback_ZGOPTaskIT_getTaskTypeObjects(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_getTaskTypeObjects<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskTypeObjects.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_getTaskTypeObjects.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_getTaskTypeObjects : public Callback_ZGOPTaskIT_getTaskTypeObjects_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_getTaskTypeObjects(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listObject;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getTaskTypeObjects(iceP_listObject, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listObject, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskTypeObjects.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_getTaskTypeObjectsPtr
newCallback_ZGOPTaskIT_getTaskTypeObjects(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_getTaskTypeObjects<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_getTaskTypeObjects.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_getTaskTypeObjectsPtr
newCallback_ZGOPTaskIT_getTaskTypeObjects(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_getTaskTypeObjects<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createSpecialTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createSpecialTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_createSpecialTask : public Callback_ZGOPTaskIT_createSpecialTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_createSpecialTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_taskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createSpecialTask(iceP_taskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_taskID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createSpecialTask.
 */
template<class T> Callback_ZGOPTaskIT_createSpecialTaskPtr
newCallback_ZGOPTaskIT_createSpecialTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_createSpecialTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createSpecialTask.
 */
template<class T> Callback_ZGOPTaskIT_createSpecialTaskPtr
newCallback_ZGOPTaskIT_createSpecialTask(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_createSpecialTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createSpecialTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createSpecialTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_createSpecialTask : public Callback_ZGOPTaskIT_createSpecialTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_createSpecialTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_taskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createSpecialTask(iceP_taskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_taskID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createSpecialTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_createSpecialTaskPtr
newCallback_ZGOPTaskIT_createSpecialTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_createSpecialTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createSpecialTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_createSpecialTaskPtr
newCallback_ZGOPTaskIT_createSpecialTask(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_createSpecialTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createCustomTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createCustomTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_createCustomTask : public Callback_ZGOPTaskIT_createCustomTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_createCustomTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_taskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createCustomTask(iceP_taskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_taskID, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createCustomTask.
 */
template<class T> Callback_ZGOPTaskIT_createCustomTaskPtr
newCallback_ZGOPTaskIT_createCustomTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_createCustomTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createCustomTask.
 */
template<class T> Callback_ZGOPTaskIT_createCustomTaskPtr
newCallback_ZGOPTaskIT_createCustomTask(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_createCustomTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createCustomTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createCustomTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_createCustomTask : public Callback_ZGOPTaskIT_createCustomTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ::std::string&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_createCustomTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ::std::string iceP_taskID;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createCustomTask(iceP_taskID, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_taskID, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createCustomTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_createCustomTaskPtr
newCallback_ZGOPTaskIT_createCustomTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_createCustomTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createCustomTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_createCustomTaskPtr
newCallback_ZGOPTaskIT_createCustomTask(T* instance, void (T::*cb)(bool, const ::std::string&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_createCustomTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createUAVTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createUAVTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_createUAVTask : public Callback_ZGOPTaskIT_createUAVTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_createUAVTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createUAVTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createUAVTask.
 */
template<class T> Callback_ZGOPTaskIT_createUAVTaskPtr
newCallback_ZGOPTaskIT_createUAVTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_createUAVTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createUAVTask.
 */
template<class T> Callback_ZGOPTaskIT_createUAVTaskPtr
newCallback_ZGOPTaskIT_createUAVTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_createUAVTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_createUAVTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_createUAVTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_createUAVTask : public Callback_ZGOPTaskIT_createUAVTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_createUAVTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_createUAVTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createUAVTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_createUAVTaskPtr
newCallback_ZGOPTaskIT_createUAVTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_createUAVTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_createUAVTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_createUAVTaskPtr
newCallback_ZGOPTaskIT_createUAVTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_createUAVTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_editTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_editTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_editTask : public Callback_ZGOPTaskIT_editTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_editTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_editTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_editTask.
 */
template<class T> Callback_ZGOPTaskIT_editTaskPtr
newCallback_ZGOPTaskIT_editTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_editTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_editTask.
 */
template<class T> Callback_ZGOPTaskIT_editTaskPtr
newCallback_ZGOPTaskIT_editTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_editTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_editTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_editTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_editTask : public Callback_ZGOPTaskIT_editTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_editTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_editTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_editTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_editTaskPtr
newCallback_ZGOPTaskIT_editTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_editTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_editTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_editTaskPtr
newCallback_ZGOPTaskIT_editTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_editTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_convertTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_convertTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_convertTask : public Callback_ZGOPTaskIT_convertTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_convertTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_convertTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_convertTask.
 */
template<class T> Callback_ZGOPTaskIT_convertTaskPtr
newCallback_ZGOPTaskIT_convertTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_convertTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_convertTask.
 */
template<class T> Callback_ZGOPTaskIT_convertTaskPtr
newCallback_ZGOPTaskIT_convertTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_convertTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_convertTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_convertTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_convertTask : public Callback_ZGOPTaskIT_convertTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_convertTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_convertTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_convertTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_convertTaskPtr
newCallback_ZGOPTaskIT_convertTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_convertTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_convertTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_convertTaskPtr
newCallback_ZGOPTaskIT_convertTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_convertTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_skipItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_skipItem.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_skipItem : public Callback_ZGOPTaskIT_skipItem_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_skipItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_skipItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_skipItem.
 */
template<class T> Callback_ZGOPTaskIT_skipItemPtr
newCallback_ZGOPTaskIT_skipItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_skipItem<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_skipItem.
 */
template<class T> Callback_ZGOPTaskIT_skipItemPtr
newCallback_ZGOPTaskIT_skipItem(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_skipItem<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_skipItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_skipItem.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_skipItem : public Callback_ZGOPTaskIT_skipItem_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_skipItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_skipItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_skipItem.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_skipItemPtr
newCallback_ZGOPTaskIT_skipItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_skipItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_skipItem.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_skipItemPtr
newCallback_ZGOPTaskIT_skipItem(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_skipItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_updateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_updateTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_updateTask : public Callback_ZGOPTaskIT_updateTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_updateTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateTask.
 */
template<class T> Callback_ZGOPTaskIT_updateTaskPtr
newCallback_ZGOPTaskIT_updateTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_updateTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateTask.
 */
template<class T> Callback_ZGOPTaskIT_updateTaskPtr
newCallback_ZGOPTaskIT_updateTask(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_updateTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_updateTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_updateTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_updateTask : public Callback_ZGOPTaskIT_updateTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_updateTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateTask(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_updateTaskPtr
newCallback_ZGOPTaskIT_updateTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_updateTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_updateTaskPtr
newCallback_ZGOPTaskIT_updateTask(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_updateTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_updateItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_updateItem.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_updateItem : public Callback_ZGOPTaskIT_updateItem_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_updateItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateItem.
 */
template<class T> Callback_ZGOPTaskIT_updateItemPtr
newCallback_ZGOPTaskIT_updateItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_updateItem<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateItem.
 */
template<class T> Callback_ZGOPTaskIT_updateItemPtr
newCallback_ZGOPTaskIT_updateItem(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_updateItem<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_updateItem.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_updateItem.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_updateItem : public Callback_ZGOPTaskIT_updateItem_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_updateItem(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateItem(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateItem.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_updateItemPtr
newCallback_ZGOPTaskIT_updateItem(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_updateItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateItem.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_updateItemPtr
newCallback_ZGOPTaskIT_updateItem(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_updateItem<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_updateAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_updateAction.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_updateAction : public Callback_ZGOPTaskIT_updateAction_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_updateAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateAction.
 */
template<class T> Callback_ZGOPTaskIT_updateActionPtr
newCallback_ZGOPTaskIT_updateAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_updateAction<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateAction.
 */
template<class T> Callback_ZGOPTaskIT_updateActionPtr
newCallback_ZGOPTaskIT_updateAction(T* instance, void (T::*cb)(bool, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_updateAction<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_updateAction.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_updateAction.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_updateAction : public Callback_ZGOPTaskIT_updateAction_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_updateAction(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_updateAction(iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateAction.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_updateActionPtr
newCallback_ZGOPTaskIT_updateAction(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_updateAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_updateAction.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_updateActionPtr
newCallback_ZGOPTaskIT_updateAction(T* instance, void (T::*cb)(bool, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_updateAction<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_downloadTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_downloadTask.
 */
template<class T>
class CallbackNC_ZGOPTaskIT_downloadTask : public Callback_ZGOPTaskIT_downloadTask_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGOPTaskIT_downloadTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listTask;
        ListStringMap iceP_listItem;
        ListStringMap iceP_listAction;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_downloadTask(iceP_listTask, iceP_listItem, iceP_listAction, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listTask, iceP_listItem, iceP_listAction, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_downloadTask.
 */
template<class T> Callback_ZGOPTaskIT_downloadTaskPtr
newCallback_ZGOPTaskIT_downloadTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_downloadTask<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_downloadTask.
 */
template<class T> Callback_ZGOPTaskIT_downloadTaskPtr
newCallback_ZGOPTaskIT_downloadTask(T* instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGOPTaskIT_downloadTask<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGOPTaskIT::begin_downloadTask.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGOPTaskIT_downloadTask.
 */
template<class T, typename CT>
class Callback_ZGOPTaskIT_downloadTask : public Callback_ZGOPTaskIT_downloadTask_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGOPTaskIT_downloadTask(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGOPTaskITPrx proxy = ZGOPTaskITPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listTask;
        ListStringMap iceP_listItem;
        ListStringMap iceP_listAction;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_downloadTask(iceP_listTask, iceP_listItem, iceP_listAction, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listTask, iceP_listItem, iceP_listAction, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_downloadTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_downloadTaskPtr
newCallback_ZGOPTaskIT_downloadTask(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_downloadTask<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGOPTaskIT::begin_downloadTask.
 */
template<class T, typename CT> Callback_ZGOPTaskIT_downloadTaskPtr
newCallback_ZGOPTaskIT_downloadTask(T* instance, void (T::*cb)(bool, const ListStringMap&, const ListStringMap&, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGOPTaskIT_downloadTask<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

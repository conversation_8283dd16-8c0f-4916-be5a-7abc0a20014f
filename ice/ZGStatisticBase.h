//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGStatisticBase.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGStatisticBase_h__
#define __ZGStatisticBase_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGStatisticBase;
class ZGStatisticBasePrx;

}

namespace ZG6000
{

class ZGStatisticBase : public virtual ZGServerBase
{
public:

    using ProxyType = ZGStatisticBasePrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual void statistic(::std::string id, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_statistic(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void statisticBatch(StringList listID, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_statisticBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void statisticStart(::std::string id, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_statisticStart(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void statisticStartAndCalc(::std::string id, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_statisticStartAndCalc(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void statisticStartBatch(StringList listID, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_statisticStartBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGStatisticBasePrx : public virtual ::Ice::Proxy<ZGStatisticBasePrx, ZGServerBasePrx>
{
public:

    void statistic(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGStatisticBasePrx::_iceI_statistic, id, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto statisticAsync(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGStatisticBasePrx::_iceI_statistic, id, context);
    }

    ::std::function<void()>
    statisticAsync(const ::std::string& id,
                   ::std::function<void()> response,
                   ::std::function<void(::std::exception_ptr)> ex = nullptr,
                   ::std::function<void(bool)> sent = nullptr,
                   const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGStatisticBasePrx::_iceI_statistic, id, context);
    }

    /// \cond INTERNAL
    void _iceI_statistic(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    void statisticBatch(const StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGStatisticBasePrx::_iceI_statisticBatch, listID, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto statisticBatchAsync(const StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGStatisticBasePrx::_iceI_statisticBatch, listID, context);
    }

    ::std::function<void()>
    statisticBatchAsync(const StringList& listID,
                        ::std::function<void()> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGStatisticBasePrx::_iceI_statisticBatch, listID, context);
    }

    /// \cond INTERNAL
    void _iceI_statisticBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const StringList&, const ::Ice::Context&);
    /// \endcond

    void statisticStart(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGStatisticBasePrx::_iceI_statisticStart, id, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto statisticStartAsync(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGStatisticBasePrx::_iceI_statisticStart, id, context);
    }

    ::std::function<void()>
    statisticStartAsync(const ::std::string& id,
                        ::std::function<void()> response,
                        ::std::function<void(::std::exception_ptr)> ex = nullptr,
                        ::std::function<void(bool)> sent = nullptr,
                        const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGStatisticBasePrx::_iceI_statisticStart, id, context);
    }

    /// \cond INTERNAL
    void _iceI_statisticStart(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    void statisticStartAndCalc(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGStatisticBasePrx::_iceI_statisticStartAndCalc, id, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto statisticStartAndCalcAsync(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGStatisticBasePrx::_iceI_statisticStartAndCalc, id, context);
    }

    ::std::function<void()>
    statisticStartAndCalcAsync(const ::std::string& id,
                               ::std::function<void()> response,
                               ::std::function<void(::std::exception_ptr)> ex = nullptr,
                               ::std::function<void(bool)> sent = nullptr,
                               const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGStatisticBasePrx::_iceI_statisticStartAndCalc, id, context);
    }

    /// \cond INTERNAL
    void _iceI_statisticStartAndCalc(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    void statisticStartBatch(const StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &ZGStatisticBasePrx::_iceI_statisticStartBatch, listID, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto statisticStartBatchAsync(const StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &ZGStatisticBasePrx::_iceI_statisticStartBatch, listID, context);
    }

    ::std::function<void()>
    statisticStartBatchAsync(const StringList& listID,
                             ::std::function<void()> response,
                             ::std::function<void(::std::exception_ptr)> ex = nullptr,
                             ::std::function<void(bool)> sent = nullptr,
                             const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(std::move(response), std::move(ex), std::move(sent), this, &ZG6000::ZGStatisticBasePrx::_iceI_statisticStartBatch, listID, context);
    }

    /// \cond INTERNAL
    void _iceI_statisticStartBatch(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const StringList&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGStatisticBasePrx() = default;
    friend ::std::shared_ptr<ZGStatisticBasePrx> IceInternal::createProxy<ZGStatisticBasePrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGStatisticBasePtr = ::std::shared_ptr<ZGStatisticBase>;
using ZGStatisticBasePrxPtr = ::std::shared_ptr<ZGStatisticBasePrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGStatisticBase;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGStatisticBase>&);
::IceProxy::Ice::Object* upCast(ZGStatisticBase*);
/// \endcond

}

}

namespace ZG6000
{

class ZGStatisticBase;
/// \cond INTERNAL
::Ice::Object* upCast(ZGStatisticBase*);
/// \endcond
typedef ::IceInternal::Handle< ZGStatisticBase> ZGStatisticBasePtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGStatisticBase> ZGStatisticBasePrx;
typedef ZGStatisticBasePrx ZGStatisticBasePrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGStatisticBasePtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statistic.
 */
class Callback_ZGStatisticBase_statistic_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGStatisticBase_statistic_Base> Callback_ZGStatisticBase_statisticPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticBatch.
 */
class Callback_ZGStatisticBase_statisticBatch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGStatisticBase_statisticBatch_Base> Callback_ZGStatisticBase_statisticBatchPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticStart.
 */
class Callback_ZGStatisticBase_statisticStart_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGStatisticBase_statisticStart_Base> Callback_ZGStatisticBase_statisticStartPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticStartAndCalc.
 */
class Callback_ZGStatisticBase_statisticStartAndCalc_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGStatisticBase_statisticStartAndCalc_Base> Callback_ZGStatisticBase_statisticStartAndCalcPtr;

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticStartBatch.
 */
class Callback_ZGStatisticBase_statisticStartBatch_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGStatisticBase_statisticStartBatch_Base> Callback_ZGStatisticBase_statisticStartBatchPtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGStatisticBase : public virtual ::Ice::Proxy<ZGStatisticBase, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    void statistic(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_statistic(_iceI_begin_statistic(id, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_statistic(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_statistic(id, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_statistic(const ::std::string& id, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statistic(id, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statistic(const ::std::string& id, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statistic(id, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statistic(const ::std::string& id, const ::ZG6000::Callback_ZGStatisticBase_statisticPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statistic(id, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statistic(const ::std::string& id, const ::Ice::Context& context, const ::ZG6000::Callback_ZGStatisticBase_statisticPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statistic(id, context, cb, cookie);
    }

    void end_statistic(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_statistic(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    void statisticBatch(const ::ZG6000::StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_statisticBatch(_iceI_begin_statisticBatch(listID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_statisticBatch(const ::ZG6000::StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_statisticBatch(listID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_statisticBatch(const ::ZG6000::StringList& listID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticBatch(listID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticBatch(const ::ZG6000::StringList& listID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticBatch(listID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticBatch(const ::ZG6000::StringList& listID, const ::ZG6000::Callback_ZGStatisticBase_statisticBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticBatch(listID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticBatch(const ::ZG6000::StringList& listID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGStatisticBase_statisticBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticBatch(listID, context, cb, cookie);
    }

    void end_statisticBatch(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_statisticBatch(const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    void statisticStart(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_statisticStart(_iceI_begin_statisticStart(id, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_statisticStart(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_statisticStart(id, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_statisticStart(const ::std::string& id, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStart(id, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticStart(const ::std::string& id, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStart(id, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticStart(const ::std::string& id, const ::ZG6000::Callback_ZGStatisticBase_statisticStartPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStart(id, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticStart(const ::std::string& id, const ::Ice::Context& context, const ::ZG6000::Callback_ZGStatisticBase_statisticStartPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStart(id, context, cb, cookie);
    }

    void end_statisticStart(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_statisticStart(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    void statisticStartAndCalc(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_statisticStartAndCalc(_iceI_begin_statisticStartAndCalc(id, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_statisticStartAndCalc(const ::std::string& id, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_statisticStartAndCalc(id, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_statisticStartAndCalc(const ::std::string& id, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStartAndCalc(id, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticStartAndCalc(const ::std::string& id, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStartAndCalc(id, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticStartAndCalc(const ::std::string& id, const ::ZG6000::Callback_ZGStatisticBase_statisticStartAndCalcPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStartAndCalc(id, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticStartAndCalc(const ::std::string& id, const ::Ice::Context& context, const ::ZG6000::Callback_ZGStatisticBase_statisticStartAndCalcPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStartAndCalc(id, context, cb, cookie);
    }

    void end_statisticStartAndCalc(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_statisticStartAndCalc(const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    void statisticStartBatch(const ::ZG6000::StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_statisticStartBatch(_iceI_begin_statisticStartBatch(listID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_statisticStartBatch(const ::ZG6000::StringList& listID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_statisticStartBatch(listID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_statisticStartBatch(const ::ZG6000::StringList& listID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStartBatch(listID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticStartBatch(const ::ZG6000::StringList& listID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStartBatch(listID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticStartBatch(const ::ZG6000::StringList& listID, const ::ZG6000::Callback_ZGStatisticBase_statisticStartBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStartBatch(listID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_statisticStartBatch(const ::ZG6000::StringList& listID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGStatisticBase_statisticStartBatchPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_statisticStartBatch(listID, context, cb, cookie);
    }

    void end_statisticStartBatch(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_statisticStartBatch(const ::ZG6000::StringList&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGStatisticBase : virtual public ZGServerBase
{
public:

    typedef ZGStatisticBasePrx ProxyType;
    typedef ZGStatisticBasePtr PointerType;

    virtual ~ZGStatisticBase();

#ifdef ICE_CPP11_COMPILER
    ZGStatisticBase() = default;
    ZGStatisticBase(const ZGStatisticBase&) = default;
    ZGStatisticBase& operator=(const ZGStatisticBase&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual void statistic(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_statistic(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void statisticBatch(const StringList& listID, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_statisticBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void statisticStart(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_statisticStart(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void statisticStartAndCalc(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_statisticStartAndCalc(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    virtual void statisticStartBatch(const StringList& listID, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_statisticStartBatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGStatisticBase& lhs, const ZGStatisticBase& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGStatisticBase& lhs, const ZGStatisticBase& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statistic.
 */
template<class T>
class CallbackNC_ZGStatisticBase_statistic : public Callback_ZGStatisticBase_statistic_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGStatisticBase_statistic(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 */
template<class T> Callback_ZGStatisticBase_statisticPtr
newCallback_ZGStatisticBase_statistic(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statistic<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 */
template<class T> Callback_ZGStatisticBase_statisticPtr
newCallback_ZGStatisticBase_statistic(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statistic<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 */
template<class T> Callback_ZGStatisticBase_statisticPtr
newCallback_ZGStatisticBase_statistic(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statistic<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 */
template<class T> Callback_ZGStatisticBase_statisticPtr
newCallback_ZGStatisticBase_statistic(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statistic<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statistic.
 */
template<class T, typename CT>
class Callback_ZGStatisticBase_statistic : public Callback_ZGStatisticBase_statistic_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGStatisticBase_statistic(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticPtr
newCallback_ZGStatisticBase_statistic(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statistic<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticPtr
newCallback_ZGStatisticBase_statistic(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statistic<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticPtr
newCallback_ZGStatisticBase_statistic(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statistic<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statistic.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticPtr
newCallback_ZGStatisticBase_statistic(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statistic<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticBatch.
 */
template<class T>
class CallbackNC_ZGStatisticBase_statisticBatch : public Callback_ZGStatisticBase_statisticBatch_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGStatisticBase_statisticBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 */
template<class T> Callback_ZGStatisticBase_statisticBatchPtr
newCallback_ZGStatisticBase_statisticBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 */
template<class T> Callback_ZGStatisticBase_statisticBatchPtr
newCallback_ZGStatisticBase_statisticBatch(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticBatch<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 */
template<class T> Callback_ZGStatisticBase_statisticBatchPtr
newCallback_ZGStatisticBase_statisticBatch(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 */
template<class T> Callback_ZGStatisticBase_statisticBatchPtr
newCallback_ZGStatisticBase_statisticBatch(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticBatch<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticBatch.
 */
template<class T, typename CT>
class Callback_ZGStatisticBase_statisticBatch : public Callback_ZGStatisticBase_statisticBatch_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGStatisticBase_statisticBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticBatchPtr
newCallback_ZGStatisticBase_statisticBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticBatchPtr
newCallback_ZGStatisticBase_statisticBatch(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticBatch<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticBatchPtr
newCallback_ZGStatisticBase_statisticBatch(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticBatch.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticBatchPtr
newCallback_ZGStatisticBase_statisticBatch(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticBatch<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticStart.
 */
template<class T>
class CallbackNC_ZGStatisticBase_statisticStart : public Callback_ZGStatisticBase_statisticStart_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGStatisticBase_statisticStart(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 */
template<class T> Callback_ZGStatisticBase_statisticStartPtr
newCallback_ZGStatisticBase_statisticStart(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStart<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 */
template<class T> Callback_ZGStatisticBase_statisticStartPtr
newCallback_ZGStatisticBase_statisticStart(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStart<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 */
template<class T> Callback_ZGStatisticBase_statisticStartPtr
newCallback_ZGStatisticBase_statisticStart(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStart<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 */
template<class T> Callback_ZGStatisticBase_statisticStartPtr
newCallback_ZGStatisticBase_statisticStart(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStart<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticStart.
 */
template<class T, typename CT>
class Callback_ZGStatisticBase_statisticStart : public Callback_ZGStatisticBase_statisticStart_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGStatisticBase_statisticStart(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartPtr
newCallback_ZGStatisticBase_statisticStart(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStart<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartPtr
newCallback_ZGStatisticBase_statisticStart(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStart<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartPtr
newCallback_ZGStatisticBase_statisticStart(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStart<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStart.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartPtr
newCallback_ZGStatisticBase_statisticStart(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStart<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticStartAndCalc.
 */
template<class T>
class CallbackNC_ZGStatisticBase_statisticStartAndCalc : public Callback_ZGStatisticBase_statisticStartAndCalc_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGStatisticBase_statisticStartAndCalc(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 */
template<class T> Callback_ZGStatisticBase_statisticStartAndCalcPtr
newCallback_ZGStatisticBase_statisticStartAndCalc(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStartAndCalc<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 */
template<class T> Callback_ZGStatisticBase_statisticStartAndCalcPtr
newCallback_ZGStatisticBase_statisticStartAndCalc(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStartAndCalc<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 */
template<class T> Callback_ZGStatisticBase_statisticStartAndCalcPtr
newCallback_ZGStatisticBase_statisticStartAndCalc(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStartAndCalc<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 */
template<class T> Callback_ZGStatisticBase_statisticStartAndCalcPtr
newCallback_ZGStatisticBase_statisticStartAndCalc(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStartAndCalc<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticStartAndCalc.
 */
template<class T, typename CT>
class Callback_ZGStatisticBase_statisticStartAndCalc : public Callback_ZGStatisticBase_statisticStartAndCalc_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGStatisticBase_statisticStartAndCalc(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartAndCalcPtr
newCallback_ZGStatisticBase_statisticStartAndCalc(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStartAndCalc<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartAndCalcPtr
newCallback_ZGStatisticBase_statisticStartAndCalc(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStartAndCalc<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartAndCalcPtr
newCallback_ZGStatisticBase_statisticStartAndCalc(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStartAndCalc<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartAndCalc.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartAndCalcPtr
newCallback_ZGStatisticBase_statisticStartAndCalc(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStartAndCalc<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticStartBatch.
 */
template<class T>
class CallbackNC_ZGStatisticBase_statisticStartBatch : public Callback_ZGStatisticBase_statisticStartBatch_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_ZGStatisticBase_statisticStartBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 */
template<class T> Callback_ZGStatisticBase_statisticStartBatchPtr
newCallback_ZGStatisticBase_statisticStartBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStartBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 */
template<class T> Callback_ZGStatisticBase_statisticStartBatchPtr
newCallback_ZGStatisticBase_statisticStartBatch(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStartBatch<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 */
template<class T> Callback_ZGStatisticBase_statisticStartBatchPtr
newCallback_ZGStatisticBase_statisticStartBatch(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStartBatch<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 */
template<class T> Callback_ZGStatisticBase_statisticStartBatchPtr
newCallback_ZGStatisticBase_statisticStartBatch(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGStatisticBase_statisticStartBatch<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGStatisticBase_statisticStartBatch.
 */
template<class T, typename CT>
class Callback_ZGStatisticBase_statisticStartBatch : public Callback_ZGStatisticBase_statisticStartBatch_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_ZGStatisticBase_statisticStartBatch(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartBatchPtr
newCallback_ZGStatisticBase_statisticStartBatch(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStartBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartBatchPtr
newCallback_ZGStatisticBase_statisticStartBatch(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStartBatch<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartBatchPtr
newCallback_ZGStatisticBase_statisticStartBatch(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStartBatch<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGStatisticBase::begin_statisticStartBatch.
 */
template<class T, typename CT> Callback_ZGStatisticBase_statisticStartBatchPtr
newCallback_ZGStatisticBase_statisticStartBatch(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGStatisticBase_statisticStartBatch<T, CT>(instance, 0, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

//
// Copyright (c) ZeroC, Inc. All rights reserved.
//
//
// Ice version 3.7.6
//
// <auto-generated>
//
// Generated from file `ZGMPRealWarn.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __ZGMPRealWarn_h__
#define __ZGMPRealWarn_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <ZGServerBase.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 >= 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 6
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace ZG6000
{

class ZGMPRealWarn;
class ZGMPRealWarnPrx;

}

namespace ZG6000
{

class ZGMPRealWarn : public virtual ZGServerBase
{
public:

    using ProxyType = ZGMPRealWarnPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    /**
     * Encapsulates the results of a call to getRealWarnByAppNode.
     */
    struct GetRealWarnByAppNodeResult
    {
        bool returnValue;
        ListStringMap listRealWarn;
        ErrorInfo e;
    };

    virtual bool getRealWarnByAppNode(::std::string appNodeID, ::std::string subsystemID, ListStringMap& listRealWarn, ErrorInfo& e, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_getRealWarnByAppNode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace ZG6000
{

class ZGMPRealWarnPrx : public virtual ::Ice::Proxy<ZGMPRealWarnPrx, ZGServerBasePrx>
{
public:

    bool getRealWarnByAppNode(const ::std::string& appNodeID, const ::std::string& subsystemID, ListStringMap& listRealWarn, ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _result = _makePromiseOutgoing<ZGMPRealWarn::GetRealWarnByAppNodeResult>(true, this, &ZGMPRealWarnPrx::_iceI_getRealWarnByAppNode, appNodeID, subsystemID, context).get();
        listRealWarn = ::std::move(_result.listRealWarn);
        e = ::std::move(_result.e);
        return _result.returnValue;
    }

    template<template<typename> class P = ::std::promise>
    auto getRealWarnByAppNodeAsync(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<ZGMPRealWarn::GetRealWarnByAppNodeResult>>().get_future())
    {
        return _makePromiseOutgoing<ZGMPRealWarn::GetRealWarnByAppNodeResult, P>(false, this, &ZGMPRealWarnPrx::_iceI_getRealWarnByAppNode, appNodeID, subsystemID, context);
    }

    ::std::function<void()>
    getRealWarnByAppNodeAsync(const ::std::string& appNodeID, const ::std::string& subsystemID,
                              ::std::function<void(bool, ::ZG6000::ListStringMap, ::ZG6000::ErrorInfo)> response,
                              ::std::function<void(::std::exception_ptr)> ex = nullptr,
                              ::std::function<void(bool)> sent = nullptr,
                              const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        auto _responseCb = [response](ZGMPRealWarn::GetRealWarnByAppNodeResult&& _result)
        {
            response(_result.returnValue, ::std::move(_result.listRealWarn), ::std::move(_result.e));
        };
        return _makeLamdaOutgoing<ZGMPRealWarn::GetRealWarnByAppNodeResult>(std::move(_responseCb), std::move(ex), std::move(sent), this, &ZG6000::ZGMPRealWarnPrx::_iceI_getRealWarnByAppNode, appNodeID, subsystemID, context);
    }

    /// \cond INTERNAL
    void _iceI_getRealWarnByAppNode(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<ZGMPRealWarn::GetRealWarnByAppNodeResult>>&, const ::std::string&, const ::std::string&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    ZGMPRealWarnPrx() = default;
    friend ::std::shared_ptr<ZGMPRealWarnPrx> IceInternal::createProxy<ZGMPRealWarnPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

/// \cond INTERNAL
namespace ZG6000
{

using ZGMPRealWarnPtr = ::std::shared_ptr<ZGMPRealWarn>;
using ZGMPRealWarnPrxPtr = ::std::shared_ptr<ZGMPRealWarnPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace ZG6000
{

class ZGMPRealWarn;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ZGMPRealWarn>&);
::IceProxy::Ice::Object* upCast(ZGMPRealWarn*);
/// \endcond

}

}

namespace ZG6000
{

class ZGMPRealWarn;
/// \cond INTERNAL
::Ice::Object* upCast(ZGMPRealWarn*);
/// \endcond
typedef ::IceInternal::Handle< ZGMPRealWarn> ZGMPRealWarnPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::ZG6000::ZGMPRealWarn> ZGMPRealWarnPrx;
typedef ZGMPRealWarnPrx ZGMPRealWarnPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(ZGMPRealWarnPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace ZG6000
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::ZG6000::ZGMPRealWarn::begin_getRealWarnByAppNode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRealWarn_getRealWarnByAppNode.
 */
class Callback_ZGMPRealWarn_getRealWarnByAppNode_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_ZGMPRealWarn_getRealWarnByAppNode_Base> Callback_ZGMPRealWarn_getRealWarnByAppNodePtr;

}

namespace IceProxy
{

namespace ZG6000
{

class ZGMPRealWarn : public virtual ::Ice::Proxy<ZGMPRealWarn, ::IceProxy::ZG6000::ZGServerBase>
{
public:

    bool getRealWarnByAppNode(const ::std::string& appNodeID, const ::std::string& subsystemID, ::ZG6000::ListStringMap& listRealWarn, ::ZG6000::ErrorInfo& e, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return end_getRealWarnByAppNode(listRealWarn, e, _iceI_begin_getRealWarnByAppNode(appNodeID, subsystemID, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_getRealWarnByAppNode(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_getRealWarnByAppNode(appNodeID, subsystemID, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_getRealWarnByAppNode(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRealWarnByAppNode(appNodeID, subsystemID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getRealWarnByAppNode(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRealWarnByAppNode(appNodeID, subsystemID, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getRealWarnByAppNode(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::ZG6000::Callback_ZGMPRealWarn_getRealWarnByAppNodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRealWarnByAppNode(appNodeID, subsystemID, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_getRealWarnByAppNode(const ::std::string& appNodeID, const ::std::string& subsystemID, const ::Ice::Context& context, const ::ZG6000::Callback_ZGMPRealWarn_getRealWarnByAppNodePtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_getRealWarnByAppNode(appNodeID, subsystemID, context, cb, cookie);
    }

    bool end_getRealWarnByAppNode(::ZG6000::ListStringMap& listRealWarn, ::ZG6000::ErrorInfo& e, const ::Ice::AsyncResultPtr& result);
    /// \cond INTERNAL

    void _iceI_end_getRealWarnByAppNode(::ZG6000::ListStringMap& iceP_listRealWarn, ::ZG6000::ErrorInfo& iceP_e, bool& ret, const ::Ice::AsyncResultPtr&);
    /// \endcond

private:

    ::Ice::AsyncResultPtr _iceI_begin_getRealWarnByAppNode(const ::std::string&, const ::std::string&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace ZG6000
{

class ZGMPRealWarn : virtual public ZGServerBase
{
public:

    typedef ZGMPRealWarnPrx ProxyType;
    typedef ZGMPRealWarnPtr PointerType;

    virtual ~ZGMPRealWarn();

#ifdef ICE_CPP11_COMPILER
    ZGMPRealWarn() = default;
    ZGMPRealWarn(const ZGMPRealWarn&) = default;
    ZGMPRealWarn& operator=(const ZGMPRealWarn&) = default;
#endif

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual bool getRealWarnByAppNode(const ::std::string& appNodeID, const ::std::string& subsystemID, ListStringMap& listRealWarn, ErrorInfo& e, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_getRealWarnByAppNode(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const ZGMPRealWarn& lhs, const ZGMPRealWarn& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const ZGMPRealWarn& lhs, const ZGMPRealWarn& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

}
/// \endcond

namespace ZG6000
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::ZG6000::ZGMPRealWarn::begin_getRealWarnByAppNode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRealWarn_getRealWarnByAppNode.
 */
template<class T>
class CallbackNC_ZGMPRealWarn_getRealWarnByAppNode : public Callback_ZGMPRealWarn_getRealWarnByAppNode_Base, public ::IceInternal::TwowayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&);

    CallbackNC_ZGMPRealWarn_getRealWarnByAppNode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallbackNC<T>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRealWarnPrx proxy = ZGMPRealWarnPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listRealWarn;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRealWarnByAppNode(iceP_listRealWarn, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::CallbackNC<T>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::CallbackNC<T>::_callback.get()->*_response)(ret, iceP_listRealWarn, iceP_e);
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRealWarn::begin_getRealWarnByAppNode.
 */
template<class T> Callback_ZGMPRealWarn_getRealWarnByAppNodePtr
newCallback_ZGMPRealWarn_getRealWarnByAppNode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRealWarn_getRealWarnByAppNode<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRealWarn::begin_getRealWarnByAppNode.
 */
template<class T> Callback_ZGMPRealWarn_getRealWarnByAppNodePtr
newCallback_ZGMPRealWarn_getRealWarnByAppNode(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_ZGMPRealWarn_getRealWarnByAppNode<T>(instance, cb, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::ZG6000::ZGMPRealWarn::begin_getRealWarnByAppNode.
 * Create a wrapper instance by calling ::ZG6000::newCallback_ZGMPRealWarn_getRealWarnByAppNode.
 */
template<class T, typename CT>
class Callback_ZGMPRealWarn_getRealWarnByAppNode : public Callback_ZGMPRealWarn_getRealWarnByAppNode_Base, public ::IceInternal::TwowayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(bool, const ListStringMap&, const ErrorInfo&, const CT&);

    Callback_ZGMPRealWarn_getRealWarnByAppNode(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::TwowayCallback<T, CT>(obj, cb != 0, excb, sentcb), _response(cb)
    {
    }

    /// \cond INTERNAL
    virtual void completed(const ::Ice::AsyncResultPtr& result) const
    {
        ZGMPRealWarnPrx proxy = ZGMPRealWarnPrx::uncheckedCast(result->getProxy());
        ListStringMap iceP_listRealWarn;
        ErrorInfo iceP_e;
        bool ret;
        try
        {
            ret = proxy->end_getRealWarnByAppNode(iceP_listRealWarn, iceP_e, result);
        }
        catch(const ::Ice::Exception& ex)
        {
            ::IceInternal::Callback<T, CT>::exception(result, ex);
            return;
        }
        if(_response)
        {
            (::IceInternal::Callback<T, CT>::_callback.get()->*_response)(ret, iceP_listRealWarn, iceP_e, CT::dynamicCast(result->getCookie()));
        }
    }
    /// \endcond

private:

    Response _response;
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRealWarn::begin_getRealWarnByAppNode.
 */
template<class T, typename CT> Callback_ZGMPRealWarn_getRealWarnByAppNodePtr
newCallback_ZGMPRealWarn_getRealWarnByAppNode(const IceUtil::Handle<T>& instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRealWarn_getRealWarnByAppNode<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::ZG6000::ZGMPRealWarn::begin_getRealWarnByAppNode.
 */
template<class T, typename CT> Callback_ZGMPRealWarn_getRealWarnByAppNodePtr
newCallback_ZGMPRealWarn_getRealWarnByAppNode(T* instance, void (T::*cb)(bool, const ListStringMap&, const ErrorInfo&, const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_ZGMPRealWarn_getRealWarnByAppNode<T, CT>(instance, cb, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif

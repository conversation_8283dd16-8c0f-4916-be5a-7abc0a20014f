#include "ZGSPModifyOnlineMng.h"

#include <QMutexLocker>
#include <QRandomGenerator>
#include <QtConcurrent>

#include "ZGDebugMng.h"
#include "ZGPubFun.h"
#include "ZGRuntime.h"
#include "ZGJson.h"
#include "ZGUtils.h"
#include "zgerror/ZGSPModifyOnlineError.h"
#include "ZGMqttClient.h"

static ZGSPModifyOnlineMng* g_pModifyOnlineMng = nullptr;

ZGSPModifyOnlineMng* ZGSPModifyOnlineMng::instance()
{
	if (g_pModifyOnlineMng == nullptr)
		g_pModifyOnlineMng = new ZGSPModifyOnlineMng;
	return g_pModifyOnlineMng;
}

void ZGSPModifyOnlineMng::init()
{
    ZGLOG_INFO("ZGSPModifyOnline init start...");
	initEvents();
	initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initMaintainTable())
    {
	    ZGLOG_ERROR("initMaintainTable error.");
		QThread::msleep(1000);
    }
    while (!updateTableParam())
    {
        ZGLOG_ERROR("updateTableParam error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initSyncTable())
    {
        ZGLOG_ERROR("initSyncTable error.");
        QThread::msleep(m_initInterval * 1000);
    }
    ZGLOG_INFO("getDBDataProxy");
    while ((m_dbDataProxy = getDBDataProxy()) == nullptr)
    {
        ZGLOG_ERROR("getDBDataProxy error.");
        QThread::msleep(m_initInterval * 1000);
    }
    ZGLOG_INFO("getRTDataProxy");
    while ((m_rtDataProxy = getRTDataProxy()) == nullptr)
    {
        ZGLOG_ERROR("getRTDataProxy error.");
        QThread::msleep(m_initInterval * 1000);
    }
    ZGLOG_INFO("initMqttClient");
    while (!initMqttClient())
    {
        ZGLOG_ERROR("initMqttClient error.");
        QThread::sleep(m_initInterval);
    }
    m_checkTimer.start(m_checkInterval * 1000);
    m_initialized = true;
    ZGLOG_INFO("ZGSPModifyOnline init finished");
    start();
}

bool ZGSPModifyOnlineMng::checkDBProxyValid(ZG6000::ErrorInfo& e)
{
	if (m_dbDataProxy == nullptr)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
		e.errDetail = QStringLiteral("数据库服务代理对象为空").toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGSPModifyOnlineMng::checkRTProxyValid(ZG6000::ErrorInfo& e)
{
	if (m_rtDataProxy == nullptr)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
		e.errDetail = QStringLiteral("实时库服务代理对象为空").toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

ZG6000::ListStringMap ZGSPModifyOnlineMng::getChangedRecords(ZG6000::ErrorInfo& e)
{
	ZG6000::ListStringMap listRecord;
	try
	{
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
        {
            if (!m_dbDataProxy->execQuerySqlToListMap("SELECT * FROM sp_maintain_records LIMIT 1000;", listRecord, e))
                ZGLOG_ERROR(e);
        }
        else if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
        {
            if (!m_dbDataProxy->execQuerySqlToListMap("SELECT * FROM sp_maintain_records ORDER BY id OFFSET 0 ROWS FETCH NEXT 1000 ROWS ONLY;", listRecord, e))
                ZGLOG_ERROR(e);
        }
	}
	catch (const Ice::Exception& ex)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
		e.errDetail = ex.what();
		ZGLOG_ERROR(e);
	}
	return listRecord;
}

bool ZGSPModifyOnlineMng::isUpdateDataID(const ZG6000::StringMap& record)
{
    try
    {
        return ((ZGUtils::get(record, "operator") == "2") && (ZGUtils::get(record, "oldID") != ZGUtils::get(record, "newID")));
    }
    catch (const std::exception& e)
    {
		ZGLOG_ERROR(e.what());
		return false;
    }
}

bool ZGSPModifyOnlineMng::doSyncData(ZG6000::ErrorInfo& e)
{
	if (m_listBatchData.empty())
        return true;
    ZGLOG_TRACE(QString("m_listBatchData size: %1").arg(m_listBatchData.size()));
	const auto& [tableName, fields, oper] = m_batchTable;
    bool success;
	if (oper == "1")
        success = doSyncInsertData(tableName, e);
    else if (oper == "2")
        success = doSyncUpdateData(tableName, fields, e);
    else if (oper == "3")
        success = doSyncDeleteData(tableName, e);
    else
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("无效的操作类型'%1'").arg(oper.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (success)
    {
        m_listBatchData.clear();
        success = deleteRecords(e);
    }
    return success;
}

bool ZGSPModifyOnlineMng::doSyncInsertData(const std::string& tableName, ZG6000::ErrorInfo& e)
{
    Output output("doSyncInsertData");
	ZG6000::StringList listDeletedID, listID;
	std::string ids;
	for (auto& record : m_listBatchData)
	{
		listDeletedID.push_back(record["id"]);
		listID.push_back(record["newID"]);
		ids += "'" + record["newID"] + "',";
	}
	ids.pop_back();
	QString sql = QString("SELECT * FROM %1 WHERE id IN (%2)").arg(tableName.c_str()).arg(ids.c_str());
	ZG6000::ListStringMap listRecord;
	if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
		e.errDetail = QStringLiteral("获取表'%1'数据失败").arg(tableName.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
    // 因为插入的记录可能随后会被删除，因此不能保证取到的记录数与维护表中的记录数相同
    if (listRecord.empty())
    {
        std::move(listDeletedID.begin(), listDeletedID.end(), std::back_inserter(m_listDeletedID));
        return true;
    }
    ZG6000::StringList listRealID;
    for (auto& record: listRecord)
    {
        listRealID.push_back(record["id"]);
    }
    if (!ZGProxyCommon::minsertDataByID(tableName, listRealID, listRecord))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
		e.errDetail = QStringLiteral("插入实时表'%1'数据失败").arg(tableName.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	std::move(listDeletedID.begin(), listDeletedID.end(), std::back_inserter(m_listDeletedID));
    if (m_setSyncTable.find(tableName) != m_setSyncTable.end())
    {
        const auto& json = ZGJson::convertToJson(listRecord);
        m_pMqttClient->sendPublish("SyncData/insert", extendJson(tableName, json));
        updateTableVersion(tableName);
    }
	return true;
}

bool ZGSPModifyOnlineMng::doSyncDeleteData(const std::string& tableName, ZG6000::ErrorInfo& e)
{
    Output output("doSyncDeleteData");
	ZG6000::StringList listDeletedID, listID;
	std::string ids;
	for (auto& record : m_listBatchData)
	{
		listDeletedID.push_back(record["id"]);
		listID.push_back(record["oldID"]);
		ids += "'" + record["oldID"] + "',";
	}
	ids.pop_back();
	QString sql = QString("DELETE FROM %1 WHERE id IN (%2)").arg(tableName.c_str()).arg(ids.c_str());
	if (!ZGProxyCommon::execSql(sql.toStdString()))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
		e.errDetail = QStringLiteral("删除表'%1'数据失败").arg(tableName.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	if (!ZGProxyCommon::mdeleteDataByID(tableName, listID))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
		e.errDetail = QStringLiteral("删除实时表'%1'数据失败").arg(tableName.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	std::move(listDeletedID.begin(), listDeletedID.end(), std::back_inserter(m_listDeletedID));
    if (m_setSyncTable.find(tableName) != m_setSyncTable.end())
    {
        const auto& json = ZGJson::convertToJson(listID);
        m_pMqttClient->sendPublish("SyncData/delete", extendJson(tableName, json));
        updateTableVersion(tableName);
    }
	return true;
}

bool ZGSPModifyOnlineMng::doSyncUpdateData(const std::string& tableName, const std::string& fields, ZG6000::ErrorInfo& e)
{
    Output output("doSyncUpdateData");
	if (fields.empty())
	{
        for (auto& record : m_listBatchData)
        {
            if (!ZGProxyCommon::updateDataID(tableName, record["oldID"], record["newID"]))
            {
				e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
				e.errDetail = QStringLiteral("更新表'%1'数据ID失败，旧ID:'%2'，新ID:'%3'")
                        .arg(tableName.c_str())
                        .arg(record["oldID"].c_str())
				        .arg(record["newID"].c_str()).toStdString();
				ZGLOG_ERROR(e);
                return false;
            }
			m_listDeletedID.push_back(record["id"]);
        }
	}
	else
	{
	    ZG6000::StringList listDeletedID, listID;
        std::string fieldsBk = "id," + fields;
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
            fieldsBk.pop_back();
		std::string ids;
		for (auto& record : m_listBatchData)
		{
			listDeletedID.push_back(record["id"]);
			listID.push_back(record["newID"]);
			ids += "'" + record["newID"] + "',";
		}
		ids.pop_back();
		QString sql = QString("SELECT %1 FROM %2 WHERE id IN (%3)").arg(fieldsBk.c_str()).arg(tableName.c_str()).arg(ids.c_str());
        ZG6000::ListStringMap listRecord;
		if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取表'%1'数据失败").arg(tableName.c_str()).toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
        // 因为更新的记录可能随后会被删除，因此不能保证取到的记录数与维护表中的记录数相同
        if (listRecord.empty())
        {
            std::move(listDeletedID.begin(), listDeletedID.end(), std::back_inserter(m_listDeletedID));
            return true;
        }
        ZG6000::StringList listRealID;
        for (auto& record: listRecord)
        {
            listRealID.push_back(record["id"]);
        }
        if (!ZGProxyCommon::mupdateDataByFields(tableName, listRealID, listRecord))
		{
            e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_RT);
            e.errDetail = QStringLiteral("更新实时表'%1'数据失败").arg(tableName.c_str()).toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
		std::move(listDeletedID.begin(), listDeletedID.end(), std::back_inserter(m_listDeletedID));
        if (m_setSyncTable.find(tableName) != m_setSyncTable.end())
        {
            const auto& json = ZGJson::convertToJson(listRecord);
            m_pMqttClient->sendPublish("SyncData/update", extendJson(tableName, json));
            updateTableVersion(tableName);
        }
	}
	return true;
}

bool ZGSPModifyOnlineMng::doSyncTask(ZG6000::ErrorInfo& e)
{
	QMutexLocker<QMutex> locker(&m_mutex);
	if (!checkDBProxyValid(e))
		return false;
	if (!checkRTProxyValid(e))
		return false;
    ZGLOG_TRACE("doSyncTask");
	while (true)
	{
		if (!deleteRecords(e))
			return false;
		auto listRecord = getChangedRecords(e);
		if (listRecord.empty())
            return true;
        ZGLOG_DEBUG(QString("listRecord size: %1").arg(listRecord.size()));
		for (auto& record : listRecord)
		{
			const auto& tuple = std::make_tuple(record["tableName"], record["fields"], record["operator"]);
			if (tuple == m_batchTable)
			{
				if (std::find_if(m_listBatchData.begin(), m_listBatchData.end(), [&](const ZG6000::StringMap& data)
				{
					if (record["operator"] == "3")
						return ZGUtils::get(data, "oldID") == record["oldID"];
					return ZGUtils::get(data, "newID") == record["newID"];
				}) == m_listBatchData.end())
				{
					m_listBatchData.push_back(std::move(record));
				}
				else
				{
					if (!m_listBatchData.empty())
					{
						if (!doSyncData(e))
							return false;
					}
					m_batchTable = std::make_tuple(record["tableName"], record["fields"], record["operator"]);
					m_listBatchData.push_back(std::move(record));
				}
			}
			else
			{
				const auto& [tableName, fields, oper] = m_batchTable;
				if (!tableName.empty() && !m_listBatchData.empty())
				{
					ZGLOG_DEBUG(QString("old tableName: '%1', new tableName: '%2'").arg(tableName.c_str()).arg(record["tableName"].c_str()));
					if (!doSyncData(e))
						return false;
				}
				m_batchTable = std::make_tuple(record["tableName"], record["fields"], record["operator"]);
				m_listBatchData.push_back(std::move(record));
			}
		}
		if (!m_listBatchData.empty())
		{
			if (!doSyncData(e))
				return false;
		}
	}
}

void ZGSPModifyOnlineMng::onStartSync()
{
    auto future = QtConcurrent::run([&](){
        while (m_running)
        {
            // if (ZGRuntime::instance()->isMaster())
            {
                ZG6000::ErrorInfo e;
                if (!doSyncTask(e))
                {
                    ZGLOG_ERROR(e);
                }
            }
            QThread::msleep(500);
        }
    });
}

bool ZGSPModifyOnlineMng::syncData(ZG6000::ErrorInfo& e)
{
	if (!ZGRuntime::instance()->isMaster())
	{
        QThread::msleep(1000);
		return true;
	}
	return doSyncTask(e);
}

bool ZGSPModifyOnlineMng::checkState()
{
	return m_initialized;
}

void ZGSPModifyOnlineMng::run()
{
    while (m_running)
    {
        ZGLOG_TRACE("check and sync");
        if (ZGRuntime::instance()->isMaster())
        {
            ZG6000::ErrorInfo e;
            if (!doSyncTask(e))
            {
                ZGLOG_ERROR(e);
            }
        }
        QThread::msleep(500);
    }
}

ZGSPModifyOnlineMng::ZGSPModifyOnlineMng(QObject* parent) : QThread(parent)
{

}

ZGSPModifyOnlineMng::~ZGSPModifyOnlineMng()
{
	stop();
}

void ZGSPModifyOnlineMng::initEvents()
{
	connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPModifyOnlineMng::onPeriodTask);
}

void ZGSPModifyOnlineMng::initServerInstConfig()
{
	const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
	QString errMsg;
	int value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_initInterval = value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_checkInterval = value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "sync_interval", value, 10, 30, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_syncInterval = value;
}

bool ZGSPModifyOnlineMng::initServerInstInfo()
{
	m_serverName = ZGRuntime::instance()->getServerID();
	if (m_serverName.isEmpty())
	{
		ZGLOG_ERROR("Empty server id.");
		return false;
	}
	m_instName = ZGRuntime::instance()->getInstanceID();
	if (m_instName.isEmpty())
	{
		ZGLOG_ERROR("Empty server instance id.");
		return false;
	}
    return true;
}

bool ZGSPModifyOnlineMng::initSyncTable()
{
    QString sql = QString("SELECT id FROM sp_table_info WHERE syncMode = 2");
    ZG6000::StringList listTableID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listTableID))
    {
        ZGLOG_ERROR(QStringLiteral("获取同步表信息失败"));
        return false;
    }
    for (const auto& tableID: listTableID)
    {
        m_setSyncTable.insert(tableID);
    }
    return true;
}

bool ZGSPModifyOnlineMng::initMqttClient()
{
    if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
    {
        ZGLOG_ERROR("initMqttClient error.");
        return false;
    }
    m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
    if (m_pMqttClient == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage return null.");
        return false;
    }
    m_pMqttClient->connectToHost();
    return true;
}

bool ZGSPModifyOnlineMng::initMaintainTable()
{
	if (!ZGProxyCommon::execSql("DELETE FROM sp_maintain_records;"))
	{
		ZGLOG_ERROR("清空维护记录表失败");
		return false;
	}
	return true;
}

void ZGSPModifyOnlineMng::updateTableVersion(const std::string &tableName)
{
    if (tableName != "sp_table_info")
    {
        const auto& dt = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        ZG6000::StringMap table{{"id", tableName}, {"rtVersion", dt}};
        std::string sql = ZGUtils::generateUpdateSql("sp_table_info", table);
        if (!ZGProxyCommon::execSql(sql))
            ZGLOG_ERROR(QStringLiteral("更新表'%1'版本失败").arg(tableName.c_str()));
    }
}

QString ZGSPModifyOnlineMng::extendJson(const std::string &tableName, const std::string &json)
{
    QString newJson = QString(R"({"table": "%1","data": %2})").arg(tableName.c_str()).arg(json.c_str());
    return newJson;
}

void ZGSPModifyOnlineMng::stop()
{
	m_running = false;
}

bool ZGSPModifyOnlineMng::isProxyAvailable(ZG6000::ZGServerBasePrxPtr prx)
{
	if (prx == nullptr)
    {
        ZGLOG_ERROR("proxy is null.");
		return false;
    }
	bool success;
	try
	{
		success = prx->checkState();
	}
    catch (const Ice::Exception& e)
	{
		success = false;
        ZGLOG_ERROR(e.what());
	}
    catch (const std::exception& e)
	{
		success = false;
        ZGLOG_INFO(e.what());
	}
	return success;
}

ZG6000::ZGServerBasePrxPtr ZGSPModifyOnlineMng::findProxyByCondition(
	const ZG6000::ListStringMap& listMapResult, const std::string& serviceName,
	const std::function<bool(const std::string& nodeID)>& condition)
{
	for (auto& mapResult : listMapResult)
	{
		try
		{
			const auto& nodeID = mapResult.at("nodeID");
			if (!condition(nodeID))
				continue;
			const auto& id = mapResult.at("id");
			std::shared_ptr<ZG6000::ZGServerBasePrx> prx = getProxyFromServiceInst(serviceName, id);
			if (isProxyAvailable(prx))
				return prx;
		}
		catch (std::out_of_range& e)
		{
			ZGLOG_ERROR(e.what());
		}
	}
	return nullptr;
}

ZG6000::ZGServerBasePrxPtr ZGSPModifyOnlineMng::getPreferredProxy(const std::string& serviceName)
{
	std::string localNodeID = ZGPubFun::getLocalNodeID().toStdString();
	const auto& dbPrx = ZGProxyMng::instance()->getProxySPDBData();
	std::string sql = "SELECT id, nodeID FROM sp_param_node_service_instance WHERE serviceID = '" + serviceName + "';";
	ZG6000::ListStringMap listMapResult;
	ZG6000::ErrorInfo e;
	try
	{
		if (!dbPrx->execQuerySqlToListMap(sql, listMapResult, e))
		{
			ZGLOG_ERROR(e);
			return nullptr;
		}
	}
	catch (const Ice::Exception& ex)
	{
		e.errReason = ex.what();
		ZGLOG_ERROR(e);
		return nullptr;
	}
	std::shared_ptr<ZG6000::ZGServerBasePrx> prx = findProxyByCondition(listMapResult, serviceName,
		[&localNodeID](const std::string& nodeID)->bool
	{
		return localNodeID == nodeID;
	});
	if (prx == nullptr)
	{
		prx = findProxyByCondition(listMapResult, serviceName,
			[&localNodeID](const std::string& nodeID)->bool
		{
			return localNodeID != nodeID;
		});
	}
	return prx;
}

ZG6000::ZGServerBasePrxPtr ZGSPModifyOnlineMng::getProxyFromServiceInst(const std::string& serviceName,
	const std::string& instName)
{
    ZGLOG_INFO(instName.c_str());
	std::shared_ptr<ZG6000::ZGServerBasePrx> prx = nullptr;
	if (serviceName == "ZGSPDBData")
		prx = ZGProxyMng::instance()->getProxySPDBData(QString::fromStdString(instName));
	else if (serviceName == "ZGSPRTData")
		prx = ZGProxyMng::instance()->getProxySPRTData(QString::fromStdString(instName));
	if (prx == nullptr)
	{
		QString errMsg = QString("Get proxy for service %1 error.").arg(serviceName.c_str());
		ZGLOG_ERROR(errMsg);
		return nullptr;
	}
	return prx;
}

ZG6000::ZGSPDBDataPrxPtr ZGSPModifyOnlineMng::getDBDataProxy()
{
	const auto& baseDbProxy = getPreferredProxy("ZGSPDBData");
	if (baseDbProxy == nullptr)
	{
		ZG6000::ErrorInfo e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
		e.errDetail = "Can't get available ZGSPDBDataPrx.";
		ZGLOG_ERROR(e);
		return nullptr;
	}
	return ZGProxyMng::instance()->getProxySPDBData(baseDbProxy);
}

ZG6000::ZGSPRTDataPrxPtr ZGSPModifyOnlineMng::getRTDataProxy()
{
	const auto& baseRtProxy = getPreferredProxy("ZGSPRTData");
	if (baseRtProxy == nullptr)
	{
		ZG6000::ErrorInfo e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_RT);
		e.errDetail = "Can't get available ZGSPRTDataPrx";
		ZGLOG_ERROR(e);
		return nullptr;
	}
	return ZGProxyMng::instance()->getProxySPRTData(baseRtProxy);
}

bool ZGSPModifyOnlineMng::deleteRecords(ZG6000::ErrorInfo& e)
{   
    if (m_listDeletedID.empty())
		return true;
    Output output("deleteRecords");
    std::string ids;
    for (const auto& id : m_listDeletedID)
    {
        ids += "'" + id + "',";
    }
    ids.pop_back();
    QString sql = QString("DELETE FROM sp_maintain_records WHERE id IN (%1);").arg(ids.c_str());
    if (!ZGProxyCommon::execSql(sql.toStdString()))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPModifyOnline::ZG_ERR_DB);
		e.errDetail = QStringLiteral("删除维护记录表记录失败").toStdString();
	    ZGLOG_ERROR(e);
		return false;
	}
	m_listDeletedID.clear();
	return true;
}

bool ZGSPModifyOnlineMng::isTableExists(const std::string& tableName)
{
	return m_mapTableFields.find(tableName) != m_mapTableFields.end();
}

bool ZGSPModifyOnlineMng::isTableFieldsExists(const std::string& tableName, const ZG6000::StringList& listField)
{
	auto pair = m_mapTableFields.find(tableName);
	if (pair == m_mapTableFields.end())
		return false;
	return std::all_of(listField.begin(), listField.end(), [&](const std::string& field)
	{
        return pair->second.find(field) != pair->second.end();
	});
}

bool ZGSPModifyOnlineMng::updateTableParam()
{
	auto dbProxy = getDBDataProxy();
	if (dbProxy == nullptr)
		return false;
    try
    {
        ZG6000::StringList listTable;
        ZG6000::ErrorInfo e;
        if (!dbProxy->getTablesToList(listTable, e))
        {
            ZGLOG_ERROR(e);
			return false;
        }
        for (const auto& table : listTable)
        {
            ZG6000::StringList listFieldName;
            if (!dbProxy->getTableFieldNameToList(table, listFieldName, e))
            {
                ZGLOG_ERROR(e);
				continue;
            }
            for (const auto& fieldName : listFieldName)
            {
                auto pair = m_mapTableFields.find(table);
				if (pair == m_mapTableFields.end())
                    m_mapTableFields.insert(std::make_pair(table, std::unordered_map<std::string, bool>{std::make_pair(fieldName, true)}));
				else
					pair->second.insert(std::make_pair(fieldName, true));
            }
        }
		return true;
    }
    catch (const Ice::Exception& e)
    {
		ZGLOG_ERROR(e.what());
		return false;
    }
}

void ZGSPModifyOnlineMng::onPeriodTask()
{
    m_dbDataProxy = getDBDataProxy();
    m_rtDataProxy = getRTDataProxy();
    updateTableParam();
}

#ifndef ZGSPMODIFYONLINEMNG_H
#define ZGSPMODIFYONLINEMNG_H

#include <ZGServerCommon.h>
#include "ZGProxyMng.h"
#include "ZGDebugMng.h"
#include <QTimer>
#include <QThread>
#include <QObject>
#include <QMutex>
#include <unordered_set>

class ZGMqttClient;
class ZGSPModifyOnlineMng : public QThread
{
	Q_OBJECT
public:
	static ZGSPModifyOnlineMng* instance();
	void init();
	virtual bool syncData(ZG6000::ErrorInfo& e);
	virtual bool checkState();

    // QThread interface
protected:
    void run() override;

private:
	explicit ZGSPModifyOnlineMng(QObject* parent = nullptr);
	~ZGSPModifyOnlineMng() override;
	void initEvents();
	void initServerInstConfig();
	bool initServerInstInfo();
    bool initSyncTable();
    bool initMqttClient();
	bool initMaintainTable();
    void updateTableVersion(const std::string& tableName);
    QString extendJson(const std::string& tableName, const std::string& json);
	void stop();
	bool isProxyAvailable(ZG6000::ZGServerBasePrxPtr prx);
	ZG6000::ZGServerBasePrxPtr findProxyByCondition(const ZG6000::ListStringMap& listMapResult,
	                                                const std::string& serviceName,
	                                                const std::function<bool(const std::string& nodeID)>& condition);
	ZG6000::ZGServerBasePrxPtr getPreferredProxy(const std::string& serviceName);
	ZG6000::ZGServerBasePrxPtr getProxyFromServiceInst(const std::string& serviceName, const std::string& instName);
	ZG6000::ZGSPDBDataPrxPtr getDBDataProxy();
	ZG6000::ZGSPRTDataPrxPtr getRTDataProxy();
    bool deleteRecords(ZG6000::ErrorInfo& e);
	inline bool isTableExists(const std::string& tableName);
	inline bool isTableFieldsExists(const std::string& tableName, const ZG6000::StringList& listField);
	bool updateTableParam();
	bool checkDBProxyValid(ZG6000::ErrorInfo& e);
	bool checkRTProxyValid(ZG6000::ErrorInfo& e);
	ZG6000::ListStringMap getChangedRecords(ZG6000::ErrorInfo& e);
	bool isUpdateDataID(const ZG6000::StringMap& record);
	bool doSyncData(ZG6000::ErrorInfo& e);
	bool doSyncInsertData(const std::string& tableName, ZG6000::ErrorInfo& e);
	bool doSyncDeleteData(const std::string& tableName, ZG6000::ErrorInfo& e);
	bool doSyncUpdateData(const std::string& tableName, const std::string& fields, ZG6000::ErrorInfo& e);
	bool doSyncTask(ZG6000::ErrorInfo& e);

signals:
	void initFinished();

private slots:
    void onStartSync();
	void onPeriodTask();
private:
    class Output {
    public:
        Output(const QString& msg): m_msg(msg) {ZGLOG_DEBUG(QString("begin %1").arg(msg));}
        ~Output() {ZGLOG_DEBUG(QString("end %1").arg(m_msg));}
    private:
        QString m_msg;
    };
	bool m_initialized{false};
	QString m_serverName{""};
	QString m_instName{""};
	int m_initInterval{10};
	int m_checkInterval{10};
	int m_syncInterval{1};
    ZGMqttClient* m_pMqttClient{nullptr};
	QTimer m_checkTimer;
	std::atomic<bool> m_running{true};
	std::tuple<std::string, std::string, std::string> m_batchTable;
	ZG6000::ListStringMap m_listBatchData;
	ZG6000::StringList m_listDeletedID;
	ZG6000::ZGSPDBDataPrxPtr m_dbDataProxy{nullptr};
	ZG6000::ZGSPRTDataPrxPtr m_rtDataProxy{nullptr};
	std::unordered_map<std::string, std::unordered_map<std::string, bool>> m_mapTableFields;
    std::unordered_set<std::string> m_setSyncTable;
	QMutex m_mutex;
};

#endif // ZGSPMODIFYONLINEMNG_H

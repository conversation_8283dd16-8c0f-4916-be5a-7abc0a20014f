#include <ZGSPModifyOnlineI.h>
#include "ZGSPModifyOnlineMng.h"


ZG6000::ZGSPModifyOnlineI::ZGSPModifyOnlineI()
{
    ZGSPModifyOnlineMng::instance()->init();
}

bool
ZG6000::ZGSPModifyOnlineI::checkState(const Ice::Current& current)
{
    return ZGSPModifyOnlineMng::instance()->checkState();
}

bool
ZG6000::ZGSPModifyOnlineI::syncData(ErrorInfo& e, const Ice::Current& current)
{
    return ZGSPModifyOnlineMng::instance()->syncData(e);
}

#include "ZGOPTaskOTItem.h"
#include "ZGOPTaskOTTicket.h"
#include "ZGOPTaskOTMng.h"
#include "ZGOPTaskOTDefine.h"
#include "ZGJson.h"
#include <QThread>

ZGOPTaskOTItem::ZGOPTaskOTItem(QObject* parent)
    : QObject{parent},
      m_pTicket(dynamic_cast<ZGOPTaskOTTicket*>(parent))
{
}

void ZGOPTaskOTItem::initialize()
{
    m_confirmDateTime = QDateTime::currentDateTime();
}

bool ZGOPTaskOTItem::getCurrentState(const ZG6000::StringMap& item, std::string& state)
{
    try
    {
        state = ZGUtils::get(item, "rtStateID");
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

bool ZGOPTaskOTItem::setCurrentState(const std::string& itemID, const std::string& state)
{
    if (itemID == "-1")
        return false;
    const QString sql = QString("UPDATE op_param_ot_item SET rtStateID = '%1' WHERE id = '%2'").arg(state.c_str()).arg(itemID.c_str());
    ZGLOG_INFO(sql);
    if (!ZGProxyCommon::execSql(sql.toStdString()))
    {
        ZGLOG_ERROR(QStringLiteral("更新步骤项'%1'状态'%2'失败").arg(itemID.c_str()).arg(state.c_str()));
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

void ZGOPTaskOTItem::onTimer(const std::string& itemID)
{
    if (itemID.empty())
    {
        ZGLOG_TRACE("nextItem");
        nextItem();
        return;
    }
    ZG6000::ListStringMap listItem;
    QString sql = QStringLiteral("SELECT * FROM op_param_ot_item WHERE id = '%1'").arg(itemID.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listItem))
    {
        ZGLOG_ERROR(QStringLiteral("查询步骤'%1'信息失败").arg(itemID.c_str()));
        return;
    }
    if (listItem.empty())
    {
        ZGLOG_ERROR(QStringLiteral("步骤'%1'不存在").arg(itemID.c_str()));
        return;
    }
    auto otItem = listItem.front();
    if (!checkItemCondition(otItem))
        return;
    const QDateTime currentTime = QDateTime::currentDateTime();
    auto strExecTime = getExecTime(otItem);
    if (strExecTime.empty())
    {
        ZG6000::StringMap item{{"id", itemID}, {"rtExecTime", ZGUtils::DateTimeToString(currentTime, true).toStdString()}};
        sql = ZGUtils::generateUpdateSql("op_param_ot_item", item).c_str();
        ZGProxyCommon::execSql(sql.toStdString());
        return;
    }
    QDateTime execTime;
    if (!ZGUtils::StringToDateTime(strExecTime.c_str(), execTime, true))
    {
        ZGLOG_ERROR(QStringLiteral("无效的执行时间'%1'").arg(getExecTime(otItem).c_str()));
        return;
    }
    std::string itemState;
    if (!getCurrentState(otItem, itemState))
    {
        ZGLOG_ERROR(QStringLiteral("获取当前票项'%1'状态失败").arg(itemID.c_str()));
        return;
    }
    const int timeout = std::atoi(otItem["timeout"].c_str());
    if (execTime.secsTo(currentTime) >= timeout)
    {
        // 如果当前步骤已经完成，直接进入下一步，防止双机切换情况下步骤超时后重复执行
        if (itemState == "ZG_OIS_FINISHED")
            nextItem();
        else
        {
            std::string errMsg;
            if (setCurrentState(itemID, "ZG_OIS_TIMEOUT"))
                notify(ZGOPITEM_TIMEOUT, {}, errMsg);
        }
        return;
    }
    if (itemState.empty())
    {
        ZGLOG_DEBUG(QString("processEmptyState, id: '%1'").arg(itemID.c_str()));
        processEmptyState(execTime, otItem);
    }
    if (itemState == "ZG_OIS_READY")
    {
        ZGLOG_DEBUG(QString("processReadyState, id: '%1'").arg(itemID.c_str()));
        processReadyState(execTime, otItem);
    }
    if (itemState == "ZG_OIS_WAIT")
    {
        ZGLOG_DEBUG(QString("processWaitState, id: '%1'").arg(itemID.c_str()));
        processWaitState(execTime, otItem);
    }
    if (itemState == "ZG_OIS_VERIFY")
    {
        ZGLOG_DEBUG(QString("processVerifyState, id: '%1'").arg(itemID.c_str()));
        processVerifyState(execTime, otItem);
    }
    if (itemState == "ZG_OIS_EXECUTE")
    {
        ZGLOG_DEBUG(QString("processExecuteState, id: '%1'").arg(itemID.c_str()));
        processExecuteState(execTime, otItem);
    }
    if (itemState == "ZG_OIS_CONFIRM_WAIT")
    {
        ZGLOG_DEBUG(QString("processConfirmWaitState, id: '%1'").arg(itemID.c_str()));
        processConfirmWaitState(execTime, otItem);
    }
    if (itemState == "ZG_OIS_CONFIRM")
    {
        ZGLOG_DEBUG(QString("processConfirmState, id: '%1'").arg(itemID.c_str()));
        processConfirmState(execTime, otItem);
    }
    if (itemState == "ZG_OIS_TOUR")
    {
        ZGLOG_DEBUG(QString("processTourState, id: '%1'").arg(itemID.c_str()));
        processTourState(execTime, otItem);
    }
    if (itemState == "ZG_OIS_FINISHED")
    {
        ZGLOG_DEBUG(QString("processFinishedState, id: '%1'").arg(itemID.c_str()));
        processFinishState(execTime, otItem);
    }
}

bool ZGOPTaskOTItem::checkItemCondition(const ZG6000::StringMap& otItem)
{
    try
    {
        const auto& termItemGroupID = ZGUtils::get(otItem, "termItemGroupID");
        return termItemGroupID == "ZG_OTIG_SERVER";
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

std::string ZGOPTaskOTItem::getExecTime(const ZG6000::StringMap& otItem)
{
    return ZGUtils::get(otItem, "rtExecTime", "");
}

void ZGOPTaskOTItem::processEmptyState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    const auto & itemID = ZGUtils::get(otItem, "id");
    if (!setCurrentState(itemID, "ZG_OIS_READY"))
        ZGLOG_ERROR(QStringLiteral("设置步骤'%1'准备状态失败").arg(itemID.c_str()));
}

bool ZGOPTaskOTItem::checkUserCard(const std::string& deviceID, const std::string& userID, bool& pass, std::string& errMsg)
{
    ZG6000::ErrorInfo e;
    ZG6000::StringMap card;
    if (!ZGProxyCommon::getProperty(deviceID, "CardNumber", card, e))
    {
        errMsg = QStringLiteral("获取设备'%1'卡号失败").arg(deviceID.c_str()).toStdString();
        return false;
    }
    const auto& cardNumber = card["rtNewValue"];
    if (cardNumber.empty())
    {
        pass = false;
        return true;
    }
    const QString sql = QString("SELECT userID FROM sp_param_hrm_user_card WHERE cardID = '%1' "
        "AND userID = '%2'").arg(cardNumber.c_str()).arg(userID.c_str());
    ZG6000::StringList listCardID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listCardID))
    {
        errMsg = QStringLiteral("查询人员'%1'是否持有卡'%2'失败").arg(userID.c_str()).arg(cardNumber.c_str()).toStdString();
        return false;
    }
    const auto& updateTime = card["rtUpdateTime"];
    const QDateTime currTime = QDateTime::currentDateTime();
    QDateTime updateDateTime;
    if (!ZGUtils::StringToDateTime(updateTime.c_str(), updateDateTime))
    {
        errMsg = QStringLiteral("转换更新时间'%1'失败").arg(updateTime.c_str()).toStdString();
        return false;
    }
    pass = !listCardID.empty() && (updateDateTime.secsTo(currTime) <= 10);
    return true;
}

bool ZGOPTaskOTItem::checkUserID(const std::string& deviceID, const std::string& userID, bool& pass, std::string& errMsg)
{
    ZG6000::ErrorInfo e;
    ZG6000::StringMap user;
    if (!ZGProxyCommon::getProperty(deviceID, "WorkNumber", user, e))
    {
        errMsg = QStringLiteral("获取设备'%1'人员失败").arg(deviceID.c_str()).toStdString();
        return false;
    }
    const auto& workNumber = user["rtNewValue"];
    const auto& updateTime = user["rtUpdateTime"];
    ZGLOG_TRACE(QString("workNumber = '%1', userID = '%2', updateTime = '%3'").arg(workNumber.c_str()).arg(userID.c_str()).arg(updateTime.c_str()));
    if (workNumber.empty())
    {
        pass = false;
        return true;
    }
    const QDateTime currTime = QDateTime::currentDateTime();
    QDateTime updateDateTime;
    if (!ZGUtils::StringToDateTime(updateTime.c_str(), updateDateTime))
    {
        errMsg = QStringLiteral("转换更新时间'%1'失败").arg(updateTime.c_str()).toStdString();
        return false;
    }
    pass = (workNumber == userID) && (updateDateTime.secsTo(currTime) <= 10);
    ZGLOG_TRACE(QString("pass = %1").arg(pass));
    return true;
}

bool ZGOPTaskOTItem::getUserIDFromCard(const std::string& cardID, std::string& userID, std::string& errMsg)
{
    const QString sql = QString("SELECT userID FROM sp_param_hrm_user_card WHERE cardID = '%1'").arg(cardID.c_str());
    ZG6000::StringList listUserID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
    {
        errMsg = QStringLiteral("查询卡'%1'对应的人员失败").arg(cardID.c_str()).toStdString();
        return false;
    }
    if (listUserID.empty())
    {
        errMsg = QStringLiteral("卡'%1'未绑定人员").arg(cardID.c_str()).toStdString();
        return false;
    }
    userID = listUserID.front();
    return true;
}

bool ZGOPTaskOTItem::getAvailableTourUsers(const ZG6000::StringMap& otItem, ZG6000::StringMap& tourUsers, std::string& errMsg)
{
    try
    {
        const auto& isOperExec = ZGUtils::get(otItem, "isOperExec");
        const auto& rtIsOperExec = ZGUtils::get(otItem, "rtIsOperExec");
        if ((isOperExec == "1") && (rtIsOperExec != "1"))
        {
            std::string operUserID;
            if (!m_pTicket->getOperUser(operUserID))
            {
                errMsg = QStringLiteral("获取操作员失败").toStdString();
                return false;
            }
            tourUsers[operUserID] = "rtIsOperExec";
        }
        const auto& isMonExec = ZGUtils::get(otItem, "isMonExec");
        const auto& rtIsMonExec = ZGUtils::get(otItem, "rtIsMonExec");
        if ((isMonExec == "1") && (rtIsMonExec != "1"))
        {
            std::string monUserID;
            if (!m_pTicket->getMonUser(monUserID))
            {
                errMsg = QStringLiteral("获取监护员失败").toStdString();
                return false;
            }
            tourUsers[monUserID] = "rtIsMonExec";
        }
        return true;
    }
    catch (const std::exception& e)
    {
        errMsg = e.what();
        return false;
    }
}

void ZGOPTaskOTItem::notifyError(const std::string& itemID, const std::string& message)
{
    if (setCurrentState(itemID, "ZG_OIS_ERROR"))
    {
        std::string errMsg;
        notify(ZGOPITEM_ERROR, {{"rtErrorDesc", message}}, errMsg);
    }
}

void ZGOPTaskOTItem::processReadyState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    const auto& isAutoExec = otItem["isAutoExec"];
    const auto& itemID = otItem["id"];
    if (isAutoExec != "1")
    {
        const auto & termItemTypeID = otItem["termItemTypeID"];
        if (termItemTypeID == "ZG_OTIT_TOUR")
        {
            if (!setCurrentState(itemID, "ZG_OIS_TOUR"))
                ZGLOG_ERROR(QStringLiteral("设置步骤'%1'巡视状态失败").arg(itemID.c_str()));
        }
        else
        {
            if (!setCurrentState(itemID, "ZG_OIS_VERIFY"))
                ZGLOG_ERROR(QStringLiteral("设置步骤'%1'验证状态失败").arg(itemID.c_str()));
        }
    }
    else
    {
        if (!setCurrentState(itemID, "ZG_OIS_WAIT"))
            ZGLOG_ERROR(QStringLiteral("设置步骤'%1'等待状态失败").arg(itemID.c_str()));
    }
}

void ZGOPTaskOTItem::processWaitState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    const int startDelay = std::atoi(otItem["startDelay"].c_str());
    if (execTime.secsTo(QDateTime::currentDateTime()) >= startDelay)
    {
        const auto & termItemTypeID = otItem["termItemTypeID"];
        const auto& itemID = otItem["id"];
        if (termItemTypeID == "ZG_OTIT_TOUR")
        {
            if (!setCurrentState(itemID, "ZG_OIS_TOUR"))
                ZGLOG_ERROR(QStringLiteral("设置步骤'%1'巡视状态失败").arg(itemID.c_str()));
        }
        else
        {
            if (!setCurrentState(itemID, "ZG_OIS_VERIFY"))
                ZGLOG_ERROR(QStringLiteral("设置步骤'%1'验证状态失败").arg(itemID.c_str()));
        }
    }
}

void ZGOPTaskOTItem::processVerifyState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    const auto& itemID = otItem["id"];
    if (otItem["isCheckExecCondition"] == "1")
    {
        try
        {
            std::string errMsg;
            ZG6000::ErrorInfo e;
            const auto ruleEngine = ZGProxyMng::instance()->getProxyMPRuleEngine();
            if (ruleEngine == nullptr)
            {
                ZGLOG_ERROR(QStringLiteral("获取规则引擎服务代理对象失败"));
                return;
            }
            const auto& deviceID = ZGUtils::get(otItem, "deviceID");
            const auto& termID = ZGUtils::get(otItem, "termID");
            if (!deviceID.empty())
            {
                ZGLOG_DEBUG(QString("checkDeviceExecCondition, deviceID: '%1', termID: '%2'").arg(deviceID.c_str()).arg(termID.c_str()));
                if (!ruleEngine->checkDeviceExecCondition(deviceID, termID, getSimFlag(), e))
                {
                    ZGLOG_ERROR(e);
                    notifyError(itemID, u8"验证执行条件失败");
                    return;
                }
            }
            else
            {
                const auto& otID = ZGUtils::get(otItem, "otID");
                std::string appNodeID;
                if (!ZGProxyCommon::getDataByField("op_param_task", otID, "appNodeID", appNodeID))
                {
                    ZGLOG_ERROR(QStringLiteral("获取操作票'%1'所属应用节点失败").arg(otID.c_str()));
                    return;
                }
                ZGLOG_DEBUG(QString("checkAppNodeExecCondition, appNodeID: '%1', termID: '%2'").arg(appNodeID.c_str()).arg(termID.c_str()));
                if (!ruleEngine->checkAppNodeExecCondition(appNodeID, termID, getSimFlag(), e))
                {
                    ZGLOG_ERROR(e);
                    if (setCurrentState(itemID, "ZG_OIS_ERROR"))
                    {
                        notify(ZGOPITEM_ERROR, {{"rtErrorDesc", QStringLiteral("验证执行条件失败").toStdString()}}, errMsg);
                        return;
                    }
                }
            }
        }
        catch (const Ice::Exception& e)
        {
            ZGLOG_ERROR(e.what());
            return;
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
            return;
        }
    }
    setCurrentState(itemID, "ZG_OIS_EXECUTE");
}

void ZGOPTaskOTItem::processExecuteState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    const auto& itemID = otItem["id"];
    if (otItem["termItemTypeID"] == "ZG_OTIT_CTRL")
    {
        const auto& termID = otItem["termID"];
        ZG6000::StringMap term;
        ZGProxyCommon::getDataByFields("mp_param_term", termID, {"propertyName", "dstValue"}, term);
        if (!ZG6000::ZGOPTaskOTMng::instance()->sendCtrlCommand(otItem["deviceID"], term["propertyName"],
            term["dstValue"]))
            return;
    }
    else if (otItem["termItemTypeID"] == "ZG_OTIT_VERIFY")
    {
        const auto& deviceID = otItem["deviceID"];
        // TODO: 根据任务项验证属性值
        const auto& termID = otItem["termID"];
        ZG6000::StringMap term;
        ZGProxyCommon::getDataByFields("mp_param_term", termID, {"propertyName", "dstValue"}, term);
        const auto& propertyName = term["propertyName"];
        const auto& dstValue = term["dstValue"];
        if ((!deviceID.empty()) && (!propertyName.empty()) && (!dstValue.empty()))
        {
            std::string errMsg;
            std::string propertyValue;
            ZG6000::ErrorInfo e;
            if (!ZGProxyCommon::getPropertyValue(deviceID, propertyName, propertyValue, e))
            {
                if (setCurrentState(itemID, "ZG_OIS_ERROR"))
                {
                    notify(ZGOPITEM_ERROR, {{"rtErrorDesc", ZGJson::convertToJson(e)}}, errMsg);
                    return;
                }
            }
            if (propertyValue != dstValue)
            {
                if (setCurrentState(itemID, "ZG_OIS_ERROR"))
                {
                    notify(ZGOPITEM_ERROR, {{"rtErrorDesc", QStringLiteral("状态验证未通过").toStdString()}}, errMsg);
                    return;
                }
            }
        }
    }
    const int confirmDelay = std::atoi(otItem["confirmDelay"].c_str());
    ZGLOG_TRACE(QString("confirmDelay = %1").arg(confirmDelay));
    if (confirmDelay > 0)
        setCurrentState(itemID, "ZG_OIS_CONFIRM_WAIT");
    else
    {
        ZGLOG_INFO(QString("set item '%1' state to ZG_OIS_CONFIRM").arg(itemID.c_str()));
        setCurrentState(itemID, "ZG_OIS_CONFIRM");
        m_confirmDateTime = QDateTime::currentDateTime();
    }
}

void ZGOPTaskOTItem::processConfirmWaitState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    ++m_confirmWaitCount;
    const int confirmDelay = std::atoi(otItem["confirmDelay"].c_str());
    if (m_confirmWaitCount >= confirmDelay)
    {
        m_confirmWaitCount = 0;
        const auto& itemID = otItem["id"];
        ZGLOG_INFO(QString("set item '%1' state to ZG_OIS_CONFIRM").arg(itemID.c_str()));
        setCurrentState(itemID, "ZG_OIS_CONFIRM");
        m_confirmDateTime = QDateTime::currentDateTime();
    }
}

void ZGOPTaskOTItem::processConfirmState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    if (otItem["isCheckConfirmCondition"] == "1")
    {
        try
        {
            ZG6000::ErrorInfo e;
            const auto ruleEngine = ZGProxyMng::instance()->getProxyMPRuleEngine();
            if (ruleEngine == nullptr)
            {
                ZGLOG_ERROR(QStringLiteral("获取规则引擎服务代理对象失败"));
                return;
            }
            const auto& deviceID = ZGUtils::get(otItem, "deviceID");
            const auto& termID = ZGUtils::get(otItem, "termID");
            if (!deviceID.empty())
            {
                ZGLOG_DEBUG(QString("checkDeviceConfirmCondition, deviceID: '%1', termID: '%2'").arg(deviceID.c_str()).arg(termID.c_str()));
                if (!ruleEngine->checkDeviceConfirmCondition(deviceID, termID, getSimFlag(), e))
                {
                    ZGLOG_DEBUG(e);
                    return;
                }
            }
            else
            {
                const auto& otID = ZGUtils::get(otItem, "otID");
                std::string appNodeID;
                if (!ZGProxyCommon::getDataByField("op_param_task", otID, "appNodeID", appNodeID))
                {
                    ZGLOG_ERROR(QStringLiteral("获取操作票'%1'所属应用节点失败").arg(otID.c_str()));
                    return;
                }
                ZGLOG_DEBUG(QString("checkAppNodeConfirmCondition, appNodeID: '%1', termID: '%2'").arg(appNodeID.c_str()).arg(termID.c_str()));
                if (!ruleEngine->checkAppNodeConfirmCondition(appNodeID, termID, getSimFlag(), e))
                {
                    ZGLOG_DEBUG(e);
                    return;
                }
            }
        }
        catch (const Ice::Exception& e)
        {
            ZGLOG_ERROR(e.what());
            return;
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
            return;
        }
    }
    const auto& itemID = otItem["id"];
    setCurrentState(itemID, "ZG_OIS_FINISHED");
    m_confirmDateTime = QDateTime::currentDateTime();
}

bool ZGOPTaskOTItem::checkUpdateTimeValid(const ZG6000::StringMap::mapped_type& updateTime)
{
    const QDateTime currTime = QDateTime::currentDateTime();
    QDateTime updateDateTime;
    if (!ZGUtils::StringToDateTime(updateTime.c_str(), updateDateTime))
    {
        ZGLOG_ERROR(QStringLiteral("转换更新时间'%1'失败").arg(updateTime.c_str()));
        return false;
    }
    // 如果更新时间与当前时间之差大于10秒，不处理
    if (updateDateTime.secsTo(currTime) > 10)
        return false;
    return true;
}

void ZGOPTaskOTItem::processTourState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    // 得到当前步骤项设备ID
    const auto& deviceID = otItem["deviceID"];
    const auto& itemID = otItem["id"];
    // 获取设备子类型
    std::string subtypeID;
    std::string errMsg;
    if (!ZGProxyCommon::getDataByField("mp_param_device", deviceID, "subtypeID", subtypeID))
    {
        errMsg = QStringLiteral("获取设备'%1'子类型失败").arg(deviceID.c_str()).toStdString();
        ZGLOG_ERROR(errMsg.c_str());
        notifyError(itemID, errMsg);
        return;
    }
    ZGLOG_TRACE(QString("deviceID = '%1', subtypeID = '%2'").arg(deviceID.c_str()).arg(subtypeID.c_str()));
    std::string userID;
    ZG6000::ErrorInfo e;
    if (subtypeID == "ZG_DS_IDENT_CARD")
    {
        ZG6000::StringMap card;
        if (!ZGProxyCommon::getProperty(deviceID, "CardNumber", card, e))
        {
            errMsg = QStringLiteral("获取设备'%1'卡号失败").arg(deviceID.c_str()).toStdString();
            ZGLOG_ERROR(errMsg.c_str());
            notifyError(itemID, errMsg);
            return;
        }
        const auto& cardNumber = card["rtNewValue"];
        ZGLOG_TRACE(QString("cardNumber = '%1'").arg(cardNumber.c_str()));
        if (cardNumber.empty())
            return;
        const auto& updateTime = card["rtUpdateTime"];
        if (!checkUpdateTimeValid(updateTime))
        {
            ZGLOG_TRACE(QString("checkUpdateTimeValid failed, updateTime = '%1'").arg(updateTime.c_str()));
            return;
        }
        if (!getUserIDFromCard(cardNumber, userID, errMsg))
        {
            ZGLOG_ERROR(errMsg.c_str());
            return;
        }
    }
    else if (subtypeID == "ZG_DS_IDENT_SMART")
    {
        ZG6000::StringMap user;
        if (!ZGProxyCommon::getProperty(deviceID, "WorkNumber", user, e))
        {
            errMsg = QStringLiteral("获取设备'%1'人员失败").arg(deviceID.c_str()).toStdString();
            ZGLOG_ERROR(errMsg.c_str());
            notifyError(itemID, errMsg);
            return;
        }
        userID = user["rtNewValue"];
        const auto& updateTime = user["rtUpdateTime"];
        if (!checkUpdateTimeValid(updateTime))
            return;
    }
    else
    {
        errMsg = QStringLiteral("不支持的设备子类型'%1'").arg(subtypeID.c_str()).toStdString();
        ZGLOG_ERROR(errMsg.c_str());
        notifyError(itemID, errMsg);
        return;
    }
    ZGLOG_TRACE(QString("userID = '%1'").arg(userID.c_str()));
    ZG6000::StringMap tourUsers;
    if (!getAvailableTourUsers(otItem, tourUsers, errMsg))
    {
        ZGLOG_ERROR(errMsg.c_str());
        notifyError(itemID, errMsg);
        return;
    }
    ZGLOG_TRACE(QString("tourUsers.size() = %1").arg(tourUsers.size()));
    if (tourUsers.empty())
    {
        setCurrentState(itemID, "ZG_OIS_FINISHED");
        return;
    }
    for (const auto& [tourUserID, field] : tourUsers)
    {
        if (tourUserID == userID)
        {
            ZGLOG_TRACE(QStringLiteral("巡视用户: '%1'").arg(userID.c_str()));
            ZG6000::StringMap updateItem{{"id", itemID}, {field, "1"}};
            const auto& sql = ZGUtils::generateUpdateSql("op_param_ot_item", updateItem);
            if (!ZGProxyCommon::execSql(sql))
            {
                ZGLOG_ERROR(QStringLiteral("更新步骤'%1'操作人或监护人执行状态失败").arg(itemID.c_str()));
                return;
            }
            ZGProxyCommon::synchronize();
            return;
        }
    }
}

void ZGOPTaskOTItem::processFinishState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    try
    {
        const QDateTime currTime = QDateTime::currentDateTime();
        const int endDelay = std::atoi(ZGUtils::get(otItem, "endDelay").c_str());
        if (m_confirmDateTime.secsTo(currTime) >= endDelay)
        {
            m_pTicket->nextItem();
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

bool ZGOPTaskOTItem::nextItem()
{
    return m_pTicket->nextItem();
}

bool ZGOPTaskOTItem::notify(const std::string& action, ZG6000::StringMap args, std::string& errMsg)
{
    return m_pTicket->command(action, std::move(args), errMsg);
}

int ZGOPTaskOTItem::getSimFlag()
{
    return 0;
}

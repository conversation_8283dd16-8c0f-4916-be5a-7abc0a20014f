#ifndef ZG6000_ZGOPTASKOTMNG_H
#define ZG6000_ZGOPTASKOTMNG_H

#include <QObject>
#include <QTimer>
#include <QDateTime>
#include <QReadWriteLock>
#include "ZGProxyCommon.h"

class ZGMqttClient;
class ZGRedisClient;
class ZGOPTaskOTTicket;
namespace ZG6000 {
class ZGOPTaskOTMng : public QObject
{
    friend class ::ZGOPTaskOTTicket;
	Q_OBJECT
public:
    static ZGOPTaskOTMng* instance();
    void init();
    bool checkState();

    /**
     * @brief	创建操作票
     *
     * @param 		  	taskTypeID	票类型ID
     * @param 		  	param	  	票参数
     * @param [in,out]	taskID	  	创建成功后的票ID
     * @param [in,out]	e		  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool createOT(std::string taskTypeID, StringMap param, std::string& taskID, ErrorInfo& e);

    /**
     * @brief	编辑操作票
     *
     * @param 		  	otID   	票ID
     * @param 		  	head   	票头信息
     * @param 		  	items  	票项信息
     * @param [in,out]	e	   	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool editOT(std::string otID, StringMap head, ListStringMap items, ErrorInfo& e);

    /**
     * @brief	删除任务
     *
     * @param 		  	taskID	任务ID
     * @param 		  	param 	任务参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool deleteTask(std::string taskID, StringMap param, ErrorInfo& e);

    /**
     * @brief	启动任务
     *
     * @param 		  	taskID	任务ID
     * @param 		  	param 	任务参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool startTask(std::string taskID, StringMap param, ErrorInfo& e);

    /**
     * @brief	确认任务
     *
     * @param 		  	taskID	任务ID
     * @param 		  	param 	任务参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool confirmTask(std::string taskID, StringMap param, ErrorInfo& e);

    /**
     * @brief	暂停任务
     *
     * @param 		  	taskID	任务ID
     * @param 		  	param 	任务参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool pauseTask(std::string taskID, StringMap param, ErrorInfo& e);

    /**
     * @brief	继续任务
     *
     * @param 		  	taskID	任务ID
     * @param 		  	param 	任务参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool resumeTask(std::string taskID, StringMap param, ErrorInfo& e);

    /**
     * @brief	重试任务
     *
     * @param 		  	taskID	任务ID
     * @param 		  	param 	任务参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool retryTask(std::string taskID, StringMap param, ErrorInfo& e);

    /**
     * @brief	作废任务
     *
     * @param 		  	taskID	任务ID
     * @param 		  	param 	任务参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool abolishTask(std::string taskID, StringMap param, ErrorInfo& e);

    /**
     * @brief	获取任务列表
     *
     * @param 		  	param 	查询参数
     * @param [in,out]	listTask  	任务列表
     * @param [in,out]	e		  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool getTaskList(StringMap param, ListStringMap& listTask, ErrorInfo& e);

    /**
     * @brief	获取操作票信息
     *
     * @param 		  	otID   	操作票ID
     * @param [in,out]	otHead 	票头信息
     * @param [in,out]	otItems	票项信息
     * @param [in,out]	e	   	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool getOT(std::string otID, StringMap& otHead, ListStringMap& otItems, ErrorInfo& e);

    /**
     * @brief	跳过当前步骤
     *
     * @param 		  	otID 	操作票ID
     * @param 		  	param 	票参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool skipItem(std::string otID, StringMap param, ErrorInfo& e);

    /**
     * @brief	更改操作票类型
     *
     * @param 		  	otID 	操作票ID
     * @param 		  	param	票参数
     * @param [in,out]	e	 	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool convertOT(std::string otID, StringMap param, ErrorInfo& e);

    /**
     * @brief	开始预演
     *
     * @param 		  	otID 	操作票ID
     * @param 		  	param 	票参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool startPreview(std::string otID, StringMap param, ErrorInfo& e);

    /**
     * @brief	停止预演
     *
     * @param 		  	otID 	操作票ID
     * @param 		  	param 	票参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool stopPreview(std::string otID, StringMap param, ErrorInfo& e);

    /**
     * @brief	暂停预演
     *
     * @param 		  	otID 	操作票ID
     * @param 		  	param 	票参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool pausePreview(std::string otID, StringMap param, ErrorInfo& e);

    /**
     * @brief	继续预演
     *
     * @param 		  	otID 	操作票ID
     * @param 		  	param 	票参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool resumePreview(std::string otID, StringMap param, ErrorInfo& e);

    /**
     * @brief	重试预演步骤
     *
     * @param 		  	otID 	操作票ID
     * @param 		  	param 	票参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool retryPreview(std::string otID, StringMap param, ErrorInfo& e);

    /**
     * @brief	确认预演
     *
     * @param 		  	otID 	操作票ID
     * @param 		  	param 	票参数
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false
     */
    bool confirmPreview(std::string otID, StringMap param, ErrorInfo& e);
    bool createOtItem(std::string otID, StringMap param, ListStringMap& items, ErrorInfo& e);
    bool deleteOtItem(std::string otID, StringMap param, ErrorInfo& e);
    bool downloadTask(std::string clientID, StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ErrorInfo& e);
    bool updateTask(ListStringMap listTask, ErrorInfo& e);
    bool updateItem(ListStringMap listItem, ErrorInfo& e);

public:
    void publishMessage(const QString& topic, const QString& msg, quint8 qos = 0, bool retain = false);
    void publishUserActionEvent(const std::string& taskID, const ZG6000::StringMap& param, std::string message);
    void sendEventMessage(const std::string& taskID, const std::string& eventTypeID, std::string message);
    bool sendCtrlCommand(const std::string& deviceID, const std::string& propertyName, const std::string& value);
    bool execTaskAction(const std::string& taskID, const StringMap& param,
                        ErrorInfo& e, std::function<bool(ZGOPTaskOTTicket*)> func);
    std::string getTaskState(const std::string& id) {
        return m_mapTaskState.find(id) != m_mapTaskState.end() ? m_mapTaskState[id] : "";
    }
    std::string getTaskStage(const std::string& id) {
        return m_mapTaskStage.find(id) != m_mapTaskStage.end() ? m_mapTaskStage[id] : "";
    }
    std::string getItemState(const std::string& id) {
        return m_mapItemState.find(id) != m_mapItemState.end() ? m_mapItemState[id] : "";
    }
    ZG6000::StringMap getAppNode(const std::string& id) {
        ZG6000::StringMap appNode;
        auto pair = m_mapAppNode.find(id);
        if (pair != m_mapAppNode.end())
            appNode = pair->second;
        return appNode;
    }
    ZG6000::StringMap getSystemParam() const {
        return m_mapSystemParam;
    }
    std::string getSubsystem(const std::string& id) {
        return m_mapSubsystem.find(id) != m_mapSubsystem.end() ? m_mapSubsystem[id] : "";
    }
    std::string getMajor(const std::string& id) {
        return m_mapMajor.find(id) != m_mapMajor.end() ? m_mapMajor[id] : "";
    }
    std::string getTemplate(const std::string& id) {
        return m_mapTemplate.find(id) != m_mapTemplate.end() ? m_mapTemplate[id] : "";
    }
    std::string getOTType(const std::string& id)
    {
        return m_mapOTType.find(id) != m_mapOTType.end() ? m_mapOTType[id] : "";
    }

private:
    explicit ZGOPTaskOTMng(QObject* parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initTask();
    void clearTask();
    bool initParams();
    bool initMqttClient();
    bool initRedisClient();
    ZGOPTaskOTTicket* fetchTask(std::string taskID);
    bool addTicket(std::string taskID);
    void removeTicket(std::string taskID);
    bool getOTHead(std::string otID, StringMap& otHead, ErrorInfo& e);
    bool getOTItems(std::string otID, ListStringMap& otItems, ErrorInfo& e);
    std::string generateUserEvent(const std::string& taskID, const ZG6000::StringMap& param, std::string message);
    ZG6000::StringMap generateEventMessage(const std::string& taskID, const std::string& eventTypeID, std::string message);
    void sendMessage(StringMap event);
    void dispatchTableData(const std::string& tableName, const std::string& oper, const std::string& reason, const std::string& time, const ListRecord& listRecord);
    bool switchToMaster();
    bool canUpdate() const;

private slots:
    void onTimer();
    void onReceivedMessage(const QString& channel, const QString& message);

private:
    bool m_initialized{ false };
    QString m_serverName{ "" };
    QString m_instName{ "" };
    std::string m_localNodeID;
    bool m_masterInst{ false };
    int m_initInterval{ 10 };
    int m_checkInterval{ 10 };
    long m_masterTickCount{ 0 };
    QTimer m_checkTimer;
    QDateTime m_lastDateTime;
    bool m_localNetNodeNormal{false};
    QDateTime m_lastNetRestoreTime;
    ZGMqttClient* m_pMqttClient{ nullptr };
    ZGRedisClient* m_pRedisQueue{nullptr};
    ZGRedisClient* m_pRedisTopic{nullptr};
    StringMap m_mapTaskState;
    StringMap m_mapTaskStage;
    StringMap m_mapItemState;
    StringMap m_mapDeviceSubType;
    MapStringMap m_mapAppNode;
    StringMap m_mapSubsystem;
    StringMap m_mapMajor;
    StringMap m_mapOTType;
    StringMap m_mapTemplate;
    StringMap m_mapTermItemGroup;
    StringMap m_mapTermItemType;
    StringMap m_mapSystemParam;
    MapStringMap m_mapTermItem;
    std::unordered_map<std::string, ZGOPTaskOTTicket*> m_mapTask{};
    QReadWriteLock m_lock;
    std::set<std::string> m_setTaskFields{"id", "name", "taskTypeID", "appNodeID", "subsystemID", "majorID", "rtCreateUserID", "rtCreateTime",
        "rtOperUserID", "rtIsOperVerify", "rtMonUserID", "rtIsMonVerify", "rtStartTime", "rtEndTime", "rtExecStartTime", "rtExecEndTime", "rtTaskStageID", "rtTaskStateID", "rtClientID", "rtErrorDesc"};
    std::set<std::string> m_setOtFields{"id", "rtNumber", "rtIsPreview", "rtPreviewStateID", "rtItemID", "rtItemIndex", "rtPreviewItemID", "rtPreviewItemIndex"};
};

inline static ZGOPTaskOTMng* g_pInstance = nullptr;

} // namespace ZG6000

#endif // ZG6000_ZGOPTASKOTMNG_H

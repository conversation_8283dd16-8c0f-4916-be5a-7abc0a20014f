#include "ZGServerApplication.h"
#include "ZGOPTaskOTI.h"

const QString ZG_SERVER_VERSION = "V1.00.00.0000";
const QString ZG_APP_VERSION = ZG_SERVER_VERSION + ZGPubFun::getCompileVersion(__DATE__, __TIME__);

int main(int argc, char* argv[])
{
    ZGServerApplication a(argc, argv, ZG_APP_VERSION);
    ZG6000::ZGOPTaskOTI* pServer = new ZG6000::ZGOPTaskOTI;
    if (!a.startServer(pServer))
    {
        return -1;
    }

    return a.exec();
}
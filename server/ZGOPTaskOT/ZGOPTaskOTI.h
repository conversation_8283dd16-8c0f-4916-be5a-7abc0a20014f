#ifndef ZG6000_ZGOPTASKOTI_H
#define ZG6000_ZGOPTASKOTI_H

#include <ZGOPTaskOT.h>

namespace ZG6000 {

class ZGOPTaskOTI : public ZG6000::ZGOPTaskOT
{
public:
    

    ZGOPTaskOTI();
    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;

    // ZGOPTaskBase interface
public:
    bool createOT(std::string taskTypeID, StringMap param, std::string& otID, ErrorInfo& e, const Ice::Current& current) override;
    bool deleteTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool startTask(std::string taskID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool editOT(std::string otID, StringMap head, ListStringMap items, ErrorInfo& e, const Ice::Current& current) override;
    bool pauseTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool resumeTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool retryTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool abolishTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool getTaskList(StringMap param, ListStringMap& listTask, ErrorInfo& e, const Ice::Current& current) override;
    bool confirmTask(std::string taskID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool getOT(std::string otID, StringMap& otHead, ListStringMap& otItems, ErrorInfo& e, const Ice::Current& current) override;
    bool skipItem(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool startPreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool stopPreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool pausePreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool resumePreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool retryPreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool confirmPreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool convertOT(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool createOtItem(std::string otID, StringMap param, ListStringMap& items, ErrorInfo& e, const Ice::Current& current) override;
    bool getDeviceTerm(std::string deviceID, StringMap param, ListStringMap& terms, ErrorInfo& e, const Ice::Current& current) override;
    bool getCommonTerm(StringMap param, ListStringMap& terms, ErrorInfo& e, const Ice::Current& current) override;
    bool deleteOtItem(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool setOtSimulateValue(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool clearOtSimulateValue(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool downloadTask(std::string clientID, StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ErrorInfo& e, const Ice::Current& current) override;
    bool updateTask(ListStringMap listTask, ErrorInfo& e, const Ice::Current& current) override;
    bool updateItem(ListStringMap listItem, ErrorInfo& e, const Ice::Current& current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGOPTASKOTI_H

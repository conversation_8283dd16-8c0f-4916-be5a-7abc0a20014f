#include "ZGOPTaskOTPreviewItem.h"
#include "ZGOPTaskOTTicket.h"
#include <QJsonDocument>

ZGOPTaskOTPreviewItem::ZGOPTaskOTPreviewItem(QObject *parent)
    : ZGOPTaskOTItem{parent}
{

}

bool ZGOPTaskOTPreviewItem::getCurrentState(const ZG6000::StringMap& item, std::string& state)
{
    try
    {
        state = ZGUtils::get(item, "rtPreviewStateID");
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

bool ZGOPTaskOTPreviewItem::setCurrentState(const std::string& itemID, const std::string& state)
{
	QString sql = QString("UPDATE op_param_ot_item SET rtPreviewStateID = '%1' WHERE id = '%2'").arg(state.c_str()).arg(itemID.c_str());
	if (!ZGProxyCommon::execSql(sql.toStdString()))
	{
		ZGLOG_ERROR(QStringLiteral("更新步骤项'%1'预演状态失败").arg(itemID.c_str()));
		return false;
	}
	ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTPreviewItem::checkItemCondition(const ZG6000::StringMap& otItem)
{
    return true;
}

void ZGOPTaskOTPreviewItem::processVerifyState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
	ZGOPTaskOTItem::processVerifyState(execTime, otItem);
}

void ZGOPTaskOTPreviewItem::processExecuteState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    const auto& itemID = otItem["id"];
    try
    {
        const auto& deviceID = ZGUtils::get(otItem, "deviceID");
        ZG6000::ErrorInfo e;
        if (otItem["isCheckConfirmCondition"] == "1")
        {
            auto ruleEngine = ZGProxyMng::instance()->getProxyMPRuleEngine();
            if (ruleEngine == nullptr)
            {
                ZGLOG_ERROR(QStringLiteral("获取规则引擎服务代理对象失败"));
                return;
            }
            const auto& termID = ZGUtils::get(otItem, "termID");
            if (!deviceID.empty())
            {
                ZGLOG_TRACE(QString("setDeviceConfirmCondition, deviceID: '%1', termID: '%2'").arg(deviceID.c_str()).arg(termID.c_str()));
                if (!ruleEngine->setDeviceConfirmCondition(deviceID, termID, e))
                {
                    ZGLOG_ERROR(e);
                    ZGLOG_ERROR(QStringLiteral("设置设备'%1'操作术语'%2'确认条件失败").arg(deviceID.c_str()).arg(termID.c_str()));
                    return;
                }
            }
            else
            {
                const auto& otID = ZGUtils::get(otItem, "otID");
                std::string appNodeID;
                if (!ZGProxyCommon::getDataByField("op_param_task", otID, "appNodeID", appNodeID))
                {
                    ZGLOG_ERROR(QStringLiteral("获取操作票'%1'所属应用节点失败").arg(otID.c_str()));
                    return;
                }
                ZGLOG_TRACE(QString("setAppNodeConfirmCondition, appNodeID: '%1', termID: '%2'").arg(appNodeID.c_str()).arg(termID.c_str()));
                if (!ruleEngine->setAppNodeConfirmCondition(appNodeID, termID, e))
                {
                    ZGLOG_ERROR(e);
                    ZGLOG_ERROR(QStringLiteral("设置应用节点'%1'操作术语'%2'确认条件失败").arg(appNodeID.c_str()).arg(termID.c_str()));
                    return;
                }
            }
        }
        if (!otItem["updateValues"].empty())
        {
            QJsonDocument doc = QJsonDocument::fromJson(otItem["updateValues"].c_str());
            QJsonObject object = doc.object();
            if (!object.empty())
            {
                ZG6000::MapStringMap propertis;
                for (auto it = object.begin(); it != object.end(); ++it)
                {
                    propertis[it.key().toStdString()] = ZG6000::StringMap{{"rtSimulateFlag", "1"},
                        {"rtSimulateValue", it.value().toVariant().toString().toStdString()}};
                }
                if (!ZGProxyCommon::updateProperties(deviceID, propertis, e))
                {
                    ZGLOG_ERROR(e);
                    ZGLOG_ERROR(QStringLiteral("更新设备'%1'预演值失败"));
                    return;
                }
            }
        }
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
        return;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return;
    }
    setCurrentState(itemID, "ZG_OIS_CONFIRM");
}

void ZGOPTaskOTPreviewItem::processConfirmState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    ZGOPTaskOTItem::processConfirmState(execTime, otItem);
}

void ZGOPTaskOTPreviewItem::processFinishState(const QDateTime& execTime, ZG6000::StringMap otItem)
{
    try
    {
        QDateTime currTime = QDateTime::currentDateTime();
        int endDelay = std::atoi(ZGUtils::get(otItem, "endDelay").c_str());
        if (m_confirmDateTime.secsTo(currTime) >= endDelay)
        {
            auto task = dynamic_cast<ZGOPTaskOTTicket*>(parent());
            task->nextPreviewItem();
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

bool ZGOPTaskOTPreviewItem::notify(const std::string &action, ZG6000::StringMap args, std::string &errMsg)
{
    return m_pTicket->previewCommand(action, std::move(args), errMsg);
}

bool ZGOPTaskOTPreviewItem::nextItem()
{
    auto task = dynamic_cast<ZGOPTaskOTTicket*>(parent());
    if (task)
        return task->nextPreviewItem();
    return false;
}

int ZGOPTaskOTPreviewItem::getSimFlag()
{
    return 1;
}

std::string ZGOPTaskOTPreviewItem::getExecTime(const ZG6000::StringMap& otItem)
{
    const auto& previewExecTime = ZGUtils::get(otItem, "rtPreviewExecTime", "");
    return ZGUtils::get(otItem, "rtPreviewExecTime", "");
}

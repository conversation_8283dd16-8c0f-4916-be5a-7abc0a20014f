#include "ZGOPTaskOTMng.h"
#include "ZGOPTaskOTTicket.h"
#include "zgerror/ZGOPTaskOTError.h"

#include <QJsonObject>
#include <QtConcurrent>
#include <utility>
#include <ZGJson.h>

#include "ZGRuntime.h"
#include "ZGUtils.h"

namespace ZG6000
{
    ZGOPTaskOTMng* ZGOPTaskOTMng::instance()
    {
        if (g_pInstance == nullptr)
            g_pInstance = new ZGOPTaskOTMng;
        return g_pInstance;
    }

    void ZGOPTaskOTMng::init()
    {
        initEvents();
        initServerInstConfig();
        while (!initServerInstInfo())
        {
            ZGLOG_ERROR("initServerInstInfo error.");
            QThread::msleep(m_initInterval * 1000);
        }
        QThread::msleep(QRandomGenerator::global()->bounded(5, 10));
        while (!initParams())
        {
            ZGLOG_ERROR("initParams error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initRedisClient())
        {
            ZGLOG_ERROR("initRedisClient");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initMqttClient())
        {
            ZGLOG_ERROR("initMqttClient error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initTask())
        {
            ZGLOG_ERROR("initTask error.");
            QThread::msleep(m_initInterval * 1000);
        }
        m_initialized = true;
        m_lastDateTime = QDateTime::currentDateTime();
        m_localNetNodeNormal = ZGPubFun::isLocalNetNormal();
        ZGLOG_INFO(QString("m_localNetNodeNormal = %1").arg(m_localNetNodeNormal));
        m_lastNetRestoreTime = QDateTime::currentDateTime();
        m_checkTimer.start(1000);
        ZGLOG_INFO("ZGOPTaskOT init finished.");
    }

    bool ZGOPTaskOTMng::checkState()
    {
        return m_initialized;
    }

    void ZGOPTaskOTMng::dispatchTableData(const std::string& tableName, const std::string& oper, const std::string& reason, const std::string& time, const ListRecord& listRecord)
    {
        if (!m_initialized)
            return;
        if (reason != "change")
            return;
        if (oper == "update")
        {
            if (!ZGRuntime::instance()->isMaster())
                return;
            try
            {
                for (const auto& record : listRecord)
                {
                    std::string otID;
                    if (tableName == "op_param_task" || tableName == "op_param_ot")
                    {
                        otID = ZGUtils::get(record, "id").newValue;
                    }
                    if (tableName == "op_param_ot_item")
                    {
                        const auto& itemID = ZGUtils::get(record, "id").newValue;
                        if (!ZGProxyCommon::getDataByField("op_param_ot_item", itemID, "otID", otID))
                            continue;
                    }
                    if (tableName == "sp_real_exam")
                    {
                        const auto& pair = record.find("examStateID");
                        if (pair == record.end())
                            continue;
                        const auto& examID = ZGUtils::get(record, "id").newValue;
                        QString sql = QString("SELECT id FROM op_param_task WHERE rtExamID = '%1'").arg(examID.c_str());
                        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), otID))
                            continue;
                        if (otID.empty())
                            continue;
                    }
                    auto ticket = fetchTask(otID);
                    if (ticket != nullptr)
                        ticket->dispatchData(tableName, record);
                }
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(e.what());
            }
        }
        if (oper == "insert")
        {
            try
            {
                if (tableName == "op_param_task")
                {
                    if (!ZGRuntime::instance()->isMaster())
                        return;
                    for (const auto& record : listRecord)
                    {
                        std::string taskID = ZGUtils::get(record, "id").newValue;
                        std::string taskTypeID;
                        if (ZGProxyCommon::getDataByField("op_param_task", taskID, "taskTypeID", taskTypeID))
                        {
                            publishMessage(QString("op_param_task/%1/%2").arg(taskTypeID.c_str()).arg(oper.c_str()), QString("%1").arg(taskID.c_str()));
                        }
                    }
                }
                if (tableName == "op_param_ot")
                {
                    for (const auto& record : listRecord)
                    {
                        std::string taskID = ZGUtils::get(record, "id").newValue;
                        ZGLOG_TRACE(QStringLiteral("添加操作票'%1'对象").arg(taskID.c_str()));
                        if (!addTicket(taskID))
                            ZGLOG_ERROR(QStringLiteral("添加操作票'%1'对象失败").arg(taskID.c_str()));
                    }
                }
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(e.what());
            }
        }
        if (oper == "delete")
        {
            try
            {
                if (tableName == "op_param_task")
                {
                    ZGLOG_DEBUG("op_param_task delete");
                    for (const auto& record : listRecord)
                    {
                        std::string taskID = ZGUtils::get(record, "id").newValue;
                        removeTicket(taskID);
                        if (ZGRuntime::instance()->isMaster())
                            publishMessage(QString("op_param_task/%1").arg(oper.c_str()), QString("%1").arg(taskID.c_str()));
                    }
                }
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(e.what());
            }
        }
    }

    bool ZGOPTaskOTMng::switchToMaster()
    {
        if (!initTask())
            return false;
        ZG6000::StringMap msg{{"nodeID", m_localNodeID}, {"oldState", "slave"}, {"newState", "master"}};
        const auto& json = ZGJson::convertToJson(msg);
        publishMessage("system/ot/switch", json.c_str());
        return true;
    }

    bool ZGOPTaskOTMng::canUpdate() const
    {
        ZGLOG_INFO(QString("m_localNetNodeNormal = %1, m_lastNetRestoreTime = %2").arg(m_localNetNodeNormal).arg(m_lastNetRestoreTime.toString("yyyy-MM-dd hh:mm:ss.zzz")));
        if (!m_localNetNodeNormal)
            return false;
        if (m_localNetNodeNormal && m_lastNetRestoreTime.secsTo(QDateTime::currentDateTime()) < 20)
            return false;
        return true;
    }

    bool ZGOPTaskOTMng::createOT(std::string taskTypeID, StringMap param, std::string& taskID, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!ZGOPTaskOTTicket::createTicket(std::move(taskTypeID), std::move(param), taskID, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOTMng::editOT(std::string otID, StringMap head, ListStringMap items, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        auto ticket = fetchTask(otID);
        if (ticket == nullptr)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("操作票'%1'不可用").arg(otID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return ticket->editTicket(std::move(otID), std::move(head), std::move(items), e);
    }

    bool ZGOPTaskOTMng::deleteTask(std::string taskID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(taskID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            std::string otType;
            QString sql = QStringLiteral("SELECT typeID FROM op_param_ot WHERE id = '%1'").arg(taskID.c_str());
            if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), otType))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取操作票'%1'类型失败").arg(taskID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            std::string taskName;
            sql = QStringLiteral("SELECT name FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
            if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), taskName))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取任务'%1'名称失败").arg(taskID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            QString message = QStringLiteral("删除任务【%1】").arg(taskName.c_str());
            const auto& msg = generateUserEvent(taskID, param, message.toStdString());
            const auto& event = generateEventMessage(taskID, "ZG_ET_OPER", msg);
            if (!ticket->deleteTicket(e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            sendMessage(event);
            return true;
        });
    }

    bool ZGOPTaskOTMng::startTask(std::string taskID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(taskID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            if (!ticket->start(param, e))
                return false;
            std::string taskName;
            ZGProxyCommon::getDataByField("op_param_task", taskID, "name", taskName);
            QString message = QStringLiteral("启动任务【%1】").arg(taskName.c_str());
            publishUserActionEvent(taskID, param, message.toStdString());
            std::string appNodeID;
            ZGProxyCommon::getDataByField("op_param_task", taskID, "appNodeID", appNodeID);
            if (!appNodeID.empty())
            {
                std::string bcGroupID;
                ZGProxyCommon::getDataByField("sp_param_appnode", appNodeID, "bcGroupID", bcGroupID);
                if (!bcGroupID.empty())
                {
                    auto broadcastProxy = ZGProxyMng::instance()->getProxyMPBroadcastServer();
                    if (broadcastProxy)
                    {
                        try
                        {
                            broadcastProxy->playGroupTTS(bcGroupID, message.toStdString(), e);
                        }
                        catch (const Ice::Exception& ie)
                        {
                            ZGLOG_ERROR(ie.what());
                        }
                    }
                }
            }
            return true;
        });
    }

    bool ZGOPTaskOTMng::confirmTask(std::string taskID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(taskID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            ZG6000::StringMap task;
            ZGProxyCommon::getDataByFields("op_param_task", taskID, {"name", "rtTaskStageID"}, task);
            if (!ticket->confirm(param, e))
                return false;
            if (task["rtTaskStageID"] == "ZG_TS_EXECUTE")
            {
                QString message = QStringLiteral("完成任务【%1】").arg(task["name"].c_str());
                publishUserActionEvent(taskID, param, message.toStdString());
            }
            return true;
        });
    }

    bool ZGOPTaskOTMng::getTaskList(ZG6000::StringMap param, ListStringMap& listTask, ErrorInfo& e)
    {
        std::string condition = param.find("condition") != param.end() ? param["condition"] : "1=1";
        std::string orderType = param.find("order") != param.end() ? param["order"] : "ASC";
        std::string orderField = param.find("sort") != param.end() ? param["sort"] : "a.id";
        std::string offset = param.find("offset") != param.end() ? param["offset"] : "0";
        std::string limit = param.find("limit") != param.end() ? param["limit"] : "1000";
        QString addition = QString(" ORDER BY %1 %2 LIMIT %3, %4").arg(orderField.c_str())
                                                                  .arg(orderType.c_str()).arg(offset.c_str()).arg(limit.c_str());
        QString sql = QString("SELECT * FROM op_param_task a "
            "WHERE %1").arg(condition.c_str()) + addition;
        auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
        if (dbProxy == nullptr)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取数据服务代理对象失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listTask, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            for (auto& task : listTask)
            {
                const auto& appNode = getAppNode(task["appNodeID"]);
                task["appNodeName"] = ZGUtils::get(appNode, "name", "");
                task["subsystemName"] = ZGUtils::get(m_mapSubsystem, task["subsystemID"], "");
                task["majorName"] = ZGUtils::get(m_mapMajor, task["majorID"], "");
                std::string operUserName;
                if (!task["rtOperUserID"].empty())
                    ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtOperUserID"], "name", operUserName);
                task["rtOperUserName"] = operUserName;
                std::string monUserName;
                if (!task["rtMonUserID"].empty())
                    ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtMonUserID"], "name", monUserName);
                task["rtMonUserName"] = monUserName;
                task["rtTaskStageName"] = ZGUtils::get(m_mapTaskStage, task["rtTaskStageID"], "");
                task["rtTaskStateName"] = ZGUtils::get(m_mapTaskState, task["rtTaskStateID"], "");
                ZG6000::StringMap ot;
                ZGProxyCommon::getDataByFields("op_param_ot", task["id"], {"typeID", "rtNumber", "rtTaskOrder", "rtPreviewStateID"}, ot);
                task["typeID"] = ot["typeID"];
                task["typeName"] = ZGUtils::get(m_mapOTType, task["typeID"], "");
                task["rtNumber"] = ot["rtNumber"];
                task["rtTaskOrder"] = ot["rtTaskOrder"];
                task["rtPreviewStateID"] = ot["rtPreviewStateID"];
                task["rtPreviewStateName"] = ZGUtils::get(m_mapTaskState, ot["rtPreviewStateID"], "");
            }
            return true;
        }
        catch (const Ice::Exception& ie)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = ie.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOTMng::getOT(std::string otID, StringMap& otHead, ListStringMap& otItems, ErrorInfo& e)
    {
        if (!getOTHead(otID, otHead, e))
            return false;
        if (!getOTItems(otID, otItems, e))
            return false;
        return true;
    }

    bool ZGOPTaskOTMng::skipItem(std::string otID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(otID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            const auto& skipUserID = ZGUtils::get(param, "skipUserID", "");
            if (skipUserID.empty())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("跳步执行人为空").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (!ticket->skip(param, e))
                return false;
            std::string taskName;
            ZGProxyCommon::getDataByField("op_param_task", otID, "name", taskName);
            std::string skipUserName;
            if (!skipUserID.empty())
                ZGProxyCommon::getDataByField("sp_param_hrm_user", skipUserID, "name", skipUserName);
            QString message = QStringLiteral("【%1】跳步执行，执行人：【%2】").arg(taskName.c_str()).arg(skipUserName.c_str());
            ZGLOG_INFO(message);
            sendEventMessage(otID.c_str(), "ZG_ET_OPER", message.toStdString());
            return true;
        });
    }

    bool ZGOPTaskOTMng::convertOT(std::string otID, StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(otID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            return ticket->convert(param, e);
        });
    }

    bool ZGOPTaskOTMng::startPreview(std::string otID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(otID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            if (!ticket->previewStart(param, e))
                return false;
            return true;
        });
    }

    bool ZGOPTaskOTMng::stopPreview(std::string otID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(otID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            if (!ticket->previewStop(param, e))
                return false;
            return true;
        });
    }

    bool ZGOPTaskOTMng::pausePreview(std::string otID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(otID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            return ticket->previewPause(param, e);
        });
    }

    bool ZGOPTaskOTMng::resumePreview(std::string otID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(otID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            return ticket->previewResume(param, e);
        });
    }

    bool ZGOPTaskOTMng::retryPreview(std::string otID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(otID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            return ticket->previewRetry(param, e);
        });
    }

    bool ZGOPTaskOTMng::confirmPreview(std::string otID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(otID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            if (!ticket->previewConfirm(param, e))
                return false;
            return true;
        });
    }

    bool ZGOPTaskOTMng::createOtItem(std::string otID, StringMap param, ListStringMap& items, ErrorInfo& e)
    {
        return execTaskAction(otID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            return ticket->createItem(param, items, e);
        });
    }

    bool ZGOPTaskOTMng::deleteOtItem(std::string otID, StringMap param, ErrorInfo& e)
    {
        return execTaskAction(otID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            return ticket->deleteItem(param, e);
        });
    }

    bool ZGOPTaskOTMng::downloadTask(std::string clientID, StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ErrorInfo& e)
    {
        const auto& ids = ZGUtils::join(listTaskID, ",", "'", "'");
        QString sql = QString("SELECT a.*, b.* FROM op_param_task a LEFT JOIN op_param_ot b ON a.id = b.id WHERE a.id IN (%1) ORDER BY a.id").arg(ids.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取操作票列表失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (auto& task : listTask)
        {
            if (task["taskTypeID"] != "ZG_TT_OT")
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("任务'%1'不是操作票任务").arg(task["id"].c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (task["rtClientID"] != clientID && (!task["rtClientID"].empty()))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("操作票'%1'已下载到客户端'%2'").arg(task["id"].c_str()).arg(task["rtClientID"].c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            task.erase("id(1)");
        }
        sql = QString("SELECT * FROM op_param_ot_item WHERE otID IN (%1)").arg(ids.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listItem))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取操作票项列表失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZG6000::StringList listSql;
        for (const auto& taskID : listTaskID)
        {
            ZG6000::StringMap task{{"id", taskID}, {"rtClientID", clientID}};
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新操作票关联客户端失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOTMng::updateTask(ListStringMap listTask, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringList listSql;
        auto json = ZGJson::convertToJson(listTask);
        ZGLOG_INFO(json.c_str());
        for (auto& task : listTask)
        {
            QString errMsg;
            if (!ZGUtils::checkRequiredParam(task, {"id"}, errMsg))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
                e.errDetail = errMsg.toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            StringMap taskTotal, taskOt;
            for (auto& [key, value] : task)
            {
                if (m_setTaskFields.find(key) != m_setTaskFields.end())
                    taskTotal[key] = value;
                if (m_setOtFields.find(key) != m_setOtFields.end())
                    taskOt[key] = value;
            }
            taskTotal["rtVersion"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
            if (!taskTotal.empty())
                listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskTotal));
            if (!taskOt.empty())
                listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", taskOt));
        }
        if (!listSql.empty())
        {
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_DB);
                e.errDetail = QStringLiteral("更新任务失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            ZGProxyCommon::synchronize();
        }
        return true;
    }

    bool ZGOPTaskOTMng::updateItem(ListStringMap listItem, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringList listSql;
        auto json = ZGJson::convertToJson(listItem);
        ZGLOG_INFO(json.c_str());
        for (auto& item : listItem)
        {
            try
            {
                QString errMsg;
                if (!ZGUtils::checkRequiredParam(item, {"id"}, errMsg))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
                    e.errDetail = errMsg.toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                const auto& id = ZGUtils::get(item, "id");
                StringMap itemParam;
                if (!ZGProxyCommon::getDataByID("op_param_ot_item", id, itemParam))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_RT);
                    e.errDetail = QStringLiteral("获取任务项'%1'信息失败").arg(id.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                if (item.find("rtStateID") != item.end())
                {
                    const auto& rtStateID = ZGUtils::get(item, "rtStateID");
                    if (rtStateID == "ZG_OIS_FINISHED")
                    {
                        const auto& deviceID = ZGUtils::get(itemParam, "deviceID");
                        const auto& updateValues = ZGUtils::get(itemParam, "updateValues");
                        if (!updateValues.empty())
                        {
                            QJsonDocument doc = QJsonDocument::fromJson(updateValues.c_str());
                            QJsonObject obj = doc.object();
                            if (!obj.empty())
                            {
                                StringMap propertyValues;
                                for (auto it = obj.begin(); it != obj.end(); ++it)
                                {
                                    ZGLOG_TRACE(QString("key: '%1', value: '%2'").arg(it.key()).arg(it.value().toVariant().toString()));
                                    propertyValues[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
                                }
                                if (!ZGProxyCommon::updatePropertyValues(deviceID, propertyValues, e, true))
                                {
                                    ZGLOG_ERROR(e);
                                    return false;
                                }
                            }
                        }
                    }
                }
                listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot_item", item));
            }
            catch (const std::exception& ex)
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
                e.errDetail = ex.what();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        if (!listSql.empty())
        {
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_DB);
                e.errDetail = QStringLiteral("更新任务项失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            ZGProxyCommon::synchronize();
        }
        return true;
    }

    bool ZGOPTaskOTMng::pauseTask(std::string taskID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(taskID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            if (!ticket->pause(param, e))
                return false;
            std::string taskName;
            ZGProxyCommon::getDataByField("op_param_task", taskID, "name", taskName);
            QString message = QStringLiteral("暂停任务【%1】").arg(taskName.c_str());
            publishUserActionEvent(taskID, param, message.toStdString());
            return true;
        });
    }

    bool ZGOPTaskOTMng::resumeTask(std::string taskID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(taskID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            if (!ticket->resume(param, e))
                return false;
            std::string taskName;
            ZGProxyCommon::getDataByField("op_param_task", taskID, "name", taskName);
            QString message = QStringLiteral("继续任务【%1】").arg(taskName.c_str());
            publishUserActionEvent(taskID, param, message.toStdString());
            return true;
        });
    }

    bool ZGOPTaskOTMng::retryTask(std::string taskID, ZG6000::StringMap param, ErrorInfo& e)
    {
        if (!canUpdate())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("网络状态异常……").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return execTaskAction(taskID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            if (!ticket->retry(param, e))
                return false;
            std::string taskName;
            ZGProxyCommon::getDataByField("op_param_task", taskID, "name", taskName);
            QString message = QStringLiteral("重试任务【%1】").arg(taskName.c_str());
            publishUserActionEvent(taskID, param, message.toStdString());
            return true;
        });
    }

    bool ZGOPTaskOTMng::abolishTask(std::string taskID, ZG6000::StringMap param, ErrorInfo& e)
    {
        return execTaskAction(taskID, param, e, [&](ZGOPTaskOTTicket* ticket)
        {
            std::string taskName;
            ZGProxyCommon::getDataByField("op_param_task", taskID, "name", taskName);
            QString message = QStringLiteral("作废任务【%1】").arg(taskName.c_str());
            const auto& msg = generateUserEvent(taskID, param, message.toStdString());
            const auto& event = generateEventMessage(taskID, "ZG_ET_OPER", msg);
            if (!ticket->abolish(param, e))
                return false;
            sendMessage(event);
            return true;
        });
    }

    void ZGOPTaskOTMng::publishMessage(const QString& topic, const QString& msg, quint8 qos, bool retain)
    {
        if (m_pMqttClient)
            m_pMqttClient->sendPublish(topic, msg, qos, retain);
    }

    void ZGOPTaskOTMng::publishUserActionEvent(const std::string& taskID, const ZG6000::StringMap& param, std::string message)
    {
        const auto& operUserID = ZGUtils::get(param, "operator", "");
        const auto& monUserID = ZGUtils::get(param, "monitor", "");
        std::string operUserName, monUserName;
        if (!operUserID.empty())
            ZGProxyCommon::getDataByField("sp_param_hrm_user", operUserID, "name", operUserName);
        if (!monUserID.empty())
            ZGProxyCommon::getDataByField("sp_param_hrm_user", monUserID, "name", monUserName);
        std::string eventMessage = std::move(message);
        if (!operUserName.empty())
            eventMessage += u8"，操作员：【" + operUserName + u8"】";
        if (!monUserName.empty())
            eventMessage += u8"，监护员：【" + monUserName + u8"】";
        ZGLOG_INFO(eventMessage.c_str());
        sendEventMessage(taskID, "ZG_ET_OPER", std::move(eventMessage));
    }

    void ZGOPTaskOTMng::sendEventMessage(const std::string& taskID, const std::string& eventTypeID, std::string message)
    {
        auto eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
        ZG6000::StringMap event;
        event["eventTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        event["eventTypeID"] = eventTypeID;
        event["alarmLevelID"] = "ZG_AL_LEVEL0";
        ZG6000::StringMap record;
        if (!ZGProxyCommon::getDataByFields("op_param_task", taskID, {"appNodeID", "subsystemID", "majorID"}, record))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务'%1'应用节点信息失败").arg(taskID.c_str()));
            return;
        }
        event["appNodeID"] = std::move(record["appNodeID"]);
        event["subsystemID"] = std::move(record["subsystemID"]);
        event["majorID"] = std::move(record["majorID"]);
        event["eventInfo"] = std::move(message);
        try
        {
            auto oneway = eventProcessPrx->ice_oneway();
            oneway->processEvent(event);
        }
        catch (const Ice::Exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    bool ZGOPTaskOTMng::sendCtrlCommand(const std::string& deviceID, const std::string& propertyName, const std::string& value)
    {
        ZGLOG_DEBUG(QString("sendCtrlCommand, deviceID = '%1', propertyName = '%2', value = '%3'").arg(deviceID.c_str()).arg(propertyName.c_str()).arg(value.c_str()));
        std::string tableName, dataID;
        ZG6000::ErrorInfo e;
        if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        QJsonObject command;
        command["id"] = dataID.c_str();
        std::string modelTable = tableName;
        ZGUtils::replaceString(modelTable, "dataset", "model");
        std::string sql =
            "SELECT a.isSelectCtrl FROM " + modelTable + " a LEFT JOIN " + tableName + " b ON b.dataModelID = a.id "
            "WHERE b.id = '" + dataID + "' ORDER BY a.id";
        std::string result;
        if (!ZGProxyCommon::execQuerySqlField(sql, result))
        {
            ZGLOG_ERROR("execQuerySqlField error.");
            return false;
        }
        QString commandID;
        if (tableName == "mp_param_dataset_yk")
            commandID = "ZG_DC_YK_EXEC";
        if (tableName == "mp_param_dataset_ys")
            commandID = "ZG_DC_YS_EXEC";
        if (result == "1")
        {
            if (tableName == "mp_param_dataset_yk")
                commandID = "ZG_DC_YK_SELECT";
            if (tableName == "mp_param_dataset_ys")
                commandID = "ZG_DC_YS_SELECT";
        }
        command["commandID"] = commandID;
        command["isReturnValue"] = "0";
        command["srcType"] = "auto";
        command["srcID"] = "-1";
        command["rtCode"] = QString::number(ZGUtils::genNumber(0, 10000));
        command["rtValue"] = value.c_str();
        command["rtCommandTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
        QJsonArray array;
        array.append(command);
        QJsonDocument doc(array);
        long long size;
        QString errMsg;
        std::string topicName;
        if (tableName == "mp_param_dataset_yk")
            topicName = "ZG_Q_SYSTEM_YK";
        if (tableName == "mp_param_dataset_ys")
            topicName = "ZG_Q_SYSTEM_YS";
        ZGLOG_DEBUG(doc.toJson());
        if (!m_pRedisQueue->rpush(topicName.c_str(), doc.toJson(), size, errMsg))
        {
            ZGLOG_ERROR("Send command to ys queue error.");
        }
        return true;
    }

    bool ZGOPTaskOTMng::execTaskAction(const std::string& taskID, const ZG6000::StringMap& param,
                                       ErrorInfo& e, std::function<bool(ZGOPTaskOTTicket*)> func)
    {
        auto ticket = fetchTask(taskID);
        if (ticket == nullptr)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到可用的任务'%1'").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return func(ticket);
    }

    ZGOPTaskOTMng::ZGOPTaskOTMng(QObject* parent)
        : QObject{parent}
    {
    }

    void ZGOPTaskOTMng::initEvents()
    {
        connect(&m_checkTimer, &QTimer::timeout, this, &ZGOPTaskOTMng::onTimer);
    }

    void ZGOPTaskOTMng::initServerInstConfig()
    {
        const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
        QString errMsg;
        int value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
            ZGLOG_WARN(errMsg);
        else
            m_initInterval = value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
            ZGLOG_WARN(errMsg);
        else
            m_checkInterval = value;
    }

    bool ZGOPTaskOTMng::initServerInstInfo()
    {
        m_serverName = ZGRuntime::instance()->getServerID();
        if (m_serverName.isEmpty())
        {
            ZGLOG_ERROR("Empty server id.");
            return false;
        }
        m_instName = ZGRuntime::instance()->getInstanceID();
        if (m_instName.isEmpty())
        {
            ZGLOG_ERROR("Empty server instance id.");
            return false;
        }
        m_localNodeID = ZGPubFun::getLocalNodeID().toStdString();
        return true;
    }

    bool ZGOPTaskOTMng::initTask()
    {
        ZGLOG_INFO(QStringLiteral("初始化操作票任务"));
        {
            QWriteLocker locker(&m_lock);
            clearTask();
        }
        QString sql = "SELECT id, rtTaskStageID, rtExamID FROM op_param_task WHERE taskTypeID = 'ZG_TT_OT'";
        ListStringMap listTask;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
        {
            ZGLOG_ERROR(QString("获取操作票任务列表失败"));
            return false;
        }
        for (auto& task : listTask)
        {
            if (task["rtTaskStageID"] == "ZG_TS_EXAM")
            {
                if (task["rtExamID"].empty())
                {
                    ZGLOG_ERROR(QStringLiteral("操作票'%1'审批ID为空").arg(task["rtExamID"].c_str()));
                    return false;
                }
                const auto& examID = task["rtExamID"];
                sql = QString("SELECT examStateID FROM sp_real_exam WHERE id = '%1'").arg(examID.c_str());
                ZG6000::StringList listExamState;
                if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listExamState))
                {
                    ZGLOG_ERROR(QStringLiteral("获取操作票'%1'审批信息失败").arg(task["id"].c_str()));
                    return false;
                }
                if (listExamState.empty())
                {
                    ZGLOG_ERROR(QStringLiteral("找不到操作票'%1'审批信息").arg(task["id"].c_str()));
                    return false;
                }
                const auto& examStateID = listExamState[0];
                if (examStateID == "ZG_ES_ACCEPT")
                {
                    StringMap taskState{{"id", task["id"]}, {"rtTaskStateID", "ZG_TS_FINISHED"}};
                    sql = ZGUtils::generateUpdateSql("op_param_task", taskState).c_str();
                    if (!ZGProxyCommon::execSql(sql.toStdString()))
                    {
                        ZGLOG_ERROR(QStringLiteral("更新操作票'%1'审批完成状态失败").arg(task["id"].c_str()));
                        return false;
                    }
                }
            }
            if (!addTicket(task["id"]))
            {
                ZGLOG_ERROR(QStringLiteral("初始化操作票'%1'失败").arg(task["id"].c_str()));
                return false;
            }
        }
        return true;
    }

    void ZGOPTaskOTMng::clearTask()
    {
        for (auto& [taskID, ticket] : m_mapTask)
        {
            if (ticket)
                delete ticket;
        }
        m_mapTask.clear();
    }

    bool ZGOPTaskOTMng::initParams()
    {
        std::string sql = "SELECT id, name FROM op_dict_task_state";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTaskState))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务状态信息失败"));
            return false;
        }
        sql = "SELECT id, name FROM op_dict_task_stage";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTaskStage))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务阶段信息失败"));
            return false;
        }
        sql = "SELECT id, name FROM op_dict_ot_item_state";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapItemState))
        {
            ZGLOG_ERROR(QStringLiteral("获取操作票项状态信息失败"));
            return false;
        }
        sql = "SELECT id, name FROM mp_dict_device_subtype";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDeviceSubType))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备子类型信息失败"));
            return false;
        }
        sql = "SELECT id, name FROM op_dict_ot_term_item_group";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTermItemGroup))
        {
            ZGLOG_ERROR(QStringLiteral("获取操作术语项组失败"));
            return false;
        }
        sql = "SELECT id, name FROM op_dict_ot_term_item_type";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTermItemType))
        {
            ZGLOG_ERROR(QStringLiteral("获取操作术语项类型失败"));
            return false;
        }
        sql = "SELECT id, name, voice FROM sp_param_appnode";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapAppNode))
        {
            ZGLOG_ERROR(QStringLiteral("获取应用节点信息失败"));
            return false;
        }
        sql = "SELECT id, name FROM sp_param_subsystem";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapSubsystem))
        {
            ZGLOG_ERROR(QStringLiteral("获取子系统信息失败"));
            return false;
        }
        sql = "SELECT id, name FROM sp_param_major";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapMajor))
        {
            ZGLOG_ERROR(QStringLiteral("获取专业信息失败"));
            return false;
        }
        sql = "SELECT id, name FROM op_dict_ot_type";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapOTType))
        {
            ZGLOG_ERROR(QStringLiteral("获取票类型失败"));
            return false;
        }
        sql = "SELECT id, name FROM op_param_ot_template";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTemplate))
        {
            ZGLOG_ERROR(QStringLiteral("获取票模板失败"));
            return false;
        }
        sql = "SELECT * FROM op_param_ot_term_item";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapTermItem))
        {
            ZGLOG_ERROR(QStringLiteral("获取操作术语项失败"));
            return false;
        }
        sql = "SELECT * FROM op_param_ot_system";
        ZG6000::ListStringMap listSystamParam;
        if (!ZGProxyCommon::execQuerySql(sql, listSystamParam))
        {
            ZGLOG_ERROR(QStringLiteral("获取操作票系统参数失败"));
            return false;
        }
        if (listSystamParam.empty())
        {
            ZGLOG_ERROR(QStringLiteral("未配置操作票系统参数"));
            return false;
        }
        m_mapSystemParam = listSystamParam[0];
        return true;
    }

    bool ZGOPTaskOTMng::initMqttClient()
    {
        if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
        {
            ZGLOG_ERROR("initMqttClient error.");
            return false;
        }
        m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
        if (m_pMqttClient == nullptr)
        {
            ZGLOG_ERROR("getMqttClientMessage error.");
            return false;
        }
        m_pMqttClient->connectToHost();
        return true;
    }

    bool ZGOPTaskOTMng::initRedisClient()
    {
        QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
        listClientType << ZGRuntime::REDIS_RT_QUEUE << ZGRuntime::REDIS_RT_TOPIC;
        if (!ZGRuntime::instance()->initRedisClient(listClientType))
        {
            ZGLOG_ERROR("Init redis client error.");
            return false;
        }
        m_pRedisQueue = ZGRuntime::instance()->getRedisClientRTQueue();
        if (m_pRedisQueue == nullptr)
        {
            ZGLOG_ERROR("getRedisClientRTQueue error.");
            return false;
        }
        m_pRedisTopic = ZGRuntime::instance()->getRedisClientRTTopic();
        if (m_pRedisTopic == nullptr)
        {
            ZGLOG_ERROR("getRedisClientRTTopic error.");
            return false;
        }
        QThread::msleep(1000);
        if (!m_pRedisTopic->connected())
            return false;
        const StringList listTopic{"op_param_task", "op_param_ot", "op_param_ot_item", "sp_real_exam"};
        m_pRedisTopic->subscribe(listTopic);
        connect(m_pRedisTopic, &ZGRedisClient::receivedMessage, this, &ZGOPTaskOTMng::onReceivedMessage);
        m_pRedisTopic->consume();
        return true;
    }

    ZGOPTaskOTTicket* ZGOPTaskOTMng::fetchTask(std::string taskID)
    {
        ZGOPTaskOTTicket* task = nullptr;
        QReadLocker locker(&m_lock);
        auto pair = m_mapTask.find(taskID);
        if (pair != m_mapTask.end())
            task = pair->second;
        else
        {
            std::string id;
            if (ZGProxyCommon::getDataByField("op_param_task", taskID, "id", id))
            {
                task = new ZGOPTaskOTTicket(taskID);
                if (!task->initialize())
                {
                    delete task;
                    task = nullptr;
                }
                else
                    m_mapTask[std::move(taskID)] = task;
            }
        }
        return task;
    }

    bool ZGOPTaskOTMng::addTicket(std::string taskID)
    {
        QWriteLocker locker(&m_lock);
        if (m_mapTask.find(taskID) == m_mapTask.end())
        {
            ZGOPTaskOTTicket* task = new ZGOPTaskOTTicket(taskID);
            if (!task->initialize())
            {
                delete task;
                return false;
            }
            ZGLOG_INFO(QStringLiteral("添加操作票'%1'").arg(taskID.c_str()));
            m_mapTask[std::move(taskID)] = task;
        }
        return true;
    }

    void ZGOPTaskOTMng::removeTicket(std::string taskID)
    {
        QWriteLocker locker(&m_lock);
        auto pair = m_mapTask.find(std::move(taskID));
        if (pair != m_mapTask.end())
        {
            delete pair->second;
            m_mapTask.erase(pair);
        }
    }

    bool ZGOPTaskOTMng::getOTHead(std::string otID, StringMap& otHead, ErrorInfo& e)
    {
        ListStringMap listTask;
        if (!getTaskList({{"condition", "a.id = '" + otID + "'"}}, listTask, e))
            return false;
        if (listTask.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到操作票'%1'").arg(otID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        otHead = std::move(listTask[0]);
        QString sql = QString("SELECT rtNumber, rtTaskOrder, rtIsPreview, rtPreviewItemID, rtPreviewStateID, rtItemID, rtItemIndex, rtPreviewItemID, "
            "rtPreviewItemIndex FROM op_param_ot WHERE id = '%1'").arg(otID.c_str());
        ZG6000::StringMap ot;
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), ot))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取操作票'%1'信息失败").arg(otID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ot["rtPreviewStateName"] = ZGUtils::get(m_mapTaskState, ot["rtPreviewStateID"], "");
        for (auto& pair : ot)
        {
            otHead[pair.first] = pair.second;
        }
        return true;
    }

    bool ZGOPTaskOTMng::getOTItems(std::string otID, ListStringMap& otItems, ErrorInfo& e)
    {
        QString sql = QString("SELECT a.*, b.name AS deviceName FROM op_param_ot_item a LEFT JOIN "
            "mp_param_device b ON a.deviceID = b.id WHERE a.otID = '%1' ORDER BY itemIndex").arg(otID.c_str());
        //		QString sql = QString("SELECT a.id, a.name AS itemName, a.deviceID, b.name AS deviceName, a.itemIndex, a.rtStateID, c.name AS rtStateName, a.rtExecTime FROM op_param_ot_item a "
        //			"LEFT JOIN mp_param_device b ON a.deviceID = b.id LEFT JOIN op_dict_ot_item_state c ON a.rtStateID = c.id WHERE a.otID = '%1' ORDER by a.itemIndex").arg(otID.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), otItems))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取操作票'%1'步骤失败").arg(otID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            for (auto& otItem : otItems)
            {
                const auto& termItemGroupID = ZGUtils::get(otItem, "termItemGroupID");
                otItem["termItemGroupID"] = termItemGroupID;
                const auto& termItemGroupName = ZGUtils::get(m_mapTermItemGroup, termItemGroupID, "");
                otItem["termItemGroupName"] = termItemGroupName;
                const auto& termItemTypeID = ZGUtils::get(otItem, "termItemTypeID");
                const auto& termItemTypeName = ZGUtils::get(m_mapTermItemType, termItemTypeID, "");
                otItem["termItemTypeName"] = termItemTypeName;
                otItem["rtStateName"] = ZGUtils::get(m_mapItemState, otItem["rtStateID"], "");
                otItem["rtPreviewStateName"] = ZGUtils::get(m_mapItemState, otItem["rtPreviewStateID"], "");
            }
        }
        catch (const std::exception& ex)
        {
            ZGLOG_ERROR(ex.what());
            return false;
        }
        return true;
    }

    std::string ZGOPTaskOTMng::generateUserEvent(const std::string& taskID, const StringMap& param, std::string message)
    {
        const auto& operUserID = ZGUtils::get(param, "operator", "");
        const auto& monUserID = ZGUtils::get(param, "monitor", "");
        std::string operUserName, monUserName;
        if (!operUserID.empty())
            ZGProxyCommon::getDataByField("sp_param_hrm_user", operUserID, "name", operUserName);
        if (!monUserID.empty())
            ZGProxyCommon::getDataByField("sp_param_hrm_user", monUserID, "name", monUserName);
        std::string eventMessage = std::move(message);
        if (!operUserName.empty())
            eventMessage += u8"，操作员：【" + operUserName + u8"】";
        if (!monUserName.empty())
            eventMessage += u8"，监护员：【" + monUserName + u8"】";
        return eventMessage;
    }

    StringMap ZGOPTaskOTMng::generateEventMessage(const std::string& taskID, const std::string& eventTypeID, std::string message)
    {
        ZG6000::StringMap event, record;
        if (!ZGProxyCommon::getDataByFields("op_param_task", taskID, {"appNodeID", "subsystemID", "majorID"}, record))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务'%1'应用节点信息失败").arg(taskID.c_str()));
            return event;
        }
        event["eventTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        event["eventTypeID"] = eventTypeID;
        event["alarmLevelID"] = "ZG_AL_LEVEL0";
        event["appNodeID"] = std::move(record["appNodeID"]);
        event["subsystemID"] = std::move(record["subsystemID"]);
        event["majorID"] = std::move(record["majorID"]);
        event["eventInfo"] = std::move(message);
        return event;
    }

    void ZGOPTaskOTMng::sendMessage(ZG6000::StringMap event)
    {
        if (event.empty())
            return;
        auto eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
        try
        {
            auto oneway = eventProcessPrx->ice_oneway();
            oneway->processEvent(event);
        }
        catch (const Ice::Exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    void ZGOPTaskOTMng::onTimer()
    {
        auto localNetNormal = ZGRuntime::instance()->isLocalNetNormal();
        if (m_localNetNodeNormal != localNetNormal)
        {
            ZGLOG_INFO(QString("m_localNetNodeNormal = %1, isLocalNetNormal = %2").arg(m_localNetNodeNormal).arg(ZGRuntime::instance()->isLocalNetNormal()));
            if (localNetNormal)
                m_lastNetRestoreTime = QDateTime::currentDateTime();
            m_localNetNodeNormal = localNetNormal;
        }
        if (!m_localNetNodeNormal)
        {
            ZGLOG_TRACE(QStringLiteral("本地网络状态异常"));
            return;
        }
        auto isMaster = ZGRuntime::instance()->isMaster();
        if (isMaster != m_masterInst)
        {
            if (isMaster)
            {
                if (!switchToMaster())
                {
                    ZGLOG_ERROR(QStringLiteral("切换到主服务失败"));
                    return;
                }
                ZGLOG_INFO(QStringLiteral("切换到主服务实例"));
                m_masterTickCount = 0;
            }
            m_masterInst = isMaster;
        }
        if (!isMaster)
            return;
        m_masterTickCount++;
        if (m_masterTickCount < 10)
            return;
        QDateTime dt = QDateTime::currentDateTime();
        std::vector<ZGOPTaskOTTicket*> listTask;
        {
            QReadLocker locker(&m_lock);
            for (const auto& pair : m_mapTask)
            {
                listTask.push_back(pair.second);
            }
        }
        for (auto task : listTask)
        {
            auto future = QtConcurrent::run([=]() { task->onTimer(); });
        }
        if (m_lastDateTime.daysTo(dt) >= 1)
        {
            ZGLOG_INFO("Reset rtNumberIndex.");
            QString sql = QString("SELECT * FROM op_param_ot_system");
            ZG6000::ListStringMap listSystem;
            if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listSystem))
            {
                ZGLOG_ERROR(QStringLiteral("获取操作票系统参数失败"));
                return;
            }
            if (listSystem.empty())
                return;
            ZG6000::StringMap systemParam = listSystem[0];
            systemParam["rtNumberIndex"] = "0";
            m_lastDateTime = dt;
            sql = ZGUtils::generateUpdateSql("op_param_ot_system", systemParam).c_str();
            ZGProxyCommon::execSql(sql.toStdString());
        }
    }

    void ZGOPTaskOTMng::onReceivedMessage(const QString& channel, const QString& message)
    {
        std::string tableName;
        std::string oper;
        std::string reason;
        std::string aTime;
        std::string errMsg;
        ZG6000::ListRecord listRecord;
        if (!ZGJson::convertFromJsonToListRecord(message.toStdString(), tableName, oper,
            reason, aTime, listRecord, errMsg))
        {
            ZGLOG_ERROR(errMsg.c_str());
            return;
        }
        dispatchTableData(tableName, oper, reason, aTime, listRecord);
    }
} // namespace ZG6000

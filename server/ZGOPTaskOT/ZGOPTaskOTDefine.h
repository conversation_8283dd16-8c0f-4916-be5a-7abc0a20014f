#ifndef ZGOPTASKOTDEFINE_H
#define ZGOPTASKOTDEFINE_H

inline const char* ZGOPOT_CONFIRM = "confirm";
inline const char* ZGOPOT_START = "start";
inline const char* ZGOPOT_STOP = "stop";
inline const char* ZGOPOT_PAUSE = "pause";
inline const char* ZGOPOT_RESUME = "resume";
inline const char* ZGOPOT_RETRY = "retry";
inline const char* ZGOPOT_PREVIEW = "preview";
inline const char* ZGOPOT_ABOLISH = "abolish";
inline const char* ZGOPOT_NEXT_STEP = "next";

inline const char* ZGOPITEM_SKIP = "skip";
inline const char* ZGOPITEM_CONTINUE = "continue";
inline const char* ZGOPITEM_TIMEOUT = "itemTimeout";
inline const char* ZGOPITEM_ERROR = "itemError";


#endif // ZGOPTASKOTDEFINE_H

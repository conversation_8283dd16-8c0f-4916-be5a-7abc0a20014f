#include "ZGOPTaskOTTicket.h"
#include "ZGJson.h"
#include "ZGOPTaskOTDefine.h"
#include "ZGOPTaskOTItem.h"
#include "ZGOPTaskOTMng.h"
#include "ZGOPTaskOTPreviewItem.h"
#include "ZGRuntime.h"
#include "ZGSPExamManager.h"
#include "ZGUtils.h"
#include "zgerror/ZGOPTaskOTError.h"

#include <QJsonDocument>

inline void checkAndCopyField(ZG6000::StringMap &dstParam, const ZG6000::StringMap &srcParam, const std::string &field)
{
    if (srcParam.find(field) != srcParam.end())
        dstParam[field] = ZGUtils::get(srcParam, field);
}

ZGOPTaskOTTicket::ZGOPTaskOTTicket(std::string id, QObject *parent) : QObject{parent}, m_id(std::move(id))
{
}

bool ZGOPTaskOTTicket::initialize()
{
    if (!initExecuteContext())
    {
        ZGLOG_ERROR(QString("initExecuteContext error, taskID = '%1'").arg(m_id.c_str()));
        return false;
    }
    if (!initPreviewContext())
    {
        ZGLOG_ERROR(QString("initPreviewContext error, taskID = '%1'").arg(m_id.c_str()));
        return false;
    }
    return true;
}

void ZGOPTaskOTTicket::onTimer()
{
    QMutexLocker lock(&m_mutex);
    ZG6000::ListStringMap listTask, listOt;
    QString sql = QString("SELECT * FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
    {
        ZGLOG_ERROR(QStringLiteral("获取操作票'%1'失败").arg(m_id.c_str()));
        return;
    }
    if (listTask.empty())
    {
        ZGLOG_ERROR(QStringLiteral("操作票'%1'不存在").arg(m_id.c_str()));
        return;
    }
    const auto& task = listTask.front();
    sql = QString("SELECT * FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listOt))
    {
        ZGLOG_ERROR(QStringLiteral("获取操作票'%1'失败").arg(m_id.c_str()));
        return;
    }
    if (listOt.empty())
    {
        ZGLOG_ERROR(QStringLiteral("操作票'%1'不存在").arg(m_id.c_str()));
        return;
    }
    const auto& ot = listOt.front();
    try
    {
        const auto& taskStageID = ZGUtils::get(task, "rtTaskStageID");
        if (taskStageID == "ZG_TS_EXAM" || taskStageID == "ZG_TS_EXECUTE")
        {
            const auto& rtEndTime = ZGUtils::get(task, "rtEndTime");
            QDateTime endTime;
            if (ZGUtils::StringToDateTime(rtEndTime.c_str(), endTime, true))
            {
                QDateTime currTime = QDateTime::currentDateTime();
                if (currTime > endTime)
                {
                    setCurrentState("ZG_TS_TASK_TIMEOUT");
                    return;
                }
            }
        }
        const auto& taskStateID = ZGUtils::get(task, "rtTaskStateID");
        if (taskStateID == "ZG_TS_PAUSED")
        {
            const auto& rtItemID = ZGUtils::get(ot, "rtItemID");
            ZG6000::ListStringMap listItem;
            sql = QString("SELECT * FROM op_param_ot_item WHERE id = '%1'").arg(rtItemID.c_str());
            if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listItem))
            {
                ZGLOG_ERROR(QStringLiteral("获取操作票项'%1'失败").arg(rtItemID.c_str()));
                return;
            }
            if (listItem.empty())
            {
                ZGLOG_ERROR(QStringLiteral("操作票项'%1'不存在").arg(rtItemID.c_str()));
                return;
            }
            auto otItem = listItem.front();
            if (otItem["rtStateID"].empty())
            {
                if (otItem["rtExecTime"].empty())
                    return;
                QDateTime dt = QDateTime::currentDateTime();
                QDateTime execTime;
                if (!ZGUtils::StringToDateTime(otItem["rtExecTime"].c_str(), execTime, true))
                    return;
                const auto& startDelay = std::atoi(otItem["startDelay"].c_str());
                if (execTime.secsTo(dt) >= startDelay)
                {
                    ZG6000::StringMap newOtItem{{"id", rtItemID}, {"rtStateID", "ZG_OIS_READY"}};
                    sql = ZGUtils::generateUpdateSql("op_param_ot_item", newOtItem).c_str();
                    if (!ZGProxyCommon::execSql(sql.toStdString()))
                        return;
                    ZGProxyCommon::synchronize();
                }
            }
        }
        if (taskStateID == "ZG_TS_EXECUTING")
        {
            const auto& rtItemID = ZGUtils::get(ot, "rtItemID");
            m_pCurrentItem->onTimer(rtItemID);
        }
        const auto& previewState = ZGUtils::get(ot, "rtPreviewStateID");
        if (previewState == "ZG_TS_EXECUTING")
        {
            const auto& rtPreviewItemID = ZGUtils::get(ot, "rtPreviewItemID");
            m_pCurrentPreviewItem->onTimer(rtPreviewItemID);
        }
    }
    catch (const std::exception &e)
    {
        ZGLOG_DEBUG(e.what());
    }
}

bool ZGOPTaskOTTicket::command(const std::string &action, ZG6000::StringMap args, std::string &errMsg)
{
    ZG6000::ListStringMap listTask;
    QString sql = QString("SELECT rtTaskStageID, rtTaskStateID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
    {
        errMsg = QStringLiteral("获取操作票'%1'阶段及状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (listTask.empty())
    {
        errMsg = QStringLiteral("操作票'%1'不存在").arg(m_id.c_str()).toStdString();
        return false;
    }
    const auto& task = listTask.front();
    const auto& stageID = ZGUtils::get(task, "rtTaskStageID", "");
    const auto& stateID = ZGUtils::get(task, "rtTaskStateID", "");
    fsm.set_state({ stageID, stateID });
    return fsm.command(action, std::move(args), errMsg);
}

bool ZGOPTaskOTTicket::previewCommand(const std::string &action, ZG6000::StringMap args, std::string &errMsg)
{
    std::string previewStateID;
    QString sql = QString("SELECT rtPreviewStateID FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), previewStateID))
    {
        errMsg = QStringLiteral("获取操作票'%1'预演状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    fsm_preview.set_state(previewStateID);
    return fsm_preview.command(action, std::move(args), errMsg);
}

bool ZGOPTaskOTTicket::getCurrentState(std::string &state)
{
    QString sql = QString("SELECT rtTaskStateID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    return ZGProxyCommon::execQuerySqlField(sql.toStdString(), state);
}

bool ZGOPTaskOTTicket::setCurrentState(std::string state)
{
    ZG6000::StringMap ticket;
    ticket["id"] = m_id;
    ticket["rtTaskStateID"] = std::move(state);
    std::string sql = ZGUtils::generateUpdateSql("op_param_task", ticket);
    if (!ZGProxyCommon::execSql(sql))
        return false;
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::getCurrentStage(std::string &stage)
{
    QString sql = QString("SELECT rtTaskStageID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    return ZGProxyCommon::execQuerySqlField(sql.toStdString(), stage);
}

bool ZGOPTaskOTTicket::setCurrentStage(std::string stage)
{
    ZG6000::StringMap ticket;
    ticket["id"] = m_id;
    ticket["rtTaskStageID"] = std::move(stage);
    std::string sql = ZGUtils::generateUpdateSql("op_param_task", ticket);
    if (!ZGProxyCommon::execSql(sql))
        return false;
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::getPreviewState(std::string &state)
{
    QString sql = QString("SELECT rtPreviewStateID FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    return ZGProxyCommon::execQuerySqlField(sql.toStdString(), state);
}

bool ZGOPTaskOTTicket::setPreviewState(std::string state)
{
    ZG6000::StringMap ot;
    ot["id"] = m_id;
    ot["rtPreviewStateID"] = std::move(state);
    std::string sql = ZGUtils::generateUpdateSql("op_param_ot", ot);
    if (!ZGProxyCommon::execSql(sql))
        return false;
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::getCurrentItem(std::string &itemID)
{
    QString sql = QString("SELECT rtItemID FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    return ZGProxyCommon::execQuerySqlField(sql.toStdString(), itemID);
}

bool ZGOPTaskOTTicket::getPreviewItem(std::string &itemID)
{
    QString sql = QString("SELECT rtPreviewItemID FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    return ZGProxyCommon::execQuerySqlField(sql.toStdString(), itemID);
}

bool ZGOPTaskOTTicket::getOperUser(std::string& userID)
{
    QString sql = QString("SELECT rtOperUserID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    return ZGProxyCommon::execQuerySqlField(sql.toStdString(), userID);
}

bool ZGOPTaskOTTicket::getMonUser(std::string& userID)
{
    QString sql = QString("SELECT rtMonUserID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    return ZGProxyCommon::execQuerySqlField(sql.toStdString(), userID);
}

bool ZGOPTaskOTTicket::isPreview(bool &preview)
{
    std::string rtIsPreview;
    QString sql = QString("SELECT rtIsPreview FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtIsPreview))
        return false;
    preview = (rtIsPreview == "1");
    return true;
}

void ZGOPTaskOTTicket::dispatchData(const std::string &tableName, const ZG6000::MapField &record)
{
    if (tableName == "op_param_task")
        processTaskChange(record);
    if (tableName == "op_param_ot")
        processOtChange(record);
    if (tableName == "op_param_ot_item")
        processItemChange(record);
    if (tableName == "sp_real_exam")
        processExamChange(record);
}

bool ZGOPTaskOTTicket::createTemplateTicket(ZG6000::StringMap param, std::string &ticketId, ZG6000::ErrorInfo &e)
{
    QString errMsg;
    if (!checkRequiredParam(param, {"appNodeID", "subsystemID", "majorID", "templateID", "createUserID"}, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg.toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    const auto &appNodeID = param["appNodeID"];
    const auto &subsystemID = param["subsystemID"];
    const auto &majorID = param["majorID"];
    const auto &templateID = param["templateID"];
    auto systemParam = ZG6000::ZGOPTaskOTMng::instance()->getSystemParam();
    if (systemParam["isEnableMultiTask"] != "1")
    {
        if (!checkOtUnique(appNodeID, e))
            return false;
    }
//    if (!checkTypeOtUnique(appNodeID, subsystemID, majorID, templateID, e))
//        return false;
    std::string execTermID;
    if (!ZGProxyCommon::getDataByField("op_param_ot_template", templateID, "execTermID", execTermID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取模板票执行规则失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    // if (!execTermID.empty())
    // {
    //     if (!checkAppNodeTermExecCondition(appNodeID, execTermID))
    //     {
    //         e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
    //         e.errDetail = QStringLiteral("操作票执行条件不满足，无法创建").toStdString();
    //         ZGLOG_ERROR(e);
    //         return false;
    //     }
    // }
    if (!ZGProxyCommon::createUUID(ticketId))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建票ID失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringList listSql;
    auto otParam = generateOtHeadParam(ticketId, "ZG_OT_TEMPLATE", param);
    otParam["execTermID"] = execTermID;
    QString sql = QString("SELECT pageID FROM hmi_param_appnode_page WHERE appNodeID = '%1' "
                  "AND subsystemID = '%2' AND majorID = '%3' AND isMainPic = 1")
              .arg(param["appNodeID"].c_str())
              .arg(param["subsystemID"].c_str())
              .arg(param["majorID"].c_str());
    std::string pageID;
    if (ZGProxyCommon::execQuerySqlField(sql.toStdString(), pageID))
        param["pageID"] = pageID;
    listSql.push_back(ZGUtils::generateInsertSql("op_param_ot", std::move(otParam)));
    listSql.push_back(ZGUtils::generateInsertSql("op_param_task", generateTemplateTaskHead(ticketId, param)));
    sql = QString("SELECT * FROM op_param_ot_template_item WHERE templateID = '%1' ORDER BY itemIndex")
              .arg(templateID.c_str());
    ZG6000::ListStringMap listTemplateItem;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTemplateItem))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建模板'%'项失败").arg(templateID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        int itemIndex = 1;
        for (const auto &templateItem : listTemplateItem)
        {
            const auto &termID = ZGUtils::get(templateItem, "termID");
            ZG6000::StringMap term;
            if (!ZGProxyCommon::getDataByID("mp_param_term", termID, term))
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取操作术语'%'失败").arg(termID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            } 
            const auto &termItemGroupID = ZGUtils::get(templateItem, "termItemGroupID");
            const auto &deviceTag = ZGUtils::get(templateItem, "deviceTag");
            QJsonDocument doc;
            QJsonObject obj;
            if (!deviceTag.empty())
            {
                doc = QJsonDocument::fromJson(deviceTag.c_str());
                obj = doc.object();
            }
            sql = QString("SELECT * FROM op_param_ot_term_item WHERE termID = '%1' AND termItemGroupID = '%2' ORDER BY "
                          "itemIndex")
                      .arg(termID.c_str())
                      .arg(termItemGroupID.c_str());
            ZG6000::ListStringMap listTermItem;
            if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTermItem))
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
                e.errDetail = QStringLiteral("获取操作术语'%1'分组'%2'项失败")
                                  .arg(termID.c_str())
                                  .arg(termItemGroupID.c_str())
                                  .toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            for (const auto &termItem : listTermItem)
            {
                ZG6000::StringMap otItem;
                if (!generateOtItemParam(ticketId, term, termItem, appNodeID, templateItem, obj, otItem, itemIndex, e))
                    return false;
                listSql.push_back(ZGUtils::generateInsertSql("op_param_ot_item", otItem));
            }
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建操作票'%1'失败").arg(ticketId.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }
    catch (const std::exception &ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGOPTaskOTTicket::createTypicalTicket(ZG6000::StringMap param, std::string &ticketId, ZG6000::ErrorInfo &e)
{
    QString errMsg;
    if (!checkRequiredParam(param, {"id", "createUserID"}, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg.toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    const auto &taskID = param["id"];
    std::string appNodeID;
    if (!ZGProxyCommon::getDataByField("op_param_task", taskID, "appNodeID", appNodeID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取票'%1'应用节点失败").arg(taskID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    auto systemParam = ZG6000::ZGOPTaskOTMng::instance()->getSystemParam();
    if (systemParam["isEnableMultiTask"] != "1")
    {
        if (!checkOtUnique(appNodeID, e))
            return false;
    }
    std::string execTermID;
    if (!ZGProxyCommon::getDataByField("op_param_ot", taskID, "execTermID", execTermID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取票'%1'执行条件失败").arg(taskID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!execTermID.empty())
    {
        if (!checkAppNodeTermExecCondition(appNodeID, execTermID))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("操作票执行条件不满足，无法创建").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
    }
    ZG6000::StringMap task = generateTypicalTaskHead(taskID, param);
    ZG6000::StringList listSql;
    ZG6000::StringMap ot = generateOtHeadParam(taskID, "ZG_OT_TYPICAL", std::move(param));
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建典型票'%1'任务失败").arg(taskID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ticketId = taskID;
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::createTemporaryTicket(const std::string &ticketTypeID, ZG6000::StringMap param,
                                             std::string &ticketId, ZG6000::ErrorInfo &e)
{
    QString errMsg;
    if (!checkRequiredParam(param, {"appNodeID", "subsystemID", "majorID", "createUserID"}, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg.toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    const auto &appNodeID = param["appNodeID"];
    QString sql = QString("SELECT id FROM op_param_task WHERE taskTypeID = 'ZG_TT_OT' "
                          "AND rtTaskStageID <> 'ZG_TS_INIT' AND appNodeID = '%1'").arg(appNodeID.c_str());
    ZG6000::StringList listTaskID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listTaskID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取应用节点'%1'关联操作票失败").arg(appNodeID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!listTaskID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("该节点存在未结束的操作票").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!ZGProxyCommon::createUUID(ticketId))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建票ID失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringList listSql;
    ZG6000::StringMap task = generateFixedTaskHead(ticketId, "ZG_TS_CREATE", "ZG_TS_FINISHED", param);
    ZG6000::StringMap ot = generateOtHeadParam(ticketId, ticketTypeID, param);    
    if (param.find("pageID") == param.end())
    {
        const auto &subsystemID = param["subsystemID"];
        const auto &majorID = param["majorID"];
        QString sql = QString("SELECT pageID FROM hmi_param_appnode_page WHERE appNodeID = '%1' AND subsystemID = '%2' "
                              "AND majorID = '%3' AND isMainPic = 1")
                          .arg(appNodeID.c_str())
                          .arg(subsystemID.c_str())
                          .arg(majorID.c_str());
        std::string pageID;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), pageID))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取主页失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        task["pageID"] = pageID;
    }
    listSql.push_back(ZGUtils::generateInsertSql("op_param_ot", ot));
    listSql.push_back(ZGUtils::generateInsertSql("op_param_task", task));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建临时票'%1'任务失败").arg(ticketId.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::checkRequiredParam(const ZG6000::StringMap &param, const ZG6000::StringList &required,
                                          QString &errMsg)
{
    for (const auto &field : required)
    {
        if (param.find(field) == param.end())
        {
            errMsg = QStringLiteral("缺少必要参数'%1'").arg(field.c_str());
            return false;
        }
    }
    return true;
}

bool ZGOPTaskOTTicket::createTicket(std::string ticketTypeID, ZG6000::StringMap param, std::string &ticketID,
                                    ZG6000::ErrorInfo &e)
{
    if (ticketTypeID == "ZG_OT_TEMPLATE")
    {
        if (!createTemplateTicket(std::move(param), ticketID, e))
            return false;
    }
    else if (ticketTypeID == "ZG_OT_TYPICAL")
    {
        if (!createTypicalTicket(std::move(param), ticketID, e))
            return false;
    }
    else if (ticketTypeID == "ZG_OT_TEMP" || ticketTypeID == "ZG_OT_PIC")
    {
        if (!createTemporaryTicket(ticketTypeID, std::move(param), ticketID, e))
            return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::deleteTicket(ZG6000::ErrorInfo &e)
{
    if (!deleteExam(e))
        return false;
    std::string ticketTypeID;
    QString sql = QString("SELECT typeID FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), ticketTypeID))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取操作票'%1'类型失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (ticketTypeID == "ZG_OT_TYPICAL")
    {
        std::string errMsg;
        if (!updateInitFinish({}, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = errMsg;
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }
    ZG6000::StringList listSql;
    listSql.push_back(QString("DELETE FROM op_param_task WHERE id = '%1';").arg(m_id.c_str()).toStdString());
    listSql.push_back(QString("DELETE FROM op_param_ot WHERE  id = '%1';").arg(m_id.c_str()).toStdString());
    listSql.push_back(QString("DELETE FROM op_param_ot_item WHERE otID = '%1';").arg(m_id.c_str()).toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("删除操作票'%1'失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::MapField record;
    record["id"].newValue = m_id;
    record["rtTaskStageID"].newValue = "ZG_TS_DELETE";
    processTaskChange(record);
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::editTicket(std::string otID, ZG6000::StringMap head, ZG6000::ListStringMap items,
                                  ZG6000::ErrorInfo &e)
{
    QString sql = QString("SELECT rtTaskStageID FROM op_param_task WHERE id = '%1'").arg(otID.c_str());
    ZG6000::StringList listTaskStageID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listTaskStageID))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取操作票阶段失败").arg(otID.c_str()).toStdString();
        ZGLOG_ERROR(QStringLiteral("获取操作票'%1'阶段失败").arg(otID.c_str()));
        return false;
    }
    if (listTaskStageID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("操作票不存在").arg(otID.c_str()).toStdString();
        ZGLOG_ERROR(QStringLiteral("操作票'%1'不存在").arg(otID.c_str()));
        return false;
    }
    const auto &taskStageID = listTaskStageID.front();
    if (taskStageID != "ZG_TS_CREATE")
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("操作票已提交，不能重复提交").arg(otID.c_str()).toStdString();
        ZGLOG_ERROR(QStringLiteral("操作票'%1'已提交，不能重复提交").arg(otID.c_str()));
        return false;
    }
    ZG6000::StringList listSql;
    ZG6000::StringMap task, ot;
    task["id"] = otID;
    ot["id"] = otID;
    for (const auto& [field, value]: head)
    {
        if (field == "rtNumber")
            ot["rtNumber"] = value;
        else if (field == "rtTaskOrder")
            ot["rtTaskOrder"] = value;
        else
            task[field] = value;
    }
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    if (items.size() > 0)
    {
        for (const auto &item : items)
        {
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot_item", item));
        }
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("编辑操作票'%1'失败").arg(otID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::start(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    std::string rtPreviewStateID;
    QString sql = QString("SELECT rtPreviewStateID FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtPreviewStateID))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取操作票'%1'预演状态失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (rtPreviewStateID != "ZG_TS_READY")
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("当前票预演未正常结束，不能启动操作票").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return doAction(ZGOPOT_START, std::move(param), e);
}

bool ZGOPTaskOTTicket::stop(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doAction(ZGOPOT_STOP, std::move(param), e);
}

bool ZGOPTaskOTTicket::pause(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doAction(ZGOPOT_PAUSE, std::move(param), e);
}

bool ZGOPTaskOTTicket::resume(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doAction(ZGOPOT_RESUME, std::move(param), e);
}

bool ZGOPTaskOTTicket::retry(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doAction(ZGOPOT_RETRY, std::move(param), e);
}

bool ZGOPTaskOTTicket::skip(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doAction(ZGOPITEM_SKIP, std::move(param), e);
}

bool ZGOPTaskOTTicket::abolish(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doAction(ZGOPOT_ABOLISH, std::move(param), e);
}

bool ZGOPTaskOTTicket::confirm(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doAction(ZGOPOT_CONFIRM, std::move(param), e);
}

bool ZGOPTaskOTTicket::convert(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    if (param.find("typeID") == param.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("未指定票'%1'类型参数").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (ZG6000::ZGOPTaskOTMng::instance()->getOTType(param["typeID"]).empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("无效的票类型'%1'").arg(param["typeID"].c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringMap ot{{"id", m_id}, {"typeID", param["typeID"]}};
    std::string sql = ZGUtils::generateUpdateSql("op_param_ot", ot);
    if (!ZGProxyCommon::execSql(sql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("更改票'%1'类型失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::previewStart(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    std::string rtTaskStageID;
    QString sql = QString("SELECT rtTaskStageID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtTaskStageID))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取操作票'%1'阶段失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (rtTaskStageID == "ZG_TS_EXECUTE")
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("当前操作票已进入执行阶段，无法执行预演").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return doPreviewAction(ZGOPOT_START, std::move(param), e);
}

bool ZGOPTaskOTTicket::previewStop(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doPreviewAction(ZGOPOT_STOP, std::move(param), e);
}

bool ZGOPTaskOTTicket::previewPause(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doPreviewAction(ZGOPOT_PAUSE, std::move(param), e);
}

bool ZGOPTaskOTTicket::previewResume(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doPreviewAction(ZGOPOT_RESUME, std::move(param), e);
}

bool ZGOPTaskOTTicket::previewRetry(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doPreviewAction(ZGOPOT_RETRY, std::move(param), e);
}

bool ZGOPTaskOTTicket::previewConfirm(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    return doPreviewAction(ZGOPOT_CONFIRM, std::move(param), e);
}

bool ZGOPTaskOTTicket::createItem(ZG6000::StringMap param, ZG6000::ListStringMap &items, ZG6000::ErrorInfo &e)
{
    QString errMsg;
    if (!checkRequiredParam(param, {"termID", "termItemGroupID"}, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg.toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    const auto &termID = param["termID"];
    const auto& termItemGroupID = param["termItemGroupID"];
    QString sql = QString("SELECT * FROM op_param_ot_term_item WHERE termID = '%1' AND termItemGroupID = '%2'").arg(termID.c_str()).arg(termItemGroupID.c_str());
    ZG6000::ListStringMap listTermItem;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTermItem))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取操作术语'%1'项失败").arg(termID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZGLOG_TRACE(QString("listTermItem size: %1").arg(listTermItem.size()));
    if (listTermItem.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("该操作术语无可用步骤").arg(termID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    sql = QString("SELECT MAX(itemIndex) FROM op_param_ot_item WHERE otID = '%1'").arg(m_id.c_str());
    ZG6000::StringList listIndex;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listIndex))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取操作票'%1'最大步骤项失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    int itemIndex = 0;
    if (!listIndex.empty())
    {
        itemIndex = std::atoi(listIndex[0].c_str());
    }
    ZG6000::StringList listSql;
    for (auto &termItem : listTermItem)
    {
        ZG6000::StringMap otItem;
        const auto &listVarible = ZGUtils::extractVariables(termItem["name"]);
        std::string itemName = termItem["name"];
        std::string itemVoice = termItem["voice"];
        if (!listVarible.empty())
        {
            for (const auto &varible : listVarible)
            {
                if (varible == "appNode")
                {
                    if (param.find("appNodeID") == param.end())
                    {
                        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
                        e.errDetail =
                            QStringLiteral("操作术语项'%1'未实例化应用节点").arg(termItem["id"].c_str()).toStdString();
                        ZGLOG_ERROR(e);
                        return false;
                    }
                    const auto &appNode = ZG6000::ZGOPTaskOTMng::instance()->getAppNode(param["appNodeID"]);
                    std::string appNodeName = ZGUtils::get(appNode, "name", "");
                    std::string appNodeVoice = ZGUtils::get(appNode, "voice", "");
                    ZGUtils::replaceString(itemName, "[" + varible + "]", appNodeName);
                    ZGUtils::replaceString(itemVoice, "[" + varible + "]", appNodeVoice);
                }
                else
                {
                    if (param.find("deviceID") == param.end())
                    {
                        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
                        e.errDetail =
                            QStringLiteral("操作术语项'%1'未实例化设备").arg(termItem["id"].c_str()).toStdString();
                        ZGLOG_ERROR(e);
                        return false;
                    }
                    ZG6000::StringMap deviceParam;
                    ZGProxyCommon::getDataByFields("mp_param_device", param["deviceID"], {"name", "voice", "subtypeID"},
                                                   deviceParam);
                    if (varible != deviceParam["subtypeID"])
                    {
                        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
                        e.errDetail = QStringLiteral("设备的子类型与预期不匹配，预期'%1', 实际'%2'")
                                          .arg(varible.c_str())
                                          .arg(deviceParam["subtypeID"].c_str())
                                          .toStdString();
                        ZGLOG_ERROR(e);
                        return false;
                    }
                    ZGUtils::replaceString(itemName, "[" + varible + "]", deviceParam["name"]);
                    ZGUtils::replaceString(itemVoice, "[" + varible + "]", deviceParam["voice"]);
                }
            }
        }
        otItem["deviceID"] = param["deviceID"];
        if (!otItem["deviceID"].empty())
        {
            std::string lockID;
            if (!termItem["lockTag"].empty())
            {
                if (!getLockIDByTag(otItem["deviceID"], termItem["lockTag"], lockID, e))
                    return false;
            }
            otItem["lockID"] = lockID;
        }
        otItem["name"] = itemName;
        otItem["voice"] = itemVoice;
        otItem["itemIndex"] = std::to_string(++itemIndex);
        otItem["termID"] = termID;
        otItem["termItemID"] = termItem["id"];
        otItem["updateValues"] = termItem["updateValues"];
        otItem["termItemGroupID"] = termItem["termItemGroupID"];
        otItem["termItemTypeID"] = termItem["termItemTypeID"];
        otItem["isCheckExecCondition"] = termItem["isCheckExecCondition"];
        otItem["isCheckConfirmCondition"] = termItem["isCheckConfirmCondition"];
        otItem["isAutoExec"] = termItem["isAutoExec"];
        otItem["lockConfirmCondition"] = termItem["lockConfirmCondition"];
        otItem["startDelay"] = termItem["startDelay"];
        otItem["confirmDelay"] = termItem["confirmDelay"];
        otItem["endDelay"] = termItem["endDelay"];
        otItem["timeout"] = termItem["timeout"];
        std::string uuid;
        if (!ZGProxyCommon::createUUID(uuid))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建操作票项ID失败").arg(m_id.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        otItem["id"] = uuid;
        otItem["otID"] = m_id;
        listSql.push_back(ZGUtils::generateInsertSql("op_param_ot_item", otItem));
        items.push_back(std::move(otItem));
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建操作票项失败，操作票ID: '%1', 操作术语ID: '%2'")
                          .arg(m_id.c_str())
                          .arg(termID.c_str())
                          .toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::deleteItem(ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    QString sql =
        QString("SELECT * FROM op_param_ot_item WHERE otID = '%1' ORDER BY itemIndex DESC").arg(m_id.c_str());
    ZG6000::ListStringMap listItem;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listItem))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取操作票'%1'项失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listItem.empty())
        return true;
    const auto &termID = listItem[0]["termID"];
    const auto &deviceID = listItem[0]["deviceID"];
    std::string ids;
    for (auto &item : listItem)
    {
        if (item["termID"] == termID && item["deviceID"] == deviceID)
            ids += "'" + item["id"] + "',";
        else
            break;
    }
    ids.pop_back();
    sql = QString("DELETE FROM op_param_ot_item WHERE id IN (%1)").arg(ids.c_str());
    if (!ZGProxyCommon::execSql(sql.toStdString()))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("删除操作票'%1'项失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::nextItem()
{
    std::string itemIndex;
    QString sql = QString("SELECT rtItemIndex FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), itemIndex))
    {
        ZGLOG_ERROR(QStringLiteral("获取操作票'%1'当前项失败").arg(m_id.c_str()));
        return false;
    }
    ZGLOG_TRACE(QString("currentItemIndex: %1").arg(itemIndex.c_str()));
    int nextItemIndex = itemIndex.empty() ? 1 : std::atoi(itemIndex.c_str()) + 1;
    sql = QString("SELECT id FROM op_param_ot_item WHERE otID = '%1' AND itemIndex = '%2'")
        .arg(m_id.c_str())
        .arg(nextItemIndex);
    ZG6000::StringList listItemId;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listItemId))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'下一项失败").arg(m_id.c_str()));
        return false;
    }
    ZG6000::StringList listSql;
    try
    {
        if (listItemId.empty())
        {
            ZGLOG_TRACE(QStringLiteral("任务'%1'已完成").arg(m_id.c_str()));
            ZG6000::StringMap task{
                {"id", m_id},
                {"rtTaskStateID", "ZG_TS_FINISHED"},
                {"rtExecEndTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString()} };
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
        }
        else
        {
            if (listItemId.size() > 1)
            {
                ZGLOG_ERROR(QStringLiteral("任务'%1'序号为'%2'的项不唯一").arg(m_id.c_str()).arg(nextItemIndex));
                return false;
            }
            const auto& itemID = listItemId[0];
            ZG6000::StringMap ot{ {"id", m_id}, {"rtItemID", itemID}, {"rtItemIndex", std::to_string(nextItemIndex)} };
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            ZGLOG_ERROR(QStringLiteral("更新任务'%1'下一项失败").arg(m_id.c_str()));
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

bool ZGOPTaskOTTicket::nextPreviewItem()
{
    std::string itemIndex;
    QString sql = QString("SELECT rtPreviewItemIndex FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), itemIndex))
    {
        ZGLOG_ERROR(QStringLiteral("获取操作票'%1'当前预演项失败").arg(m_id.c_str()));
        return false;
    }
    ZGLOG_TRACE(QString("currentPreviewItemIndex: %1").arg(itemIndex.c_str()));
    int nextItemIndex = itemIndex.empty() ? 1 : std::atoi(itemIndex.c_str()) + 1;
    sql = QString("SELECT id FROM op_param_ot_item WHERE otID = '%1' AND itemIndex = '%2'")
        .arg(m_id.c_str())
        .arg(nextItemIndex);
    ZG6000::StringList listItemId;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listItemId))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'下一项失败").arg(m_id.c_str()));
        return false;
    }
    ZG6000::StringList listSql;
    try
    {
        if (listItemId.empty())
        {
            ZG6000::StringMap ot{{"id", m_id}, {"rtPreviewStateID", "ZG_TS_FINISHED"}};
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
        }
        else
        {
            if (listItemId.size() > 1)
            {
                ZGLOG_ERROR(QStringLiteral("任务'%1'序号为'%2'的项不唯一").arg(m_id.c_str()).arg(nextItemIndex));
                return false;
            }
            const auto& itemID = listItemId[0];
            ZG6000::StringMap ot{ {"id", m_id}, {"rtPreviewItemID", itemID}, {"rtPreviewItemIndex", std::to_string(nextItemIndex)} };
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            ZGLOG_ERROR(QStringLiteral("更新任务'%1'下一项预演项失败").arg(m_id.c_str()));
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

inline bool ZGOPTaskOTTicket::doAction(std::string action, ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    ZG6000::ListStringMap listTask;
    QString sql = QString("SELECT rtTaskStageID, rtTaskStateID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取操作票'%1'阶段及状态失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listTask.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("操作票'%1'不存在").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    const auto& task = listTask.front();
    const auto& stageID = ZGUtils::get(task, "rtTaskStageID", "");
    const auto& stateID = ZGUtils::get(task, "rtTaskStateID", "");
    fsm.set_state({ stageID, stateID });
    std::string errMsg;
    if (!fsm.command(action, std::move(param), errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg;
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::doPreviewAction(std::string action, ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    std::string previewStateID;
    QString sql = QString("SELECT rtPreviewStateID FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), previewStateID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取操作票'%1'预演状态失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    fsm_preview.set_state(previewStateID);
    std::string errMsg;
    if (!fsm_preview.command(std::move(action), std::move(param), errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg;
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::initExecuteContext()
{
    m_pCurrentItem = new ZGOPTaskOTItem(this);
    m_pCurrentItem->initialize();
    registerAction();
    return true;
}

bool ZGOPTaskOTTicket::initPreviewContext()
{
    m_pCurrentPreviewItem = new ZGOPTaskOTPreviewItem(this);
    m_pCurrentPreviewItem->initialize();
    registerPreviewAction();
    return true;
}

void ZGOPTaskOTTicket::registerAction()
{
    fsm.on({"ZG_TS_CREATE", "ZG_TS_FINISHED"}, ZGOPOT_CONFIRM) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onCreateConfirm(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXAM", "ZG_TS_READY"}, ZGOPOT_CONFIRM) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExamConfirm(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXAM", "ZG_TS_FINISHED"}, ZGOPOT_CONFIRM) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExamConfirm(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_READY"}, ZGOPOT_START) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteStart(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_EXECUTING"}, ZGOPOT_PAUSE) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecutePause(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_EXECUTING"}, ZGOPOT_NEXT_STEP) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteNextStep(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_EXECUTING"}, ZGOPITEM_TIMEOUT) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteItemTimeout(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_EXECUTING"}, ZGOPITEM_ERROR) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteItemError(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_PAUSED"}, ZGOPOT_RESUME) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteResume(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_ERROR"}, ZGOPOT_RETRY) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteRetry(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_ERROR"}, ZGOPITEM_SKIP) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteSkip(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_ITEM_TIMEOUT"}, ZGOPOT_RETRY) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteRetry(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_ITEM_TIMEOUT"}, ZGOPITEM_SKIP) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteSkip(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_FINISHED"}, ZGOPOT_CONFIRM) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteConfirm(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_STORE", "ZG_TS_FINISHED"}, ZGOPOT_CONFIRM) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onStoreFinish(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_STOPPED"}, ZGOPOT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_READY"}, ZGOPOT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_PAUSED"}, ZGOPOT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_ERROR"}, ZGOPOT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_ITEM_TIMEOUT"}, ZGOPOT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_TASK_TIMEOUT"}, ZGOPOT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
}

void ZGOPTaskOTTicket::registerPreviewAction()
{
    fsm_preview.on("ZG_TS_READY", ZGOPOT_START) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewStart(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_EXECUTING", ZGOPOT_STOP) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewStop(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_EXECUTING", ZGOPOT_PAUSE) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewPause(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_EXECUTING", ZGOPOT_NEXT_STEP) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewNextStep(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_EXECUTING", ZGOPITEM_TIMEOUT) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewItemTimeout(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_EXECUTING", ZGOPITEM_ERROR) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewItemError(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_PAUSED", ZGOPOT_RESUME) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewResume(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_ERROR", ZGOPOT_RETRY) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewRetry(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_ITEM_TIMEOUT", ZGOPOT_RETRY) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewRetry(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_FINISHED", ZGOPOT_CONFIRM) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewConfirm(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_PAUSED", ZGOPOT_STOP) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewStop(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_ERROR", ZGOPOT_STOP) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewStop(std::move(args), errMsg);
    };
    fsm_preview.on("ZG_TS_ITEM_TIMEOUT", ZGOPOT_STOP) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onPreviewStop(std::move(args), errMsg);
    };
}

inline bool ZGOPTaskOTTicket::onCreateConfirm(ZG6000::StringMap args, std::string &errMsg)
{
    std::string rtPreviewStateID;
    QString sql = QString("SELECT rtPreviewStateID FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtPreviewStateID))
    {
        errMsg = QStringLiteral("获取操作票预演状态失败").toStdString();
        return false;
    }
    if (!rtPreviewStateID.empty())
    {
        if (rtPreviewStateID != "ZG_TS_READY")
        {
            errMsg = QStringLiteral("当前操作票未正常结束预演，无法提交操作票").toStdString();
            return false;
        }
    }
    sql = QString("SELECT examID FROM op_param_ot_system");
    ZG6000::StringList listExamID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listExamID))
    {
        errMsg = QStringLiteral("获取操作票系统表审批模板失败").toStdString();
        return false;
    }
    if (listExamID.empty())
    {
        errMsg = QStringLiteral("操作票系统表无记录").toStdString();
        return false;
    }
    if (listExamID[0].empty())
    {
        ZGLOG_DEBUG("execute ready");
        return updateExecuteReady(std::move(args), errMsg);
    }
    ZGLOG_DEBUG(QString("createExam, id = '%1'").arg(listExamID[0].c_str()));
    args["examID"] = listExamID[0];
    return createExam(std::move(args), errMsg);
}

bool ZGOPTaskOTTicket::onExamConfirm(ZG6000::StringMap args, std::string &errMsg)
{
    std::string rtExamID;
    QString sql = QString("SELECT rtExamID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtExamID))
    {
        errMsg = QStringLiteral("获取操作票审批ID失败").toStdString();
        return false;
    }
    if (!onPreviewStop(args, errMsg))
    {
        errMsg = QStringLiteral("停止预演失败").toStdString();
        return false;
    }
    sql = QString("SELECT examStateID FROM sp_real_exam WHERE id = '%1'").arg(rtExamID.c_str());
    std::string examStateID;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), examStateID))
    {
        errMsg = QStringLiteral("获取审批状态失败").toStdString();
        return false;
    }
    if (examStateID != "ZG_ES_ACCEPT")
    {
        errMsg = QStringLiteral("审批未通过").toStdString();
        return false;
    }
    if (!finishExam({{"examID", rtExamID}}, errMsg))
        return false;
    return updateExecuteReady(std::move(args), errMsg);
}

bool ZGOPTaskOTTicket::onExecuteStart(ZG6000::StringMap args, std::string &errMsg)
{
    if (!checkTicketValid(errMsg))
        return false;
    ZG6000::StringList listSql;
    QDateTime currTime = QDateTime::currentDateTime();
    ZG6000::StringMap task{ {"id", m_id},
                           {"rtTaskStateID", "ZG_TS_EXECUTING"},
                           {"rtExecStartTime", ZGUtils::DateTimeToString(currTime, true).toStdString()},
                           {"rtErrorDesc", ""} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    ZG6000::StringMap ot{ {"id", m_id},
                         {"rtItemID", ""},
                         {"rtItemIndex", "0"},
                         {"rtPreviewStateID", "ZG_TS_READY"},
                         {"rtPreviewItemID", ""},
                         {"rtPreviewItemIndex", "0"} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新票'%1'执行启动失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::onExecutePause(ZG6000::StringMap args, std::string &errMsg)
{
    if (!setCurrentState("ZG_TS_PAUSED"))
    {
        errMsg = QStringLiteral("设置票'%1'暂停状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::onExecuteResume(ZG6000::StringMap args, std::string &errMsg)
{
    std::string itemID;
    if (!getCurrentItem(itemID))
    {
        errMsg = QStringLiteral("获取票'%1'当前步骤项失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (itemID.empty())
    {
        errMsg = QStringLiteral("票'%1'当前步骤项为空").toStdString();
        return false;
    }
    ZG6000::StringMap itemState;
    QString sql = QString("SELECT termItemGroupID, rtStateID FROM op_param_ot_item WHERE id = '%1'").arg(itemID.c_str());
    if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), itemState))
    {
        errMsg = QStringLiteral("获取操作票项'%1'状态失败").arg(itemID.c_str()).toStdString();
        return false;
    }
    if ((itemState["termItemGroupID"] == "ZG_OTIG_SERVER") && itemState["rtStateID"].empty())
    {
        errMsg = QStringLiteral("步骤等待时间未完成，不能继续操作票").toStdString();
        return false;
    }
    ZG6000::StringList listSql;
    ZG6000::StringMap task{ {"id", m_id}, {"rtTaskStateID", "ZG_TS_EXECUTING"} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    QDateTime currTime = QDateTime::currentDateTime();
    ZG6000::StringMap item{ {"id", itemID}, {"rtExecTime", ZGUtils::DateTimeToString(currTime, true).toStdString()} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot_item", item));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新票'%1'执行继续失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::onExecuteRetry(ZG6000::StringMap args, std::string &errMsg)
{
    std::string itemID;
    if (!getCurrentItem(itemID))
    {
        errMsg = QStringLiteral("获取票'%1'当前步骤项失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (itemID.empty())
    {
        errMsg = QStringLiteral("操作票项ID不能为空").toStdString();
        return false;
    }
    ZG6000::StringMap task{ {"id", m_id}, {"rtTaskStateID", "ZG_TS_EXECUTING"}, {"rtErrorDesc", ""} };
    ZG6000::StringList listSql;
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    QDateTime currTime = QDateTime::currentDateTime();
    ZG6000::StringMap item{ {"id", itemID},
                           {"rtStateID", "ZG_OIS_READY"},
                           {"rtExecTime", ZGUtils::DateTimeToString(currTime, true).toStdString()} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot_item", item));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新票'%1'执行启动失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::onExecuteConfirm(ZG6000::StringMap args, std::string &errMsg)
{
    ZG6000::StringMap task{ {"id", m_id},  {"rtExecEndTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString()}};
    const auto& sql = ZGUtils::generateUpdateSql("op_param_task", task);
    if (!ZGProxyCommon::execSql(sql))
    {
        errMsg = QStringLiteral("更新操作票执行完成时间失败").toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    if (!achiveOt(errMsg))
        return false;
    return true;
}

bool ZGOPTaskOTTicket::onExecuteSkip(ZG6000::StringMap args, std::string &errMsg)
{
    std::string itemID;
    if (!getCurrentItem(itemID))
    {
        errMsg = QStringLiteral("获取票'%1'当前步骤项失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    std::string isForbidSkip;
    if (!ZGProxyCommon::getDataByField("op_param_ot_item", itemID, "isForbidSkip", isForbidSkip))
    {
        errMsg = QStringLiteral("获取步骤'%1'信息失败").arg(itemID.c_str()).toStdString();
        return false;
    }
    if (isForbidSkip == "1")
    {
        errMsg = QStringLiteral("步骤'%1'不允许跳步").arg(itemID.c_str()).toStdString();
        return false;
    }
    if (!m_pCurrentItem->setCurrentState(itemID, "ZG_OIS_SKIP"))
    {
        errMsg = QStringLiteral("设置跳步状态失败").toStdString();
        return false;
    }
    return onExecuteNextStep(std::move(args), errMsg);
}

bool ZGOPTaskOTTicket::onExecuteNextStep(ZG6000::StringMap args, std::string &errMsg)
{
    std::string currentItemID;
    if (!getCurrentItem(currentItemID))
    {
        errMsg = QStringLiteral("获取当前操作票项信息失败").toStdString();
        return false;
    }
    int nextIndex;
    if (currentItemID == "-1")
        nextIndex = 1;
    else
    {
        // 获得当前步骤的顺序号
        ZG6000::StringMap otItem;
        if (!ZGProxyCommon::getDataByFields("op_param_ot_item", currentItemID, {"itemIndex"}, otItem))
        {
            errMsg = QStringLiteral("获取步骤'%1'顺序号失败").arg(currentItemID.c_str()).toStdString();
            return false;
        }
        // 查询是否存在下一步骤
        nextIndex = std::atoi(otItem["itemIndex"].c_str()) + 1;
    }
    ZGLOG_DEBUG(QString("next step index: %1").arg(nextIndex));
    QString sql = QString("SELECT id FROM op_param_ot_item WHERE otID = '%1' AND itemIndex = %2")
                      .arg(m_id.c_str())
                      .arg(nextIndex);
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
    {
        errMsg = QStringLiteral("获取下一步骤失败，当前步骤'%1'").arg(currentItemID.c_str()).toStdString();
        return false;
    }
    if (listID.empty())
    {
        ZG6000::StringMap task;
        task["id"] = m_id;
        task["rtTaskStateID"] = "ZG_TS_FINISHED";
        task["rtExecEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString();
        sql = ZGUtils::generateUpdateSql("op_param_task", task).c_str();
        if (!ZGProxyCommon::execSql(sql.toStdString()))
        {
            errMsg = QStringLiteral("设置票'%1'完成状态失败").arg(m_id.c_str()).toStdString();
            return false;
        }
        return true;
    }
    if (listID.size() > 1)
    {
        errMsg = QStringLiteral("步骤'%1'存在重复的下一步骤").arg(currentItemID.c_str()).toStdString();
        return false;
    }
    const auto &itemID = listID[0];
    ZG6000::StringList listSql;
    ZG6000::StringMap ot{{"id", m_id}, {"rtItemID", itemID}, {"rtItemIndex", std::to_string(nextIndex)}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新任务'%1'下一步骤失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::onExecuteItemTimeout(ZG6000::StringMap args, std::string &errMsg)
{
    if (!setCurrentState("ZG_TS_ITEM_TIMEOUT"))
    {
        errMsg = QStringLiteral("设置票'%1'超时状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::onExecuteItemError(ZG6000::StringMap args, std::string &errMsg)
{
    ZGLOG_DEBUG("onExecuteItemError");
    ZG6000::StringMap mapParam;
    mapParam["id"] = m_id;
    mapParam["rtTaskStateID"] = "ZG_TS_ERROR";
    mapParam["rtErrorDesc"] = args["rtErrorDesc"];
    ZGLOG_DEBUG(QStringLiteral("rtErrorDesc: '%1'").arg(args["rtErrorDesc"].c_str()));
    std::string sql = ZGUtils::generateUpdateSql("op_param_task", mapParam);
    if (!ZGProxyCommon::execSql(sql))
    {
        errMsg = QStringLiteral("更新票'%1'出错状态出错").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::onStoreFinish(ZG6000::StringMap args, std::string &errMsg)
{
    return false;
}

bool ZGOPTaskOTTicket::onAbolish(ZG6000::StringMap args, std::string &errMsg)
{
    std::string typeID;
    if (!ZGProxyCommon::getDataByField("op_param_ot", m_id, "typeID", typeID))
    {
        errMsg = QStringLiteral("获取票'%1'类型失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (!setCurrentState("ZG_TS_ABOLISH"))
    {
        errMsg = QStringLiteral("设置票'%1'作废状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (!achiveOt(errMsg))
        return false;
    return true;
}

bool ZGOPTaskOTTicket::onPreviewStart(ZG6000::StringMap args, std::string &errMsg)
{
    if (!updatePreviewReady(args, errMsg))
        return false;
    ZG6000::StringList listSql;
    ZG6000::StringMap task{ {"id", m_id}, {"rtErrorDesc", ""} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    ZG6000::StringMap ot{ {"id", m_id}, {"rtPreviewStateID", "ZG_TS_EXECUTING"} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新票'%1'预演执行启动失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::onPreviewStop(ZG6000::StringMap args, std::string &errMsg)
{
    if (!clearItemFlag(errMsg))
        return false;
    return updatePreviewReady(std::move(args), errMsg);
}

bool ZGOPTaskOTTicket::onPreviewPause(ZG6000::StringMap args, std::string &errMsg)
{
    if (!setPreviewState("ZG_TS_PAUSED"))
    {
        errMsg = QStringLiteral("设置票'%1'预演暂停状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::onPreviewResume(ZG6000::StringMap args, std::string &errMsg)
{
    std::string itemID;
    if (!getPreviewItem(itemID))
    {
        errMsg = QStringLiteral("获取票'%1'当前预演步骤ID失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (itemID.empty() || itemID == "-1")
    {
        errMsg = QStringLiteral("票'%1'当前步骤ID无效").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZG6000::StringList listSql;
    ZG6000::StringMap ot{ {"id", m_id}, {"rtPreviewStateID", "ZG_TS_EXECUTING"} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    QDateTime currTime = QDateTime::currentDateTime();
    ZG6000::StringMap item{ {"id", itemID}, {"rtPreviewExecTime", ZGUtils::DateTimeToString(currTime, true).toStdString()} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot_item", item));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新票'%1'执行继续失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::onPreviewRetry(ZG6000::StringMap args, std::string &errMsg)
{
    std::string itemID;
    if (!getPreviewItem(itemID))
    {
        errMsg = QStringLiteral("获取票'%1'当前预演步骤ID失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (itemID.empty() || itemID == "-1")
    {
        errMsg = QStringLiteral("票'%1'当前步骤ID无效").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZG6000::StringList listSql;
    ZG6000::StringMap task{ {"id", m_id}, {"rtErrorDesc", ""} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    ZG6000::StringMap ot{ {"id", m_id}, {"rtPreviewStateID", "ZG_TS_EXECUTING"} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    QDateTime currTime = QDateTime::currentDateTime();
    ZG6000::StringMap item{ {"id", itemID},
                           {"rtPreviewStateID", "ZG_OIS_READY"},
                           {"rtPreviewExecTime", ZGUtils::DateTimeToString(currTime, true).toStdString()} };
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot_item", item));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新票'%1'执行重试失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::onPreviewConfirm(ZG6000::StringMap args, std::string &errMsg)
{
    if (!clearItemFlag(errMsg))
    {
        errMsg = u8"清除预演标志失败";
        return false;
    }
    return updatePreviewReady(std::move(args), errMsg);
}

bool ZGOPTaskOTTicket::onPreviewNextStep(ZG6000::StringMap args, std::string &errMsg)
{
    ZGLOG_DEBUG("onPreviewNextStep");
    std::string currentItemID;
    if (!getPreviewItem(currentItemID))
    {
        errMsg = QStringLiteral("获取票'%1'当前预演步骤ID失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    int nextIndex;
    if (currentItemID == "-1")
        nextIndex = 1;
    else
    {
        // 获得当前步骤的顺序号
        ZG6000::StringMap otItem;
        if (!ZGProxyCommon::getDataByFields("op_param_ot_item", currentItemID, {"itemIndex"}, otItem))
        {
            errMsg = QStringLiteral("获取步骤'%1'顺序号失败").arg(currentItemID.c_str()).toStdString();
            return false;
        }
        // 查询是否存在下一步骤
        nextIndex = std::atoi(otItem["itemIndex"].c_str()) + 1;
    }
    ZGLOG_DEBUG(QString("next step index: %1").arg(nextIndex));
    QString sql = QString("SELECT id FROM op_param_ot_item WHERE otID = '%1' AND itemIndex = %2")
                      .arg(m_id.c_str())
                      .arg(nextIndex);
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
    {
        errMsg = QStringLiteral("获取下一步骤失败，当前步骤'%1'").arg(currentItemID.c_str()).toStdString();
        return false;
    }
    if (listID.empty())
    {
        if (!setPreviewState("ZG_TS_FINISHED"))
        {
            errMsg = QStringLiteral("设置票'%1'预演完成状态失败").arg(m_id.c_str()).toStdString();
            return false;
        }
        return true;
    }
    if (listID.size() > 1)
    {
        errMsg = QStringLiteral("步骤'%1'存在重复的下一步骤").arg(currentItemID.c_str()).toStdString();
        return false;
    }
    const auto &itemID = listID[0];
    ZGLOG_DEBUG(QString("next itemID: %1").arg(itemID.c_str()));
    ZG6000::StringList listSql;
    ZG6000::StringMap ot{{"id", m_id}, {"rtPreviewItemID", itemID}, {"rtPreviewItemIndex", std::to_string(nextIndex)}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新任务'%1'下一预演步骤失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::onPreviewItemTimeout(ZG6000::StringMap args, std::string &errMsg)
{
    ZGLOG_DEBUG("onPreviewItemTimeout");
    if (!setPreviewState("ZG_TS_ITEM_TIMEOUT"))
    {
        errMsg = QStringLiteral("设置票'%1'预演超时状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::onPreviewItemError(ZG6000::StringMap args, std::string &errMsg)
{
    ZG6000::StringMap task, ot;
    task["id"] = m_id;
    ot["id"] = m_id;
    ot["rtPreviewStateID"] = "ZG_TS_ERROR";
    task["rtErrorDesc"] = args["rtErrorDesc"];
    ZG6000::StringList listSql;
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新票'%1'预演错误状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

void ZGOPTaskOTTicket::processTaskChange(const ZG6000::MapField &record)
{
    QJsonObject root;
    QJsonObject dataObj;
    try
    {
        for (const auto &field : record)
        {
            if (field.first == "rtTaskStageID")
            {
                dataObj.insert("rtTaskStageName",
                               ZG6000::ZGOPTaskOTMng::instance()->getTaskStage(field.second.newValue).c_str());
            }
            if (field.first == "rtTaskStateID")
            {
                dataObj.insert("rtTaskStateName",
                               ZG6000::ZGOPTaskOTMng::instance()->getTaskState(field.second.newValue).c_str());
            }
            dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
        }
        root["head"] = dataObj;
        QJsonDocument doc(root);
        const auto &taskID = ZGUtils::get(record, "id").newValue;
        ZGLOG_DEBUG(doc.toJson());
        ZG6000::ZGOPTaskOTMng::instance()->publishMessage(QString("op_param_ot/%1").arg(taskID.c_str()), doc.toJson());
        std::string taskTypeID;
        if (ZGProxyCommon::getDataByField("op_param_task", taskID, "taskTypeID", taskTypeID))
            ZG6000::ZGOPTaskOTMng::instance()->publishMessage(
                QString("op_param_task/%1/update").arg(taskTypeID.c_str()), QString("%1").arg(taskID.c_str()));
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGOPTaskOTTicket::processOtChange(const ZG6000::MapField &record)
{
    QJsonObject root;
    QJsonObject dataObj;
    try
    {
        for (const auto &field : record)
        {
            dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
            if (field.first == "rtItemID")
            {
                ZGLOG_TRACE(QString("rtItemID = '%1'").arg(field.second.newValue.c_str()));
                const auto &itemID = field.second.newValue;
                if (itemID.empty())
                    continue;
                ZG6000::StringMap item;
                if (!ZGProxyCommon::getDataByID("op_param_ot_item", itemID, item))
                {
                    ZGLOG_ERROR(QStringLiteral("获取步骤'%1'信息失败").arg(itemID.c_str()));
                    continue;
                }
                ZG6000::StringList listSql;
                const auto& termItemGroupID = ZGUtils::get(item, "termItemGroupID");
                const auto& autoExec = ZGUtils::get(item, "isAutoExec");
                QDateTime currTime = QDateTime::currentDateTime();
                if (termItemGroupID == "ZG_OTIG_SERVER")
                {
                    if (autoExec == "1")
                    {
                        ZG6000::StringMap task{{"id", m_id}, {"rtTaskStateID", "ZG_TS_EXECUTING"}, {"rtErrorDesc", ""}};
                        listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
                        ZG6000::StringMap otNewItem{{"id", itemID},
                                                {"rtStateID", "ZG_OIS_READY"},
                                                {"rtExecTime", ZGUtils::DateTimeToString(currTime, true).toStdString()}};
                        listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot_item", otNewItem));
                    }
                    else
                    {
                        ZG6000::StringMap task{{"id", m_id}, {"rtTaskStateID", "ZG_TS_PAUSED"}, {"rtErrorDesc", ""}};
                        listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
                        ZG6000::StringMap otNewItem = ZG6000::StringMap{{"id", itemID},
                                                    {"rtStateID", ""},
                                                   {"rtExecTime", ZGUtils::DateTimeToString(currTime, true).toStdString()}};
                        listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot_item", otNewItem));
                    }
                }
                else
                {
                    ZG6000::StringMap task{{"id", m_id}, {"rtTaskStateID", "ZG_TS_EXECUTING"}, {"rtErrorDesc", ""}};
                    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
                }
                if (!ZGProxyCommon::execBatchSql(listSql))
                {
                    for (const auto& sql: listSql)
                    {
                        ZGLOG_TRACE(sql.c_str());
                    }
                    ZGLOG_ERROR(QStringLiteral("更新步骤'%1'状态失败").arg(itemID.c_str()));
                    continue;
                }
                ZGProxyCommon::synchronize();
            }
            if (field.first == "rtPreviewItemID")
            {
                const auto &itemID = field.second.newValue;
                if (itemID.empty())
                    continue;
                ZG6000::StringMap item;
                if (!ZGProxyCommon::getDataByID("op_param_ot_item", itemID, item))
                {
                    ZGLOG_ERROR(QStringLiteral("获取步骤'%1'信息失败").arg(itemID.c_str()));
                    continue;
                }
                ZG6000::StringList listSql;
                QDateTime currTime = QDateTime::currentDateTime();
                ZG6000::StringMap otNewItem{
                        {"id", itemID},
                        {"rtPreviewStateID", "ZG_OIS_READY"},
                        {"rtPreviewExecTime", ZGUtils::DateTimeToString(currTime, true).toStdString()} };
                listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot_item", otNewItem));
                if (!ZGProxyCommon::execBatchSql(listSql))
                {
                    ZGLOG_ERROR(QStringLiteral("更新步骤'%1'预演状态失败").arg(itemID.c_str()));
                    continue;
                }
                ZGProxyCommon::synchronize();
            }
            if (field.first == "rtPreviewStateID")
            {
                dataObj.insert("rtPreviewStateName",
                               ZG6000::ZGOPTaskOTMng::instance()->getTaskState(field.second.newValue).c_str());
            }
        }
        root["head"] = dataObj;
        QJsonDocument doc(root);
        const auto &taskID = ZGUtils::get(record, "id");
        ZGLOG_INFO(doc.toJson());
        ZG6000::ZGOPTaskOTMng::instance()->publishMessage(QString("op_param_ot/%1").arg(taskID.newValue.c_str()),
                                                          doc.toJson());
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGOPTaskOTTicket::processItemChange(const ZG6000::MapField &record)
{
    QJsonObject root;
    QJsonObject item;
    QJsonObject dataObj;
    try
    {
        const auto &itemID = ZGUtils::get(record, "id").newValue;
        for (const auto &field : record)
        {
            if (field.first == "rtStateID")
            {
                dataObj.insert("rtStateName",
                               ZG6000::ZGOPTaskOTMng::instance()->getItemState(field.second.newValue).c_str());
            }
            if (field.first == "rtPreviewStateID")
            {
                dataObj.insert("rtPreviewStateName",
                               ZG6000::ZGOPTaskOTMng::instance()->getItemState(field.second.newValue).c_str());
            }
            dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
        }
        root["item"] = dataObj;
        QJsonDocument doc(root);
        const auto& json = doc.toJson();
        ZGLOG_INFO(json);
        ZG6000::ZGOPTaskOTMng::instance()->publishMessage(QString("op_param_ot/%1").arg(m_id.c_str()), json);
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGOPTaskOTTicket::processExamChange(const ZG6000::MapField &record)
{
    try
    {
        // 获取当前任务的执行阶段
        std::string rtTaskStageID;
        QString sql = QString("SELECT rtTaskStageID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtTaskStageID))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务'%1'执行阶段失败").arg(m_id.c_str()));
            return;
        }
        // 如果当前阶段不是审批阶段，不处理
        if (rtTaskStageID != "ZG_TS_EXAM")
            return;
        const auto &examStateID = ZGUtils::get(record, "examStateID").newValue;
        std::string newTaskStateID;
        auto it =
            std::find_if(m_listPairState.begin(), m_listPairState.end(),
                         [&](const std::pair<std::string, std::string> &pair) { return pair.second == examStateID; });
        if (it != m_listPairState.end())
            newTaskStateID = it->first;
        ZG6000::StringMap updateTask{{"id", m_id}};
        updateTask["rtTaskStageID"] = "ZG_TS_EXAM";
        updateTask["rtTaskStateID"] = newTaskStateID;
        if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql("op_param_task", updateTask)))
        {
            ZGLOG_ERROR(QStringLiteral("更新任务'%1'审批状态失败").arg(m_id.c_str()));
            return;
        }
        ZGProxyCommon::synchronize();
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

bool ZGOPTaskOTTicket::updateInitFinish(ZG6000::StringMap args, std::string &errMsg)
{
    // 前端需要获取删除状态
    if (!setCurrentStage("ZG_TS_DELETE"))
    {
        ZGLOG_ERROR(QStringLiteral("设置票'%1'删除阶段失败").arg(m_id.c_str()));
        return false;
    }
    ZG6000::StringList listSql;
    ZG6000::StringMap task = generateInitTaskHead();
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    ZG6000::StringMap ot{{"id", m_id}, {"rtNumber", ""}, {"rtTaskOrder", ""}, {"rtPreviewStateID", "ZG_TS_READY"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    ZG6000::StringMap item;
    item["rtStateID"] = "ZG_OIS_READY";
    item["rtExecTime"] = "";
    listSql.push_back(
        ZGUtils::generateUpdateSql("op_param_ot_item", item, QString("otID='%1'").arg(m_id.c_str()).toStdString()));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新票'%1'初始化状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::updateExecuteReady(ZG6000::StringMap args, std::string &errMsg)
{
    ZG6000::StringList listSql;
    ZG6000::StringMap task{{"id", m_id}, {"rtTaskStageID", "ZG_TS_EXECUTE"}, {"rtTaskStateID", "ZG_TS_READY"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    ZG6000::StringMap ot{{"id", m_id},
                         {"rtItemID", ""},
                         {"rtItemIndex", "0"},
                         {"rtPreviewStateID", "ZG_TS_READY"},
                         {"rtPreviewItemID", ""},
                         {"rtPreviewItemIndex", "0"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    ZG6000::StringMap item{{"rtStateID", ""}, {"rtExecTime", ""}, {"rtPreviewStateID", ""}, {"rtPreviewExecTime", ""}};
    listSql.push_back(
        ZGUtils::generateUpdateSql("op_param_ot_item", item, QString("otID = '%1'").arg(m_id.c_str()).toStdString()));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新票'%1'执行就绪失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::updatePreviewReady(ZG6000::StringMap args, std::string &errMsg)
{
    ZG6000::StringList listSql;
    ZG6000::StringMap ot{
        {"id", m_id}, {"rtPreviewStateID", "ZG_TS_READY"}, {"rtPreviewItemID", ""}, {"rtPreviewItemIndex", "0"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_ot", ot));
    ZG6000::StringMap item{{"rtPreviewStateID", ""}, {"rtPreviewExecTime", ""}};
    listSql.push_back(
        ZGUtils::generateUpdateSql("op_param_ot_item", item, QString("otID = '%1'").arg(m_id.c_str()).toStdString()));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新票'%1'预演执行就绪失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskOTTicket::createExam(ZG6000::StringMap args, std::string &errMsg)
{
    auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
    if (examProxy == nullptr)
    {
        errMsg = QStringLiteral("获取审批管理服务代理对象失败").toStdString();
        return false;
    }
    try
    {
        std::string examID;
        ZG6000::ErrorInfo e;
        if (!examProxy->createExam(args["examID"], examID, e))
        {
            errMsg = e.errDetail;
            return false;
        }
        ZG6000::StringMap task;
        task["id"] = m_id;
        task["rtTaskStageID"] = "ZG_TS_EXAM";
        task["rtTaskStateID"] = "ZG_TS_READY";
        task["rtExamID"] = examID;
        if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql("op_param_task", task)))
        {
            errMsg = QStringLiteral("更新任务审批阶段失败").toStdString();
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }
    catch (const Ice::Exception &e)
    {
        errMsg = e.what();
        return false;
    }
}

bool ZGOPTaskOTTicket::finishExam(ZG6000::StringMap args, std::string &errMsg)
{
    auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
    if (examProxy == nullptr)
    {
        errMsg = QStringLiteral("获取审批管理服务代理对象失败").toStdString();
        return false;
    }
    try
    {
        ZG6000::ErrorInfo e;
        if (!examProxy->finishExam(args["examID"], e))
        {
            errMsg = e.errDetail;
            return false;
        }
        return true;
    }
    catch (const Ice::Exception &e)
    {
        errMsg = e.what();
        return false;
    }
}

bool ZGOPTaskOTTicket::achiveOt(std::string &errMsg)
{
    if (!saveOt(errMsg))
        return false;
    ZG6000::ErrorInfo e;
    if (!ZG6000::ZGOPTaskOTMng::instance()->deleteTask(m_id, {}, e))
    {
        errMsg = e.errDetail;
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::saveOt(std::string &errMsg)
{
    QString sql = QString("SELECT * FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    ZG6000::ListStringMap listTask;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
    {
        errMsg = QStringLiteral("获取操作票'%1'信息失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (listTask.empty())
    {
        errMsg = QStringLiteral("操作票'%1'不存在").arg(m_id.c_str()).toStdString();
        return false;
    }
    auto task = listTask.front();
    sql = QString("SELECT * FROM op_param_ot WHERE id = '%1'").arg(m_id.c_str());
    ZG6000::ListStringMap listOt;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listOt))
    {
        errMsg = QStringLiteral("获取操作票'%1'信息失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (listOt.empty())
    {
        errMsg = QStringLiteral("操作票'%1'不存在").arg(m_id.c_str()).toStdString();
        return false;
    }
    auto ot = listOt.front();
    ZG6000::StringMap hisOt;
    std::string uuid;
    if (!ZGProxyCommon::createUUID(uuid))
    {
        errMsg = QStringLiteral("创建历史票ID失败").toStdString();
        return false;
    }
    hisOt["id"] = uuid;
    hisOt["name"] = task["name"];
    hisOt["typeID"] = ot["typeID"];
    hisOt["typeName"] = ZG6000::ZGOPTaskOTMng::instance()->getOTType(ot["typeID"]);
    hisOt["appNodeID"] = task["appNodeID"];
    const auto &appNode = ZG6000::ZGOPTaskOTMng::instance()->getAppNode(task["appNodeID"]);
    hisOt["appNodeName"] = ZGUtils::get(appNode, "name", "");
    hisOt["subsystemID"] = task["subsystemID"];
    hisOt["subsystemName"] = ZG6000::ZGOPTaskOTMng::instance()->getSubsystem(task["subsystemID"]);
    hisOt["majorID"] = task["majorID"];
    hisOt["majorName"] = ZG6000::ZGOPTaskOTMng::instance()->getMajor(task["majorID"]);
    hisOt["startVoice"] = task["startVoice"];
    hisOt["endVoice"] = task["endVoice"];
    hisOt["rtNumber"] = ot["rtNumber"];
    hisOt["rtTaskOrder"] = ot["rtTaskOrder"];
    hisOt["reportTemplate"] = task["reportTemplate"];
    hisOt["rtOperUserID"] = task["rtOperUserID"];
    std::string userName;
    ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtOperUserID"], "name", userName);
    hisOt["rtOperUserName"] = userName;
    if (!task["rtMonUserID"].empty())
    {
        hisOt["rtMonUserID"] = task["rtMonUserID"];
        ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtMonUserID"], "name", userName);
        hisOt["rtMonUserName"] = userName;
    }
    hisOt["rtCreateUserID"] = task["rtCreateUserID"];
    ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtCreateUserID"], "name", userName);
    hisOt["rtCreateUserName"] = userName;
    hisOt["rtCreateTime"] = task["rtCreateTime"];
    hisOt["rtStartTime"] = task["rtStartTime"];
    hisOt["rtEndTime"] = task["rtEndTime"];
    hisOt["rtTaskStageID"] = task["rtTaskStageID"];
    hisOt["rtTaskStageName"] = ZG6000::ZGOPTaskOTMng::instance()->getTaskStage(task["rtTaskStageID"]);
    hisOt["rtTaskStateID"] = task["rtTaskStateID"];
    hisOt["rtTaskStateName"] = ZG6000::ZGOPTaskOTMng::instance()->getTaskState(task["rtTaskStateID"]);
    hisOt["rtExecStartTime"] = task["rtExecStartTime"];
    hisOt["rtExecEndTime"] = task["rtExecEndTime"];
    hisOt["rtItemIndex"] = ot["rtItemIndex"];
    hisOt["rtStoreTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
    QDateTime dt;
    if (!ZGUtils::StringToDateTime(hisOt["rtStartTime"].c_str(), dt, true))
    {
        errMsg = QStringLiteral("无效的操作票开始时间'%1'").arg(hisOt["rtStartTime"].c_str()).toStdString();
        return false;
    }
    std::string hisOtTable = "op_his_ot_" + std::to_string(dt.date().year());
    ZG6000::StringList listSql;
    listSql.push_back(ZGUtils::generateInsertSql(hisOtTable, hisOt));
    sql = QString("SELECT * FROM op_param_ot_item WHERE otID = '%1'").arg(m_id.c_str());
    ZG6000::ListStringMap listOtItem;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listOtItem))
    {
        errMsg = QStringLiteral("获取操作票'%1'步骤信息失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    std::string hisItemTable = "op_his_ot_item_" + std::to_string(dt.date().year());
    for (auto &otItem : listOtItem)
    {
        std::string itemID;
        if (!ZGProxyCommon::createUUID(itemID))
        {
            errMsg = QStringLiteral("创建历史票项ID失败").toStdString();
            return false;
        }
        ZG6000::StringMap hisItem;
        hisItem["id"] = itemID;
        hisItem["otID"] = uuid;
        hisItem["termID"] = otItem["termID"];
        hisItem["termItemID"] = otItem["termItemID"];
        hisItem["name"] = otItem["name"];
        hisItem["itemIndex"] = otItem["itemIndex"];
        hisItem["deviceID"] = otItem["deviceID"];
        hisItem["updateValues"] = otItem["updateValues"];
        hisItem["lockID"] = otItem["lockID"];
        hisItem["termItemGroupID"] = otItem["termItemGroupID"];
        hisItem["termItemTypeID"] = otItem["termItemTypeID"];
        hisItem["isCheckExecCondition"] = otItem["isCheckExecCondition"];
        hisItem["isCheckConfirmCondition"] = otItem["isCheckConfirmCondition"];
        hisItem["isAutoExec"] = otItem["isAutoExec"];
        hisItem["lockConfirmCondition"] = otItem["lockConfirmCondition"];
        hisItem["startDelay"] = otItem["startDelay"];
        hisItem["endDelay"] = otItem["endDelay"];
        hisItem["timeout"] = otItem["timeout"];
        hisItem["rtStateID"] = otItem["rtStateID"];
        hisItem["rtStateName"] = ZG6000::ZGOPTaskOTMng::instance()->getItemState(otItem["rtStateID"]);
        hisItem["rtExecTime"] = otItem["rtExecTime"];
        listSql.push_back(ZGUtils::generateInsertSql(hisItemTable, hisItem));
    }
    if (!ZGProxyCommon::execBatchSql(listSql, true))
    {
        errMsg = QStringLiteral("保存操作票'%1'失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::getItemAutoExecState(const std::string &itemID, bool &autoExec)
{
    std::string isAutoExec;
    if (!ZGProxyCommon::getDataByField("op_param_ot_item", itemID, "isAutoExec", isAutoExec))
        return false;
    autoExec = (isAutoExec == "1");
    return true;
}

bool ZGOPTaskOTTicket::clearItemFlag(std::string &errMsg)
{
    QString sql = QString("SELECT * FROM op_param_ot_item WHERE otID = '%1'").arg(m_id.c_str());
    ZG6000::ListStringMap listItem;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listItem))
    {
        errMsg = QStringLiteral("获取操作票'%1'步骤项失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    auto ruleProxy = ZGProxyMng::instance()->getProxyMPRuleEngine();
    if (ruleProxy == nullptr)
    {
        errMsg = QStringLiteral("获取规则引擎服务代理对象失败").toStdString();
        return false;
    }
    std::map<std::string, bool> mapTerm;
    try
    {
        ZG6000::ErrorInfo e;
        for (const auto &item : listItem)
        {
            const auto &deviceID = ZGUtils::get(item, "deviceID");
            const auto& isCheckConfirmCondition = ZGUtils::get(item, "isCheckConfirmCondition");
            if (isCheckConfirmCondition == "1")
            {
                const auto &termID = ZGUtils::get(item, "termID");
                auto pair = mapTerm.find(termID);
                if (pair != mapTerm.end())
                    continue;
                std::string deviceSubtypeID;
                if (!ZGProxyCommon::getDataByField("mp_param_term", termID, "deviceSubtypeID", deviceSubtypeID))
                {
                    errMsg = QStringLiteral("获取操作术语'%1'子类型失败").arg(termID.c_str()).toStdString();
                    return false;
                }
                if (!deviceSubtypeID.empty())
                {
                    if (!deviceID.empty())
                    {
                        if (!ruleProxy->clearDeviceConfirmCondition(deviceID, termID, e))
                        {
                            ZGLOG_ERROR(e);
                            return false;
                        }
                    }
                }
                else
                {
                    const auto &otID = ZGUtils::get(item, "otID");
                    std::string appNodeID;
                    if (!ZGProxyCommon::getDataByField("op_param_task", otID, "appNodeID", appNodeID))
                    {
                        errMsg = QStringLiteral("获取任务'%1'关联应用节点失败").arg(otID.c_str()).toStdString();
                        return false;
                    }
                    if (!ruleProxy->clearAppNodeConfirmCondition(appNodeID, termID, e))
                    {
                        ZGLOG_ERROR(e);
                        return false;
                    }
                }
            }
            const auto& updateValues = ZGUtils::get(item, "updateValues");
            if (!updateValues.empty())
            {
                QJsonDocument doc = QJsonDocument::fromJson(updateValues.c_str());
                QJsonObject object = doc.object();
                if (!object.empty())
                {
                    ZG6000::MapStringMap propertis;
                    for (auto it = object.begin(); it != object.end(); ++it)
                    {
                        propertis[it.key().toStdString()] = ZG6000::StringMap{{"rtSimulateFlag", "0"}};
                    }
                    if (!ZGProxyCommon::updateProperties(deviceID, propertis, e))
                    {
                        ZGLOG_ERROR(e);
                        ZGLOG_ERROR(QStringLiteral("清除设备'%1'预演标志失败").arg(deviceID.c_str()));
                        return false;
                    }
                }
            }
        }
        return true;
    }
    catch (const Ice::Exception &e)
    {
        errMsg = e.what();
        return false;
    }
    catch (const std::exception &e)
    {
        errMsg = e.what();
        return false;
    }
}

bool ZGOPTaskOTTicket::deleteExam(ZG6000::ErrorInfo &e)
{
    std::string examID;
    QString sql = QString("SELECT rtExamID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), examID))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取任务'%1'审批ID失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!examID.empty())
    {
        auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
        if (examProxy == nullptr)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取审批管理服务代理对象失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            if (!examProxy->deleteExam(examID, e))
            {
                ZGLOG_ERROR(ZGJson::convertToJson(e).c_str());
                return false;
            }
        }
        catch (const Ice::Exception &ie)
        {
            ZGLOG_ERROR(ie.what());
            return false;
        }
    }
    return true;
}

bool ZGOPTaskOTTicket::checkTicketValid(std::string &errMsg)
{
    QString sql =
        QString("SELECT rtStartTime, rtEndTime, appNodeID FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    ZG6000::StringMap task;
    if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), task))
    {
        errMsg = QStringLiteral("获取任务'%1'时间参数失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    QDateTime currTime = QDateTime::currentDateTime();
    QDateTime startTime, endTime;
    if (!ZGUtils::StringToDateTime(task["rtStartTime"].c_str(), startTime, true))
    {
        errMsg = QStringLiteral("无效的票开始时间'%1'").arg(task["rtStartTime"].c_str()).toStdString();
        return false;
    }
    if (!ZGUtils::StringToDateTime(task["rtEndTime"].c_str(), endTime, true))
    {
        errMsg = QStringLiteral("无效的票结束时间'%1'").arg(task["rtEndTime"].c_str()).toStdString();
        return false;
    }
    ZGLOG_INFO(QString("currTime = %1, startTime = %2, endTime = %3").arg(ZGUtils::DateTimeToString(currTime))
        .arg(ZGUtils::DateTimeToString(startTime)).arg(ZGUtils::DateTimeToString(endTime)));
    if ((currTime < startTime) || (currTime > endTime))
    {
        errMsg = QStringLiteral("当前时间不在操作票的有效时间范围内").toStdString();
        return false;
    }
    return true;
}

std::string ZGOPTaskOTTicket::generateNumber(const std::string& rule, int otIndex)
{
    std::string otNumber = rule;
    const auto& variables = ZGUtils::extractVariables(otNumber);
    ZGLOG_TRACE(QString("%1").arg(variables.size()));
    for (const auto& variable : variables)
    {
        auto pos = variable.find(":");
        const auto& name = variable.substr(0, pos);
        std::string format;
        if (pos != std::string::npos)
            format = variable.substr(pos + 1);
        ZGLOG_TRACE(QString("%1,%2,%3").arg(variable.c_str()).arg(name.c_str()).arg(format.c_str()));
        if (name == "date")
        {
            if (format.empty())
                ZGUtils::replaceString(otNumber, "[" + variable + "]",
                    QDateTime::currentDateTime().date().toString("yyyy-MM-dd").toStdString());
            else
                ZGUtils::replaceString(otNumber, "[" + variable + "]",
                    QDateTime::currentDateTime().date().toString(format.c_str()).toStdString());
        }
        if (name == "index")
        {
            QString index;
            if (format.empty())
                index = QString("%1").arg(otIndex, 2, 10, QLatin1Char('0'));
            else
                index = QString("%1").arg(otIndex, std::atoi(format.c_str()), 10, QLatin1Char('0'));
            ZGUtils::replaceString(otNumber, "[" + variable + "]", index.toStdString());
        }
    }
    return otNumber;
}

bool ZGOPTaskOTTicket::checkOtUnique(const std::string &appNodeID, ZG6000::ErrorInfo &e)
{
    QString sql = QString("SELECT id FROM op_param_task WHERE taskTypeID = 'ZG_TT_OT' "
                          "AND rtTaskStageID <> 'ZG_TS_INIT' AND appNodeID = '%1'").arg(appNodeID.c_str());
    ZG6000::StringList listTaskID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listTaskID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取应用节点'%1'关联操作票失败").arg(appNodeID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!listTaskID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("该节点存在未结束的操作票").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::checkTypeOtUnique(const std::string &appNodeID, const std::string& subsystemID, const std::string& majorID, const std::string& templateID, ZG6000::ErrorInfo &e)
{
    QString sql = QString("SELECT a.id FROM op_param_task a LEFT JOIN op_param_ot b ON a.id = b.id "
                "WHERE a.appNodeID = '%1' AND a.subsystemID = '%2' AND a.majorID = '%3' AND b.templateID = '%4' ORDER BY a.id")
            .arg(appNodeID.c_str())
            .arg(subsystemID.c_str())
            .arg(majorID.c_str())
            .arg(templateID.c_str());
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取模板'%1'关联票失败").arg(templateID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!listID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("该节点已经存在同类型操作票，无法重复创建").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::checkAppNodeTermExecCondition(const std::string &appNodeID, const std::string &termID)
{
    auto ruleProxy = ZGProxyMng::instance()->getProxyMPRuleEngine();
    if (!ruleProxy)
    {
        ZGLOG_ERROR(QStringLiteral("获取规则引擎服务代理对象失败"));
        return false;
    }
    try
    {
        ZG6000::ErrorInfo e;
        if (!ruleProxy->checkAppNodeExecCondition(appNodeID, termID, 0, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
    return true;
}

ZG6000::StringMap ZGOPTaskOTTicket::generateFixedTaskHead(const std::string &taskID, const std::string &stageID,
                                                          const std::string &stateID, const ZG6000::StringMap &srcParam)
{
    ZG6000::StringMap dstParam;
    dstParam["id"] = taskID;
    dstParam["taskTypeID"] = "ZG_TT_OT";
    dstParam["rtExecStartTime"] = "";
    dstParam["rtExecEndTime"] = "";
    dstParam["rtErrorDesc"] = "";
    dstParam["rtTaskStageID"] = stageID;
    dstParam["rtTaskStateID"] = stateID;
    dstParam["rtCreateUserID"] = ZGUtils::get(srcParam, "createUserID", "");
    dstParam["rtCreateTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString();
    checkAndCopyField(dstParam, srcParam, "name");
    checkAndCopyField(dstParam, srcParam, "appNodeID");
    checkAndCopyField(dstParam, srcParam, "subsystemID");
    checkAndCopyField(dstParam, srcParam, "majorID");
    checkAndCopyField(dstParam, srcParam, "pageID");
    checkAndCopyField(dstParam, srcParam, "rtOperUserID");
    checkAndCopyField(dstParam, srcParam, "rtMonUserID");
    checkAndCopyField(dstParam, srcParam, "rtStartTime");
    checkAndCopyField(dstParam, srcParam, "rtEndTime");
    return dstParam;
}

inline ZG6000::StringMap ZGOPTaskOTTicket::generateTemplateTaskHead(const std::string &taskID,
                                                                    const ZG6000::StringMap &srcParam)
{
    ZG6000::StringMap dstParam;
    try
    {
        dstParam = generateFixedTaskHead(taskID, "ZG_TS_CREATE", "ZG_TS_FINISHED", srcParam);
        const auto &templateID = ZGUtils::get(srcParam, "templateID");
        std::string reportTemplate;
        ZGProxyCommon::getDataByField("op_param_ot_template", templateID, "reportTemplate", reportTemplate);
        dstParam["reportTemplate"] = reportTemplate;
        std::string taskName = ZG6000::ZGOPTaskOTMng::instance()->getTemplate(templateID);
        const auto &appNodeID = ZGUtils::get(srcParam, "appNodeID");
        const auto &appNode = ZG6000::ZGOPTaskOTMng::instance()->getAppNode(appNodeID);
        const auto &appNodeName = ZGUtils::get(appNode, "name", "");
        ZGUtils::replaceString(taskName, "[appNode]", appNodeName);
        dstParam["name"] = taskName;
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
    return dstParam;
}

ZG6000::StringMap ZGOPTaskOTTicket::generateTypicalTaskHead(const std::string &taskID,
                                                            const ZG6000::StringMap &srcParam)
{
    ZG6000::StringMap dstParam;
    dstParam["id"] = taskID;
    dstParam["rtTaskStageID"] = "ZG_TS_CREATE";
    dstParam["rtTaskStateID"] = "ZG_TS_FINISHED";
    dstParam["rtExamID"] = "";
    dstParam["rtCreateUserID"] = ZGUtils::get(srcParam, "createUserID", "");
    dstParam["rtCreateTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString();
    dstParam["rtOperUserID"] = "";
    dstParam["rtMonUserID"] = "";
    dstParam["rtStartTime"] = "";
    dstParam["rtEndTime"] = "";
    dstParam["rtExecStartTime"] = "";
    dstParam["rtExecEndTime"] = "";
    dstParam["rtErrorDesc"] = "";
    return dstParam;
}

ZG6000::StringMap ZGOPTaskOTTicket::generateInitTaskHead() const
{
    ZG6000::StringMap dstParam;
    dstParam["id"] = m_id;
    dstParam["rtTaskStageID"] = "ZG_TS_INIT";
    dstParam["rtTaskStateID"] = "ZG_TS_FINISHED";
    dstParam["rtExamID"] = "";
    dstParam["rtCreateUserID"] = "";
    dstParam["rtCreateTime"] = "";
    dstParam["rtOperUserID"] = "";
    dstParam["rtMonUserID"] = "";
    dstParam["rtStartTime"] = "";
    dstParam["rtEndTime"] = "";
    dstParam["rtExecStartTime"] = "";
    dstParam["rtExecEndTime"] = "";
    dstParam["rtClientID"] = "";
    dstParam["rtErrorDesc"] = "";
    return dstParam;
}

inline ZG6000::StringMap ZGOPTaskOTTicket::generateOtHeadParam(const std::string &ticketID, const std::string &typeID,
                                                               const ZG6000::StringMap &srcParam)
{
    ZG6000::StringMap dstParam;
    dstParam["id"] = ticketID;
    dstParam["typeID"] = typeID;
    dstParam["rtPreviewStateID"] = "ZG_TS_READY";
    checkAndCopyField(dstParam, srcParam, "templateID");
    checkAndCopyField(dstParam, srcParam, "rtNumber");
    dstParam["rtTaskOrder"] = "";
    ZG6000::StringMap systemParam = ZG6000::ZGOPTaskOTMng::instance()->getSystemParam();
    std::string rtNumberIndex;
    QString sql = QString("SELECT rtNumberIndex FROM op_param_ot_system WHERE id = '%1'").arg(systemParam["id"].c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtNumberIndex))
    {
        ZGLOG_ERROR(QStringLiteral("获取操作票当前序号失败"));
        return dstParam;
    }
    int otIndex = std::atoi(rtNumberIndex.c_str());
    ZGLOG_INFO(QString("rtNumberIndex = %1, otIndex = %2").arg(rtNumberIndex.c_str()).arg(otIndex));
    ++otIndex;
    ZG6000::StringMap systemIndex{{"id", systemParam["id"]}, {"rtNumberIndex", std::to_string(otIndex)}};
    sql = ZGUtils::generateUpdateSql("op_param_ot_system", systemIndex).c_str();
    ZGLOG_TRACE(sql);
    if (!ZGProxyCommon::execSql(sql.toStdString()))
    {
        ZGLOG_ERROR(QStringLiteral("更新操作票当前序号失败"));
        return dstParam;
    }
    if (!systemParam["numberRule"].empty())
    {
        dstParam["rtNumber"] = generateNumber(systemParam["numberRule"], otIndex);
        ZGLOG_TRACE(dstParam["rtNumber"].c_str());
    }
    if (!systemParam["taskOrderRule"].empty())
    {
        dstParam["rtTaskOrder"] = generateNumber(systemParam["taskOrderRule"], otIndex);
    }
    return dstParam;
}

bool ZGOPTaskOTTicket::generateOtItemParam(const std::string &ticketId, const ZG6000::StringMap &term,
                                           const ZG6000::StringMap &termItem, const std::string &appNodeID,
                                           const ZG6000::StringMap &templateItem, const QJsonObject &obj,
                                           ZG6000::StringMap &otItem, int &itemIndex, ZG6000::ErrorInfo &e)
{
    std::string itemID;
    if (!ZGProxyCommon::createUUID(itemID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建操作票'%1'项ID失败").arg(ticketId.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        otItem["id"] = itemID;
        otItem["otID"] = ticketId;
        otItem["termID"] = ZGUtils::get(term, "id");
        const auto &termItemID = ZGUtils::get(termItem, "id");
        otItem["termItemID"] = termItemID;
        otItem["termItemGroupID"] = ZGUtils::get(termItem, "termItemGroupID");
        otItem["termItemTypeID"] = ZGUtils::get(termItem, "termItemTypeID");
        otItem["updateValues"] = ZGUtils::get(termItem, "updateValues");
        otItem["isCheckExecCondition"] = ZGUtils::get(termItem, "isCheckExecCondition");
        otItem["isCheckConfirmCondition"] = ZGUtils::get(termItem, "isCheckConfirmCondition");
        otItem["isForbidSkip"] = ZGUtils::get(termItem, "isForbidSkip");
        otItem["isOperExec"] = ZGUtils::get(termItem, "isOperExec");
        otItem["isMonExec"] = ZGUtils::get(termItem, "isMonExec");
        otItem["isAutoExec"] = ZGUtils::get(termItem, "isAutoExec");
        otItem["lockConfirmCondition"] = ZGUtils::get(termItem, "lockConfirmCondition");
        otItem["startDelay"] = ZGUtils::get(termItem, "startDelay");
        otItem["confirmDelay"] = ZGUtils::get(termItem, "confirmDelay");
        otItem["endDelay"] = ZGUtils::get(termItem, "endDelay");
        otItem["timeout"] = ZGUtils::get(termItem, "timeout");
        otItem["itemIndex"] = std::to_string(itemIndex);
        ++itemIndex;
        const auto &deviceSubtypeID = ZGUtils::get(term, "deviceSubtypeID");
        const auto &templateItemID = ZGUtils::get(templateItem, "id");
        std::string deviceTag;
        if (!deviceSubtypeID.empty())
        {
            if (!getDeviceTag(templateItemID, obj, deviceSubtypeID, deviceTag, e))
                return false;
            ZG6000::StringMap device;
            if (!getDeviceByTemplateParam(templateItemID, appNodeID, deviceSubtypeID, deviceTag, device, e))
                return false;
            otItem["deviceID"] = ZGUtils::get(device, "id");
            const auto& lockTag = ZGUtils::get(termItem, "lockTag");
            if (!lockTag.empty())
            {
                std::string lockID;
                if (!getLockIDByTag(otItem["deviceID"], lockTag, lockID, e))
                    return false;
                otItem["lockID"] = lockID;
            }
        }
        const auto &termItemName = ZGUtils::get(termItem, "name");
        const auto &termItemVoice = ZGUtils::get(termItem, "voice");
        std::string otItemName;
        std::string otItemVoice;
        const auto &listVarible = ZGUtils::extractVariables(termItemName);
        otItemName = termItemName;
        otItemVoice = termItemVoice;
        if (!listVarible.empty())
        {
            for (const auto &varible : listVarible)
            {
                if (varible == "appNode")
                {
                    const auto &appNode = ZG6000::ZGOPTaskOTMng::instance()->getAppNode(appNodeID);
                    const auto &name = ZGUtils::get(appNode, "name", "");
                    const auto &voice = ZGUtils::get(appNode, "voice", "");
                    ZGUtils::replaceString(otItemName, "[" + varible + "]", name);
                    ZGUtils::replaceString(otItemVoice, "[" + varible + "]", voice);
                }
                else
                {
                    std::string varDeviceTag;
                    if (!getDeviceTag(templateItemID, obj, varible, varDeviceTag, e))
                        return false;
                    ZG6000::StringMap varDevice;
                    if (!getDeviceByTemplateParam(templateItemID, appNodeID, varible, varDeviceTag, varDevice, e))
                        return false;
                    const auto &deviceName = ZGUtils::get(varDevice, "name");
                    const auto &deviceVoice = ZGUtils::get(varDevice, "voice");
                    ZGUtils::replaceString(otItemName, "[" + varible + "]", deviceName);
                    ZGUtils::replaceString(otItemVoice, "[" + varible + "]", deviceVoice);
                }
            }
        }
        otItem["name"] = otItemName;
        otItem["voice"] = otItemVoice;
        otItem["rtStateID"] = "ZG_OIS_READY";
    }
    catch (const std::exception &ex)
    {
        ZGLOG_ERROR(ex.what());
        return false;
    }
    return true;
}

bool ZGOPTaskOTTicket::getDeviceByTemplateParam(const std::string &templateItemID, const std::string &appNodeID,
                                                const std::string &subtypeID, const std::string &deviceTag,
                                                ZG6000::StringMap &device, ZG6000::ErrorInfo &e)
{
    QString sql = QString("SELECT id, name, voice FROM mp_param_device WHERE appNodeID = '%1' AND subtypeID = '%2' AND "
                          "deviceTag = '%3'")
                      .arg(appNodeID.c_str())
                      .arg(subtypeID.c_str())
                      .arg(deviceTag.c_str());
    ZG6000::ListStringMap listDevice;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevice))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("在实例化操作票模板项'%1'时，获取设备失败，应用节点'%2', 子类型'%3', 设备标识'%4'")
                          .arg(templateItemID.c_str())
                          .arg(appNodeID.c_str())
                          .arg(subtypeID.c_str())
                          .arg(deviceTag.c_str())
                          .toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listDevice.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("在实例化操作票模板项'%1'时，找不到应用节点'%2'下子类型为'%3'设备标识为'%4'的设备")
                          .arg(templateItemID.c_str())
                          .arg(appNodeID.c_str())
                          .arg(subtypeID.c_str())
                          .arg(deviceTag.c_str())
                          .toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listDevice.size() > 1)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("在应用节点'%1'下找到的子类型为'%2'设备标识为'%3'的设备不唯一")
                          .arg(appNodeID.c_str())
                          .arg(subtypeID.c_str())
                          .arg(deviceTag.c_str())
                          .toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    device = std::move(listDevice[0]);
    return true;
}

bool ZGOPTaskOTTicket::getDeviceTag(const std::string &templateItemID, const QJsonObject &obj,
                                    const std::string &subtypeID, std::string &deviceTag, ZG6000::ErrorInfo &e)
{
    auto it = obj.find(subtypeID.c_str());
    if (it == obj.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail =
            QStringLiteral("在操作票模板项'%1'中找不到'%2'类型的设备标识，该类型在模板项关联的操作术语的项中存在")
                .arg(templateItemID.c_str())
                .arg(subtypeID.c_str())
                .toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    deviceTag = it.value().toString().toStdString();
    return true;
}

bool ZGOPTaskOTTicket::getLockIDByTag(const std::string& deviceID, const std::string& lockTag, std::string& lockID, ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT id FROM mp_param_lock WHERE deviceID = '%1' AND tag = '%2'").arg(deviceID.c_str()).arg(lockTag.c_str());
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取设备'%1'下标识为'%2'的锁具失败").arg(deviceID.c_str()).arg(lockTag.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("设备'%1'下找不到标识为'%2'的锁具").arg(deviceID.c_str()).arg(lockTag.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listID.size() > 1)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskOT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("设备'%1'下标识为'%2'的锁具不唯一").arg(deviceID.c_str()).arg(lockTag.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    lockID = listID[0];
    return true;
}

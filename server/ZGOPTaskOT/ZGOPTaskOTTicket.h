#ifndef ZGOPTASKOTTICKET_H
#define ZGOPTASKOTTICKET_H

#include <QObject>
#include <QJsonObject>
#include "ZGProxyCommon.h"
#include "ZGUtils.h"
#include "ZGFSM.hpp"

class ZGOPTaskOTItem;
class ZGOPTaskOTTicket : public QObject
{
	Q_OBJECT
public:
	explicit ZGOPTaskOTTicket(std::string id, QObject* parent = nullptr);
    bool initialize();
	void onTimer();
	bool command(const std::string& action, ZG6000::StringMap args, std::string& errMsg);
    bool previewCommand(const std::string& action, ZG6000::StringMap args, std::string& errMsg);
	bool getCurrentState(std::string& state);
	bool setCurrentState(std::string state);
	bool getCurrentStage(std::string& stage);
	bool setCurrentStage(std::string stage);
	bool getPreviewState(std::string& state);
	bool setPreviewState(std::string state);
    bool getCurrentItem(std::string& itemID);
    bool getPreviewItem(std::string& itemID);
	bool getOperUser(std::string& userID);
	bool getMonUser(std::string& userID);
	bool isPreview(bool& preview);
	void dispatchData(const std::string& tableName, const ZG6000::MapField& record);
	static bool createTicket(std::string ticketTypeID, ZG6000::StringMap param, std::string& ticketID, ZG6000::ErrorInfo& e);
    bool deleteTicket(ZG6000::ErrorInfo& e);
    bool editTicket(std::string otID, ZG6000::StringMap head, ZG6000::ListStringMap items, ZG6000::ErrorInfo& e);
    bool start(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
	bool stop(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool pause(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool resume(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool retry(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool skip(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool abolish(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool confirm(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
	bool convert(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool previewStart(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool previewStop(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool previewPause(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool previewResume(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool previewRetry(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool previewConfirm(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
	bool createItem(ZG6000::StringMap param, ZG6000::ListStringMap& items, ZG6000::ErrorInfo& e);
	bool deleteItem(ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    bool nextItem();
    bool nextPreviewItem();

private:
	static bool createTemplateTicket(ZG6000::StringMap param, std::string& ticketId, ZG6000::ErrorInfo& e);
    static bool createTypicalTicket(ZG6000::StringMap param, std::string& ticketId, ZG6000::ErrorInfo& e);
	static bool createTemporaryTicket(const std::string& ticketTypeID, ZG6000::StringMap param, std::string& ticketId, ZG6000::ErrorInfo& e);
	static bool checkRequiredParam(const ZG6000::StringMap& param, const ZG6000::StringList& required, QString& errMsg);
    static ZG6000::StringMap generateTemplateTaskHead(const std::string& taskID, const ZG6000::StringMap& srcParam);
    static ZG6000::StringMap generateTypicalTaskHead(const std::string& taskID, const ZG6000::StringMap& srcParam);
    ZG6000::StringMap generateInitTaskHead() const;
    static ZG6000::StringMap generateFixedTaskHead(const std::string& taskID, const std::string &stageID, const std::string &stateID, const ZG6000::StringMap& srcParam);
    static ZG6000::StringMap generateOtHeadParam(const std::string& ticketID, const std::string& typeID, const ZG6000::StringMap& srcParam);
    static bool generateOtItemParam(const std::string& ticketId, const ZG6000::StringMap &term, const ZG6000::StringMap& termItem, const std::string& appNodeID, const ZG6000::StringMap &templateItem,
                                    const QJsonObject& obj, ZG6000::StringMap& otItem, int& itemIndex, ZG6000::ErrorInfo& e);
    static bool getDeviceByTemplateParam(const std::string &templateItemID, const std::string& appNodeID, const std::string& subtypeID, const std::string& deviceTag, ZG6000::StringMap &device, ZG6000::ErrorInfo& e);
    static bool getDeviceTag(const std::string& templateItemID, const QJsonObject& obj, const std::string& subtypeID, std::string& deviceTag, ZG6000::ErrorInfo& e);
    static bool getLockIDByTag(const std::string& deviceID, const std::string& lockTag, std::string& lockID, ZG6000::ErrorInfo& e);
    static void checkAndCopyField(ZG6000::StringMap& dstParam, const ZG6000::StringMap& srcParam, const std::string& field)
	{
		if (srcParam.find(field) != srcParam.end())
			dstParam[field] = ZGUtils::get(srcParam, field);
        else
            dstParam[field] = "";
	}
    static bool checkOtUnique(const std::string& appNodeID, ZG6000::ErrorInfo& e);
    static bool checkTypeOtUnique(const std::string& appNodeID, const std::string &subsystemID,
                                   const std::string &majorID, const std::string &templateID, ZG6000::ErrorInfo& e);
    static bool checkAppNodeTermExecCondition(const std::string& appNodeID, const std::string& termID);
    static std::string generateNumber(const std::string &rule, int otIndex);
	bool doAction(std::string action, ZG6000::StringMap param, ZG6000::ErrorInfo& e);
	bool doPreviewAction(std::string action, ZG6000::StringMap param, ZG6000::ErrorInfo& e);

    bool initExecuteContext();
    bool initPreviewContext();
	void registerAction();
	void registerPreviewAction();
	bool onCreateConfirm(ZG6000::StringMap args, std::string& errMsg);
	bool onExamConfirm(ZG6000::StringMap args, std::string& errMsg);
	bool onExecuteStart(ZG6000::StringMap args, std::string& errMsg);
	bool onExecutePause(ZG6000::StringMap args, std::string& errMsg);
	bool onExecuteResume(ZG6000::StringMap args, std::string& errMsg);
	bool onExecuteRetry(ZG6000::StringMap args, std::string& errMsg);
	bool onExecuteConfirm(ZG6000::StringMap args, std::string& errMsg);
    bool onExecuteSkip(ZG6000::StringMap args, std::string& errMsg);
	bool onExecuteNextStep(ZG6000::StringMap args, std::string& errMsg);
	bool onExecuteItemTimeout(ZG6000::StringMap args, std::string& errMsg);
    bool onExecuteItemError(ZG6000::StringMap args, std::string& errMsg);
	bool onStoreFinish(ZG6000::StringMap args, std::string& errMsg);
	bool onAbolish(ZG6000::StringMap args, std::string& errMsg);
	bool onPreviewStart(ZG6000::StringMap args, std::string& errMsg);
	bool onPreviewStop(ZG6000::StringMap args, std::string& errMsg);
	bool onPreviewPause(ZG6000::StringMap args, std::string& errMsg);
	bool onPreviewResume(ZG6000::StringMap args, std::string& errMsg);
	bool onPreviewRetry(ZG6000::StringMap args, std::string& errMsg);
	bool onPreviewConfirm(ZG6000::StringMap args, std::string& errMsg);
	bool onPreviewNextStep(ZG6000::StringMap args, std::string& errMsg);
	bool onPreviewItemTimeout(ZG6000::StringMap args, std::string& errMsg);
	bool onPreviewItemError(ZG6000::StringMap args, std::string& errMsg);
	void processTaskChange(const ZG6000::MapField& record);
	void processOtChange(const ZG6000::MapField& record);
	void processItemChange(const ZG6000::MapField& record);
    void processExamChange(const ZG6000::MapField& record);
    bool updateInitFinish(ZG6000::StringMap args, std::string& errMsg);
    bool updateExecuteReady(ZG6000::StringMap args, std::string& errMsg);
    bool updatePreviewReady(ZG6000::StringMap args, std::string& errMsg);
	bool createExam(ZG6000::StringMap args, std::string& errMsg);
	bool finishExam(ZG6000::StringMap args, std::string& errMsg);
    bool achiveOt(std::string& errMsg);
	bool saveOt(std::string& errMsg);
    bool getItemAutoExecState(const std::string& itemID, bool& autoExec);
    bool clearItemFlag(std::string& errMsg);
    bool deleteExam(ZG6000::ErrorInfo& e);
    bool checkTicketValid(std::string& errMsg);


private:
    class State {
    public:
        State() = default;
        State(std::string stage, std::string state): stage_(std::move(stage)), state_(std::move(state)) {}
        std::string to_string() const {
            return stage_ + ":" + state_;
        }
        bool operator<(const State& rhs) const {
            return stage_ < rhs.stage_ || (stage_ == rhs.stage_ && state_ < rhs.state_);
        }
        bool operator==(const State& rhs) const {
            return stage_ == rhs.stage_ && state_ == rhs.state_;
        }
        std::string stage_;
        std::string state_;
    };
    ZGFSM<State, std::string> fsm;
	ZGFSM<std::string, std::string> fsm_preview;
    QMutex m_mutex;
	std::string m_id;
	ZGOPTaskOTItem* m_pCurrentItem{nullptr};
	ZGOPTaskOTItem* m_pCurrentPreviewItem{ nullptr };
	std::vector<std::pair<std::string, std::string>> m_listPairState{ {"ZG_TS_READY", "ZG_ES_READY"}, {"ZG_TS_EXECUTING", "ZG_ES_EXAM"},
		{"ZG_TS_FINISHED", "ZG_ES_ACCEPT"}, {"ZG_TS_STOPPED", "ZG_ES_REJECT"} };
	ZG6000::StringMap m_mapExamState{ {"ZG_TS_READY", "ZG_ES_READY"}, {"ZG_TS_EXECUTING", "ZG_ES_EXAM"},
		{"ZG_TS_FINISHED", "ZG_ES_ACCEPT"}, {"ZG_TS_STOPPED", "ZG_ES_REJECT"}};
};

#endif // ZGOPTASKOTTICKET_H

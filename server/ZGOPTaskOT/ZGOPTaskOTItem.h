#ifndef ZGOPTASKOTITEM_H
#define ZGOPTASKOTITEM_H

#include <QObject>
#include <QReadWriteLock>
#include <ZGProxyCommon.h>
#include "ZGOPTaskOTTicket.h"
#include "ZGFSM.hpp"

class ZGOPTaskOTTicket;
class ZGOPTaskOTItem : public QObject
{
    Q_OBJECT
public:
    enum ExecuteState
    {
        esWait, esVerify, esExecute, esConfirm
    };
    explicit ZGOPTaskOTItem(QObject *parent = nullptr);
    void initialize();
    virtual bool getCurrentState(const ZG6000::StringMap& item, std::string& state);
    virtual bool setCurrentState(const std::string& itemID, const std::string& state);
    void onTimer(const std::string& itemID);

protected:
    virtual bool checkItemCondition(const ZG6000::StringMap& otItem);
    virtual std::string getExecTime(const ZG6000::StringMap& otItem);
    virtual void processEmptyState(const QDateTime& execTime, ZG6000::StringMap otItem);
    virtual void processReadyState(const QDateTime& execTime, ZG6000::StringMap otItem);
    virtual void processWaitState(const QDateTime& execTime, ZG6000::StringMap otItem);
    virtual void processVerifyState(const QDateTime& execTime, ZG6000::StringMap otItem);
    virtual void processExecuteState(const QDateTime& execTime, ZG6000::StringMap otItem);
    virtual void processConfirmWaitState(const QDateTime& execTime, ZG6000::StringMap otItem);
    virtual void processConfirmState(const QDateTime& execTime, ZG6000::StringMap otItem);
    bool checkUpdateTimeValid(const ZG6000::StringMap::mapped_type& updateTime);
    virtual void processTourState(const QDateTime& execTime, ZG6000::StringMap otItem);
    virtual void processFinishState(const QDateTime& execTime, ZG6000::StringMap otItem);
    virtual bool nextItem();
    virtual bool notify(const std::string &action, ZG6000::StringMap args, std::string &errMsg);
    virtual int getSimFlag();
    bool getAvailableTourUsers(const ZG6000::StringMap& otItem, ZG6000::StringMap& tourUsers, std::string& errMsg);
    void notifyError(const std::string& itemID, const std::string& message);
    static bool checkUserCard(const std::string& deviceID, const std::string& userID, bool& pass, std::string& errMsg);
    static bool checkUserID(const std::string& deviceID, const std::string& userID, bool& pass, std::string& errMsg);
    static bool getUserIDFromCard(const std::string& cardID, std::string& userID, std::string& errMsg);
    

protected:
    ZGFSM<std::string, std::string> fsm;
    ZGOPTaskOTTicket* m_pTicket{nullptr};
    QDateTime m_confirmDateTime;
    int m_confirmWaitCount{0};
    QReadWriteLock m_lock;
};

#endif // ZGOPTASKOTITEM_H

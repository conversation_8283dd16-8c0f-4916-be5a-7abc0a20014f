#ifndef ZGOPTASKOTPREVIEWITEM_H
#define ZGOPTASKOTPREVIEWITEM_H

#include "ZGOPTaskOTItem.h"

class ZGOPTaskOTPreviewItem : public ZGOPTaskOTItem
{
public:
	explicit ZGOPTaskOTPreviewItem(QObject* parent = nullptr);
	bool getCurrentState(const ZG6000::StringMap& item, std::string& state) override;
	bool setCurrentState(const std::string& itemID, const std::string& state) override;

protected:
	bool checkItemCondition(const ZG6000::StringMap& otItem) override;
	std::string getExecTime(const ZG6000::StringMap& otItem) override;
	void processVerifyState(const QDateTime& execTime, ZG6000::StringMap otItem) override;
	void processExecuteState(const QDateTime& execTime, ZG6000::StringMap otItem) override;
	void processConfirmState(const QDateTime& execTime, ZG6000::StringMap otItem) override;
	void processFinishState(const QDateTime& execTime, ZG6000::StringMap otItem) override;
    bool notify(const std::string &action, ZG6000::StringMap args, std::string &errMsg) override;
	bool nextItem() override;
    int getSimFlag() override;
};

#endif // ZGOPTASKOTPREVIEWITEM_H

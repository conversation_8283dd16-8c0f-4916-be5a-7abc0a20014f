#include "ZGOPTaskOTI.h"
#include "ZGOPTaskOTMng.h"

namespace ZG6000 {

ZGOPTaskOTI::ZGOPTaskOTI()
{
	ZGOPTaskOTMng::instance()->init();
}

bool ZGOPTaskOTI::checkState(const Ice::Current &current)
{
	return ZGOPTaskOTMng::instance()->checkState();
}

bool ZGOPTaskOTI::createOT(std::string taskTypeID, StringMap param, std::string& otID, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->createOT(std::move(taskTypeID), std::move(param), otID, e);
}

bool ZGOPTaskOTI::deleteTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
	return ZGOPTaskOTMng::instance()->deleteTask(std::move(taskID), std::move(param), e);
}

bool ZGOPTaskOTI::startTask(std::string taskID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->startTask(std::move(taskID), std::move(param), e);
}

bool ZGOPTaskOTI::editOT(std::string otID, StringMap head, ListStringMap items, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->editOT(std::move(otID), std::move(head), std::move(items), e);
}

bool ZGOPTaskOTI::pauseTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
	return ZGOPTaskOTMng::instance()->pauseTask(std::move(taskID), std::move(param), e);
}

bool ZGOPTaskOTI::resumeTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
	return ZGOPTaskOTMng::instance()->resumeTask(std::move(taskID), std::move(param), e);
}

bool ZGOPTaskOTI::retryTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
	return ZGOPTaskOTMng::instance()->retryTask(std::move(taskID), std::move(param), e);
}

bool ZGOPTaskOTI::abolishTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
	return ZGOPTaskOTMng::instance()->abolishTask(std::move(taskID), std::move(param), e);
}

bool ZGOPTaskOTI::getTaskList(StringMap param, ListStringMap& listTask, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->getTaskList(std::move(param), listTask, e);
}

bool ZGOPTaskOTI::confirmTask(std::string taskID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->confirmTask(std::move(taskID), std::move(param), e);
}

bool ZGOPTaskOTI::getOT(std::string otID, StringMap& otHead, ListStringMap& otItems, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->getOT(std::move(otID), otHead, otItems, e);
}

bool ZGOPTaskOTI::skipItem(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->skipItem(std::move(otID), std::move(param), e);
}

bool ZGOPTaskOTI::startPreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->startPreview(std::move(otID), std::move(param), e);
}

bool ZGOPTaskOTI::stopPreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->stopPreview(std::move(otID), std::move(param), e);
}

bool ZGOPTaskOTI::pausePreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->pausePreview(std::move(otID), std::move(param), e);
}

bool ZGOPTaskOTI::resumePreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->resumePreview(std::move(otID), std::move(param), e);
}

bool ZGOPTaskOTI::retryPreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->retryPreview(std::move(otID), std::move(param), e);
}

bool ZGOPTaskOTI::confirmPreview(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->confirmPreview(std::move(otID), std::move(param), e);
}

bool ZGOPTaskOTI::convertOT(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->convertOT(std::move(otID), std::move(param), e);
}

bool ZGOPTaskOTI::createOtItem(std::string otID, StringMap param, ListStringMap& items, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->createOtItem(std::move(otID), std::move(param), items, e);
}

bool ZGOPTaskOTI::getDeviceTerm(std::string deviceID, StringMap param, ListStringMap& terms, ErrorInfo& e, const Ice::Current& current)
{
    return false;
}

bool ZGOPTaskOTI::getCommonTerm(StringMap param, ListStringMap& terms, ErrorInfo& e, const Ice::Current& current)
{
    return false;
}

bool ZGOPTaskOTI::deleteOtItem(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskOTMng::instance()->deleteOtItem(std::move(otID), std::move(param), e);
}

bool ZGOPTaskOTI::setOtSimulateValue(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
    return false;
}

bool ZGOPTaskOTI::clearOtSimulateValue(std::string otID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
    return false;
}

bool ZGOPTaskOTI::downloadTask(std::string clientID, StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPTaskOTMng::instance()->downloadTask(std::move(clientID), std::move(listTaskID), listTask, listItem, e);
}

bool ZGOPTaskOTI::updateTask(ListStringMap listTask, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskOTMng::instance()->updateTask(std::move(listTask), e);
}

bool ZGOPTaskOTI::updateItem(ListStringMap listItem, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskOTMng::instance()->updateItem(std::move(listItem), e);
}
} // namespace ZG6000

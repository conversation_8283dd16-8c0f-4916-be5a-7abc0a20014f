#include "ZGHMIWebModule.h"
#include "ZGHMIHandle.h"
#include "ZGProxyCommon.h"
#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "ZGJson.h"
#include <QtConcurrent>

ZGHMIWebModule::ZGHMIWebModule(QObject *parent)
    : ZGWebModule(parent)
{
    ZGHMIHandle* pZGHMIHandle = new ZGHMIHandle(this);
    registerHandle("hmi/cellcontent/get", pZGHMIHandle, &ZGHMIHandle::on_hmi_cellcontent_get);
    registerHandle("hmi/cell/create", pZGHMIHandle, &ZGHMIHandle::on_hmi_cell_create);
    registerHandle("hmi/cell/delete", pZGHMIHandle, &ZGHMIHandle::on_hmi_cell_delete);
    registerHandle("hmi/cell/update", pZGHMIHandle, &ZGHMIHandle::on_hmi_cell_update);
    registerHandle("hmi/itemcontent/get", pZGHMIHandle, &ZGHMIHandle::on_hmi_itemcontent_get);
    registerHandle("hmi/item/create", pZGHMIHandle, &ZGHMIHandle::on_hmi_item_create);
    registerHandle("hmi/item/update", pZGHMIHandle, &ZGHMIHandle::on_hmi_item_update);
    registerHandle("hmi/item/delete", pZGHMIHandle, &ZGHMIHandle::on_hmi_item_delete);
    registerHandle("hmi/page/get", pZGHMIHandle, &ZGHMIHandle::on_hmi_page_get);
    registerHandle("hmi/page/logical", pZGHMIHandle, &ZGHMIHandle::on_hmi_page_logical);
    registerHandle("hmi/page/index/update", pZGHMIHandle, &ZGHMIHandle::on_hmi_page_index_update);
    registerHandle("hmi/pagecontent/get", pZGHMIHandle, &ZGHMIHandle::on_hmi_pagecontent_get);
    registerHandle("hmi/page/create", pZGHMIHandle, &ZGHMIHandle::on_hmi_page_create);
    registerHandle("hmi/page/update", pZGHMIHandle, &ZGHMIHandle::on_hmi_page_update);
    registerHandle("hmi/page/delete", pZGHMIHandle, &ZGHMIHandle::on_hmi_page_delete);
    registerHandle("hmi/page/major/get", pZGHMIHandle, &ZGHMIHandle::on_hmi_page_major_get);
    registerHandle("hmi/page/major/update", pZGHMIHandle, &ZGHMIHandle::on_hmi_page_major_update);
    registerHandle("hmi/jscontent/get", pZGHMIHandle, &ZGHMIHandle::on_hmi_jscontent_get);
    registerHandle("hmi/js/create", pZGHMIHandle, &ZGHMIHandle::on_hmi_js_create);
    registerHandle("hmi/js/update", pZGHMIHandle, &ZGHMIHandle::on_hmi_js_update);
    registerHandle("hmi/js/delete", pZGHMIHandle, &ZGHMIHandle::on_hmi_js_delete);
    registerHandle("hmi/csstype/get", pZGHMIHandle, &ZGHMIHandle::on_hmi_csstype_get);
    registerHandle("hmi/csscontent/get", pZGHMIHandle, &ZGHMIHandle::on_hmi_csscontent_get);
    registerHandle("hmi/css/create", pZGHMIHandle, &ZGHMIHandle::on_hmi_css_create);
    registerHandle("hmi/css/update", pZGHMIHandle, &ZGHMIHandle::on_hmi_css_update);
    registerHandle("hmi/css/delete", pZGHMIHandle, &ZGHMIHandle::on_hmi_css_delete);
    registerHandle("hmi/major/get", pZGHMIHandle, &ZGHMIHandle::on_hmi_major_get);
}

bool ZGHMIWebModule::initialize()
{
    return true;
}

QString ZGHMIWebModule::prefix()
{
    return "hmi";
}

bool ZGHMIWebModule::fetchFileFromDatabase(const QString& fileContentID, QString &content)
{
    std::string sql = "SELECT content FROM hmi_param_file_content WHERE id = '" + fileContentID.toStdString() + "'";
    std::string result;
    if (!ZGProxyCommon::execQuerySqlField(sql, result))
    {
        ZGLOG_ERROR(QString("Can't find file content by id %1").arg(fileContentID));
        return false;
    }
    content = result.c_str();
    return true;
}

bool ZGHMIWebModule::removeFile(const QString& fileContentID, std::string& errMsg)
{
    std::string sql = "DELETE FROM hmi_param_file_content WHERE id = '" + fileContentID.toStdString() + "'";
    if (!ZGProxyCommon::execSql(sql))
    {
        errMsg = "exec query error.";
        return false;
    }
    QString fileName = ZGPubFun::getRootDir() + "/web/cache/" + fileContentID;
    QFile::remove(fileName);
    return true;
}

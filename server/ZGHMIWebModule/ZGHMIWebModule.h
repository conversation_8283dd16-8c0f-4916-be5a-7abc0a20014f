#ifndef ZGHMIWEBMODULE_H
#define ZGHMIWEBMODULE_H

#include "ZGWebModule.h"

class ZGRedisClient;
class ZGHMIWebModule : public ZGWebModule
{
    Q_OBJECT
    Q_PLUGIN_METADATA(IID ZGWebModule_iid FILE "ZGHMIWebModule.json")
    Q_INTERFACES(ZGWebModule)

public:
    explicit ZGHMIWebModule(QObject *parent = nullptr);

    // ZGWebModule interface
public:
    bool initialize() override;
    QString prefix() override;
    static bool fetchFileFromDatabase(const QString &fileContentID, QString& content);
    static bool removeFile(const QString& fileContentID, std::string& errMsg);

private:
    ZGRedisClient* m_pRedisClient{nullptr};
};

#endif // ZGHMIWEBMODULE_H

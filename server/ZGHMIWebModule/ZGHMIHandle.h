#ifndef ZGHMIHANDLE_H
#define ZGHMIHANDLE_H

#include "ZGWebModule.h"

class ZGHMIHandle : public QObject
{
    Q_OBJECT
public:
    explicit ZGHMIHandle(QObject *parent = nullptr);

public:
    ZGWebModule::Response on_hmi_cellcontent_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_cell_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_cell_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_cell_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_itemcontent_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_item_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_item_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_item_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_page_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_page_logical(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_page_index_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_pagecontent_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_page_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_page_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_page_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_page_major_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_page_major_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_jscontent_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_js_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_js_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_js_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_csstype_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_csscontent_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_css_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_css_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_css_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_hmi_major_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);

private:
    struct ForeignField
    {
        QString table;
        QString field;
        QString name;
    };
    static ZGWebModule::Response getFileContent(const QJsonValue& param, const QMap<QString, ForeignField>& mapField, const std::string tableName);
    ZGWebModule::Response createFile(const QJsonValue& param, const std::string tableName);
    ZGWebModule::Response updateFile(const QJsonValue& param, const std::string tableName);
    ZGWebModule::Response removeFile(const QJsonValue& param, const std::string tableName);
};

#endif // ZGHMIHANDLE_H

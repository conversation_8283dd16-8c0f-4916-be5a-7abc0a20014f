#include "ZGHMIHandle.h"
#include "ZGHMIWebModule.h"
#include "ZGJson.h"
#include "ZGPubFun.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"
#include <QFile>
#include <QJsonDocument>
#include <QJsonArray>

ZGHMIHandle::ZGHMIHandle(QObject* parent) : QObject(parent)
{
}

ZGWebModule::Response ZGHMIHandle::on_hmi_cellcontent_get(const QString& /*clientID*/,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& /*req*/)
{
    QMap<QString, ForeignField> mapField;
    mapField.insert({
        {"cellTypeID", {"hmi_dict_cell_type", "name", "cellTypeName"}},
        {"fileContentID", {"hmi_param_file_content", "content", "content"}}
    });
    return getFileContent(param, mapField, "hmi_param_cell");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_cell_create(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return createFile(param, "hmi_param_cell");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_cell_delete(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& /*req*/)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return removeFile(param, "hmi_param_cell");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_cell_update(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& /*req*/)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return updateFile(param, "hmi_param_cell");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_item_update(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& /*req*/)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return updateFile(param, "hmi_param_item");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_item_delete(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& /*req*/)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return removeFile(param, "hmi_param_item");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_itemcontent_get(const QString& /*clientID*/,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& /*req*/)
{
    QMap<QString, ForeignField> mapField;
    mapField.insert({
        {"itemTypeID", {"hmi_dict_item_type", "name", "itemTypeName"}},
        {"itemSubtypeID", {"hmi_dict_item_subtype", "name", "itemSubtypeName"}},
        {"fileContentID", {"hmi_param_file_content", "content", "content"}}
    });
    return getFileContent(param, mapField, "hmi_param_item");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_item_create(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return createFile(param, "hmi_param_item");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_pagecontent_get(const QString& /*clientID*/,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& /*req*/)
{
    QMap<QString, ForeignField> mapField;
    mapField.insert({
        {"pageTypeID", {"hmi_dict_page_type", "name", "pageTypeName"}},
        {"fileContentID", {"hmi_param_file_content", "content", "content"}}
    });
    return getFileContent(param, mapField, "hmi_param_page");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_jscontent_get(const QString& /*clientID*/,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& /*req*/)
{
    QMap<QString, ForeignField> mapField;
    mapField.insert({
        {"jsTypeID", {"hmi_dict_js_type", "name", "jsTypeName"}},
        {"fileContentID", {"hmi_param_file_content", "content", "content"}}
    });
    return getFileContent(param, mapField, "hmi_param_js");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_js_create(const QString &clientID, const QVariantMap &headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return createFile(param, "hmi_param_js");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_csscontent_get(const QString& /*clientID*/,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& /*req*/)
{
    QMap<QString, ForeignField> mapField;
    mapField.insert({
        {"cssTypeID", {"hmi_dict_css_type", "name", "cssTypeName"}},
        {"fileContentID", {"hmi_param_file_content", "content", "content"}}
    });
    return getFileContent(param, mapField, "hmi_param_css");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_css_create(const QString &clientID, const QVariantMap &headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return createFile(param, "hmi_param_css");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_css_update(const QString &clientID, const QVariantMap &headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return updateFile(param, "hmi_param_css");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_page_create(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& /*req*/)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& obj = param.toObject();
    if (!ZGWebModule::checkRequiredFields(obj, {"appNode", "pageID", "pageTypeID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    ZG6000::StringList listSql;
    std::string fileID, fileContentID;
    if (!ZGProxyCommon::createUUID(fileID))
        return ZGWebModule::errorObject(QStringLiteral("创建文件ID失败."));
    if (!ZGProxyCommon::createUUID(fileContentID))
        return ZGWebModule::errorObject(QStringLiteral("创建文件内容ID失败."));
    QString fields = "id,fileContentID,";
    QString values = QString("'%1','%2',").arg(fileID.c_str()).arg(fileContentID.c_str());
    auto it = obj.begin();
    while (it != obj.end())
    {
        if (it.key() != "appNode")
        {
            fields += it.key() + ",";
            values += QString("'%1',").arg(it.value().toString());
        }
        ++it;
    }
    fields.chop(1);
    values.chop(1);
    QString sql = QString("INSERT INTO hmi_param_page (%1) VALUES (%2);").arg(fields).arg(values);
    listSql.push_back(sql.toStdString());
    sql = QString("INSERT INTO hmi_param_file_content (id) VALUES (%1);").arg(fileContentID.c_str());
    listSql.push_back(sql.toStdString());
    std::string appNodeID, subsystemID, majorID;
    fields = "id,appnodeID,subsystemID,majorID,pageID";
    const auto& appNodeObj = obj["appNode"].toObject();
    auto itNode = appNodeObj.begin();
    while (itNode != appNodeObj.end())
    {
        appNodeID = itNode.key().toStdString();
        const auto& subSystemObj = itNode.value().toObject();
        std::string id;
        if (!ZGProxyCommon::createUUID(id))
            return ZGWebModule::errorObject(QStringLiteral("创建应用节点页面ID失败."));
        if (subSystemObj.empty())
        {
            values = QString("'%1','%2','%3','%4','%5'").arg(id.c_str()).arg(appNodeID.c_str()).arg("").arg("").arg(
                fileID.c_str());
            sql = QString("INSERT INTO hmi_param_appnode_page (%1) VALUES (%2);").arg(fields).arg(values);
            listSql.push_back(sql.toStdString());
        }
        else
        {
            auto itSubsystem = subSystemObj.begin();
            while (itSubsystem != subSystemObj.end())
            {
                subsystemID = itSubsystem.key().toStdString();
                const auto& majorArray = itSubsystem.value().toArray();
                if (majorArray.empty())
                {
                    values = QString("'%1','%2','%3','%4','%5'").arg(id.c_str()).arg(appNodeID.c_str()).
                                                                 arg(subsystemID.c_str()).arg("").arg(fileID.c_str());
                    sql = QString("INSERT INTO hmi_param_appnode_page (%1) VALUES (%2);").arg(fields).arg(values);
                    listSql.push_back(sql.toStdString());
                }
                else
                {
                    for (auto majorRef : majorArray)
                    {
                        majorID = majorRef.toString().toStdString();
                        values = QString("'%1','%2','%3','%4','%5'").arg(id.c_str()).arg(appNodeID.c_str()).
                                                                     arg(subsystemID.c_str()).arg(majorID.c_str()).arg(
                                                                         fileID.c_str());
                        sql = QString("INSERT INTO hmi_param_appnode_page (%1) VALUES (%2);").arg(fields).arg(values);
                        listSql.push_back(sql.toStdString());
                    }
                }
                ++itSubsystem;
            }
        }
        ++itNode;
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("创建页面失败!"));
    return ZGWebModule::replyObject(fileID.c_str());
}

ZGWebModule::Response ZGHMIHandle::on_hmi_page_get(const QString& /*clientID*/,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& /*req*/)
{
    std::string typeId = param.toString().toStdString();
    const auto& object = param.toObject();
    QStringList lstCondition;
    const auto& pageTypeID = object.find("pageTypeID");
    if (pageTypeID != object.end())
        lstCondition.append("hmi_param_page.pageTypeID = '" + pageTypeID->toString() + "'");
    const auto& appnodeID = object.find("appnodeID");
    if (appnodeID != object.end())
        lstCondition.append("hmi_param_appnode_page.appnodeID = '" + appnodeID->toString() + "'");
    const auto& subsystemID = object.find("subsystemID");
    if (subsystemID != object.end())
        lstCondition.append("hmi_param_appnode_page.subsystemID = '" + subsystemID->toString() + "'");
    const auto& majorID = object.find("majorID");
    if (majorID != object.end())
        lstCondition.append("hmi_param_appnode_page.majorID = '" + majorID->toString() + "'");
    QString conditions = lstCondition.join(" AND ");
    std::string sql = "SELECT hmi_param_page.id AS id, hmi_param_page.name AS name, hmi_param_page.attr AS attr, "
        "hmi_param_page.topic AS topic FROM hmi_param_page LEFT JOIN hmi_param_appnode_page "
        "ON hmi_param_page.id = hmi_param_appnode_page.pageID WHERE " + conditions.toStdString() +
        " ORDER BY pageIndex";
    return ZGWebModule::replyStringMap(sql);
}

ZGWebModule::Response ZGHMIHandle::on_hmi_page_logical(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    QString pageID = param.toString();
    auto topyPrx = ZGProxyMng::instance()->getProxySPGraphicTopology();
    if (topyPrx == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取图形拓扑服务代理对象失败"));
    try
    {
        ZG6000::ListStringMap listItem;
        ZG6000::ErrorInfo e;
        if (!topyPrx->getHMIPageTopologyState(pageID.toStdString(), listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonDocument doc = QJsonDocument::fromJson(ZGJson::convertToJson(listItem).c_str());
        return ZGWebModule::replyObject(doc.array());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGHMIHandle::on_hmi_page_index_update(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& object = param.toObject();
    if (!ZGWebModule::checkRequiredFields(object, {"appNodeID", "subsystemID", "pageIndex"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& appNodeID = object["appNodeID"].toString();
    const auto& subsystemID = object["subsystemID"].toString();
    QString majorID;
    if (object.contains("majorID"))
        majorID = object["majorID"].toString();
    else
        majorID = "";
    const auto& pageArray = object["pageIndex"].toArray();
    ZG6000::StringList listSql;
    int index = 1;
    for (auto pageRef : pageArray)
    {
        const auto& pageID = pageRef.toString();
        QString sql = QString("UPDATE hmi_param_appnode_page SET pageIndex = %1 "
                          "WHERE appNodeID = '%2' AND subsystemID = '%3' AND majorID = '%4' AND pageID = '%5'")
                      .arg(index++).arg(appNodeID).arg(subsystemID).arg(majorID).arg(pageID);
        listSql.push_back(sql.toStdString());
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("更新应用节点页面索引失败"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_page_update(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& /*req*/)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return updateFile(param, "hmi_param_page");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_page_delete(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& /*req*/)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    QString fileID = param.toString();
    std::string sql = "SELECT fileContentID FROM hmi_param_page WHERE id = '" + fileID.toStdString() + "'";
    std::string fileContentId;
    if (!ZGProxyCommon::execQuerySqlField(sql, fileContentId))
        return ZGWebModule::errorObject("Get fileContentID error.");
    sql = "DELETE FROM hmi_param_page WHERE id = '" + fileID.toStdString() + "'";
    if (!ZGProxyCommon::execSql(sql))
        return ZGWebModule::errorObject("Delete page error.");
    sql = "DELETE FROM hmi_param_appnode_page WHERE pageID = '" + fileID.toStdString() + "'";
    if (!ZGProxyCommon::execSql(sql))
        return ZGWebModule::errorObject("Delete appnode page error.");
    sql = "DELETE FROM hmi_param_file_content WHERE id = '" + fileContentId + "'";
    if (!ZGProxyCommon::execSql(sql))
        return ZGWebModule::errorObject("Delete page content error.");
    std::string errorString;
    if (!ZGHMIWebModule::removeFile(fileContentId.c_str(), errorString))
        return ZGWebModule::errorObject(errorString.c_str());
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_page_major_get(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req)
{
    const auto& pageID = param.toString();
    QString sql = QString("SELECT a.appNodeID, b.name AS appNodeName, a.subsystemID, "
        "c.name AS subsystemName, a.majorID, d.name AS majorName, a.isMainPic FROM hmi_param_appnode_page a "
        "LEFT JOIN sp_param_appnode b ON a.appNodeID = b.id "
        "LEFT JOIN sp_param_subsystem c ON a.subsystemID = c.id "
        "LEFT JOIN sp_param_major d ON a.majorID = d.id "
        "WHERE a.pageID = '%1' ORDER BY a.appNodeID").arg(pageID);
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
        return ZGWebModule::errorObject(QStringLiteral("获取页面'%1'关联应用节点专业失败"));
    const auto& json = ZGJson::convertToJson(listRecord);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGHMIHandle::on_hmi_page_major_update(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& object = param.toObject();
    if (!ZGWebModule::checkRequiredFields(object, {"pageID", "params"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    QString pageID = object["pageID"].toString();
    ZG6000::StringList listSql;
    listSql.push_back(QString("DELETE FROM hmi_param_appnode_page WHERE pageID = '%1'").arg(pageID).toStdString());
    const auto& array = object["params"].toArray();
    ZG6000::StringList listID;
    if (!ZGProxyCommon::createUUID(array.size(), listID))
        return ZGWebModule::errorObject(QStringLiteral("创建UUID失败"));
    int index = 0;
    for (auto objRef : array)
    {
        const auto& obj = objRef.toObject();
        if (!ZGWebModule::checkRequiredFields(obj, {"appNodeID", "subsystemID"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        ZG6000::StringMap pageParam;
        pageParam["id"] = listID[index++];
        pageParam["pageID"] = pageID.toStdString();
        for (auto it = obj.begin(); it != obj.end(); ++it)
        {
            pageParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        listSql.push_back(ZGUtils::generateInsertSql("hmi_param_appnode_page", pageParam));
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("更新页面关联专业失败!"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_js_update(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& /*req*/)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return updateFile(param, "hmi_param_js");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_js_delete(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& /*req*/)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return removeFile(param, "hmi_param_js");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_csstype_get(const QString& /*clientID*/,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& /*req*/)
{
    std::string sql = "SELECT id,name FROM hmi_dict_css_type";
    return ZGWebModule::replyStringMap(sql);
}

ZGWebModule::Response ZGHMIHandle::on_hmi_css_delete(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& /*req*/)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return removeFile(param, "hmi_param_css");
}

ZGWebModule::Response ZGHMIHandle::on_hmi_major_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    std::string subsystemID = param.toString().toStdString();
    std::string sql = "SELECT a.majorID, b.name AS majorName, a.majorIndex FROM sp_param_client_major AS a "
        "LEFT JOIN sp_param_major AS b ON a.majorID = b.id "
        "WHERE a.clientID = '" + clientID.toStdString() + "' AND a.subsystemID = '" + subsystemID + "'";
    return ZGWebModule::replyStringMap(sql);
}

ZGWebModule::Response ZGHMIHandle::getFileContent(const QJsonValue& param,
                                                  const QMap<QString, ForeignField>& mapField,
                                                  const std::string tableName)
{
    QStringList lstForeignField, lstForeignJoin;
    for (auto it = mapField.begin(); it != mapField.end(); ++it)
    {
        lstForeignField.append(QString("%1.%2 AS %3").arg(it.value().table).arg(it.value().field).arg(it.value().name));
        lstForeignJoin.append(
            QString("LEFT JOIN %1 ON %2.%3=%1.id").arg(it.value().table).arg(tableName.c_str()).arg(it.key()));
    }
    QString foreignFields = lstForeignField.join(",");
    QString foreignJoin = lstForeignJoin.join(" ");
    QString id = param.toString();
    QString sql = QString("SELECT %1.*,%2 FROM %1 %3 WHERE %1.id = '%4'").arg(tableName.c_str()).arg(foreignFields).
                                                                          arg(foreignJoin).arg(id);
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
        return ZGWebModule::errorObject(QString("Get table %1 record %2 fail.").arg(tableName.c_str()).arg(id));
    if (listRecord.empty())
        return ZGWebModule::errorObject(QStringLiteral("无法找到'%'对应的记录"));
    QJsonObject obj;
    for (const auto& pair : listRecord[0])
    {
        obj[pair.first.c_str()] = pair.second.c_str();
    }
    return ZGWebModule::replyObject(obj);
}

ZGWebModule::Response ZGHMIHandle::createFile(const QJsonValue& param,
                                              const std::string tableName)
{
    const auto& obj = param.toObject();
    ZG6000::StringList listSql;
    std::string fileID, fileContentID;
    if (!ZGProxyCommon::createUUID(fileID))
        return ZGWebModule::errorObject(QStringLiteral("创建文件ID失败."));
    if (!ZGProxyCommon::createUUID(fileContentID))
        return ZGWebModule::errorObject(QStringLiteral("创建文件内容ID失败."));
    QString fields = "id,fileContentID,";
    QString values = QString("'%1','%2',").arg(fileID.c_str()).arg(fileContentID.c_str());
    auto it = obj.begin();
    while (it != obj.end())
    {
        fields += it.key() + ",";
        values += QString("'%1',").arg(it.value().toString());
        ++it;
    }
    fields.chop(1);
    values.chop(1);
    QString sql = QString("INSERT INTO %1 (%2) VALUES (%3);").arg(tableName.c_str()).arg(fields).arg(values);
    listSql.push_back(sql.toStdString());
    sql = QString("INSERT INTO hmi_param_file_content (id) VALUES (%1);").arg(fileContentID.c_str());
    listSql.push_back(sql.toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("更新文件失败"));
    return ZGWebModule::replyObject(fileID.c_str());
}

ZGWebModule::Response ZGHMIHandle::updateFile(const QJsonValue& param,
                                              const std::string tableName)
{
    const auto& obj = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(obj, {"id"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& pageID = obj["id"].toString();
    const auto& content = obj["content"].toString();
    QString sql = QString("SELECT fileContentID FROM %1 WHERE id = '%2'").arg(tableName.c_str()).arg(pageID);
    std::string fileContentID;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), fileContentID))
        return ZGWebModule::errorObject(QStringLiteral("获取文件内容ID失败"));
    QString fieldsValue;
    auto it = obj.begin();
    while (it != obj.end())
    {
        if ((it.key() != "id") && (it.key() != "content"))
        {
            fieldsValue += QString("%1 = '%2',").arg(it.key()).arg(it.value().toString());
        }
        ++it;
    }
    fieldsValue.chop(1);
    ZG6000::StringList listSql;
    sql = QString("UPDATE %1 SET %2 WHERE id = '%3'").arg(tableName.c_str()).arg(fieldsValue).arg(pageID);
    ZGLOG_DEBUG(sql);
    listSql.push_back(sql.toStdString());
    sql = QString("UPDATE hmi_param_file_content SET content = '%1' WHERE id = '%2'").arg(content).arg(
        fileContentID.c_str());
    ZGLOG_DEBUG(sql);
    listSql.push_back(sql.toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject("更新文件失败");
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGHMIHandle::removeFile(const QJsonValue& param,
                                              const std::string tableName)
{
    QString fileID = param.toString();
    std::string sql = "SELECT fileContentID FROM " + tableName + " WHERE id = '" + fileID.toStdString() + "'";
    std::string fileContentId;
    if (!ZGProxyCommon::execQuerySqlField(sql, fileContentId))
        return ZGWebModule::errorObject("Get fileContentID error.");
    ZG6000::StringList listSql;
    sql = "DELETE FROM " + tableName + " WHERE id = '" + fileID.toStdString() + "'";
    listSql.push_back(sql);
    sql = "DELETE FROM hmi_param_file_content WHERE id = '" + fileContentId + "'";
    listSql.push_back(sql);
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("删除文件失败"));
    return ZGWebModule::replyObject("");
}

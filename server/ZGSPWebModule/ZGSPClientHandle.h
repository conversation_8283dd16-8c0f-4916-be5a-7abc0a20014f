#ifndef ZGSPCLIENTHANDLE_H
#define ZGSPCLIENTHANDLE_H

#include <QtHttpServer/QHttpServerRequest>
#include "ZGWebModule.h"
#include "ZGProxyMng.h"

class ZGSPClientHandle : public QObject
{
    Q_OBJECT

public:
    explicit ZGSPClientHandle(QObject* parent = nullptr);
    ZGWebModule::Response on_sp_client_info(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_list(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_update(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_activate(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_delete(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_auth_update(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_verify(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_close(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_state_switch(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_switch_allow(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_unregister(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_client_unbind(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_password_verify(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_card_verify(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_auth_dev_verify(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_list(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_users_get(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_info(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_add(const QString& clientID,
                                         const QVariantMap& headers,
                                         const QJsonValue& param,
                                         const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_update(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_delete(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_card_add(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_card_delete(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_fingers_get(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_finger_update(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_finger_delete(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_face_get(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_face_update(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_face_delete(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_sync(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_password_send(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_login(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_verify_login(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_verify_send(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_logout(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_role_power_list(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_role_power_edit(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_role_delete(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_card_list(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_card_use(const QString& clientID,
                                         const QVariantMap& headers,
                                         const QJsonValue& param,
                                         const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_company_add(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_company_update(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_company_delete(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_department_add(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_department_update(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_department_delete(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_role_add(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_role_update(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_password_change(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_password_force_change(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_user_password_reset(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);

private:
    ZGWebModule::Response userCall(const QString& clientID,
                                   const QJsonValue& param,
                                   std::function<ZGWebModule::Response(
                                       const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy)> func);
    ZGWebModule::Response powerCall(const QString& clientID,
                                    const QJsonValue& param,
                                    std::function<ZGWebModule::Response(
                                        const std::shared_ptr<ZG6000::ZGSPPowerVerifyPrx> powerProxy)> func);
    ZGWebModule::Response clientCall(const QString& clientID,
                                     const QJsonValue& param,
                                     std::function<ZGWebModule::Response(
                                         const std::shared_ptr<ZG6000::ZGSPClientManagerPrx> clientProxy)> func);

    bool getUserPowers(const QString& userID,
                       ZG6000::StringList& listPower,
                       QString& errMsg);
};

#endif // ZGSPCLIENTHANDLE_H

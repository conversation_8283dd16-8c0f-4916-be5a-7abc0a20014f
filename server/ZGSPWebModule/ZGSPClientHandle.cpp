#include "ZGSPClientHandle.h"

#include <QJsonDocument>
#include <QJsonArray>

#include "ZGProxyCommon.h"
#include "ZGWebModule.h"
#include "ZGJson.h"
#include "ZGUtils.h"

ZGSPClientHandle::ZGSPClientHandle(QObject* parent)
    : QObject{parent}
{
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_info(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    const auto& queryClientID = param.toString();
    ZG6000::StringMap clientInfo;
    if (!ZGProxyCommon::getDataByFields("sp_param_client", queryClientID.toStdString(), {
        "id", "name", "clientTypeID", "authDevID", "rtAppNodeID", "rtSubsystemID", "rtRegisterTime", "rtMasterState",
        "rtLoginUserID", "rtLoginTime", "rtIsBound"
    }, clientInfo))
        return ZGWebModule::errorObject(QStringLiteral("获取客户端'%1'信息失败").arg(queryClientID));
    if (clientInfo["id"].empty())
        return ZGWebModule::errorObject(QStringLiteral("无可用的客户端'%1'").arg(queryClientID));
    QJsonObject object;
    for (const auto& pair : clientInfo)
    {
        if (pair.first == "clientTypeID")
        {
            std::string clientTypeName;
            ZGProxyCommon::getDataByField("sp_dict_client_type", pair.second, "name", clientTypeName);
            object["clientTypeName"] = clientTypeName.c_str();
        }
        if (pair.first == "rtAppNodeID")
        {
            std::string appNodeName;
            ZGProxyCommon::getDataByField("sp_param_appnode", pair.second, "name", appNodeName);
            object["appNodeName"] = appNodeName.c_str();
        }
        if (pair.first == "rtSubsystemID")
        {
            std::string subsystemName;
            ZGProxyCommon::getDataByField("sp_param_subsystem", pair.second, "name", subsystemName);
            object["subsystemName"] = subsystemName.c_str();
        }
        if (pair.first == "rtLoginUserID")
        {
            std::string userName;
            if (pair.second == "root")
                userName = u8"超级管理员";
            else
                ZGProxyCommon::getDataByField("sp_param_hrm_user", pair.second, "name", userName);
            object["userName"] = userName.c_str();
        }
        if (pair.first == "authDevID")
        {
            ZG6000::StringMap authDev;
            ZGProxyCommon::getDataByFields("mp_param_device", pair.second, {"name", "subtypeID"}, authDev);
            object["authDevName"] = ZGUtils::get(authDev, "name", "").c_str();
            object["authDevSubtypeID"] = ZGUtils::get(authDev, "subtypeID", "").c_str();
        }
        object[pair.first.c_str()] = pair.second.c_str();
    }
    QJsonArray authArray;
    QString sql = QString("SELECT authModeID, isDefault FROM sp_param_client_auth WHERE clientID = '%1'").
        arg(queryClientID);
    ZG6000::ListStringMap listAuth;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listAuth))
        return ZGWebModule::errorObject(QStringLiteral("获取客户端'%1'授权方式失败").arg(queryClientID));
    for (auto& auth : listAuth)
    {
        QJsonObject authObj;
        authObj["id"] = auth["authModeID"].c_str();
        authObj["isDefault"] = auth["isDefault"].c_str();
        authArray.append(authObj);
    }
    object["auth"] = authArray;
    return ZGWebModule::replyObject(object);
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_list(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    QString sql = QString("SELECT a.*, b.name AS appNodeName, c.name as authDevName, d.name AS clientTypeName "
        "FROM sp_param_client a "
        "LEFT JOIN sp_param_appnode b ON a.rtAppNodeID = b.id "
        "LEFT JOIN mp_param_device c ON a.authDevID = c.id "
        "LEFT JOIN sp_dict_client_type d ON a.clientTypeID = d.id "
        "ORDER BY a.id");
    ZG6000::ListStringMap listClient;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listClient))
        return ZGWebModule::errorObject(QStringLiteral("获取客户端列表失败"));
    for (auto& client : listClient)
    {
        ZG6000::StringMap realClient;
        if (!ZGProxyCommon::getDataByFields("sp_param_client", client["id"],
            {"rtState", "rtMasterState", "rtLoginUserID", "rtLoginTime"}, realClient))
            continue;
        for (const auto& [key, value] : realClient)
        {
            if (key == "rtLoginUserID" && (!value.empty()))
            {
                std::string rtLoginUserName;
                if (value == "root")
                    rtLoginUserName = u8"超级管理员";
                else
                    ZGProxyCommon::getDataByField("sp_param_hrm_user", value, "name", rtLoginUserName);
                client["rtLoginUserName"] = rtLoginUserName;
            }
            client[key] = value;
        }
    }
    const auto& clientArray = ZGWebModule::listStringMapToArray(listClient);
    return ZGWebModule::replyObject(clientArray);
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_update(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    auto clientParam = ZGWebModule::objectToStringMap(param.toObject());
    return clientCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPClientManagerPrx> clientProxy) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!clientProxy->editClient(clientParam, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_activate(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return clientCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPClientManagerPrx> clientProxy) -> ZGWebModule::Response
        {
            const auto& activateClientID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!clientProxy->activateClient(activateClientID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_delete(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& delClientID = param.toString().toStdString();
    ZG6000::StringList listSql;
    listSql.push_back(QString("DELETE FROM sp_param_client WHERE id = '%1'").arg(delClientID.c_str()).toStdString());
    listSql.push_back(
        QString("DELETE FROM sp_param_client_auth WHERE clientID = '%1'").arg(delClientID.c_str()).toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("删除客户端失败"));
    ZGProxyCommon::synchronize();
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_auth_update(const QString& clientID,
                                                                 const QVariantMap& headers,
                                                                 const QJsonValue& param,
                                                                 const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return clientCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPClientManagerPrx> clientProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            if (!ZGWebModule::checkRequiredFields(object, {"clientID", "auth"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& changeClientID = object["clientID"].toString().toStdString();
            const auto& authArray = object["auth"].toArray();
            const auto& listAuthParam = ZGWebModule::arrayToListStringMap(authArray);
            ZGLOG_TRACE(QString("listAuthParam size: %1").arg(listAuthParam.size()));
            for (const auto& authParam : listAuthParam)
            {
                for (const auto& [key, value] : authParam)
                {
                    ZGLOG_TRACE(QString("'%1': '%2'").arg(key.c_str()).arg(value.c_str()));
                }
            }
            ZG6000::ErrorInfo e;
            if (!clientProxy->updateClientAuthMode(changeClientID, listAuthParam, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_verify(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    std::string loginUserID;
    if (!ZGProxyCommon::getDataByField("sp_param_client", clientID.toStdString(), "rtLoginUserID", loginUserID))
        return ZGWebModule::errorObject(QStringLiteral("获取客户端'%1'登录用户失败").arg(clientID));
    if (loginUserID.empty())
        return ZGWebModule::errorObject(QStringLiteral("客户端'%1'未登录").arg(clientID));
    std::string password;
    if (!ZGProxyCommon::getDataByField("sp_param_hrm_user", loginUserID, "password", password))
        return ZGWebModule::errorObject(QStringLiteral("获取用户'%1'密码失败"));
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& powerID = param.toString();
            QString errMsg;
            ZG6000::ErrorInfo e;
            if (!userProxy->verifyByPassword(clientID.toStdString(), loginUserID, password, "", powerID.toStdString(),
                e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_close(const QString& clientID,
                                                           const QVariantMap& headers,
                                                           const QJsonValue& param,
                                                           const QHttpServerRequest& req)
{
    std::string loginUserID;
    if (!ZGProxyCommon::getDataByField("sp_param_client", clientID.toStdString(), "rtLoginUserID", loginUserID))
        return ZGWebModule::errorObject(QStringLiteral("获取客户端'%1'登录用户失败").arg(clientID));
    if (loginUserID.empty())
        return ZGWebModule::errorObject(QStringLiteral("客户端'%1'未登录").arg(clientID));
    std::string password;
    if (!ZGProxyCommon::getDataByField("sp_param_hrm_user", loginUserID, "password", password))
        return ZGWebModule::errorObject(QStringLiteral("获取用户'%1'密码失败"));
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            QString errMsg;
            ZG6000::ErrorInfo e;
            if (!userProxy->verifyByPassword(clientID.toStdString(), loginUserID, password, "", "ZG_HP_MAINTAIN", e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            const auto& clientProxy = ZGProxyMng::instance()->getProxySPClientManager();
            if (clientProxy == nullptr)
                return ZGWebModule::errorObject(QStringLiteral("获取客户端管理服务代理失败"));
            if (!clientProxy->unregisterClient(clientID.toStdString(), e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_state_switch(const QString& clientID,
                                                                  const QVariantMap& headers,
                                                                  const QJsonValue& param,
                                                                  const QHttpServerRequest& req)
{
    const auto& masterState = param.toString().toStdString();
    auto clientPrx = ZGProxyMng::instance()->getProxySPClientManager();
    if (clientPrx == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取客户端管理服务代理对象失败"));
    try
    {
        if (masterState == "2")
            clientPrx->toMaster(clientID.toStdString());
        else if (masterState == "1")
            clientPrx->toSlave(clientID.toStdString());
        else
            return ZGWebModule::errorObject(QStringLiteral("无效的主备状态'%1'").arg(masterState.c_str()));
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_switch_allow(const QString& clientID,
                                                                  const QVariantMap& headers,
                                                                  const QJsonValue& param,
                                                                  const QHttpServerRequest& req)
{
    return ZGWebModule::replyObject("1");
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_unregister(const QString& clientID,
                                                                const QVariantMap& headers,
                                                                const QJsonValue& param,
                                                                const QHttpServerRequest& req)
{
    return clientCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPClientManagerPrx> clientProxy) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!clientProxy->unregisterClient(clientID.toStdString(), e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_client_unbind(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    return clientCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPClientManagerPrx> clientProxy) -> ZGWebModule::Response
        {
            const auto& unbindClientID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!clientProxy->unbindClient(unbindClientID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_password_verify(const QString& clientID,
                                                                   const QVariantMap& headers,
                                                                   const QJsonValue& param,
                                                                   const QHttpServerRequest& req)
{
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"userID", "password", "powerID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& userID = object["userID"].toString();
            const auto& password = object["password"].toString();
            // 密码使用了客户端的对称密钥加密过，需要解密
            QByteArray clientKey;
            if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
            {
                return ZGWebModule::errorObject(errMsg);
            }
            QByteArray original = password.toLatin1();
            QByteArray decrypted;
            if (!ZGWebModule::decryptWithClientKey(clientKey, original, decrypted, errMsg))
            {
                return ZGWebModule::errorObject(errMsg);
            }
            QString decryptedPassword = decrypted.data();
            const auto& powerID = object["powerID"].toString();
            QString appNodeID;
            if (object.find("appNodeID") != object.end())
                appNodeID = object["appNodeID"].toString();
            ZG6000::ErrorInfo e;
            if (!userProxy->verifyByPassword(clientID.toStdString(), userID.toStdString(),
                decryptedPassword.toStdString(),
                appNodeID.toStdString(), powerID.toStdString(), e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_card_verify(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"cardID", "authModeID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& authModeID = object["authModeID"].toString().toStdString();
            QString userID, powerID, appNodeID;
            if (object.find("userID") != object.end())
                userID = object["userID"].toString();
            if (object.find("powerID") != object.end())
                powerID = object["powerID"].toString();
            if (object.find("appNodeID") != object.end())
                appNodeID = object["appNodeID"].toString();
            const auto& cardID = object["cardID"].toString();
            ZG6000::ErrorInfo e;
            std::string realUserID;
            if (!userProxy->verifyByCard(clientID.toStdString(), userID.toStdString(), authModeID, cardID.toStdString(),
                appNodeID.toStdString(), powerID.toStdString(), realUserID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            std::string userName;
            ZGProxyCommon::getDataByField("sp_param_hrm_user", realUserID, "name", userName);
            QJsonObject obj;
            obj["userID"] = realUserID.c_str();
            obj["userName"] = userName.c_str();
            return ZGWebModule::replyObject(obj);
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_auth_dev_verify(const QString& clientID,
                                                                   const QVariantMap& headers,
                                                                   const QJsonValue& param,
                                                                   const QHttpServerRequest& req)
{
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"userID", "authModeID", "powerID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& userID = object["userID"].toString().toStdString();
            const auto& authModeID = object["authModeID"].toString().toStdString();
            const auto& powerID = object["powerID"].toString().toStdString();
            QString appNodeID;
            if (object.find("appNodeID") != object.end())
                appNodeID = object["appNodeID"].toString();
            ZG6000::ErrorInfo e;
            if (!userProxy->verifyByAuthDev(clientID.toStdString(), userID, authModeID, appNodeID.toStdString(),
                powerID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            std::string userName;
            ZGProxyCommon::getDataByField("sp_param_hrm_user", userID, "name", userName);
            QJsonObject obj;
            obj["userID"] = userID.c_str();
            obj["userName"] = userName.c_str();
            return ZGWebModule::replyObject(obj);
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_list(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    QString sql = "SELECT a.*, b.name AS companyName, c.name AS organName FROM sp_param_hrm_user a "
        "LEFT JOIN sp_param_hrm_company b ON a.companyID = b.id "
        "LEFT JOIN sp_param_hrm_organ c ON a.organID = c.id ORDER BY a.name";
    ZG6000::ListStringMap listUser;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listUser))
        return ZGWebModule::errorObject(QStringLiteral("获取用户列表失败"));
    const auto& userArray = ZGWebModule::listStringMapToArray(listUser);
    return ZGWebModule::replyObject(userArray);
}

ZGWebModule::Response ZGSPClientHandle::on_sp_users_get(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"powerID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& powerID = object["powerID"].toString().toStdString();
            QString appNodeID;
            if (object.find("appNodeID") != object.end())
                appNodeID = object["appNodeID"].toString();
            ZG6000::ErrorInfo e;
            ZG6000::ListStringMap lstUser;
            if (!userProxy->getAvaiableUser(clientID.toStdString(), appNodeID.toStdString(), powerID, lstUser, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            const auto& json = ZGJson::convertToJson(lstUser);
            QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
            return ZGWebModule::replyObject(doc.array());
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_info(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& userID = param.toString().toStdString();
            std::string userInfo;
            ZG6000::ErrorInfo e;
            ZG6000::StringMap user;
            ZG6000::ListStringMap listRole;
            ZG6000::ListStringMap listCard;
            ZG6000::ListStringMap listAuth;
            ZG6000::ListStringMap listAppNode;
            if (!userProxy->getUserInfo(userID, user, listRole, listCard, listAuth, listAppNode, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            QJsonObject object;
            const auto& userObj = ZGWebModule::stringMapToObject(user);
            const auto& roleArray = ZGWebModule::listStringMapToArray(listRole);
            const auto& cardArray = ZGWebModule::listStringMapToArray(listCard);
            const auto& authArray = ZGWebModule::listStringMapToArray(listAuth);
            const auto& appNodeArray = ZGWebModule::listStringMapToArray(listAppNode);
            object["user"] = userObj;
            object["role"] = roleArray;
            object["card"] = cardArray;
            object["auth"] = authArray;
            object["appNode"] = appNodeArray;
            return ZGWebModule::replyObject(object);
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_add(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            if (!ZGWebModule::checkRequiredFields(object, {"user", "role", "auth", "appNode"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& userObj = object["user"].toObject();
            const auto& roleArr = object["role"].toArray();
            const auto& authArr = object["auth"].toArray();
            const auto& appNodeArr = object["appNode"].toArray();
            const auto& user = ZGWebModule::objectToStringMap(userObj);
            const auto& listRole = ZGWebModule::arrayToListStringMap(roleArr);
            const auto& listAuth = ZGWebModule::arrayToListStringMap(authArr);
            const auto& listAppNode = ZGWebModule::arrayToListStringMap(appNodeArr);
            ZG6000::ErrorInfo e;
            if (!userProxy->addUser(user, listRole, listAuth, listAppNode, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_update(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            if (!ZGWebModule::checkRequiredFields(object, {"user", "role", "auth", "appNode"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& userObj = object["user"].toObject();
            const auto& roleArr = object["role"].toArray();
            const auto& authArr = object["auth"].toArray();
            const auto& appNodeArr = object["appNode"].toArray();
            const auto& user = ZGWebModule::objectToStringMap(userObj);
            const auto& listRole = ZGWebModule::arrayToListStringMap(roleArr);
            const auto& listAuth = ZGWebModule::arrayToListStringMap(authArr);
            const auto& listAppNode = ZGWebModule::arrayToListStringMap(appNodeArr);
            ZG6000::ErrorInfo e;
            if (!userProxy->updateUser(user, listRole, listAuth, listAppNode, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_delete(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            if (!ZGWebModule::checkRequiredFields(object, {"userID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& userID = object["userID"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!userProxy->deleteUser(userID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_card_add(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param, [&](std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        if (!ZGWebModule::checkRequiredFields(object, {"userID", "cardNo"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& userID = object["userID"].toString().toStdString();
        const auto& cardNo = object["cardNo"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!userProxy->addUserCard(userID, cardNo, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_card_delete(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param, [&](std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        if (!ZGWebModule::checkRequiredFields(object, {"cardNo"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& cardNo = object["cardNo"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!userProxy->deleteUserCard(cardNo, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_fingers_get(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    return userCall(clientID, param, [&](std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
    {
        const auto& userID = param.toString().toStdString();
        ZG6000::ListStringMap listFinger;
        ZG6000::ErrorInfo e;
        if (!userProxy->getUserFingers(userID, listFinger, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& fingerArray = ZGWebModule::listStringMapToArray(listFinger);
        return ZGWebModule::replyObject(fingerArray);
    });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_face_get(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    return userCall(clientID, param, [&](std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
    {
        const auto& userID = param.toString().toStdString();
        std::string faceData;
        ZG6000::ErrorInfo e;
        if (!userProxy->getUserFace(userID, faceData, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(faceData.c_str());
    });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_finger_update(const QString& clientID,
                                                                 const QVariantMap& headers,
                                                                 const QJsonValue& param,
                                                                 const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param, [&](std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        if (!ZGWebModule::checkRequiredFields(object, {"userID", "fingerNo", "fingerData"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& userID = object["userID"].toString().toStdString();
        const auto& fingerNo = object["fingerNo"].toVariant().toString().toInt();
        const auto& fingerData = object["fingerData"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!userProxy->updateUserFinger(userID, fingerNo, fingerData, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_finger_delete(const QString& clientID,
                                                                 const QVariantMap& headers,
                                                                 const QJsonValue& param,
                                                                 const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param, [&](std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        if (!ZGWebModule::checkRequiredFields(object, {"userID", "fingerNo"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& userID = object["userID"].toString().toStdString();
        const auto& fingerNo = object["fingerNo"].toVariant().toString().toInt();
        ZG6000::ErrorInfo e;
        if (!userProxy->deleteUserFinger(userID, fingerNo, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_face_update(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param, [&](std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        if (!ZGWebModule::checkRequiredFields(object, {"userID", "faceData"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& userID = object["userID"].toString().toStdString();
        const auto& faceData = object["faceData"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!userProxy->updateUserFace(userID, faceData, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_face_delete(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param, [&](std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
    {
        const auto& userID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!userProxy->deleteUserFace(userID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_sync(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    auto identProxy = ZGProxyMng::instance()->getProxyMPIdentifyManager();
    if (identProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取身份识别管理服务代理对象失败"));
    try
    {
        ZG6000::ErrorInfo e;
        if (!identProxy->syncUserFromDB(e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_password_send(const QString& clientID,
                                                                 const QVariantMap& headers,
                                                                 const QJsonValue& param,
                                                                 const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param, [&](std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
    {
        const auto& userID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!userProxy->sendRandomPassword(userID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_login(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req)
{
    const auto& paramObject = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(paramObject, {"type", "keepTime"}, errMsg))
    {
        return ZGWebModule::errorObject(errMsg);
    }
    const auto& type = paramObject["type"].toString();
    const auto& keepTime = paramObject["keepTime"].toInt();
    auto powerProxy = ZGProxyMng::instance()->getProxySPPowerVerify();
    if (powerProxy == nullptr)
    {
        return ZGWebModule::errorObject(QStringLiteral("获取权限验证服务代理失败"));
    }
    ZG6000::ErrorInfo e;
    QString userID;
    if (type == "password")
    {
        if (!ZGWebModule::checkRequiredFields(paramObject, {"userID", "password"}, errMsg))
        {
            return ZGWebModule::errorObject(errMsg);
        }
        userID = paramObject["userID"].toString();
        const auto& password = paramObject["password"].toString();
        // 密码使用了客户端的对称密钥加密过，需要解密
        QByteArray clientKey;
        if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
        {
            return ZGWebModule::errorObject(errMsg);
        }
        QByteArray original = password.toLatin1();
        QByteArray decrypted;
        if (!ZGWebModule::decryptWithClientKey(clientKey, original, decrypted, errMsg))
        {
            return ZGWebModule::errorObject(errMsg);
        }
        QString decryptedPassword = decrypted.data();
        try
        {
            if (!powerProxy->loginByPassword(clientID.toStdString(), userID.toStdString(),
                decryptedPassword.toStdString(), keepTime, e))
            {
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            }
        }
        catch (const Ice::Exception& ie)
        {
            return ZGWebModule::errorObject(ie.what());
        }
    }
    else if (type == "card")
    {
        if (!ZGWebModule::checkRequiredFields(paramObject, {"cardID", "authModeID"}, errMsg))
        {
            return ZGWebModule::errorObject(errMsg);
        }
        const auto& cardID = paramObject["cardID"].toString();
        const auto& authModeID = paramObject["authModeID"].toString();
        try
        {
            std::string realUserID;
            if (!powerProxy->loginByCard(clientID.toStdString(), "", authModeID.toStdString(),
                cardID.toStdString(), keepTime, realUserID, e))
            {
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            }
            userID = realUserID.c_str();
        }
        catch (const Ice::Exception& ie)
        {
            return ZGWebModule::errorObject(ie.what());
        }
    }
    else
    {
        return ZGWebModule::errorObject(QStringLiteral("未知的登录类型"));
    }
    ZG6000::StringList listPower;
    if (!getUserPowers(userID, listPower, errMsg))
    {
        return ZGWebModule::errorObject(errMsg);
    }
    QJsonObject dataObject;
    QString tokenID, expireTime;
    if (!ZGWebModule::generateTokenObject(userID, keepTime, false, dataObject, tokenID, expireTime, errMsg))
    {
        return ZGWebModule::errorObject(errMsg);
    }
    return ZGWebModule::replyObject(dataObject);
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_verify_login(const QString& clientID,
                                                                const QVariantMap& headers,
                                                                const QJsonValue& param,
                                                                const QHttpServerRequest& req)
{
    const auto& paramObject = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(paramObject, {"userID", "password", "verifyCode", "keepTime"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& userID = paramObject["userID"].toString();
    const auto& password = paramObject["password"].toString();
    const auto& verifyCode = paramObject["verifyCode"].toString();
    const auto& keepTime = paramObject["keepTime"].toInt();
    QByteArray clientKey;
    if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
    {
        return ZGWebModule::errorObject(errMsg);
    }
    QByteArray pwdOriginal = password.toLatin1();
    QByteArray pwdDecrypted;
    if (!ZGWebModule::decryptWithClientKey(clientKey, pwdOriginal, pwdDecrypted, errMsg))
    {
        return ZGWebModule::errorObject(errMsg);
    }
    QByteArray codeOriginal = verifyCode.toLatin1();
    QByteArray codeDecrypted;
    if (!ZGWebModule::decryptWithClientKey(clientKey, codeOriginal, codeDecrypted, errMsg))
    {
        return ZGWebModule::errorObject(errMsg);
    }
    QString decryptedPassword = pwdDecrypted.data();
    QString decryptedVerifyCode = codeDecrypted.data();
    auto powerProxy = ZGProxyMng::instance()->getProxySPPowerVerify();
    if (powerProxy == nullptr)
    {
        return ZGWebModule::errorObject(QStringLiteral("获取权限验证服务代理失败"));
    }
    try
    {
        ZG6000::ErrorInfo e;
        if (!powerProxy->loginByPasswordAndVerifyCode(clientID.toStdString(), userID.toStdString(),
            decryptedPassword.toStdString(), decryptedVerifyCode.toStdString(), keepTime, e))
        {
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        }
        ZG6000::StringList listPower;
        if (!getUserPowers(userID, listPower, errMsg))
        {
            return ZGWebModule::errorObject(errMsg);
        }
        QJsonObject dataObject;
        QString tokenID, expireTime;
        if (!ZGWebModule::generateTokenObject(userID, keepTime, false, dataObject, tokenID, expireTime, errMsg))
        {
            return ZGWebModule::errorObject(errMsg);
        }
        return ZGWebModule::replyObject(dataObject);
    }
    catch (const Ice::Exception& ie)
    {
        return ZGWebModule::errorObject(ie.what());
    }
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_verify_send(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    const auto& userID = param.toString();
    auto powerProxy = ZGProxyMng::instance()->getProxySPPowerVerify();
    if (powerProxy == nullptr)
    {
        return ZGWebModule::errorObject(QStringLiteral("获取权限验证服务代理失败"));
    }
    try
    {
        ZG6000::ErrorInfo e;
        if (!powerProxy->sendVerifyCode(userID.toStdString(), e))
        {
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        }
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& ie)
    {
        return ZGWebModule::errorObject(ie.what());
    }
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_logout(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    std::string loginUserId;
    if (!ZGProxyCommon::getDataByField("sp_param_client", clientID.toStdString(), "rtLoginUserID", loginUserId))
        return ZGWebModule::errorObject(QString(QStringLiteral("获取客户端%1出错")).arg(clientID));
    return powerCall(clientID, param,
        [&](std::shared_ptr<ZG6000::ZGSPPowerVerifyPrx> powerProxy) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            ZGLOG_INFO(QString("client %1 user %2 logout.").arg(clientID).arg(loginUserId.c_str()));
            if (!powerProxy->logout(clientID.toStdString(), loginUserId, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_role_power_list(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    const auto& roleID = param.toString();
    QString sql = QString(
        "SELECT a.powerID, b.name FROM sp_param_hrm_role_power a LEFT JOIN sp_param_hrm_power b ON a.powerID = b.id "
        "WHERE a.roleID = '%1' ORDER BY a.id").arg(roleID);
    ZG6000::ListStringMap listPower;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listPower))
        return ZGWebModule::errorObject(QStringLiteral("获取角色'%1'权限失败").arg(roleID));
    const auto& powerArray = ZGWebModule::listStringMapToArray(listPower);
    return ZGWebModule::replyObject(powerArray);
}

ZGWebModule::Response ZGSPClientHandle::on_sp_role_power_edit(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& object = param.toObject();
    ZG6000::StringList listSql;
    for (auto it = object.begin(); it != object.end(); ++it)
    {
        const auto& roleID = it.key().toStdString();
        const auto& powerArray = it.value().toArray();
        const auto& listPowerID = ZGWebModule::arrayToStringList(powerArray);
        listSql.push_back(
            QString("DELETE FROM sp_param_hrm_role_power WHERE roleID = '%1';").arg(roleID.c_str()).toStdString());
        ZG6000::StringList listUUID;
        if (!ZGProxyCommon::createUUID(static_cast<int>(listPowerID.size()), listUUID))
            return ZGWebModule::errorObject(QStringLiteral("创建uuid失败"));
        for (size_t i = 0; i < listPowerID.size(); ++i)
        {
            ZG6000::StringMap rolePower{{"id", listUUID[i]}, {"roleID", roleID}, {"powerID", listPowerID[i]}};
            listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_role_power", rolePower));
        }
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("编辑角色权限失败"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGSPClientHandle::on_sp_role_delete(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& roleID = param.toString();
    ZG6000::StringList listSql;
    listSql.push_back(QString("DELETE FROM sp_param_hrm_role_power WHERE roleID = '%1';").arg(roleID).toStdString());
    listSql.push_back(QString("DELETE FROM sp_param_hrm_user_role WHERE roleID = '%1';").arg(roleID).toStdString());
    listSql.push_back(QString("DELETE FROM sp_param_hrm_role WHERE id = '%1';").arg(roleID).toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("角色删除失败"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGSPClientHandle::on_sp_card_list(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    std::string sql =
        "SELECT sp_param_hrm_card.id, sp_param_hrm_user_card.userID, sp_param_hrm_user.name FROM sp_param_hrm_card LEFT JOIN "
        "sp_param_hrm_user_card ON sp_param_hrm_card.id = sp_param_hrm_user_card.cardID LEFT JOIN "
        "sp_param_hrm_user ON sp_param_hrm_user_card.userID = sp_param_hrm_user.id ORDER BY sp_param_hrm_card.id";
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql, listRecord))
        return ZGWebModule::errorObject(u8"获取卡号列表失败");
    const auto& json = ZGJson::convertToJson(listRecord);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGSPClientHandle::on_sp_card_use(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& cardID = param.toString();
            bool isBind;
            std::string userID;
            ZG6000::ErrorInfo e;
            if (!userProxy->isCardBindUser(cardID.toStdString(), isBind, userID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            QJsonObject object;
            object["isBind"] = isBind;
            object["userID"] = userID.c_str();
            return ZGWebModule::replyObject(object);
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_company_add(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    std::string uuid;
    if (!ZGProxyCommon::createUUID(uuid))
        return ZGWebModule::errorObject(QStringLiteral("创建uuid失败"));
    const auto& object = param.toObject();
    const auto& companyName = object["name"].toString().toStdString();
    ZG6000::StringMap company{{"id", uuid}, {"name", companyName}};
    const auto& sql = ZGUtils::generateInsertSql("sp_param_hrm_company", company);
    if (!ZGProxyCommon::execSql(sql))
        return ZGWebModule::errorObject(QStringLiteral("添加公司失败"));
    return ZGWebModule::replyObject(uuid.c_str());
}

ZGWebModule::Response ZGSPClientHandle::on_sp_company_update(const QString& clientID,
                                                             const QVariantMap& headers,
                                                             const QJsonValue& param,
                                                             const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& object = param.toObject();
    const auto& id = object["id"].toString().toStdString();
    const auto& name = object["name"].toString().toStdString();
    ZG6000::StringMap company{{"id", id}, {"name", name}};
    const auto& sql = ZGUtils::generateUpdateSql("sp_param_hrm_company", company);
    if (!ZGProxyCommon::execSql(sql))
        return ZGWebModule::errorObject(QStringLiteral("更新公司失败"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGSPClientHandle::on_sp_company_delete(const QString& clientID,
                                                             const QVariantMap& headers,
                                                             const QJsonValue& param,
                                                             const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& id = param.toString().toStdString();
    QString sql = QString("DELETE FROM sp_param_hrm_company WHERE id = '%1'").arg(id.c_str());
    if (!ZGProxyCommon::execSql(sql.toStdString()))
        return ZGWebModule::errorObject(QStringLiteral("删除公司失败"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGSPClientHandle::on_sp_department_add(const QString& clientID,
                                                             const QVariantMap& headers,
                                                             const QJsonValue& param,
                                                             const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    std::string uuid;
    if (!ZGProxyCommon::createUUID(uuid))
        return ZGWebModule::errorObject(QStringLiteral("创建uuid失败"));
    const auto& object = param.toObject();
    const auto& departmentName = object["name"].toString().toStdString();
    ZG6000::StringMap department{{"id", uuid}, {"name", departmentName}};
    const auto& sql = ZGUtils::generateInsertSql("sp_param_hrm_organ", department);
    if (!ZGProxyCommon::execSql(sql))
        return ZGWebModule::errorObject(QStringLiteral("添加部门失败"));
    return ZGWebModule::replyObject(uuid.c_str());
}

ZGWebModule::Response ZGSPClientHandle::on_sp_department_update(const QString& clientID,
                                                                const QVariantMap& headers,
                                                                const QJsonValue& param,
                                                                const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& object = param.toObject();
    const auto& id = object["id"].toString().toStdString();
    const auto& name = object["name"].toString().toStdString();
    ZG6000::StringMap department{{"id", id}, {"name", name}};
    const auto& sql = ZGUtils::generateUpdateSql("sp_param_hrm_organ", department);
    if (!ZGProxyCommon::execSql(sql))
        return ZGWebModule::errorObject(QStringLiteral("更新部门失败"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGSPClientHandle::on_sp_department_delete(const QString& clientID,
                                                                const QVariantMap& headers,
                                                                const QJsonValue& param,
                                                                const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& id = param.toString().toStdString();
    QString sql = QString("DELETE FROM sp_param_hrm_organ WHERE id = '%1'").arg(id.c_str());
    if (!ZGProxyCommon::execSql(sql.toStdString()))
        return ZGWebModule::errorObject(QStringLiteral("删除部门失败"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGSPClientHandle::on_sp_role_add(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    std::string uuid;
    if (!ZGProxyCommon::createUUID(uuid))
        return ZGWebModule::errorObject(QStringLiteral("创建uuid失败"));
    const auto& object = param.toObject();
    const auto& roleName = object["name"].toString().toStdString();
    ZG6000::StringMap role{{"id", uuid}, {"name", roleName}};
    const auto& sql = ZGUtils::generateInsertSql("sp_param_hrm_role", role);
    if (!ZGProxyCommon::execSql(sql))
        return ZGWebModule::errorObject(QStringLiteral("添加角色失败"));
    return ZGWebModule::replyObject(uuid.c_str());
}

ZGWebModule::Response ZGSPClientHandle::on_sp_role_update(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& object = param.toObject();
    if (!ZGWebModule::checkRequiredFields(object, {"id", "powers"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& id = object["id"].toString().toStdString();
    const auto& powerArray = object["powers"].toArray();
    ZG6000::StringList listSql;
    // 先删除sp_param_hrm_role_power表中该角色下所有的权限
    QString sql = QString("DELETE FROM sp_param_hrm_role_power WHERE roleID = '%1'").arg(id.c_str());
    listSql.push_back(sql.toStdString());
    // 再添加新的权限
    const auto& listPowerID = ZGWebModule::arrayToStringList(powerArray);
    ZG6000::StringList listUUID;
    if (!ZGProxyCommon::createUUID(static_cast<int>(listPowerID.size()), listUUID))
        return ZGWebModule::errorObject(QStringLiteral("创建uuid失败"));
    for (size_t i = 0; i < listPowerID.size(); ++i)
    {
        ZG6000::StringMap rolePower{{"id", listUUID[i]}, {"roleID", id}, {"powerID", listPowerID[i]}};
        listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_role_power", rolePower));
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("编辑角色权限失败"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_password_change(const QString& clientID,
                                                                   const QVariantMap& headers,
                                                                   const QJsonValue& param,
                                                                   const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    // 不验证权限，只是为了拿到token
    if (!ZGWebModule::verifyAuthentication(clientID, headers, "", token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    std::string userID;
    if (!ZGProxyCommon::getDataByField("sp_param_client", clientID.toStdString(),
        {"rtLoginUserID"}, userID))
        return ZGWebModule::errorObject(u8"获取客户端'" + clientID + u8"'登录用户失败");
    // 比较令牌的userID和登录用户的userID是否一致
    const auto& tokenUserID = token["userID"].toString().toStdString();
    if (tokenUserID != userID)
        return ZGWebModule::errorObject(u8"令牌中的用户ID与登录用户ID不一致");
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            const auto& oldPassword = object["oldPassword"].toString();
            const auto& newPassword = object["newPassword"].toString();
            QByteArray clientKey;

            if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
            {
                return ZGWebModule::errorObject(errMsg);
            }
            QByteArray oldOriginal = oldPassword.toLatin1();
            QByteArray oldDecrypted;
            if (!ZGWebModule::decryptWithClientKey(clientKey, oldOriginal, oldDecrypted, errMsg))
            {
                return ZGWebModule::errorObject(errMsg);
            }
            QByteArray newOriginal = newPassword.toLatin1();
            QByteArray newDecrypted;
            if (!ZGWebModule::decryptWithClientKey(clientKey, newOriginal, newDecrypted, errMsg))
            {
                return ZGWebModule::errorObject(errMsg);
            }
            QString decryptedOldPassword = oldDecrypted.data();
            QString decryptedNewPassword = newDecrypted.data();
            ZG6000::ErrorInfo e;
            if (!userProxy->changePassword(userID, decryptedOldPassword.toStdString(),
                decryptedNewPassword.toStdString(), e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_password_force_change(const QString &clientID, const QVariantMap &headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    QString errMsg;
    return userCall(clientID, param,
                    [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
                    {
                        const auto& object = param.toObject();
                        if (!ZGWebModule::checkRequiredFields(object, {"userID", "oldPassword", "newPassword"}, errMsg))
                        {
                            return ZGWebModule::errorObject(errMsg);
                        }
                        const auto& oldPassword = object["oldPassword"].toString();
                        const auto& newPassword = object["newPassword"].toString();
                        const auto& userID = object["userID"].toString().toStdString();
                        QByteArray clientKey;
                        if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
                        {
                            return ZGWebModule::errorObject(errMsg);
                        }
                        QByteArray oldOriginal = oldPassword.toLatin1();
                        QByteArray oldDecrypted;
                        if (!ZGWebModule::decryptWithClientKey(clientKey, oldOriginal, oldDecrypted, errMsg))
                        {
                            return ZGWebModule::errorObject(errMsg);
                        }
                        QByteArray newOriginal = newPassword.toLatin1();
                        QByteArray newDecrypted;
                        if (!ZGWebModule::decryptWithClientKey(clientKey, newOriginal, newDecrypted, errMsg))
                        {
                            return ZGWebModule::errorObject(errMsg);
                        }
                        QString decryptedOldPassword = oldDecrypted.data();
                        QString decryptedNewPassword = newDecrypted.data();
                        ZG6000::ErrorInfo e;
                        if (!userProxy->changePassword(userID, decryptedOldPassword.toStdString(),
                                                       decryptedNewPassword.toStdString(), e))
                            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
                        return ZGWebModule::replyObject("");
                    });
}

ZGWebModule::Response ZGSPClientHandle::on_sp_user_password_reset(const QString& clientID,
                                                                  const QVariantMap& headers,
                                                                  const QJsonValue& param,
                                                                  const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_USER_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return userCall(clientID, param,
        [&](const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy) -> ZGWebModule::Response
        {
            const auto& userID = param.toString();
            ZG6000::ErrorInfo e;
            if (!userProxy->resetPassword(userID.toStdString(), e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGSPClientHandle::userCall(const QString& clientID,
                                                 const QJsonValue& param,
                                                 std::function<ZGWebModule::Response(
                                                     const std::shared_ptr<ZG6000::ZGSPUserManagerPrx> userProxy)> func)
{
    const auto& userProxy = ZGProxyMng::instance()->getProxySPUserManager();
    if (userProxy == nullptr)
        return ZGWebModule::errorObject("获取用户管理服务代理对象失败");
    try
    {
        return func(userProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPClientHandle::powerCall(const QString& clientID,
                                                  const QJsonValue& param,
                                                  std::function<ZGWebModule::Response(
                                                      const std::shared_ptr<ZG6000::ZGSPPowerVerifyPrx> powerProxy)>
                                                  func)
{
    const auto& powerProxy = ZGProxyMng::instance()->getProxySPPowerVerify();
    if (powerProxy == nullptr)
        return ZGWebModule::errorObject("获取权限验证服务代理对象失败");
    try
    {
        return func(powerProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPClientHandle::clientCall(const QString& clientID,
                                                   const QJsonValue& param,
                                                   std::function<ZGWebModule::Response(
                                                       const std::shared_ptr<ZG6000::ZGSPClientManagerPrx> clientProxy)>
                                                   func)
{
    auto clientProxy = ZGProxyMng::instance()->getProxySPClientManager();
    if (clientProxy == nullptr)
        return ZGWebModule::errorObject("获取客户端管理服务代理对象失败");
    try
    {
        return func(clientProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

bool ZGSPClientHandle::getUserPowers(const QString& userID,
                                     ZG6000::StringList& listPower,
                                     QString& errMsg)
{
    QString sql = QString("SELECT DISTINCT powerID FROM sp_param_hrm_role_power a "
        "LEFT JOIN sp_param_hrm_user_role b ON a.roleID = b.roleID "
        "WHERE b.userID = '%1' ORDER BY powerID").arg(userID);
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listPower))
    {
        errMsg = QStringLiteral("获取用户'%1'权限失败").arg(userID);
        return false;
    }
    return true;
}

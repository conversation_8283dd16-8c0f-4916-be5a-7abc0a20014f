#ifndef ZGSPWEBMODULE_H
#define ZGSPWEBMODULE_H

#include "ZGWebModule.h"

class ZGSPHandle;
class ZGSPClientHandle;
class ZGSPWebModule : public ZGWebModule
{
    Q_OBJECT
    Q_PLUGIN_METADATA(IID ZGWebModule_iid FILE "ZGSPWebModule.json")
    Q_INTERFACES(ZGWebModule)

public:
    explicit ZGSPWebModule(QObject *parent = nullptr);

    // ZGWebModule interface
public:
    bool initialize() override;
    QString prefix() override;

private:
    ZGSPHandle* m_pHandle{nullptr};
    ZGSPClientHandle* m_pClientHandle{nullptr};
};

#endif // ZGSPWEBMODULE_H

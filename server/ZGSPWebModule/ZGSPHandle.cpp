#include "ZGSPHandle.h"
#include "ZGWebModule.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"
#include "ZGJson.h"
#include <QFile>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>

ZGSPHandle::ZGSPHandle(QObject* parent) : QObject(parent)
{
}

bool ZGSPHandle::initialize()
{
    if (!initAlarmLevel())
        return false;
    return true;
}

ZGWebModule::Response ZGSPHandle::on_sp_system_param_get(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req)
{
    QString sql = QString("SELECT * FROM sp_param_system");
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
        return ZGWebModule::errorObject(QStringLiteral("获取系统参数失败"));
    if (listRecord.empty())
        return ZGWebModule::errorObject(QStringLiteral("未配置系统参数"));
    const auto& json = ZGJson::convertToJson(listRecord[0]);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.object());
}

ZGWebModule::Response ZGSPHandle::on_sp_time_sync(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    ZG6000::ErrorInfo e;
    ZGLOG_TRACE(QString("client %1 sync time").arg(clientID));
    // 获取本地系统时间
    QDateTime now = QDateTime::currentDateTime();
    QJsonObject obj;
    obj["year"] = now.date().year();
    obj["month"] = now.date().month();
    obj["day"] = now.date().day();
    obj["hour"] = now.time().hour();
    obj["minute"] = now.time().minute();
    obj["second"] = now.time().second();
    return ZGWebModule::replyObject(obj);
}

ZGWebModule::Response ZGSPHandle::on_sp_mqtt_server_get(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    std::string sql =
        "SELECT aNetAddr, aNetPort, bNetAddr, bNetPort FROM sp_param_node_server WHERE serverTypeID = 'ZG_ST_MQTT_MESSAGE'";
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql, listRecord))
        return ZGWebModule::errorObject(u8"获取mqtt服务器失败");
    QJsonArray array;
    for (const auto& record : listRecord)
    {
        const auto& aNetAddr = ZGUtils::get(record, "aNetAddr");
        const auto& aNetPort = ZGUtils::get(record, "aNetPort");
        if (!aNetAddr.empty() && !aNetPort.empty() && (aNetPort != "0"))
        {
            QJsonObject obj;
            obj["address"] = aNetAddr.c_str();
            obj["port"] = aNetPort.c_str();
            array.append(obj);
        }
        const auto& bNetAddr = ZGUtils::get(record, "bNetAddr");
        const auto& bNetPort = ZGUtils::get(record, "bNetPort");
        if (!bNetAddr.empty() && !bNetPort.empty() && (bNetPort != "0"))
        {
            QJsonObject obj;
            obj["address"] = bNetAddr.c_str();
            obj["port"] = bNetPort.c_str();
            array.append(obj);
        }
    }
    return ZGWebModule::replyObject(array);
}

ZGWebModule::Response ZGSPHandle::on_sp_rule_invoke(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    auto scriptProxy = ZGProxyMng::instance()->getProxySPScriptProcess();
    if (scriptProxy == nullptr)
        return ZGWebModule::errorObject(u8"获取规则服务代理失败");
    try
    {
        std::string ruleID = param.toString().toStdString();
        std::string result;
        ZG6000::ErrorInfo e;
        if (!scriptProxy->invokeToString(ruleID, result, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(result.c_str());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_event_confirm(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"userID", "userName", "events"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& userID = object["userID"].toString().toStdString();
    const auto& userName = object["userName"].toString().toStdString();
    const auto& array = object["events"].toArray();
    ZG6000::ListStringMap listEvent;
    for (auto ref : array)
    {
        const auto& eventObj = ref.toObject();
        ZG6000::StringMap event;
        event.insert(std::make_pair("id", eventObj["id"].toString().toStdString()));
        event.insert(std::make_pair("eventTime", eventObj["eventTime"].toString().toStdString()));
        listEvent.push_back(std::move(event));
    }
    const auto& eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
    try
    {
        ZG6000::ErrorInfo e;
        if (!eventProcessPrx->confirmEvents(listEvent, userID, userName, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_event_get(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"appNodeID", "subsystemID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& appNodeID = object["appNodeID"].toString().toStdString();
    const auto& subsystemID = object["subsystemID"].toString().toStdString();
    auto eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
    if (eventProcessPrx == nullptr)
        return ZGWebModule::errorObject(u8"获取事件处理服务代理失败");
    try
    {
        ZG6000::ListStringMap listEvent;
        ZG6000::ErrorInfo e;
        if (!eventProcessPrx->getUnconfirmedEvent(appNodeID, subsystemID, listEvent, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonArray array = ZGWebModule::listStringMapToArray(listEvent);
        return ZGWebModule::replyObject(array);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_event_publish(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    const auto& event = ZGWebModule::objectToStringMap(object);
    const auto& eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
    if (eventProcessPrx == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取事件处理服务代理对象失败"));
    try
    {
        eventProcessPrx->processEvent(event);
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_node_list(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    QString sql = QString("SELECT * FROM sp_param_node");
    ZG6000::ListStringMap listNode;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listNode))
        return ZGWebModule::errorObject(u8"获取节点列表失败");
    auto array = ZGWebModule::listStringMapToArray(listNode);
    return ZGWebModule::replyObject(array);
}

ZGWebModule::Response ZGSPHandle::on_sp_server_reboot(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& instProxy = ZGProxyMng::instance()->getProxySPSystemServiceInstance();
    if (instProxy == nullptr)
        return ZGWebModule::errorObject(u8"获取服务实例代理对象失败");
    const auto& onewayInstProxy = instProxy->ice_oneway();
    try
    {
        onewayInstProxy->rebootByTypeID("ZGPTL");
        onewayInstProxy->rebootByTypeID("ZGMP");
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_history_table_count(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"tableName", "condition"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& tableName = object["tableName"].toString().toStdString();
    const auto& condition = object["condition"].toString().toStdString();
    const auto& hisDataManagerPrx = ZGProxyMng::instance()->getProxySPHisDataManager();
    if (hisDataManagerPrx == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取历史数据管理服务代理对象失败"));
    try
    {
        int recordCount;
        ZG6000::ErrorInfo e;
        if (!hisDataManagerPrx->queryTableCount(tableName, condition, recordCount, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(std::to_string(recordCount).c_str());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_history_table_query(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"tableName", "condition", "offset", "limit"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    std::string orderField{"id"};
    if (object.contains("sort"))
        orderField = object["sort"].toString().toStdString();
    std::string orderType{"ASC"};
    if (object.contains("order"))
        orderType = object["order"].toString().toStdString();
    const auto& tableName = object["tableName"].toString().toStdString();
    const auto& condition = object["condition"].toString().toStdString();
    int offset = object["offset"].toInt();
    int limit = object["limit"].toInt();
    const auto& hisDataManagerPrx = ZGProxyMng::instance()->getProxySPHisDataManager();
    if (hisDataManagerPrx == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取历史数据管理服务代理对象失败"));
    try
    {
        ZG6000::StringList listTitle;
        ZG6000::ListStringList listValues;
        ZG6000::ErrorInfo e;
        if (!hisDataManagerPrx->queryTableData(tableName, condition, offset, limit, orderField, orderType, listTitle,
            listValues, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonObject replyObj;
        QJsonArray arrayTitle, arrayValues;
        for (const auto& title : listTitle)
        {
            arrayTitle.append(title.c_str());
        }
        for (const auto& values : listValues)
        {
            QJsonArray arrayValue;
            for (const auto& value : values)
            {
                arrayValue.append(value.c_str());
            }
            arrayValues.append(arrayValue);
        }
        replyObj["title"] = arrayTitle;
        replyObj["items"] = arrayValues;
        return ZGWebModule::replyObject(replyObj);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_history_log_query(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_MAINTAIN"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return on_sp_history_table_query(clientID, headers, param, req);
}

ZGWebModule::Response ZGSPHandle::on_sp_history_store_yc_query(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    return on_sp_history_store_query(clientID, param, "yc", req);
}

ZGWebModule::Response ZGSPHandle::on_sp_history_store_yx_query(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    return on_sp_history_store_query(clientID, param, "yx", req);
}

ZGWebModule::Response ZGSPHandle::on_sp_history_store_text_query(const QString& clientID,
                                                                 const QVariantMap& headers,
                                                                 const QJsonValue& param,
                                                                 const QHttpServerRequest& req)
{
    return on_sp_history_store_query(clientID, param, "text", req);
}

ZGWebModule::Response ZGSPHandle::on_sp_history_store_ym_query(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    return on_sp_history_store_query(clientID, param, "ym", req);
}

ZGWebModule::Response ZGSPHandle::on_sp_appnode_layer_get(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    QJsonArray array;
    std::string appNodeID = param.toString().toStdString();
    if (appNodeID.empty())
    {
        if (!ZGProxyCommon::getDataByField("sp_param_client", clientID.toStdString(), "rtAppNodeID", appNodeID))
            return ZGWebModule::errorObject(QStringLiteral("获取客户端'%1'关联应用节点失败").arg(clientID));
    }
    writeNodeLayer(appNodeID, array);
    return ZGWebModule::replyObject(array);
}

ZGWebModule::Response ZGSPHandle::on_sp_appnode_type_layer_get(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"appNodeID", "appNodeTypeID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    auto appNodeID = object["appNodeID"].toString().toStdString();
    const auto& appNodeTypeID = object["appNodeTypeID"].toString().toStdString();
    if (appNodeID.empty())
    {
        if (!ZGProxyCommon::getDataByField("sp_param_client", clientID.toStdString(), "rtAppNodeID", appNodeID))
            return ZGWebModule::errorObject(QStringLiteral("获取客户端'%1'关联应用节点失败").arg(clientID));
    }
    QJsonArray array;
    writeNodeLayer(appNodeID, appNodeTypeID, array);
    return ZGWebModule::replyObject(array);
}

ZGWebModule::Response ZGSPHandle::on_sp_appnode_yv_get(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    return ZGWebModule::errorObject("Not implement");
    //	auto appNodeProxy = ZGProxyMng::instance()->getProxySPAppNodeManager();
    //	if (appNodeProxy == nullptr)
    //	    return ZGWebModule::errorObject(QStringLiteral("获取应用节点管理服务代理对象失败"));
    //    try
    //    {
    //		const auto& appNodeID = param.toString().toStdString();
    //        ZG6000::ListStringMap listYv;
    //        ZG6000::ErrorInfo e;
    //		if (!appNodeProxy->getListYv(appNodeID, listYv, e))
    //			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    //		const auto& array = ZGWebModule::listStringMapToArray(listYv);
    //		return ZGWebModule::replyObject(array);
    //    }
    //    catch (const Ice::Exception& e)
    //    {
    //		return ZGWebModule::errorObject(e.what());
    //    }
}

ZGWebModule::Response ZGSPHandle::on_sp_appnode_user_add(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"appNodeID", "users"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& appNodeID = object["appNodeID"].toString();
    const auto& userArray = object["users"].toArray();
    ZG6000::StringList listSql, listID;
    if (!ZGProxyCommon::createUUID(userArray.size(), listID))
        return ZGWebModule::errorObject(QStringLiteral("生成记录ID失败"));
    listSql.push_back(
        QString("DELETE FROM sp_param_appnode_user WHERE appNodeID = '%1';").arg(appNodeID).toStdString());
    for (size_t i = 0; i < userArray.size(); ++i)
    {
        ZG6000::StringMap appNodeUser{
            {"id", listID[i]}, {"appNodeID", appNodeID.toStdString()}, {"userID", userArray[i].toString().toStdString()}
        };
        listSql.push_back(ZGUtils::generateInsertSql("sp_param_appnode_user", appNodeUser));
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("添加应用节点人员失败"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGSPHandle::on_sp_appnode_pos_event_get(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    auto processProxy = ZGProxyMng::instance()->getProxySPEventProcess();
    if (processProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取事件处理服务代理对象失败"));
    try
    {
        auto object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"appNodeID", "subsystemID", "position"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& appNodeID = object["appNodeID"].toString().toStdString();
        const auto& subsystemID = object["subsystemID"].toString().toStdString();
        const auto& position = object["position"].toString().toStdString();
        ZG6000::ListStringMap listEvent;
        ZG6000::ErrorInfo e;
        if (!processProxy->getUnconfirmedPosEvent(appNodeID, subsystemID, position, listEvent, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonArray array = ZGWebModule::listStringMapToArray(listEvent);
        return ZGWebModule::replyObject(array);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_appnode_pos_eventnum_get(const QString& clientID,
                                                                 const QVariantMap& headers,
                                                                 const QJsonValue& param,
                                                                 const QHttpServerRequest& req)
{
    auto processProxy = ZGProxyMng::instance()->getProxySPEventProcess();
    if (processProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取事件处理服务代理对象失败"));
    try
    {
        ZG6000::StringMap appNodeEventNum;
        ZG6000::ErrorInfo e;
        if (!processProxy->getUnconfirmedEventNum(appNodeEventNum, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& object = ZGWebModule::stringMapToObject(appNodeEventNum);
        return ZGWebModule::replyObject(object);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_exam_create(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
    if (examProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取审批管理服务代理对象失败"));
    try
    {
        auto object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"paramExamID"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& paramExamID = object["paramExamID"].toString();
        std::string examID;
        ZG6000::ErrorInfo e;
        if (!examProxy->createExam(paramExamID.toStdString(), examID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(examID.c_str());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_exam_info(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
    if (examProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取审批管理服务代理对象失败"));
    try
    {
        const auto& examID = param.toString().toStdString();
        std::string examInfo;
        ZG6000::ErrorInfo e;
        if (!examProxy->getExamInfo(examID, examInfo, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonDocument doc = QJsonDocument::fromJson(examInfo.c_str());
        return ZGWebModule::replyObject(doc.object());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_exam_step_exec(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
    if (examProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取审批管理服务代理对象失败"));
    try
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"stepID", "examUserID", "examInfo", "examResultID"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        ZG6000::StringMap params;
        params["examUserID"] = object["examUserID"].toString().toStdString();
        params["examInfo"] = object["examInfo"].toString().toStdString();
        params["examResultID"] = object["examResultID"].toString().toStdString();
        std::string rtAppNodeID;
        if (!ZGProxyCommon::getDataByField("sp_param_client", clientID.toStdString(), "rtAppNodeID", rtAppNodeID))
            return ZGWebModule::errorObject(QStringLiteral("获取客户端关联应用节点失败"));
        ZG6000::ErrorInfo e;
        if (!examProxy->execStep(rtAppNodeID, object["stepID"].toString().toStdString(), params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_exam_delete(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
    if (examProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取审批管理服务代理对象失败"));
    try
    {
        const auto& examID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!examProxy->deleteExam(examID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_exam_finish(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
    if (examProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取审批管理服务代理对象失败"));
    try
    {
        const auto& examID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!examProxy->finishExam(examID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_debug_start(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& serverInst = param.toString();
    auto proxyObj = ZGProxyMng::instance()->getProxyZGServerBase(serverInst);
    if (proxyObj == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取服务实例'%1'代理对象失败").arg(serverInst));
    try
    {
        ZG6000::StringList listDebugLevel;
        bool result = proxyObj->startDebug(listDebugLevel);
        return ZGWebModule::replyObject(result);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_debug_stop(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& serverInst = param.toString();
    auto proxyObj = ZGProxyMng::instance()->getProxyZGServerBase(serverInst);
    if (proxyObj == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取服务实例'%1'代理对象失败").arg(serverInst));
    try
    {
        bool result = proxyObj->stopDebug();
        return ZGWebModule::replyObject(result);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_debug_heart(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& serverInst = param.toString();
    auto proxyObj = ZGProxyMng::instance()->getProxyZGServerBase(serverInst);
    if (proxyObj == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取服务实例'%1'代理对象失败").arg(serverInst));
    try
    {
        bool result = proxyObj->heartDebug();
        return ZGWebModule::replyObject(result);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_local_node_get(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    ZG6000::ListStringMap listRecord;
    const auto& localNodeID = ZGPubFun::getLocalNodeID();
    QString sql = QString("SELECT * FROM sp_param_node WHERE id = '%1'").arg(localNodeID);
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
        return ZGWebModule::errorObject(u8"获取本地节点失败");
    if (listRecord.empty())
        return ZGWebModule::errorObject(u8"本地节点不存在");
    const auto& record = listRecord.front();
    const auto& object = ZGWebModule::stringMapToObject(record);
    return ZGWebModule::replyObject(object);
}

ZGWebModule::Response ZGSPHandle::on_sp_publish_version_get(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    return ZGWebModule::replyObject("1.0");
}

ZGWebModule::Response ZGSPHandle::on_sp_internal_version_get(const QString& clientID,
                                                             const QVariantMap& headers,
                                                             const QJsonValue& param,
                                                             const QHttpServerRequest& req)
{
    auto systemProxy = ZGProxyMng::instance()->getProxySPSystemInfo();
    if (systemProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取系统信息服务代理对象失败"));
    try
    {
        const auto& verison = systemProxy->getVersion();
        return ZGWebModule::replyObject(verison.c_str());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

bool ZGSPHandle::initAlarmLevel()
{
    std::string sql = "SELECT id, color FROM sp_dict_alarm_level";
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql, listRecord))
    {
        ZGLOG_ERROR(QStringLiteral("获取告警级别失败"));
        return false;
    }
    for (const auto& record : listRecord)
    {
        m_mapAlarmLevelColor.insert(std::make_pair(record.at("id"), record.at("color")));
    }
    return true;
}

bool ZGSPHandle::convertRecords(ZG6000::ListStringMap listRecords,
                                QJsonObject& object)
{
    try
    {
        std::unordered_map<std::string, std::unordered_map<std::string, QJsonArray>> mapRecords;
        for (const auto& record : listRecords)
        {
            QJsonObject recordObj;
            for (const auto& [key, value] : record)
            {
                recordObj[key.c_str()] = value.c_str();
            }
            const auto& deviceID = ZGUtils::get(record, "deviceID");
            const auto& propertyName = ZGUtils::get(record, "propertyName");
            mapRecords[deviceID][propertyName].append(recordObj);
        }
        for (auto& [deviceID, deviceRecords] : mapRecords)
        {
            QJsonObject deviceObj;
            for (auto& [propName, records] : deviceRecords)
            {
                deviceObj[propName.c_str()] = std::move(records);
            }
            object[deviceID.c_str()] = std::move(deviceObj);
        }
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

ZGWebModule::Response ZGSPHandle::on_sp_history_store_query(const QString& clientID,
                                                            const QJsonValue& param,
                                                            const QString& type,
                                                            const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"devices", "properties", "startTime", "endTime"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    auto dataManagerPrx = ZGProxyMng::instance()->getProxySPHisDataManager();
    if (dataManagerPrx == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取历史数据管理服务代理对象失败"));
    try
    {
        auto deviceArray = object["devices"].toArray();
        auto propertyArray = object["properties"].toArray();
        ZG6000::StringList listDevice, listProperty;
        for (auto deviceRef : deviceArray)
        {
            listDevice.push_back(deviceRef.toString().toStdString());
        }
        for (auto propertyRef : propertyArray)
        {
            listProperty.push_back(propertyRef.toString().toStdString());
        }
        const auto& startTime = object["startTime"].toString().toStdString();
        const auto& endTime = object["endTime"].toString().toStdString();
        ZG6000::ListStringMap listResult;
        bool result;
        ZG6000::ErrorInfo e;
        if (type == "yc")
            result = dataManagerPrx->queryStoreYc(listDevice, listProperty, startTime, endTime, listResult, e);
        else if (type == "yx")
            result = dataManagerPrx->queryStoreYx(listDevice, listProperty, startTime, endTime, listResult, e);
        else if (type == "text")
            result = dataManagerPrx->queryStoreText(listDevice, listProperty, startTime, endTime, listResult, e);
        else if (type == "ym")
            result = dataManagerPrx->queryStoreYm(listDevice, listProperty, startTime, endTime, listResult, e);
        else
            return ZGWebModule::errorObject(QStringLiteral("不支持的历史数据类型'%1'").arg(type));
        if (!result)
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonObject retObj;
        if (!convertRecords(listResult, retObj))
            return ZGWebModule::errorObject(QStringLiteral("对象转换失败"));
        return ZGWebModule::replyObject(retObj);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

void ZGSPHandle::writeNodeLayer(const std::string& nodeID,
                                QJsonArray& array)
{
    ZG6000::StringMap node;
    std::string sql = "SELECT name, appNodeTypeID, position FROM sp_param_appnode WHERE id = '" + nodeID + "'";
    if (!ZGProxyCommon::execQuerySqlRow(sql, node))
    {
        ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'名称失败").arg(nodeID.c_str()));
        return;
    }
    QJsonObject obj{};
    obj.insert("id", nodeID.c_str());
    obj.insert("text", node["name"].c_str());
    obj.insert("appNodeTypeID", node["appNodeTypeID"].c_str());
    obj.insert("position", node["position"].c_str());
    sql = "SELECT appNodeID FROM sp_param_appnode_layer WHERE parentAppNodeID = '" + nodeID + "' ORDER BY itemIndex";
    ZG6000::StringList listAppNodeID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listAppNodeID))
    {
        ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'层次失败").arg(nodeID.c_str()));
        array.append(obj);
        return;
    }
    if (listAppNodeID.empty())
    {
        array.append(obj);
        return;
    }
    QJsonArray childArray;
    for (const auto& appNodeId : listAppNodeID)
    {
        writeNodeLayer(appNodeId, childArray);
    }
    obj.insert("nodes", childArray);
    array.append(obj);
}

void ZGSPHandle::writeNodeLayer(const std::string& nodeID,
                                const std::string& appNodeTypeID,
                                QJsonArray& array)
{
    ZG6000::StringMap node;
    std::string sql = "SELECT name, appNodeTypeID, position FROM sp_param_appnode WHERE id = '" + nodeID + "'";
    if (!ZGProxyCommon::execQuerySqlRow(sql, node))
    {
        ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'名称失败").arg(nodeID.c_str()));
        return;
    }
    QJsonObject obj{};
    obj.insert("id", nodeID.c_str());
    obj.insert("text", node["name"].c_str());
    obj.insert("appNodeTypeID", node["appNodeTypeID"].c_str());
    obj.insert("position", node["position"].c_str());
    sql = "SELECT appNodeID FROM sp_param_appnode_layer WHERE parentAppNodeID = '" + nodeID + "' ORDER BY itemIndex";
    ZG6000::StringList listAppNodeID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listAppNodeID))
    {
        ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'层次失败").arg(nodeID.c_str()));
        array.append(obj);
        return;
    }
    if (listAppNodeID.empty())
    {
        if (node["appNodeTypeID"] == appNodeTypeID)
            array.append(obj);
        return;
    }
    QJsonArray childArray;
    for (const auto& appNodeId : listAppNodeID)
    {
        writeNodeLayer(appNodeId, appNodeTypeID, childArray);
    }
    obj.insert("nodes", childArray);
    array.append(obj);
}

bool ZGSPHandle::getParamRecords(const QJsonValue& param,
                                 ZG6000::StringList& listID,
                                 ZG6000::ListStringMap& listRecord)
{
    if (!param.isArray())
        return false;
    const auto& paramArray = param.toArray();
    for (const auto value : paramArray)
    {
        const auto& recordObj = value.toObject();
        auto it = recordObj.find("id");
        if (it == recordObj.end())
            return false;
        ZG6000::StringMap record;
        it = recordObj.begin();
        while (it != recordObj.end())
        {
            QString key = it.key();
            QString val = it.value().toString();
            if (key == "id")
                listID.push_back(val.toStdString());
            else
                record.insert(std::make_pair(key.toStdString(), val.toStdString()));
            ++it;
        }
        listRecord.push_back(record);
    }
    return true;
}

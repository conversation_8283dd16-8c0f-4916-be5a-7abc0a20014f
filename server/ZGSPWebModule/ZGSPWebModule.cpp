#include "ZGSPWebModule.h"
#include "ZGSPHandle.h"
#include "ZGSPClientHandle.h"

ZGSPWebModule::ZGSPWebModule(QObject *parent)
    : ZGWebModule(parent)
{
    m_pHandle = new ZGSPHandle(this);
    m_pClientHandle = new ZGSPClientHandle(this);
    registerHandle("sp/system/param/get", m_pHandle, &ZGSPHandle::on_sp_system_param_get);
    registerHandle("sp/time/sync", m_pHandle, &ZGSPHandle::on_sp_time_sync);
    registerHandle("sp/mqtt/server/get", m_pHandle, &ZGSPHandle::on_sp_mqtt_server_get);
    registerHandle("sp/rule/invoke", m_pHandle, &ZGSPHandle::on_sp_rule_invoke);
    registerHandle("sp/event/confirm", m_pHandle, &ZGSPHandle::on_sp_event_confirm);
    registerHandle("sp/event/get", m_pHandle, &ZGSPHandle::on_sp_event_get);
    registerHandle("sp/event/publish", m_pHandle, &ZGSPHandle::on_sp_event_publish);
    registerHandle("sp/server/reboot", m_pHandle, &ZGSPHandle::on_sp_server_reboot);
    registerHandle("sp/node/list", m_pHandle, &ZGSPHandle::on_sp_node_list);
    registerHandle("sp/history/table/count", m_pHandle, &ZGSPHandle::on_sp_history_table_count);
    registerHandle("sp/history/table/query", m_pHandle, &ZGSPHandle::on_sp_history_table_query);
    registerHandle("sp/history/log/query", m_pHandle, &ZGSPHandle::on_sp_history_log_query);
    registerHandle("sp/history/store/yc/query", m_pHandle, &ZGSPHandle::on_sp_history_store_yc_query);
    registerHandle("sp/history/store/yx/query", m_pHandle, &ZGSPHandle::on_sp_history_store_yx_query);
    registerHandle("sp/history/store/text/query", m_pHandle, &ZGSPHandle::on_sp_history_store_text_query);
    registerHandle("sp/history/store/ym/query", m_pHandle, &ZGSPHandle::on_sp_history_store_ym_query);
    registerHandle("sp/appnode/layer/get", m_pHandle, &ZGSPHandle::on_sp_appnode_layer_get);
    registerHandle("sp/appnode/type/layer/get", m_pHandle, &ZGSPHandle::on_sp_appnode_type_layer_get);
    registerHandle("sp/appnode/user/add", m_pHandle, &ZGSPHandle::on_sp_appnode_user_add);
    registerHandle("sp/appnode/pos/event/get", m_pHandle, &ZGSPHandle::on_sp_appnode_pos_event_get);
    registerHandle("sp/appnode/pos/eventnum/get", m_pHandle, &ZGSPHandle::on_sp_appnode_pos_eventnum_get);
    registerHandle("sp/exam/create", m_pHandle, &ZGSPHandle::on_sp_exam_create);
    registerHandle("sp/exam/info", m_pHandle, &ZGSPHandle::on_sp_exam_info);
    registerHandle("sp/exam/step/exec", m_pHandle, &ZGSPHandle::on_sp_exam_step_exec);
    registerHandle("sp/exam/delete", m_pHandle, &ZGSPHandle::on_sp_exam_delete);
    registerHandle("sp/exam/finish", m_pHandle, &ZGSPHandle::on_sp_exam_finish);
    registerHandle("sp/debug/start", m_pHandle, &ZGSPHandle::on_sp_debug_start);
    registerHandle("sp/debug/stop", m_pHandle, &ZGSPHandle::on_sp_debug_stop);
    registerHandle("sp/debug/heart", m_pHandle, &ZGSPHandle::on_sp_debug_heart);
    registerHandle("sp/local/node/get", m_pHandle, &ZGSPHandle::on_sp_local_node_get);
    registerHandle("sp/client/list", m_pClientHandle, &ZGSPClientHandle::on_sp_client_list);
    registerHandle("sp/client/info", m_pClientHandle, &ZGSPClientHandle::on_sp_client_info);
    registerHandle("sp/client/update", m_pClientHandle, &ZGSPClientHandle::on_sp_client_update);
    registerHandle("sp/client/activate", m_pClientHandle, &ZGSPClientHandle::on_sp_client_activate);
    registerHandle("sp/client/delete", m_pClientHandle, &ZGSPClientHandle::on_sp_client_delete);
    registerHandle("sp/client/auth/update", m_pClientHandle, &ZGSPClientHandle::on_sp_client_auth_update);
    registerHandle("sp/client/verify", m_pClientHandle, &ZGSPClientHandle::on_sp_client_verify);
    registerHandle("sp/client/state/switch", m_pClientHandle, &ZGSPClientHandle::on_sp_client_state_switch);
    registerHandle("sp/client/switch/allow", m_pClientHandle, &ZGSPClientHandle::on_sp_client_switch_allow);
    registerHandle("sp/client/unbind", m_pClientHandle, &ZGSPClientHandle::on_sp_client_unbind);
    registerHandle("sp/client/unregister", m_pClientHandle, &ZGSPClientHandle::on_sp_client_unregister);
    registerHandle("sp/user/password/verify", m_pClientHandle, &ZGSPClientHandle::on_sp_user_password_verify);
    registerHandle("sp/user/card/verify", m_pClientHandle, &ZGSPClientHandle::on_sp_user_card_verify);
    registerHandle("sp/user/auth/dev/verify", m_pClientHandle, &ZGSPClientHandle::on_sp_user_auth_dev_verify);
    registerHandle("sp/user/get", m_pClientHandle, &ZGSPClientHandle::on_sp_users_get);
    registerHandle("sp/user/list", m_pClientHandle, &ZGSPClientHandle::on_sp_user_list);
    registerHandle("sp/user/info", m_pClientHandle, &ZGSPClientHandle::on_sp_user_info);
    registerHandle("sp/user/add", m_pClientHandle, &ZGSPClientHandle::on_sp_user_add);
    registerHandle("sp/user/update", m_pClientHandle, &ZGSPClientHandle::on_sp_user_update);
    registerHandle("sp/user/delete", m_pClientHandle, &ZGSPClientHandle::on_sp_user_delete);
    registerHandle("sp/card/check", m_pClientHandle, &ZGSPClientHandle::on_sp_card_use);
    registerHandle("sp/card/list", m_pClientHandle, &ZGSPClientHandle::on_sp_card_list);
    registerHandle("sp/user/password/change", m_pClientHandle, &ZGSPClientHandle::on_sp_user_password_change);
    registerHandle("sp/user/password/force/change", m_pClientHandle, &ZGSPClientHandle::on_sp_user_password_force_change);
    registerHandle("sp/user/password/reset", m_pClientHandle, &ZGSPClientHandle::on_sp_user_password_reset);
    registerHandle("sp/user/card/add", m_pClientHandle, &ZGSPClientHandle::on_sp_user_card_add);
    registerHandle("sp/user/card/delete", m_pClientHandle, &ZGSPClientHandle::on_sp_user_card_delete);
    registerHandle("sp/user/fingers/get", m_pClientHandle, &ZGSPClientHandle::on_sp_user_fingers_get);
    registerHandle("sp/user/finger/update", m_pClientHandle, &ZGSPClientHandle::on_sp_user_finger_update);
    registerHandle("sp/user/finger/delete", m_pClientHandle, &ZGSPClientHandle::on_sp_user_finger_delete);
    registerHandle("sp/user/face/get", m_pClientHandle, &ZGSPClientHandle::on_sp_user_face_get);
    registerHandle("sp/user/face/update", m_pClientHandle, &ZGSPClientHandle::on_sp_user_face_update);
    registerHandle("sp/user/face/delete", m_pClientHandle, &ZGSPClientHandle::on_sp_user_face_delete);
    registerHandle("sp/user/sync", m_pClientHandle, &ZGSPClientHandle::on_sp_user_sync);
    registerHandle("sp/user/password/send", m_pClientHandle, &ZGSPClientHandle::on_sp_user_password_send);
    registerHandle("sp/user/login", m_pClientHandle, &ZGSPClientHandle::on_sp_user_login);
    registerHandle("sp/user/verify/login", m_pClientHandle, &ZGSPClientHandle::on_sp_user_verify_login);
    registerHandle("sp/user/verify/send", m_pClientHandle, &ZGSPClientHandle::on_sp_user_verify_send);
    registerHandle("sp/user/logout", m_pClientHandle, &ZGSPClientHandle::on_sp_user_logout);
    registerHandle("sp/role/power/list", m_pClientHandle, &ZGSPClientHandle::on_sp_role_power_list);
    registerHandle("sp/role/power/edit", m_pClientHandle, &ZGSPClientHandle::on_sp_role_power_edit);
    registerHandle("sp/role/delete", m_pClientHandle, &ZGSPClientHandle::on_sp_role_delete);
    registerHandle("sp/company/add", m_pClientHandle, &ZGSPClientHandle::on_sp_company_add);
    registerHandle("sp/company/update", m_pClientHandle, &ZGSPClientHandle::on_sp_company_update);
    registerHandle("sp/company/delete", m_pClientHandle, &ZGSPClientHandle::on_sp_company_delete);
    registerHandle("sp/department/add", m_pClientHandle, &ZGSPClientHandle::on_sp_department_add);
    registerHandle("sp/department/update", m_pClientHandle, &ZGSPClientHandle::on_sp_department_update);
    registerHandle("sp/department/delete", m_pClientHandle, &ZGSPClientHandle::on_sp_department_delete);
    registerHandle("sp/role/add", m_pClientHandle, &ZGSPClientHandle::on_sp_role_add);
    registerHandle("sp/role/update", m_pClientHandle, &ZGSPClientHandle::on_sp_role_update);
    registerHandle("sp/public/version/get", m_pHandle, &ZGSPHandle::on_sp_publish_version_get);
    registerHandle("sp/internal/version/get", m_pHandle, &ZGSPHandle::on_sp_internal_version_get);
}

bool ZGSPWebModule::initialize()
{
    if (!m_pHandle->initialize())
        return false;
    return true;
}

QString ZGSPWebModule::prefix()
{
    return "sp";
}

#ifndef ZGSPHANDLE_H
#define ZGSPHANDLE_H

#include <QObject>
#include <QJsonObject>
#include "ZGWebModule.h"
#include "ZGServerCommon.h"

class ZGSPHandle : public QObject
{
    Q_OBJECT

public:
    explicit ZGSPHandle(QObject* parent = nullptr);
    bool initialize();

public:
    ZGWebModule::Response on_sp_system_param_get(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_time_sync(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_mqtt_server_get(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_rule_invoke(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_event_confirm(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_event_get(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_event_publish(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_node_list(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_server_reboot(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_history_table_count(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_history_table_query(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_history_log_query(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_history_store_yc_query(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_history_store_yx_query(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_history_store_text_query(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_history_store_ym_query(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_appnode_layer_get(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_appnode_type_layer_get(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_appnode_yv_get(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_appnode_user_add(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_appnode_pos_event_get(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_appnode_pos_eventnum_get(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_exam_create(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_exam_info(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_exam_step_exec(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_exam_delete(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_exam_finish(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_debug_start(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_debug_stop(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_debug_heart(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_local_node_get(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_publish_version_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);
    ZGWebModule::Response on_sp_internal_version_get(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req);

private:
    bool initAlarmLevel();
    bool convertRecords(ZG6000::ListStringMap listRecords,
                        QJsonObject& object);
    ZGWebModule::Response on_sp_history_store_query(const QString& clientID,
                                                    const QJsonValue& param,
                                                    const QString& type,
                                                    const QHttpServerRequest& req);
    void writeNodeLayer(const std::string& nodeID,
                        QJsonArray& array);
    void writeNodeLayer(const std::string& nodeID,
                        const std::string& appNodeTypeID,
                        QJsonArray& array);

private:
    static bool getParamRecords(const QJsonValue& param,
                                ZG6000::StringList& listID,
                                ZG6000::ListStringMap& listRecord);
    std::unordered_map<std::string, std::string> m_mapAlarmLevelColor;
};

#endif // ZGSPHANDLE_H

#ifndef ZG6000_ZGMPDEVICEPROPERTYPUBLISH_H
#define ZG6000_ZGMPDEVICEPROPERTYPUBLISH_H

#include <QObject>
#include <ZGServerCommon.h>

namespace ZG6000 {

class ZGMPDevicePropertyPublish : public QObject
{
    Q_OBJECT
public:
    explicit ZGMPDevicePropertyPublish(QObject *parent = nullptr);
    bool initialize();
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord);

signals:

};

} // namespace ZG6000

#endif // ZG6000_ZGMPDEVICEPROPERTYPUBLISH_H

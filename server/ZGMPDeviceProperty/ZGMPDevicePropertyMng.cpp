#include "ZGMPDevicePropertyMng.h"

#include <QtConcurrent>
#include <QFuture>
#include <QRandomGenerator>
#include <QThread>

#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGMPDeviceBlock.h"
#include "ZGMPDevicePropertyPublish.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "zgerror/ZGMPDevicePropertyError.h"

namespace ZG6000
{
    ZGMPDevicePropertyMng* ZGMPDevicePropertyMng::instance()
    {
        if (g_pInstance == nullptr)
            g_pInstance = new ZGMPDevicePropertyMng;
        return g_pInstance;
    }

    void ZGMPDevicePropertyMng::init()
    {
        initEvents();
        initDeviceFields();
        initServerInstConfig();
        while (!initServerInstInfo())
        {
            ZGLOG_ERROR("initServerInstInfo error.");
            QThread::sleep(m_initInterval);
        }
        QThread::sleep(QRandomGenerator::global()->bounded(1, 5));
        while (!initOtherParam())
        {
            ZGLOG_ERROR("initDictParam error.");
            QThread::sleep(m_initInterval);
        }
        while (!initDataCategoryParam())
        {
            ZGLOG_ERROR("initDataCategoryParam error.");
            QThread::sleep(m_initInterval);
        }
        while (!initModelParam())
        {
            ZGLOG_ERROR("initModelParam error.");
            QThread::sleep(m_initInterval);
        }
        while (!initDeviceParam())
        {
            ZGLOG_ERROR("initDeviceParam error.");
            QThread::sleep(m_initInterval);
        }
        while (!initDatasetProperties())
        {
            ZGLOG_ERROR("initDatasetProperties error.");
            QThread::sleep(m_initInterval);
        }
        // m_pDeviceBlock = new ZGMPDeviceBlock;
        // while (!m_pDeviceBlock->initialize())
        // {
        //     ZGLOG_ERROR("Device block initialize error.");
        //     QThread::sleep(m_initInterval);
        // }
        m_pPropertyPublish = new ZGMPDevicePropertyPublish;
        while (!m_pPropertyPublish->initialize())
        {
            ZGLOG_ERROR("Device property publish initialize error.");
            QThread::sleep(m_initInterval);
        }
        while (!initMqttClient())
        {
            ZGLOG_ERROR("initMqttClient error.");
            QThread::sleep(m_initInterval);
        }
        while (!initRedisClient())
        {
            ZGLOG_ERROR("initRedisClient error.");
            QThread::sleep(m_initInterval);
        }
        m_initialized = true;
        ZGLOG_INFO("ZGMPDeviceProperty init finished.");
        m_timer.start(1000);
    }

    bool ZGMPDevicePropertyMng::checkState()
    {
        return m_initialized;
    }

    void ZGMPDevicePropertyMng::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord)
    {
        if (!m_initialized)
            return;
        // if (tableName == "sp_param_appnode" || tableName == "mp_param_appnode_vol_level")
        //     m_pDeviceBlock->dispatchData(std::move(tableName), std::move(oper), std::move(reason), std::move(time), std::move(listRecord));
        // else
        {
            if (tableName == "mp_param_device")
                processFieldChange(std::move(listRecord));
            else
                processDataChange(std::move(tableName), std::move(listRecord));
        }
    }

    bool ZGMPDevicePropertyMng::isAllowCtrl(std::string deviceID, bool& allow, StringMap& conditions, ErrorInfo& e)
    {
        if (!m_initialized)
            return false;
        return true;
        // return m_pDeviceBlock->isAllowCtrl(deviceID, allow, conditions, e);
    }

    bool ZGMPDevicePropertyMng::mgetPropertiesAll(StringList listDeviceID, MapMapStringMap& mapProperties, ErrorInfo& e)
    {
        if (listDeviceID.empty())
            return true;
        if (!mgetPropertiesFromFields(listDeviceID, mapProperties, e))
            return false;
        std::map<std::string, StringList> mapTableDataID;
        for (const auto& deviceID: listDeviceID)
        {
            auto pair = m_mapDeviceProperties.find(deviceID);
            if (pair == m_mapDeviceProperties.end())
            {
                ZGLOG_DEBUG(QStringLiteral("设备'%1'无可用的属性").arg(deviceID.c_str()));
                continue;
            }
            const auto& listPropertyName = pair->second;
            for (const auto& propertyName: listPropertyName)
            {
                const auto& [tableName, dataID] = m_mapPropertyData[{deviceID, propertyName}];
                mapTableDataID[tableName].push_back(dataID);
            }
        }
        for (auto& [tableName, listDataID]: mapTableDataID)
        {
            std::string modelName = tableName;
            ZGUtils::replaceString(modelName, "dataset", "model");
            ListStringMap listProperties;
            if (!mgetDatasetProperties(tableName, std::move(listDataID), m_mapDataFields[tableName],
                m_mapModelFields[modelName], listProperties, e))
                return false;
            for (auto& properties: listProperties)
            {
                const auto& [deviceID, name] = m_mapDataProperty[properties["id"]];
                mapProperties[deviceID][name] = std::move(properties);
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::mgetProperties(StringList listDeviceID, StringList listName, MapMapStringMap& mapProperties, ErrorInfo& e)
    {
        if (listDeviceID.empty())
            return true;
        if (listName.empty())
            return true;
        StringList listDeviceField, listDataField;
        splitPropertyNames(std::move(listName), listDeviceField, listDataField);
        if (!getDevicesFieldProperties(listDeviceID, listDeviceField, [&](const std::string& deviceID, const std::string& name, std::string&& value)
        {
            mapProperties[deviceID][name] = {{"value", std::move(value)}};
        }, e))
            return false;
        if (!listDataField.empty())
        {
            std::map<std::string, StringList> mapTableData;
            if (!getDevicesTableData(listDeviceID, listDataField, mapTableData, true, e))
                return false;
            for (const auto& [table, listDataID]: mapTableData)
            {
                std::string modelTable = table;
                ZGUtils::replaceString(modelTable, "dataset", "model");
                ListStringMap listProperties;
                if (!mgetDatasetProperties(table, listDataID, m_mapDataFields[table],
                    m_mapModelFields[modelTable], listProperties, e))
                    return false;
                for (size_t i = 0; i < listDataID.size(); ++i)
                {
                    auto pair = m_mapDataProperty.find(listDataID[i]);
                    if (pair == m_mapDataProperty.end())
                    {
                        e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                        e.errDetail = QStringLiteral("找不到表'%1'数据'%2'关联的设备属性").arg(table.c_str()).arg(listDataID[i].c_str()).toStdString();
                        ZGLOG_ERROR(e);
                        return false;
                    }
                    const auto& [deviceID, name] = pair->second;
                    listProperties[i]["tableName"] = table;
                    mapProperties[deviceID][name] = std::move(listProperties[i]);
                }
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& mapValues, ErrorInfo& e)
    {
        return mgetPropertyValues(std::move(listDeviceID), std::move(listName), mapValues, true, e);
    }

    bool ZGMPDevicePropertyMng::mgetPropertyValuesEx(StringList listDeviceID, StringList listName, MapStringMap& mapValues, ErrorInfo& e)
    {
        return mgetPropertyValues(std::move(listDeviceID), std::move(listName), mapValues, false, e);
    }

    bool ZGMPDevicePropertyMng::getGroupProperties(std::string deviceID, MapMapStringMap& mapProperties, ErrorInfo& e)
    {
        MapStringMap properties;
        if (!getPropertiesAll(deviceID, properties, e))
            return false;
        StringMap tableType;
        for (const auto& tableName: m_listDataTable)
        {
            size_t pos = tableName.find_last_of("_");
            std::string typeName = tableName.substr(pos + 1);
            mapProperties[typeName] = {};
            tableType[tableName] = typeName;
        }
        for (auto& [name, property]: properties)
        {
            auto pairField = m_mapPrimaryDevField.find(name);
            if (pairField != m_mapPrimaryDevField.end())
            {
                property["name"] = pairField->second;
                mapProperties["dev"][name] = std::move(property);
                continue;
            }
            pairField = m_mapSecondDevField.find(name);
            if (pairField != m_mapSecondDevField.end())
            {
                property["name"] = pairField->second;
                mapProperties["dev"][name] = std::move(property);
                continue;
            }
            auto pair = m_mapPropertyData.find({deviceID, name});
            if (pair == m_mapPropertyData.end())
                continue;
            const auto& tableName = pair->second.first;
            mapProperties[tableType[tableName]][name] = std::move(property);
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::getTableProperties(std::string deviceID, std::string tableName, MapStringMap& properties, ErrorInfo& e)
    {
        std::string modelName = tableName;
        ZGUtils::replaceString(modelName, "dataset", "model");
        QString sql = QString("SELECT a.id FROM mp_param_dataset_yx a "
            "LEFT JOIN mp_param_model_yx b ON a.dataModelID = b.id "
            "LEFT JOIN mp_param_device c ON a.deviceID = c.id "
            "WHERE b.isEnable = 1 AND c.isEnable = 1 AND c.id = '%1' ORDER BY a.id").arg(deviceID.c_str());
        StringList listDataID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDataID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取设备'%1'表'%2'数据ID失败").arg(deviceID.c_str()).arg(tableName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ListStringMap listProperties;
        if (!mgetDatasetProperties(tableName, listDataID, m_mapDataFields[tableName],
            m_mapModelFields[modelName], listProperties, e))
            return false;
        for (size_t i = 0; i < listDataID.size(); ++i)
        {
            auto pair = m_mapDataProperty.find(listDataID[i]);
            if (pair == m_mapDataProperty.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到表'%1'数据'%2'关联的设备属性").arg(tableName.c_str()).arg(listDataID[i].c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& [id, name] = pair->second;
            properties[name] = std::move(listProperties[i]);
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::getPropertiesAll(std::string deviceID, MapStringMap& properties, ErrorInfo& e)
    {
        MapMapStringMap mapProperties;
        if (!mgetPropertiesAll({deviceID}, mapProperties, e))
            return false;
        properties = std::move(mapProperties[deviceID]);
        return true;
    }

    bool ZGMPDevicePropertyMng::getProperties(std::string deviceID, StringList listName, MapStringMap& properties, ErrorInfo& e)
    {
        MapMapStringMap mapProperties;
        if (!mgetProperties({deviceID}, std::move(listName), mapProperties, e))
            return false;
        properties = std::move(mapProperties[std::move(deviceID)]);
        return true;
    }

    bool ZGMPDevicePropertyMng::getProperty(std::string deviceID, std::string name, StringMap& property, ErrorInfo& e)
    {
        MapStringMap properties;
        if (!getProperties(std::move(deviceID), {name}, properties, e))
            return false;
        property = std::move(properties[std::move(name)]);
        return true;
    }

    bool ZGMPDevicePropertyMng::getPropertyValues(std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e)
    {
        MapStringMap mapValues;
        if (!mgetPropertyValues({deviceID}, std::move(listName), mapValues, e))
            return false;
        values = std::move(mapValues[std::move(deviceID)]);
        return true;
    }

    bool ZGMPDevicePropertyMng::getPropertyValuesEx(std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e)
    {
        MapStringMap mapValues;
        if (!mgetPropertyValuesEx({deviceID}, std::move(listName), mapValues, e))
            return false;
        values = std::move(mapValues[std::move(deviceID)]);
        return true;
    }

    // 单独处理以提高速度
    bool ZGMPDevicePropertyMng::getPropertyValue(std::string deviceID, std::string name, std::string& value, ErrorInfo& e)
    {
        if (m_mapPrimaryDevField.find(name) != m_mapPrimaryDevField.end() || m_mapSecondDevField.find(name) != m_mapSecondDevField.end())
        {
            if (!ZGProxyCommon::getDataByField("mp_param_device", deviceID, name, value))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取设备'%1'字段属性'%2'失败").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        else
        {
            auto pair = m_mapPropertyData.find({deviceID, name});
            if (pair == m_mapPropertyData.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (!ZGProxyCommon::getDataByField(pair->second.first, pair->second.second, "rtNewValue", value))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取设备'%1'数据属性'%2'失败").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::getPropertyValueEx(std::string deviceID, std::string name, std::string& value, ErrorInfo& e)
    {
        StringMap values;
        if (!getPropertyValuesEx(std::move(deviceID), {name}, values, e))
            return false;
        value = std::move(values[name]);
        return true;
    }

    bool ZGMPDevicePropertyMng::mupdateProperties(MapMapStringMap mapProperties, bool saveToDB, ErrorInfo& e)
    {
        if (mapProperties.empty())
            return true;
        if (saveToDB)
        {
            if (!mupdatePropertiesToDb(std::move(mapProperties), true, e))
                return false;
            return true;
        }
        return mupdatePropertiesToRt(std::move(mapProperties), true, e);
    }

    bool ZGMPDevicePropertyMng::mupdatePropertyValues(MapStringMap mapValues, bool saveToDB, ErrorInfo& e)
    {
        if (mapValues.empty())
            return true;
        if (saveToDB)
        {
            if (!mupdatePropertyValuesToDb(mapValues, true, e))
                return false;
            return true;
        }
        return mupdatePropertyValuesToRt(std::move(mapValues), true, e);
    }

    bool ZGMPDevicePropertyMng::mupdatePropertyValuesEx(MapStringMap mapValues, bool saveToDB, ErrorInfo& e)
    {
        if (mapValues.empty())
            return true;
        if (saveToDB)
        {
            if (!mupdatePropertyValuesToDb(std::move(mapValues), false, e))
                return false;
            return true;
        }
        return mupdatePropertyValuesToRt(std::move(mapValues), false, e);
    }

    bool ZGMPDevicePropertyMng::updateProperty(std::string deviceID, std::string name, StringMap property, bool saveToDB, ErrorInfo& e)
    {
        MapStringMap properties{{std::move(name), std::move(property)}};
        return updateProperties(std::move(deviceID), std::move(properties), saveToDB, e);
    }

    bool ZGMPDevicePropertyMng::updateProperties(std::string deviceID, MapStringMap properties, bool saveToDB, ErrorInfo& e)
    {
        MapMapStringMap mapProperties{{deviceID, std::move(properties)}};
        return mupdateProperties(std::move(mapProperties), saveToDB, e);
    }

    bool ZGMPDevicePropertyMng::updatePropertyValues(std::string deviceID, StringMap values, bool saveToDB, ErrorInfo& e)
    {
        MapStringMap mapValues{{deviceID, values}};
        return mupdatePropertyValues(std::move(mapValues), saveToDB, e);
    }

    bool ZGMPDevicePropertyMng::updatePropertyValuesEx(std::string deviceID, StringMap values, bool saveToDB, ErrorInfo& e)
    {
        MapStringMap mapValues{{deviceID, values}};
        return mupdatePropertyValuesEx(std::move(mapValues), saveToDB, e);
    }

    bool ZGMPDevicePropertyMng::updatePropertyValue(std::string deviceID, std::string name, std::string value, bool saveToDB, ErrorInfo& e)
    {
        if (m_mapPrimaryDevField.find(name) != m_mapPrimaryDevField.end() ||
            m_mapSecondDevField.find(name) != m_mapSecondDevField.end())
        {
            if (saveToDB)
            {
                StringMap fieldParam{{"id", deviceID}, {name, value}};
                const auto& sql = ZGUtils::generateUpdateSql("mp_param_device", fieldParam);
                if (!ZGProxyCommon::execSql(sql))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                    e.errDetail = QStringLiteral("更新设备'%1'字段'%2'属性失败").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                return true;
            }
            if (!ZGProxyCommon::updateDataByField("mp_param_device", deviceID, name, value))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新设备'%1'字段'%2'属性失败").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        else
        {
            std::string currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
            if (saveToDB)
            {
                auto pair = m_mapPropertyData.find({deviceID, name});
                if (pair == m_mapPropertyData.end())
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                const auto& [tableName, dataID] = pair->second;
                StringMap dataParam{{"id", dataID}};
                dataParam["rtNewValue"] = value;
                dataParam["rtUpdateTime"] = currentTime;
                std::string sql = ZGUtils::generateUpdateSql(tableName, std::move(dataParam));
                if (!ZGProxyCommon::execSql(sql))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                    e.errDetail = QStringLiteral("更新设备属性失败").toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                return true;
            }
            auto pair = m_mapPropertyData.find({deviceID, name});
            if (pair == m_mapPropertyData.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据ID").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& [tableName, dataID] = pair->second;
            if (!ZGProxyCommon::updateDataByID(tableName, dataID, {{"rtNewValue", value}, {"rtUpdateTime", currentTime}}))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新表'%1'数据'%2'值失败").arg(tableName.c_str()).arg(dataID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::updatePropertyValueEx(std::string deviceID, std::string name, std::string value, bool saveToDB, ErrorInfo& e)
    {
        StringMap values{{name, value}};
        return updatePropertyValuesEx(std::move(deviceID), std::move(values), saveToDB, e);
    }

    bool ZGMPDevicePropertyMng::getDataIDByProperty(std::string deviceID, std::string name, std::string& tableName, std::string& dataID, ErrorInfo& e)
    {
        auto pair = m_mapPropertyData.find({deviceID, name});
        if (pair == m_mapPropertyData.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据ID").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        tableName = pair->second.first;
        dataID = pair->second.second;
        return true;
    }

    bool ZGMPDevicePropertyMng::getPropertyByDataID(std::string dataID, std::string& deviceID, std::string& name, ErrorInfo& e)
    {
        auto pair = m_mapDataProperty.find(dataID);
        if (pair == m_mapDataProperty.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到数据'%2'关联的设备属性").arg(dataID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        deviceID = pair->second.first;
        name = pair->second.second;
        return true;
    }

    bool ZGMPDevicePropertyMng::isPropertyExists(std::string deviceID, std::string name)
    {
        if (m_mapPrimaryDevField.find(name) != m_mapPrimaryDevField.end() ||
            m_mapSecondDevField.find(name) != m_mapSecondDevField.end())
            return true;
        return (m_mapPropertyData.find({deviceID, name}) != m_mapPropertyData.end());
    }

    ZGMPDevicePropertyMng::ZGMPDevicePropertyMng(QObject* parent)
        : QObject{parent}
    {
    }

    void ZGMPDevicePropertyMng::initDeviceFields()
    {
        m_mapPrimaryDevField = {
            {"name", u8"名称"}, {"nameL2", u8"名称2"}, {"typeID", u8"类型"}, {"subtypeID", u8"子类型"}, {"appNodeID", u8"站点"}, {"subsystemID", u8"子系统"},
            {"majorID", u8"专业"}, {"volLevelID", u8"电压等级"}, {"position", u8"位置"}
        };
        m_mapSecondDevField = {
            {"name", u8"名称"}, {"nameL2", u8"名称2"}, {"typeID", u8"类型"}, {"subtypeID", u8"子类型"}, {"appNodeID", u8"站点"}, {"subsystemID", u8"子系统"}, {"position", u8"位置"},
            {"majorID", u8"专业"}, {"rtState", u8"网络状态"}, {"aNetAddr", u8"A网地址"}, {"bNetAddr", u8"B网地址"}, {"rtANetState", u8"A网状态"}, {"rtBNetState", u8"B网状态"},
            {"rtCNetState", u8"C网状态"}, {"rtDNetState", u8"D网状态"}, {"rtHeartTime", u8"心跳时间"}, {"rtMasterState", u8"主备状态"},
            {"volLevelID", u8"电压等级"}
        };
    }

    void ZGMPDevicePropertyMng::initEvents()
    {
        connect(&m_timer, &QTimer::timeout, this, &ZGMPDevicePropertyMng::onTimer);
    }

    void ZGMPDevicePropertyMng::initServerInstConfig()
    {
        const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
        QString errMsg;
        int value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
            ZGLOG_WARN(errMsg);
        else
            m_initInterval = value;
    }

    bool ZGMPDevicePropertyMng::initServerInstInfo()
    {
        m_serverName = ZGRuntime::instance()->getServerID();
        if (m_serverName.isEmpty())
        {
            ZGLOG_ERROR("Empty server id.");
            return false;
        }
        m_instName = ZGRuntime::instance()->getInstanceID();
        if (m_instName.isEmpty())
        {
            ZGLOG_ERROR("Empty server instance id.");
            return false;
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::initDataCategoryParam()
    {
        std::string sql = "SELECT * FROM mp_param_data_category_property";
        ZG6000::ListStringMap listRecord;
        if (!ZGProxyCommon::execQuerySql(sql, listRecord))
        {
            ZGLOG_ERROR(QStringLiteral("获取数据类别属性失败"));
            return false;
        }
        try
        {
            for (const auto& record: listRecord)
            {
                const auto& dataCategoryId = ZGUtils::get(record, "dataCategoryID");
                const auto& propValue = ZGUtils::get(record, "propValue");
                const auto& propName = ZGUtils::get(record, "propName");
                const auto& propNameL2 = ZGUtils::get(record, "propNameL2");
                const auto& key = dataCategoryId + "/" + propValue;
                m_mapDataCategoryProp[key]["propName"] = propName;
                m_mapDataCategoryProp[key]["propNameL2"] = propNameL2;
            }
            return true;
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
            return false;
        }
    }

    bool ZGMPDevicePropertyMng::initDeviceParam()
    {
        QString sql = QString("SELECT * FROM mp_param_device WHERE isEnable = '1'");
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapDeviceParam))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备参数失败"));
            return false;
        }
        for (const auto& [deviceID, deviceParam]: m_mapDeviceParam)
        {
            int interval = std::atoi(ZGUtils::get(deviceParam, "publishInterval", "0").c_str());
            if (interval > 0)
                m_mapDeviceTickCount[deviceID] = QRandomGenerator::global()->bounded(1, interval);
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::initModelParam()
    {
        for (const auto& tableName: m_listDataTable)
        {
            MapStringMap mapProperty;
            std::string modelTable = tableName;
            ZGUtils::replaceString(modelTable, "dataset", "model");
            if (!initModelTableParam(modelTable, mapProperty))
                return false;
            m_mapModelPointParam.merge(mapProperty);
        }
        ZGLOG_DEBUG(QString("mapModelPointParam size: %1").arg(m_mapModelPointParam.size()));
        return true;
    }

    bool ZGMPDevicePropertyMng::initOtherParam()
    {
        std::string sql = "SELECT id, name, nameL2 FROM sp_dict_data_unit";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapDataUnit))
        {
            ZGLOG_ERROR(QStringLiteral("获取数据单位参数失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM mp_dict_device_type";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapDeviceType))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备类型参数失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM mp_dict_device_subtype";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapDeviceSubtype))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备子类型参数失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 from sp_param_appnode";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapAppNode))
        {
            ZGLOG_ERROR(QStringLiteral("获取应用节点参数失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 from sp_param_subsystem";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapSubsystem))
        {
            ZGLOG_ERROR(QStringLiteral("获取子系统参数失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 from sp_param_major";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapMajor))
        {
            ZGLOG_ERROR(QStringLiteral("获取专业参数失败"));
            return false;
        }
        sql = "SELECT id, name FROM mp_dict_auth_pos";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapAuthPos))
            return false;
        QMap<QString, QString> mapCfgParam;
        QList<QMap<QString, QString>> listMapParam;
        if (ZGPubFun::getParamCfgDB(mapCfgParam, listMapParam))
        {
            auto it = mapCfgParam.find("db_max_record");
            if (it != mapCfgParam.end())
                m_maxRecordCount = it.value().toUInt();
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::initDatasetProperties()
    {
        m_mapDeviceProperties.clear();
        m_mapDataParam.clear();
        m_mapDataProperty.clear();
        m_mapPropertyData.clear();
        for (const auto& tableName: m_listDataTable)
        {
            ZGLOG_DEBUG(QString("tableName: %1").arg(tableName.c_str()));
            if (!initDatasetTableProperties(tableName))
                return false;
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::initMqttClient()
    {
        if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
        {
            ZGLOG_ERROR("initMqttClient error.");
            return false;
        }
        m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
        if (m_pMqttClient == nullptr)
        {
            ZGLOG_ERROR("getMqttClientMessage return null.");
            return false;
        }
        m_pMqttClient->connectToHost();
        return true;
    }

    bool ZGMPDevicePropertyMng::initRedisClient()
    {
        QList<ZGRuntime::REDIS_CLIENT_TYPE> listType;
        listType << ZGRuntime::REDIS_RT_TOPIC;
        if (!ZGRuntime::instance()->initRedisClient(listType))
        {
            ZGLOG_ERROR("initRedisClient error.");
            return false;
        }
        m_pRedisClient = ZGRuntime::instance()->getRedisClientRTTopic();
        if (m_pRedisClient == nullptr)
        {
            ZGLOG_ERROR("getRedisClientRTTopic error.");
            return false;
        }
        return true;
    }

    void ZGMPDevicePropertyMng::cacheDatasetProperties(const std::string& tableName, ListStringMap listResult)
    {
        for (auto& result: listResult)
        {
            auto pairModel = m_mapModelPointParam.find(result["dataModelID"]);
            if (pairModel == m_mapModelPointParam.end())
                continue;
            auto pairProp = pairModel->second.find("propertyName");
            if (pairProp == pairModel->second.end())
                continue;
            const auto& propertyName = pairProp->second;
            m_mapDeviceProperties[result["deviceID"]].push_back(propertyName);
            auto pair = m_mapPropertyData.find({result["deviceID"], propertyName});
            if (pair != m_mapPropertyData.end())
            {
                ZGLOG_WARN(QStringLiteral("设备'%1'属性'%2'已经关联到表'%3'数据'%4', 无法关联到表'%5'数据'%6'")
                    .arg(result["deviceID"].c_str()).arg(propertyName.c_str())
                    .arg(pair->second.first.c_str()).arg(pair->second.second.c_str())
                    .arg(tableName.c_str()).arg(result["id"].c_str()));
            }
            else
            {
                m_mapPropertyData.insert({{result["deviceID"], propertyName}, {tableName, result["id"]}});
                //                m_mapPropertyData[{result["deviceID"], propertyName}] = {tableName, result["id"]};
            }
            auto pairData = m_mapDataProperty.find(result["id"]);
            if (pairData != m_mapDataProperty.end())
            {
                ZGLOG_WARN(QStringLiteral("数据ID'%1'已经存在"));
            }
            else
            {
                m_mapDataProperty.insert({result["id"], {result["deviceID"], propertyName}});
                //                m_mapDataProperty[result["id"]] = {result["deviceID"], propertyName};
            }
            const auto& dataCategoryID = ZGUtils::get(pairModel->second, "dataCategoryID", "");
            const auto& isPublishMQ = ZGUtils::get(pairModel->second, "isPublishMQ", "");
            m_mapDataParam.insert({
                result["id"], {
                    {"dataModelID", result["dataModelID"]},
                    {"dataCategoryID", dataCategoryID}, {"isPublishMQ", isPublishMQ}
                }
            });
            //            m_mapDataParam[result["id"]] = {
            //                {"dataModelID", result["dataModelID"]},
            //                {"dataCategoryID", dataCategoryID}, {"isPublishMQ", isPublishMQ}
            //            };
        }
    }

    bool ZGMPDevicePropertyMng::initDatasetTableProperties(const std::string& tableName)
    {
        QString sql = QString("SELECT COUNT(*) FROM %1 a "
            "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
            "WHERE b.isEnable = 1").arg(tableName.c_str());
        std::string recordCount;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), recordCount))
        {
            ZGLOG_ERROR(QStringLiteral("获取表'%1'记录数失败").arg(tableName.c_str()));
            return false;
        }
        size_t count = static_cast<size_t>(std::atoi(recordCount.c_str()));
        size_t callCount = (count % m_maxRecordCount == 0) ? count / m_maxRecordCount : count / m_maxRecordCount + 1;
        auto dataPrx = ZGProxyMng::instance()->getProxySPDBData();
        std::vector<ListStringMap> listResults;
        std::vector<QFuture<ListStringMap>> listFuture;
        for (int i = 0; i < callCount; ++i)
        {
            listFuture.push_back(QtConcurrent::run([&](int index)
            {
                ListStringMap results;
                QString inSql = QString("SELECT a.id, a.deviceID, a.dataModelID FROM %1 a "
                                    "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
                                        "WHERE b.isEnable = 1 ORDER BY a.id").arg(tableName.c_str());
                if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
                {
                    inSql += QString(" LIMIT %1, %2").arg(index * m_maxRecordCount).arg(m_maxRecordCount);
                }
                else
                {
                    inSql += QString(" OFFSET %1 ROWS FETCH NEXT %2 ROWS ONLY").arg(index * m_maxRecordCount).arg(m_maxRecordCount);
                }
                ZGLOG_TRACE(inSql);
                ErrorInfo e;
                if (!dataPrx->execQuerySqlToListMap(inSql.toStdString(), results, e))
                    ZGLOG_ERROR(e);
                return results;
            }, i));
        }
        for (int i = 0; i < callCount; ++i)
        {
            cacheDatasetProperties(tableName, listFuture[i].result());
        }
        ZGLOG_DEBUG(QString("DeviceProperties size: %1").arg(m_mapDeviceProperties.size()));
        ZGLOG_DEBUG(QString("PropertyData size: %1").arg(m_mapPropertyData.size()));
        ZGLOG_DEBUG(QString("DataProperty size: %1").arg(m_mapDataProperty.size()));
        if (m_mapPropertyData.size() != m_mapDataProperty.size())
        {
            ZGLOG_ERROR(QStringLiteral("存在关联了多个ID的设备属性"));
            return false;
        }
        ZGLOG_DEBUG(QString("DataParam size: %1").arg(m_mapDataParam.size()));
        return true;
    }


    //     bool ZGMPDevicePropertyMng::initDatasetTableProperties(const std::string& tableName)
    //     {
    //         std::string modelTable = tableName;
    //         ZGUtils::replaceString(modelTable, "dataset", "model");
    // //        QString sql = QString("SELECT a.id, a.deviceID, a.dataModelID FROM %1 a "
    // //                              "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
    // //                              "LEFT JOIN %2 c ON a.dataModelID = c.id "
    // //                              "WHERE b.isEnable = 1 AND c.isEnable = 1 ORDER BY a.id")
    // //                .arg(tableName.c_str()).arg(modelTable.c_str());
    //         ZG6000::StringList listDeviceID;
    //         for (const auto& [deviceID, deviceParam]: m_mapDeviceParam)
    //         {
    //             listDeviceID.push_back(deviceID);
    //         }
    //         std::string deviceIDs = ZGUtils::join(listDeviceID, ",", "'", "'");
    // //        QString sql = QString("SELECT a.id, a.deviceID, a.dataModelID FROM %1 a "
    // //                              "LEFT JOIN %2 b ON a.dataModelID = b.id "
    // //                              "WHERE b.isEnable = 1 AND b.propertyName <> '' AND a.deviceID IN (%3) ORDER BY a.id")
    // //                .arg(tableName.c_str()).arg(modelTable.c_str()).arg(deviceIDs.c_str());
    //         QString sql = QString("SELECT a.id, a.deviceID, a.dataModelID FROM %1 a "
    //                               "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
    //                               "WHERE b.isEnable = 1").arg(tableName.c_str());
    //         ListStringMap listResult;
    //         if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listResult))
    //         {
    //             ZGLOG_ERROR(QStringLiteral("获取表'%1'设备属性失败").arg(tableName.c_str()));
    //             return false;
    //         }
    //         cacheDatasetProperties(tableName, std::move(listResult));
    //         ZGLOG_DEBUG(QString("DeviceProperties size: %1").arg(m_mapDeviceProperties.size()));
    //         ZGLOG_DEBUG(QString("PropertyData size: %1").arg(m_mapPropertyData.size()));
    //         ZGLOG_DEBUG(QString("DataProperty size: %1").arg(m_mapDataProperty.size()));
    //         if (m_mapPropertyData.size() != m_mapDataProperty.size())
    //         {
    //             ZGLOG_ERROR(QStringLiteral("存在关联了多个ID的设备属性"));
    //             return false;
    //         }
    //         ZGLOG_DEBUG(QString("DataParam size: %1").arg(m_mapDataParam.size()));
    //         return true;
    //     }

    bool ZGMPDevicePropertyMng::initModelTableParam(const std::string& tableName, ZG6000::MapStringMap& mapProperty)
    {
        mapProperty.clear();
        std::string sql = "SELECT * FROM " + tableName + " WHERE isEnable = 1 AND propertyName <> ''";
        if (!ZGProxyCommon::execQuerySql(sql, mapProperty))
        {
            ZGLOG_ERROR(QStringLiteral("获取表'%1'模型数据失败").arg(tableName.c_str()));
            return false;
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::mgetDatasetProperties(const std::string& tableName, const StringList& listID, const StringList& dataFields,
                                                      const StringList& modelFields, ListStringMap& listProperties, ErrorInfo& e)
    {
        if (!ZGProxyCommon::mgetDataByFields(tableName, listID, dataFields, listProperties))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取表'%1'数据属性失败").arg(tableName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listID.size() != listProperties.size())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取表'%1'数据属性失败").arg(tableName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        auto pairFunc = m_mapExtendedModelFunc.find(tableName);
        for (size_t i = 0; i < listID.size(); ++i)
        {
            for (auto& [field, value]: listProperties[i])
            {
                if (field == "rtNewValue" || field == "rtSimulateValue")
                {
                    std::string dataCategoryID;
                    if (!getDataModelParam(listID[i], "dataCategoryID", dataCategoryID, e))
                        continue;
                    const auto& key = dataCategoryID + "/" + value;
                    const auto& desc = ZGUtils::getName(m_mapDataCategoryProp, key, "propName");
                    const auto& descL2 = ZGUtils::getName(m_mapDataCategoryProp, key, "propNameL2");
                    listProperties[i][field + "Desc"] = desc;
                    listProperties[i][field + "DescL2"] = descL2;
                }
            }
            std::string dataModelID;
            if (!getDataParam(listID[i], "dataModelID", dataModelID, e))
                return false;
            auto pairModel = m_mapModelPointParam.find(dataModelID);
            if (pairModel == m_mapModelPointParam.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到可用的模型数据ID'%1'").arg(dataModelID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& [id, modelParam] = *pairModel;
            for (const auto& modelField: modelFields)
            {
                auto pairParam = modelParam.find(modelField);
                if (pairParam == modelParam.end())
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("模型ID'%1'找不到可用的模型字段'%2'").arg(dataModelID.c_str()).arg(modelField.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                const auto& [field, value] = *pairParam;
                if (modelField == "dataUnitID")
                {
                    const auto& unitName = ZGUtils::getName(m_mapDataUnit, value, "name");
                    const auto& unitNameL2 = ZGUtils::getName(m_mapDataUnit, value, "nameL2");
                    listProperties[i]["dataUnitValue"] = unitName;
                    listProperties[i]["dataUnitValueL2"] = unitNameL2;
                }
                listProperties[i][modelField] = value;
                if (pairFunc != m_mapExtendedModelFunc.end())
                    pairFunc->second(listProperties[i], *pairParam);
            }
        }
        return true;
    }

    void ZGMPDevicePropertyMng::splitPropertyNames(StringList listName, StringList& listDeviceField, StringList& listDataField)
    {
        for (auto&& name: listName)
        {
            if (m_mapPrimaryDevField.find(name) != m_mapPrimaryDevField.end() || m_mapSecondDevField.find(name) != m_mapSecondDevField.end())
                listDeviceField.push_back(std::move(name));
            else
                listDataField.push_back(std::move(name));
        }
    }

    bool ZGMPDevicePropertyMng::getDevicesTableData(const StringList& listDeviceID, const StringList& listName, std::map<std::string, StringList>& mapTableData, bool strict, ErrorInfo& e)
    {
        for (const auto& deviceID: listDeviceID)
        {
            for (const auto& name: listName)
            {
                auto pair = m_mapPropertyData.find({deviceID, name});
                if (pair == m_mapPropertyData.end())
                {
                    if (strict)
                    {
                        e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                        e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                        ZGLOG_ERROR(e);
                        return false;
                    }
                    else
                        continue;
                }
                const auto& [tableName, dataID] = pair->second;
                mapTableData[tableName].push_back(dataID);
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::getDevicesFieldProperties(const StringList& listDeviceID, const StringList& listDeviceField,
                                                          const std::function<void(const std::string&, const std::string&, std::string&&)>& func, ErrorInfo& e)
    {
        if (!listDeviceField.empty())
        {
            ListStringMap listDeviceFieldValue;
            if (!ZGProxyCommon::mgetDataByFields("mp_param_device", listDeviceID, listDeviceField, listDeviceFieldValue))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取设备字段属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (listDeviceID.size() != listDeviceFieldValue.size())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("获取设备字段属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            for (size_t i = 0; i < listDeviceID.size(); ++i)
            {
                MapStringMap properties;
                for (auto& [fieldName, fieldValue]: listDeviceFieldValue[i])
                {
                    func(listDeviceID[i], fieldName, std::move(fieldValue));
                }
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::mgetPropertiesFromFields(const StringList& listDeviceID, MapMapStringMap& mapProperties, ErrorInfo& e)
    {
        StringList listCategoryID;
        if (!ZGProxyCommon::mgetDataByField("mp_param_device", listDeviceID, "categoryID", listCategoryID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取设备类别失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringList listPrimaryDeviceID, listSecondaryDeviceID;
        for (size_t i = 0; i < listDeviceID.size(); ++i)
        {
            if (listCategoryID[i] == "ZG_DC_PRIMARY_DEV")
                listPrimaryDeviceID.push_back(std::move(listDeviceID[i]));
            if (listCategoryID[i] == "ZG_DC_SECOND_DEV")
                listSecondaryDeviceID.push_back(std::move(listDeviceID[i]));
        }
        if (!mgetPropertiesFromFields(listPrimaryDeviceID, m_mapPrimaryDevField, mapProperties, e))
            return false;
        if (!mgetPropertiesFromFields(listSecondaryDeviceID, m_mapSecondDevField, mapProperties, e))
            return false;
        return true;
    }

    bool ZGMPDevicePropertyMng::mgetPropertiesFromFields(const StringList& listDeviceID, const StringMap& mapDevField, MapMapStringMap& mapProperties, ErrorInfo& e)
    {
        if (!listDeviceID.empty())
        {
            StringList listDevField;
            std::for_each(mapDevField.begin(), mapDevField.end(), [&](const std::pair<std::string, std::string>& pair)
            {
                listDevField.push_back(pair.first);
            });
            ListStringMap listField;
            if (!ZGProxyCommon::mgetDataByFields("mp_param_device", listDeviceID, listDevField, listField))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取设备字段属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (listDeviceID.size() != listField.size())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("获取设备字段属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            for (size_t i = 0; i < listDeviceID.size(); ++i)
            {
                for (size_t j = 0; j < listDevField.size(); ++j)
                {
                    StringMap property{{"rtNewValue", listField[i][listDevField[j]]}};                   
                    if (listDevField[j] == "appNodeID")
                    {
                        const auto& desc = ZGUtils::getName(m_mapAppNode, listField[i][listDevField[j]], "name");
                        const auto& descL2 = ZGUtils::getName(m_mapAppNode, listField[i][listDevField[j]], "nameL2");
                        property["desc"] = desc;
                        property["descL2"] = descL2;
                    }
                    if (listDevField[j] == "subsystemID")
                    {
                        const auto& desc = ZGUtils::getName(m_mapSubsystem, listField[i][listDevField[j]], "name");
                        const auto& descL2 = ZGUtils::getName(m_mapSubsystem, listField[i][listDevField[j]], "nameL2");
                        property["desc"] = desc;
                        property["descL2"] = descL2;
                    }
                    if (listDevField[j] == "majorID")
                    {
                        const auto& desc = ZGUtils::getName(m_mapMajor, listField[i][listDevField[j]], "name");
                        const auto& descL2 = ZGUtils::getName(m_mapMajor, listField[i][listDevField[j]], "nameL2");
                        property["desc"] = desc;
                        property["descL2"] = descL2;
                    }
                    if (listDevField[j] == "typeID")
                    {
                        const auto& desc = ZGUtils::getName(m_mapDeviceType, listField[i][listDevField[j]], "name");
                        const auto& descL2 = ZGUtils::getName(m_mapDeviceType, listField[i][listDevField[j]], "nameL2");
                        property["desc"] = desc;
                        property["descL2"] = descL2;
                    }
                    if (listDevField[j] == "volLevelID")
                        property["desc"] = ZGUtils::get(m_mapVolLevelType, listField[i][listDevField[j]], "");
                    if (listDevField[j] == "rtAuthPosID")
                        property["desc"] = ZGUtils::get(m_mapAuthPos, listField[i][listDevField[j]], "");
                    mapProperties[listDeviceID[i]][listDevField[j]] = std::move(property);
                }
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::mgetPropertiesFromTable(const std::string& tableName, const StringList& listDeviceID, MapMapStringMap& mapProperties, ErrorInfo& e)
    {
        std::string devices = ZGUtils::join(listDeviceID, ",", "'", "'");
        QString sql = QString("SELECT id FROM %1 WHERE deviceID IN (%2)")
                      .arg(tableName.c_str()).arg(devices.c_str());
        StringList listDataID;
        ListStringMap listProperties;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDataID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取表'%1'数据ID失败").arg(tableName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        std::string modelTable = tableName;
        ZGUtils::replaceString(modelTable, "dataset", "model");
        if (!mgetDatasetProperties(tableName, listDataID, m_mapDataFields[tableName],
            m_mapModelFields[modelTable], listProperties, e))
            return false;
        for (auto& properties: listProperties)
        {
            auto pair = m_mapDataProperty.find(properties["id"]);
            if (pair == m_mapDataProperty.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到表'%1'数据'%2'关联的设备属性").arg(tableName.c_str()).arg(properties["id"].c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& [deviceID, name] = pair->second;
            mapProperties[deviceID][name] = std::move(properties);
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& mapValues, bool strict, ErrorInfo& e)
    {
        StringList listDeviceField, listDataField;
        splitPropertyNames(std::move(listName), listDeviceField, listDataField);
        if (!getDevicesFieldProperties(listDeviceID, listDeviceField, [&](const std::string& deviceID, const std::string& name, std::string&& value)
        {
            mapValues[deviceID][name] = std::move(value);
        }, e))
            return false;
        if (!listDataField.empty())
        {
            std::map<std::string, StringList> mapTableData;
            if (!getDevicesTableData(listDeviceID, listDataField, mapTableData, strict, e))
                return false;
            for (const auto& [table, listDataID]: mapTableData)
            {
                ListStringMap listProperties;
                if (!mgetDatasetProperties(table, listDataID, {"rtNewValue"},
                    {}, listProperties, e))
                    return false;
                for (size_t i = 0; i < listDataID.size(); ++i)
                {
                    auto pair = m_mapDataProperty.find(listDataID[i]);
                    if (pair == m_mapDataProperty.end())
                    {
                        if (strict)
                        {
                            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                            e.errDetail = QStringLiteral("找不到表'%1'数据'%2'关联的设备属性").arg(table.c_str()).arg(listDataID[i].c_str()).toStdString();
                            ZGLOG_ERROR(e);
                            return false;
                        }
                        continue;
                    }
                    const auto& [deviceID, name] = pair->second;
                    mapValues[deviceID][name] = listProperties[i]["rtNewValue"];
                }
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::mupdatePropertiesToRt(MapMapStringMap mapProperties, bool strict, ErrorInfo& e)
    {
        std::map<StringList, ListStringMap> mapFieldProperties, mapDataProperties;
        for (auto& [deviceID, properties]: mapProperties)
        {
            StringList listDevField;
            StringMap deviceParam{{"id", deviceID}};
            for (auto& [name, property]: properties)
            {
                if (m_mapPrimaryDevField.find(name) != m_mapPrimaryDevField.end() ||
                    m_mapSecondDevField.find(name) != m_mapSecondDevField.end())
                {
                    listDevField.push_back({name});
                    deviceParam[name] = std::move(property["value"]);
                }
                else
                {
                    auto pair = m_mapPropertyData.find({deviceID, name});
                    if (pair == m_mapPropertyData.end())
                    {
                        if (strict)
                        {
                            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                            e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                            ZGLOG_ERROR(e);
                            return false;
                        }
                        continue;
                    }
                    const auto& [tableName, dataID] = pair->second;
                    StringMap dataParam{{"id", dataID}};
                    StringList listDataField{tableName};
                    for (auto& [field, value]: property)
                    {
                        listDataField.push_back(field);
                        dataParam[field] = std::move(value);
                    }
                    mapDataProperties[std::move(listDataField)].push_back(std::move(dataParam));
                }
            }
            mapFieldProperties[std::move(listDevField)].push_back(std::move(deviceParam));
        }
        for (auto& [listDevField, listDeviceParam]: mapFieldProperties)
        {
            StringList listPartDeviceID;
            for (auto& deviceParam: listDeviceParam)
            {
                listPartDeviceID.push_back(deviceParam["id"]);
            }
            if (!ZGProxyCommon::mupdateDataByFields("mp_param_device", listPartDeviceID, listDeviceParam))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新设备字段属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        for (auto& [listDataField, listDataParam]: mapDataProperties)
        {
            StringList listDataID;
            for (auto& dataParam: listDataParam)
            {
                listDataID.push_back(dataParam["id"]);
            }
            const auto& tableName = listDataField[0];
            if (!ZGProxyCommon::mupdateDataByFields(tableName, listDataID, listDataParam))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新设备数据属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::mupdatePropertiesToDb(MapMapStringMap mapProperties, bool strict, ErrorInfo& e)
    {
        StringList listSql;
        for (auto& [deviceID, properties]: mapProperties)
        {
            StringMap fieldParam{{"id", deviceID}};
            for (auto& [name, property]: properties)
            {
                if (m_mapPrimaryDevField.find(name) != m_mapPrimaryDevField.end() ||
                    m_mapSecondDevField.find(name) != m_mapSecondDevField.end())
                {
                    fieldParam[name] = property["value"];
                }
                else
                {
                    auto pair = m_mapPropertyData.find({deviceID, name});
                    if (pair == m_mapPropertyData.end())
                    {
                        if (strict)
                        {
                            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                            e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                            ZGLOG_ERROR(e);
                            return false;
                        }
                        continue;
                    }
                    const auto& [tableName, dataID] = pair->second;
                    property["id"] = dataID;
                    listSql.push_back(ZGUtils::generateUpdateSql(tableName, std::move(property)));
                }
            }
            if (fieldParam.size() > 1)
                listSql.push_back(ZGUtils::generateUpdateSql("mp_param_device", fieldParam));
        }
        if (!listSql.empty())
        {
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新设备属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::mupdatePropertyValuesToRt(MapStringMap mapValues, bool strict, ErrorInfo& e)
    {
        std::map<StringList, ListStringMap> mapFieldProperties;
        std::map<std::string, StringMap> mapDataProperties;
        for (auto& [deviceID, properties]: mapValues)
        {
            StringList listDevField;
            StringMap deviceParam{{"id", deviceID}};
            for (auto& [name, value]: properties)
            {
                if (m_mapPrimaryDevField.find(name) != m_mapPrimaryDevField.end() ||
                    m_mapSecondDevField.find(name) != m_mapSecondDevField.end())
                {
                    listDevField.push_back({name});
                    deviceParam[name] = std::move(value);
                }
                else
                {
                    auto pair = m_mapPropertyData.find({deviceID, name});
                    if (pair == m_mapPropertyData.end())
                    {
                        if (strict)
                        {
                            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                            e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                            ZGLOG_ERROR(e);
                            return false;
                        }
                        continue;
                    }
                    const auto& [tableName, dataID] = pair->second;
                    mapDataProperties[tableName][dataID] = std::move(value);
                }
            }
            mapFieldProperties[std::move(listDevField)].push_back(std::move(deviceParam));
        }
        for (auto& [listDevField, listDeviceParam]: mapFieldProperties)
        {
            StringList listPartDeviceID;
            for (auto& deviceParam: listDeviceParam)
            {
                listPartDeviceID.push_back(deviceParam["id"]);
            }
            if (!ZGProxyCommon::mupdateDataByFields("mp_param_device", listPartDeviceID, listDeviceParam))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新设备字段属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        std::string currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        for (auto& [tableName, dataParam]: mapDataProperties)
        {
            StringList listDataID;
            ListStringMap listData;
            for (auto& [dataID, value]: dataParam)
            {
                listDataID.push_back(dataID);
                StringMap data;
                data["rtNewValue"] = std::move(value);
                data["rtUpdateTime"] = currentTime;
                listData.push_back(std::move(data));
            }
            if (!ZGProxyCommon::mupdateDataByFields(tableName, std::move(listDataID), listData))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新设备数据属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGMPDevicePropertyMng::mupdatePropertyValuesToDb(MapStringMap mapValues, bool strict, ErrorInfo& e)
    {
        StringList listSql;
        std::string currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        for (auto& [deviceID, properties]: mapValues)
        {
            StringMap fieldParam{{"id", deviceID}};
            for (auto& [name, value]: properties)
            {
                if (m_mapPrimaryDevField.find(name) != m_mapPrimaryDevField.end() ||
                    m_mapSecondDevField.find(name) != m_mapSecondDevField.end())
                {
                    fieldParam[name] = value;
                }
                else
                {
                    auto pair = m_mapPropertyData.find({deviceID, name});
                    if (pair == m_mapPropertyData.end())
                    {
                        if (strict)
                        {
                            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
                            e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据").arg(deviceID.c_str()).arg(name.c_str()).toStdString();
                            ZGLOG_ERROR(e);
                            return false;
                        }
                        continue;
                    }
                    const auto& [tableName, dataID] = pair->second;
                    StringMap dataParam{{"id", dataID}};
                    dataParam["rtNewValue"] = value;
                    dataParam["rtUpdateTime"] = currentTime;
                    listSql.push_back(ZGUtils::generateUpdateSql(tableName, std::move(dataParam)));
                }
            }
            if (fieldParam.size() > 1)
                listSql.push_back(ZGUtils::generateUpdateSql("mp_param_device", fieldParam));
        }
        if (!listSql.empty())
        {
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新设备属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    void ZGMPDevicePropertyMng::processFieldChange(ListRecord listRecord)
    {
        for (auto& record: listRecord)
        {
            MapMapStringMap mapProperties;
            const auto& deviceID = record["id"].newValue;
            auto pair = m_mapDeviceParam.find(deviceID);
            if (pair == m_mapDeviceParam.end())
                continue;
            const auto& deviceParam = pair->second;
            const auto& isPublishMQ = ZGUtils::get(deviceParam, "isPublishMQ", "");
            if (isPublishMQ != "1")
                continue;
            ZG6000::StringMap mapFields;
            for (auto& [field, value]: record)
            {
                if (m_mapPrimaryDevField.find(field) != m_mapPrimaryDevField.end())
                    mapFields[field] = m_mapPrimaryDevField[field];
                if (m_mapSecondDevField.find(field) != m_mapSecondDevField.end())
                    mapFields[field] = m_mapSecondDevField[field];
            }
            if (mapFields.empty())
                continue;
            ZG6000::ErrorInfo e;
            if (mgetPropertiesFromFields({deviceID}, mapFields, mapProperties, e))
            {
                for (const auto& [id, properties]: mapProperties)
                {
                    QString topicName = QString("mp_param_device/%1").arg(id.c_str());
                    const auto& json = ZGJson::convertToJson(properties);
                    m_pMqttClient->sendPublish(topicName, json.c_str());
                }
            }
            //            for (auto& [field, value]: record)
            //            {
            //                mapProperties[deviceID][field] = {{"value", value.newValue}};
            //            }
        }
        //        for (const auto& [deviceID, properties] : mapProperties)
        //        {
        //            QString topicName = QString("mp_param_device/%1").arg(deviceID.c_str());
        //            const auto& json = ZGJson::convertToJson(properties);
        //            m_pMqttClient->sendPublish(topicName, json.c_str());
        //        }
    }

    void ZGMPDevicePropertyMng::processDataChange(std::string tableName, ListRecord listRecord)
    {
        if (std::find(m_listDataTable.begin(), m_listDataTable.end(), tableName) == m_listDataTable.end())
            return;
        MapMapStringMap mapRedisProperties;
        MapMapStringMap mapMqttProperties;
        for (auto& record: listRecord)
        {
            const auto& dataID = record["id"].newValue;
            auto pair = m_mapDataProperty.find(dataID);
            if (pair == m_mapDataProperty.end())
                continue;
            const auto& [deviceID, name] = pair->second;
            auto pairDevice = m_mapDeviceParam.find(deviceID);
            if (pairDevice == m_mapDeviceParam.end())
                continue;
            StringMap propertyParam;
            auto pairData = m_mapDataParam.find(dataID);
            if (pairData == m_mapDataParam.end())
                continue;
            const auto& dataModelID = pairData->second["dataModelID"];
            auto pairModel = m_mapModelPointParam.find(dataModelID);
            if (pairModel == m_mapModelPointParam.end())
                continue;
            for (const auto& [field, value]: record)
            {
                if (std::find(m_mapDataFields[tableName].begin(), m_mapDataFields[tableName].end(), field) == m_mapDataFields[tableName].end())
                    continue;
                propertyParam[field] = value.newValue;
                if (field == "rtNewValue" || field == "rtSimulateValue")
                {
                    const auto& dataCategoryID = pairModel->second["dataCategoryID"];
                    const auto& desc = ZGUtils::getName(m_mapDataCategoryProp, dataCategoryID + "/" + value.newValue, "propName");
                    const auto& descL2 = ZGUtils::getName(m_mapDataCategoryProp, dataCategoryID + "/" + value.newValue, "propNameL2");
                    propertyParam[field + "Desc"] = desc;
                    propertyParam[field + "DescL2"] = descL2;
                }
            }
            mapRedisProperties[deviceID][name] = propertyParam;
            const auto& deviceParam = pairDevice->second;
            const auto& isPublishMQ = ZGUtils::get(deviceParam, "isPublishMQ", "");
            if (isPublishMQ != "1")
                continue;
            if (pairModel->second["isPublishMQ"] == "1")
            {
                mapMqttProperties[deviceID][name] = std::move(propertyParam);
            }
        }
        for (const auto& [deviceID, properties]: mapRedisProperties)
        {
            QString topicName = QString("mp_param_device/%1").arg(deviceID.c_str());
            const auto& json = ZGJson::convertToJson(properties);
            long long num;
            QString errMsg;
            if (!m_pRedisClient->publish(topicName, json.c_str(), num, errMsg))
                ZGLOG_ERROR(errMsg);
        }
        for (const auto& [deviceID, properties]: mapMqttProperties)
        {
            QString topicName = QString("mp_param_device/%1").arg(deviceID.c_str());
            const auto& json = ZGJson::convertToJson(properties);
            m_pMqttClient->sendPublish(topicName, json.c_str());
        }
    }

    bool ZGMPDevicePropertyMng::getDataParam(const std::string& dataID, const std::string& paramName, std::string& value, ErrorInfo& e)
    {
        auto pairData = m_mapDataParam.find(dataID);
        if (pairData == m_mapDataParam.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到可用的数据ID'%1'").arg(dataID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& [_dataID, dataParam] = *pairData;
        auto pairParam = dataParam.find(paramName);
        if (pairParam == dataParam.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("数据ID'%1'找不到可用的参数'%2'").arg(dataID.c_str()).arg(paramName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        value = pairParam->second;
        return true;
    }

    bool ZGMPDevicePropertyMng::getDataModelParam(const std::string& dataID, const std::string& paramName, std::string& value, ErrorInfo& e)
    {
        auto pairData = m_mapDataParam.find(dataID);
        if (pairData == m_mapDataParam.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到可用的数据ID'%1'").arg(dataID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& [_dataID, dataParam] = *pairData;
        const auto& dataModelID = ZGUtils::get(dataParam, "dataModelID", "");
        auto pairModel = m_mapModelPointParam.find(dataModelID);
        if (pairModel == m_mapModelPointParam.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到可用的模型ID'%1'").arg(dataModelID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& [_dataModelID, modelParam] = *pairModel;
        auto pairParam = modelParam.find(paramName);
        if (pairParam == modelParam.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到模型ID'%1'的参数'%2'").arg(dataModelID.c_str()).arg(paramName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        value = pairParam->second;
        return true;
    }

    void ZGMPDevicePropertyMng::onTimer()
    {
        StringList listDeviceID;
        for (auto& [deviceID, tickCount]: m_mapDeviceTickCount)
        {
            auto pair = m_mapDeviceParam.find(deviceID);
            if (pair == m_mapDeviceParam.end())
                continue;
            auto& [_, param] = *pair;
            if (param["isPublishMQ"] != "1")
                continue;
            int interval = std::atoi(param["publishInterval"].c_str());
            if (interval <= 0)
                continue;
            if (tickCount < interval)
            {
                ++tickCount;
                continue;
            }
            listDeviceID.push_back(deviceID);
            tickCount = 1;
        }
        if (listDeviceID.empty())
            return;
        MapMapStringMap mapProperties;
        ErrorInfo e;
        if (!mgetPropertiesAll(std::move(listDeviceID), mapProperties, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
        for (auto& [deviceID, properties]: mapProperties)
        {
            const auto& json = ZGJson::convertToJson(properties);
            QString topicName = QString("mp_param_device/%1").arg(deviceID.c_str());
            ZGLOG_DEBUG(QString("deviceID: '%1', content: '%2'").arg(deviceID.c_str()).arg(json.c_str()));
            m_pMqttClient->sendPublish(topicName, json.c_str());
        }
        // m_pDeviceBlock->initDeviceBlockState();
    }
} // namespace ZG6000

#include "ZGMPDeviceBlock.h"

#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "ZGMPDevicePropertyMng.h"
#include "zgerror/ZGMPDevicePropertyError.h"

namespace ZG6000 {

ZGMPDeviceBlock::ZGMPDeviceBlock(QObject *parent)
    : QObject{parent}
{

}

bool ZGMPDeviceBlock::initialize()
{
    if (!initAppNodeDevice())
        return false;
    if (!initDeviceBlockState())
        return false;
    return true;
}

void ZGMPDeviceBlock::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord)
{
    if (tableName == "sp_param_appnode")
        processAppNodeChange(std::move(tableName), std::move(oper), std::move(reason), std::move(listRecord));
    else if (tableName == "mp_param_appnode_vol_level")
        processVolLevelChange(std::move(tableName), std::move(oper), std::move(reason), std::move(listRecord));
}

bool ZGMPDeviceBlock::isAllowCtrl(const std::string& deviceID, bool &allow, StringMap &conditions, ZG6000::ErrorInfo& e)
{
    allow = false;
    ZG6000::StringMap device;
    if (!ZGProxyCommon::getDataByID("mp_param_device", deviceID, device))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
		e.errDetail = QStringLiteral("获取设备'%1'参数信息失败").arg(deviceID.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
    }
	if (device["id"].empty())
	{
		e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
		e.errDetail = QStringLiteral("无效的设备'%1'").arg(deviceID.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	if (device["isEnable"] != "1")
	{
		e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_ERR_RT);
		e.errDetail = QStringLiteral("设备'%1'未启用").arg(deviceID.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
    for (const auto& field: m_lstBlockFields)
    {
        std::string tableName, dataID;
        if (!ZGMPDevicePropertyMng::instance()->isPropertyExists(deviceID, field))
            continue;
        std::string value;
        if (!ZGMPDevicePropertyMng::instance()->getPropertyValue(deviceID, field, value, e))
            return false;
        if (field == "rtAuthPosID")
        {
            if (value != "ZG_AP_LOCAL")
            {
                conditions[field] = value;
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_SUCCESS);
                e.errDetail = u8"设备控制权限不在本地";
                ZGLOG_ERROR(e);
                return true;
            }
        }
        else
        {
            if (value == "2")
            {
                conditions[field] = value;
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDeviceProperty::ZG_SUCCESS);
                e.errDetail = QStringLiteral("设备'%1'不允许控制").arg(deviceID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return true;
            }
        }
    }
    allow = true;
    return true;
}

bool ZGMPDeviceBlock::initAppNodeDevice()
{
    QString sql = QString("SELECT id, appNodeID FROM mp_param_device WHERE isEnable = 1");
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
    {
        ZGLOG_ERROR(QStringLiteral("初始化设备应用节点失败"));
        return false;
    }
    for (auto& record: listRecord)
    {
        m_mapAppnodeDevice[record["appNodeID"]].push_back(record["id"]);
    }
    return true;
}

bool ZGMPDeviceBlock::initDeviceBlockState()
{
    QString sql = QString("SELECT id FROM sp_param_appnode");
    ZG6000::StringList listAppNodeID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listAppNodeID))
    {
        ZGLOG_ERROR(QStringLiteral("初始化应用节点失败"));
        return false;
    }
    for (const auto& appNodeID: listAppNodeID)
    {
        ZG6000::StringMap stationState;
        if (!ZGProxyCommon::getDataByFields("sp_param_appnode", appNodeID, m_lstStationField, stationState))
        {
            ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'状态失败").arg(appNodeID.c_str()));
            return false;
        }
        auto pair = m_mapAppnodeDevice.find(appNodeID);
        if (pair == m_mapAppnodeDevice.end())
            continue;
        const auto& listDevice = pair->second;
        for (const auto& deviceID: listDevice)
        {
            ZG6000::StringMap deviceState;
            for (const auto& [field, value]: stationState)
            {
                if (ZGMPDevicePropertyMng::instance()->isPropertyExists(deviceID, m_mapStationField[field]))
                    deviceState[m_mapStationField[field]] = value;
            }
            ZG6000::ErrorInfo e;
            if (!ZGMPDevicePropertyMng::instance()->updatePropertyValues(deviceID, deviceState, true, e))
            {
                ZGLOG_ERROR(e);
            }
        }
    }
    return true;
}

void ZGMPDeviceBlock::processAppNodeChange(std::string tableName, std::string oper, std::string reason, ListRecord listRecord)
{
	if (oper != "update")
		return;
	if (reason != "change")
		return;
	for (const auto& record : listRecord)
	{
		const auto& id = ZGUtils::get(record, "id");
		ZG6000::StringMap mapFieldValue;
		for (const auto& field : m_lstStationField)
		{
			const auto& pair = record.find(field);
			if (pair == record.end())
				continue;
			const auto& devField = ZGUtils::get(m_mapStationField, field);
			mapFieldValue.insert(std::make_pair(devField, pair->second.newValue));
		}
		updateAppNodeDevice(id.newValue, mapFieldValue);
	}
}

void ZGMPDeviceBlock::processVolLevelChange(std::string tableName, std::string oper, std::string reason, ListRecord listRecord)
{
	if (oper != "update")
		return;
	if (reason != "change")
		return;
	for (const auto& record : listRecord)
	{
		const auto& id = ZGUtils::get(record, "id");
		ZG6000::StringMap mapFieldValue;
		for (const auto& field : m_lstVolField)
		{
			const auto& pair = record.find(field);
			if (pair == record.end())
				continue;
			const auto& devField = ZGUtils::get(m_mapVolField, field);
			mapFieldValue.insert(std::make_pair(devField, pair->second.newValue));
		}
		updateAppNodeVolDevice(id.newValue, mapFieldValue);
	}
}

void ZGMPDeviceBlock::updateAppNodeDevice(const std::string& appNodeId, const StringMap& mapFieldValue)
{
    std::string sql = "SELECT id FROM mp_param_device WHERE appnodeID = '" + appNodeId + "'";
    ZG6000::StringList listDeviceID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listDeviceID))
	{
		ZGLOG_ERROR(QStringLiteral("获取应用节点%1一次设备失败").arg(appNodeId.c_str()));
		return;
	}
    if (listDeviceID.empty())
		return;
    for (const auto& deviceID: listDeviceID)
    {
        ZG6000::ErrorInfo e;
        if (!ZGProxyCommon::updatePropertyValues(deviceID, mapFieldValue, e, true))
            ZGLOG_ERROR(e);
    }
}

void ZGMPDeviceBlock::updateAppNodeVolDevice(const std::string& appNodeVolId, const ZG6000::StringMap& mapFieldValue)
{
	std::string sql = "SELECT appNodeID, volLevelID FROM mp_param_appnode_vol_level WHERE id = '" + appNodeVolId + "'";
	ZG6000::StringMap record;
	if (!ZGProxyCommon::execQuerySqlRow(sql, record))
	{
		ZGLOG_ERROR(QStringLiteral("获取应用节点电压等级信息失败, appNodeVolId = %1").arg(appNodeVolId.c_str()));
		return;
	}
	try
	{
		const auto& appNodeId = ZGUtils::get(record, "appNodeID");
		const auto& volLevelId = ZGUtils::get(record, "volLevelID");
		if (appNodeId.empty() || volLevelId.empty())
		{
			ZGLOG_ERROR(QStringLiteral("应用节点或电压等级为空, appNodeVolId = %1").arg(appNodeVolId.c_str()));
			return;
		}
		sql = "SELECT deviceID FROM mp_param_appnode_device WHERE appnodeID = '" + appNodeId + "'";
		ZG6000::StringList listEquipmentId;
		if (!ZGProxyCommon::execQuerySqlCol(sql, listEquipmentId))
		{
			ZGLOG_ERROR(QStringLiteral("获取应用节点%1设备失败").arg(appNodeId.c_str()));
			return;
		}
		if (listEquipmentId.empty())
			return;
		std::string equipments = "";
		for (const auto& equipmentId : listEquipmentId)
		{
			equipments += "'" + equipmentId + "',";
		}
		if (!equipments.empty())
			equipments.pop_back();
		sql = "SELECT id FROM mp_param_device WHERE id IN(" + equipments + ") AND volLevelID = '" + volLevelId + "'";
		if (!ZGProxyCommon::execQuerySqlCol(sql, listEquipmentId))
		{
			ZGLOG_ERROR(QStringLiteral("获取设备ID失败"));
			return;
        }
		if (listEquipmentId.empty())
			return;
		if (!ZGProxyCommon::mupdateDataByFields("mp_param_device", listEquipmentId, mapFieldValue))
		{
			ZGLOG_ERROR(QStringLiteral("更新设备实时状态失败"));
		}
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
    }
}
} // namespace ZG6000

#ifndef ZG6000_ZGMPDEVICEPROPERTYMNG_H
#define ZG6000_ZGMPDEVICEPROPERTYMNG_H

#include <QObject>
#include <QThread>
#include <QTimer>

#include "ZGMPDeviceProperty.h"

class ZGMqttClient;
class ZGRedisClient;
namespace ZG6000
{
    class ZGMPDeviceBlock;
    class ZGMPDevicePropertyPublish;

    class ZGMPDevicePropertyMng : public QObject
    {
        Q_OBJECT
    public:
        static ZGMPDevicePropertyMng* instance();
        void init();
        bool checkState();
        void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord);
        bool isAllowCtrl(std::string deviceID, bool& allow, ZG6000::StringMap& conditions, ErrorInfo& e);
        bool mgetPropertiesAll(StringList listDeviceID, MapMapStringMap& mapProperties, ErrorInfo& e);
        bool mgetProperties(StringList listDeviceID, StringList listName, MapMapStringMap& mapProperties, ErrorInfo& e);
        bool mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& mapValues, ErrorInfo& e);
        bool mgetPropertyValuesEx(StringList listDeviceID, StringList listName, MapStringMap& mapValues, ErrorInfo& e);
        bool getGroupProperties(std::string deviceID, MapMapStringMap& mapProperties, ErrorInfo& e);
        bool getTableProperties(std::string deviceID, std::string tableName, MapStringMap &properties, ErrorInfo &e);
        bool getPropertiesAll(std::string deviceID, MapStringMap& properties, ErrorInfo& e);
        bool getProperties(std::string deviceID, StringList listName, MapStringMap& properties, ErrorInfo& e);
        bool getProperty(std::string deviceID, std::string name, StringMap& property, ErrorInfo& e);
        bool getPropertyValues(std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e);
        bool getPropertyValuesEx(std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e);
        bool getPropertyValue(std::string deviceID, std::string name, std::string& value, ErrorInfo& e);
        bool getPropertyValueEx(std::string deviceID, std::string name, std::string& value, ErrorInfo& e);
        bool mupdateProperties(MapMapStringMap mapProperties, bool saveToDB, ErrorInfo& e);
        bool mupdatePropertyValues(MapStringMap mapValues, bool saveToDB, ErrorInfo& e);
        bool mupdatePropertyValuesEx(MapStringMap mapValues, bool saveToDB, ErrorInfo& e);
        bool updateProperty(std::string deviceID, std::string name, StringMap property, bool saveToDB, ErrorInfo& e);
        bool updateProperties(std::string deviceID, MapStringMap properties, bool saveToDB, ErrorInfo& e);
        bool updatePropertyValues(std::string deviceID, StringMap values, bool saveToDB, ErrorInfo& e);
        bool updatePropertyValuesEx(std::string deviceID, StringMap values, bool saveToDB, ErrorInfo& e);
        bool updatePropertyValue(std::string deviceID, std::string name, std::string value, bool saveToDB, ErrorInfo& e);
        bool updatePropertyValueEx(std::string deviceID, std::string name, std::string value, bool saveToDB, ErrorInfo& e);
        bool getDataIDByProperty(std::string deviceID, std::string name, std::string& tableName, std::string& dataID, ErrorInfo& e);
        bool getPropertyByDataID(std::string dataID, std::string& deviceID, std::string& name, ErrorInfo& e);
        bool isPropertyExists(std::string deviceID, std::string name);

    private:
        explicit ZGMPDevicePropertyMng(QObject* parent = nullptr);
        void initDeviceFields();
        void initEvents();
        void initServerInstConfig();
        bool initServerInstInfo();
        bool initDataCategoryParam();
        bool initDeviceParam();
        bool initModelParam();
        bool initOtherParam();
        bool initDatasetProperties();
        bool initMqttClient();
        bool initRedisClient();
        void cacheDatasetProperties(const std::string& tableName, ListStringMap listResult);
        bool initDatasetTableProperties(const std::string& tableName);
        bool initModelTableParam(const std::string& tableName, MapStringMap& mapProperty);
        bool mgetDatasetProperties(const std::string& tableName, const StringList& listID, const StringList& dataFields, const StringList& modelFields,
                                   ListStringMap& listProperties, ErrorInfo& e);
        void splitPropertyNames(StringList listName, StringList& listDeviceField, StringList& listDataField);
        bool getDevicesTableData(const StringList& listDeviceID, const StringList& listName, std::map<std::string, StringList>& mapTableData, bool strict, ErrorInfo& e);
        bool getDevicesFieldProperties(const StringList& listDeviceID, const StringList& listDeviceField,
                                       const std::function<void(const std::string&, const std::string&, std::string&&)>& func, ErrorInfo& e);
        bool mgetPropertiesFromFields(const StringList& listDeviceID, MapMapStringMap& mapProperties, ErrorInfo& e);
        bool mgetPropertiesFromFields(const StringList& listDeviceID, const StringMap& mapDevField, MapMapStringMap& mapProperties, ErrorInfo& e);
        bool mgetPropertiesFromTable(const std::string& tableName, const StringList& listDeviceID, MapMapStringMap& mapProperties, ErrorInfo& e);
        bool mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& mapValues, bool strict, ErrorInfo& e);
        bool mupdatePropertiesToRt(MapMapStringMap mapProperties, bool strict, ErrorInfo& e);
        bool mupdatePropertiesToDb(MapMapStringMap mapProperties, bool strict, ErrorInfo& e);
        bool mupdatePropertyValuesToRt(MapStringMap mapValues, bool strict, ErrorInfo& e);
        bool mupdatePropertyValuesToDb(MapStringMap mapValues, bool strict, ErrorInfo& e);
        void processFieldChange(ListRecord listRecord);
        void processDataChange(std::string tableName, ListRecord listRecord);
        bool getDataParam(const std::string& dataID, const std::string& paramName, std::string& value, ErrorInfo& e);
        bool getDataModelParam(const std::string& dataID, const std::string& paramName, std::string& value, ErrorInfo& e);

    private slots:
        void onTimer();

    private:
        bool m_initialized{false};
        int m_initInterval{10};
        QString m_serverName{""};
        QString m_instName{""};
        QTimer m_timer;
        StringList m_listDataTable {
            "mp_param_dataset_bt", "mp_param_dataset_yc", "mp_param_dataset_yx",
            "mp_param_dataset_text", "mp_param_dataset_param", "mp_param_dataset_ym",
            "mp_param_dataset_yk", "mp_param_dataset_ys", "mp_param_dataset_yt", "mp_param_dataset_yv"
        }; // 数据集数据表
        std::map<std::string, StringList> m_mapDataFields{
            {"mp_param_dataset_bt", {"id", "name", "nameL2", "shortName", "dataIndex", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_yc", {"id", "name", "nameL2", "shortName", "deviceLogicalName", "dataIndex", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_yx", {"id", "name", "nameL2", "shortName", "deviceLogicalName", "dataIndex", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_text", {"id", "name", "nameL2", "shortName", "deviceLogicalName", "dataIndex", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_param", {"id", "name", "nameL2", "shortName", "deviceLogicalName", "dataIndex", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_ym", {"id", "name", "nameL2", "shortName", "deviceLogicalName", "dataIndex", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_yk", {"id", "name", "nameL2", "shortName", "deviceLogicalName", "dataIndex", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_ys", {"id", "name", "nameL2", "shortName", "deviceLogicalName", "dataIndex", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_yt", {"id", "name", "nameL2", "shortName", "deviceLogicalName", "dataIndex", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_yv", {"id", "name", "nameL2", "shortName", "deviceLogicalName", "dataIndex", "rtspAddr", "isEnableChannel", "videoChannel", "rtConnectNum", "rtUpdateTime"}}
        };
        std::map<std::string, StringList> m_mapModelFields{
            {"mp_param_model_bt", {"dataCategoryID", "dataTypeID", "dataUnitID", "isPrivate"}},
            {"mp_param_model_yc", {"dataCategoryID", "dataTypeID", "dataUnitID", "isPrivate"}},
            {"mp_param_model_yx", {"dataCategoryID", "dataTypeID", "dataUnitID", "isPrivate", "relationProperty"}},
            {"mp_param_model_text", {"dataCategoryID", "dataTypeID", "dataUnitID", "isPrivate"}},
            {"mp_param_model_param", {"dataCategoryID", "dataTypeID", "dataUnitID", "isPrivate"}},
            {"mp_param_model_ym", {"dataCategoryID", "dataTypeID", "dataUnitID", "isPrivate"}},
            {"mp_param_model_yk", {"dataCategoryID", "isCheckExecRule", "isCheckConfirmRule", "isCheckErrorRule", "isPrivate", "overtime"}},
            {"mp_param_model_ys", {"dataCategoryID", "isCheckExecRule", "isCheckConfirmRule", "isCheckErrorRule", "isPrivate", "overtime"}},
            {"mp_param_model_yt", {"dataCategoryID", "isCheckExecRule", "isCheckConfirmRule", "isCheckErrorRule", "isPrivate", "overtime"}},
            {"mp_param_model_yv", {"dataCategoryID", "isPrivate"}}
        };
        MapStringMap m_mapDataCategoryProp; // 数据类别属性
        MapStringMap m_mapDataUnit; // 数据单位
        StringMap m_mapPrimaryDevField; // 一次设备字段
        StringMap m_mapSecondDevField; // 二次设备字段
        MapStringMap m_mapModelPointParam; // 模型点参数
        MapStringMap m_mapDeviceParam; // 设备参数
        MapStringMap m_mapDeviceType; // 设备类型
        MapStringMap m_mapDeviceSubtype; // 设备子类型
        MapStringMap m_mapAppNode; // 应用节点
        MapStringMap m_mapSubsystem; // 子系统
        MapStringMap m_mapMajor; // 专业
        StringMap m_mapVolLevelType; // 电压等级类型
        StringMap m_mapAuthPos; // 授权位置
        std::map<std::string, int> m_mapDeviceTickCount;
        using StringPair = std::pair<std::string, std::string>;
        std::map<std::string, StringList> m_mapDeviceProperties; // 设备属性名列表(数据集部分)
        std::map<StringPair, StringPair> m_mapPropertyData; // 设备属性到数据ID
        std::map<std::string, StringPair> m_mapDataProperty; // 数据ID到设备属性
        MapStringMap m_mapDataParam; // 数据参数
        using ExtendedFunc = std::function<void(StringMap&, const StringPair&)>;
        std::map<std::string, ExtendedFunc> m_mapExtendedModelFunc;
        ZGMqttClient* m_pMqttClient{nullptr};
        ZGRedisClient* m_pRedisClient{nullptr};
        // ZGMPDeviceBlock* m_pDeviceBlock{nullptr};
        ZGMPDevicePropertyPublish* m_pPropertyPublish{nullptr};
        size_t m_maxRecordCount{1000};
    };

    inline static ZGMPDevicePropertyMng* g_pInstance = nullptr;
} // namespace ZG6000

#endif // ZG6000_ZGMPDEVICEPROPERTYMNG_H

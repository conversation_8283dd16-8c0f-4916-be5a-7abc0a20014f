#include "ZGMPDevicePropertyI.h"
#include "ZGMPDevicePropertyMng.h"

namespace ZG6000 {
    ZGMPDevicePropertyI::ZGMPDevicePropertyI()
    {
        ZGMPDevicePropertyMng::instance()->init();
    }

    void ZGMPDevicePropertyI::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->dispatchData(std::move(tableName), std::move(oper), std::move(reason), std::move(time), std::move(listRecord));
    }

    bool ZGMPDevicePropertyI::checkState(const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->checkState();
    }

    bool ZGMPDevicePropertyI::isAllowCtrl(std::string deviceID, bool &allow, StringMap &conditions, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->isAllowCtrl(std::move(deviceID), allow, conditions, e);
    }

    bool ZGMPDevicePropertyI::mgetPropertiesAll(StringList listDeviceID, MapMapStringMap& properties, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->mgetPropertiesAll(std::move(listDeviceID), properties, e);
    }

    bool ZGMPDevicePropertyI::mgetProperties(StringList listDeviceID, StringList listName, MapMapStringMap& properties, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->mgetProperties(std::move(listDeviceID), std::move(listName), properties, e);
    }

    bool ZGMPDevicePropertyI::mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& values, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->mgetPropertyValues(std::move(listDeviceID), std::move(listName), values, e);
    }

    bool ZGMPDevicePropertyI::mgetPropertyValuesEx(StringList listDeviceID, StringList listName, MapStringMap& values, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->mgetPropertyValuesEx(std::move(listDeviceID), std::move(listName), values, e);
    }

    bool ZGMPDevicePropertyI::getGroupProperties(std::string deviceID, MapMapStringMap& properties, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->getGroupProperties(std::move(deviceID), properties, e);
    }

    bool ZGMPDevicePropertyI::getTableProperties(std::string deviceID, std::string tableName, MapStringMap &properties, ErrorInfo &e, const Ice::Current &current)
    {
        return ZGMPDevicePropertyMng::instance()->getTableProperties(std::move(deviceID), std::move(tableName), properties, e);
    }

    bool ZGMPDevicePropertyI::getPropertiesAll(std::string deviceID, MapStringMap& properties, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->getPropertiesAll(std::move(deviceID), properties, e);
    }

    bool ZGMPDevicePropertyI::getProperties(std::string deviceID, StringList listName, MapStringMap& properties, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->getProperties(std::move(deviceID), std::move(listName), properties, e);
    }

    bool ZGMPDevicePropertyI::getProperty(std::string deviceID, std::string name, StringMap& property, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->getProperty(std::move(deviceID), std::move(name), property, e);
    }

    bool ZGMPDevicePropertyI::getPropertyValues(std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->getPropertyValues(std::move(deviceID), std::move(listName), values, e);
    }

    bool ZGMPDevicePropertyI::getPropertyValuesEx(std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->getPropertyValuesEx(std::move(deviceID), std::move(listName), values, e);
    }

    bool ZGMPDevicePropertyI::getPropertyValue(std::string deviceID, std::string name, std::string& value, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->getPropertyValue(std::move(deviceID), std::move(name), value, e);
    }

    bool ZGMPDevicePropertyI::getPropertyValueEx(std::string deviceID, std::string name, std::string& value, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->getPropertyValueEx(std::move(deviceID), std::move(name), value, e);
    }

    bool ZGMPDevicePropertyI::mupdateProperties(MapMapStringMap properties, bool saveToDB, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->mupdateProperties(std::move(properties), saveToDB, e);
    }

    bool ZGMPDevicePropertyI::mupdatePropertyValues(MapStringMap values, bool saveToDB, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->mupdatePropertyValues(std::move(values), saveToDB, e);
    }

    bool ZGMPDevicePropertyI::updateProperty(std::string deviceID, std::string name, StringMap property, bool saveToDB, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->updateProperty(std::move(deviceID), std::move(name), std::move(property), saveToDB, e);
    }

    bool ZGMPDevicePropertyI::updateProperties(std::string deviceID, MapStringMap properties, bool saveToDB, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->updateProperties(std::move(deviceID), std::move(properties), saveToDB, e);
    }

    bool ZGMPDevicePropertyI::updatePropertyValues(std::string deviceID, StringMap values, bool saveToDB, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->updatePropertyValues(std::move(deviceID), std::move(values), saveToDB, e);
    }

    bool ZGMPDevicePropertyI::updatePropertyValuesEx(std::string deviceID, StringMap values, bool saveToDB, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->updatePropertyValuesEx(std::move(deviceID), std::move(values), saveToDB, e);
    }

    bool ZGMPDevicePropertyI::updatePropertyValue(std::string deviceID, std::string name, std::string value, bool saveToDB, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->updatePropertyValue(std::move(deviceID), std::move(name), std::move(value), saveToDB, e);
    }

    bool ZGMPDevicePropertyI::updatePropertyValueEx(std::string deviceID, std::string name, std::string value, bool saveToDB, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->updatePropertyValueEx(std::move(deviceID), std::move(name), std::move(value), saveToDB, e);
    }

    bool ZGMPDevicePropertyI::getDataIDByProperty(std::string deviceID, std::string name, std::string& tableName, std::string& dataID, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->getDataIDByProperty(std::move(deviceID), std::move(name), tableName, dataID, e);
    }

    bool ZGMPDevicePropertyI::getPropertyByDataID(std::string dataID, std::string& deviceID, std::string& name, ErrorInfo& e, const Ice::Current& current)
    {
        return ZGMPDevicePropertyMng::instance()->getPropertyByDataID(std::move(dataID), deviceID, name, e);
    }

    bool ZGMPDevicePropertyI::isPropertyExists(std::string deviceID, std::string name, bool &exists, ErrorInfo &e, const Ice::Current &current)
    {
        exists = ZGMPDevicePropertyMng::instance()->isPropertyExists(std::move(deviceID), std::move(name));
        return true;
    }

} // namespace ZG6000

#ifndef ZG6000_ZGMPDEVICEBLOCK_H
#define ZG6000_ZGMPDEVICEBLOCK_H

#include <QObject>
#include <QTimer>
#include <ZGServerCommon.h>

namespace ZG6000
{
    class ZGMPDeviceBlock : public QObject
    {
        Q_OBJECT

    public:
        explicit ZGMPDeviceBlock(QObject* parent = nullptr);
        bool initialize();
        bool initDeviceBlockState();
        void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord);
        bool isAllowCtrl(const std::string& deviceID, bool& allow, ZG6000::StringMap& conditions, ErrorInfo& e);

    private:
        bool initAppNodeDevice();
        void processAppNodeChange(std::string tableName, std::string oper, std::string reason,
                                  ListRecord listRecord);
        void processVolLevelChange(std::string tableName, std::string oper, std::string reason,
                                   ListRecord listRecord);
        void updateAppNodeDevice(const std::string& appNodeId, const StringMap& mapFieldValue);
        void updateAppNodeVolDevice(const std::string& appNodeVolId, const ZG6000::StringMap& mapFieldValue);

    private:
        StringList m_lstStationField{"rtGroundLock", "rtYkBlock", "rtRepairBlock", "rtForbid", "rtAuthPosID"};
        StringList m_lstVolField{"rtGroundLock", "rtYkBlock", "rtRepairBlock", "rtForbid"};
        StringList m_lstBlockFields{"StationGroundLock", "VolGroundLock", "GroundLock",
                                    "StationYkBlock", "VolYkBlock", "YkBlock", "StationRepairBlock",
                                    "VolRepairBlock", "RepairBlock", "StationForbid", "VolForbid",
                                    "Forbid", "rtAuthPosID"};
        MapStringMap m_mapVolLevelType;
        MapStringMap m_mapAuthPos;
        StringMap m_mapStationField{{"rtGroundLock", "StationGroundLock"}, {"rtYkBlock", "StationYkBlock"},
                                    {"rtRepairBlock", "StationRepairBlock"}, {"rtForbid", "StationForbid"},
                                    {"rtAuthPosID", "rtAuthPosID"}};
        StringMap m_mapVolField;
        std::map<std::string, ZG6000::StringList> m_mapAppnodeDevice;
    };
} // namespace ZG6000

#endif // ZG6000_ZGMPDEVICEBLOCK_H

#ifndef ZG6000_ZGMPDEVICEPROPERTYI_H
#define ZG6000_ZGMPDEVICEPROPERTYI_H

#include <ZGMPDeviceProperty.h>

namespace ZG6000 {

class ZGMPDevicePropertyI : public virtual ZGMPDeviceProperty
{
public:
    ZGMPDevicePropertyI();
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current& current) override;
    bool checkState(const Ice::Current& current) override;
    bool isAllowCtrl(std::string deviceID, bool& allow, ZG6000::StringMap& conditions, ErrorInfo& e, const Ice::Current& current) override;
    bool mgetPropertiesAll(StringList listDeviceID, MapMapStringMap& properties, ErrorInfo& e, const Ice::Current& current) override;
    bool mgetProperties(StringList listDeviceID, StringList listName, MapMapStringMap& properties, ErrorInfo& e, const Ice::Current& current) override;
    bool mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& values, ErrorInfo& e, const Ice::Current& current) override;
    bool mgetPropertyValuesEx(StringList listDeviceID, StringList listName, MapStringMap& values, ErrorInfo& e, const Ice::Current& current) override;
    bool getGroupProperties(std::string deviceID, MapMapStringMap& properties, ErrorInfo& e, const Ice::Current& current) override;
    bool getTableProperties(std::string deviceID, std::string tableName, MapStringMap &properties, ErrorInfo &e, const Ice::Current &current) override;
    bool getPropertiesAll(std::string deviceID, MapStringMap& properties, ErrorInfo& e, const Ice::Current& current) override;
    bool getProperties(std::string deviceID, StringList listName, MapStringMap& properties, ErrorInfo& e, const Ice::Current& current) override;
    bool getProperty(std::string deviceID, std::string name, StringMap& property, ErrorInfo& e, const Ice::Current& current) override;
    bool getPropertyValues(std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e, const Ice::Current& current) override;
    bool getPropertyValuesEx(std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e, const Ice::Current& current) override;
    bool getPropertyValue(std::string deviceID, std::string name, std::string& value, ErrorInfo& e, const Ice::Current& current) override;
    bool getPropertyValueEx(std::string deviceID, std::string name, std::string& value, ErrorInfo& e, const Ice::Current& current) override;
    bool mupdateProperties(MapMapStringMap properties, bool saveToDB, ErrorInfo& e, const Ice::Current& current) override;
    bool mupdatePropertyValues(MapStringMap values, bool saveToDB, ErrorInfo& e, const Ice::Current& current) override;
    bool updateProperty(std::string deviceID, std::string name, StringMap property, bool saveToDB, ErrorInfo& e, const Ice::Current& current) override;
    bool updateProperties(std::string deviceID, MapStringMap properties, bool saveToDB, ErrorInfo& e, const Ice::Current& current) override;
    bool updatePropertyValues(std::string deviceID, StringMap values, bool saveToDB, ErrorInfo& e, const Ice::Current& current) override;
    bool updatePropertyValuesEx(std::string deviceID, StringMap values, bool saveToDB, ErrorInfo& e, const Ice::Current& current) override;
    bool updatePropertyValue(std::string deviceID, std::string name, std::string value, bool saveToDB, ErrorInfo& e, const Ice::Current& current) override;
    bool updatePropertyValueEx(std::string deviceID, std::string name, std::string value, bool saveToDB, ErrorInfo& e, const Ice::Current& current) override;
    bool getDataIDByProperty(std::string deviceID, std::string name, std::string& tableName, std::string& dataID, ErrorInfo& e, const Ice::Current& current) override;
    bool getPropertyByDataID(std::string dataID, std::string& deviceID, std::string& name, ErrorInfo& e, const Ice::Current& current) override;
    bool isPropertyExists(std::string deviceID, std::string name, bool &exists, ErrorInfo &e, const Ice::Current &current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGMPDEVICEPROPERTYI_H

#ifndef ZG6000_ZGSPHISTORYMAINTAINMNG_H
#define ZG6000_ZGSPHISTORYMAINTAINMNG_H

#include <QObject>
#include <QTimer>
#include <ZGSPHistoryMaintain.h>

namespace ZG6000 {

class ZGSPHistoryMaintainMng : public QObject
{
    Q_OBJECT
public:
    static ZGSPHistoryMaintainMng* instance();
    bool checkState(const Ice::Current &current);
    void init();

private:
    explicit ZGSPHistoryMaintainMng(QObject *parent = nullptr);
	void initEvents();
	void initServerInstConfig();
	bool initServerInstInfo();
	bool initSystemParam();
	bool addHistoryTable(int year);
	bool dropHistoryTable(int year);
	bool execSqlFile(const QString& fileName, int year);

private slots:
	void onCheckStatus();
    void onBackupDatabase();

private:
	bool m_initialized{ false };
	QString m_serverName{ "" };
	QString m_instName{ "" };
	bool m_masterInst{ false };
	int m_initInterval{ 10 };
    int m_checkInterval{ 60 };
    int m_backupInterval{ 7 };
	int m_backupCount{ 0 };
	int m_hisBackupInterval{ 30 };
	int m_hisBackupCount{ 0 };
	int m_saveYears{ 1 };
	StringList m_tableNames;
	QTimer m_checkTimer;
    QTimer m_backupTimer;
};

} // namespace ZG6000

#endif // ZG6000_ZGSPHISTORYMAINTAINMNG_H

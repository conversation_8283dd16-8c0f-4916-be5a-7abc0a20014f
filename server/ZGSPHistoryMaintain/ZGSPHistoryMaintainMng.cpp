#include "ZGSPHistoryMaintainMng.h"

#include <QDir>
#include <QRandomGenerator>

#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"

#include <QThread>
#include <QTextStream>
#include <QFile>

namespace ZG6000 {

static ZGSPHistoryMaintainMng* g_pInstance = nullptr;

ZGSPHistoryMaintainMng *ZGSPHistoryMaintainMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGSPHistoryMaintainMng;
    return g_pInstance;
}

bool ZGSPHistoryMaintainMng::checkState(const Ice::Current &current)
{
	return m_initialized;
}

void ZGSPHistoryMaintainMng::init()
{
    initEvents();
    initServerInstConfig();
    ZGLOG_INFO("initServerInstInfo");
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
//	QThread::sleep(QRandomGenerator::global()->bounded(5, 10));
    ZGLOG_INFO("initSystemParam");
    while (!initSystemParam())
    {
        ZGLOG_ERROR("initSystemParam error.");
        QThread::msleep(m_initInterval * 1000);
    }
	m_initialized = true;
	ZGLOG_INFO("ZGSPHistoryMaintain init finished.");
    QDate date = QDate::currentDate();
    addHistoryTable(date.year());
    dropHistoryTable(date.year() - m_saveYears - 1);
    m_checkTimer.start(m_checkInterval * 1000 * 60);
    m_backupTimer.start(24 * 60 * 60 * 1000);
}

ZGSPHistoryMaintainMng::ZGSPHistoryMaintainMng(QObject *parent)
    : QObject{parent}
{

}

void ZGSPHistoryMaintainMng::initEvents()
{
	connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPHistoryMaintainMng::onCheckStatus);
    connect(&m_backupTimer, &QTimer::timeout, this, &ZGSPHistoryMaintainMng::onBackupDatabase);
}

void ZGSPHistoryMaintainMng::initServerInstConfig()
{
	const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
	QString errMsg;
	int value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_initInterval = value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_checkInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "backup_interval", value, 1, 30, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_backupInterval = value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "his_backup_interval", value, 1, 30, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_hisBackupInterval = value;
}

bool ZGSPHistoryMaintainMng::initServerInstInfo()
{
	m_serverName = ZGRuntime::instance()->getServerID();
	if (m_serverName.isEmpty())
	{
		ZGLOG_ERROR("Empty server id.");
		return false;
	}
	m_instName = ZGRuntime::instance()->getInstanceID();
	if (m_instName.isEmpty())
	{
		ZGLOG_ERROR("Empty server instance id.");
		return false;
	}
	return true;
}

bool ZGSPHistoryMaintainMng::initSystemParam()
{
	std::string sql = "SELECT historyStoreYears FROM sp_param_system";
	ZG6000::StringList listResult;
	if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
	{
		ZGLOG_ERROR(QStringLiteral("获取系统参数失败"));
		return false;
	}
	if (!listResult.empty())
	{
		int saveYears = std::atoi(listResult[0].c_str());
		if (saveYears > 0)
			m_saveYears = saveYears;
	}
	return true;
}

bool ZGSPHistoryMaintainMng::addHistoryTable(int year)
{
    if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
    {
        QString filePath = ZGPubFun::getCfgDir() + "/history_add.sql";
        return execSqlFile(filePath, year);
    }
    else
    {
        QString sql = QString("EXEC add_history '%1'").arg(year);
        ZGLOG_TRACE(sql);
        return ZGProxyCommon::execSql(sql.toStdString(), true);
    }
}

bool ZGSPHistoryMaintainMng::dropHistoryTable(int year)
{
    if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
    {
        QString filePath = ZGPubFun::getCfgDir() + "/history_drop.sql";
        return execSqlFile(filePath, year);
    }
    else
    {
        QString sql = QString("EXEC delete_history '%1'").arg(year);
        ZGLOG_TRACE(sql);
        return ZGProxyCommon::execSql(sql.toStdString(), true);
    }
}

bool ZGSPHistoryMaintainMng::execSqlFile(const QString& fileName, int year)
{
	QFile file(fileName);
	if (!file.open(QFile::ReadOnly))
	{
        ZGLOG_ERROR(QStringLiteral("打开文件失败"));
		return false;
	}
	QTextStream stream(&file);
	QString sql = stream.readAll().arg(year);
    ZGLOG_DEBUG(sql);
	if (!ZGProxyCommon::execSql(sql.toStdString(), true))
	{
        ZGLOG_ERROR(QStringLiteral("添加历史记录表失败"));
		return false;
	}
    return true;
}

void ZGSPHistoryMaintainMng::onCheckStatus()
{
	if (!ZGRuntime::instance()->isMaster())
		return;
	QDate date = QDate::currentDate();
	addHistoryTable(date.year());
	if (date.month() == 12)
		addHistoryTable(date.year() + 1);
	dropHistoryTable(date.year() - m_saveYears - 1);
}

void ZGSPHistoryMaintainMng::onBackupDatabase()
{
    if (!ZGRuntime::instance()->isMaster())
        return;
	const auto dbPath = ZGPubFun::getRootDir() + "\\db";
	// 如果db目录不存在，则创建
	if (!QFile::exists(dbPath))
	{
		bool success = QDir().mkpath(dbPath);
		if (!success)
		{
			ZGLOG_ERROR(QStringLiteral("创建数据库备份目录失败"));
			return;
		}
	}
	++m_backupCount;
	if (m_backupCount >= m_backupInterval)
	{
		m_backupCount = 0;
		QString sql = QString("EXEC BackupDatabase @DatabaseName = N'zg6000', @BackupPath = N'%1'").arg(dbPath);
		ZGLOG_TRACE(sql);
		if (!ZGProxyCommon::execSql(sql.toStdString()))
		{
			ZGLOG_ERROR(QStringLiteral("备份参数数据库失败"));
		}
		else
		{
			ZGLOG_INFO(QStringLiteral("备份参数数据库成功"));
		}
	}
    ++m_hisBackupCount;
	if (m_hisBackupCount >= m_hisBackupInterval)
	{
		m_hisBackupCount = 0;
		QString sql = QString("EXEC BackupDatabase @DatabaseName = N'zg6000his', @BackupPath = N'%1'").arg(dbPath);
		ZGLOG_TRACE(sql);
		if (!ZGProxyCommon::execSql(sql.toStdString()))
		{
			ZGLOG_ERROR(QStringLiteral("备份历史数据库失败"));
		}
		else
		{
			ZGLOG_INFO(QStringLiteral("备份历史数据库成功"));
		}
	}
}

} // namespace ZG6000

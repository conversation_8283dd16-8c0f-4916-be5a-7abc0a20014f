#ifndef ZGSPAPPNODEMANAGERMNG_H
#define ZGSPAPPNODEMANAGERMNG_H

#include <QThread>
#include <QTimer>
#include "ZGServerCommon.h"

class ZGSPAppNodeManagerMng : public QThread
{
    Q_OBJECT
public:
    static ZGSPAppNodeManagerMng* instance();
    void init();
    bool checkState();
    bool getChild(const std::string& appNodeID, ZG6000::ListStringMap &listChild, ZG6000::ErrorInfo &e);
    bool getParent(const std::string& appNodeID, ZG6000::ListStringMap &listParent, ZG6000::ErrorInfo &e);
    bool getDescendant(const std::string& appNodeID, ZG6000::ListStringMap &listDescendant, ZG6000::ErrorInfo &e);
    bool getAncestor(const std::string& appNodeID, ZG6000::ListStringMap &listAncestor, ZG6000::ErrorInfo &e);
    bool getListYv(const std::string& appNodeID, ZG6000::ListStringMap& listYv, ZG6000::ErrorInfo& e);

protected:
    void run() override;

private:
    explicit ZGSPAppNodeManagerMng(QObject *parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();

signals:
    void initFinished();

private slots:
    void onInitFinished();
    void onCheckStatus();
    void onReceivedData(int requestID, QByteArray data, int statusCode);

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    bool m_masterInst{false};
    int m_initInterval{10};
    int m_checkInterval{10};
    QTimer m_checkTimer;
};

#endif // ZGSPAPPNODEMANAGERMNG_H

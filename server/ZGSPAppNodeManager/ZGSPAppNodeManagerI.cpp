#include "ZGSPAppNodeManagerI.h"
#include "ZGSPAppNodeManagerMng.h"

namespace ZG6000 {

ZGSPAppNodeManagerI::ZGSPAppNodeManagerI()
{
    ZGSPAppNodeManagerMng::instance()->init();
}

bool ZGSPAppNodeManagerI::checkState(const Ice::Current &current)
{
    return ZGSPAppNodeManagerMng::instance()->checkState();
}

bool ZGSPAppNodeManagerI::getChild(std::string appNodeID, ListStringMap &listChild, ErrorInfo &e, const Ice::Current &current)
{
    return ZGSPAppNodeManagerMng::instance()->getChild(appNodeID, listChild, e);
}

bool ZGSPAppNodeManagerI::getParent(std::string appNodeID, ListStringMap &listParent, ErrorInfo &e, const Ice::Current &current)
{
    return ZGSPAppNodeManagerMng::instance()->getParent(appNodeID, listParent, e);
}

bool ZGSPAppNodeManagerI::getDescendant(std::string appNodeID, ListStringMap &listDescendant, ErrorInfo &e, const Ice::Current &current)
{
    return ZGSPAppNodeManagerMng::instance()->getDescendant(appNodeID, listDescendant, e);
}

bool ZGSPAppNodeManagerI::getAncestor(std::string appNodeID, ListStringMap &listAncestor, ErrorInfo &e, const Ice::Current &current)
{
    return ZGSPAppNodeManagerMng::instance()->getAncestor(appNodeID, listAncestor, e);
}

bool ZGSPAppNodeManagerI::getListYv(std::string appNodeID, ListStringMap& listYv, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSPAppNodeManagerMng::instance()->getListYv(appNodeID, listYv, e);
}
} // namespace ZG6000

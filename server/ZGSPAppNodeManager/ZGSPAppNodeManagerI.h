#ifndef ZG6000_ZGSPAPPNODEMANAGERI_H
#define ZG6000_ZGSPAPPNODEMANAGERI_H

#include "ZGSPAppNodeManager.h"

namespace ZG6000 {

class ZGSPAppNodeManagerI : public ZGSPAppNodeManager
{
public:

    ZGSPAppNodeManagerI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;

    // ZGSPAppNodeManager interface
public:
    bool getChild(std::string appNodeID, ListStringMap &listChild, ErrorInfo &e, const Ice::Current &current) override;
    bool getParent(std::string appNodeID, ListStringMap &listParent, ErrorInfo &e, const Ice::Current &current) override;
    bool getDescendant(std::string appNodeID, ListStringMap &listDescendant, ErrorInfo &e, const Ice::Current &current) override;
    bool getAncestor(std::string appNodeID, ListStringMap &listAncestor, ErrorInfo &e, const Ice::Current &current) override;
    bool getListYv(std::string appNodeID, ListStringMap& listYv, ErrorInfo& e, const Ice::Current& current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGSPAPPNODEMANAGERI_H

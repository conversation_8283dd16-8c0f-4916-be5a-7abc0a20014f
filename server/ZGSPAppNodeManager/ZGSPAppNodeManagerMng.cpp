#include "ZGSPAppNodeManagerMng.h"
#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "zgerror/ZGSPAppNodeManagerError.h"

ZGSPAppNodeManagerMng* g_pInstance = nullptr;

ZGSPAppNodeManagerMng *ZGSPAppNodeManagerMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGSPAppNodeManagerMng;
    return g_pInstance;
}

void ZGSPAppNodeManagerMng::init()
{
    initEvents();
    initServerInstConfig();
    start();
    ZGLOG_INFO("ZGSPAppNodeManager init start...");
}

bool ZGSPAppNodeManagerMng::checkState()
{
    return m_initialized;
}

bool ZGSPAppNodeManagerMng::getChild(const std::string &appNodeID, ZG6000::ListStringMap &listChild, ZG6000::ErrorInfo &e)
{
    std::string sql = "SELECT a.id, a.name FROM sp_param_appnode a LEFT JOIN sp_param_appnode_layer b "
                      "ON a.id = b.appNodeID WHERE b.parentAppNodeID = '" + appNodeID + "' ORDER BY a.id";
    if (!ZGProxyCommon::execQuerySql(sql, listChild))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPAppNodeManager::ZG_ERR_DB);
        e.errDetail = u8"查询'" + appNodeID + u8"'子节点失败";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPAppNodeManagerMng::getParent(const std::string &appNodeID, ZG6000::ListStringMap &listParent, ZG6000::ErrorInfo &e)
{
    std::string sql = "SELECT a.id, a.name FROM sp_param_appnode a LEFT JOIN sp_param_appnode_layer b ON a.id = b.parentAppNodeID "
            "WHERE b.appNodeID = '" + appNodeID + "' AND (b.parentAppNodeID <> '' OR b.parentAppNodeID IS NOT NULL) ORDER BY a.id";
    if (!ZGProxyCommon::execQuerySql(sql, listParent))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPAppNodeManager::ZG_ERR_DB);
        e.errDetail = u8"查询'" + appNodeID + u8"'父节点失败";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPAppNodeManagerMng::getDescendant(const std::string &appNodeID, ZG6000::ListStringMap &listDescendant, ZG6000::ErrorInfo &e)
{
    ZG6000::ListStringMap listChild;
    if (!getChild(appNodeID, listChild, e))
        return false;
    if (listChild.empty())
        return true;
    for (const auto& child: listChild)
    {
        const auto& childID = ZGUtils::get(child, "id");
        if (!getChild(childID, listDescendant, e))
            return false;
    }
    std::move(listChild.begin(), listChild.end(), std::back_inserter(listDescendant));
    return true;
}

bool ZGSPAppNodeManagerMng::getAncestor(const std::string &appNodeID, ZG6000::ListStringMap &listAncestor, ZG6000::ErrorInfo &e)
{
    ZG6000::ListStringMap listParent;
    if (!getParent(appNodeID, listParent, e))
        return false;
    if (listParent.empty())
        return true;
    for (const auto& parent: listParent)
    {
        const auto& parentID = ZGUtils::get(parent, "id");
        if (!getParent(parentID, listAncestor, e))
            return false;
    }
    std::move(listParent.begin(), listParent.end(), std::back_inserter(listAncestor));
    return true;
}

bool ZGSPAppNodeManagerMng::getListYv(const std::string& appNodeID, ZG6000::ListStringMap& listYv, ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT a.id FROM mp_param_dataset_yv a LEFT JOIN mp_param_model_yv b ON a.dataModelID = b.id "
                          "WHERE a.appNodeID = '%1' AND b.isEnable = 1 AND (b.isPrivate <> 1 OR b.isPrivate IS NULL) ORDER BY a.id").arg(appNodeID.c_str());
    ZG6000::StringList listYvID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listYvID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPAppNodeManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取应用节点'%1'关联视频失败").arg(appNodeID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listYvID.empty())
        return true;
    const auto& ids = ZGUtils::join(listYvID, ",", "'", "'");
    sql = QString("SELECT a.*, c.userName, c.password, c.aNetAddr FROM mp_param_dataset_yv a "
        "LEFT JOIN mp_param_dataset b ON a.datasetID = b.id "
        "LEFT JOIN mp_param_device c ON c.datasetID = b.id "
        "WHERE a.id IN (%1) ORDER BY a.id").arg(ids.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listYv))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPAppNodeManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取应用节点'%1'视频失败").arg(appNodeID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

void ZGSPAppNodeManagerMng::run()
{
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        msleep(m_initInterval * 1000);
    }
    m_masterInst = ZGRuntime::instance()->isMaster();
    emit initFinished();
}

ZGSPAppNodeManagerMng::ZGSPAppNodeManagerMng(QObject *parent)
    : QThread{parent}
{
    
}

void ZGSPAppNodeManagerMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPAppNodeManagerMng::onCheckStatus);
    connect(this, &ZGSPAppNodeManagerMng::initFinished, this, &ZGSPAppNodeManagerMng::onInitFinished);
}

void ZGSPAppNodeManagerMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGSPAppNodeManagerMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

void ZGSPAppNodeManagerMng::onInitFinished()
{
    m_initialized = true;
    ZGLOG_INFO("ZGSPAppNodeManager init finished.");
    m_checkTimer.start(m_checkInterval * 1000);
}

void ZGSPAppNodeManagerMng::onCheckStatus()
{
    m_masterInst = ZGRuntime::instance()->isMaster();
}

void ZGSPAppNodeManagerMng::onReceivedData(int requestID, QByteArray data, int statusCode)
{
    ZGLOG_TRACE(QString("requestID: %1, data: %2").arg(requestID).arg(data));
}

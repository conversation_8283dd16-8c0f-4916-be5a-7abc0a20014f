#ifndef ZGDPDEVICEMANAGERI_H
#define ZGDPDEVICEMANAGERI_H

#include "ZGDPDeviceManager.h"

namespace ZG6000
{
	class ZGDPDeviceManagerI : public ZGDPDeviceManager
	{
	public:
		ZGDPDeviceManagerI();

	public:
		bool checkState(const Ice::Current& current) override;

	public:
		void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current& current) override;
		bool getDevices(std::string condition, int offset, int limit, std::string orderField, std::string orderType, ListStringMap& devices,
		                ErrorInfo& e, const Ice::Current& current) override;
		bool getDynamicProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current) override;
		bool getFieldsProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current) override;
		bool getModelProperties(std::string modelID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current) override;
		bool getRuntimeProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current) override;
		bool getWholeProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current) override;
		bool getProperty(std::string deviceID, std::string propertyName, bool isExtend, StringMap& property, ErrorInfo& e, const Ice::Current& current) override;
		bool getPropertyValue(std::string deviceID, std::string propertyName, bool isExtend, std::string& propertyValue, ErrorInfo& e, const Ice::Current& current) override;
		bool updatePropertyValue(std::string deviceID, std::string propertyName, bool isExtend, std::string propertyValue, ErrorInfo& e, const Ice::Current& current) override;
		bool addDevice(std::string deviceID, StringMap properties, ErrorInfo& e, const Ice::Current& current) override;
		bool updateDevice(std::string deviceID, StringMap properties, ErrorInfo& e, const Ice::Current& current) override;
		bool removeDevice(std::string deviceID, ErrorInfo& e, const Ice::Current& current) override;
		bool getDeviceProperty(std::string deviceID, std::string property, std::string& value, ErrorInfo& e, const Ice::Current& current) override;
		bool getDeviceResume(std::string deviceID, ListStringMap& listDeviceResume, ErrorInfo& e, const Ice::Current& current) override;
		bool addDeviceResume(std::string deviceID, StringMap deviceResume, ErrorInfo& e, const Ice::Current& current) override;
	};
}

#endif // ZGDPDEVICEMANAGERI_H

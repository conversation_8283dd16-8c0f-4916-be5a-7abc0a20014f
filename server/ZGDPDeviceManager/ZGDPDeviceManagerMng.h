#ifndef ZGDPDEVICEMANAGERMNG_H
#define ZGDPDEVICEMANAGERMNG_H

#include <QThread>
#include <QTimer>
#include <QJsonObject>
#include <QReadWriteLock>
#include "Ice/Ice.h"
#include "ZGProxyCommon.h"

class ZGMqttClient;

class ZGDPDeviceManagerMng : public QThread
{
	Q_OBJECT
public:
	static ZGDPDeviceManagerMng* instance();
	void init();
	bool checkState();
	void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ZG6000::ListRecord listRecord, const Ice::Current& current);
	bool getDevices(std::string condition, int offset, int limit, std::string orderField, std::string orderType, ZG6000::ListStringMap& devices,
	                ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool getModelProperties(std::string modelID, ZG6000::ListStringMap& properties, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool getDynamicProperties(std::string deviceID, ZG6000::ListStringMap& properties, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool getFieldsProperties(std::string deviceID, ZG6000::ListStringMap& properties, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool getRuntimeProperties(std::string deviceID, ZG6000::ListStringMap& properties, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool getWholeProperties(std::string deviceID, ZG6000::ListStringMap& properties, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool getProperty(std::string deviceID, std::string propertyName, bool isExtend, ZG6000::StringMap& property, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool getPropertyValue(std::string deviceID, std::string propertyName, bool isExtend, std::string& propertyValue, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool updatePropertyValue(std::string deviceID, std::string propertyName, bool isExtend, std::string propertyValue, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool addDevice(std::string deviceID, ZG6000::StringMap properties, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool updateDevice(std::string deviceID, ZG6000::StringMap properties, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool removeDevice(std::string deviceID, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool getDeviceProperty(std::string deviceID, std::string property, std::string& value, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool getDeviceResume(std::string deviceID, ZG6000::ListStringMap& listDeviceResume, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool addDeviceResume(std::string deviceID, ZG6000::StringMap deviceResume, ZG6000::ErrorInfo& e, const Ice::Current& current);
	bool equipmentCall(std::function<bool(std::shared_ptr<ZG6000::ZGMPEquipmentManagerPrx >)> func, ZG6000::ErrorInfo& e);

protected:
	void run() override;
	void initEvents();
	void initServerInstConfig();
	bool initServerInstInfo();

signals:
	void initFinished();

private slots:
	void onInitFinished();
	void onCheckStatus();

private:
	explicit ZGDPDeviceManagerMng(QObject* parent = nullptr);

	void initDevFields();
	void initMapFields();
	void initMapFieldName();
	void initProcessor();
	bool initDeviceEquipment();
	bool initModelProperty();
	bool initModelPropertyRule();
	bool initModelDevice();
	bool initDevStates();
	bool initMqttClient();
	bool checkDeviceValid(const std::string& deviceID, ZG6000::ErrorInfo& e);
	bool getAttributeProperties(const std::string& deviceID, ZG6000::ListStringMap& listRecord, ZG6000::ErrorInfo& e);
	bool getFieldsProperties(const std::string& deviceID, ZG6000::StringMap& properties, ZG6000::ErrorInfo& e);
	bool getModelProperties(std::string modelID, ZG6000::ListStringMap& properties, int type, ZG6000::ErrorInfo& e);
	bool getModelProperties(const std::string& modelID, ZG6000::MapStringMap& mapProperties, int type, ZG6000::ErrorInfo& e);
	bool getDynamicProperties(const std::string& deviceID, ZG6000::StringMap& mapProperty, ZG6000::ErrorInfo& e);
	void processDeviceFieldChange(std::string tableName, std::string oper, std::string reason, std::string time,
	                              ZG6000::ListRecord listRecord);
	void processDeviceAttributeChange(std::string tableName, std::string oper, std::string reason, std::string time,
	                              ZG6000::ListRecord listRecord);
	void processModelAttributeChange(std::string tableName, std::string oper, std::string reason, std::string time,
	                              ZG6000::ListRecord listRecord);
	void processEquipmentChange(std::string tableName, std::string oper, std::string reason, std::string time,
	                              ZG6000::ListRecord listRecord);
	bool getPropertyFromFields(const std::string& deviceID, const std::string& propertyName, ZG6000::StringMap& property, ZG6000::ErrorInfo& e);
	bool getPropertyFromFields(const std::string& deviceID, const std::string& propertyName, std::string& propertyValue, ZG6000::ErrorInfo& e);
	bool getPropertyFromRealFields(const std::string& deviceID, const std::string& propertyName, ZG6000::StringMap& property, ZG6000::ErrorInfo& e);
	bool getPropertyFromRealFields(const std::string& deviceID, const std::string& propertyName, std::string& propertyValue, ZG6000::ErrorInfo& e);
	bool getPropertyFromDeviceAttribute(const std::string& deviceID, const std::string& propertyName, ZG6000::StringMap& property, ZG6000::ErrorInfo& e);
	bool getPropertyFromDeviceAttribute(const std::string& deviceID, const std::string& propertyName, std::string& propertyValue, ZG6000::ErrorInfo& e);
	bool getPropertyFromEquipmentServer(const std::string& equipmentID, const std::string& propertyName, ZG6000::StringMap& property, ZG6000::ErrorInfo& e);
	bool getModelPropertyKey(const std::string& deviceID, const std::string& propertyName, std::string& propertyKey, ZG6000::ErrorInfo& e);
	bool getAssociateEquipment(const std::string& deviceID, std::string& equipmentID);
	bool updatePropertyToFields(const std::string& deviceID, const std::string& propertyName, const std::string& propertyValue, ZG6000::ErrorInfo& e);
	bool updatePropertyToRealFields(const std::string& deviceID, const std::string& propertyName, const std::string& propertyValue, ZG6000::ErrorInfo& e);
	bool updatePropertyToDeviceAttribute(const std::string& deviceID, const std::string& propertyName, const std::string& propertyValue, ZG6000::ErrorInfo& e);
	bool updatePropertyToEquipmentServer(const std::string& equipmentID, const std::string& propertyName, const std::string& propertyValue, ZG6000::ErrorInfo& e);
	inline void removeOldKey(const std::string& oldKey);
	inline bool addNewKey(const std::string& id, const std::string& newKey);
	inline bool updateKeyValue(const std::string& id, const std::string& field, const std::string& value);
	template <typename F, typename... ARGS>
	void insertProcessor(const std::string& tableName, F&& f)
	{
		
        m_mapProcessor.insert({tableName, std::bind(std::forward<F>(f), this,
                               std::placeholders::_1, std::placeholders::_2, std::placeholders::_3,
                               std::placeholders::_4, std::placeholders::_5)});
	}
	
private:
	bool m_initialized{false};
	QString m_serverName{""};
	QString m_instName{""};
	bool m_masterInst{false};
	int m_initInterval{10};
	int m_checkInterval{1};
	QTimer m_initTimer;
	QTimer m_checkTimer;
	ZG6000::StringMap m_mapDevField;
	std::set<std::string> m_setFields;
	std::set<std::string> m_setRealFields;
	std::map<std::string, std::pair<std::string, std::string>> m_mapFieldName;
	std::unordered_map<std::string, ZG6000::StringMap> m_mapModelProperty;
	std::unordered_map<std::string, std::set<std::string>> m_mapModelDevice;
	ZG6000::ListStringMap m_listModelPropertyRule;
	ZG6000::StringMap m_mapDevState;
	ZG6000::StringMap m_mapDeviceEquipment;
	ZGMqttClient* m_pMqttClient{nullptr};
	std::unordered_map<std::string, std::function<void(std::string, std::string, std::string, std::string,
	                                         ZG6000::ListRecord listRecord)>> m_mapProcessor;
	QReadWriteLock m_lock1;
	QReadWriteLock m_lock2;
	QReadWriteLock m_lock3;
};

#endif // ZGDPDEVICEMANAGERMNG_H

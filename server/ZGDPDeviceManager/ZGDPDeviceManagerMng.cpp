#include "ZGDPDeviceManagerMng.h"
#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "ZGJson.h"
#include "ZGMqttClient.h"
#include "zgerror/ZGDPDeviceManagerError.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QRandomGenerator>

static ZGDPDeviceManagerMng* g_pInstance = nullptr;

ZGDPDeviceManagerMng* ZGDPDeviceManagerMng::instance()
{
	if (g_pInstance == nullptr)
		g_pInstance = new ZGDPDeviceManagerMng;
	return g_pInstance;
}

void ZGDPDeviceManagerMng::init()
{
	initEvents();
	initServerInstConfig();
	initProcessor();
	start();
	ZGLOG_INFO("ZGDPDeviceManager init start...");
}

bool ZGDPDeviceManagerMng::checkState()
{
	return m_initialized;
}

void ZGDPDeviceManagerMng::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ZG6000::ListRecord listRecord, const Ice::Current& current)
{
	if (!m_initialized)
		return;
	if (!m_masterInst)
		return;
	auto it = m_mapProcessor.find(tableName);
	if (it != m_mapProcessor.end())
		it->second(tableName, oper, reason, time, std::move(listRecord));
}

bool ZGDPDeviceManagerMng::getDevices(std::string condition, int offset, int limit, std::string orderField, std::string orderType, ZG6000::ListStringMap& devices, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	// 这里先计算后面的部分，否则如果condition中带有"LIKE '%1'"这种条件会将%1字符当做格式化字符，生成字符串内容错误
	QString addition = QString(" ORDER BY %1 %2 LIMIT %3, %4").arg(orderField.c_str())
	                                                          .arg(orderType.c_str()).arg(offset).arg(limit);
	QString sql = QString("SELECT a.id, a.name AS name, a.typeID, b.name AS typeName, a.modelID, c.name AS modelName, "
		"a.productModelID, d.name AS productModelName, a.manufactorID, e.name AS manufactorName, "
		"a.organID, f.name AS organName, a.productDate, a.unitID, g.name AS unitName FROM dp_param_device a "
		"LEFT JOIN dp_dict_device_type b ON a.typeID = b.id LEFT JOIN dp_param_device_model c ON a.modelID = c.id "
		"LEFT JOIN dp_dict_product_model d ON a.productModelID = d.id LEFT JOIN dp_dict_device_manufactory e ON a.manufactorID = e.id "
		"LEFT JOIN sp_param_hrm_organ f ON a.organID = f.id "
		"LEFT JOIN sp_dict_unit g ON a.unitID = g.id WHERE %1").arg(condition.c_str()) + addition;
	auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
	if (dbProxy == nullptr)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
		e.errDetail = u8"获取数据服务代理失败";
		ZGLOG_ERROR(e);
		return false;
	}
	try
	{
		if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), devices, e))
			return false;
	}
	catch (const Ice::Exception& ie)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
		e.errDetail = ie.what();
		ZGLOG_ERROR(e);
		return false;
	}
	ZG6000::StringList listDeviceID;
	for (auto& device: devices)
	{
		listDeviceID.push_back(device["deviceID"]);
	}
	ZG6000::ListStringMap listRecord;
	if (!ZGProxyCommon::mgetDataByFields("dp_param_device", listDeviceID, {"rtStateID", "rtCoordinates", "rtHealthRuleID"},
		listRecord))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
		e.errDetail = u8"获取设备实时数据失败";
		ZGLOG_ERROR(e);
		return false;
	}
	for (size_t i = 0; i < listDeviceID.size(); ++i)
	{
		const auto& rtStateID = listRecord[i]["rtStateID"];
		std::string rtStateName;
		ZGProxyCommon::getDataByField("dp_dict_device_state", rtStateID, "name", rtStateName);
		devices[i].insert(std::make_pair("rtStateID", rtStateID));
		devices[i].insert(std::make_pair("rtStateName", rtStateName));
		const auto& rtHealthRuleID = listRecord[i]["rtHealthRuleID"];
		std::string rtHealthRuleName;
		ZGProxyCommon::getDataByField("sp_param_rule", rtHealthRuleID, "name", rtHealthRuleName);
		devices[i].insert(std::make_pair("rtHealthRuleID", rtHealthRuleID));
		devices[i].insert(std::make_pair("rtHealthRuleName", rtHealthRuleName));
	}
	return true;
}

bool ZGDPDeviceManagerMng::getModelProperties(std::string modelID, ZG6000::ListStringMap& properties, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	return getModelProperties(modelID, properties, 0, e);
}

bool ZGDPDeviceManagerMng::getDynamicProperties(std::string deviceID, ZG6000::ListStringMap& properties, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	std::string modelID;
	std::string sql = "SELECT modelID FROM dp_param_device WHERE id = '" + deviceID + "'";
	if (!ZGProxyCommon::execQuerySqlField(sql, modelID))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = u8"获取设备'" + deviceID + u8"'模型ID失败";
		ZGLOG_ERROR(e);
		return false;
	}
	ZG6000::MapStringMap mapProperties;
	if (!getModelProperties(modelID, mapProperties, 1, e))
		return false;
	ZG6000::StringMap mapProperty;
	if (!getDynamicProperties(deviceID, mapProperty, e))
		return false;
	for (const auto& [name, value]: mapProperty)
	{
		auto pair = mapProperties.find(name);
		if (pair != mapProperties.end())
		{
			pair->second["value"] = value;
			properties.push_back(pair->second);
		}
	}
	return true;
}

bool ZGDPDeviceManagerMng::getFieldsProperties(std::string deviceID, ZG6000::ListStringMap& properties, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	ZG6000::StringMap mapProperty;
	if (!getFieldsProperties(deviceID, mapProperty, e))
		return false;
	for (auto& pair: mapProperty)
	{
		ZG6000::StringMap property;
		property["name"] = pair.first;
		property["value"] = pair.second;
		const auto& it = m_mapDevField.find(pair.first);
		property["desc"] = (it == m_mapDevField.end() ? "" : it->second);
		property["typeID"] = "";
		property["typeName"] = u8"公共属性";
		properties.push_back(std::move(property));
	}
	return true;
}

bool ZGDPDeviceManagerMng::getRuntimeProperties(std::string deviceID, ZG6000::ListStringMap& properties, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	std::string equipmentID;
	bool find = true;
	{
		QReadLocker locker(&m_lock1);
		auto pair = m_mapDeviceEquipment.find(deviceID);
		if (pair == m_mapDeviceEquipment.end())
			find = false;
		else
			equipmentID = pair->second;
	}
	if (!find)
		return true;
	auto equipmentPrx = ZGProxyMng::instance()->getProxyMPEquipmentManager();
	if (equipmentPrx == nullptr)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
		e.errDetail = u8"获取一次设备管理服务代理失败";
		ZGLOG_ERROR(e);
		return false;
	}
	try
	{
		if (!equipmentPrx->getRuntimeProperties(equipmentID, properties, e))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
			e.errDetail = u8"获取一次设备'" + equipmentID + u8"'运行属性失败";
			return false;
		}
		for (auto& property: properties)
		{
			property["typeID"] = "";
			property["typeName"] = u8"运行属性";
		}
		return true;
	}
	catch (const Ice::Exception& ie)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
		e.errDetail = ie.what();
		return false;
	}
}

bool ZGDPDeviceManagerMng::getWholeProperties(std::string deviceID, ZG6000::ListStringMap& properties, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	ZG6000::ListStringMap fieldsProperties;
	if (!getFieldsProperties(deviceID, fieldsProperties, e, current))
		return false;
	std::move(fieldsProperties.begin(), fieldsProperties.end(), std::back_inserter(properties));
	std::string modelID;
	if (!ZGProxyCommon::getDataByField("dp_param_device", deviceID, "modelID", modelID))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
		e.errDetail = u8"获取设备'" + deviceID + u8"'模型ID失败";
		ZGLOG_ERROR(e);
		return false;
	}
	ZG6000::ListStringMap modelProperties;
	if (!getModelProperties(modelID, modelProperties, 2, e))
		return false;
	if (!modelProperties.empty())
		std::move(modelProperties.begin(), modelProperties.end(), std::back_inserter(properties));
	ZG6000::ListStringMap attributeProperties;
	if (!getDynamicProperties(deviceID, attributeProperties, e, current))
		return false;
	if (!attributeProperties.empty())
		std::move(attributeProperties.begin(), attributeProperties.end(), std::back_inserter(properties));
	ZG6000::ListStringMap runtimeProperties;
	if (!getRuntimeProperties(deviceID, runtimeProperties, e, current))
		return false;
	if (!runtimeProperties.empty())
		std::move(runtimeProperties.begin(), runtimeProperties.end(), std::back_inserter(properties));
	return true;
}

bool ZGDPDeviceManagerMng::getProperty(std::string deviceID, std::string propertyName, bool isExtend, ZG6000::StringMap& property, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	if (m_setFields.find(propertyName) != m_setFields.end())
		return getPropertyFromFields(deviceID, propertyName, property, e);
	if (m_setRealFields.find(propertyName) != m_setRealFields.end())
		return getPropertyFromRealFields(deviceID, propertyName, property, e);
	std::string modelProperty;
	if (!getModelPropertyKey(deviceID, propertyName, modelProperty, e))
		return false;
	auto pair = m_mapModelProperty.find(modelProperty);
	if (pair != m_mapModelProperty.end())
	{
		if (pair->second["isDynamic"] != "1")
		{
			std::string propertyValue;
			propertyValue = pair->second["defaultValue"];
			property["value"] = propertyValue;
			return true;
		}
		return getPropertyFromDeviceAttribute(deviceID, propertyName, property, e);
	}
	std::string equipmentID;
	if (!getAssociateEquipment(deviceID, equipmentID))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
		e.errDetail = QStringLiteral("设备'%1'没有指定的属性'%2'")
			.arg(deviceID.c_str()).arg(propertyName.c_str()).toStdString();
		return false;
	}
    return equipmentCall([&equipmentID, &propertyName, &property, &e](std::shared_ptr<ZG6000::ZGMPEquipmentManagerPrx> equipPrx) ->bool
	{
		return equipPrx->getProperty(equipmentID, propertyName, false, property, e);
	}, e);
}

bool ZGDPDeviceManagerMng::getPropertyValue(std::string deviceID, std::string propertyName, bool isExtend, std::string& propertyValue, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	if (m_setFields.find(propertyName) != m_setFields.end())
		return getPropertyFromFields(deviceID, propertyName, propertyValue, e);
	if (m_setRealFields.find(propertyName) != m_setRealFields.end())
		return getPropertyFromRealFields(deviceID, propertyName, propertyValue, e);
	std::string modelProperty;
	if (!getModelPropertyKey(deviceID, propertyName, modelProperty, e))
		return false;
	auto pair = m_mapModelProperty.find(modelProperty);
	if (pair != m_mapModelProperty.end())
	{
		if (pair->second["isDynamic"] != "1")
		{
			propertyValue = pair->second["defaultValue"];
			return true;
		}
		return getPropertyFromDeviceAttribute(deviceID, propertyName, propertyValue, e);
	}
	std::string equipmentID;
	if (!getAssociateEquipment(deviceID, equipmentID))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
		e.errDetail = QStringLiteral("设备'%1'没有指定的属性'%2'")
			.arg(deviceID.c_str()).arg(propertyName.c_str()).toStdString();
		return false;
	}
    return equipmentCall([&equipmentID, &propertyName, &propertyValue, &e](std::shared_ptr<ZG6000::ZGMPEquipmentManagerPrx> equipPrx) ->bool
	{
		return equipPrx->getPropertyValue(equipmentID, propertyName, false, propertyValue, e);
	}, e);
}

bool ZGDPDeviceManagerMng::updatePropertyValue(std::string deviceID, std::string propertyName, bool isExtend, std::string propertyValue, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	if (m_setFields.find(propertyName) != m_setFields.end())
		return updatePropertyToFields(deviceID, propertyName, propertyValue, e);
	if (m_setRealFields.find(propertyName) != m_setRealFields.end())
		return updatePropertyToRealFields(deviceID, propertyName, propertyValue, e);
	std::string modelProperty;
	if (!getModelPropertyKey(deviceID, propertyName, modelProperty, e))
		return false;
	auto pair = m_mapModelProperty.find(modelProperty);
	if (pair != m_mapModelProperty.end())
	{
		if (pair->second["isDynamic"] != "1")
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
			e.errDetail = u8"不允许更新设备'" + deviceID + u8"'非动态属性";
			ZGLOG_ERROR(e);
			return false;
		}
		return updatePropertyToDeviceAttribute(deviceID, propertyName, propertyValue, e);
	}
	std::string equipmentID;
	if (!getAssociateEquipment(deviceID, equipmentID))
		return false;
	return updatePropertyToEquipmentServer(equipmentID, propertyName, propertyValue, e);
}

bool ZGDPDeviceManagerMng::removeDevice(std::string deviceID, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	ZG6000::StringList listSql;
	std::string sql = "DELETE FROM dp_param_device WHERE id = '" + deviceID + "';";
	listSql.push_back(sql);
	sql = "DELETE FROM dp_param_device_attribute WHERE deviceID = '" + deviceID + "';";
	listSql.push_back(sql);
	sql = "SELECT healthRuleID FROM dp_param_device_health WHERE deviceID = '" + deviceID + "'";
	ZG6000::StringList listRuleID;
	if (ZGProxyCommon::execQuerySqlCol(sql, listRuleID))
	{
		if (!listRuleID.empty())
		{
			sql = "DELETE FROM dp_param_device_health WHERE deviceID = '" + deviceID + "';";
			listSql.push_back(sql);
			std::string condition;
			for (const auto& ruleID: listRuleID)
			{
				condition += "'" + ruleID + "',";
			}
			condition.pop_back();
			sql = "DELETE FROM sp_param_rule WHERE id IN (" + condition + ");";
			listSql.push_back(sql);
		}
	}
	if (!ZGProxyCommon::execBatchSql(listSql))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = u8"删除设备'" + deviceID + u8"'失败";
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::addDevice(std::string deviceID, ZG6000::StringMap properties, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	QStringList listField{"id"}, listValue;
	listValue.append(QString("'%1'").arg(deviceID.c_str()));
	ZG6000::StringList listSql;
	ZG6000::StringMap mapFieldValue;
	bool hasModel = false;
	std::string modelID;
	const auto& it = properties.find("modelID");
	if (it != properties.end())
	{
		modelID = it->second;
		hasModel = true;
	}
	for (const auto& [name, value]: properties)
	{
		const auto& itField = m_setFields.find(name);
		const auto& itRealField = m_setRealFields.find(name);
		if (itField != m_setFields.end() || itRealField != m_setRealFields.end())
		{
			listField.append(name.c_str());
			listValue.append(QString("'%1'").arg(value.c_str()));
		}
		else
		{
			if (!hasModel)
			{
				e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
				e.errDetail = u8"无效的属性名'" + name + "'";
				ZGLOG_ERROR(e);
				return false;
			}
			QString sql = QString("INSERT INTO dp_param_device_attribute (deviceID,modelID,attributeName,attributeValue) "
				"VALUES ('%1','%2','%3','%4');").arg(deviceID.c_str()).arg(modelID.c_str()).arg(name.c_str()).arg(value.c_str());
			listSql.push_back(sql.toStdString());
		}
	}
	QString fields = listField.join(",");
	QString values = listValue.join(",");
	QString sql = QString("INSERT INTO dp_param_device (%1) VALUES (%2);").arg(fields).arg(values);
	listSql.push_back(sql.toStdString());
	if (!ZGProxyCommon::execBatchSql(listSql))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = u8"添加设备'" + std::move(deviceID) + u8"'失败";
		ZGLOG_ERROR(e);
		return false;
	}
	auto modifyProxy = ZGProxyMng::instance()->getProxySPModifyOnline();
	if (modifyProxy)
	{
		try
		{
			modifyProxy->syncData(e);
		}
		catch (const Ice::Exception& ie)
		{
			ZGLOG_ERROR(ie.what());
		}
	}
	return true;
}

bool ZGDPDeviceManagerMng::updateDevice(std::string deviceID, ZG6000::StringMap properties, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	QStringList listFieldValue;
	ZG6000::StringMap mapFieldValue;
	ZG6000::StringList listSql;
	bool hasModel = false;
	std::string modelID;
	auto it = properties.find("modelID");
	if (it != properties.end())
	{
		modelID = it->second;
		hasModel = true;
	}
	if (hasModel)
	{
		QString sql = QString("DELETE FROM dp_param_device_attribute WHERE deviceID = '%1';").arg(deviceID.c_str());
		listSql.push_back(sql.toStdString());
	}
	for (const auto& [name, value]: properties)
	{
		const auto& itField = m_setFields.find(name);
		const auto& itRealField = m_setRealFields.find(name);
		if (itField != m_setFields.end())
		{
			listFieldValue.append(QString("%1='%2'").arg(name.c_str()).arg(value.c_str()));
		}
		else if (itRealField != m_setRealFields.end())
		{
			mapFieldValue[name] = value;
		}
		else
		{
			QString sql;
			if (!modelID.empty())
			{
				sql = QString("INSERT INTO dp_param_device_attribute (deviceID,modelID,attributeName,attributeValue) "
					"VALUES ('%1','%2','%3','%4');").arg(deviceID.c_str()).arg(modelID.c_str()).arg(name.c_str()).arg(value.c_str());
			}
			else
			{
				sql = QString("UPDATE dp_param_device_attribute SET attributeValue = '%1' WHERE "
						"deviceID = '%2' AND attributeName = '%3';").arg(value.c_str()).arg(deviceID.c_str())
						                                            .arg(name.c_str());
			}
			listSql.push_back(sql.toStdString());
		}
	}
	if (!listFieldValue.isEmpty())
	{
		QString fieldValues = listFieldValue.join(",");
		QString sql = QString("UPDATE dp_param_device SET %1 WHERE id = '%2'").arg(fieldValues).arg(deviceID.c_str());
		listSql.push_back(sql.toStdString());
	}
	if (!listSql.empty())
	{
		if (!ZGProxyCommon::execBatchSql(listSql))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
			e.errDetail = u8"更新设备'" + deviceID + u8"'失败";
			ZGLOG_ERROR(e);
			return false;
		}
		auto modifyProxy = ZGProxyMng::instance()->getProxySPModifyOnline();
		if (modifyProxy)
		{
			try
			{
				modifyProxy->syncData(e);
			}
			catch (const Ice::Exception& ie)
			{
				ZGLOG_ERROR(ie.what());
			}
		}
	}
	if (!mapFieldValue.empty())
	{
		if (!ZGProxyCommon::updateDataByID("dp_param_device", deviceID, mapFieldValue))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
			e.errDetail = u8"更新设备'" + deviceID + u8"'实时状态失败";
			ZGLOG_ERROR(e);
			return false;
		}
	}
	return true;
}

bool ZGDPDeviceManagerMng::getDeviceProperty(std::string deviceID, std::string property, std::string& value, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	auto it = m_setFields.find(property);
	if (it != m_setFields.end())
	{
		if (!ZGProxyCommon::getDataByField("dp_param_device", deviceID, property, value))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
			e.errDetail = QStringLiteral("获取设备'%1'字段属性'%2'失败").arg(deviceID.c_str()).arg(property.c_str()).toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
	}
	else
	{
		std::string modelID;
		if (!ZGProxyCommon::getDataByField("dp_param_device", deviceID, "modelID", modelID))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
			e.errDetail = QStringLiteral("获取设备'%1'模型ID失败").arg(deviceID.c_str()).toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
		QString sql = QString("SELECT isDynamic, defaultValue FROM dp_param_device_model_attribute WHERE modelID = '%1' AND name = '%2'")
		              .arg(modelID.c_str()).arg(property.c_str());
		ZG6000::ListStringMap listRecord;
		if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
			e.errDetail = QStringLiteral("获取模型'%1'属性失败").arg(modelID.c_str()).toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
		if (listRecord.empty())
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
			e.errDetail = QStringLiteral("未知的设备属性'%1'").arg(property.c_str()).toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
		const auto& record = listRecord[0];
		try
		{
			const auto& isDynamic = ZGUtils::get(record, "isDynamic");
			if (isDynamic != "1")
				value = ZGUtils::get(record, "defaultValue");
			else
			{
				sql = QString("SELECT attributeValue FROM dp_param_device_attribute WHERE deviceID = '%1' AND attributeName = '%2'")
				      .arg(deviceID.c_str()).arg(property.c_str());
				if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), value))
				{
					e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
					e.errDetail = QStringLiteral("获取设备'%1'属性失败").arg(deviceID.c_str()).toStdString();
					ZGLOG_ERROR(e);
					return false;
				}
			}
		}
		catch (const std::exception& ex)
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
			e.errDetail = ex.what();
			ZGLOG_ERROR(e);
			return false;
		}
	}
	return true;
}

void ZGDPDeviceManagerMng::run()
{
	while (!initServerInstInfo())
	{
		ZGLOG_ERROR("initServerInstInfo error.");
		msleep(m_initInterval * 1000);
	}
	while (!initDeviceEquipment())
	{
		ZGLOG_ERROR("initDeviceEquipment error.");
		msleep(m_initInterval * 1000);
	}
	while (!initModelProperty())
	{
		ZGLOG_ERROR("initModelProperty error.");
		msleep(m_initInterval * 1000);
	}
	while (!initModelDevice())
	{
		ZGLOG_ERROR("initModelDevice error.");
		msleep(m_initInterval * 1000);
	}
	while (!initModelPropertyRule())
	{
		ZGLOG_ERROR("initModelPropertyRule error.");
		msleep(m_initInterval * 1000);
	}
	sleep(QRandomGenerator::global()->bounded(5, 10));
	m_masterInst = ZGRuntime::instance()->isMaster();
	emit initFinished();
}

void ZGDPDeviceManagerMng::initEvents()
{
	connect(&m_checkTimer, &QTimer::timeout, this, &ZGDPDeviceManagerMng::onCheckStatus);
	connect(this, &ZGDPDeviceManagerMng::initFinished, this, &ZGDPDeviceManagerMng::onInitFinished);
}

void ZGDPDeviceManagerMng::initServerInstConfig()
{
	const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
	QString errMsg;
	int value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_initInterval = value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_checkInterval = value;
}

bool ZGDPDeviceManagerMng::initServerInstInfo()
{
	m_serverName = ZGRuntime::instance()->getServerID();
	if (m_serverName.isEmpty())
	{
		ZGLOG_ERROR("Empty server id.");
		return false;
	}
	m_instName = ZGRuntime::instance()->getInstanceID();
	if (m_instName.isEmpty())
	{
		ZGLOG_ERROR("Empty server instance id.");
		return false;
	}
	return true;
}

void ZGDPDeviceManagerMng::onInitFinished()
{
	while (!initMqttClient())
	{
		ZGLOG_ERROR("initMqttClient error.");
		msleep(m_initInterval * 1000);
	}
	m_initialized = true;
	ZGLOG_INFO("ZGDPDeviceManager init finished.");
	m_checkTimer.start(m_checkInterval * 1000);
}

void ZGDPDeviceManagerMng::onCheckStatus()
{
	m_masterInst = ZGRuntime::instance()->isMaster();
	if (!m_masterInst)
		return;
	auto scriptProxy = ZGProxyMng::instance()->getProxySPScriptProcess();
	if (scriptProxy == nullptr)
	{
		ZGLOG_ERROR(QStringLiteral("获取脚本服务代理对象失败"));
		return;
	}
	ZG6000::ListStringMap listModelPropetyRule;
	{
		QReadLocker locker(&m_lock3);
		listModelPropetyRule = m_listModelPropertyRule;
	}
	QDateTime currTime = QDateTime::currentDateTime();
	for (size_t i = 0; i < listModelPropetyRule.size(); ++i)
	{
		QDateTime lastTime;
		if (!ZGUtils::StringToDateTime(listModelPropetyRule[i]["rtUpdateTime"].c_str(), lastTime))
		{
			{
				QWriteLocker locker(&m_lock3);
				m_listModelPropertyRule[i]["rtUpdateTime"] = ZGUtils::DateTimeToString(currTime).toStdString();
			}
			continue;
		}
		if (lastTime.secsTo(currTime) >= std::atoi(listModelPropetyRule[i]["execInterval"].c_str()))
		{
			{
				QWriteLocker locker(&m_lock3);
				m_listModelPropertyRule[i]["rtUpdateTime"] = ZGUtils::DateTimeToString(currTime).toStdString();
			}
			auto pair = m_mapModelDevice.find(listModelPropetyRule[i]["modelID"]);
			if (pair != m_mapModelDevice.end())
			{
				ZG6000::ListStringMap listExpression;
				const auto& expressionID = listModelPropetyRule[i]["expressionID"];
				const auto& expressionParam = listModelPropetyRule[i]["expressionParam"];
				size_t pos = std::string::npos;
				if (!expressionParam.empty())
				{
					pos = expressionParam.find_first_of("[");
					if (pos == std::string::npos)
					{
						ZGLOG_ERROR(QStringLiteral("无效的表达式参数'%1'").arg(expressionParam.c_str()));
						continue;
					}
				}
				for (const auto& deviceID : pair->second)
				{
					ZG6000::StringMap expression;
					expression.insert({"id", expressionID});
					if (expressionParam.empty())
						expression.insert({"param", QString("[\"%1\"]").arg(deviceID.c_str()).toStdString()});
					else
					{
						if (pos != std::string::npos)
						{
							std::string insertParam = "\"" + deviceID + "\", ";
							std::string newExpressionParam = expressionParam;
							newExpressionParam.insert(pos + 1, insertParam.c_str());
							expression.insert({"param", newExpressionParam});
						}
					}
					ZG6000::ErrorInfo e;
					if (!scriptProxy->callJson(expression["id"], expression["param"], e))
						ZGLOG_ERROR(ZGJson::convertToJson(e).c_str());
					// listExpression.push_back(std::move(expression));
				}
				try
				{
					// scriptProxy->callBatch(listExpression);
				}
				catch (const Ice::Exception& e)
				{
					ZGLOG_ERROR(e.what());
				}
			}
		}
	}
}

void ZGDPDeviceManagerMng::initDevFields()
{
	m_mapDevField.insert(std::make_pair("name", u8"设备名称"));
	m_mapDevField.insert(std::make_pair("typeID", u8"设备类型ID"));
	m_mapDevField.insert(std::make_pair("typeName", u8"设备类型"));
	m_mapDevField.insert(std::make_pair("modelID", u8"模型ID"));
	m_mapDevField.insert(std::make_pair("modelName", u8"模型名称"));
	m_mapDevField.insert(std::make_pair("productModelID", u8"型号ID"));
	m_mapDevField.insert(std::make_pair("productModelName", u8"型号"));
	m_mapDevField.insert(std::make_pair("manufactorID", u8"制造商ID"));
	m_mapDevField.insert(std::make_pair("manufactorName", u8"制造商"));
	m_mapDevField.insert(std::make_pair("organID", u8"部门ID"));
	m_mapDevField.insert(std::make_pair("organName", u8"所属部门"));
	m_mapDevField.insert(std::make_pair("productDate", u8"生产日期"));
	m_mapDevField.insert(std::make_pair("unitID", u8"单位ID"));
	m_mapDevField.insert(std::make_pair("unitName", u8"单位"));
	m_mapDevField.insert(std::make_pair("rtStateID", u8"设备状态ID"));
	m_mapDevField.insert(std::make_pair("rtStateName", u8"设备状态"));
	m_mapDevField.insert(std::make_pair("rtCoordinates", u8"坐标位置"));
	m_mapDevField.insert(std::make_pair("rtHealthRuleID", u8"健康规则ID"));
	m_mapDevField.insert(std::make_pair("rtHealthRuleName", u8"健康规则"));
}

void ZGDPDeviceManagerMng::initMapFields()
{
	m_setFields.emplace("name");
	m_setFields.emplace("typeID");
	m_setFields.emplace("modelID");
	m_setFields.emplace("productModelID");
	m_setFields.emplace("manufactorID");
	m_setFields.emplace("organID");
	m_setFields.emplace("productDate");
	m_setFields.emplace("amount");
	m_setFields.emplace("unitID");
	m_setFields.emplace("rfid");
	m_setFields.emplace("qrcode");
	m_setFields.emplace("brcode");
	m_setRealFields.emplace("rtStateID");
	m_setRealFields.emplace("rtCoordinates");
	m_setRealFields.emplace("rtHealthRuleID");
}

void ZGDPDeviceManagerMng::initMapFieldName()
{
	m_mapFieldName.insert({"typeID", {"dp_dict_device_type", "typeName"}});
	m_mapFieldName.insert({"modelID", {"dp_param_device_model", "modelName"}});
	m_mapFieldName.insert({"productModelID", {"dp_dict_product_model", "productModelName"}});
	m_mapFieldName.insert({"manufactorID", {"dp_dict_device_manufactory", "manufactorName"}});
	m_mapFieldName.insert({"organID", {"sp_param_hrm_organ", "organName"}});
	m_mapFieldName.insert({"rtStateID", {"dp_dict_device_state", "rtStateName"}});
	m_mapFieldName.insert({"rtHealthRuleID", {"sp_param_rule", "rtHealthRuleName"}});
}

void ZGDPDeviceManagerMng::initProcessor()
{
    insertProcessor("dp_param_device", &ZGDPDeviceManagerMng::processDeviceFieldChange);
    insertProcessor("dp_param_device_attribute", &ZGDPDeviceManagerMng::processDeviceAttributeChange);
    insertProcessor("dp_param_device_model_attribute", &ZGDPDeviceManagerMng::processModelAttributeChange);
    insertProcessor("mp_param_equipment", &ZGDPDeviceManagerMng::processEquipmentChange);
}

bool ZGDPDeviceManagerMng::initDeviceEquipment()
{
	std::string sql = "SELECT dpDeviceID AS id, id AS equipmentID FROM mp_param_device WHERE dpDeviceID != ''";
	QWriteLocker locker(&m_lock1);
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDeviceEquipment))
	{
		ZGLOG_ERROR(QStringLiteral("获取设备关联一次设备参数失败"));
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::initModelProperty()
{
	std::string sql = "SELECT CONCAT(modelID,'/',name) as id, id AS no, isDynamic, defaultValue FROM dp_param_device_model_attribute";
	ZG6000::MapStringMap mapRecord;
	if (!ZGProxyCommon::execQuerySql(sql, mapRecord))
	{
		ZGLOG_ERROR(QStringLiteral("获取设备模型属性失败"));
		return false;
	}
	for (auto&& [key, record] : mapRecord)
	{
		m_mapModelProperty.insert({std::move(key), std::move(record)});
	}
	return true;
}

bool ZGDPDeviceManagerMng::initModelPropertyRule()
{
	std::string sql = "SELECT modelID, propertyName, expressionID, expressionParam, execInterval "
		"FROM dp_param_device_model_attribute_rule WHERE isEnabled = 1";
	if (!ZGProxyCommon::execQuerySql(sql, m_listModelPropertyRule))
	{
		ZGLOG_ERROR(QStringLiteral("获取设备属性规则失败"));
		return false;
	}
	std::string currTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
	for (auto& modelPropertyRule: m_listModelPropertyRule)
	{
		if (modelPropertyRule["execInterval"].empty())
			modelPropertyRule["execInterval"] = "30";
		const auto& execInterval = modelPropertyRule["execInterval"];
		int interval = std::atoi(execInterval.c_str());
		if (interval <= 0)
			interval = 30;
		interval = QRandomGenerator::global()->bounded(interval - interval / 2, interval + interval / 2);
		modelPropertyRule["execInterval"] = std::to_string(interval);
		modelPropertyRule["rtUpdateTime"] = currTime;
	}
	return true;
}

bool ZGDPDeviceManagerMng::initModelDevice()
{
	std::string sql = "SELECT id, modelID FROM dp_param_device";
	ZG6000::ListStringMap listRecord;
	if (!ZGProxyCommon::execQuerySql(sql, listRecord))
	{
		ZGLOG_ERROR(QStringLiteral("获取设备模型失败"));
		return false;
	}
	for (auto& record: listRecord)
	{
		if (record["modelID"].empty())
			continue;
		auto pair = m_mapModelDevice.find(record["modelID"]);
		if (pair == m_mapModelDevice.end())
			m_mapModelDevice.insert({std::move(record["modelID"]), std::set<std::string>{std::move(record["id"])}});
		else
			pair->second.insert(std::move(record["id"]));
	}
	return true;
}

bool ZGDPDeviceManagerMng::initDevStates()
{
	std::string sql = "SELECT id, name FROM dp_dict_device_state";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDevState))
	{
		ZGLOG_ERROR(QStringLiteral("初始化设备状态失败"));
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::initMqttClient()
{
	if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
	{
		ZGLOG_ERROR("initMqttClient error.");
		return false;
	}
	m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
	if (m_pMqttClient == nullptr)
	{
		ZGLOG_ERROR("getMqttClientMessage error.");
		return false;
	}
	m_pMqttClient->connectToHost();
	return true;
}

ZGDPDeviceManagerMng::ZGDPDeviceManagerMng(QObject* parent)
	: QThread{parent}
{
	initDevFields();
	initMapFields();
	initDevStates();
	initMapFieldName();
}

bool ZGDPDeviceManagerMng::checkDeviceValid(const std::string& deviceID, ZG6000::ErrorInfo& e)
{
	std::string sql = "SELECT id FROM dp_param_device WHERE id = '" + deviceID + "'";
	std::string result;
	if (!ZGProxyCommon::execQuerySqlField(sql, result))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = u8"获取设备'" + deviceID + u8"'表记录失败";
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::getAttributeProperties(const std::string& deviceID, ZG6000::ListStringMap& listRecord, ZG6000::ErrorInfo& e)
{
	std::string sql = "SELECT dp_param_device_attribute.name AS name, "
		"dp_param_device_attribute.attributeValue AS value, dp_param_device_attribute.attributeDesc AS desc, "
		"sp_dict_device_attribute_type.id AS typeID, sp_dict_device_attribute_type.name AS typeName "
		"FROM dp_param_device_attribute LEFT JOIN sp_dict_device_attribute_type "
		"ON dp_param_device_attribute.attributeTypeID = sp_dict_device_attribute_type.id WHERE deviceID = '"
		+ deviceID + "' ORDER BY typeName";
	if (!ZGProxyCommon::execQuerySql(sql, listRecord))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = u8"查询设备属性失败";
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::getFieldsProperties(const std::string& deviceID, ZG6000::StringMap& properties, ZG6000::ErrorInfo& e)
{
	std::string sql = "SELECT a.id AS id, a.name AS name, a.typeID, b.name AS typeName, a.modelID, c.name AS modelName, "
		"a.productModelID, d.name AS productModelName, a.manufactorID, e.name AS manufactorName, "
		"a.organID, f.name AS organName, a.productDate, a.amount, a.unitID, g.name AS unitName, a.rfid, a.qrcode, a.brcode FROM dp_param_device a "
		"LEFT JOIN dp_dict_device_type b ON a.typeID = b.id LEFT JOIN dp_param_device_model c ON a.modelID = c.id "
		"LEFT JOIN dp_dict_product_model d ON a.productModelID = d.id LEFT JOIN dp_dict_device_manufactory e ON a.manufactorID = e.id "
		"LEFT JOIN sp_param_hrm_organ f ON a.organID = f.id "
		"LEFT JOIN sp_dict_unit g ON a.unitID = g.id WHERE a.id = '" + deviceID + "'";
	ZG6000::ListStringMap listRecord;
	if (!ZGProxyCommon::execQuerySql(sql, listRecord))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = u8"查询设备字段失败";
		ZGLOG_ERROR(e);
		return false;
	}
	if (listRecord.empty())
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = QStringLiteral("设备%1不存在").arg(deviceID.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	properties = std::move(listRecord[0]);
	ZG6000::StringMap dynamicValues;
	if (!ZGProxyCommon::getDataByFields("dp_param_device", deviceID, {"rtStateID", "rtCoordinates", "rtHealthRuleID"}, dynamicValues))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = u8"获取设备实时数据失败";
		ZGLOG_ERROR(e);
		return false;
	}
	const auto& rtStateID = dynamicValues["rtStateID"];
	dynamicValues["rtStateName"] = "";
	const auto& pair = m_mapDevState.find(rtStateID);
	if (pair != m_mapDevState.end())
		dynamicValues["rtStateName"] = pair->second;
	const auto& rtHealthRuleID = dynamicValues["rtHealthRuleID"];
	dynamicValues["rtHealthRuleName"] = "";
	std::string rtHealthRuleName;
	if (ZGProxyCommon::getDataByField("sp_param_rule", rtHealthRuleID, "name", rtHealthRuleName))
		dynamicValues["rtHealthRuleName"] = rtHealthRuleName;
	properties.insert(dynamicValues.begin(), dynamicValues.end());
	return true;
}

bool ZGDPDeviceManagerMng::getModelProperties(std::string modelID, ZG6000::ListStringMap& properties, int type, ZG6000::ErrorInfo& e)
{
	QString sql = QString("SELECT a.name, a.isDynamic, a.modelID, a.attributeTypeID AS typeID, b.`name` AS typeName, "
		"description AS `desc`, unitID, c.name AS unitName, defaultValue AS value FROM dp_param_device_model_attribute a "
		"LEFT JOIN dp_dict_device_attribute_type b ON a.attributeTypeID = b.id "
		"LEFT JOIN sp_dict_unit c ON a.unitID = c.id WHERE modelID = '%1'").arg(modelID.c_str());
	if (type == 1)
		sql += QString(" AND a.isDynamic = 1");
	else if (type == 2)
		sql += QString(" AND a.isDynamic = 0");
	if (!ZGProxyCommon::execQuerySql(sql.toStdString(), properties))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = QStringLiteral("查询模型'%1'属性失败").arg(modelID.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::getModelProperties(const std::string& modelID, ZG6000::MapStringMap& mapProperties, int type, ZG6000::ErrorInfo& e)
{
	ZG6000::ListStringMap listProperties;
	if (!getModelProperties(modelID, listProperties, type, e))
		return false;
	for (auto&& record: listProperties)
	{
		auto name = ZGUtils::get(record, "name");
		mapProperties.insert({std::move(name), std::move(record)});
	}
	return true;
}

bool ZGDPDeviceManagerMng::getDynamicProperties(const std::string& deviceID, ZG6000::StringMap& mapProperty, ZG6000::ErrorInfo& e)
{
	std::string sql = "SELECT attributeName AS name, attributeValue AS value FROM dp_param_device_attribute WHERE deviceID = '" + deviceID + "'";
	ZG6000::ListStringMap listProperties;
	if (!ZGProxyCommon::execQuerySql(sql, listProperties))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = QStringLiteral("查询设备'%1'动态属性失败").arg(deviceID.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	for (auto&& properties: listProperties)
	{
		mapProperty.insert(std::make_pair(std::move(properties["name"]), std::move(properties["value"])));
	}
	return true;
}

void ZGDPDeviceManagerMng::processDeviceFieldChange(std::string tableName, std::string oper, std::string reason, std::string time, ZG6000::ListRecord listRecord)
{
	if (oper != "update")
		return;
	QDateTime dateTime;
	if (!ZGUtils::StringToDateTime(time.c_str(), dateTime))
	{
		ZGLOG_ERROR(QString("Invalid time format: %1").arg(time.c_str()));
	}
	for (auto& record: listRecord)
	{
		ZG6000::StringMap publishRecord;
		ZG6000::ListStringMap listSaveRecord, listSaveResumeRecord;
		std::string deviceID = record["id"].newValue;
		for (auto& [field, values]: record)
		{
			publishRecord[field] = values.newValue;
			auto pair = m_mapFieldName.find(field);
			if (pair != m_mapFieldName.end())
			{
				publishRecord[pair->second.second] = "";
				std::string fieldValue;
				if (!ZGProxyCommon::getDataByField(pair->second.first, values.newValue, "name", fieldValue))
					continue;
				publishRecord[pair->second.second] = fieldValue;
			}
			if (field != "id")
			{
				ZG6000::StringMap saveRecord;
				saveRecord["deviceID"] = deviceID;
				saveRecord["propertyName"] = field;
				saveRecord["oldValue"] = values.oldValue;
				saveRecord["newValue"] = values.newValue;
				saveRecord["changeTime"] = time;
				listSaveRecord.push_back(std::move(saveRecord));
			}
			if (field == "rtStateID")
			{
				ZG6000::StringMap resumeRecord;
				resumeRecord["deviceID"] = deviceID;
				resumeRecord["oldStateID"] = values.oldValue;
				resumeRecord["newStateID"] = values.newValue;
				resumeRecord["changeTime"] = time;
				listSaveResumeRecord.push_back(std::move(resumeRecord));
			}
		}
		const auto& json = ZGJson::convertToJson(publishRecord);
        m_pMqttClient->sendPublish("ZG_DP_DEVICE", json.c_str());
		std::string topicName = "dp_param_device/" + deviceID;
        m_pMqttClient->sendPublish(topicName.c_str(), json.c_str());
		QString hisTable = QString("dp_his_device_%1").arg(dateTime.date().year());
		ZGProxyCommon::insertDataByTable(hisTable.toStdString(), listSaveRecord, true, true);
		if (!listSaveResumeRecord.empty())
			ZGProxyCommon::insertDataByTable("dp_his_device_resume", listSaveResumeRecord, true, true);
	}
}

void ZGDPDeviceManagerMng::processDeviceAttributeChange(std::string tableName, std::string oper, std::string reason, std::string time, ZG6000::ListRecord listRecord)
{
	if (oper != "update")
		return;
	QDateTime dateTime;
	if (!ZGUtils::StringToDateTime(time.c_str(), dateTime))
	{
		ZGLOG_ERROR(QString("Invalid time format: %1").arg(time.c_str()));
	}
	ZG6000::MapStringMap mapPublishRecord;
	ZG6000::ListStringMap listSaveRecord;
	for (auto& record: listRecord)
	{
		auto it = record.find("attributeValue");
		if (it == record.end())
			continue;
		const auto& attributeValue = it->second.newValue;
		const auto& id = record["id"].newValue;
		ZG6000::StringMap mapFieldValue;
		if (!ZGProxyCommon::getDataByFields("dp_param_device_attribute", id,
			{"deviceID", "attributeName"}, mapFieldValue))
			continue;
		auto pair = mapPublishRecord.find(mapFieldValue["deviceID"]);
		if (pair == mapPublishRecord.end())
		{
			ZG6000::StringMap publishRecord;
			publishRecord[mapFieldValue["attributeName"]] = attributeValue;
			mapPublishRecord[mapFieldValue["deviceID"]] = std::move(publishRecord);
		}
		else
		{
			pair->second[mapFieldValue["attributeName"]] = attributeValue;
		}
		ZG6000::StringMap saveRecord;
		saveRecord["deviceID"] = mapFieldValue["deviceID"];
		saveRecord["propertyName"] = mapFieldValue["attributeName"];
		saveRecord["oldValue"] = it->second.oldValue;
		saveRecord["newValue"] = it->second.newValue;
		saveRecord["changeTime"] = time;
		listSaveRecord.push_back(std::move(saveRecord));
	}
	for (const auto& [deviceID, props]: mapPublishRecord)
	{
		std::string topicName = "dp_param_device/" + deviceID;
		const auto& json = ZGJson::convertToJson(props);
		m_pMqttClient->sendPublish(topicName.c_str(), json.c_str());
	}
	QString hisTable = QString("dp_his_device_%1").arg(dateTime.date().year());
	ZGProxyCommon::insertDataByTable(hisTable.toStdString(), listSaveRecord, true, true);
}

void ZGDPDeviceManagerMng::processModelAttributeChange(std::string tableName, std::string oper, std::string reason, std::string time, ZG6000::ListRecord listRecord)
{
	if (oper == "insert")
	{
		for (auto& record: listRecord)
		{
			if ((!record["name"].newValue.empty()) && (!record["modelID"].newValue.empty()))
			{
				auto key = record["modelID"].newValue + "/" + record["name"].newValue;
				ZG6000::StringMap val;
				val["no"] = record["id"].newValue;
				val["isDynamic"] = record["isDynamic"].newValue;
				val["defaultValue"] = record["defaultValue"].newValue;
				QWriteLocker locker(&m_lock2);
				m_mapModelProperty.insert({key, std::move(val)});
			}
		}
	}
	if (oper == "update")
	{
		for (auto& record: listRecord)
		{
			std::string oldName, oldModelID, newName, newModelID, oldKey, newKey;
			if (record.find("name") != record.end())
			{
				oldName = record["name"].oldValue;
				newName = record["name"].newValue;
				if (record.find("modelID") != record.end())
				{
					oldModelID = record["modelID"].oldValue;
					newModelID = record["modelID"].newValue;
					if ((!oldName.empty()) && (!oldModelID.empty()))
					{
						oldKey = oldModelID + "/" + oldName;
						removeOldKey(oldKey);
						QWriteLocker locker(&m_lock2);
					}
					if ((!newName.empty()) && (!newModelID.empty()))
					{
						newKey = newModelID + "/" + newName;
						const auto& id = record["id"].newValue;
						addNewKey(id, newKey);
					}
				}
				else
				{
					ZGProxyCommon::getDataByField(tableName, record["id"].newValue, "modelID", newModelID);
					if (!newModelID.empty())
					{
						if (!oldName.empty())
						{
							oldKey = newModelID + "/" + oldName;
							removeOldKey(oldKey);
						}
						if (!newName.empty())
						{
							newKey = newModelID + "/" + newName;
							const auto& id = record["id"].newValue;
							addNewKey(id, newKey);
						}
					}
				}
			}
			else if (record.find("modelID") != record.end())
			{
				oldModelID = record["modelID"].oldValue;
				newModelID = record["modelID"].newValue;
				ZGProxyCommon::getDataByField(tableName, record["id"].newValue, "name", newName);
				if (!newName.empty())
				{
					if (!oldModelID.empty())
					{
						oldKey = oldModelID + "/" + newName;
						removeOldKey(oldKey);
					}
					if (!newModelID.empty())
					{
						newKey = newModelID + "/" + newName;
						const auto& id = record["id"].newValue;
						addNewKey(id, newKey);
					}
				}
			}
			if (record.find("defaultValue") != record.end())
				updateKeyValue(record["id"].newValue, "defaultValue", record["defaultValue"].newValue);
			if (record.find("isDynamic") != record.end())
				updateKeyValue(record["id"].newValue, "isDynamic", record["isDynamic"].newValue);
		}
	}
	if (oper == "delete")
	{
		for (auto& record: listRecord)
		{
			const auto& id = record["id"].newValue;
			for (auto it = m_mapModelProperty.begin(); it != m_mapModelProperty.end();)
			{
				if (it->second["no"] == id)
					it = m_mapModelProperty.erase(it);
				else
					++it;
			}
		}
	}
}

void ZGDPDeviceManagerMng::processEquipmentChange(std::string tableName, std::string oper, std::string reason, std::string time, ZG6000::ListRecord listRecord)
{
	if (oper == "insert" || oper == "update")
	{
		for (auto& record: listRecord)
		{
			if (record.find("dpDeviceID") != record.end())
			{
				QWriteLocker locker(&m_lock1);
				m_mapDeviceEquipment[record["dpDeviceID"].newValue] = record["id"].newValue;
			}
		}
	}
	if (oper == "delete")
	{
		for (auto& record: listRecord)
		{
			const auto& equipmentID = record["id"].newValue;
			QWriteLocker locker(&m_lock1);
			for (auto it = m_mapDeviceEquipment.begin(); it != m_mapDeviceEquipment.end();)
			{
				if (it->second == equipmentID)
					it = m_mapDeviceEquipment.erase(it);
				else
					++it;
			}
		}
	}
}

bool ZGDPDeviceManagerMng::getPropertyFromFields(const std::string& deviceID, const std::string& propertyName, ZG6000::StringMap& property, ZG6000::ErrorInfo& e)
{
	std::string propertyValue;
	if (!getPropertyFromFields(deviceID, propertyName, propertyValue, e))
		return false;
	property["value"] = propertyValue;
	return true;
}

bool ZGDPDeviceManagerMng::getPropertyFromFields(const std::string& deviceID, const std::string& propertyName, std::string& propertyValue, ZG6000::ErrorInfo& e)
{
	QString sql = QString("SELECT %1 FROM dp_param_device WHERE id = '%2'")
		.arg(propertyName.c_str()).arg(deviceID.c_str());
	if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), propertyValue))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = u8"获取设备'" + deviceID + u8"'字段属性失败";
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::getPropertyFromRealFields(const std::string& deviceID, const std::string& propertyName, ZG6000::StringMap& property, ZG6000::ErrorInfo& e)
{
	std::string propertyValue;
	if (!getPropertyFromFields(deviceID, propertyName, propertyValue, e))
		return false;
	property["value"] = propertyValue;
	return true;
}

bool ZGDPDeviceManagerMng::getPropertyFromRealFields(const std::string& deviceID, const std::string& propertyName, std::string& propertyValue, ZG6000::ErrorInfo& e)
{
	if (!ZGProxyCommon::getDataByField("dp_param_device", deviceID, propertyName, propertyValue))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
		e.errDetail = u8"获取设备'" + deviceID + u8"'实时字段属性失败";
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::getPropertyFromDeviceAttribute(const std::string& deviceID, const std::string& propertyName, ZG6000::StringMap& property, ZG6000::ErrorInfo& e)
{
	std::string propertyValue;
	if (!getPropertyFromDeviceAttribute(deviceID, propertyName, propertyValue, e))
		return false;
	property["value"] = propertyValue;
	return true;
}

bool ZGDPDeviceManagerMng::getPropertyFromDeviceAttribute(const std::string& deviceID, const std::string& propertyName, std::string& propertyValue, ZG6000::ErrorInfo& e)
{
	QString sql = QString("SELECT id FROM dp_param_device_attribute WHERE deviceID = '%1' AND attributeName = '%2'")
		.arg(deviceID.c_str()).arg(propertyName.c_str());
	std::string propertyID;
	if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), propertyID))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
		e.errDetail = u8"获取设备'" + deviceID + u8"'属性'" + propertyName + u8"'ID失败";
		ZGLOG_ERROR(e);
		return false;
	}
	if (!ZGProxyCommon::getDataByField("dp_param_device_attribute", propertyID, "attributeValue",
		propertyValue))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
		e.errDetail = u8"获取设备'" + deviceID + u8"'扩展属性失败";
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::getModelPropertyKey(const std::string& deviceID, const std::string& propertyName, std::string& propertyKey, ZG6000::ErrorInfo& e)
{
	std::string modelID;
	if (!ZGProxyCommon::getDataByField("dp_param_device", deviceID, "modelID", modelID))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
		e.errDetail = u8"获取设备'" + deviceID + u8"'模型ID失败";
		ZGLOG_ERROR(e);
		return false;
	}
	propertyKey = modelID + "/" + propertyName;
	return true;
}

bool ZGDPDeviceManagerMng::getAssociateEquipment(const std::string& deviceID, std::string& equipmentID)
{
	bool find = true;
	{
		QReadLocker locker(&m_lock1);
		auto pairEquip = m_mapDeviceEquipment.find(deviceID);
		if (pairEquip == m_mapDeviceEquipment.end())
			find = false;
		else
			equipmentID = pairEquip->second;
	}
	if (!find)
	{
		ZGLOG_ERROR(QStringLiteral("设备'%1'没有关联的运行设备").arg(deviceID.c_str()));
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::updatePropertyToFields(const std::string& deviceID, const std::string& propertyName, const std::string& propertyValue, ZG6000::ErrorInfo& e)
{
	QString sql = QString("UPDATE dp_param_device SET %1 = '%2' WHERE id = '%3'")
	              .arg(propertyName.c_str()).arg(propertyValue.c_str()).arg(deviceID.c_str());
	if (!ZGProxyCommon::execSql(sql.toStdString()))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = u8"更新设备'" + deviceID + u8"'字段属性失败";
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::updatePropertyToRealFields(const std::string& deviceID, const std::string& propertyName, const std::string& propertyValue, ZG6000::ErrorInfo& e)
{
	if (!ZGProxyCommon::updateDataByField("dp_param_device", deviceID, propertyName, propertyValue))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
		e.errDetail = u8"更新设备'" + deviceID + u8"'实时字段属性失败";
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::updatePropertyToDeviceAttribute(const std::string& deviceID, const std::string& propertyName, const std::string& propertyValue, ZG6000::ErrorInfo& e)
{
	QString sql = QString("SELECT id FROM dp_param_device_attribute WHERE deviceID = '%1' AND attributeName = '%2'")
	              .arg(deviceID.c_str()).arg(propertyName.c_str());
	std::string propertyID;
	if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), propertyID))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
		e.errDetail = u8"获取设备'" + deviceID + u8"'属性'" + propertyName + u8"'ID失败";
		ZGLOG_ERROR(e);
		return false;
	}
	if (!ZGProxyCommon::updateDataByField("dp_param_device_attribute", propertyID, "attributeValue",
		propertyValue))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_RT);
		e.errDetail = u8"更新设备'" + deviceID + u8"'扩展属性失败";
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::updatePropertyToEquipmentServer(const std::string& equipmentID, const std::string& propertyName, const std::string& propertyValue, ZG6000::ErrorInfo& e)
{
    auto equipmentPrx = ZGProxyMng::instance()->getProxyMPEquipmentManager();
    if (equipmentPrx == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
        e.errDetail = u8"获取一次设备管理服务代理失败";
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        if (!equipmentPrx->updatePropertyValue(equipmentID, propertyName, false, propertyValue, e))
            return false;
    }
    catch (const Ice::Exception& ie)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
        e.errDetail = ie.what();
        ZGLOG_ERROR(e);
        return false;
    }
	return true;
}

void ZGDPDeviceManagerMng::removeOldKey(const std::string& oldKey)
{
	QWriteLocker locker(&m_lock2);
	auto it = m_mapModelProperty.find(oldKey);
	if (it != m_mapModelProperty.end())
		m_mapModelProperty.erase(it);
}

bool ZGDPDeviceManagerMng::addNewKey(const std::string& id, const std::string& newKey)
{
	ZG6000::StringMap fieldValue;
	if (!ZGProxyCommon::getDataByFields("dp_param_device_model_attribute", id, {"isDynamic", "defaultValue"},
		fieldValue))
	{
		ZGLOG_ERROR(QStringLiteral("获取模型属性失败，id = %1").arg(id.c_str()));
		return false;
	}
	fieldValue["no"] = id;
	QWriteLocker locker(&m_lock2);
	m_mapModelProperty.insert({newKey, std::move(fieldValue)});
	return true;
}

bool ZGDPDeviceManagerMng::updateKeyValue(const std::string& id, const std::string& field, const std::string& value)
{
	ZG6000::StringMap record;
	if (!ZGProxyCommon::getDataByFields("dp_param_device_model_attribute", id, {"modelID", "name"},
		record))
	{
		ZGLOG_ERROR(QStringLiteral("获取模型属性失败，id = %1").arg(id.c_str()));
		return false;
	}
	if ((!record["modelID"].empty()) && (!record["name"].empty()))
	{
		std::string key = record["modelID"] + "/" + record["name"];
		QWriteLocker locker(&m_lock2);
		auto pair = m_mapModelProperty.find(key);
		if (pair != m_mapModelProperty.end())
			pair->second[field] = value;
	}
	return true;
}

bool ZGDPDeviceManagerMng::getPropertyFromEquipmentServer(const std::string& equipmentID, const std::string& propertyName, ZG6000::StringMap& property, ZG6000::ErrorInfo& e)
{
    auto equipmentPrx = ZGProxyMng::instance()->getProxyMPEquipmentManager();
    if (equipmentPrx == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
        e.errDetail = u8"获取一次设备管理服务代理失败";
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        if (!equipmentPrx->getProperty(equipmentID, propertyName, false, property, e))
            return false;
    }
    catch (const Ice::Exception& ie)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
        e.errDetail = ie.what();
        ZGLOG_ERROR(e);
        return false;
    }
	return true;
}

bool ZGDPDeviceManagerMng::getDeviceResume(std::string deviceID, ZG6000::ListStringMap& listDeviceResume, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	std::string sql = "SELECT oldStateID, newStateID, changeTime, documents FROM dp_his_device_resume WHERE deviceID = '"
		+ deviceID + "'";
	if (!ZGProxyCommon::execQuerySql(sql, listDeviceResume, true))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = QStringLiteral("查询设备'%1'履历失败").arg(deviceID.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	for (auto& deviceResume: listDeviceResume)
	{
		deviceResume["oldStateName"] = m_mapDevState[deviceResume["oldStateID"]];
		deviceResume["newStateName"] = m_mapDevState[deviceResume["newStateID"]];
	}
	return true;
}

bool ZGDPDeviceManagerMng::addDeviceResume(std::string deviceID, ZG6000::StringMap deviceResume, ZG6000::ErrorInfo& e, const Ice::Current& current)
{
	ZG6000::ListStringMap listDeviceResume;
	listDeviceResume.push_back(std::move(deviceResume));
	if (!ZGProxyCommon::insertDataByTable("dp_his_device_resume", listDeviceResume, true))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_DB);
		e.errDetail = QStringLiteral("添加设备'%1'履历失败").arg(deviceID.c_str()).toStdString();
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGDPDeviceManagerMng::equipmentCall(std::function<bool(std::shared_ptr<ZG6000::ZGMPEquipmentManagerPrx>)> func, ZG6000::ErrorInfo& e)
{
	auto equipmentPrx = ZGProxyMng::instance()->getProxyMPEquipmentManager();
	if (equipmentPrx == nullptr)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
		e.errDetail = u8"获取一次设备管理服务代理失败";
		ZGLOG_ERROR(e);
		return false;
	}
	try
	{
		if (!func(equipmentPrx))
			return false;
	}
	catch (const Ice::Exception& ie)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGDPDeviceManager::ZG_ERR_INTERNAL);
		e.errDetail = ie.what();
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

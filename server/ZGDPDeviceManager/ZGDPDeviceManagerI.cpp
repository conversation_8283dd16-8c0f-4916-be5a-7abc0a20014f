#include "ZGDPDeviceManagerI.h"
#include "ZGDPDeviceManagerMng.h"

ZG6000::ZGDPDeviceManagerI::ZGDPDeviceManagerI()
{
	ZGDPDeviceManagerMng::instance()->init();
}

bool ZG6000::ZGDPDeviceManagerI::checkState(const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->checkState();
}

void ZG6000::ZGDPDeviceManagerI::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current& current)
{
	ZGDPDeviceManagerMng::instance()->dispatchData(std::move(tableName), std::move(oper), std::move(reason), std::move(time), std::move(listRecord), current);
}

bool ZG6000::ZGDPDeviceManagerI::getDevices(std::string condition, int offset, int limit, std::string orderField, std::string orderType, ListStringMap& devices,
                                            ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->getDevices(std::move(condition), offset, limit, std::move(orderField), std::move(orderType), devices, e, current);
}

bool ZG6000::ZGDPDeviceManagerI::getDynamicProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->getDynamicProperties(std::move(deviceID), properties, e, current);
}

bool ZG6000::ZGDPDeviceManagerI::getFieldsProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->getFieldsProperties(std::move(deviceID), properties, e, current);
}

bool ZG6000::ZGDPDeviceManagerI::getModelProperties(std::string modelID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->getModelProperties(std::move(modelID), properties, e, current);
}

bool ZG6000::ZGDPDeviceManagerI::getRuntimeProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->getRuntimeProperties(std::move(deviceID), properties, e, current);
}

bool ZG6000::ZGDPDeviceManagerI::getWholeProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->getWholeProperties(std::move(deviceID), properties, e, current);
}

bool ZG6000::ZGDPDeviceManagerI::getProperty(std::string deviceID, std::string propertyName, bool isExtend, StringMap& property, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->getProperty(std::move(deviceID), std::move(propertyName), isExtend, property, e, current);
}

bool ZG6000::ZGDPDeviceManagerI::getPropertyValue(std::string deviceID, std::string propertyName, bool isExtend, std::string& propertyValue, ErrorInfo& e, const Ice::Current& current)
{
    return ZGDPDeviceManagerMng::instance()->getPropertyValue(std::move(deviceID), std::move(propertyName), isExtend, propertyValue, e, current);
}

bool ZG6000::ZGDPDeviceManagerI::updatePropertyValue(std::string deviceID, std::string propertyName, bool isExtend, std::string propertyValue, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->updatePropertyValue(std::move(deviceID), std::move(propertyName), isExtend, std::move(propertyValue), e, current);
}

bool ZG6000::ZGDPDeviceManagerI::addDevice(std::string deviceID, StringMap properties, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->addDevice(std::move(deviceID), std::move(properties), e, current);
}

bool ZG6000::ZGDPDeviceManagerI::updateDevice(std::string deviceID, StringMap properties, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->updateDevice(std::move(deviceID), std::move(properties), e, current);
}

bool ZG6000::ZGDPDeviceManagerI::removeDevice(std::string deviceID, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->removeDevice(std::move(deviceID), e, current);
}

bool ZG6000::ZGDPDeviceManagerI::getDeviceProperty(std::string deviceID, std::string property, std::string& value, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->getDeviceProperty(std::move(deviceID), std::move(property), value, e, current);
}

bool ZG6000::ZGDPDeviceManagerI::getDeviceResume(std::string deviceID, ListStringMap& listDeviceResume, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->getDeviceResume(std::move(deviceID), listDeviceResume, e, current);
}

bool ZG6000::ZGDPDeviceManagerI::addDeviceResume(std::string deviceID, StringMap deviceResume, ErrorInfo& e, const Ice::Current& current)
{
	return ZGDPDeviceManagerMng::instance()->addDeviceResume(std::move(deviceID), std::move(deviceResume), e, current);
}

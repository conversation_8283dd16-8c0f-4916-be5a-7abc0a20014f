#ifndef ZG6000_ZGMPDATASETPROPERTYI_H
#define ZG6000_ZGMPDATASETPROPERTYI_H

#include <ZGMPDatasetProperty.h>

namespace ZG6000 {

class ZGMPDatasetPropertyI : public virtual ZGMPDatasetProperty
{
public:
    ZGMPDatasetPropertyI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current) override;

    // ZGMPDatasetProperty interface
public:
    bool getPropertiesAll(std::string datasetID, MapStringMap &properties, ErrorInfo &e, const Ice::Current &current) override;
    bool getProperties(std::string datasetID, StringList listName, MapStringMap &properties, ErrorInfo &e, const Ice::Current &current) override;
    bool getProperty(std::string datasetID, std::string name, StringMap &property, ErrorInfo &e, const Ice::Current &current) override;
    bool getPropertyValues(std::string datasetID, StringList listName, StringMap &values, ErrorInfo &e, const Ice::Current &current) override;
    bool getPropertyValue(std::string datasetID, std::string name, std::string &value, ErrorInfo &e, const Ice::Current &current) override;
    bool updateProperty(std::string datasetID, std::string name, StringMap property, bool saveToDB, ErrorInfo &e, const Ice::Current &current) override;
    bool updateProperties(std::string datasetID, MapStringMap properties, bool saveToDB, ErrorInfo &e, const Ice::Current &current) override;
    bool updatePropertyValues(std::string datasetID, StringMap values, bool saveToDB, ErrorInfo &e, const Ice::Current &current) override;
    bool updatePropertyValue(std::string datasetID, std::string name, std::string value, bool saveToDB, ErrorInfo &e, const Ice::Current &current) override;
    bool getDataIDByProperty(std::string datasetID, std::string name, std::string &tableName, std::string &dataID, ErrorInfo &e, const Ice::Current &current) override;
    bool getPropertyByDataID(std::string dataID, std::string &datasetID, std::string &name, ErrorInfo &e, const Ice::Current &current) override;
    bool isPropertyExists(std::string datasetID, std::string name, bool &exists, ErrorInfo &e, const Ice::Current &current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGMPDATASETPROPERTYI_H

#ifndef ZG6000_ZGMPDATASETPROPERTYMNG_H
#define ZG6000_ZGMPDATASETPROPERTYMNG_H

#include <QObject>
#include <QThread>
#include <QTimer>
#include <ZGServerCommon.h>
#include <Ice/Ice.h>


class ZGMqttClient;
class ZGRedisClient;

namespace ZG6000
{
    class ZGMPDatasetPropertyMng : public QThread
    {
        Q_OBJECT

    public:
        static ZGMPDatasetPropertyMng* instance();
        void init();
        bool checkState(const Ice::Current& current);
        void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord);

    public:

        /**
         * @brief   获取数据集所有属性
         *          
         *
         * @param           datasetID   数据集ID
         * @param [in,out]  properties  属性集合
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool getPropertiesAll(std::string datasetID, MapStringMap& properties, ErrorInfo& e);

        /**
         * @brief   获取数据集指定名称列表的属性
         *
         * @param           datasetID   数据集ID
         * @param           listName    属性名列表
         * @param [in,out]  properties  属性集合
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool getProperties(std::string datasetID, StringList listName, MapStringMap& properties, ErrorInfo& e);

        /**
         * @brief   获取数据集指定名称的属性
         *
         * @param           datasetID   数据集ID
         * @param           name        属性名
         * @param [in,out]  property    属性
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool getProperty(std::string datasetID, std::string name, StringMap& property, ErrorInfo& e);

        /**
         * @brief   获取数据集指定名称列表的属性值
         *
         * @param           datasetID   数据集ID
         * @param           listName    属性名列表
         * @param [in,out]  values      属性值集合
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool getPropertyValues(std::string datasetID, StringList listName, StringMap& values, ErrorInfo& e);

        /**
         * @brief   获取数据集指定名称的属性值
         *
         * @param           datasetID   数据集ID
         * @param           name        属性名
         * @param [in,out]  value       属性值
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool getPropertyValue(std::string datasetID, std::string name, std::string& value, ErrorInfo& e);

        /**
         * @brief   更新数据集指定的属性集合
         *
         * @param           datasetID   数据集ID
         * @param           properties  属性集合
         * @param           saveToDB    是否更新到数据库
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool updateProperties(std::string datasetID, MapStringMap properties, bool saveToDB, ErrorInfo& e);

        /**
         * @brief   更新数据集指定的属性
         *
         * @param           datasetID   数据集ID
         * @param           name        属性名
         * @param           property    更新属性
         * @param           saveToDB    是否更新到数据库
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool updateProperty(std::string datasetID, std::string name, StringMap property, bool saveToDB, ErrorInfo& e);

        /**
         * @brief   更新数据集指定的属性值集合
         *
         * @param           datasetID   数据集ID
         * @param           values      属性值集合
         * @param           saveToDB    是否更新到数据库
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool updatePropertyValues(std::string datasetID, StringMap values, bool saveToDB, ErrorInfo& e);

        /**
         * @brief   更新数据集指定的属性值
         *
         * @param           datasetID   数据集ID
         * @param           name        属性名
         * @param           value       属性值
         * @param           saveToDB    是否更新到数据库
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool updatePropertyValue(std::string datasetID, std::string name, std::string value, bool saveToDB, ErrorInfo& e);

        /**
         * @brief   根据数据集属性名获取数据ID
         *
         * @param           datasetID   数据集ID
         * @param           name        属性名
         * @param [in,out]  tableName   表名
         * @param [in,out]  dataID      数据ID
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool getDataIDByProperty(std::string datasetID, std::string name, std::string& tableName, std::string& dataID, ErrorInfo& e);

        /**
         * @brief   根据数据ID获取数据集属性名
         *
         * @param           dataID      数据ID
         * @param [in,out]  datasetID   数据集ID
         * @param [in,out]  name        属性名
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool getPropertyByDataID(std::string dataID, std::string& datasetID, std::string& name, ErrorInfo& e);

        /**
         * @brief   查询指定的数据集属性是否存在
         *
         * @param           datasetID   数据集ID
         * @param           name        属性名
         * @param [in,out]  exists      True存在,False不存在
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false
         */
        bool isPropertyExists(std::string datasetID, std::string name, bool& exists, ErrorInfo& e);

    private slots:
        void onTimer();

    private:
        explicit ZGMPDatasetPropertyMng(QObject* parent = nullptr);
        void initEvents();
        void initServerInstConfig();
        bool initServerInstInfo();
        bool initDataCategoryParam();
        bool initModelParam();
        bool initDatasetParam();
        bool initOtherParam();
        bool initDatasetProperties();
        bool initMqttClient();
        bool initRedisClient();

    private:
        bool initModelTableParam(const std::string& tableName, MapStringMap& mapProperty);
        bool initDatasetTableProperties(const std::string& tableName);
        void cacheDatasetProperties(const std::string& tableName, ListStringMap&& listResult);
        bool mgetDatasetProperties(const std::string& tableName, const StringList& listID, const StringList& dataFields, const StringList& modelFields,
                                   ListStringMap& listProperties, ErrorInfo& e);
        bool getDatasetTableData(const std::string& datasetID, const StringList& listName, std::map<std::string, StringList>& mapTableData, ErrorInfo& e);
        bool updatePropertiesToDb(std::string datasetID, MapStringMap properties, ErrorInfo& e);
        bool updatePropertyValuesToDb(std::string datasetID, StringMap values, ErrorInfo& e);
        bool updatePropertiesToRt(std::string datasetID, MapStringMap properties, ErrorInfo& e);
        bool updatePropertyValuesToRt(std::string datasetID, StringMap values, ErrorInfo& e);

    private:
        bool m_initialized{false};
        int m_initInterval{10};
        QString m_serverName{""};
        QString m_instName{""};
        QTimer m_timer;
        StringList m_listDataTable{
            "mp_param_dataset_bt", "mp_param_dataset_yc", "mp_param_dataset_yx",
            "mp_param_dataset_text", "mp_param_dataset_param", "mp_param_dataset_ym",
            "mp_param_dataset_yk", "mp_param_dataset_ys", "mp_param_dataset_yt", "mp_param_dataset_yv"
        }; // 数据集数据表
        std::map<std::string, StringList> m_mapDataFields{
            {"mp_param_dataset_bt", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_yc", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtStateValue", "rtUpdateTime"}},
            {"mp_param_dataset_yx", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtStateValue", "rtUpdateTime"}},
            {"mp_param_dataset_text", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtStateValue", "rtUpdateTime"}},
            {"mp_param_dataset_param", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_ym", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_yk", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_ys", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_yt", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
            {"mp_param_dataset_yv", {"id", "name", "nameL2", "rtspAddr", "videoChannel", "rtConnectNum", "rtUpdateTime"}}
        };
        std::map<std::string, StringList> m_mapModelFields{
            {"mp_param_model_bt", {"dataCategoryID", "dataTypeID", "dataUnitID"}},
            {"mp_param_model_yc", {"dataCategoryID", "stateDataCategoryID", "dataTypeID", "dataUnitID"}},
            {"mp_param_model_yx", {"dataCategoryID", "stateDataCategoryID", "dataTypeID", "dataUnitID", "relationProperty"}},
            {"mp_param_model_text", {"dataCategoryID", "stateDataCategoryID", "dataTypeID", "dataUnitID"}},
            {"mp_param_model_param", {"dataCategoryID", "dataTypeID", "dataUnitID"}},
            {"mp_param_model_ym", {"dataCategoryID", "dataTypeID", "dataUnitID"}},
            {"mp_param_model_yk", {"dataCategoryID", "isCheckExecRule", "isCheckConfirmRule", "isCheckErrorRule"}},
            {"mp_param_model_ys", {"dataCategoryID", "isCheckExecRule", "isCheckConfirmRule", "isCheckErrorRule"}},
            {"mp_param_model_yt", {"dataCategoryID", "isCheckExecRule", "isCheckConfirmRule", "isCheckErrorRule"}},
            {"mp_param_model_yv", {"dataCategoryID"}}
        };
        StringMap m_mapDataCategoryProp; // 数据类别属性
        StringMap m_mapDataUnit; // 数据单位
        MapStringMap m_mapModelPointParam; // 模型点参数
        MapStringMap m_mapDatasetParam; // 数据集参数
        StringMap m_mapAppNode; // 应用节点
        StringMap m_mapSubsystem; // 子系统
        StringMap m_mapMajor; // 专业
        std::map<std::string, int> m_mapDatasetTickCount;
        using StringPair = std::pair<std::string, std::string>;
        std::map<std::string, StringList> m_mapDatasetProperties; // 数据集属性名列表
        std::map<StringPair, StringPair> m_mapPropertyData; // 数据集属性到数据ID
        std::map<std::string, StringPair> m_mapDataProperty; // 数据ID到数据集属性
        MapStringMap m_mapDataParam; // 数据参数
        ZGMqttClient* m_pMqttClient{nullptr};
        ZGRedisClient* m_pRedisClient{nullptr};
    };

    inline static ZGMPDatasetPropertyMng* g_pInstance = nullptr;
} // namespace ZG6000

#endif // ZG6000_ZGMPDATASETPROPERTYMNG_H

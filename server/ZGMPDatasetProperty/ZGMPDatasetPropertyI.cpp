#include "ZGMPDatasetPropertyI.h"
#include "ZGMPDatasetPropertyMng.h"

namespace ZG6000 {

ZGMPDatasetPropertyI::ZGMPDatasetPropertyI()
{
    ZGMPDatasetPropertyMng::instance()->init();
}

bool ZGMPDatasetPropertyI::checkState(const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->checkState(current);
}

void ZGMPDatasetPropertyI::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current)
{
    ZGMPDatasetPropertyMng::instance()->dispatchData(std::move(tableName), std::move(oper), std::move(reason), std::move(time), std::move(listRecord));
}

bool ZGMPDatasetPropertyI::getPropertiesAll(std::string datasetID, MapStringMap &properties, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->getPropertiesAll(std::move(datasetID), properties, e);
}

bool ZGMPDatasetPropertyI::getProperties(std::string datasetID, StringList listName, MapStringMap &properties, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->getProperties(std::move(datasetID), std::move(listName), properties, e);
}

bool ZGMPDatasetPropertyI::getProperty(std::string datasetID, std::string name, StringMap &property, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->getProperty(std::move(datasetID), std::move(name), property, e);
}

bool ZGMPDatasetPropertyI::getPropertyValues(std::string datasetID, StringList listName, StringMap &values, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->getPropertyValues(std::move(datasetID), std::move(listName), values, e);
}

bool ZGMPDatasetPropertyI::getPropertyValue(std::string datasetID, std::string name, std::string &value, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->getPropertyValue(std::move(datasetID), std::move(name), value, e);
}

bool ZGMPDatasetPropertyI::updateProperty(std::string datasetID, std::string name, StringMap property, bool saveToDB, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->updateProperty(std::move(datasetID), std::move(name), std::move(property), saveToDB, e);
}

bool ZGMPDatasetPropertyI::updateProperties(std::string datasetID, MapStringMap properties, bool saveToDB, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->updateProperties(std::move(datasetID), std::move(properties), saveToDB, e);
}

bool ZGMPDatasetPropertyI::updatePropertyValues(std::string datasetID, StringMap values, bool saveToDB, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->updatePropertyValues(std::move(datasetID), std::move(values), saveToDB, e);
}

bool ZGMPDatasetPropertyI::updatePropertyValue(std::string datasetID, std::string name, std::string value, bool saveToDB, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->updatePropertyValue(std::move(datasetID), std::move(name), std::move(value), saveToDB, e);
}

bool ZGMPDatasetPropertyI::getDataIDByProperty(std::string datasetID, std::string name, std::string &tableName, std::string &dataID, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->getDataIDByProperty(std::move(datasetID), std::move(name), tableName, dataID, e);
}

bool ZGMPDatasetPropertyI::getPropertyByDataID(std::string dataID, std::string &datasetID, std::string &name, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->getPropertyByDataID(std::move(dataID), datasetID, name, e);
}

bool ZGMPDatasetPropertyI::isPropertyExists(std::string datasetID, std::string name, bool &exists, ErrorInfo &e, const Ice::Current &current)
{
    return ZGMPDatasetPropertyMng::instance()->isPropertyExists(std::move(datasetID), std::move(name), exists, e);
}

} // namespace ZG6000

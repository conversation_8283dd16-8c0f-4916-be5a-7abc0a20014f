#include <QtConcurrent>
#include <QFuture>
#include <QRandomGenerator>

#include "ZGMPDatasetPropertyMng.h"

#include <ZGJson.h>
#include <zgerror/ZGMPDevicePropertyError.h>

#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "zgerror/ZGMPDatasetPropertyError.h"

namespace ZG6000
{
    ZGMPDatasetPropertyMng* ZGMPDatasetPropertyMng::instance()
    {
        if (g_pInstance == nullptr)
            g_pInstance = new ZGMPDatasetPropertyMng;
        return g_pInstance;
    }

    void ZGMPDatasetPropertyMng::init()
    {
        initEvents();
        initServerInstConfig();
        while (!initServerInstInfo())
        {
            ZGLOG_ERROR("initServerInstInfo error.");
            QThread::sleep(m_initInterval);
        }
        QThread::sleep(QRandomGenerator::global()->bounded(1, 5));
        while (!initOtherParam())
        {
            ZGLOG_ERROR("initDictParam error.");
            QThread::sleep(m_initInterval);
        }
        while (!initDataCategoryParam())
        {
            ZGLOG_ERROR("initDataCategoryParam error.");
            QThread::sleep(m_initInterval);
        }
        while (!initModelParam())
        {
            ZGLOG_ERROR("initModelParam error.");
            QThread::sleep(m_initInterval);
        }
        while (!initDatasetParam())
        {
            ZGLOG_ERROR("initDatasetParam error.");
            QThread::sleep(m_initInterval);
        }
        while (!initDatasetProperties())
        {
            ZGLOG_ERROR("initDatasetProperties error.");
            QThread::sleep(m_initInterval);
        }
        while (!initMqttClient())
        {
            ZGLOG_ERROR("initMqttClient error.");
            QThread::sleep(m_initInterval);
        }
        while (!initRedisClient())
        {
            ZGLOG_ERROR("initRedisClient error.");
            QThread::sleep(m_initInterval);
        }
        m_initialized = true;
        ZGLOG_INFO("ZGMPDatasetProperty init finished.");
        m_timer.start(1000);
    }

    bool ZGMPDatasetPropertyMng::checkState(const Ice::Current& current)
    {
        return m_initialized;
    }

    void ZGMPDatasetPropertyMng::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord)
    {
        if (!m_initialized)
            return;
        if (std::find(m_listDataTable.begin(), m_listDataTable.end(), tableName) == m_listDataTable.end())
            return;
        MapMapStringMap mapRedisProperties;
        MapMapStringMap mapMqttProperties;
        for (auto& record : listRecord)
        {
            const auto& dataID = record["id"].newValue;
            auto pair = m_mapDataProperty.find(dataID);
            if (pair == m_mapDataProperty.end())
                continue;
            const auto& [datasetID, name] = pair->second;
            auto pairDataset = m_mapDatasetParam.find(datasetID);
            if (pairDataset == m_mapDatasetParam.end())
                continue;
            StringMap propertyParam;
            auto pairData = m_mapDataParam.find(dataID);
            if (pairData == m_mapDataParam.end())
                continue;
            const auto& dataModelID = pairData->second["dataModelID"];
            auto pairModel = m_mapModelPointParam.find(dataModelID);
            if (pairModel == m_mapModelPointParam.end())
                continue;
            for (const auto& [field, value] : record)
            {
                if (std::find(m_mapDataFields[tableName].begin(), m_mapDataFields[tableName].end(), field) == m_mapDataFields[tableName].end())
                    continue;
                propertyParam[field] = value.newValue;
                if (field == "rtNewValue" || field == "rtSimulateValue")
                {
                    const auto& dataCategoryID = pairModel->second["dataCategoryID"];
                    auto pairProp = m_mapDataCategoryProp.find(dataCategoryID + "/" + value.newValue);
                    if (pairProp == m_mapDataCategoryProp.end())
                        continue;
                    propertyParam[field + "Desc"] = pairProp->second;
                }
            }
            mapRedisProperties[datasetID][name] = propertyParam;
            const auto& datasetParam = pairDataset->second;
            const auto& isPublishMQ = ZGUtils::get(datasetParam, "isPublishMQ", "");
            if (isPublishMQ != "1")
                continue;
            if (pairModel->second["isPublishMQ"] == "1")
            {
                mapMqttProperties[datasetID][name] = std::move(propertyParam);
            }
        }
        for (const auto& [datasetID, properties] : mapRedisProperties)
        {
            QString topicName = QString("mp_param_dataset/%1").arg(datasetID.c_str());
            const auto& json = ZGJson::convertToJson(properties);
            long long num;
            QString errMsg;
            if (!m_pRedisClient->publish(topicName, json.c_str(), num, errMsg))
                ZGLOG_ERROR(errMsg);
        }
        for (const auto& [datasetID, properties] : mapMqttProperties)
        {
            QString topicName = QString("mp_param_dataset/%1").arg(datasetID.c_str());
            const auto& json = ZGJson::convertToJson(properties);
            ZGLOG_DEBUG(QString("topicName: %1, content: %2").arg(topicName).arg(json.c_str()));
            m_pMqttClient->sendPublish(topicName, json.c_str());
        }
    }

    bool ZGMPDatasetPropertyMng::getPropertiesAll(std::string datasetID, MapStringMap& properties, ErrorInfo& e)
    {
        std::map<std::string, StringList> mapTableDataID;
        auto pair = m_mapDatasetProperties.find(datasetID);
        if (pair == m_mapDatasetProperties.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("数据集'%1'无可用的属性").arg(datasetID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& listPropertyName = pair->second;
        return getProperties(std::move(datasetID), listPropertyName, properties, e);
    }

    bool ZGMPDatasetPropertyMng::getProperties(std::string datasetID, StringList listName, MapStringMap& properties, ErrorInfo& e)
    {
        if (listName.empty())
            return true;
        std::map<std::string, StringList> mapTableData;
        if (!getDatasetTableData(datasetID, listName, mapTableData, e))
            return false;
        for (const auto& [table, listDataID] : mapTableData)
        {
            std::string modelTable = table;
            ZGUtils::replaceString(modelTable, "dataset", "model");
            ListStringMap listProperties;
            if (!mgetDatasetProperties(table, listDataID, m_mapDataFields[table],
                m_mapModelFields[modelTable], listProperties, e))
                return false;
            for (size_t i = 0; i < listDataID.size(); ++i)
            {
                auto pair = m_mapDataProperty.find(listDataID[i]);
                if (pair == m_mapDataProperty.end())
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("找不到表'%1'数据'%2'关联的设备属性").arg(table.c_str()).arg(listDataID[i].c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                const auto& [deviceID, name] = pair->second;
                listProperties[i]["tableName"] = table;
                properties[name] = std::move(listProperties[i]);
            }
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::getProperty(std::string datasetID, std::string name, StringMap& property, ErrorInfo& e)
    {
        MapStringMap properties;
        if (!getProperties(std::move(datasetID), { name }, properties, e))
            return false;
        property = std::move(properties[std::move(name)]);
        return true;
    }

    bool ZGMPDatasetPropertyMng::getPropertyValues(std::string datasetID, StringList listName, StringMap& values, ErrorInfo& e)
    {
        std::map<std::string, StringList> mapTableData;
        if (!getDatasetTableData(datasetID, listName, mapTableData, e))
            return false;
        for (const auto& [table, listDataID] : mapTableData)
        {
            ListStringMap listProperties;
            if (!mgetDatasetProperties(table, listDataID, { "rtNewValue" },
                {}, listProperties, e))
                return false;
            for (size_t i = 0; i < listDataID.size(); ++i)
            {
                auto pair = m_mapDataProperty.find(listDataID[i]);
                if (pair == m_mapDataProperty.end())
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("找不到表'%1'数据'%2'关联的设备属性").arg(table.c_str()).arg(listDataID[i].c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                const auto& [deviceID, name] = pair->second;
                values[name] = listProperties[i]["rtNewValue"];
            }
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::getPropertyValue(std::string datasetID, std::string name, std::string& value, ErrorInfo& e)
    {
        auto pair = m_mapPropertyData.find({ datasetID, name });
        if (pair == m_mapPropertyData.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'").arg(datasetID.c_str()).arg(name.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!ZGProxyCommon::getDataByField(pair->second.first, pair->second.second, "rtNewValue", value))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取设备'%1'数据属性'%2'失败").arg(datasetID.c_str()).arg(name.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::updatePropertiesToDb(std::string datasetID, MapStringMap properties, ErrorInfo& e)
    {
        if (properties.empty())
            return true;
        StringList listSql;
        for (auto& [name, property] : properties)
        {
            auto pair = m_mapPropertyData.find({ datasetID, name });
            if (pair == m_mapPropertyData.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据").arg(datasetID.c_str()).arg(name.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& [tableName, dataID] = pair->second;
            property["id"] = dataID;
            listSql.push_back(ZGUtils::generateUpdateSql(tableName, std::move(property)));
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("更新设备属性失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::updatePropertyValuesToDb(std::string datasetID, StringMap values, ErrorInfo& e)
    {
        if (values.empty())
            return true;
        StringList listSql;
        std::string currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        for (auto& [name, value] : values)
        {
            auto pair = m_mapPropertyData.find({ datasetID, name });
            if (pair == m_mapPropertyData.end())
                continue;
            const auto& [tableName, dataID] = pair->second;
            StringMap dataParam{ {"id", dataID} };
            dataParam["rtNewValue"] = value;
            dataParam["rtUpdateTime"] = currentTime;
            listSql.push_back(ZGUtils::generateUpdateSql(tableName, std::move(dataParam)));
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("更新数据集属性失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::updatePropertyValuesToRt(std::string datasetID, StringMap values, ErrorInfo& e)
    {
        std::map<std::string, StringMap> mapDataProperties;
        for (auto& [name, value] : values)
        {
            auto pair = m_mapPropertyData.find({ datasetID, name });
            if (pair == m_mapPropertyData.end())
                continue;
            const auto& [tableName, dataID] = pair->second;
            mapDataProperties[tableName][dataID] = std::move(value);
        }
        std::string currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        for (auto& [tableName, dataParam] : mapDataProperties)
        {
            StringList listDataID;
            ListStringMap listData;
            for (auto& [dataID, value] : dataParam)
            {
                listDataID.push_back(dataID);
                StringMap data;
                data["rtNewValue"] = std::move(value);
                data["rtUpdateTime"] = currentTime;
                listData.push_back(std::move(data));
            }
            if (!ZGProxyCommon::mupdateDataByFields(tableName, std::move(listDataID), listData))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新设备数据属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::updatePropertiesToRt(std::string datasetID, MapStringMap properties, ErrorInfo& e)
    {
        if (properties.empty())
            return true;
        std::map<StringList, ListStringMap> mapDataProperties;
        for (auto& [name, property] : properties)
        {
            auto pair = m_mapPropertyData.find({ datasetID, name });
            if (pair == m_mapPropertyData.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到数据集'%1'属性'%2'关联的数据").arg(datasetID.c_str()).arg(name.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& [tableName, dataID] = pair->second;
            StringMap dataParam{ {"id", dataID} };
            StringList listDataField{ tableName };
            for (auto& [field, value] : property)
            {
                listDataField.push_back(field);
                dataParam[field] = std::move(value);
            }
            mapDataProperties[std::move(listDataField)].push_back(std::move(dataParam));
        }
        for (auto& [listDataField, listDataParam] : mapDataProperties)
        {
            StringList listDataID;
            for (auto& dataParam : listDataParam)
            {
                listDataID.push_back(dataParam["id"]);
            }
            const auto& tableName = listDataField[0];
            if (!ZGProxyCommon::mupdateDataByFields(tableName, listDataID, listDataParam))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新数据集数据属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::updateProperty(std::string datasetID, std::string name, StringMap property, bool saveToDB, ErrorInfo& e)
    {
        MapStringMap properties{ {std::move(name), std::move(property)} };
        return updateProperties(std::move(datasetID), std::move(properties), saveToDB, e);
    }

    bool ZGMPDatasetPropertyMng::updateProperties(std::string datasetID, MapStringMap properties, bool saveToDB, ErrorInfo& e)
    {
        if (properties.empty())
            return true;
        if (saveToDB)
            return updatePropertiesToDb(std::move(datasetID), std::move(properties), e);
        else
            return updatePropertiesToRt(std::move(datasetID), std::move(properties), e);
    }

    bool ZGMPDatasetPropertyMng::updatePropertyValues(std::string datasetID, StringMap values, bool saveToDB, ErrorInfo& e)
    {
        if (values.empty())
            return true;
        if (saveToDB)
            return updatePropertyValuesToDb(datasetID, values, e);
        else
            return updatePropertyValuesToRt(datasetID, values, e);
    }

    bool ZGMPDatasetPropertyMng::updatePropertyValue(std::string datasetID, std::string name, std::string value, bool saveToDB, ErrorInfo& e)
    {
        std::string currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        if (saveToDB)
        {
            auto pair = m_mapPropertyData.find({ datasetID, name });
            if (pair == m_mapPropertyData.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据").arg(datasetID.c_str()).arg(name.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& [tableName, dataID] = pair->second;
            StringMap dataParam{ {"id", dataID} };
            dataParam["rtNewValue"] = value;
            dataParam["rtUpdateTime"] = currentTime;
            std::string sql = ZGUtils::generateUpdateSql(tableName, std::move(dataParam));
            if (!ZGProxyCommon::execSql(sql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新设备属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        auto pair = m_mapPropertyData.find({ datasetID, name });
        if (pair == m_mapPropertyData.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到设备'%1'属性'%2'关联的数据ID").arg(datasetID.c_str()).arg(name.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& [tableName, dataID] = pair->second;
        if (!ZGProxyCommon::updateDataByID(tableName, dataID, { {"rtNewValue", value}, {"rtUpdateTime", currentTime} }))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("更新表'%1'数据'%2'值失败").arg(tableName.c_str()).arg(dataID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::getDataIDByProperty(std::string datasetID, std::string name, std::string& tableName, std::string& dataID, ErrorInfo& e)
    {
        auto pair = m_mapPropertyData.find({ datasetID, name });
        if (pair == m_mapPropertyData.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到数据集'%1'属性'%2'关联的数据ID").arg(datasetID.c_str()).arg(name.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        tableName = pair->second.first;
        dataID = pair->second.second;
        return true;
    }

    bool ZGMPDatasetPropertyMng::getPropertyByDataID(std::string dataID, std::string& datasetID, std::string& name, ErrorInfo& e)
    {
        auto pair = m_mapDataProperty.find(dataID);
        if (pair == m_mapDataProperty.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到数据'%2'关联的设备属性").arg(dataID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        datasetID = pair->second.first;
        name = pair->second.second;
        return true;
    }

    bool ZGMPDatasetPropertyMng::isPropertyExists(std::string datasetID, std::string name, bool& exists, ErrorInfo& e)
    {
        return (m_mapPropertyData.find({ datasetID, name }) != m_mapPropertyData.end());
    }

    void ZGMPDatasetPropertyMng::onTimer()
    {
        StringList listDatasetID;
        for (auto& [datasetID, tickCount] : m_mapDatasetTickCount)
        {
            auto pair = m_mapDatasetParam.find(datasetID);
            if (pair == m_mapDatasetParam.end())
                continue;
            auto& [_, param] = *pair;
            if (param["isPublishMQ"] != "1")
                continue;
            int interval = std::atoi(param["publishInterval"].c_str());
            if (tickCount < interval)
            {
                ++tickCount;
                continue;
            }
            listDatasetID.push_back(datasetID);
            tickCount = 1;
        }
        if (listDatasetID.empty())
            return;
        for (const auto & datasetID : listDatasetID)
        {
            MapStringMap properties;
            ErrorInfo e;
            if (!getPropertiesAll(datasetID, properties, e))
                continue;
            const auto& json = ZGJson::convertToJson(properties);
            QString topicName = QString("mp_param_dataset/%1").arg(datasetID.c_str());
            ZGLOG_DEBUG(QString("datasetID: '%1', content: '%2'").arg(datasetID.c_str()).arg(json.c_str()));
            m_pMqttClient->sendPublish(topicName, json.c_str());
        }
    }

    ZGMPDatasetPropertyMng::ZGMPDatasetPropertyMng(QObject* parent)
        : QThread{parent}
    {
    }

    void ZGMPDatasetPropertyMng::initEvents()
    {
        connect(&m_timer, &QTimer::timeout, this, &ZGMPDatasetPropertyMng::onTimer);
    }

    void ZGMPDatasetPropertyMng::initServerInstConfig()
    {
        const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
        QString errMsg;
        int value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
            ZGLOG_WARN(errMsg);
        else
            m_initInterval = value;
    }

    bool ZGMPDatasetPropertyMng::initServerInstInfo()
    {
        m_serverName = ZGRuntime::instance()->getServerID();
        if (m_serverName.isEmpty())
        {
            ZGLOG_ERROR("Empty server id.");
            return false;
        }
        m_instName = ZGRuntime::instance()->getInstanceID();
        if (m_instName.isEmpty())
        {
            ZGLOG_ERROR("Empty server instance id.");
            return false;
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::initDataCategoryParam()
    {
        std::string sql = "SELECT * FROM mp_param_data_category_property";
        ZG6000::ListStringMap listRecord;
        if (!ZGProxyCommon::execQuerySql(sql, listRecord))
        {
            ZGLOG_ERROR(QStringLiteral("获取数据类别属性失败"));
            return false;
        }
        try
        {
            for (const auto& record : listRecord)
            {
                const auto& dataCategoryId = ZGUtils::get(record, "dataCategoryID");
                const auto& propValue = ZGUtils::get(record, "propValue");
                const auto& propName = ZGUtils::get(record, "propName");
                m_mapDataCategoryProp.insert(std::make_pair(dataCategoryId + "/" + propValue, propName));
            }
            return true;
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
            return false;
        }
    }

    bool ZGMPDatasetPropertyMng::initModelParam()
    {
        for (const auto& tableName : m_listDataTable)
        {
            MapStringMap mapProperty;
            std::string modelTable = tableName;
            ZGUtils::replaceString(modelTable, "dataset", "model");
            if (!initModelTableParam(modelTable, mapProperty))
                return false;
            m_mapModelPointParam.merge(mapProperty);
        }
        ZGLOG_DEBUG(QString("mapModelPointParam size: %1").arg(m_mapModelPointParam.size()));
        return true;
    }

    bool ZGMPDatasetPropertyMng::initDatasetParam()
    {
        QString sql = QString("SELECT a.id, a.isPublishMQ, a.publishInterval FROM mp_param_dataset a LEFT JOIN mp_param_model b ON a.modelID = b.id "
            "WHERE b.isMapProperty = 1 AND a.isEnable = 1 ORDER BY a.id");
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapDatasetParam))
        {
            ZGLOG_ERROR(QStringLiteral("获取数据集参数失败"));
            return false;
        }
        for (const auto& [datasetID, datasetParam] : m_mapDatasetParam)
        {
            int interval = std::atoi(ZGUtils::get(datasetParam, "publishInterval", "0").c_str());
            if (interval > 0)
                m_mapDatasetTickCount[datasetID] = QRandomGenerator::global()->bounded(1, interval);
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::initOtherParam()
    {
        std::string sql = "SELECT id, name FROM sp_dict_data_unit";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDataUnit))
        {
            ZGLOG_ERROR(QStringLiteral("获取数据单位参数失败"));
            return false;
        }
        sql = "SELECT id, name from sp_param_appnode";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapAppNode))
        {
            ZGLOG_ERROR(QStringLiteral("获取应用节点参数失败"));
            return false;
        }
        sql = "SELECT id, name from sp_param_subsystem";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapSubsystem))
        {
            ZGLOG_ERROR(QStringLiteral("获取子系统参数失败"));
            return false;
        }
        sql = "SELECT id, name from sp_param_major";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapMajor))
        {
            ZGLOG_ERROR(QStringLiteral("获取专业参数失败"));
            return false;
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::initDatasetProperties()
    {
        m_mapDatasetProperties.clear();
        m_mapDataParam.clear();
        m_mapDataProperty.clear();
        m_mapPropertyData.clear();
        for (const auto& tableName : m_listDataTable)
        {
            ZGLOG_DEBUG(QString("tableName: %1").arg(tableName.c_str()));
            if (!initDatasetTableProperties(tableName))
                return false;
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::initMqttClient()
    {
        if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
        {
            ZGLOG_ERROR("initMqttClient error.");
            return false;
        }
        m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
        if (m_pMqttClient == nullptr)
        {
            ZGLOG_ERROR("getMqttClientMessage return null.");
            return false;
        }
        m_pMqttClient->connectToHost();
        return true;
    }

    bool ZGMPDatasetPropertyMng::initRedisClient()
    {
        QList<ZGRuntime::REDIS_CLIENT_TYPE> listType;
        listType << ZGRuntime::REDIS_RT_TOPIC;
        if (!ZGRuntime::instance()->initRedisClient(listType))
        {
            ZGLOG_ERROR("initRedisClient error.");
            return false;
        }
        m_pRedisClient = ZGRuntime::instance()->getRedisClientRTTopic();
        if (m_pRedisClient == nullptr)
        {
            ZGLOG_ERROR("getRedisClientRTTopic error.");
            return false;
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::initModelTableParam(const std::string& tableName, MapStringMap& mapProperty)
    {
        mapProperty.clear();
        QString sql = QString("SELECT * FROM %1 a LEFT JOIN mp_param_model b ON a.modelID = b.id "
            "WHERE a.isEnable = 1 AND a.propertyName <> '' AND b.isMapProperty = 1 ORDER BY a.id").arg(tableName.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), mapProperty))
        {
            ZGLOG_ERROR(QStringLiteral("获取表'%1'模型数据失败").arg(tableName.c_str()));
            return false;
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::initDatasetTableProperties(const std::string& tableName)
    {
        QString sql = QString("SELECT a.id FROM mp_param_dataset a LEFT JOIN mp_param_model b ON a.modelID = b.id "
            "WHERE b.isMapProperty = 1 AND a.isEnable = 1 ORDER BY a.id");
        ZG6000::StringList listDatasetID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDatasetID))
        {
            ZGLOG_ERROR(QStringLiteral("获取模型关联数据集失败"));
            return false;
        }
        if (listDatasetID.empty())
            return true;
        const auto& ids = ZGUtils::join(listDatasetID, ",", "'", "'");
        sql = QString("SELECT id, datasetID, dataModelID FROM %1 WHERE datasetID IN (%2)").arg(tableName.c_str()).arg(ids.c_str());
        ZG6000::ListStringMap listResult;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listResult))
        {
            ZGLOG_ERROR(QStringLiteral("获取表'%1'数据集属性失败").arg(tableName.c_str()));
            return false;
        }
        cacheDatasetProperties(tableName, std::move(listResult));
        ZGLOG_DEBUG(QString("DatasetProperties size: %1").arg(m_mapDatasetProperties.size()));
        ZGLOG_DEBUG(QString("PropertyData size: %1").arg(m_mapPropertyData.size()));
        ZGLOG_DEBUG(QString("DataProperty size: %1").arg(m_mapDataProperty.size()));
        if (m_mapPropertyData.size() != m_mapDataProperty.size())
        {
            ZGLOG_ERROR(QStringLiteral("存在关联了多个ID的数据集属性"));
            return false;
        }
        ZGLOG_DEBUG(QString("DataParam size: %1").arg(m_mapDataParam.size()));
        return true;
    }

    void ZGMPDatasetPropertyMng::cacheDatasetProperties(const std::string& tableName, ListStringMap&& listResult)
    {
        for (auto& result : listResult)
        {
            auto pairModel = m_mapModelPointParam.find(result["dataModelID"]);
            if (pairModel == m_mapModelPointParam.end())
                continue;
            auto pairProp = pairModel->second.find("propertyName");
            if (pairProp == pairModel->second.end())
                continue;
            const auto& propertyName = pairProp->second;
            m_mapDatasetProperties[result["datasetID"]].push_back(propertyName);
            auto pair = m_mapPropertyData.find({result["datasetID"], propertyName});
            if (pair != m_mapPropertyData.end())
            {
                ZGLOG_WARN(QStringLiteral("数据集'%1'属性'%2'已经关联到表'%3'数据'%4', 无法关联到表'%5'数据'%6'")
                    .arg(result["datasetID"].c_str()).arg(propertyName.c_str())
                    .arg(pair->second.first.c_str()).arg(pair->second.second.c_str())
                    .arg(tableName.c_str()).arg(result["id"].c_str()));
            }
            else
            {
                m_mapPropertyData.insert({{result["datasetID"], propertyName}, {tableName, result["id"]}});
            }
            auto pairData = m_mapDataProperty.find(result["id"]);
            if (pairData != m_mapDataProperty.end())
            {
                ZGLOG_WARN(QStringLiteral("数据ID'%1'已经存在"));
            }
            else
            {
                m_mapDataProperty.insert({result["id"], {result["datasetID"], propertyName}});
            }
            const auto& dataCategoryID = ZGUtils::get(pairModel->second, "dataCategoryID", "");
            const auto& isPublishMQ = ZGUtils::get(pairModel->second, "isPublishMQ", "");
            m_mapDataParam.insert({
                result["id"], {
                    {"dataModelID", result["dataModelID"]},
                    {"dataCategoryID", dataCategoryID}, {"isPublishMQ", isPublishMQ}
                }
            });
        }
    }

    bool ZGMPDatasetPropertyMng::mgetDatasetProperties(const std::string& tableName, const StringList& listID, const StringList& dataFields, const StringList& modelFields, ListStringMap& listProperties, ErrorInfo& e)
    {
        if (!ZGProxyCommon::mgetDataByFields(tableName, listID, dataFields, listProperties))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取表'%1'数据属性失败").arg(tableName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (size_t i = 0; i < listID.size(); ++i)
        {
            std::string dataModelID;
            auto pairData = m_mapDataParam.find(listID[i]);
            if (pairData == m_mapDataParam.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到可用的数据ID'%1'").arg(listID[i].c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& [_dataID, dataParam] = *pairData;
            auto pairDataParam = dataParam.find("dataModelID");
            if (pairDataParam == dataParam.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("数据ID'%1'找不到数据模型ID").arg(listID[i].c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            dataModelID = pairDataParam->second;
            auto pairModel = m_mapModelPointParam.find(dataModelID);
            if (pairModel == m_mapModelPointParam.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到可用的模型数据'%1'").arg(dataModelID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& [id, modelParam] = *pairModel;
            for (const auto& modelField : modelFields)
            {
                auto pairParam = modelParam.find(modelField);
                if (pairParam == modelParam.end())
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("模型ID'%1'找不到可用的模型字段'%2'").arg(dataModelID.c_str()).arg(modelField.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                const auto& [field, value] = *pairParam;
                listProperties[i][modelField] = value;
            }
        }
        return true;
    }

    bool ZGMPDatasetPropertyMng::getDatasetTableData(const std::string& datasetID, const StringList& listName, std::map<std::string, StringList>& mapTableData, ErrorInfo& e)
    {
        for (const auto& name : listName)
        {
            auto pair = m_mapPropertyData.find({ datasetID, name });
            if (pair == m_mapPropertyData.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGMPDatasetProperty::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到数据集'%1'属性'%2'关联的数据").arg(datasetID.c_str()).arg(name.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& [tableName, dataID] = pair->second;
            mapTableData[tableName].push_back(dataID);
        }
        return true;
    }
} // namespace ZG6000

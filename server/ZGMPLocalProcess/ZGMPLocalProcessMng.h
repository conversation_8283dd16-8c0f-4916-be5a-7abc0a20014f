#ifndef ZGMPLOCALPROCESSMNG_H
#define ZGMPLOCALPROCESSMNG_H

#include <QObject>
#include <QTimer>

class ZGRedisClient;
class ZGMPLocalProcessMng : public QObject
{
    Q_OBJECT
public:
    static ZGMPLocalProcessMng* instance();
    void init();
    bool checkState();

private slots:
    void onCheckStatus();
    void onReceivedMessage(const QString& channel, const QString& message);
private:
    explicit ZGMPLocalProcessMng(QObject *parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initRedisRtTopic();

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    std::string m_logicalName{""};
    int m_initInterval{10};
    int m_checkInterval{10};
    QTimer m_checkTimer;
    ZGRedisClient* m_pRedisRtTopic{nullptr};
};

#endif // ZGMPLOCALPROCESSMNG_H

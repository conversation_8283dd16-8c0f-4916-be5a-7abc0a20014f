#include "ZGMPLocalProcessMng.h"

#include <QRandomGenerator>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QThread>
#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"

static ZGMPLocalProcessMng* g_pInstance = nullptr;

ZGMPLocalProcessMng* ZGMPLocalProcessMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGMPLocalProcessMng;
    return g_pInstance;
}

void ZGMPLocalProcessMng::init()
{
	initEvents();
	initServerInstConfig();
    ZGLOG_INFO("initServerInstInfo");
	while (!initServerInstInfo())
	{
		ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
	}
    QThread::sleep(QRandomGenerator::global()->bounded(1, 5));
    ZGLOG_INFO("initRedisRtTopic");
	while (!initRedisRtTopic())
	{
		ZGLOG_ERROR("initRedisRtTopic error.");
        QThread::msleep(m_initInterval * 1000);
	}
	m_initialized = true;
	ZGLOG_INFO("ZGMPLocalProcess init finished.");
	m_checkTimer.start(m_checkInterval * 1000);
}

bool ZGMPLocalProcessMng::checkState()
{
	return m_initialized;
}

void ZGMPLocalProcessMng::onCheckStatus()
{

}

void ZGMPLocalProcessMng::onReceivedMessage(const QString& channel, const QString& message)
{
    if (!ZGRuntime::instance()->isMaster())
		return;
	ZGLOG_DEBUG(message);
	qsizetype pos = channel.indexOf("/");
	const auto& type = channel.mid(pos + 1);
	QJsonDocument doc = QJsonDocument::fromJson(message.toUtf8().data());
	auto array = doc.array();
    ZG6000::StringList listSql;
    ZG6000::StringList listDataID, listDataValue, listTableName;
	for (auto objRef : array)
	{
		auto obj = objRef.toObject();
		const auto& id = obj["id"].toString().toStdString();
		QString datasetTable, modelTable;
		if (type == "yk")
		{
			datasetTable = "mp_param_dataset_yk";
			modelTable = "mp_param_model_yk";
		}
		if (type == "ys")
		{
			datasetTable = "mp_param_dataset_ys";
			modelTable = "mp_param_model_ys";
		}
		if (type == "yt")
		{
			datasetTable = "mp_param_dataset_yt";
			modelTable = "mp_param_model_yt";
		}
		QString sql = QString("SELECT a.deviceID, b.propertyName FROM %1 a LEFT JOIN %2 b "
			"ON a.dataModelID = b.id WHERE a.id = '%3' ORDER BY a.id").arg(datasetTable).arg(modelTable).arg(id.c_str());
		ZG6000::StringMap mapResult;
		if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), mapResult))
			return;
		if (mapResult["deviceID"].empty() || mapResult["propertyName"].empty())
		{
			ZGLOG_ERROR(QStringLiteral("命令ID'%1'关联的设备ID或属性名为空").arg(id.c_str()));
			return;
		}
		const auto& deviceID = mapResult["deviceID"];
		const auto& propertyName = mapResult["propertyName"];
		size_t cmdPos = propertyName.find("CMD_");
		if (cmdPos == std::string::npos)
		{
			ZGLOG_ERROR(QStringLiteral("属性'%1'不是一个合法的参数属性").arg(propertyName.c_str()));
			return;
		}
		const auto& paramProp = propertyName.substr(cmdPos + 4);
		ZGLOG_DEBUG(QString("paramProp: '%1'").arg(paramProp.c_str()));
		const auto& value = obj["rtValue"].toString().toStdString();
        std::string tableName, dataID;
        ZG6000::ErrorInfo e;
        if (ZGProxyCommon::getDataIDByProperty(deviceID, paramProp, tableName, dataID, e))
        {
            listSql.push_back(ZGUtils::generateUpdateSql(tableName, {{"id", dataID}, {"rtNewValue", value}}));
            listTableName.push_back(tableName);
            listDataID.push_back(dataID);
            listDataValue.push_back(value);
        }
	}
    if (listSql.empty())
        return;
    if (!ZGProxyCommon::execBatchSql(listSql))
        ZGLOG_ERROR(QStringLiteral("更新系统参数失败"));
    // 需要更新一下实时库，防止数据库数据与实时库数据不一致，导致无法同步
    for (size_t i = 0; i < listTableName.size(); ++i)
    {
        ZGProxyCommon::updateDataByField(listTableName[i], listDataID[i], "rtNewValue", listDataValue[i]);
    }
    ZGProxyCommon::synchronize();
}

void ZGMPLocalProcessMng::initServerInstConfig()
{
	const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
	QString errMsg;
	int value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_initInterval = value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_checkInterval = value;
}

bool ZGMPLocalProcessMng::initServerInstInfo()
{
	m_serverName = ZGRuntime::instance()->getServerID();
	if (m_serverName.isEmpty())
	{
		ZGLOG_ERROR("Empty server id.");
		return false;
	}
	m_instName = ZGRuntime::instance()->getInstanceID();
	if (m_instName.isEmpty())
	{
		ZGLOG_ERROR("Empty server instance id.");
		return false;
	}
    if (!ZGProxyCommon::getDataByField("sp_param_node_service_instance", m_instName.toStdString(), "logicalName", m_logicalName))
    {
        ZGLOG_ERROR("get instance logical name error.");
        return false;
    }
	return true;
}

bool ZGMPLocalProcessMng::initRedisRtTopic()
{
	QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
	listClientType << ZGRuntime::REDIS_RT_TOPIC;
	if (!ZGRuntime::instance()->initRedisClient(listClientType))
	{
		ZGLOG_ERROR("initRedisClient error.");
		return false;
	}
	m_pRedisRtTopic = ZGRuntime::instance()->getRedisClientRTTopic();
	if (m_pRedisRtTopic == nullptr)
	{
		ZGLOG_ERROR("getRedisClientRTTopic error.");
		return false;
	}
    ZG6000::StringList listTopic;
    listTopic.push_back(m_logicalName + "/yk");
    listTopic.push_back(m_logicalName + "/ys");
    listTopic.push_back(m_logicalName + "/yt");
	m_pRedisRtTopic->subscribe(listTopic);
	connect(m_pRedisRtTopic, &ZGRedisClient::receivedMessage, this, &ZGMPLocalProcessMng::onReceivedMessage);
	m_pRedisRtTopic->consume();
	return true;
}

ZGMPLocalProcessMng::ZGMPLocalProcessMng(QObject *parent)
    : QObject{parent}
{
    
}

void ZGMPLocalProcessMng::initEvents()
{
	connect(&m_checkTimer, &QTimer::timeout, this, &ZGMPLocalProcessMng::onCheckStatus);
}

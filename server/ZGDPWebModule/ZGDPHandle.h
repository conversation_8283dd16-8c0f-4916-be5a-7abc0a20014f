#ifndef ZGDPHANDLE_H
#define ZGDPHANDLE_H

#include <QObject>
#include <QJsonDocument>
#include "QtHttpServer/QHttpServer"
#include "ZGProxyCommon.h"

struct GroupProperties
{
    std::string typeID;
    ZG6000::MapStringMap properties;
};

class ZGDPHandle : public QObject
{
    Q_OBJECT
public:
    explicit ZGDPHandle(QObject *parent = nullptr);

public:
    QHttpServerResponse on_common_prop_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_static_prop_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_dynamic_prop_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_whole_prop_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_device_count_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_device_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_device_add(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_device_update(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_device_delete(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_device_property_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_device_property_value_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_devices_property_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_devices_property_values_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_device_property_value_set(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_event_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_event_confirm(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);

private:
    QHttpServerResponse devicePropCall(const QJsonValue& param, const QHttpServerRequest& req,
        const std::function<QHttpServerResponse(std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy)>& func);
    std::map<std::string, GroupProperties> convertProperties(ZG6000::ListStringMap&& properties);
    QJsonObject convertToObject(const std::map<std::string, GroupProperties>& mapGroupProperties);
    void initAlarmLevel();

private:
    std::unordered_map<std::string, std::string> m_mapAlarmLevelColor;
};

#endif // ZGDPHANDLE_H

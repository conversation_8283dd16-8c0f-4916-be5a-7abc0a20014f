#include "ZGDPHandle.h"

#include <ZGRuntime.h>

#include "ZGWebModule.h"
#include "ZGJson.h"
#include "ZGUtils.h"

ZGDPHandle::ZGDPHandle(QObject* parent)
	: QObject{parent}
{
	initAlarmLevel();
}

QHttpServerResponse ZGDPHandle::on_common_prop_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return devicePropCall(param, req, [&](std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy) -> QHttpServerResponse
	{
		const auto& deviceID = param.toString();
		ZG6000::ListStringMap properties;
		ZG6000::ErrorInfo e;
		if (!deviceProxy->getFieldsProperties(deviceID.toStdString(), properties, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		std::map<std::string, GroupProperties> mapGroupProperties;
		return ZGWebModule::replyObject(convertToObject(convertProperties(std::move(properties))));
	});
}

QHttpServerResponse ZGDPHandle::on_static_prop_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return devicePropCall(param, req, [&](std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy) -> QHttpServerResponse
	{
		const auto& deviceID = param.toString();
		ZG6000::ListStringMap properties;
		ZG6000::ErrorInfo e;
		if (!deviceProxy->getStaticProperties(deviceID.toStdString(), properties, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		std::map<std::string, GroupProperties> mapGroupProperties;
		return ZGWebModule::replyObject(convertToObject(convertProperties(std::move(properties))));
	});
}

QHttpServerResponse ZGDPHandle::on_dynamic_prop_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return devicePropCall(param, req, [&](std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy) -> QHttpServerResponse
	{
		const auto& deviceID = param.toString();
		ZG6000::ListStringMap properties;
		ZG6000::ErrorInfo e;
		if (!deviceProxy->getDynamicProperties(deviceID.toStdString(), properties, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		std::map<std::string, GroupProperties> mapGroupProperties;
		return ZGWebModule::replyObject(convertToObject(convertProperties(std::move(properties))));
	});
}

QHttpServerResponse ZGDPHandle::on_whole_prop_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return devicePropCall(param, req, [&](std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy) -> QHttpServerResponse
	{
		const auto& deviceID = param.toString();
		ZG6000::ListStringMap properties;
		ZG6000::ErrorInfo e;
		if (!deviceProxy->getWholeProperties(deviceID.toStdString(), properties, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		std::map<std::string, GroupProperties> mapGroupProperties;
		return ZGWebModule::replyObject(convertToObject(convertProperties(std::move(properties))));
	});
}

QHttpServerResponse ZGDPHandle::on_device_count_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	const auto& object = param.toObject();
	QString errMsg;
	if (!ZGWebModule::checkRequiredFields(object, {"condition"}, errMsg))
		return ZGWebModule::errorObject(errMsg);
	const auto& condition = object["condition"].toString();
	QString sql = QString("SELECT COUNT(*) FROM dp_param_device a WHERE %1").arg(condition);
	std::string deviceCount;
	if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), deviceCount))
		return ZGWebModule::errorObject(u8"获取设备数量失败");
	return ZGWebModule::replyObject(deviceCount.c_str());
}

QHttpServerResponse ZGDPHandle::on_device_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return ZGWebModule::replyObject("");
}

QHttpServerResponse ZGDPHandle::on_device_add(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	// 占位空实现
	return ZGWebModule::replyObject("");
}

QHttpServerResponse ZGDPHandle::on_device_update(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return ZGWebModule::replyObject("");
}

QHttpServerResponse ZGDPHandle::on_device_delete(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return ZGWebModule::replyObject("");
}

QHttpServerResponse ZGDPHandle::on_device_property_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return devicePropCall(param, req, [&](std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy) -> QHttpServerResponse
	{
		const auto& object = param.toObject();
		QString errMsg;
		if (!ZGWebModule::checkRequiredFields(object, {"id", "propertyName"}, errMsg))
			return ZGWebModule::errorObject(errMsg);
		const auto& deviceID = object["id"].toString();
		const auto& propertyName = object["propertyName"].toString();
		ZG6000::ErrorInfo e;
		ZG6000::StringMap property;
		if (!deviceProxy->getProperty(deviceID.toStdString(), propertyName.toStdString(), property, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		return ZGWebModule::replyObject(ZGWebModule::stringMapToObject(property));
	});
}

QHttpServerResponse ZGDPHandle::on_device_property_value_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return devicePropCall(param, req, [&](std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy) -> QHttpServerResponse
	{
		const auto& object = param.toObject();
		QString errMsg;
		if (!ZGWebModule::checkRequiredFields(object, {"id", "propertyName"}, errMsg))
			return ZGWebModule::errorObject(errMsg);
		const auto& deviceID = object["id"].toString();
		const auto& propertyName = object["propertyName"].toString();
		ZG6000::ErrorInfo e;
		std::string propertyValue;
		if (!deviceProxy->getPropertyValue(deviceID.toStdString(), propertyName.toStdString(), propertyValue, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		return ZGWebModule::replyObject(propertyValue.c_str());
	});
}

QHttpServerResponse ZGDPHandle::on_devices_property_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return devicePropCall(param, req, [&](std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy) -> QHttpServerResponse
	{
		const auto& object = param.toObject();
		QString errMsg;
		if (!ZGWebModule::checkRequiredFields(object, {"devices", "propertyName"}, errMsg))
			return ZGWebModule::errorObject(errMsg);
		auto deviceArray = object["devices"].toArray();
		const auto& propertyName = object["propertyName"].toString();
		ZG6000::StringList listDeviceID;
		for (auto deviceRef: deviceArray)
		{
			listDeviceID.push_back(deviceRef.toString().toStdString());
		}
		ZG6000::MapStringMap properties;
		ZG6000::ErrorInfo e;
		if (!deviceProxy->mgetProperty(listDeviceID, propertyName.toStdString(), properties, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		auto json = ZGJson::convertToJson(properties);
		QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
		return ZGWebModule::replyObject(doc.object());
	});
}

QHttpServerResponse ZGDPHandle::on_devices_property_values_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return devicePropCall(param, req, [&](std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy) -> QHttpServerResponse
	{
		const auto& object = param.toObject();
		QString errMsg;
		if (!ZGWebModule::checkRequiredFields(object, {"devices", "properties"}, errMsg))
			return ZGWebModule::errorObject(errMsg);
		auto deviceArray = object["devices"].toArray();
		auto propertyArray = object["properties"].toArray();
		ZG6000::StringList listDeviceID;
		ZG6000::StringList listName;
		for (auto deviceRef: deviceArray)
		{
			listDeviceID.push_back(deviceRef.toString().toStdString());
		}
		for (auto propertyRef: propertyArray)
		{
			listName.push_back(propertyRef.toString().toStdString());
		}
		ZG6000::MapStringMap propertyValues;
		ZG6000::ErrorInfo e;
		if (!deviceProxy->mgetPropertyValues(listDeviceID, listName, propertyValues, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		auto json = ZGJson::convertToJson(propertyValues);
		QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
		return ZGWebModule::replyObject(doc.object());
	});
}

QHttpServerResponse ZGDPHandle::on_device_property_value_set(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	return devicePropCall(param, req, [&](std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy) -> QHttpServerResponse
		{
			const auto& object = param.toObject();
			QString errMsg;
			if (!ZGWebModule::checkRequiredFields(object, {"id", "propertyName", "propertyValue"}, errMsg))
				return ZGWebModule::errorObject(errMsg);
			const auto& deviceID = object["id"].toString();
			const auto& propertyName = object["propertyName"].toString();
			const auto& propertyValue = object["propertyValue"].toString();
			ZG6000::ErrorInfo e;
			ZG6000::StringMap property;
			if (!deviceProxy->updatePropertyValue(deviceID.toStdString(), propertyName.toStdString(), propertyValue.toStdString(), e))
				return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
			return ZGWebModule::replyObject(property["value"].c_str());
		});
}

QHttpServerResponse ZGDPHandle::on_event_get(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	const auto& object = param.toObject();
	QString errMsg;
	if (!ZGWebModule::checkRequiredFields(object, { "startID", "count" }, errMsg))
		return ZGWebModule::errorObject(errMsg);
	const auto& startID = object["startID"].toInteger();
	const auto& count = object["count"].toInt();
	std::string rtAppNodeID;
	QDateTime currTime = QDateTime::currentDateTime();
	QString tableName = QString("sp_his_event_%1").arg(currTime.date().year());
    QString sql = QString("SELECT * FROM %1 WHERE id >= '%2' AND subsystemID = 'ZG_SS_DM' AND eventTypeID = 'ZG_ET_SYSTEM' ORDER BY id DESC limit 0, %3")
		.arg(tableName).arg(startID).arg(count);
	ZG6000::ListStringMap listRecord;
	auto dbProxy = ZGProxyMng::instance()->getProxySPDBDataHis();
	try
	{
		ZG6000::ErrorInfo e;
		if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listRecord, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		QJsonArray array;
		for (size_t i = listRecord.size() - 1; i != 0; --i)
		{
			auto& record = listRecord[i];
			record["color"] = m_mapAlarmLevelColor[record["alarmLevelID"]];
			QJsonObject eventObj;
			for (const auto& pair : record)
			{
				eventObj[pair.first.c_str()] = pair.second.c_str();
			}
			array.append(eventObj);
		}
		return ZGWebModule::replyObject(array);
	}
	catch (const Ice::Exception& e)
	{
		return ZGWebModule::errorObject(e.what());
	}
}

QHttpServerResponse ZGDPHandle::on_event_confirm(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
	const auto& object = param.toObject();
	QString errMsg;
	if (!ZGWebModule::checkRequiredFields(object, { "userID", "userName", "events" }, errMsg))
		return ZGWebModule::errorObject(errMsg);
	const auto& userID = object["userID"].toString().toStdString();
	const auto& userName = object["userName"].toString().toStdString();
	const auto& array = object["events"].toArray();
	ZG6000::ListStringMap listEvent;
	for (auto ref : array)
	{
		const auto& eventObj = ref.toObject();
		ZG6000::StringMap event;
		event.insert(std::make_pair("id", eventObj["id"].toString().toStdString()));
		event.insert(std::make_pair("eventTime", eventObj["eventTime"].toString().toStdString()));
		listEvent.push_back(std::move(event));
	}
	auto eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
	if (eventProcessPrx == nullptr)
		return ZGWebModule::errorObject(u8"获取事件处理服务代理失败");
	try
	{
		ZG6000::ErrorInfo e;
		if (!eventProcessPrx->confirmDPEvent(listEvent, userID, userName, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		return ZGWebModule::replyObject("");
	}
	catch (const Ice::Exception& e)
	{
		return ZGWebModule::errorObject(e.what());
	}
}

QHttpServerResponse ZGDPHandle::devicePropCall(const QJsonValue& param, const QHttpServerRequest& req,
	const std::function<QHttpServerResponse(std::shared_ptr<ZG6000::ZGDPDevicePropertyPrx> deviceProxy)>& func)
{
	auto deviceProxy = ZGProxyMng::instance()->getProxyDPDeviceProperty();
	if (deviceProxy == nullptr)
		return ZGWebModule::errorObject(u8"获取设备管理服务失败");
	try
	{
		return func(deviceProxy);
	}
	catch (const Ice::Exception& e)
	{
		return ZGWebModule::errorObject(e.what());
	}
	catch (const std::exception& e)
	{
		return ZGWebModule::errorObject(e.what());
	}
	catch (...)
	{
		return ZGWebModule::errorObject("unknown error.");
	}
}

std::map<std::string, GroupProperties> ZGDPHandle::convertProperties(ZG6000::ListStringMap&& properties)
{
	std::map<std::string, GroupProperties> mapGroupProperties;
	for (auto&& property : properties)
	{
		auto&& typeName = property["typeName"];
		ZG6000::StringMap mapProperty;
		mapProperty.insert(std::make_pair("desc", std::move(property["desc"])));
		mapProperty.insert(std::make_pair("value", std::move(property["value"])));
		if (property.find("unitID") != property.end())
			mapProperty.insert(std::make_pair("unitID", std::move(property["unitID"])));
		if (property.find("unitName") != property.end())
			mapProperty.insert(std::make_pair("unitName", std::move(property["unitName"])));
		auto it = mapGroupProperties.find(typeName);
		if (it == mapGroupProperties.end())
		{
			GroupProperties groupProperties;
			groupProperties.typeID = std::move(property["typeID"]);
			groupProperties.properties.insert({std::move(property["name"]), std::move(mapProperty)});
			mapGroupProperties[std::move(typeName)] = groupProperties;
		}
		else
		{
			it->second.properties.insert({std::move(property["name"]), std::move(mapProperty)});
		}
	}
	return mapGroupProperties;
}

QJsonObject ZGDPHandle::convertToObject(const std::map<std::string, GroupProperties>& mapGroupProperties)
{
	QJsonObject object;
	for (const auto& [typeName, groupProperties] : mapGroupProperties)
	{
		QJsonObject typeObject;
		typeObject["id"] = groupProperties.typeID.c_str();
		QJsonObject itemsObject;
		for (const auto& [name, property] : groupProperties.properties)
		{
			QJsonObject itemObject;
			for (const auto& [key, value] : property)
			{
				itemObject[key.c_str()] = value.c_str();
			}
			itemsObject[name.c_str()] = itemObject;
		}
		typeObject["items"] = itemsObject;
		object[typeName.c_str()] = typeObject;
	}
	return object;
}

void ZGDPHandle::initAlarmLevel()
{
	std::string sql = "SELECT id, color FROM sp_dict_alarm_level";
	ZG6000::ListStringMap listRecord;
	if (!ZGProxyCommon::execQuerySql(sql, listRecord))
		ZGLOG_ERROR(QStringLiteral("获取告警级别失败"));
	for (const auto& record : listRecord)
	{
		m_mapAlarmLevelColor.insert(std::make_pair(record.at("id"), record.at("color")));
	}
}

#include "ZGDPWebModule.h"
#include "ZGDPHandle.h"

ZGDPWebModule::ZGDPWebModule(QObject *parent)
{
	ZGDPHandle* pHandle = new ZGDPHandle;
	registerHandle("dp/common/prop/get", pHandle, &ZGDPHandle::on_common_prop_get);
	registerHandle("dp/model/prop/get", pHandle, &ZGDPHandle::on_static_prop_get);
	registerHandle("dp/dynamic/prop/get", pHandle, &ZGDPHandle::on_dynamic_prop_get);
	registerHandle("dp/runtime/prop/get", pHandle, &ZGDPHandle::on_runtime_prop_get);
	registerHandle("dp/whole/prop/get", pHandle, &ZGDPHandle::on_whole_prop_get);
	registerHandle("dp/equipment/prop/get", pHandle, &ZGDPHandle::on_equipment_prop_get);
	registerHandle("dp/device/count", pHandle, &ZGDPHandle::on_device_count_get);
	registerHandle("dp/device/get", pHandle, &ZGDPHandle::on_device_get);
	registerHandle("dp/device/add", pHandle, &ZGDPHandle::on_device_add);
	registerHandle("dp/device/update", pHandle, &ZGDPHandle::on_device_update);
	registerHandle("dp/device/delete", pHandle, &ZGDPHandle::on_device_delete);
	registerHandle("dp/device/prop/get", pHandle, &ZGDPHandle::on_device_property_get);
	registerHandle("dp/devices/props/get", pHandle, &ZGDPHandle::on_device_properties_get);
	registerHandle("dp/device/prop/set", pHandle, &ZGDPHandle::on_device_property_value_set);
	registerHandle("dp/event/get", pHandle, &ZGDPHandle::on_event_get);
	registerHandle("dp/event/confirm", pHandle, &ZGDPHandle::on_event_confirm);
}

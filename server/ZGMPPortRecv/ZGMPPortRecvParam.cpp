#include "ZGMPPortRecvParam.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"

std::string ZGMPPortRecvParam::getTableName()
{
    return "mp_param_dataset_param";
}

bool ZGMPPortRecvParam::initModelParam()
{
    std::string sql = "SELECT id, dataTypeID, digit, dataRatio FROM mp_param_model_param";
    ZG6000::ListStringMap listParamParam;
    if (!ZGProxyCommon::execQuerySql(sql, listParamParam))
        return false;
    m_mapModelParam.clear();
    for (const auto& mapParamParam : listParamParam)
    {
        ModelParam modelParam;
        const std::string& id = mapParamParam.at("id");
        const std::string& digit = mapParamParam.at("digit");
        if (!digit.empty())
            modelParam.digit = ZGUtils::strToInt(digit, 2);
        const std::string& dataRatio = mapParamParam.at("dataRatio");
        if (!dataRatio.empty())
            modelParam.ratio = ZGUtils::strToDouble(dataRatio, 1.0);
        const std::string& dataTypeID = mapParamParam.at("dataTypeID");
        modelParam.dataTypeID = (!dataTypeID.empty()) ? dataTypeID : "STRING";
        m_mapModelParam.insert(std::make_pair(id, modelParam));
    }
    return true;
}

bool ZGMPPortRecvParam::calcDataValue(const std::string &id, const std::string &modelID, const std::string &rawValue, std::string &value)
{
    ModelParam modelParam;
    if (!findModelParam(modelID, modelParam))
    {
        ZGLOG_WARN(QString("Can't find dataModelID of param id: %1").arg(id.c_str()));
        return false;
    }
    if (modelParam.dataTypeID == "STRING")
    {
        value = rawValue;
        return true;
    }
    double rawVal = ZGUtils::strToDouble(rawValue, 0.0);
    double newValue = rawVal * modelParam.ratio;
    QString val = QString::number(newValue, 'f', modelParam.digit);
    value = val.toStdString();
    return true;
}

bool ZGMPPortRecvParam::findModelParam(const std::string &modelID, ModelParam &modelParam)
{
    auto pair = m_mapModelParam.find(modelID);
    if (pair == m_mapModelParam.end())
    {
        ZGLOG_ERROR(QString("Can't find param model %1").arg(modelID.c_str()));
        return false;
    }
    modelParam = pair->second;
    return true;
}

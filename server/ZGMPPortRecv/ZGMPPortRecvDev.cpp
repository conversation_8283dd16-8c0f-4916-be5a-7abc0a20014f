#include "ZGJson.h"
#include "ZGDebugMng.h"
#include "ZGMPPortRecvDev.h"
#include "ZGMPPortRecvMng.h"
#include "ZGProxyCommon.h"

void ZGMPPortRecvDev::processMessage(const std::string& portID, std::string& message)
{
    emit dataPrepared(portID.c_str(), message.c_str());
}

void ZGMPPortRecvDev::onProcessMessage(const QString& portID, const QString& json)
{
    ZG6000::StringList listID;
    ZG6000::ListStringMap listMapResult;
    std::string err;
    if (!ZGJson::convertFromJson(json.toStdString(), listID, listMapResult, err))
    {
        ZGLOG_ERROR(err.c_str());
        return;
    }
    for (auto& mapResult : listMapResult)
    {
        mapResult.erase("reason");
    }
    ZG6000::ErrorInfo e;
    ZGProxyCommon::mupdateDataByFields("mp_param_device", listID, listMapResult);
}

bool ZGMPPortRecvDev::initDataParam()
{
    return true;
}

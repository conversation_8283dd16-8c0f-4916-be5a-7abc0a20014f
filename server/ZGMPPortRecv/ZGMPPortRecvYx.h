#ifndef ZGMPPORTRECVYX_H
#define ZGMPPORTRECVYX_H

#include <QObject>
#include "ZGMPPortRecvData.h"

class ZGMPPortRecvYx : public ZGMPPortRecvData
{
    Q_OBJECT
public:
    ZGMPPortRecvYx(int runMode, QObject* parent = nullptr)
        : ZGMPPortRecvData(runMode, parent)
    {
    }

protected:
    struct ModelParam
    {
        bool isInvert{ false };
    };

protected:
    bool initModelParam() override;
    std::string getTableName() override;
    bool calcDataValue(const std::string& id, const std::string& modelID, const std::string& rawValue,
        std::string& value) override;

private:
    bool findModelParam(const std::string& modelID, ModelParam& modelParam);

protected:
    std::unordered_map<std::string, ModelParam> m_mapModelParam;
};

#endif // ZGMPPORTRECVYX_H

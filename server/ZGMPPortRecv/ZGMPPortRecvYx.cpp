#include "ZGMPPortRecvYx.h"

#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"

bool ZGMPPortRecvYx::initModelParam()
{
    std::string sql = "SELECT id, isInvert FROM mp_param_model_yx";
    ZG6000::ListStringMap listYxParam;
    if (!ZGProxyCommon::execQuerySql(sql, listYxParam))
        return false;
    m_mapModelParam.clear();
    for (const auto& mapYxParam : listYxParam)
    {
        ModelParam modelParam;
        const std::string& id = mapYxParam.at("id");
        const std::string& isInvert = mapYxParam.at("isInvert");
        if (!isInvert.empty())
            modelParam.isInvert = ZGUtils::strToBool(isInvert);
        m_mapModelParam.insert(std::make_pair(id, modelParam));
    }
    return true;
}

std::string ZGMPPortRecvYx::getTableName()
{
    return "mp_param_dataset_yx";
}

bool ZGMPPortRecvYx::calcDataValue(const std::string& id, const std::string& modelID, const std::string& rawValue,
    std::string& value)
{
    ModelParam modelParam;
    if (!findModelParam(modelID, modelParam))
    {
        ZGLOG_WARN(QString("Can't find dataModelID of yx id: %1").arg(id.c_str()));
        return false;
    }
    int rawVal = ZGUtils::strToInt(rawValue, 0);
    int newValue = rawVal;
    if (modelParam.isInvert)
    {
        if (rawVal == 1)
            newValue = 2;
        else if (rawVal == 2)
            newValue = 1;
    }
    value = std::to_string(newValue);
    return true;
}

bool ZGMPPortRecvYx::findModelParam(const std::string& modelID, ModelParam& modelParam)
{
    auto pair = m_mapModelParam.find(modelID);
    if (pair == m_mapModelParam.end())
    {
        ZGLOG_ERROR(QString("Can't find yx model %1").arg(modelID.c_str()));
        return false;
    }
    modelParam = pair->second;
    return true;
}

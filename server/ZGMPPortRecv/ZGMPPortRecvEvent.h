#ifndef ZGMPPORTRECVEVENT_H
#define ZGMPPORTRECVEVENT_H

#include "ZGMPPortRecvData.h"
#include <QObject>

class ZGMPPortRecvEvent : public ZGMPPortRecvData
{
	Q_OBJECT
public:
    ZGMPPortRecvEvent(int runMode, QObject* parent = nullptr)
		: ZGMPPortRecvData(runMode, parent)
	{
	}

protected:
    std::string getTableName() override;
    bool calcDataValue(const std::string& id, const std::string& modelID, const std::string& rawValue, std::string& value) override;
};

#endif // ZGMPPORTRECVEVENT_H

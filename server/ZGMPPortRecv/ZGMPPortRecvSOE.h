#ifndef ZGMPPORTRECVSOE_H
#define ZGMPPORTRECVSOE_H

#include "ZGMPPortRecvYx.h"

class ZGRedisClient;
class ZGMPPortRecvSOE : public ZGMPPortRecvYx
{
	Q_OBJECT
public:
	explicit ZGMPPortRecvSOE(int runMode, QObject* parent = nullptr) :
		ZGMPPortRecvYx(runMode, parent) {}
	void processMessage(const std::string& portID, std::string& message) override;
protected:
	void onProcessMessage(const QString& portID, const QString& json) override;

private:
	ZGRedisClient* m_pRedisRtTopic{nullptr};
};

#endif // ZGMPPORTRECVSOE_H

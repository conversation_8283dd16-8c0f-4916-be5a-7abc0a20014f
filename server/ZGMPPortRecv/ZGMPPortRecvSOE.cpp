#include "ZGMPPortRecvSOE.h"
#include "ZGMPPortRecvMng.h"
#include "ZGJson.h"
#include "ZGUtils.h"
#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "redis/ZGRedisClient.h"

void ZGMPPortRecvSOE::processMessage(const std::string& portID, std::string& message)
{
	emit dataPrepared(portID.c_str(), message.c_str());
}

void ZGMPPortRecvSOE::onProcessMessage(const QString& portID, const QString& json)
{
	QList<QMap<QString, QString>> listSOE;
	QString errMsg;
	if (!ZGJson::convertFromJson(json, listSOE, errMsg))
	{
		ZGLOG_ERROR(errMsg);
		return;
	}
	const auto& portParam = ZGMPPortRecvMng::instance()->getPortParam();
	for (auto& soe: listSOE)
	{
        try
        {
			soe["rtNewValue"] = soe["rtNewValue"];
			const auto& params = ZGUtils::get(portParam, portID.toStdString());
			const auto& isPassthrough = ZGUtils::get(params, "isPassthrough");
			if (isPassthrough != "1")
			{
				const auto& yxID = soe["id"].toStdString();
				const auto& pairData = m_mapDataParam.find(yxID);
				if (pairData == m_mapDataParam.end())
					continue;
				const auto& pairModel = m_mapModelParam.find(pairData->second.modelID);
				if (pairModel == m_mapModelParam.end())
					continue;
				if (pairModel->second.isInvert)
				{
					if (soe["rtRawValue"] == "1")
						soe["rtNewValue"] = "2";
					else if (soe["rtRawValue"] == "2")
						soe["rtNewValue"] = "1";
					else
						soe["rtNewValue"] = soe["rtRawValue"];
				}
				else
					soe["rtNewValue"] = soe["rtRawValue"];
			}
        }
        catch (const std::exception& e)
        {
			ZGLOG_ERROR(e.what());
        }
	}
	const auto& newJson = ZGJson::convertToJson(listSOE);
	if (m_pRedisRtTopic == nullptr)
	{
		m_pRedisRtTopic = ZGRuntime::instance()->getRedisClientRTTopic();
		if (m_pRedisRtTopic == nullptr)
			return;
	}
	long long size{0};
	m_pRedisRtTopic->publish("ZG_T_SOE", newJson, size, errMsg);
}

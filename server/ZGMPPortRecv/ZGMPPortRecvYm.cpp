#include "ZGMPPortRecvYm.h"
#include "ZGDebugMng.h"
#include "ZGUtils.h"
#include "ZGProxyCommon.h"

std::string ZGMPPortRecvYm::getTableName()
{
    return "mp_param_dataset_ym";
}

bool ZGMPPortRecvYm::initModelParam()
{
    std::string sql = "SELECT id, dataRatio FROM mp_param_model_ym";
    ZG6000::ListStringMap listYmParam;
    if (!ZGProxyCommon::execQuerySql(sql, listYmParam))
        return false;
    m_mapModelParam.clear();
    try
    {
        for (const auto& mapYmParam : listYmParam)
        {
            ModelParam modelParam;
            const std::string& id = ZGUtils::get(mapYmParam, "id");
            const std::string& dataRatio = ZGUtils::get(mapYmParam, "dataRatio");
            if (!dataRatio.empty())
                modelParam.ratio = ZGUtils::strToDouble(dataRatio, 1.0);
            m_mapModelParam.insert(std::make_pair(id, modelParam));
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
    return true;
}

bool ZGMPPortRecvYm::calcDataValue(const std::string &id, const std::string &modelID, const std::string &rawValue, std::string &value)
{
    ModelParam modelParam;
    if (!findModelParam(modelID, modelParam))
    {
        ZGLOG_WARN(QString("Can't find dataModelID of ym id: %1").arg(id.c_str()));
        return false;
    }
    double rawVal = ZGUtils::strToDouble(rawValue, 0.0);
    double newValue = rawVal * modelParam.ratio;
    QString val = QString::number(newValue, 'f', modelParam.digit);
    value = val.toStdString();
    return true;
}

bool ZGMPPortRecvYm::findModelParam(const std::string &modelID, ModelParam &modelParam)
{
    auto pair = m_mapModelParam.find(modelID);
    if (pair == m_mapModelParam.end())
    {
        ZGLOG_ERROR(QString("Can't find ym model %1").arg(modelID.c_str()));
        return false;
    }
    modelParam = pair->second;
    return true;
}

#include "ZGMPPortRecvYc.h"

#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGMPPortRecvMng.h"
#include "ZGProxyCommon.h"
#include "ZGProxyMng.h"
#include "ZGUtils.h"

bool ZGMPPortRecvYc::initModelParam()
{
    std::string sql = "SELECT id, digit, dataRatio, dataOffset, zeroDriftDown, zeroDriftUp FROM mp_param_model_yc";
    ZG6000::ListStringMap listYcParam;
    if (!ZGProxyCommon::execQuerySql(sql, listYcParam))
        return false;
    m_mapModelParam.clear();
    for (const auto& mapYcParam : listYcParam)
    {
        ModelParam modelParam;
        const std::string& id = mapYcParam.at("id");
        const std::string& digit = mapYcParam.at("digit");
        if (!digit.empty())
            modelParam.digit = ZGUtils::strToInt(digit, 2);
        const std::string& dataRatio = mapYcParam.at("dataRatio");
        if (!dataRatio.empty())
            modelParam.ratio = ZGUtils::strToDouble(dataRatio, 1.0);
        const std::string& dataOffset = mapYcParam.at("dataOffset");
        if (!dataOffset.empty())
            modelParam.offset = ZGUtils::strToDouble(dataOffset, 0.0);
        const std::string& zeroDriftUp = mapYcParam.at("zeroDriftUp");
        if (!zeroDriftUp.empty())
            modelParam.zeroUp = ZGUtils::strToDouble(zeroDriftUp, 0.0);
        const std::string& zeroDriftDown = mapYcParam.at("zeroDriftDown");
        if (!zeroDriftDown.empty())
            modelParam.zeroDown = ZGUtils::strToDouble(zeroDriftDown, 0.0);
        m_mapModelParam.insert(std::make_pair(id, modelParam));
    }
    return true;
}

std::string ZGMPPortRecvYc::getTableName()
{
    return "mp_param_dataset_yc";
}

bool ZGMPPortRecvYc::calcDataValue(const std::string& id, const std::string& modelID, const std::string& rawValue, std::string& value)
{
    ModelParam modelParam;
    if (!findModelParam(modelID, modelParam))
    {
        ZGLOG_WARN(QString("Can't find dataModelID of yc id: %1").arg(id.c_str()));
        return false;
    }
    double rawVal = ZGUtils::strToDouble(rawValue, 0.0);
    double newValue = rawVal * modelParam.ratio + modelParam.offset;
    if (newValue > modelParam.zeroDown && newValue < modelParam.zeroUp)
        newValue = 0;
    QString val = QString::number(newValue, 'f', modelParam.digit);
    value = val.toStdString();
    return true;
}

bool ZGMPPortRecvYc::findModelParam(const std::string& modelID, ModelParam& modelParam)
{
    auto pair = m_mapModelParam.find(modelID);
    if (pair == m_mapModelParam.end())
    {
        ZGLOG_ERROR(QString("Can't find yc model %1").arg(modelID.c_str()));
        return false;
    }
    modelParam = pair->second;
    return true;
}

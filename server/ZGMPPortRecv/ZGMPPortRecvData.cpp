#include "ZGMPPortRecvData.h"
#include "ZGMPPortRecvMng.h"
#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGUtils.h"
#include "ZGProxyCommon.h"

std::unordered_map<std::string, std::string> ZGMPPortRecvData::m_mapExpressParam{};

ZGMPPortRecvData::ZGMPPortRecvData(int runMode, QObject *parent) : QObject(parent),
	m_runMode(runMode)
{
    initProcessor();
}

ZGMPPortRecvData::~ZGMPPortRecvData()
{
}

bool ZGMPPortRecvData::initExpressParam()
{
    std::string sql = "SELECT id, expressID FROM mp_param_dataset_preprocess";
    ZG6000::ListStringMap listTextExpress;
    if (!ZGProxyCommon::execQuerySql(sql, listTextExpress))
    {
        ZGLOG_ERROR("get express id error.");
        return false;
    }
    m_mapExpressParam.clear();
    for (const auto& textExpress : listTextExpress)
    {
        const std::string& id = textExpress.at("id");
        const std::string& expressID = textExpress.at("expressionID");
        m_mapExpressParam.insert(std::make_pair(id, expressID));
    }
    return true;
}

bool ZGMPPortRecvData::initParam()
{
    if (!initDataParam())
    {
        ZGLOG_ERROR("initDataParam error.");
        return false;
    }
    if (!initModelParam())
    {
        ZGLOG_ERROR("initModelParam error.");
        return false;
    }
    return true;
}

void ZGMPPortRecvData::initProcessor()
{
    connect(this, &ZGMPPortRecvData::dataPrepared, this, &ZGMPPortRecvData::onProcessMessage);
    connect(this, &ZGMPPortRecvData::timeout, this, &ZGMPPortRecvData::onProcessMessages);
    moveToThread(&m_thread);
    m_thread.start();
}

void ZGMPPortRecvData::processMessage(const std::string& portID, std::string& message)
{
    if (m_runMode == 1)
        emit dataPrepared(portID.c_str(), message.c_str());
    else
        addMessage(portID, message);
}

void ZGMPPortRecvData::checkTimeout()
{
    std::unique_lock locker(m_mutex);
    if (!m_listMessage.empty())
        emit timeout();
}

void ZGMPPortRecvData::onProcessMessages()
{
    std::unordered_map<std::string, ZG6000::MapStringMap> mapDatasetRecords;
    std::string tableName = getTableName();
    std::vector<Message> listMessage;
    {
        std::unique_lock locker(m_mutex);
        listMessage = std::move(m_listMessage);
    }
    const auto& portParam = ZGMPPortRecvMng::instance()->getPortParam();
    for (const auto& message : listMessage)
    {
        ZG6000::StringList listID;
        ZG6000::ListStringMap listRecord;
        std::string err;
        if (!ZGJson::convertFromJson(message.message, listID, listRecord, err))
        {
            ZGLOG_ERROR(err.c_str());
            continue;
        }
        for (size_t i = 0; i < listID.size(); ++i)
        {
            DataParam dataParam;
            if (!findDataParam(listID[i], dataParam))
                continue;
            std::string rtRawValue;
            try
            {
                rtRawValue = ZGUtils::get(listRecord[i], "rtRawValue");
                std::string calcValue = rtRawValue;
                bool success = true;
                const auto& params = ZGUtils::get(portParam, message.portID);
                const auto& isPassthrough = ZGUtils::get(params, "isPassthrough");
                if (isPassthrough != "1")
                {
                    std::string expressID;
                    if (findExpressParam(listID[i], expressID))
                        success = calculateExpress(listID[i], expressID, calcValue);
                    else
                        success = calcDataValue(listID[i], dataParam.modelID, rtRawValue, calcValue);
                }
                if (success)
                    listRecord[i]["rtNewValue"] = calcValue;
                auto pair = mapDatasetRecords.find(dataParam.datasetID);
                if (pair == mapDatasetRecords.end())
                {
                    ZG6000::MapStringMap records;
                    records.insert(std::make_pair(std::move(listID[i]), std::move(listRecord[i])));
                    mapDatasetRecords.insert(std::make_pair(dataParam.datasetID, std::move(records)));
                }
                else
                {
                    pair->second.insert(std::make_pair(std::move(listID[i]), std::move(listRecord[i])));
                }
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(e.what());
            }
        }
    }
    for (auto& datasetRecord : mapDatasetRecords)
    {
        processDatasetRecords(tableName, datasetRecord.second);
    }
}

void ZGMPPortRecvData::onProcessMessage(const QString& portID, const QString& json)
{
    ZG6000::StringList listID;
    ZG6000::ListStringMap listMapResult;
    std::string err;
    if (!ZGJson::convertFromJson(json.toStdString(), listID, listMapResult, err))
    {
        ZGLOG_ERROR(err.c_str());
        return;
    }
    std::string tableName = getTableName();
    const auto& portParam = ZGMPPortRecvMng::instance()->getPortParam();
    for (size_t i = 0; i < listID.size(); ++i)
    {
        DataParam dataParam;
        if (!findDataParam(listID[i], dataParam))
            continue;
        std::string rtRawValue;
        try
        {
            rtRawValue = ZGUtils::get(listMapResult[i], "rtRawValue");
            std::string calcValue = rtRawValue;
            bool success = true;
            const auto& params = ZGUtils::get(portParam, portID.toStdString());
            const auto& isPassthrough = ZGUtils::get(params, "isPassthrough");
            if (isPassthrough != "1")
            {
                std::string expressID;
                if (findExpressParam(listID[i], expressID))
                    success = calculateExpress(listID[i], expressID, calcValue);
                else
                    success = calcDataValue(listID[i], dataParam.modelID, rtRawValue, calcValue);
            }
            if (success)
                listMapResult[i]["rtNewValue"] = calcValue;
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
    if (listID.size() == 1)
        ZGProxyCommon::updateDataByID(tableName, listID[0], listMapResult[0], true);
    else
        ZGProxyCommon::mupdateDataByFields(tableName, listID, listMapResult, true);
}

bool ZGMPPortRecvData::initDataParam()
{
    std::string sql = "SELECT id, datasetID, dataModelID FROM " + getTableName();
    ZG6000::ListStringMap listDataParam;
    QElapsedTimer timer;
    timer.start();
    if (!ZGProxyCommon::execQuerySql(sql, listDataParam))
        return false;
    ZGLOG_TRACE(QString("listDataParam tableName = %1 size = %2, elapsed = %3")
                    .arg(getTableName().c_str())
                    .arg(listDataParam.size())
                    .arg(timer.elapsed()));
    m_mapDataParam.clear();
    for (const auto& dataParam : listDataParam)
    {
        const auto& id = dataParam.at("id");
        const auto& datasetID = dataParam.at("datasetID");
        const auto& dataModelID = dataParam.at("dataModelID");
        m_mapDataParam.insert({ id, {datasetID, dataModelID} });
    }
    return true;
}

void ZGMPPortRecvData::addMessage(const std::string& portID, std::string& message)
{
    std::unique_lock locker(m_mutex);
    m_listMessage.push_back({ portID, std::move(message) });
}

void ZGMPPortRecvData::combineMessage(const ZG6000::StringList& listMessage, ZG6000::StringList& listTotalID, ZG6000::ListStringMap& listTotalMapResult)
{
    for (const auto& message : listMessage)
    {
        ZG6000::StringList listID;
        ZG6000::ListStringMap listMapResult;
        std::string err;
        if (!ZGJson::convertFromJson(message, listID, listMapResult, err))
        {
            ZGLOG_ERROR(err.c_str());
            continue;
        }
        for (auto& mapResult : listMapResult)
        {
            mapResult["rtNewValue"] = mapResult["rtRawValue"];
        }
        std::move(listID.begin(), listID.end(), std::back_inserter(listTotalID));
        std::move(listMapResult.begin(), listMapResult.end(), std::back_inserter(listTotalMapResult));
    }
}

void ZGMPPortRecvData::processDatasetRecords(const std::string& tableName, ZG6000::MapStringMap& records)
{
    ZG6000::StringList listID;
    ZG6000::ListStringMap listRecord;
    listID.reserve(records.size());
    listRecord.reserve(records.size());
    size_t count = 0;
    for (auto& pair : records)
    {
        listID.emplace_back(std::move(pair.first));
        listRecord.emplace_back(std::move(pair.second));
        ++count;
    }
    if (count > 0)
        ZGProxyCommon::mupdateDataByFields(tableName, listID, listRecord, true);
}

bool ZGMPPortRecvData::getCurrentData(const std::string& tableName, const ZG6000::StringList& listID, ZG6000::ListStringMap& listRecord)
{
    if (!ZGProxyCommon::mgetDataByFields(tableName, listID,
        { "id", "dataModelID", "rtRawValue", "rtNewValue", "rtQualityFlag", "rtUpdateTime" }, listRecord))
    {
        ZGLOG_ERROR(QString("getCurrentData error, tableName = %1").arg(tableName.c_str()));
        return false;
    }
    return true;
}

bool ZGMPPortRecvData::calculateExpress(const std::string& id, const std::string& expressID, std::string& value)
{
    auto scriptProxy = ZGProxyMng::instance()->getProxySPScriptProcess();
    if (scriptProxy == nullptr)
    {
        ZGLOG_WARN("Can't get script proxy.");
        return false;
    }
    try
    {
        ::std::string currNewValue;
        ZG6000::ErrorInfo e;
//        if (!scriptProxy->callToString(expressID, ZG6000::StringList{ id }, value, e))
//        {
//            ZGLOG_WARN(e);
//            return false;
//        }
        return true;
    }
    catch (const Ice::Exception& ie)
    {
        ZGLOG_ERROR(ie.what());
        return false;
    }
}

bool ZGMPPortRecvData::findDataParam(const std::string& dataID, DataParam& dataParam)
{
    const auto& pair = m_mapDataParam.find(dataID);
    if (pair == m_mapDataParam.end())
    {
        ZGLOG_WARN(QString("Can't find %1 in table %2.").arg(dataID.c_str()).arg(getTableName().c_str()));
        return false;
    }
    dataParam = pair->second;
    return true;
}

bool ZGMPPortRecvData::findExpressParam(const std::string& dataID, std::string& expressID)
{
    const auto& pair = m_mapExpressParam.find(dataID);
    if (pair == m_mapExpressParam.end())
        return false;
    expressID = pair->second;
    return true;
}

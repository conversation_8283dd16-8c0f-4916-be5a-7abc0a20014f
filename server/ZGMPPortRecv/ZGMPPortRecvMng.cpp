#include "ZGMPPortRecvMng.h"

#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGMPPortRecvDev.h"
#include "ZGMPPortRecvEvent.h"
#include "ZGMPPortRecvText.h"
#include "ZGMPPortRecvYc.h"
#include "ZGMPPortRecvYx.h"
#include "ZGMPPortRecvYm.h"
#include "ZGMPPortRecvParam.h"
#include "ZGMPPortRecvSOE.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"

#include <QDebug>
#include <QRandomGenerator>
#include <QThread>

static ZGMPPortRecvMng* g_pPortRecvMng = nullptr;

ZGMPPortRecvMng *ZGMPPortRecvMng::instance()
{
    if (g_pPortRecvMng == nullptr)
        g_pPortRecvMng = new ZGMPPortRecvMng();
    return g_pPortRecvMng;
}

void ZGMPPortRecvMng::init()
{
    initServerInstConfig();
    initEvents();
    initProcessors();
    start();
    ZGLOG_INFO("ZGMPPortRecv init start...");
}

bool ZGMPPortRecvMng::checkState()
{
    return m_initialized;
}

ZGMPPortRecvMng::ZGMPPortRecvMng(QObject *parent) : QThread(parent)
{

}

void ZGMPPortRecvMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGMPPortRecvMng::onCheckStatus);
    connect(this, &ZGMPPortRecvMng::initFinished, this, &ZGMPPortRecvMng::onInitFinished);
}

void ZGMPPortRecvMng::initServerInstConfig()
{
    const auto & serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "runMode", value, 1, 2, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_runMode = value;
}

void ZGMPPortRecvMng::initProcessors()
{
    m_mapDataProcessor.insert(std::make_pair("yc", new ZGMPPortRecvYc(m_runMode)));
    m_mapDataProcessor.insert(std::make_pair("yx", new ZGMPPortRecvYx(m_runMode)));
    m_mapDataProcessor.insert(std::make_pair("text", new ZGMPPortRecvText(m_runMode)));
    m_mapDataProcessor.insert(std::make_pair("ym", new ZGMPPortRecvYm(m_runMode)));
    m_mapDataProcessor.insert(std::make_pair("param", new ZGMPPortRecvParam(m_runMode)));
    m_mapDataProcessor.insert(std::make_pair("event", new ZGMPPortRecvEvent(m_runMode)));
    m_mapDataProcessor.insert(std::make_pair("dev", new ZGMPPortRecvDev(m_runMode)));
    m_mapDataProcessor.insert(std::make_pair("soe", new ZGMPPortRecvSOE(m_runMode)));
}

bool ZGMPPortRecvMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGMPPortRecvMng::initRedisClient()
{
    QList<ZGRuntime::REDIS_CLIENT_TYPE> listType;
    listType.append(ZGRuntime::REDIS_PORT_QUEUE);
    listType.append(ZGRuntime::REDIS_RT_QUEUE);
    listType.append(ZGRuntime::REDIS_RT_TOPIC);
    if (!ZGRuntime::instance()->initRedisClient(listType))
    {
        ZGLOG_ERROR("initRedisClient failed.");
        return false;
    }
    m_pRedisPortQueue = ZGRuntime::instance()->getRedisClientPortQueue();
    if (m_pRedisPortQueue == nullptr)
    {
        ZGLOG_ERROR("getRedisClientPortQueue error.");
        return false;
    }
    m_pRedisRtQueue = ZGRuntime::instance()->getRedisClientRTQueue();
    if (m_pRedisRtQueue == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTQueue error.");
        return false;
    }
    m_pRedisRtTopic = ZGRuntime::instance()->getRedisClientRTTopic();
    if (m_pRedisRtTopic == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTTopic error.");
        return false;
    }
    return true;
}

bool ZGMPPortRecvMng::initPortParam()
{
    QString ports = ZGRuntime::instance()->getInstanceConfig("serverInst", "port");
    std::string sql;
    if (ports.isEmpty())
    {
        sql = "SELECT id, isDownPort, isPassthrough FROM mp_param_port WHERE isEnabled = 1";
    }
    else
    {
        ports.replace("&", ",");
        sql = "SELECT id, isDownPort, isPassthrough FROM mp_param_port WHERE id IN (" + ports.toStdString() + ")";
    }
    if (!ZGProxyCommon::execQuerySql(sql, m_mapPort))
        return false;
    return true;
}

bool ZGMPPortRecvMng::initProcessorParam()
{
	for (const auto & pair : m_mapDataProcessor)
	{
        if (!pair.second->initParam())
		{
            ZGLOG_ERROR(QString("%1 initParam error.").arg(pair.first.c_str()));
            return false;
		}
	}
    return true;
}

void ZGMPPortRecvMng::onInitFinished()
{
    if (m_runMode == 1)
        m_checkTimer.start(m_checkInterval * 1000);
    else if (m_runMode == 2)
        m_checkTimer.start(100);
}

void ZGMPPortRecvMng::onCheckStatus()
{
    if (m_runMode == 1)
        m_masterInst = ZGRuntime::instance()->isMaster();
    else if (m_runMode == 2)
    {
	    for (const auto & pair : m_mapDataProcessor)
	    {
            pair.second->checkTimeout();
	    }
        ++m_checkCount;
        if (m_checkCount >= m_checkInterval * 10)
        {
            m_checkCount = 0;
            m_masterInst = ZGRuntime::instance()->isMaster();
        }
    }
}

int splitString(const std::string& src, const std::string& comma, ZG6000::StringList& listString)
{
    int count = 0;
    size_t pos;
    std::string temp = src + comma;
    std::string_view view{temp.c_str()};
    while((pos = view.find(comma)) != std::string::npos) 
    {
        if (pos) 
        {
            std::string str{view.substr(0, pos)};
            listString.push_back(str);
            ++count;
        }
        view.remove_prefix(pos + 1);
    }
    return count;
}

void ZGMPPortRecvMng::run()
{
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        msleep(m_initInterval * 1000);
    }
    sleep(QRandomGenerator::global()->bounded(5, 10));
    while (!initPortParam())
    {
        ZGLOG_ERROR("initPortParam error.");
        msleep(m_initInterval * 1000);
    }
    while (!initRedisClient())
    {
        ZGLOG_ERROR("initRedisClient error.");
        msleep(m_initInterval * 1000);
    }
    msleep(100);
    while (!m_pRedisPortQueue->connected(true))
    {
        ZGLOG_ERROR("connect to redis port server failed.");
        msleep(m_initInterval * 1000);
    }
    while (!m_pRedisRtQueue->connected(true))
    {
        ZGLOG_ERROR("connect to redis rt server failed.");
        msleep(m_initInterval * 1000);
    }
    while (!initProcessorParam())
    {
        ZGLOG_ERROR("initProcessorParam error.");
        msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    emit initFinished();
    ZGLOG_INFO("ZGMPPortRecv init finished.");
    ZG6000::StringList listPortQueue;
    ZG6000::StringList listQueue{"/recv/yc", "/recv/yx", "/recv/text", "/recv/ym", "/recv/param", "/recv/event", "/recv/yk", "/recv/ys", "/recv/yt", "/recv/soe", "/recv/dev"};
    for (const auto& port : m_mapPort)
    {
        for (const auto& queue : listQueue)
        {
            std::string queueName = port.first + queue;
            ZGLOG_INFO(queueName.c_str());
            listPortQueue.push_back(queueName.c_str());
        }
    }
    std::pair<std::string, std::string> value;
    std::string errMsg;
    long long size;
    while (true)
    {
        if (m_masterInst)
        {
            if (m_pRedisPortQueue->blpop(listPortQueue, m_commandTimeout, value, errMsg))
            {
                ZG6000::StringList listString;
                size_t count = ZGUtils::splitString(value.first, "/", listString);
                const auto& portId = listString[0];
                const auto& type = listString[count - 1];
                if (type == "yx" || type == "yc" || type == "text" || type == "ym" || type == "param" || type == "dev" || type == "soe")
                {
                    auto pair = m_mapDataProcessor.find(type);
                    if (pair != m_mapDataProcessor.end())
                        pair->second->processMessage(portId, value.second);
                }
                else if (type == "event")
                {
                    auto pair = m_mapDataProcessor.find(type);
                    std::string message = value.second;
                    if (pair != m_mapDataProcessor.end())
                        pair->second->processMessage(portId, value.second);
                    ZG6000::ListStringMap listEvent;
                    if (!ZGJson::convertFromJson(value.second, listEvent, errMsg))
                        return;
                    for (auto& event : listEvent)
                    {
                        event.insert({"rtNewValue", event["rtRawValue"]});
                    }
                    m_pRedisRtTopic->publish("ZG_T_EVENT", ZGJson::convertToJson(listEvent), size, errMsg);
                }
                else if (type == "yk")
                {
                    ZGLOG_DEBUG(value.second.c_str());
                    const auto& pair = m_mapPort.find(portId);
                    if (pair != m_mapPort.end())
                    {
                        if (pair->second["isDownPort"] == "1")
                            m_pRedisRtQueue->rpush("ZG_Q_PORT_YK_RECV", value.second, size, errMsg);
                        else
                            m_pRedisRtQueue->rpush("ZG_Q_SYSTEM_YK", value.second, size, errMsg);
                    }
                }
                else if (type == "ys")
                {
                    ZGLOG_DEBUG(value.second.c_str());
                    const auto& pair = m_mapPort.find(portId);
                    if (pair != m_mapPort.end())
                    {
                        if (pair->second["isDownPort"] == "1")
                            m_pRedisRtQueue->rpush("ZG_Q_PORT_YS_RECV", value.second, size, errMsg);
                        else
                            m_pRedisRtQueue->rpush("ZG_Q_SYSTEM_YS", value.second, size, errMsg);
                    }
                }
                else if (type == "yt")
                {
                    ZGLOG_DEBUG(value.second.c_str());
                    const auto& pair = m_mapPort.find(portId);
                    if (pair != m_mapPort.end())
                    {
                        if (pair->second["isDownPort"] == "1")
                            m_pRedisRtQueue->rpush("ZG_Q_PORT_YT_RECV", value.second, size, errMsg);
                        else
                            m_pRedisRtQueue->rpush("ZG_Q_SYSTEM_YT", value.second, size, errMsg);
                    }
                }
            }
        }
        else
            msleep(1000);
    }
}

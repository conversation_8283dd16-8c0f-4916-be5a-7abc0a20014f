#ifndef ZGMPPORTRECVTEXT_H
#define ZGMPPORTRECVTEXT_H

#include <QObject>
#include "ZGMPPortRecvData.h"

class ZGMPPortRecvText : public ZGMPPortRecvData
{
	Q_OBJECT
public:
    ZGMPPortRecvText(int runMode, QObject* parent = nullptr)
		: ZGMPPortRecvData(runMode, parent)
	{
	}

protected:
    std::string getTableName() override;
    bool calcDataValue(const std::string& /*id*/, const std::string& /*modelID*/, const std::string& rawValue,
        std::string& value) override;
};

#endif // ZGMPPORTRECVTEXT_H

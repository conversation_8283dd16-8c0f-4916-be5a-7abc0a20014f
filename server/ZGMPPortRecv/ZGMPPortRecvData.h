#ifndef ZGMPPORTRECVDATA_H
#define ZGMPPORTRECVDATA_H

#include <QObject>
#include <QThread>
#include <unordered_map>
#include <mutex>

#include "ZGServerCommon.h"


class ZGMPPortRecvData : public QObject
{
    Q_OBJECT
public:
    explicit ZGMPPortRecvData(int runMode, QObject *parent = nullptr);
    ~ZGMPPortRecvData();

public:
    static bool initExpressParam();
    virtual bool initParam();
    virtual void processMessage(const std::string& portID, std::string& message);
    void initProcessor();
    void checkTimeout();

signals:
    void timeout();
    void dataPrepared(const QString& portID, const QString& json);

protected slots:
    virtual void onProcessMessages();
    virtual void onProcessMessage(const QString& portID, const QString& json);

protected:
    struct DataParam
    {
        std::string datasetID{ "" };
        std::string modelID{ "" };
    };

protected:
    virtual std::string getTableName() { return ""; }
    virtual bool initDataParam();
    virtual bool initModelParam() { return true; }
    virtual bool calcDataValue(const std::string& id, const std::string& modelID, const std::string& rawValue, std::string& value) { return true; }
    void addMessage(const std::string& portID, std::string& message);

protected:
    void combineMessage(const ZG6000::StringList& listMessage, ZG6000::StringList& listTotalID,
        ZG6000::ListStringMap& listTotalMapResult);
    void processDatasetRecords(const std::string& tableName, ZG6000::MapStringMap& records);
    bool getCurrentData(const std::string& tableName, const ZG6000::StringList& listID, ZG6000::ListStringMap& listRecord);
    bool calculateExpress(const std::string& id, const std::string& expressID, std::string& value);
    bool findDataParam(const std::string& dataID, DataParam& dataParam);
    bool findExpressParam(const std::string& dataID, std::string& expressID);

protected:
    std::unordered_map<std::string, DataParam> m_mapDataParam;
    struct Message
    {
        std::string portID;
        std::string message;
    };
    std::vector<Message> m_listMessage;
    static std::unordered_map<std::string, std::string> m_mapExpressParam;
    int m_runMode{ 1 };
    QThread m_thread;
    std::mutex m_mutex;
};

#endif // ZGMPPORTRECVDATA_H

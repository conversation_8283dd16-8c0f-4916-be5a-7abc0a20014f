#ifndef ZGMPPORTRECVYM_H
#define ZGMPPORTRECVYM_H

#include "ZGMPPortRecvData.h"

class ZGMPPortRecvYm : public ZGMPPortRecvData
{
    Q_OBJECT

public:
    ZGMPPortRecvYm(int runMode, QObject* parent = nullptr)
        : ZGMPPortRecvData(runMode, parent)
    {
    }

protected:
    struct ModelParam
    {
        int digit{ 2 };
        double ratio{ 1.0 };
    };
protected:
    std::string getTableName() override;
    bool initModelParam() override;
    bool calcDataValue(const std::string &id, const std::string &modelID, const std::string &rawValue, std::string &value) override;

private:
    bool findModelParam(const std::string& modelID, ModelParam& modelParam);

private:
    std::unordered_map<std::string, ModelParam> m_mapModelParam;
};

#endif // ZGMPPORTRECVYM_H

#ifndef ZGMPPORTRECVYC_H
#define ZGMPPORTRECVYC_H

#include <QObject>
#include "ZGMPPortRecvData.h"

class ZGMPPortRecvYc : public ZGMPPortRecvData
{
    Q_OBJECT
public:
    ZGMPPortRecvYc(int runMode, QObject* parent = nullptr)
		: ZGMPPortRecvData(runMode, parent)
	{
	}

protected:
    struct ModelParam
    {
        int digit{ 2 };
        double ratio{ 1.0 };
        double offset{ 0.0 };
        double zeroDown{ 0.0 };
        double zeroUp{ 0.0 };
    };
protected:
    bool initModelParam() override;
    std::string getTableName() override;
    bool calcDataValue(const std::string& id, const std::string& modelID, const std::string& rawValue, std::string& value) override;

private:
    bool findModelParam(const std::string& modelID, ModelParam& modelParam);

private:
    std::unordered_map<std::string, ModelParam> m_mapModelParam;
};

#endif // ZGMPPORTRECVYC_H

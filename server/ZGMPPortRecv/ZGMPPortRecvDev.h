#ifndef ZGMPPORTRECVDEV_H
#define ZGMPPORTRECVDEV_H

#include "ZGMPPortRecvData.h"

class ZGMPPortRecvDev : public ZGMPPortRecvData
{

private:
	Q_OBJECT
public:
    ZGMPPortRecvDev(int runMode, QObject* parent = nullptr)
		: ZGMPPortRecvData(runMode, parent)
	{
	}
public:
	void processMessage(const std::string& portID, std::string& message) override;

protected slots:
	void onProcessMessage(const QString& portID, const QString& json) override;

    // ZGMPPortRecvData interface
protected:
    bool initDataParam() override;
};

#endif // ZGMPPORTRECVDEV_H

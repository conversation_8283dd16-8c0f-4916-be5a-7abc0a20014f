#ifndef ZGMPPORTRECVMNG_H
#define ZGMPPORTRECVMNG_H

#include <QObject>
#include <QTimer>
#include <QThread>
#include <unordered_map>
#include <shared_mutex>
#include "ZGProxyMng.h"

class ZGRedisClient;
class ZGMPPortRecvData;
class ZGMPPortRecvDev;
class ZGMPPortRecvMng : public QThread
{
    friend class ZGMPPortRecvDev;
    Q_OBJECT

public:
    static ZGMPPortRecvMng* instance();
    void init();
    bool checkState();
    const ZG6000::MapStringMap& getPortParam() { return m_mapPort; }

signals:
    void devPrepared(const QString& json);
    void initFinished();

private slots:
    void onInitFinished();
    void onCheckStatus();

private:
    explicit ZGMPPortRecvMng(QObject *parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    void initProcessors();
    bool initServerInstInfo();
    bool initRedisClient();
    bool initPortParam();
    bool initProcessorParam();

protected:
    void run() override;
private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    bool m_masterInst{false};
    int m_initInterval{10};
    int m_checkInterval{5};
    int m_commandTimeout{5};
    QTimer m_checkTimer;
    int m_checkCount{0};
    int m_runMode{1}; // 运行模式, 1代表性能模式，2代表效率模式
    ZGRedisClient* m_pRedisPortQueue{nullptr};
    ZGRedisClient* m_pRedisRtQueue{nullptr};
    ZGRedisClient* m_pRedisRtTopic{nullptr};
    ZG6000::MapStringMap m_mapPort;
    QThread m_devThread;
    std::unordered_map<std::string, ZGMPPortRecvData*> m_mapDataProcessor;
    ZGMPPortRecvDev* m_pProcessorDev{nullptr};
    std::shared_mutex m_expressMutex;
};

#endif // ZGMPPORTRECVMNG_H

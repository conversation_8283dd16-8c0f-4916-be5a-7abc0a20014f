#ifndef ZGSPUSERMANAGERI_H
#define ZGSPUSERMANAGERI_H

#include "ZGSPUserManager.h"

namespace ZG6000
{
    class ZGSPUserManagerI : public ZGSPUserManager
    {
    public:
        ZGSPUserManagerI();
        void dispatchData(std::string tableName,
                          std::string oper,
                          std::string reason,
                          std::string time,
                          ListRecord listRecord,
                          const Ice::Current& current) override;
        bool checkState(const Ice::Current& current) override;
        bool getUserInfo(std::string userID,
                         StringMap& user,
                         ListStringMap& listRole,
                         ListStringMap& listCard,
                         ListStringMap& listAuth,
                         ListStringMap& listAppNode,
                         ErrorInfo& e,
                         const Ice::Current& current) override;
        bool getUserFingers(std::string userID,
                            ListStringMap& listFinger,
                            ErrorInfo& e,
                            const Ice::Current& current) override;
        bool getUserFace(std::string userID,
                         std::string& faceData,
                         ErrorInfo& e,
                         const Ice::Current& current) override;
        bool addUser(StringMap user,
                     ListStringMap listRole,
                     ListStringMap listAuth,
                     ListStringMap listAppNode,
                     ErrorInfo& e,
                     const Ice::Current& current) override;
        bool updateUser(StringMap user,
                        ListStringMap listRole,
                        ListStringMap listAuth,
                        ListStringMap listAppNode,
                        ErrorInfo& e,
                        const Ice::Current& current) override;
        bool deleteUser(std::string userID,
                        ErrorInfo& e,
                        const Ice::Current& current) override;
        bool isUserHasPower(std::string userID,
                            std::string powerID,
                            bool& hasPower,
                            ErrorInfo& e,
                            const Ice::Current& current) override;
        bool isCardBindUser(std::string cardID,
                            bool& isBind,
                            std::string& userID,
                            ErrorInfo& e,
                            const Ice::Current& current) override;
        bool changePassword(std::string userID,
                            std::string oldPassword,
                            std::string newPassword,
                            ErrorInfo& e,
                            const Ice::Current& current) override;
        bool resetPassword(std::string userID,
                           ErrorInfo& e,
                           const Ice::Current& current) override;
        bool loginByPassword(std::string clientID,
                             std::string userID,
                             std::string password,
                             int keepTime,
                             std::string& outClientID,
                             ErrorInfo& e,
                             const Ice::Current& current) override;
        bool loginByCard(std::string clientID,
                         std::string userID,
                         std::string authModeID,
                         std::string cardID,
                         int keepTime,
                         std::string& outClientID,
                         ErrorInfo& e,
                         const Ice::Current& current) override;
        bool loginByVerifyCode(std::string clientID,
                               std::string phoneNumber,
                               std::string code,
                               int keepTime,
                               std::string& outClientID,
                               ErrorInfo& e,
                               const Ice::Current& current) override;
        bool logout(std::string clientID,
                    std::string userID,
                    ErrorInfo& e,
                    const Ice::Current& current) override;
        bool sendVerifyCode(std::string phoneNumber,
                            std::string& seqNo,
                            ErrorInfo& e,
                            const Ice::Current& current) override;
        bool getAvaiableUser(std::string clientID,
                             std::string appNodeID,
                             std::string powerID,
                             ListStringMap& lstUser,
                             ErrorInfo& e,
                             const Ice::Current& current) override;
        bool verifyByPassword(std::string clientID,
                              std::string userID,
                              std::string password,
                              std::string appNodeID,
                              std::string powerID,
                              ErrorInfo& e,
                              const Ice::Current& current) override;
        bool verifyByPasswordNoClient(std::string userID,
                                      std::string password,
                                      std::string appNodeID,
                                      std::string powerID,
                                      ErrorInfo& e,
                                      const Ice::Current& current) override;
        bool verifyByCard(std::string clientID,
                          std::string userID,
                          std::string authModeID,
                          std::string cardID,
                          std::string appNodeID,
                          std::string powerID,
                          std::string& realUserID,
                          ErrorInfo& e,
                          const Ice::Current& current) override;
        bool verifyByCardNoClient(std::string userID,
                                  std::string authModeID,
                                  std::string cardID,
                                  std::string appNodeID,
                                  std::string powerID,
                                  std::string& realUserID,
                                  ErrorInfo& e,
                                  const Ice::Current& current) override;
        bool verifyByVerifyCode(std::string clientID,
                                std::string phoneNumber,
                                std::string code,
                                std::string appNodeID,
                                std::string powerID,
                                ErrorInfo& e,
                                const Ice::Current& current) override;
        void cancelAuth(std::string clientID,
                        const Ice::Current& current) override;
        bool loginByAuthDev(std::string clientID,
                            std::string userID,
                            std::string authModeID,
                            int keepTime,
                            ErrorInfo& e,
                            const Ice::Current& current) override;
        bool verifyByAuthDev(std::string clientID,
                             std::string userID,
                             std::string authModeID,
                             std::string appNodeID,
                             std::string powerID,
                             ErrorInfo& e,
                             const Ice::Current& current) override;
        bool verifyByAuthDevNoClient(std::string userID,
                                     std::string authModeID,
                                     std::string appNodeID,
                                     std::string powerID,
                                     ErrorInfo& e,
                                     const Ice::Current& current) override;
        bool updateUserFace(std::string userID,
                            std::string faceData,
                            ErrorInfo& e,
                            const Ice::Current& current) override;
        bool deleteUserFace(std::string userID,
                            ErrorInfo& e,
                            const Ice::Current& current) override;
        bool updateUserFinger(std::string userID,
                              int fingerNo,
                              std::string fingerData,
                              ErrorInfo& e,
                              const Ice::Current& current) override;
        bool deleteUserFinger(std::string userID,
                              int fingerNo,
                              ErrorInfo& e,
                              const Ice::Current& current) override;
        bool addUserCard(std::string userID,
                         std::string cardNo,
                         ErrorInfo& e,
                         const Ice::Current& current) override;
        bool deleteUserCard(std::string cardNo,
                            ErrorInfo& e,
                            const Ice::Current& current) override;
        bool sendRandomPassword(std::string userID,
                                ErrorInfo& e,
                                const Ice::Current& current) override;
    };
}


#endif // ZGSPUSERMANAGERI_H

#include "ZGSPUserManagerMng.h"

#include <QJsonArray>
#include <QJsonDocument>
#include <QThread>
#include "ZGProxyCommon.h"
#include "ZGDebugMng.h"
#include "ZGUtils.h"
#include "ZGJson.h"
#include "ZGRuntime.h"
#include "ZGSecure.h"
#include "zgerror/ZGSPUserManagerError.h"

#include <QJsonObject>
#include <QRandomGenerator>
#include <QUuid>
#include <QFile>

static ZGSPUserManagerMng* g_pInstance = nullptr;

ZGSPUserManagerMng* ZGSPUserManagerMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGSPUserManagerMng;
    return g_pInstance;
}

void ZGSPUserManagerMng::init()
{
    initEvents();
    initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    QThread::sleep(QRandomGenerator::global()->bounded(5, 10));
    while (!initRedisClient())
    {
        ZGLOG_ERROR("initRedisClient error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initClientDataset())
    {
        ZGLOG_ERROR("initClientDataset error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initMqttClient())
    {
        ZGLOG_ERROR("initMqttClient error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initSystemParam())
    {
        ZGLOG_ERROR("initLanguage error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initParam())
    {
        ZGLOG_ERROR("initParam error.");
        QThread::msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    ZGLOG_INFO(QString("init ZGSPUserManagerMng finished."));
    m_checkTimer.start(m_checkInterval * 1000);
}

bool ZGSPUserManagerMng::checkState()
{
    return m_initialized;
}

void ZGSPUserManagerMng::dispatchData(const std::string& tableName,
                                      const std::string& oper,
                                      const std::string& reason,
                                      const std::string& time,
                                      const ZG6000::ListRecord& listRecord)
{
    // 将sp_param_hrm_user_system表中的cfgVersion加1
    QString sql = "UPDATE sp_param_hrm_user_system SET cfgVersion = cfgVersion + 1";
    if (!ZGProxyCommon::execSql(sql.toStdString()))
        ZGLOG_ERROR(QStringLiteral("更新用户系统配置版本失败"));
}

bool ZGSPUserManagerMng::getUserInfo(const std::string& userID,
                                     ZG6000::StringMap& user,
                                     ZG6000::ListStringMap& listRole,
                                     ZG6000::ListStringMap& listCard,
                                     ZG6000::ListStringMap& listAuth,
                                     ZG6000::ListStringMap& listAppNode,
                                     ZG6000::ErrorInfo& e)
{
    std::string sql =
        "SELECT a.id, a.name, e.id AS companyID, e.name AS companyName, b.id AS organID, b.name AS organName, "
        "a.shortName, a.employeeNumber, c.id AS levelID, c.name AS levelName, "
        "d.id AS professionTypeID, d.name AS professionTypeName, a.mobileNumber, a.telephone, a.idCard, "
        "a.mailbox, a.photo FROM sp_param_hrm_user a LEFT JOIN sp_param_hrm_organ b ON a.organID = b.id "
        "LEFT JOIN sp_param_hrm_level c ON a.levelID = c.id LEFT JOIN sp_dict_profession_type d "
        "ON a.professionTypeID = d.id LEFT JOIN sp_param_hrm_company e ON a.companyID = e.id WHERE a.id = '" + userID +
        "' ORDER BY a.id";
    ZG6000::ListStringMap listUser;
    if (!ZGProxyCommon::execQuerySql(sql, listUser))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户'" + userID + u8"'基本信息失败";
        return false;
    }
    if (listUser.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
        e.errDetail = u8"用户'" + userID + u8"'不存在";
        return false;
    }
    user = std::move(listUser[0]);
    sql = "SELECT a.id, a.name FROM sp_param_hrm_role a LEFT JOIN sp_param_hrm_user_role b "
        "ON a.id = b.roleID WHERE b.userID = '" + userID + "' ORDER BY a.id";
    if (!ZGProxyCommon::execQuerySql(sql, listRole))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户'" + userID + u8"'角色信息失败";
        return false;
    }
    sql = "SELECT a.cardID AS id, b.name FROM sp_param_hrm_user_card a LEFT JOIN sp_param_hrm_card b "
        "ON a.cardID = b.id WHERE a.userID = '" + userID + "' ORDER BY id";
    if (!ZGProxyCommon::execQuerySql(sql, listCard))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户'" + userID + u8"'卡号信息失败";
        return false;
    }
    sql = "SELECT a.id, a.name FROM sp_dict_auth_mode a LEFT JOIN "
        "sp_param_hrm_user_auth b ON a.id = b.authModeID "
        "WHERE b.userID = '" + userID + "' ORDER BY a.id";
    if (!ZGProxyCommon::execQuerySql(sql, listAuth))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户'" + userID + u8"'授权信息失败";
        return false;
    }
    sql = "SELECT a.id, a.name FROM sp_param_appnode a LEFT JOIN "
        "sp_param_hrm_user_appnode b ON a.id = b.appnodeID "
        "WHERE b.userID = '" + userID + "' ORDER BY a.id";
    if (!ZGProxyCommon::execQuerySql(sql, listAppNode))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户'" + userID + u8"'应用节点信息失败";
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::getUserFingers(const std::string& userID,
                                        ZG6000::ListStringMap& listFinger,
                                        ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT fingerNo, fingerData FROM sp_param_hrm_user_finger WHERE userID = '%1'").arg(
        userID.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listFinger))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户'" + userID + u8"'指纹信息失败";
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::getUserFace(const std::string& userID,
                                     std::string& faceData,
                                     ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT photoContentID FROM sp_param_hrm_user WHERE id = '%1'").arg(userID.c_str());
    std::string fileContentID;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), fileContentID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户'" + userID + u8"'照片内容ID失败";
        return false;
    }
    if (fileContentID.empty())
        faceData = "";
    else
    {
        sql = QString("SELECT content FROM sp_param_file_content WHERE id = '%1'").arg(fileContentID.c_str());
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), faceData))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = u8"获取用户'" + userID + u8"'照片失败";
            return false;
        }
    }
    return true;
}

void ZGSPUserManagerMng::rebuildUserAssocSql(const QString& userID,
                                             const QJsonObject& object,
                                             ZG6000::StringList& listSql)
{
    QString sql;
    if (object.contains("role"))
    {
        const auto& roleArray = object["role"].toArray();
        sql = QString("DELETE FROM sp_param_hrm_user_role WHERE userID = '%1';").arg(userID);
        listSql.emplace_back(sql.toStdString());
        for (auto roleRef : roleArray)
        {
            const auto& roleObj = roleRef.toObject();
            const auto& id = QUuid::createUuid().toString(QUuid::Id128);
            sql = QString("INSERT INTO sp_param_hrm_user_role (id, userID, roleID) "
                "VALUES ('%1', '%2', '%3');").arg(id).arg(userID).arg(roleObj["id"].toString());
            listSql.emplace_back(sql.toStdString());
        }
    }
    if (object.contains("card"))
    {
        const auto& cardArray = object["card"].toArray();
        sql = QString("DELETE FROM sp_param_hrm_user_card WHERE userID = '%1';").arg(userID);
        listSql.emplace_back(sql.toStdString());
        for (auto cardRef : cardArray)
        {
            const auto& cardObj = cardRef.toObject();
            const auto& cardID = cardObj["id"].toString();
            sql = QString("INSERT IGNORE sp_param_hrm_card (id) VALUES ('%1');")
                .arg(cardID);
            listSql.emplace_back(sql.toStdString());
            sql = QString("DELETE FROM sp_param_hrm_user_card WHERE cardID = '%1';").arg(cardID);
            listSql.emplace_back(sql.toStdString());
            const auto& id = QUuid::createUuid().toString(QUuid::Id128);
            sql = QString("INSERT INTO sp_param_hrm_user_card (id, userID, cardID) "
                "VALUES ('%1', '%2', '%3');").arg(id).arg(userID).arg(cardID);
            listSql.emplace_back(sql.toStdString());
        }
    }
    if (object.contains("auth"))
    {
        const auto& authArray = object["auth"].toArray();
        sql = QString("DELETE FROM sp_param_hrm_user_auth WHERE userID = '%1';").arg(userID);
        listSql.emplace_back(sql.toStdString());
        for (auto authRef : authArray)
        {
            const auto& authObj = authRef.toObject();
            const auto& id = QUuid::createUuid().toString(QUuid::Id128);
            sql = QString("INSERT INTO sp_param_hrm_user_auth (id, userID, authModeID) "
                "VALUES ('%1', '%2', '%3');").arg(id).arg(userID).arg(authObj["id"].toString());
            listSql.emplace_back(sql.toStdString());
        }
    }
    if (object.contains("appNode"))
    {
        const auto& appNodeArray = object["appNode"].toArray();
        sql = QString("DELETE FROM sp_param_hrm_user_appnode WHERE userID = '%1';").arg(userID);
        listSql.emplace_back(sql.toStdString());
        for (auto appNodeRef : appNodeArray)
        {
            const auto& appNodeObj = appNodeRef.toObject();
            const auto& id = QUuid::createUuid().toString(QUuid::Id128);
            sql = QString("INSERT INTO sp_param_hrm_user_appnode (id, userID, appnodeID) "
                "VALUES ('%1', '%2', '%3');").arg(id).arg(userID).arg(appNodeObj["id"].toString());
            listSql.emplace_back(sql.toStdString());
        }
    }
}

bool ZGSPUserManagerMng::rebuildUserAssocSql(const std::string& userID,
                                             const ZG6000::ListStringMap& listRole,
                                             const ZG6000::ListStringMap& listAuth,
                                             const ZG6000::ListStringMap& listAppNode,
                                             ZG6000::StringList& listSql)
{
    QString sql;
    sql = QString("DELETE FROM sp_param_hrm_user_role WHERE userID = '%1';").arg(userID.c_str());
    listSql.emplace_back(sql.toStdString());
    for (const auto& role : listRole)
    {
        const auto& roleID = ZGUtils::get(role, "id", "");
        if (roleID.empty())
            continue;
        const auto& id = QUuid::createUuid().toString(QUuid::Id128);
        sql = QString("INSERT INTO sp_param_hrm_user_role (id, userID, roleID) "
            "VALUES ('%1', '%2', '%3');").arg(id).arg(userID.c_str()).arg(roleID.c_str());
        listSql.emplace_back(sql.toStdString());
    }
    sql = QString("DELETE FROM sp_param_hrm_user_auth WHERE userID = '%1';").arg(userID.c_str());
    listSql.emplace_back(sql.toStdString());
    ZG6000::StringList listAuthID;
    if (!ZGProxyCommon::createUUID(static_cast<int>(listAuth.size()), listAuthID))
    {
        ZGLOG_ERROR(QStringLiteral("创建用户授权ID失败"));
        return false;
    }
    for (size_t i = 0; i < listAuth.size(); ++i)
    {
        const auto& authID = ZGUtils::get(listAuth[i], "id", "");
        if (authID.empty())
            continue;
        const auto& id = listAuthID[i];
        sql = QString("INSERT INTO sp_param_hrm_user_auth (id, userID, authModeID) "
            "VALUES ('%1', '%2', '%3');").arg(id.c_str()).arg(userID.c_str()).arg(authID.c_str());
        listSql.emplace_back(sql.toStdString());
    }
    sql = QString("DELETE FROM sp_param_hrm_user_appnode WHERE userID = '%1';").arg(userID.c_str());
    listSql.emplace_back(sql.toStdString());
    ZG6000::StringList listAppNodeID;
    if (!ZGProxyCommon::createUUID(static_cast<int>(listAppNode.size()), listAppNodeID))
    {
        ZGLOG_ERROR(QStringLiteral("创建用户应用节点ID失败"));
        return false;
    }
    for (size_t i = 0; i < listAppNode.size(); ++i)
    {
        const auto& appNodeID = ZGUtils::get(listAppNode[i], "id", "");
        if (appNodeID.empty())
            continue;
        const auto& id = listAppNodeID[i];
        sql = QString("INSERT INTO sp_param_hrm_user_appnode (id, userID, appnodeID) "
            "VALUES ('%1', '%2', '%3');").arg(id.c_str()).arg(userID.c_str()).arg(appNodeID.c_str());
        listSql.emplace_back(sql.toStdString());
    }
    return true;
}

/**
 * 获得应用节点下所有具备指定权限的用户
 * @param appNodeID 应用节点ID
 * @param powerID 权限ID
 * @param lstUser 用户列表
 * @param e 错误信息
 * @return 执行成功返回true，失败返回false
 */
bool ZGSPUserManagerMng::getAppNodeAvaiableUser(const std::string& appNodeID,
                                                std::string powerID,
                                                ZG6000::ListStringMap& lstUser,
                                                ZG6000::ErrorInfo& e)
{
    QString sql;
    if (appNodeID.empty() && powerID.empty())
        sql = QString("SELECT id FROM sp_param_hrm_user");
    else
    {
        sql = QString("SELECT a.id FROM sp_param_hrm_user a ");
        if (!powerID.empty())
            sql += QString("LEFT JOIN sp_param_hrm_user_role b ON a.id = b.userID "
                "LEFT JOIN sp_param_hrm_role_power c ON b.roleID = c.roleID ");
        if (!appNodeID.empty())
            sql += QString("LEFT JOIN sp_param_hrm_user_appnode d ON a.id = d.userID ");
        sql += "WHERE ";
        ZG6000::StringList listCondition;
        if (!powerID.empty())
            listCondition.push_back(QString("c.powerID = '%1'").arg(powerID.c_str()).toStdString());
        if (!appNodeID.empty())
            listCondition.push_back(QString("d.appnodeID = '%1'").arg(appNodeID.c_str()).toStdString());
        const auto& conditions = ZGUtils::join(listCondition, " AND ");
        sql += conditions.c_str() + QString(" ORDER BY a.id");
    }
    ZG6000::StringList listUserID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取用户ID失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listUserID.empty())
        return true;
    const auto& userIDs = ZGUtils::join(listUserID, ",", "'", "'");
    sql = QString("SELECT a.id, a.name, a.organID, b.name AS organName FROM sp_param_hrm_user a "
        "LEFT JOIN sp_param_hrm_organ b ON a.organID = b.id "
        "WHERE a.id IN (%1) ORDER BY a.id").arg(userIDs.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), lstUser))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取用户信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::sendSMSMessage(const std::string& mobileNumber,
                                        const std::string& message,
                                        ZG6000::ErrorInfo& e)
{
    ZG6000::StringMap shortMessage;
    shortMessage["content"] = message;
    shortMessage["phoneNumber"] = mobileNumber;
    QDateTime dt = QDateTime::currentDateTime();
    shortMessage["timestamp"] = dt.toString("yyyy-MM-dd hh:mm:ss").toStdString();
    const auto& json = ZGJson::convertToJson(shortMessage);
    long long size;
    std::string errMsg;
    if (!m_redisRtQueue->rpush("ZG_Q_SHORT_MESSAGE", json, size, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = errMsg;
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::generateHashedPassword(const QString& originPassword,
                                                QString& hashedPassword,
                                                QString& salt,
                                                ZG6000::ErrorInfo& e)
{
    // 使用生成uuid的前8位字符作为盐值
    salt = QUuid::createUuid().toString().left(8);
    QByteArray saltArray = salt.toLatin1();
    QString errMsg;
    if (!ZGSecure::pbkdf2HMACSHA256(originPassword, saltArray, 10000, hashedPassword, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("生成密码哈希值失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::sendRandomPassword(std::string userID,
                                            ZG6000::ErrorInfo& e)
{
    ZG6000::StringList listState;
    if (!ZGProxyCommon::mgetDataByField("mp_param_device", m_listSMSDeviceID, "rtState", listState))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取短信设备通信状态失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (std::all_of(listState.begin(), listState.end(), [](const std::string& state) { return state != "2"; }))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("短信设备通信状态中断，生成默认密码").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    QString sql = QString("SELECT mobileNumber FROM sp_param_hrm_user WHERE id = '%1'").arg(userID.c_str());
    std::string phoneNumber;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), phoneNumber))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取用户手机号失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (phoneNumber.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
        e.errDetail = QStringLiteral("用户手机号为空").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    // 生成随机密码，保存到用户表中
    const auto& randomPassword = generatePassword(m_passwordOptions);
    ZGLOG_TRACE(randomPassword.c_str());
    QString hashedPassword, salt;
    if (!generateHashedPassword(randomPassword.c_str(), hashedPassword, salt, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    sql = QString("UPDATE sp_param_hrm_user SET password = '%1', salt = '%2' WHERE id = '%3'")
          .arg(hashedPassword).arg(salt).arg(userID.c_str());
    if (!ZGProxyCommon::execSql(sql.toStdString()))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("更新用户密码失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    // 获取子系统名称
    sql = QString("SELECT name FROM sp_param_subsystem");
    std::string subsystemName;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), subsystemName))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取子系统名称失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    QString content = QStringLiteral("【香港地铁】您在") + QString::fromStdString(subsystemName) +
        QStringLiteral("中的密码已初始化为：") + QString::fromStdString(randomPassword) +
        QStringLiteral("，请妥善保管。");
    return sendSMSMessage(phoneNumber, content.toStdString(), e);
}

bool ZGSPUserManagerMng::addUser(const ZG6000::StringMap& user,
                                 const ZG6000::ListStringMap& listRole,
                                 const ZG6000::ListStringMap& listAuth,
                                 const ZG6000::ListStringMap& listAppNode,
                                 ZG6000::ErrorInfo& e)
{
    if (!verifyUserUnique(user, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringList listSql;
    auto newUser = user;
    QString password = "12345678";
    QString hashedPassword, salt;
    if (!generateHashedPassword(password, hashedPassword, salt, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    newUser["password"] = hashedPassword.toStdString();
    newUser["salt"] = salt.toStdString();
    listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_user", newUser));
    try
    {
        const auto& userID = ZGUtils::get(user, "id");
        if (!rebuildUserAssocSql(userID, listRole, listAuth, listAppNode, listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("创建用户信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("添加用户失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 从设备表中查询是否有SMS设备，如果有则发送短信
        if (!m_listSMSDeviceID.empty())
        {
            // 发送短信
            if (!sendRandomPassword(userID, e))
            {
                ZGLOG_ERROR(e);
            }
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("用户未指定ID").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPUserManagerMng::updateUser(const ZG6000::StringMap& user,
                                    const ZG6000::ListStringMap& listRole,
                                    const ZG6000::ListStringMap& listAuth,
                                    const ZG6000::ListStringMap& listAppNode,
                                    ZG6000::ErrorInfo& e)
{
    if (!verifyUserUnique(user, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringList listSql;
    listSql.push_back(ZGUtils::generateUpdateSql("sp_param_hrm_user", user));
    try
    {
        const auto& userID = ZGUtils::get(user, "id");
        if (!rebuildUserAssocSql(userID, listRole, listAuth, listAppNode, listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("更新用户信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新用户失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("用户未指定ID").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPUserManagerMng::deleteUser(const std::string& userID,
                                    ZG6000::ErrorInfo& e)
{
    std::string fileContentID;
    QString sql = QString("SELECT photoContentID FROM sp_param_hrm_user WHERE id = '%1'").arg(userID.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), fileContentID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户照片内容ID失败";
        return false;
    }
    ZG6000::StringList listSql;
    if (!fileContentID.empty())
    {
        sql = QString("DELETE FROM sp_param_file_content WHERE id = '%1'").arg(fileContentID.c_str());
        listSql.emplace_back(sql.toStdString());
    }
    sql = QString("DELETE FROM sp_param_hrm_user WHERE id = '%1';").arg(userID.c_str());
    listSql.emplace_back(sql.toStdString());
    sql = QString("DELETE FROM sp_param_hrm_user_role WHERE userID = '%1';").arg(userID.c_str());
    listSql.emplace_back(sql.toStdString());
    sql = QString("DELETE FROM sp_param_hrm_card WHERE id IN (SELECT cardID FROM sp_param_hrm_user_card WHERE userID = '%1')")
        .arg(userID.c_str());
    listSql.emplace_back(sql.toStdString());
    sql = QString("DELETE FROM sp_param_hrm_user_card WHERE userID = '%1';").arg(userID.c_str());
    listSql.emplace_back(sql.toStdString());
    sql = QString("DELETE FROM sp_param_hrm_user_auth WHERE userID = '%1';").arg(userID.c_str());
    listSql.emplace_back(sql.toStdString());
    sql = QString("DELETE FROM sp_param_hrm_user_appnode WHERE userID = '%1';").arg(userID.c_str());
    listSql.emplace_back(sql.toStdString());
    sql = QString("DELETE FROM sp_param_hrm_user_finger WHERE userID = '%1';").arg(userID.c_str());
    listSql.emplace_back(sql.toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"删除用户失败";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

/**
 * 验证用户是否具备指定的权限
 * @param userID 用户ID
 * @param powerID 权限ID
 * @param hasPower 是否具备权限
 * @param e 错误信息
 * @return 执行成功返回true，否则返回false
 */
bool ZGSPUserManagerMng::isUserHasPower(const std::string& userID,
                                        const std::string& powerID,
                                        bool& hasPower,
                                        ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT powerID FROM sp_param_hrm_role_power WHERE roleID in "
        "(SELECT roleID FROM sp_param_hrm_user_role WHERE userID = '%1')").arg(userID.c_str());
    ZG6000::StringList listPowerID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listPowerID))
    {
        ZGLOG_ERROR(QStringLiteral("获取用户权限失败"));
        return false;
    }
    hasPower = std::find(listPowerID.begin(), listPowerID.end(), powerID) != listPowerID.end();
    return true;
}

bool ZGSPUserManagerMng::isCardBindUser(const std::string& cardID,
                                        bool& isBind,
                                        std::string& userID,
                                        ZG6000::ErrorInfo& e)
{
    std::string sql = "SELECT userID FROM sp_param_hrm_user_card WHERE cardID = '" + cardID + "'";
    ZG6000::StringList listUser;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listUser))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取卡'" + cardID + u8"'关联用户失败";
        return false;
    }
    if (listUser.empty())
        isBind = false;
    else
    {
        isBind = true;
        userID = listUser[0];
    }
    return true;
}

bool ZGSPUserManagerMng::changePassword(const std::string& userID,
                                        const std::string& oldPassword,
                                        const std::string& newPassword,
                                        ZG6000::ErrorInfo& e)
{
    auto powerProxy = ZGProxyMng::instance()->getProxySPPowerVerify();
    if (powerProxy == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = u8"获取权限验证代理失败";
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        if (!powerProxy->verifyByPassword("", userID, oldPassword, "", "", e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
    }
    catch (const Ice::Exception& ie)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = ie.what();
        ZGLOG_ERROR(e);
        return false;
    }
    auto validateResult = validatePassword(newPassword, m_passwordOptions);
    if (!validateResult.isValid)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
        ZG6000::StringList listError;
        for (auto&& error : validateResult.errors)
        {
            listError.emplace_back(std::move(error));
        }
        const auto& errors = ZGUtils::join(listError, u8"；");
        e.errDetail = errors;
        ZGLOG_ERROR(e);
        return false;
    }
    QString hashedPassword, salt;
    if (!generateHashedPassword(newPassword.c_str(), hashedPassword, salt, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    QString sql = QString("UPDATE sp_param_hrm_user SET password = '%1', salt = '%2' WHERE id = '%3'")
                  .arg(hashedPassword).arg(salt).arg(userID.c_str());
    if (!ZGProxyCommon::execSql(sql.toStdString()))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"更改用户密码失败";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::resetPassword(const std::string& userID,
                                       ZG6000::ErrorInfo& e)
{
    QString password = "12345678";
    QString hashedPassword, salt;
    if (!generateHashedPassword(password, hashedPassword, salt, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    ZGLOG_TRACE(QString("password: %1, salt: %2, hashedPassword: %3").arg(password).arg(salt).arg(hashedPassword));
    QString sql = QString("UPDATE sp_param_hrm_user set password = '%1', salt = '%2', rtErrorNum = '0' WHERE id = '%3'")
                  .arg(hashedPassword).arg(salt).arg(userID.c_str());
    if (!ZGProxyCommon::execSql(sql.toStdString()))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"更改用户密码失败";
        ZGLOG_ERROR(e);
        return false;
    }
    // 从设备表中查询是否有SMS设备，如果有则发送短信
    if (!m_listSMSDeviceID.empty())
    {
        // 发送短信
        if (!sendRandomPassword(userID, e))
        {
            ZGLOG_ERROR(e);
        }
    }
    return true;
}

bool ZGSPUserManagerMng::verifyByPassword(const std::string& userID,
                                          const std::string& password,
                                          ZG6000::ErrorInfo& e)
{
    std::string fieldValue;
    QString sql = QString("SELECT password FROM sp_param_hrm_user WHERE id = '%1'").arg(userID.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), fieldValue))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户密码失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (fieldValue != password)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
        e.errDetail = u8"验证用户密码失败";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::verifyByCard(const std::string& userID,
                                      const std::string& cardID,
                                      std::string& realUserID,
                                      ZG6000::ErrorInfo& e)
{
    realUserID = userID;
    QString newCardID = cardID.c_str();
    newCardID = newCardID.toUpper();
    if (!userID.empty())
    {
        std::string sql = "SELECT cardID FROM sp_param_hrm_user_card WHERE userID = '" + userID + "'";
        ZG6000::StringList listResult;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = u8"获取用户卡失败";
            ZGLOG_ERROR(e);
            return false;
        }
        auto it = std::find(listResult.begin(), listResult.end(), newCardID.toStdString());
        if (it == listResult.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
            e.errDetail = u8"用户卡验证失败";
            ZGLOG_ERROR(e);
            return false;
        }
    }
    else
    {
        std::string sql = "SELECT userID FROM sp_param_hrm_user_card WHERE cardID = '" + newCardID.toStdString() + "'";
        ZG6000::StringList listResult;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = u8"获取卡" + newCardID.toStdString() + u8"关联用户失败";
            ZGLOG_ERROR(QString("Get card user error, card id = %1").arg(newCardID));
            return false;
        }
        if (listResult.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
            e.errDetail = u8"卡号" + newCardID.toStdString() + u8"未与用户绑定";
            ZGLOG_ERROR(e);
            return false;
        }
        realUserID = listResult[0];
    }
    return true;
}

bool ZGSPUserManagerMng::verifyUserPower(const std::string& userID,
                                         const std::string& powerID,
                                         ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT DISTINCT a.powerID FROM sp_param_hrm_role_power a "
        "LEFT JOIN sp_param_hrm_user_role b ON a.roleID = b.roleID "
        "WHERE b.userID = '%1' ORDER BY a.powerID").arg(userID.c_str());
    ZG6000::StringList listPowerID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listPowerID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取用户权限失败").toStdString();
        ZGLOG_ERROR(QStringLiteral("获取用户'%1'权限失败").arg(userID.c_str()));
        return false;
    }
    if (std::find(listPowerID.begin(), listPowerID.end(), powerID) == listPowerID.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
        e.errDetail = QStringLiteral("用户没有指定的权限").toStdString();
        ZGLOG_ERROR(QStringLiteral("用户%1没有指定的权限%2").arg(userID.c_str()).arg(powerID.c_str()));
        return false;
    }
    return true;
}

/**
 * 使用密码登录
 * @param clientID 客户端ID
 * @param userID 用户ID
 * @param password 密码
 * @param e 错误信息
 * @return 执行成功返回true，否则返回false
 */
bool ZGSPUserManagerMng::loginByPassword(const std::string& clientID,
                                         const std::string& userID,
                                         const std::string& password,
                                         int keepTime,
                                         std::string& outClientID,
                                         ZG6000::ErrorInfo& e)
{
    // 超级用户登录
    if (isSuperUser(userID, password))
    {
        std::string sql = "SELECT id FROM sp_param_client WHERE id = '" + clientID + "'";
        ZG6000::StringList listID;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listID))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = u8"获取客户端'" + clientID + u8"'信息失败";
            ZGLOG_ERROR(e);
            return false;
        }
        if (listID.empty())
        {
            if (!createActiveClient(clientID, userID, keepTime, e))
                return false;
        }
        QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
        std::string cookie;
        if (!updateClientLogin(clientID, userID, currentTime.toStdString(), cookie, keepTime, e))
            return false;
        return true;
    }
    // 普通用户登录
    return doVerifyNormalLogin(clientID, userID, "ZG_AM_PASSWORD", e, [&]()-> bool
    {
        QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
        if (!verifyByPassword(userID, password, e))
            return false;
        std::string cookie;
        return updateClientLogin(clientID, userID, currentTime.toStdString(), cookie, keepTime, e);
    });
}

bool ZGSPUserManagerMng::loginByCard(const std::string& clientID,
                                     const std::string& userID,
                                     const std::string& authModeID,
                                     const std::string& cardID,
                                     int keepTime,
                                     std::string& outClientID,
                                     ZG6000::ErrorInfo& e)
{
    if (authModeID != "ZG_AM_USB_CARD" && authModeID != "ZG_AM_COMM_CARD")
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("无效的授权卡类型'%1'").arg(authModeID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return doVerifyNormalLogin(clientID, userID, authModeID, e, [&]()-> bool
    {
        std::string realUserID;
        QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
        if (!verifyByCard(userID, cardID, realUserID, e))
            return false;
        std::string cookie;
        return updateClientLogin(clientID, realUserID, currentTime.toStdString(), cookie, keepTime, e);
    });
}

bool ZGSPUserManagerMng::sendVerifyCode(const std::string& phoneNumber,
                                        std::string& seqNo,
                                        ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPUserManagerMng::getUserByPhone(const std::string& phoneNumber,
                                        ZG6000::StringMap& mapResult,
                                        ZG6000::ErrorInfo& e)
{
    std::string sql = "SELECT id, rtVerifyCode, rtVerifyTime FROM sp_param_hrm_user WHERE mobileNumber = '" +
        phoneNumber + "'";
    ZG6000::ListStringMap listMapResult;
    if (!ZGProxyCommon::execQuerySql(sql, listMapResult))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取手机号" + phoneNumber + u8"关联用户失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (listMapResult.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
        e.errDetail = u8"手机号" + phoneNumber + u8"未与用户绑定";
        ZGLOG_ERROR(QString("Phone number %1 is not binded to user.").arg(phoneNumber.c_str()));
        return false;
    }
    mapResult = listMapResult.at(0);
    return true;
}

bool ZGSPUserManagerMng::registerAuthRequest(const std::string& clientID,
                                             const std::string& authModeID,
                                             const std::string& userID,
                                             bool isLogin,
                                             int keepTime,
                                             ZG6000::ErrorInfo& e)
{
    std::unique_lock lock(m_mutex);
    auto pair = m_mapClientAuthParam.find(clientID);
    if (pair == m_mapClientAuthParam.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端" + clientID + u8"没有关联的认证设备或数据集";
        ZGLOG_ERROR(e);
        return false;
    }
    pair->second.authUserID = userID;
    pair->second.authModeID = authModeID;
    pair->second.authRequestTime = QDateTime::currentDateTime();
    pair->second.isLogin = isLogin;
    pair->second.keepTime = keepTime;
    return true;
}

void ZGSPUserManagerMng::clearAuthRequest(const std::string& clientID)
{
    std::unique_lock lock(m_mutex);
    auto pair = m_mapClientAuthParam.find(clientID);
    if (pair == m_mapClientAuthParam.end())
        return;
    pair->second.authModeID = "";
    pair->second.authUserID = "";
    pair->second.isLogin = false;
}

void ZGSPUserManagerMng::getRecordValue(const ZG6000::MapField& record,
                                        ZG6000::StringMap& mapValue)
{
    const auto& id = ZGUtils::get(record, "id");
    const auto& pair = record.find("rtNewValue");
    if (pair == record.end())
        return;
    if (pair->second.newValue.empty())
        return;
    std::string dataModelID;
    if (!ZGProxyCommon::getDataByField("mp_param_dataset_text",
        id.newValue, "dataModelID", dataModelID))
    {
        ZGLOG_ERROR(QString("Get text %1 dataModel error.").arg(id.newValue.c_str()));
        return;
    }
    std::string channelAddr;
    if (!ZGProxyCommon::getDataByField("mp_param_model_text", dataModelID,
        "channelAddr", channelAddr))
    {
        ZGLOG_ERROR(QString("Get text model %1 channel addr error.").arg(dataModelID.c_str()));
        return;
    }
    mapValue.insert(std::make_pair(channelAddr, pair->second.newValue));
}

bool ZGSPUserManagerMng::publishAuthResult(const std::string& clientID,
                                           const std::string& userID,
                                           const std::string& authModeID,
                                           const std::string& result,
                                           const std::string& time)
{
    ZG6000::StringMap mapResult;
    mapResult.insert(std::make_pair("userID", userID));
    mapResult.insert(std::make_pair("authModeID", authModeID));
    mapResult.insert(std::make_pair("result", "true"));
    mapResult.insert(std::make_pair("verifyTime", time));
    std::string json = ZGJson::convertToJson(mapResult);
    if (!m_mqttMessage->publish(clientID.c_str(), json.c_str()))
    {
        ZGLOG_ERROR(QString("publish message error: %1").arg(json.c_str()));
        return false;
    }
    return true;
}

void ZGSPUserManagerMng::checkLoginStatus(const QDateTime& dateTime)
{
    std::string sql = "SELECT id FROM sp_param_client";
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listID))
    {
        ZGLOG_ERROR(u8"获取客户端ID失败");
        return;
    }
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::mgetDataByFields("sp_param_client", listID,
        {"id", "rtIsActivate", "rtState", "rtLoginUserID", "rtLoginTime", "rtKeepTime"},
        listRecord))
    {
        ZGLOG_ERROR(u8"获取客户端实时参数失败");
        return;
    }
    try
    {
        ZG6000::StringList listUpdateID;
        ZG6000::ListStringMap listUpdateRecord;
        for (const auto& record : listRecord)
        {
            const auto& id = ZGUtils::get(record, "id");
            const auto& rtIsActive = ZGUtils::get(record, "rtIsActivate");
            const auto& rtState = ZGUtils::get(record, "rtState");
            const auto& rtLoginUserID = ZGUtils::get(record, "rtLoginUserID");
            const auto& rtLoginTime = ZGUtils::get(record, "rtLoginTime");
            const auto& rtKeepTime = ZGUtils::get(record, "rtKeepTime");
            if (!rtLoginUserID.empty())
            {
                if (rtIsActive != "1")
                {
                    ZGLOG_INFO(QStringLiteral("客户端'%1'未激活，清除关联用户'%2'").arg(id.c_str()));
                    ZG6000::ErrorInfo e;
                    if (!logout(id, rtLoginUserID, false, e))
                        ZGLOG_ERROR(e);
                    else
                    {
                        std::string userName;
                        if (rtLoginUserID == "root")
                            userName = u8"超级管理员";
                        else
                        {
                            sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + rtLoginUserID + "'";
                            if (!ZGProxyCommon::execQuerySqlField(sql, userName))
                                ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(rtLoginUserID.c_str()));
                        }
                        std::string clientName;
                        sql = "SELECT name FROM sp_param_client WHERE id = '" + id + "'";
                        if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
                            ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(id.c_str()));
                        // std::string eventInfo = u8"用户【" + userName + u8"】从客户端【" + clientName +
                        //     u8"】强制下线，原因：客户端未激活";
                        std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") +
                            u8"【" + userName + u8"】" + ZGUtils::languageString(m_firstLanguage, "fromClient") +
                            u8"【" + clientName + "】" + ZGUtils::languageString(m_firstLanguage, "offlineReason") +
                            u8"：" + ZGUtils::languageString(m_firstLanguage, "clientNotActivate");
                        std::string eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") +
                            u8"【" + userName + u8"】" + ZGUtils::languageString(m_secondLanguage, "fromClient") +
                            u8"【" + clientName + "】" + ZGUtils::languageString(m_secondLanguage, "offlineReason") +
                            u8"：" + ZGUtils::languageString(m_secondLanguage, "clientNotActivate");
                        publishUserEvent(id, eventInfo, eventInfoL2);
                        publishClientEvent(id, rtLoginUserID, userName, "logout");
                    }
                }
            }
            else if (rtState != "2")
            {
                QDateTime loginTime;
                ZGUtils::StringToDateTime(rtLoginTime.c_str(), loginTime, true);
                QDateTime currentTime = QDateTime::currentDateTime();
                if (loginTime.secsTo(currentTime) > 30)
                {
                    ZGLOG_INFO(QStringLiteral("客户端'%1'未在线，清除关联用户'%2'").arg(id.c_str()).arg(rtLoginUserID.c_str()));
                    ZG6000::ErrorInfo e;
                    if (!logout(id, rtLoginUserID, false, e))
                        ZGLOG_ERROR(e);
                    else
                    {
                        std::string userName;
                        if (rtLoginUserID == "root")
                            userName = u8"超级管理员";
                        else
                        {
                            sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + rtLoginUserID + "'";
                            if (!ZGProxyCommon::execQuerySqlField(sql, userName))
                                ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(rtLoginUserID.c_str()));
                        }
                        std::string clientName;
                        sql = "SELECT name FROM sp_param_client WHERE id = '" + id + "'";
                        if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
                            ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(id.c_str()));
                        // std::string eventInfo = u8"用户【" + userName + u8"】从客户端【" + clientName +
                        //     u8"】强制下线，原因：客户端离线";
                        std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") +
                            u8"【" + userName + u8"】" + ZGUtils::languageString(m_firstLanguage, "fromClient") +
                            u8"【" + clientName + "】" + ZGUtils::languageString(m_firstLanguage, "offlineReason") +
                            u8"：" + ZGUtils::languageString(m_firstLanguage, "clientOffline");
                        std::string eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") +
                            u8"【" + userName + u8"】" + ZGUtils::languageString(m_secondLanguage, "fromClient") +
                            u8"【" + clientName + "】" + ZGUtils::languageString(m_secondLanguage, "offlineReason") +
                            u8"：" + ZGUtils::languageString(m_secondLanguage, "clientOffline");
                        publishUserEvent(id, eventInfo, eventInfoL2);
                        publishClientEvent(id, rtLoginUserID, userName, "logout");
                    }
                }
            }
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGSPUserManagerMng::publishUserEvent(const std::string& clientID,
                                          const std::string& eventInfo,
                                          const std::string& eventInfoL2)
{
    auto eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
    if (eventProcessPrx == nullptr)
    {
        ZGLOG_ERROR(QStringLiteral("获取事件处理服务代理失败"));
        return;
    }
    try
    {
        auto onewayPrx = eventProcessPrx->ice_oneway();
        ZG6000::StringMap event;
        std::string appNodeID;
        ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtAppNodeID", appNodeID);
        event["appNodeID"] = appNodeID;
        event["eventTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        event["eventTypeID"] = "ZG_ET_SYSTEM";
        event["alarmLevelID"] = "ZG_AL_LEVEL0";
        event["eventInfo"] = eventInfo;
        event["eventInfoL2"] = eventInfoL2;
        onewayPrx->processEvent(event);
    }
    catch (const Ice::Exception& ex)
    {
        ZGLOG_WARN(ex.what());
    }
}

void ZGSPUserManagerMng::publishClientEvent(const std::string& clientID,
                                            const std::string& userID,
                                            const std::string& userName,
                                            const std::string& type)
{
    ZG6000::StringMap clientEvent;
    clientEvent["userID"] = userID;
    clientEvent["userName"] = userName;
    clientEvent["type"] = type;
    const auto& json = ZGJson::convertToJson(clientEvent);
    QString topic = QString("sp_param_client/%1/user").arg(clientID.c_str());
    m_mqttMessage->sendPublish(topic, json.c_str());
}

bool ZGSPUserManagerMng::logout(const std::string& clientID,
                                const std::string& userID,
                                bool publishEvent,
                                ZG6000::ErrorInfo& e)
{
    if (userID.empty())
        return true;
    std::string loginUserID;
    if (!isClientExists(clientID, e))
        return false;
    std::string sql = "SELECT rtLoginUserID FROM sp_param_client WHERE id = '" + clientID + "'";
    if (!ZGProxyCommon::execQuerySqlField(sql, loginUserID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
        e.errDetail = u8"获取客户端'" + clientID + u8"'登录用户失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (loginUserID != userID)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
        e.errDetail = u8"客户端" + clientID + u8"未与指定的用户" + userID + u8"绑定";
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringMap mapFieldValue;
    mapFieldValue["id"] = clientID;
    mapFieldValue["rtLoginTime"] = "";
    mapFieldValue["rtUserAuthID"] = "";
    mapFieldValue["rtUserStateID"] = "";
    mapFieldValue["rtLoginTime"] = "";
    mapFieldValue["rtCookieID"] = "";
    mapFieldValue["rtUpdateTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
    sql = ZGUtils::generateUpdateSql("sp_param_client", mapFieldValue);
    if (!ZGProxyCommon::execSql(sql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"更新客户端'" + clientID + u8"'登录信息失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (publishEvent)
    {
        std::string userName;
        std::string sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + userID + "'";
        if (!ZGProxyCommon::execQuerySqlField(sql, userName))
            ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(userID.c_str()));
        std::string clientName;
        sql = "SELECT name FROM sp_param_client WHERE id = '" + clientID + "'";
        if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
            ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(clientID.c_str()));
        // std::string eventInfo = u8"用户【" + userName + u8"】从客户端【" + clientName + u8"】登出";
        std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") +
            u8"【" + userName + u8"】" + ZGUtils::languageString(m_firstLanguage, "fromClient") +
            u8"【" + clientName + u8"】" + ZGUtils::languageString(m_firstLanguage, "logout");
        std::string eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") +
            u8"【" + userName + u8"】" + ZGUtils::languageString(m_secondLanguage, "fromClient") +
            u8"【" + clientName + u8"】" + ZGUtils::languageString(m_secondLanguage, "logout");
        publishUserEvent(clientID, eventInfo, eventInfoL2);
        publishClientEvent(clientID, userID, userName, "logout");
    }
    return true;
}

bool ZGSPUserManagerMng::verifyCode(const std::string& inputCode,
                                    const std::string& verifyCode,
                                    const std::string& time,
                                    ZG6000::ErrorInfo& e)
{
    if (verifyCode != inputCode)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
        e.errDetail = u8"验证码错误";
        ZGLOG_ERROR(e);
        return false;
    }
    QDateTime currentTime = QDateTime::currentDateTime();
    QDateTime codeTime = QDateTime::fromString(time.c_str(), "yyyy-MM-dd hh:mm:ss");
    if (codeTime.secsTo(currentTime) > 60)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
        e.errDetail = u8"验证码超时";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::verifyUserUnique(const ZG6000::StringMap& user,
                                          ZG6000::ErrorInfo& e)
{
    try
    {
        const auto& employeeNumber = ZGUtils::get(user, "employeeNumber");
        const auto& name = ZGUtils::get(user, "name");
        const auto& mobileNumber = ZGUtils::get(user, "mobileNumber");
        QString sql = QStringLiteral("SELECT id FROM sp_param_hrm_user WHERE employeeNumber = '%1'")
            .arg(employeeNumber.c_str());
        ZG6000::StringList listUserID;
        if (ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
        {
            if (listUserID.size() > 1)
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
                e.errDetail = QStringLiteral("用户工号已存在").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (listUserID.size() == 1)
            {
                const auto& userID = listUserID[0];
                if (userID != ZGUtils::get(user, "id"))
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
                    e.errDetail = QStringLiteral("用户工号已存在").toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
        }
        else
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("查询用户失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        sql = QStringLiteral("SELECT id FROM sp_param_hrm_user WHERE name = '%1'").arg(name.c_str());
        listUserID.clear();
        if (ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
        {
            if (listUserID.size() > 1)
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
                e.errDetail = QStringLiteral("用户名称已存在").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (listUserID.size() == 1)
            {
                const auto& userID = listUserID[0];
                if (userID != ZGUtils::get(user, "id"))
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
                    e.errDetail = QStringLiteral("用户名称已存在").toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
        }
        else
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("查询用户失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 验证手机号码是否唯一
        sql = QStringLiteral("SELECT id FROM sp_param_hrm_user WHERE mobileNumber = '%1'").arg(mobileNumber.c_str());
        listUserID.clear();
        if (ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
        {
            if (listUserID.size() > 1)
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
                e.errDetail = QStringLiteral("用户手机号码已存在").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (listUserID.size() == 1)
            {
                const auto& userID = listUserID[0];
                if (userID != ZGUtils::get(user, "id"))
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
                    e.errDetail = QStringLiteral("用户手机号码已存在").toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
        }
        else
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("查询用户失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("用户未指定工号或名称").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPUserManagerMng::isSuperUser(const std::string& userID,
                                     const std::string& password)
{
    return (userID == "root") && (password == "cdzgyfb");
}

void ZGSPUserManagerMng::cleanVerifyCode(const std::string& userID)
{
    ZG6000::StringMap mapFieldValue;
    mapFieldValue.insert(std::make_pair("rtVerifyCode", ""));
    mapFieldValue.insert(std::make_pair("rtVerifyTime", ""));
    if (!ZGProxyCommon::updateDataByID("sp_param_hrm_user", userID, mapFieldValue))
        ZGLOG_ERROR(QString("Clean user %1 verify code error.").arg(userID.c_str()));
}

bool ZGSPUserManagerMng::loginByVerifyCode(const std::string& clientID,
                                           const std::string& phoneNumber,
                                           const std::string& code,
                                           int keepTime,
                                           std::string& outClientID,
                                           ZG6000::ErrorInfo& e)
{
    if (code.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
        e.errDetail = u8"验证码为空";
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringMap mapResult;
    if (!getUserByPhone(phoneNumber, mapResult, e))
        return false;
    try
    {
        const auto& userID = ZGUtils::get(mapResult, "id");
        const auto& rtVerifyCode = ZGUtils::get(mapResult, "rtVerifyCode");
        const auto& rtVerifyTime = ZGUtils::get(mapResult, "rtVerifyTime");
        return doVerifyNormalLogin(clientID, userID, "ZG_AM_CODE", e, [&]()-> bool
        {
            QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
            if (!verifyCode(code, rtVerifyCode, rtVerifyTime, e))
                return false;
            cleanVerifyCode(userID);
            std::string cookie;
            return updateClientLogin(clientID, userID, currentTime.toStdString(), cookie, keepTime, e);
        });
    }
    catch (const std::exception& ie)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = ie.what();
        return false;
    }
}

bool ZGSPUserManagerMng::logout(const std::string& clientID,
                                const std::string& userID,
                                ZG6000::ErrorInfo& e)
{
    return logout(clientID, userID, true, e);
}

bool ZGSPUserManagerMng::getAvaiableUser(const std::string& clientID,
                                         const std::string& appNodeID,
                                         const std::string& powerID,
                                         ZG6000::ListStringMap& lstUser,
                                         ZG6000::ErrorInfo& e)
{
    //    if (!clientID.empty())
    //    {
    //        std::string rtAppNodeID;
    //        if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtAppNodeID", rtAppNodeID))
    //        {
    //            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
    //            e.errDetail = u8"获取客户端" + clientID + u8"应用节点失败";
    //            ZGLOG_ERROR(e);
    //            return false;
    //        }
    //        return getAppNodeAvaiableUser(rtAppNodeID, powerID, lstUser, e);
    //    }
    return getAppNodeAvaiableUser(appNodeID, powerID, lstUser, e);
}

bool ZGSPUserManagerMng::verifyByUser(const std::string& clientID,
                                      const std::string& userID,
                                      const std::string& powerID,
                                      ZG6000::ErrorInfo& e)
{
    return doVerifyNormalCheck(clientID, userID, "ZG_AM_PASSWORD", e, [&]()-> bool
    {
        if (!powerID.empty())
        {
            if (!verifyUserPower(userID, powerID, e))
                return false;
        }
        return true;
    });
}

/**
 * @brief   使用密码进行用户权限验证
 *
 * @param           clientID    客户端ID
 * @param           userID      用户ID
 * @param           password    密码
 * @param           powerID     权限ID
 * @param [in,out]  e           执行出错时的错误信息
 *
 * @return  执行成功返回true，失败返回false。
 */
bool ZGSPUserManagerMng::verifyByPassword(const std::string& clientID,
                                          const std::string& userID,
                                          const std::string& password,
                                          const std::string& appNodeID,
                                          const std::string& powerID,
                                          ZG6000::ErrorInfo& e)
{
    // 超级用户执行特殊验证流程
    if (isSuperUser(userID, password))
    {
        return doVerifySuperCheck(clientID, userID, password, e, [&]()-> bool { return true; });
    }
    // 普通用户执行通用验证流程
    return doVerifyNormalCheck(clientID, userID, "ZG_AM_PASSWORD", e, [&]()-> bool
    {
        // 验证用户密码是否正确
        if (!verifyByPassword(userID, password, e))
            return false;
        if (!checkUserAuthState(userID, "ZG_AM_PASSWORD", e))
            return false;
        if (!appNodeID.empty())
        {
            if (!checkUserAppNode(userID, appNodeID, e))
                return false;
        }
        // 如果权限ID不为空，则验证用户是否具有指定权限
        if (!powerID.empty())
        {
            // 验证用户是否具有指定权限
            if (!verifyUserPower(userID, powerID, e))
                return false;
        }
        return true;
    });
}

bool ZGSPUserManagerMng::verifyByPasswordNoClient(const std::string& userID,
                                                  const std::string& password,
                                                  const std::string& appNodeID,
                                                  const std::string& powerID,
                                                  ZG6000::ErrorInfo& e)
{
    if (isSuperUser(userID, password))
        return true;
    if (!checkUserAuthState(userID, "ZG_AM_PASSWORD", e))
        return false;
    if (!appNodeID.empty())
    {
        if (!checkUserAppNode(userID, appNodeID, e))
            return false;
    }
    if (!verifyByPassword(userID, password, e))
        return false;
    return verifyUserPower(userID, powerID, e);
}

bool ZGSPUserManagerMng::verifyByCard(const std::string& clientID,
                                      const std::string& userID,
                                      const std::string& authModeID,
                                      const std::string& cardID,
                                      const std::string& appNodeID,
                                      const std::string& powerID,
                                      std::string& realUserID,
                                      ZG6000::ErrorInfo& e)
{
    if (authModeID != "ZG_AM_USB_CARD" && authModeID != "ZG_AM_COMM_CARD")
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("无效的授权卡类型'%1'").arg(authModeID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return doVerifyNormalCheck(clientID, authModeID, e, [&]()-> bool
    {
        if (!verifyByCard(userID, cardID, realUserID, e))
            return false;
        if (!checkUserAuthState(realUserID, authModeID, e))
            return false;
        if (!appNodeID.empty())
        {
            if (!checkUserAppNode(realUserID, appNodeID, e))
                return false;
        }
        if (!powerID.empty())
        {
            if (!verifyUserPower(realUserID, powerID, e))
                return false;
        }
        return true;
    });
}

bool ZGSPUserManagerMng::verifyByCardNoClient(const std::string& userID,
                                              const std::string& authModeID,
                                              const std::string& cardID,
                                              const std::string& appNodeID,
                                              const std::string& powerID,
                                              std::string& realUserID,
                                              ZG6000::ErrorInfo& e)
{
    if (authModeID != "ZG_AM_USB_CARD" && authModeID != "ZG_AM_COMM_CARD")
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("无效的授权卡类型'%1'").arg(authModeID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!verifyByCard(userID, cardID, realUserID, e))
        return false;
    if (!checkUserAuthState(realUserID, authModeID, e))
        return false;
    if (!appNodeID.empty())
    {
        if (!checkUserAppNode(realUserID, appNodeID, e))
            return false;
    }
    if (!powerID.empty())
    {
        if (!verifyUserPower(realUserID, powerID, e))
            return false;
    }
    return true;
}

bool ZGSPUserManagerMng::verifyByVerifyCode(const std::string& clientID,
                                            const std::string& phoneNumber,
                                            const std::string& code,
                                            ZG6000::ErrorInfo& e)
{
    if (code.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_VERIFY);
        e.errDetail = u8"验证码为空";
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringMap mapResult;
    if (!getUserByPhone(phoneNumber, mapResult, e))
        return false;
    try
    {
        const auto& userID = ZGUtils::get(mapResult, "id");
        const auto& rtVerifyCode = ZGUtils::get(mapResult, "rtVerifyCode");
        const auto& rtVerifyTime = ZGUtils::get(mapResult, "rtVerifyTime");
        return doVerifyNormalCheck(clientID, userID, "ZG_AM_CODE", e, [&]()-> bool
        {
            if (!verifyCode(code, rtVerifyCode, rtVerifyTime, e))
                return false;
            cleanVerifyCode(userID);
            return true;
        });
    }
    catch (const std::exception& ie)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = ie.what();
        return false;
    }
}

void ZGSPUserManagerMng::cancelRequest(const std::string& clientID)
{
    clearAuthRequest(clientID);
}

bool ZGSPUserManagerMng::updateUserFace(const std::string& userID,
                                        std::string&& faceData,
                                        ZG6000::ErrorInfo& e)
{
    // 从用户表中获取photoContentID字段值
    std::string photoContentID;
    QString sql = QString("SELECT photoContentID FROM sp_param_hrm_user WHERE id = '%1'").arg(
        userID.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), photoContentID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取人员照片信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringList listSql;
    size_t faceHash = qHash(faceData.c_str());
    ZG6000::StringMap user{{"id", userID}, {"photoHash", std::to_string(faceHash)}};
    ZG6000::StringMap fileContent{{"content", std::move(faceData)}};
    // 如果photoContentID字段值为空，则插入新的照片数据
    if (photoContentID.empty())
    {
        if (!ZGProxyCommon::createUUID(photoContentID))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("生成人员照片ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        fileContent["id"] = photoContentID;
        listSql.push_back(ZGUtils::generateInsertSql("sp_param_file_content", fileContent));
    }
    else
    {
        fileContent["id"] = photoContentID;
        listSql.push_back(ZGUtils::generateUpdateSql("sp_param_file_content", fileContent));
    }
    user["photoContentID"] = photoContentID;
    listSql.push_back(ZGUtils::generateUpdateSql("sp_param_hrm_user", user));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("更新人员照片信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::deleteUserFace(const std::string& userID,
                                        ZG6000::ErrorInfo& e)
{
    // 从用户表中获取photoContentID字段值
    std::string photoContentID;
    QString sql = QString("SELECT photoContentID FROM sp_param_hrm_user WHERE id = '%1'").arg(
        userID.c_str());
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), photoContentID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取人员照片信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringList listSql;
    if (!photoContentID.empty())
    {
        listSql.push_back(
            QString("DELETE FROM sp_param_file_content WHERE id = '%1'").arg(photoContentID.c_str()).
                                                                         toStdString());
    }
    ZG6000::StringMap user{{"id", userID}, {"photoContentID", ""}, {"photoHash", ""}};
    listSql.push_back(ZGUtils::generateUpdateSql("sp_param_hrm_user", user));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("删除人员照片信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::updateUserFinger(const std::string& userID,
                                          int fingerNo,
                                          const std::string& fingerData,
                                          ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT id FROM sp_param_hrm_user_finger WHERE userID = '%1' AND fingerNo = %2")
                  .arg(userID.c_str()).arg(fingerNo);
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取人员'%1'指纹'%2'信息失败").arg(userID.c_str()).arg(fingerNo).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!listID.empty())
    {
        ZG6000::StringMap userFinger{{"id", listID[0]}, {"fingerData", fingerData}};
        if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql("sp_param_hrm_user_finger", userFinger)))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新人员'%1'指纹'%2'信息失败").arg(userID.c_str()).arg(fingerNo).
                                                               toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
    }
    else
    {
        std::string userFingerID;
        if (!ZGProxyCommon::createUUID(userFingerID))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("生成人员'%1'指纹'%2'ID失败").arg(userID.c_str()).arg(fingerNo).
                                                               toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZG6000::StringMap userFinger{
            {"id", userFingerID}, {"userID", userID}, {"fingerNo", std::to_string(fingerNo)},
            {"fingerData", fingerData}
        };
        if (!ZGProxyCommon::execSql(ZGUtils::generateInsertSql("sp_param_hrm_user_finger", userFinger)))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("增加人员'%1'指纹'%2'信息失败").arg(userID.c_str()).arg(fingerNo).
                                                               toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
    }
    return true;
}

bool ZGSPUserManagerMng::deleteUserFinger(const std::string& userID,
                                          int fingerNo,
                                          ZG6000::ErrorInfo& e)
{
    QString sql = QString("DELETE FROM sp_param_hrm_user_finger WHERE userID = '%1' AND fingerNo = %2")
                  .arg(userID.c_str()).arg(fingerNo);
    if (!ZGProxyCommon::execSql(sql.toStdString()))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("删除人员'%1'指纹'%2'信息失败").arg(userID.c_str()).arg(fingerNo).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::addUserCard(const std::string& userID,
                                     const std::string& cardNo,
                                     ZG6000::ErrorInfo& e)
{
    ZG6000::StringList listSql;
    QString newCardNo = cardNo.c_str();
    newCardNo = newCardNo.toUpper();
    QString sql = QString("SELECT userID FROM sp_param_hrm_user_card WHERE cardID = '%1'").arg(newCardNo);
    ZG6000::StringList listUserID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取卡'%1'关联人员失败").arg(newCardNo).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!listUserID.empty())
    {
        std::string userName;
        ZGProxyCommon::getDataByField("sp_param_hrm_user", listUserID[0], "name", userName);
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("卡'%1'已经绑定到人员'%2'").arg(newCardNo).arg(userName.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    sql = QString("DELETE FROM sp_param_hrm_card WHERE id = '%1';").arg(newCardNo);
    listSql.emplace_back(sql.toStdString());
    sql = QString("DELETE FROM sp_param_hrm_user_card WHERE cardID = '%1';").arg(newCardNo);
    listSql.emplace_back(sql.toStdString());
    sql = QString("INSERT INTO sp_param_hrm_card (id) VALUES ('%1');")
        .arg(newCardNo);
    listSql.emplace_back(sql.toStdString());
    const auto& id = QUuid::createUuid().toString(QUuid::Id128);
    sql = QString("INSERT INTO sp_param_hrm_user_card (id, userID, cardID) "
        "VALUES ('%1', '%2', '%3');").arg(id).arg(userID.c_str()).arg(newCardNo);
    listSql.emplace_back(sql.toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("增加人员卡'%1'信息失败").arg(newCardNo).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::deleteUserCard(const std::string& cardNo,
                                        ZG6000::ErrorInfo& e)
{
    ZG6000::StringList listSql;
    QString sql = QString("DELETE FROM sp_param_hrm_card WHERE id = '%1';").arg(cardNo.c_str());
    listSql.emplace_back(sql.toStdString());
    sql = QString("DELETE FROM sp_param_hrm_user_card WHERE cardID = '%1';").arg(cardNo.c_str());
    listSql.emplace_back(sql.toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("删除卡'%1'信息失败").arg(cardNo.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

void ZGSPUserManagerMng::initEvents()
{
    // connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPUserManagerMng::onCheckStatus);
}

void ZGSPUserManagerMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "update_interval", value, 5, 30, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_updateInterval = value;
}

bool ZGSPUserManagerMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::initRedisClient()
{
    QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
    listClientType << ZGRuntime::REDIS_RT_TOPIC << ZGRuntime::REDIS_RT_QUEUE;
    if (!ZGRuntime::instance()->initRedisClient(listClientType))
    {
        ZGLOG_ERROR("initRedisClient error.");
        return false;
    }
    m_redisRtQueue = ZGRuntime::instance()->getRedisClientRTQueue();
    if (m_redisRtQueue == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTQueue error.");
        return false;
    }
    // m_redisRtTopic = ZGRuntime::instance()->getRedisClientRTTopic();
    // if (m_redisRtTopic == nullptr)
    // {
    //     ZGLOG_ERROR("getRedisClientRTTopic error.");
    //     return false;
    // }
    // connect(m_redisRtTopic, &ZGRedisClient::receivedMessage, this, &ZGSPUserManagerMng::onReceivedMessage);
    // ZG6000::StringList listTopic;
    // for (const auto& pair : m_mapDatasetClient)
    // {
    //     listTopic.emplace_back(pair.first + "/text");
    // }
    // m_redisRtTopic->subscribe(listTopic);
    // m_redisRtTopic->consume();
    return true;
}

bool ZGSPUserManagerMng::initMqttClient()
{
    if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
    {
        ZGLOG_ERROR("initMqttClient error.");
        return false;
    }
    m_mqttMessage = ZGRuntime::instance()->getMqttClientMessage();
    if (m_mqttMessage == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage error.");
        return false;
    }
    m_mqttMessage->connectToHost();
    return true;
}

bool ZGSPUserManagerMng::initSystemParam()
{
    QString sql = QString("SELECT isEnableSafety, firstLanguageID, secondLanguageID FROM sp_param_system");
    ZG6000::StringMap mapLanguage;
    if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), mapLanguage))
    {
        ZGLOG_ERROR("获取系统语言参数失败");
        return false;
    }
    m_enableSafety = mapLanguage["isEnableSafety"] == "1";
    if (m_enableSafety)
    {
        m_passwordOptions.minLength = 8;
        m_passwordOptions.requireUppercase = true;
        m_passwordOptions.minUppercase = 1;
        m_passwordOptions.requireLowercase = true;
        m_passwordOptions.minLowercase = 1;
        m_passwordOptions.requireDigits = true;
        m_passwordOptions.minDigits = 1;
        m_passwordOptions.requireSpecial = true;
        m_passwordOptions.minSpecial = 1;
    }
    else
    {
        m_passwordOptions.minLength = 8;
        m_passwordOptions.requireUppercase = true;
        m_passwordOptions.minUppercase = 0;
        m_passwordOptions.requireLowercase = true;
        m_passwordOptions.minLowercase = 0;
        m_passwordOptions.requireDigits = true;
        m_passwordOptions.minDigits = 0;
        m_passwordOptions.requireSpecial = false;
        m_passwordOptions.minSpecial = 0;
    }
    m_firstLanguage = mapLanguage["firstLanguageID"];
    if (m_firstLanguage.empty())
        m_firstLanguage = "ZG_DL_CN";
    m_secondLanguage = mapLanguage["secondLanguageID"];
    if (!ZGUtils::initLanguage(m_serverName))
    {
        ZGLOG_ERROR(QStringLiteral("初始化语言配置失败"));
        return false;
    }
    ZGLOG_INFO(
        QString("first language: %1, second language: %2").arg(m_firstLanguage.c_str()).arg(m_secondLanguage
            .c_str()));
    return true;
}

bool ZGSPUserManagerMng::initParam()
{
    std::string sql = "SELECT id FROM mp_param_device WHERE isEnable = 1 AND typeID = 'ZG_DT_SMS'";
    if (!ZGProxyCommon::execQuerySqlCol(sql, m_listSMSDeviceID))
    {
        ZGLOG_ERROR(u8"获取短信设备ID失败");
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::loginByAuthDev(const std::string& clientID,
                                        const std::string& userID,
                                        const std::string& authModeID,
                                        int keepTime,
                                        ZG6000::ErrorInfo& e)
{
    return doVerifyNormalLogin(clientID, userID, authModeID, e, [&]()-> bool
    {
        QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
        std::string cookie;
        return updateClientLogin(clientID, userID, currentTime.toStdString(), cookie, keepTime, e);
    });
}

bool ZGSPUserManagerMng::verifyByAuthDev(const std::string& clientID,
                                         const std::string& userID,
                                         const std::string& authModeID,
                                         const std::string& appNodeID,
                                         const std::string& powerID,
                                         ZG6000::ErrorInfo& e)
{
    return doVerifyNormalCheck(clientID, userID, authModeID, e, [&]()-> bool
    {
        if (!appNodeID.empty())
        {
            if (!checkUserAppNode(userID, appNodeID, e))
                return false;
        }
        if (!powerID.empty())
        {
            if (!verifyUserPower(userID, powerID, e))
                return false;
        }
        return true;
    });
}

bool ZGSPUserManagerMng::verifyByAuthDevNoClient(const std::string& userID,
                                                 const std::string& authModeID,
                                                 const std::string& appNodeID,
                                                 const std::string& powerID,
                                                 ZG6000::ErrorInfo& e)
{
    if (!checkUserAuthState(userID, authModeID, e))
        return false;
    if (!appNodeID.empty())
    {
        if (!checkUserAppNode(userID, appNodeID, e))
            return false;
    }
    if (!powerID.empty())
    {
        if (!verifyUserPower(userID, powerID, e))
            return false;
    }
    return true;
}

bool ZGSPUserManagerMng::initClientDataset()
{
    std::string sql = "SELECT id, authDevID FROM sp_param_client WHERE authDevID != ''";
    ZG6000::ListStringMap listMapResult;
    if (!ZGProxyCommon::execQuerySql(sql, listMapResult))
        return false;
    for (const auto& mapResult : listMapResult)
    {
        try
        {
            const auto& clientID = ZGUtils::get(mapResult, "id");
            const auto& authDevID = ZGUtils::get(mapResult, "authDevID");
            std::string datasetID;
            if (!ZGProxyCommon::getDataByField("mp_param_device", authDevID, "datasetID", datasetID))
                continue;
            m_mapDatasetClient.insert(std::make_pair(datasetID, clientID));
            m_mapClientAuthParam.insert(std::make_pair(clientID, AuthParam()));
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
    return true;
}

void ZGSPUserManagerMng::onCheckStatus()
{
    QDateTime currentTime = QDateTime::currentDateTime();
    ++m_checkCount;
    if (m_checkCount >= m_updateInterval)
    {
        m_checkCount = 0;
        checkLoginStatus(currentTime);
    }
    std::unique_lock lock(m_mutex);
    for (auto& pair : m_mapClientAuthParam)
    {
        if (!pair.second.authUserID.empty() || (!pair.second.authModeID.empty()))
        {
            if (pair.second.authRequestTime.secsTo(currentTime) > m_checkTimeout)
            {
                QString currTime = currentTime.toString("yyyy-MM-dd hh:mm:ss");
                publishAuthResult(pair.first, pair.second.authUserID, pair.second.authModeID,
                    "timeout", currTime.toStdString());
                pair.second.authUserID = "";
                pair.second.authModeID = "";
                pair.second.isLogin = false;
            }
        }
    }
}

bool ZGSPUserManagerMng::checkClientState(const std::string& clientID,
                                          const std::string& userID,
                                          const std::string& authModeID,
                                          bool isLogin,
                                          ZG6000::ErrorInfo& e)
{
    if (clientID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端ID未指定";
        ZGLOG_ERROR(e);
        return false;
    }
    std::string fieldValue;
    if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "id", fieldValue))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
        e.errDetail = u8"指定的客户端'" + clientID + u8"'不存在";
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringMap mapFieldValue;
    if (!ZGProxyCommon::getDataByFields("sp_param_client", clientID, {
        "rtIsActivate", "rtState",
        "rtAppNodeID"
    }, mapFieldValue))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
        e.errDetail = u8"获取客户端信息错误，客户端:" + clientID;
        ZGLOG_ERROR(e);
        return false;
    }
    const auto& rtIsActivate = ZGUtils::get(mapFieldValue, "rtIsActivate");
    const auto& rtState = ZGUtils::get(mapFieldValue, "rtState");
    const auto& rtAppNodeID = ZGUtils::get(mapFieldValue, "rtAppNodeID");
    if (rtIsActivate != "1")
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端" + clientID + u8"未激活";
        ZGLOG_ERROR(e);
        return false;
    }
    if (rtState != "2")
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端" + clientID + u8"不在线";
        ZGLOG_ERROR(e);
        return false;
    }
    if (rtAppNodeID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端" + clientID + u8"关联应用节点不存在";
        ZGLOG_ERROR(e);
        return false;
    }
    std::string sql = "SELECT authModeID FROM sp_param_client_auth WHERE clientID = '" + clientID + "'";
    ZG6000::StringList listResult;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取客户端" + clientID + u8"授权模式失败";
        ZGLOG_ERROR(e);
        return false;
    }
    auto it = std::find(listResult.begin(), listResult.end(), authModeID);
    if (it == listResult.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端" + clientID + u8"不具备相关的授权模式" + authModeID;
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::checkClientLoginState(const std::string& clientID,
                                               const std::string& userID,
                                               ZG6000::ErrorInfo& e)
{
    std::string loginUserID;
    if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtLoginUserID", loginUserID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
        e.errDetail = u8"获取客户端'" + clientID + u8"登录用户失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (!loginUserID.empty() && (userID != loginUserID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端'" + clientID + u8"'已经存在登录的用户'" + loginUserID + "'";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::checkUserState(const std::string& clientID,
                                        const std::string& userID,
                                        const std::string& authModeID,
                                        bool isLogin,
                                        ZG6000::ErrorInfo& e)
{
    std::string sql = "SELECT authModeID FROM sp_param_hrm_user_auth WHERE userID = '" + userID + "'";
    ZG6000::StringList listResult;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户授权模式失败";
        ZGLOG_ERROR(e);
        return false;
    }
    auto it = std::find(listResult.begin(), listResult.end(), authModeID);
    if (it == listResult.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
        e.errDetail = u8"用户不具备相关的授权模式" + authModeID;
        ZGLOG_ERROR(e);
        return false;
    }
    sql = "SELECT appnodeID FROM sp_param_hrm_user_appnode WHERE userID = '" + userID + "'";
    listResult.clear();
    if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户应用节点错误";
        ZGLOG_ERROR(e);
        return false;
    }
    if (!clientID.empty())
    {
        std::string rtAppNodeID;
        if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtAppNodeID", rtAppNodeID))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
            e.errDetail = u8"获取客户端信息错误，客户端:'" + clientID + "'";
            ZGLOG_ERROR(e);
            return false;
        }
        auto item = std::find(listResult.begin(), listResult.end(), rtAppNodeID);
        if (item == listResult.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
            e.errDetail = u8"客户端'" + clientID + u8"'不在用户的管理区域";
            ZGLOG_ERROR(e);
            return false;
        }
    }
    return true;
}

bool ZGSPUserManagerMng::checkUserAppNode(const std::string& userID,
                                          const std::string& appNodeID,
                                          ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT appnodeID FROM sp_param_hrm_user_appnode WHERE userID = '%1' "
        "AND appnodeID = '%2'").arg(userID.c_str()).arg(appNodeID.c_str());
    ZG6000::StringList listResult;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listResult))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户应用节点错误";
        ZGLOG_ERROR(e);
        return false;
    }
    if (listResult.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        std::string appNodeName;
        ZGProxyCommon::getDataByField("sp_param_appnode", appNodeID, "name", appNodeName);
        e.errDetail = QStringLiteral("用户未包含指定的节点'%2'").arg(appNodeName.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::checkUserLoginState(const std::string& clientID,
                                             const std::string& userID,
                                             ZG6000::ErrorInfo& e)
{
    // 检查该用户是否已在其他客户端登录
    QString sql = QString("SELECT id FROM sp_param_client WHERE rtLoginUserID = '%1'").arg(userID.c_str());
    ZG6000::StringList listClientID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listClientID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户登录客户端信息失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (!listClientID.empty())
    {
        const auto& loginClientID = listClientID.front();
        if (loginClientID != clientID)
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
            e.errDetail = u8"用户已在客户端'" + loginClientID + u8"'登录";
            ZGLOG_ERROR(e);
            return false;
        }
    }
    return true;
}

bool ZGSPUserManagerMng::isClientExists(const std::string& clientID,
                                        ZG6000::ErrorInfo& e)
{
    if (clientID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端ID未指定";
        ZGLOG_ERROR(e);
        return false;
    }
    std::string sql = "SELECT id FROM sp_param_client WHERE id = '" + clientID + "'";
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取客户端'" + clientID + u8"'信息失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (listID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
        e.errDetail = u8"指定的客户端'" + clientID + u8"'不存在";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::isClientValid(const std::string& clientID,
                                       ZG6000::ErrorInfo& e)
{
    std::string clientActive;
    if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtIsActivate", clientActive))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
        e.errDetail = u8"获取客户端'" + clientID + u8"'激活信息失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (clientActive != "1")
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端" + clientID + u8"未激活";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::isClientOnline(const std::string clientID,
                                        ZG6000::ErrorInfo& e)
{
    std::string clientOnline;
    if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtState", clientOnline))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
        e.errDetail = u8"获取客户端'" + clientID + u8"'在线信息失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (clientOnline != "2")
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端" + clientID + u8"不在线";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::isUserValid(const std::string& userID,
                                     ZG6000::ErrorInfo& e)
{
    if (userID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
        e.errDetail = u8"用户ID未指定";
        ZGLOG_ERROR(e);
        return false;
    }
    std::string sql = "SELECT id FROM sp_param_hrm_user WHERE id = '" + userID + "'";
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户信息失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (listID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
        e.errDetail = u8"指定的用户ID'" + userID + u8"'不存在";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::checkClientAuthState(const std::string& clientID,
                                              const std::string& authModeID,
                                              ZG6000::ErrorInfo& e)
{
    std::string sql = "SELECT authModeID FROM sp_param_client_auth WHERE clientID = '" + clientID + "'";
    ZG6000::StringList listResult;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取客户端" + clientID + u8"授权模式失败";
        ZGLOG_ERROR(e);
        return false;
    }
    auto it = std::find(listResult.begin(), listResult.end(), authModeID);
    if (it == listResult.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"客户端" + clientID + u8"不具备相关的授权模式" + authModeID;
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::checkUserAuthState(const std::string& userID,
                                            const std::string& authModeID,
                                            ZG6000::ErrorInfo& e)
{
    std::string sql = "SELECT authModeID FROM sp_param_hrm_user_auth WHERE userID = '" + userID + "'";
    ZG6000::StringList listResult;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取用户授权模式失败";
        ZGLOG_ERROR(e);
        return false;
    }
    auto it = std::find(listResult.begin(), listResult.end(), authModeID);
    if (it == listResult.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_USER);
        e.errDetail = u8"用户不具备相关的授权模式" + authModeID;
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::createActiveClient(const std::string& clientID,
                                            const std::string& userID,
                                            int keepTime,
                                            ZG6000::ErrorInfo& e)
{
    ZG6000::StringMap record;
    record["id"] = clientID;
    record["name"] = clientID;
    ZG6000::ListStringMap listRecord;
    listRecord.push_back(record);
    if (!ZGProxyCommon::insertDataByTable("sp_param_client", listRecord))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"新建客户端'" + clientID + u8"'失败";
        ZGLOG_ERROR(e);
        return false;
    }
    // record["rtIsActivate"] = "1";
    // record["rtState"] = "1";
    // record["rtLoginUserID"] = userID;
    // record["rtLoginTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
    // record["rtKeepTime"] = std::to_string(keepTime);
    // QString uuid = QUuid::createUuid().toString(QUuid::Id128);
    // record["rtCookieID"] = uuid.toStdString();
    // if (!ZGProxyCommon::updateDataByID("sp_param_client", clientID, record))
    // {
    //     e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
    //     e.errDetail = u8"更新客户端'" + clientID + u8"'失败";
    //     ZGLOG_ERROR(e);
    //     return false;
    // }
    return true;
}

bool ZGSPUserManagerMng::getAvailableClient(const std::string& userID,
                                            std::string& clientID,
                                            std::string& cookie,
                                            ZG6000::ErrorInfo& e)
{
    std::string sql = "SELECT id, rtLoginUserID, rtCookieID FROM sp_param_client WHERE clientTypeID = 'ZG_CT_MAINTAIN'";
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql, listRecord))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取维护客户端信息失败";
        ZGLOG_ERROR(e);
        return false;
    }
    for (const auto& record : listRecord)
    {
        const auto& rtLoginUserID = ZGUtils::get(record, "rtLoginUserID");
        if (rtLoginUserID == userID)
        {
            clientID = ZGUtils::get(record, "id");
            cookie = ZGUtils::get(record, "rtCookieID");
            return true;
        }
        if (rtLoginUserID.empty() && clientID.empty())
        {
            clientID = ZGUtils::get(record, "id");
            cookie = ZGUtils::get(record, "rtCookieID");
        }
    }
    if (clientID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
        e.errDetail = u8"无可用的维护客户端";
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPUserManagerMng::getLoginClientInfo(const std::string& clientID,
                                            const std::string& userID,
                                            std::string& outClientID,
                                            std::string& cookie,
                                            ZG6000::ErrorInfo& e)
{
    if (clientID.empty())
    {
        if (!getAvailableClient(userID, outClientID, cookie, e))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_CLIENT);
            e.errDetail = u8"没有可用的维护客户端";
            ZGLOG_ERROR(e);
            return false;
        }
    }
    else
    {
        if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtCookieID", cookie))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
            e.errDetail = u8"获取客户端'" + clientID + u8"' cookie 失败";
            ZGLOG_ERROR(e);
            return false;
        }
        outClientID = clientID;
    }
    return true;
}

bool ZGSPUserManagerMng::updateClientLogin(const std::string& clientID,
                                           const std::string& userID,
                                           const std::string& loginTime,
                                           const std::string& cookie,
                                           int keepTime,
                                           ZG6000::ErrorInfo& e)
{
    std::string sql = "SELECT clientTypeID FROM sp_param_client WHERE id = '" + clientID + "'";
    std::string clientType;
    if (!ZGProxyCommon::execQuerySqlField(sql, clientType))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取客户端'" + clientID + u8"'类型失败";
        ZGLOG_ERROR(e);
        return false;
    }
    sql = "SELECT id FROM sp_param_client WHERE clientTypeID = '" + clientType + "'";
    ZG6000::StringList listClientID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listClientID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"获取客户端信息失败";
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringMap mapFieldValue{
        {"id", clientID}, {"rtLoginUserID", userID}, {"rtLoginTime", loginTime},
        {"rtKeepTime", std::to_string(keepTime)},
        {"rtUpdateTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString()}
    };
    if (cookie.empty())
    {
        QString uuid = QUuid::createUuid().toString(QUuid::Id128);
        mapFieldValue.insert(std::make_pair("rtCookieID", uuid.toStdString()));
    }
    else
        mapFieldValue.insert(std::make_pair("rtCookieID", cookie));
    sql = ZGUtils::generateUpdateSql("sp_param_client", mapFieldValue);
    if (!ZGProxyCommon::execSql(sql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_DB);
        e.errDetail = u8"更新客户端登录信息失败";
        ZGLOG_ERROR(e);
        return false;
    }
    std::string userName;
    if (userID == "root")
        userName = u8"超级管理员";
    else
    {
        sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + userID + "'";
        if (!ZGProxyCommon::execQuerySqlField(sql, userName))
            ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(userID.c_str()));
    }
    std::string clientName;
    sql = "SELECT name FROM sp_param_client WHERE id = '" + clientID + "'";
    if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
        ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(clientID.c_str()));
    // std::string eventInfo = u8"用户【" + userName + u8"】登录至客户端【" + clientName + "】";
    std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") + u8"【" + userName + u8"】" +
        ZGUtils::languageString(m_firstLanguage, "loginToClient") + "【" + clientName + u8"】";
    std::string eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") + u8"【" + userName + u8"】" +
        ZGUtils::languageString(m_secondLanguage, "loginToClient") + "【" + clientName + u8"】";
    publishUserEvent(clientID, eventInfo, eventInfoL2);
    publishClientEvent(clientID, userID, userName, "login");
    if (!listClientID.empty())
    {
        ZG6000::ListStringMap listLoginUser;
        if (!ZGProxyCommon::mgetDataByFields("sp_param_client", listClientID,
            {"rtAppNodeID", "rtLoginUserID"}, listLoginUser))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPUserManager::ZG_ERR_RT);
            e.errDetail = u8"获取客户端登录用户失败";
            ZGLOG_ERROR(e);
            return false;
        }
        for (size_t i = 0; i < listClientID.size(); ++i)
        {
            if ((listLoginUser[i]["rtLoginUserID"] == userID) && (listClientID[i] != clientID))
            {
                if (!logout(listClientID[i], userID, false, e))
                    return false;
                sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + userID + "'";
                if (!ZGProxyCommon::execQuerySqlField(sql, userName))
                    ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(userID.c_str()));
                sql = "SELECT name FROM sp_param_client WHERE id = '" + listClientID[i] + "'";
                if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
                    ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(listClientID[i].c_str()));
                // eventInfo = u8"用户【" + userName + u8"】已从客户端【" + clientName +
                //     u8"】强制下线，原因：该用户在其他客户端登录";
                eventInfo = ZGUtils::languageString(m_firstLanguage, "user") + u8"【" + userName + u8"】" +
                    ZGUtils::languageString(m_firstLanguage, "fromClient") + "【" + clientName + u8"】" +
                    ZGUtils::languageString(m_firstLanguage, "offlineReason") + u8"：" +
                    ZGUtils::languageString(m_firstLanguage, "loginOtherClient");
                eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") + u8"【" + userName + u8"】" +
                    ZGUtils::languageString(m_secondLanguage, "fromClient") + "【" + clientName + u8"】" +
                    ZGUtils::languageString(m_secondLanguage, "offlineReason") + u8"：" +
                    ZGUtils::languageString(m_secondLanguage, "loginOtherClient");
                publishUserEvent(clientID, eventInfo, eventInfoL2);
                publishClientEvent(listClientID[i], userID, userName, "logout");
            }
        }
    }
    return true;
}

/**
 * 普通用户登录
 * @param clientID 客户端ID
 * @param userID 用户ID
 * @param authModeID 授权模式ID
 * @param e 错误信息
 * @param func 执行函数
 * @return 登录成功返回true，否则返回false
 */
bool ZGSPUserManagerMng::doVerifyNormalLogin(const std::string& clientID,
                                             const std::string& userID,
                                             const std::string& authModeID,
                                             ZG6000::ErrorInfo& e,
                                             const std::function<bool()>& func)
{
    // 客户端是否存在
    if (!isClientExists(clientID, e))
        return false;
    // 客户端是否激活
    if (!isClientValid(clientID, e))
        return false;
    // 用户是否存在
    if (!isUserValid(userID, e))
        return false;
    if (!checkClientAuthState(clientID, authModeID, e))
        return false;
    if (!checkUserAuthState(userID, authModeID, e))
        return false;
    // 客户端是否在线
    if (!checkClientLoginState(clientID, userID, e))
        return false;
    if (!func())
        return false;
    return true;
}

bool ZGSPUserManagerMng::doVerifyNormalCheck(const std::string& clientID,
                                             const std::string& authModeID,
                                             ZG6000::ErrorInfo& e,
                                             const std::function<bool ()>& func)
{
    if (!isClientExists(clientID, e))
        return false;
    if (!isClientValid(clientID, e))
        return false;
    if (!isClientOnline(clientID, e))
        return false;
    if (!checkClientAuthState(clientID, authModeID, e))
        return false;
    if (!func())
        return false;
    return true;
}

/**
 * 执行普通验证检查
 * @param clientID 客户端ID
 * @param userID 用户ID
 * @param authModeID 授权模式ID
 * @param e 错误信息
 * @param func 执行函数
 * @return 验证通过返回true，否则返回false
 */
bool ZGSPUserManagerMng::doVerifyNormalCheck(const std::string& clientID,
                                             const std::string& userID,
                                             const std::string& authModeID,
                                             ZG6000::ErrorInfo& e,
                                             const std::function<bool()>& func)
{
    // 客户端是否存在
    if (!isClientExists(clientID, e))
        return false;
    // 客户端是否激活
    if (!isClientValid(clientID, e))
        return false;
    // 用户是否存在
    if (!isUserValid(userID, e))
        return false;
    // 客户端是否在线
    if (!isClientOnline(clientID, e))
        return false;
    if (!checkClientAuthState(clientID, authModeID, e))
        return false;
    if (!checkUserAuthState(userID, authModeID, e))
        return false;
    if (!func())
        return false;
    return true;
}

bool ZGSPUserManagerMng::doVerifySuperCheck(const std::string& clientID,
                                            const std::string& userID,
                                            const std::string& password,
                                            ZG6000::ErrorInfo& e,
                                            const std::function<bool()>& func)
{
    if (!isSuperUser(userID, password))
        return false;
    if (!isClientExists(clientID, e))
        return false;
    if (!isClientValid(clientID, e))
        return false;
    return true;
}

void ZGSPUserManagerMng::onReceivedMessage(const QString& topic,
                                           const QString& message)
{
    std::string strTopic = topic.toStdString();
    size_t pos = strTopic.find_first_of("/");
    std::string datasetID = strTopic.substr(0, pos);
    std::string strMessage = message.toStdString();
    std::string tableName, oper, reason, occurTime, errMsg;
    ZG6000::ListRecord listRecord;
    if (!ZGJson::convertFromJsonToListRecord(strMessage, tableName, oper, reason, occurTime, listRecord,
        errMsg))
    {
        ZGLOG_ERROR("Convert json message error.");
        return;
    }
    ZG6000::StringMap mapValue;
    for (const auto& record : listRecord)
    {
        getRecordValue(record, mapValue);
    }
    if (mapValue.find("0") == mapValue.end() || (mapValue.find("1") == mapValue.end()))
    {
        ZGLOG_ERROR("Can't acquire both user and authMode.");
        return;
    }
    const auto& userID = ZGUtils::get(mapValue, "0");
    const auto& authModeID = ZGUtils::get(mapValue, "1");
    auto pairDataset = m_mapDatasetClient.find(datasetID);
    if (pairDataset == m_mapDatasetClient.end())
    {
        ZGLOG_ERROR(QString("Can't find dataset %1 client.").arg(datasetID.c_str()));
        return;
    }
    const auto& clientID = pairDataset->second;
    std::unique_lock lock(m_mutex);
    auto pairClient = m_mapClientAuthParam.find(clientID);
    if (pairClient == m_mapClientAuthParam.end())
    {
        ZGLOG_ERROR(QString("Can't find client %1 auth param.").arg(clientID.c_str()));
        return;
    }
    AuthParam authParam = pairClient->second;
    lock.unlock();
    if (authParam.authModeID.empty() && authParam.authUserID.empty())
        return;
    if (authParam.authUserID.empty())
        authParam.authUserID = userID;
    if (authParam.authModeID.empty())
        authParam.authModeID = authModeID;
    QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    if ((userID != authParam.authUserID) || (authModeID != authParam.authModeID))
    {
        publishAuthResult(clientID, userID, authModeID, "false", currentTime.toStdString());
        ZGLOG_ERROR(QString("User %1 verify error.").arg(userID.c_str()));
        return;
    }
    if (!publishAuthResult(clientID, userID, authModeID, "true", currentTime.toStdString()))
        return;
    clearAuthRequest(clientID);
    if (authParam.isLogin)
    {
        QString uuid = QUuid::createUuid().toString(QUuid::Id128);
        ZG6000::ErrorInfo e;
        if (!updateClientLogin(clientID, userID, currentTime.toStdString(), uuid.toStdString(),
            authParam.keepTime, e))
            ZGLOG_ERROR(e);
    }
}

ZGSPUserManagerMng::ZGSPUserManagerMng(QObject* parent)
    :
    QObject(parent)
{
}

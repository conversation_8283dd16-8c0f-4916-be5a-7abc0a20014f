#ifndef ZGPASSWORDUTILS_H
#define ZGPASSWORDUTILS_H

#include <string>
#include <vector>

// 密码生成和验证的选项结构体
struct PasswordOptions {
    int minLength = 7;              // 密码最小长度
    bool requireUppercase = true;   // 是否需要大写字母
    bool requireLowercase = true;   // 是否需要小写字母
    bool requireDigits = true;      // 是否需要数字
    bool requireSpecial = false;    // 是否需要特殊字符
    int minUppercase = 1;           // 最少大写字母数量
    int minLowercase = 1;           // 最少小写字母数量
    int minDigits = 1;              // 最少数字数量
    int minSpecial = 0;             // 最少特殊字符数量
    std::string allowedSpecial = "!@#$%^&*()-_=+[]{}|;:,.<>?/"; // 允许的特殊字符
};

// 密码验证结果结构体
struct PasswordValidationResult {
    bool isValid = false;                 // 密码是否有效
    std::vector<std::string> errors;      // 错误信息列表

    // 添加错误信息
    void addError(const std::string& error) {
        errors.push_back(error);
        isValid = false;
    }
};

/**
 * 生成符合指定要求的随机密码
 *
 * @param options 密码生成选项
 * @return 生成的随机密码
 */
std::string generatePassword(const PasswordOptions& options);

/**
 * 验证密码是否符合指定要求
 *
 * @param password 要验证的密码
 * @param options 密码验证选项
 * @return 验证结果
 */
PasswordValidationResult validatePassword(const std::string& password, const PasswordOptions& options);

#endif // ZGPASSWORDUTILS_H

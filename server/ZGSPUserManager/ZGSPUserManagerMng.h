#ifndef ZGSPUSERMANAGERMNG_H
#define ZGSPUSERMANAGERMNG_H

#include <QTimer>
#include <QJsonObject>
#include <QJsonArray>
#include <unordered_map>
#include <mutex>
#include "ZGServerCommon.h"
#include "ZGPasswordUtils.h"

class ZGRedisClient;
class ZGMqttClient;

class ZGSPUserManagerMng : public QObject
{
    Q_OBJECT

public:
    static ZGSPUserManagerMng* instance();
    void init();
    bool checkState();

    void dispatchData(const std::string& tableName,
                      const std::string& oper,
                      const std::string& reason,
                      const std::string& time,
                      const ZG6000::ListRecord& listRecord);

    bool getUserInfo(const std::string& userID,
                     ZG6000::StringMap& user,
                     ZG6000::ListStringMap& listRole,
                     ZG6000::ListStringMap& listCard,
                     ZG6000::ListStringMap& listAuth,
                     ZG6000::ListStringMap& listAppNode,
                     ZG6000::ErrorInfo& e);

    bool getUserFingers(const std::string& userID,
                        ZG6000::ListStringMap& listFinger,
                        ZG6000::ErrorInfo& e);

    bool getUserFace(const std::string& userID,
                     std::string& faceData,
                     ZG6000::ErrorInfo& e);

    bool addUser(const ZG6000::StringMap& user,
                 const ZG6000::ListStringMap& listRole,
                 const ZG6000::ListStringMap& listAuth,
                 const ZG6000::ListStringMap& listAppNode,
                 ZG6000::ErrorInfo& e);

    bool updateUser(const ZG6000::StringMap& user,
                    const ZG6000::ListStringMap& listRole,
                    const ZG6000::ListStringMap& listAuth,
                    const ZG6000::ListStringMap& listAppNode,
                    ZG6000::ErrorInfo& e);

    /**
     * @brief	删除用户
     *
     * @param 		  	userID	用户ID
     * @param [in,out]	e	  	执行出错时的错误信息
     *
     * @return	执行成功返回true，失败返回false。
     */
    bool deleteUser(const std::string& userID,
                    ZG6000::ErrorInfo& e);

    bool isUserHasPower(const std::string& userID,
                        const std::string& powerID,
                        bool& hasPower,
                        ZG6000::ErrorInfo& e);

    bool isCardBindUser(const std::string& cardID,
                        bool& isBind,
                        std::string& userID,
                        ZG6000::ErrorInfo& e);

    bool changePassword(const std::string& userID,
                        const std::string& oldPassword,
                        const std::string& newPassword,
                        ZG6000::ErrorInfo& e);

    bool resetPassword(const std::string& userID,
                       ZG6000::ErrorInfo& e);

    /**
     * @brief   使用用户名和密码登录
     *
     * @param           clientID    客户端ID
     * @param           userID      用户ID
     * @param           password    密码
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool loginByPassword(const std::string& clientID,
                         const std::string& userID,
                         const std::string& password,
                         int keepTime,
                         std::string& outClientID,
                         ZG6000::ErrorInfo& e);

    /**
     * @brief   使用卡号登录
     *
     * @param           clientID    客户端ID
     * @param           userID      用户ID(用户ID不为空时，表示使用该用户的卡登录)
     * @param           cardID      卡ID
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool loginByCard(const std::string& clientID,
                     const std::string& userID,
                     const std::string& authModeID,
                     const std::string& cardID,
                     int keepTime,
                     std::string& outClientID,
                     ZG6000::ErrorInfo& e);

    /**
     * @brief   向手机发送验证码
     *
     * @param           phoneNumber 手机号
     * @param [in,out]  seqNo       验证码序号
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool sendVerifyCode(const std::string& phoneNumber,
                        std::string& seqNo,
                        ZG6000::ErrorInfo& e);

    /**
     * @brief   使用手机验证码登录
     *
     * @param           code        验证码
     * @param           clientID    客户端ID
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool loginByVerifyCode(const std::string& clientID,
                           const std::string& phoneNumber,
                           const std::string& code,
                           int keepTime,
                           std::string& outClientID,
                           ZG6000::ErrorInfo& e);

    /**
     * @brief   使用第三方认证设备进行登录
     *
     * @param           clientID    客户端ID.
     * @param           userID      用户ID(不为空时表示只能该用户通过认证设备登录)
     * @param           authModeID  授权模式ID.
     * @param           keepTime    保持时间
     * @param [in,out]  e           执行出错时的错误信息.
     *
     * @return  执行成功返回true，失败返回false。.
     */
    bool loginByAuthDev(const std::string& clientID,
                        const std::string& userID,
                        const std::string& authModeID,
                        int keepTime,
                        ZG6000::ErrorInfo& e);

    /**
     * @brief   用户登出
     *
     * @param           userID      用户ID
     * @param           clientID    客户端ID
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool logout(const std::string& clientID,
                const std::string& userID,
                ZG6000::ErrorInfo& e);

    /**
     * @brief   获得可用用户
     *
     * @param           clientID    客户端ID(如果为空，表示不限制客户端）
     * @param           appNodeID   应用节点ID(如果为空，表示不限制应用节点)
     * @param           powerID     权限ID(如果为空，表示不限制权限)
     * @param		    lstUser     可用用户列表
     * @param		    e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool getAvaiableUser(const std::string& clientID,
                         const std::string& appNodeID,
                         const std::string& powerID,
                         ZG6000::ListStringMap& lstUser,
                         ZG6000::ErrorInfo& e);

    bool verifyByUser(const std::string& clientID,
                      const std::string& userID,
                      const std::string& powerID,
                      ZG6000::ErrorInfo& e);
    
    bool verifyByPassword(const std::string& clientID,
                          const std::string& userID,
                          const std::string& password,
                          const std::string& appNodeID,
                          const std::string& powerID,
                          ZG6000::ErrorInfo& e);

    /**
     * @brief	使用密码进行用户权限验证(不判客户端)
     *
     * @param 		  	userID  	用户ID
     * @param 		  	password	密码
     * @param 		  	powerID 	权限ID
     * @param [in,out]	e			执行出错时的错误信息
     */
    bool verifyByPasswordNoClient(const std::string& userID,
                                  const std::string& password,
                                  const std::string& appNodeID,
                                  const std::string& powerID,
                                  ZG6000::ErrorInfo& e);

    /**
     * @brief   使用卡号进行用户权限验证
     *
     * @param           clientID    客户端ID
     * @param           userID      用户ID(不为空时，代表只使用该用户的卡进行验证)
     * @param           cardID      卡ID
     * @param           powerID     权限ID
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool verifyByCard(const std::string& clientID,
                      const std::string& userID,
                      const std::string& authModeID,
                      const std::string& cardID,
                      const std::string& appNodeID,
                      const std::string& powerID,
                      std::string& realUserID,
                      ZG6000::ErrorInfo& e);

    bool verifyByCardNoClient(const std::string& userID,
                              const std::string& authModeID,
                              const std::string& cardID,
                              const std::string& appNodeID,
                              const std::string& powerID,
                              std::string& realUserID,
                              ZG6000::ErrorInfo& e);

    /**
     * @brief   使用验证码进行用户权限验证
     *
     * @param           clientID    客户端ID
     * @param           phoneNumber 手机号
     * @param           code        验证码
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool verifyByVerifyCode(const std::string& clientID,
                            const std::string& phoneNumber,
                            const std::string& code,
                            ZG6000::ErrorInfo& e);

    /**
     * @brief   使用第三方认证设备进行用户权限验证
     *
     * @param           clientID    客户端ID.
     * @param           userID      用户ID(不为空时表示只使用该用户进行认证设备认证)
     * @param           authModeID  授权模式ID.
     * @param           powerID     权限ID.
     * @param [in,out]  e           执行出错时的错误信息.
     *
     * @return  执行成功返回true，失败返回false。.
     */
    bool verifyByAuthDev(const std::string& clientID,
                         const std::string& userID,
                         const std::string& authModeID,
                         const std::string& appNodeID,
                         const std::string& powerID,
                         ZG6000::ErrorInfo& e);

    bool verifyByAuthDevNoClient(const std::string& userID,
                                 const std::string& authModeID,
                                 const std::string& appNodeID,
                                 const std::string& powerID,
                                 ZG6000::ErrorInfo& e);

    /**
     * @brief   中止正在进行的第三方设备认证
     *
     * @param   clientID    客户端ID.
     */
    void cancelRequest(const std::string& clientID);

    bool updateUserFace(const std::string& userID,
                        std::string&& faceData,
                        ZG6000::ErrorInfo& e);

    bool deleteUserFace(const std::string& userID,
                        ZG6000::ErrorInfo& e);

    bool updateUserFinger(const std::string& userID,
                          int fingerNo,
                          const std::string& fingerData,
                          ZG6000::ErrorInfo& e);

    bool deleteUserFinger(const std::string& userID,
                          int fingerNo,
                          ZG6000::ErrorInfo& e);

    bool addUserCard(const std::string& userID,
                     const std::string& cardNo,
                     ZG6000::ErrorInfo& e);

    bool deleteUserCard(const std::string& cardNo,
                        ZG6000::ErrorInfo& e);

    bool sendRandomPassword(std::string userID,
                            ZG6000::ErrorInfo& e);

public:
    [[nodiscard]] std::string firstLanguage() const
    {
        return m_firstLanguage;
    }

    [[nodiscard]] std::string secondLanguage() const
    {
        return m_secondLanguage;
    }

private:
    explicit ZGSPUserManagerMng(QObject* parent = nullptr);
    bool initClientDataset();
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initRedisClient();
    bool initMqttClient();
    bool initSystemParam();
    bool initParam();
    bool checkClientState(const std::string& clientID,
                          const std::string& userID,
                          const std::string& authModeID,
                          bool isLogin,
                          ZG6000::ErrorInfo& e);
    bool checkClientLoginState(const std::string& clientID,
                               const std::string& userID,
                               ZG6000::ErrorInfo& e);
    bool checkUserState(const std::string& clientID,
                        const std::string& userID,
                        const std::string& authModeID,
                        bool isLogin,
                        ZG6000::ErrorInfo& e);
    bool checkUserAppNode(const std::string& userID,
                          const std::string& appNodeID,
                          ZG6000::ErrorInfo& e);
    bool checkUserLoginState(const std::string& clientID,
                             const std::string& userID,
                             ZG6000::ErrorInfo& e);
    bool isClientExists(const std::string& clientID,
                        ZG6000::ErrorInfo& e);
    bool isClientValid(const std::string& clientID,
                       ZG6000::ErrorInfo& e);
    bool isClientOnline(const std::string clientID,
                        ZG6000::ErrorInfo& e);
    bool isUserValid(const std::string& userID,
                     ZG6000::ErrorInfo& e);
    bool checkClientAuthState(const std::string& clientID,
                              const std::string& authModeID,
                              ZG6000::ErrorInfo& e);
    bool checkUserAuthState(const std::string& userID,
                            const std::string& authModeID,
                            ZG6000::ErrorInfo& e);
    bool createActiveClient(const std::string& clientID,
                            const std::string& userID,
                            int keepTime,
                            ZG6000::ErrorInfo& e);
    bool getAvailableClient(const std::string& userID,
                            std::string& clientID,
                            std::string& cookie,
                            ZG6000::ErrorInfo& e);
    bool getLoginClientInfo(const std::string& clientID,
                            const std::string& userID,
                            std::string& outClientID,
                            std::string& cookie,
                            ZG6000::ErrorInfo& e);
    bool updateClientLogin(const std::string& clientID,
                           const std::string& userID,
                           const std::string& loginTime,
                           const std::string& cookie,
                           int keepTime,
                           ZG6000::ErrorInfo& e);
    bool doVerifyNormalLogin(const std::string& clientID,
                             const std::string& userID,
                             const std::string& authModeID,
                             ZG6000::ErrorInfo& e,
                             const std::function<bool()>& func);
    bool doVerifyNormalCheck(const std::string& clientID,
                             const std::string& authModeID,
                             ZG6000::ErrorInfo& e,
                             const std::function<bool()>& func);
    bool doVerifyNormalCheck(const std::string& clientID,
                             const std::string& userID,
                             const std::string& authModeID,
                             ZG6000::ErrorInfo& e,
                             const std::function<bool()>& func);
    bool doVerifySuperCheck(const std::string& clientID,
                            const std::string& userID,
                            const std::string& password,
                            ZG6000::ErrorInfo& e,
                            const std::function<bool()>& func);
    bool verifyByPassword(const std::string& userID,
                          const std::string& password,
                          ZG6000::ErrorInfo& e);
    bool verifyByCard(const std::string& userID,
                      const std::string& cardID,
                      std::string& realUserID,
                      ZG6000::ErrorInfo& e);
    bool verifyUserPower(const std::string& userID,
                         const std::string& powerID,
                         ZG6000::ErrorInfo& e);
    bool verifyCode(const std::string& inputCode,
                    const std::string& verifyCode,
                    const std::string& time,
                    ZG6000::ErrorInfo& e);
    bool verifyUserUnique(const ZG6000::StringMap& user,
                          ZG6000::ErrorInfo& e);
    bool isSuperUser(const std::string& userID,
                     const std::string& password);
    void cleanVerifyCode(const std::string& userID);
    bool getUserByPhone(const std::string& phoneNumber,
                        ZG6000::StringMap& mapResult,
                        ZG6000::ErrorInfo& e);
    bool registerAuthRequest(const std::string& clientID,
                             const std::string& authModeID,
                             const std::string& userID,
                             bool isLogin,
                             int keepTime,
                             ZG6000::ErrorInfo& e);
    void clearAuthRequest(const std::string& clientID);
    void getRecordValue(const ZG6000::MapField& record,
                        ZG6000::StringMap& mapValue);
    bool publishAuthResult(const std::string& clientID,
                           const std::string& userID,
                           const std::string& authModeID,
                           const std::string& result,
                           const std::string& time);
    void checkLoginStatus(const QDateTime& dateTime);
    void publishUserEvent(const std::string& clientID,
                          const std::string& eventInfo,
                          const std::string& eventInfoL2);
    void publishClientEvent(const std::string& clientID,
                            const std::string& userID,
                            const std::string& userName,
                            const std::string& type);
    bool logout(const std::string& clientID,
                const std::string& userID,
                bool publishEvent,
                ZG6000::ErrorInfo& e);
    void rebuildUserAssocSql(const QString& userID,
                             const QJsonObject& object,
                             ZG6000::StringList& listSql);
    bool rebuildUserAssocSql(const std::string& userID,
                             const ZG6000::ListStringMap& listRole,
                             const ZG6000::ListStringMap& listAuth,
                             const ZG6000::ListStringMap& listAppNode,
                             ZG6000::StringList& listSql);
    bool getAppNodeAvaiableUser(const std::string& appNodeID,
                                std::string powerID,
                                ZG6000::ListStringMap& lstUser,
                                ZG6000::ErrorInfo& e);
    bool sendSMSMessage(const std::string& mobileNumber,
                        const std::string& message,
                        ZG6000::ErrorInfo& e);
    bool generateHashedPassword(const QString& originPassword,
                                QString& hashedPassword,
                                QString& salt,
                                ZG6000::ErrorInfo& e);

private slots:
    void onCheckStatus();
    void onReceivedMessage(const QString& topic,
                           const QString& message);

private:
    struct AuthParam
    {
        std::string authUserID;
        std::string authModeID;
        QDateTime authRequestTime;
        int keepTime{1440};
        bool isLogin{false};
    };

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    int m_initInterval{10};
    int m_checkInterval{1};
    int m_checkTimeout{10};
    int m_updateInterval{10};
    QTimer m_initTimer;
    QTimer m_checkTimer;
    int m_checkCount{0};
    ZGRedisClient* m_redisRtQueue{nullptr};
    ZGRedisClient* m_redisRtTopic{nullptr};
    ZGMqttClient* m_mqttMessage{nullptr};
    mutable std::mutex m_mutex;
    std::unordered_map<std::string, AuthParam> m_mapClientAuthParam;
    std::unordered_map<std::string, std::string> m_mapDatasetClient;
    std::string m_firstLanguage;
    std::string m_secondLanguage;
    bool m_enableSafety{false};
    ZG6000::StringList m_listSMSDeviceID;
    PasswordOptions m_passwordOptions;
};

#endif // ZGSPUSERMANAGERMNG_H

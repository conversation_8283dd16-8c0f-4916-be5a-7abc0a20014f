#include "ZGSPUserManagerI.h"
#include "ZGSPUserManagerMng.h"

ZG6000::ZGSPUserManagerI::ZGSPUserManagerI()
{
    ZGSPUserManagerMng::instance()->init();
}

void ZG6000::ZGSPUserManagerI::dispatchData(std::string tableName,
                                            std::string oper,
                                            std::string reason,
                                            std::string time,
                                            ListRecord listRecord,
                                            const Ice::Current& current)
{
    ZGSPUserManagerMng::instance()->dispatchData(tableName, oper, reason, time, listRecord);
}

bool ZG6000::ZGSPUserManagerI::checkState(const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->checkState();
}

bool ZG6000::ZGSPUserManagerI::getUserInfo(std::string userID,
                                           StringMap& user,
                                           ListStringMap& listRole,
                                           ListStringMap& listCard,
                                           ListStringMap& listAuth,
                                           ListStringMap& listAppNode,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->getUserInfo(userID, user, listRole, listCard, listAuth, listAppNode, e);
}

bool ZG6000::ZGSPUserManagerI::getUserFingers(std::string userID,
                                              ListStringMap& listFinger,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->getUserFingers(userID, listFinger, e);
}

bool ZG6000::ZGSPUserManagerI::getUserFace(std::string userID,
                                           std::string& faceData,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->getUserFace(userID, faceData, e);
}

bool ZG6000::ZGSPUserManagerI::addUser(StringMap user,
                                       ListStringMap listRole,
                                       ListStringMap listAuth,
                                       ListStringMap listAppNode,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->addUser(user, listRole, listAuth, listAppNode, e);
}

bool ZG6000::ZGSPUserManagerI::updateUser(StringMap user,
                                          ListStringMap listRole,
                                          ListStringMap listAuth,
                                          ListStringMap listAppNode,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->updateUser(user, listRole, listAuth, listAppNode, e);
}

bool ZG6000::ZGSPUserManagerI::deleteUser(std::string userID,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->deleteUser(userID, e);
}

bool ZG6000::ZGSPUserManagerI::isUserHasPower(std::string userID,
                                              std::string powerID,
                                              bool& hasPower,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->isUserHasPower(userID, powerID, hasPower, e);
}

bool ZG6000::ZGSPUserManagerI::isCardBindUser(std::string cardID,
                                              bool& isBind,
                                              std::string& userID,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->isCardBindUser(cardID, isBind, userID, e);
}

bool ZG6000::ZGSPUserManagerI::changePassword(std::string userID,
                                              std::string oldPassword,
                                              std::string newPassword,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->changePassword(userID, oldPassword, newPassword, e);
}

bool ZG6000::ZGSPUserManagerI::resetPassword(std::string userID,
                                             ErrorInfo& e,
                                             const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->resetPassword(userID, e);
}

bool ZG6000::ZGSPUserManagerI::loginByPassword(std::string clientID,
                                               std::string userID,
                                               std::string password,
                                               int keepTime,
                                               std::string& outClientID,
                                               ErrorInfo& e,
                                               const Ice::Current& current)
{
    return false;
    // return ZGSPUserManagerMng::instance()->loginByPassword(clientID, userID, password, keepTime, outClientID, e);
}

bool ZG6000::ZGSPUserManagerI::loginByCard(std::string clientID,
                                           std::string userID,
                                           std::string authModeID,
                                           std::string cardID,
                                           int keepTime,
                                           std::string& outClientID,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->loginByCard(clientID, userID, authModeID, cardID, keepTime, outClientID, e);
}

bool ZG6000::ZGSPUserManagerI::loginByVerifyCode(std::string clientID,
                                                 std::string phoneNumber,
                                                 std::string code,
                                                 int keepTime,
                                                 std::string& outClientID,
                                                 ErrorInfo& e,
                                                 const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->loginByVerifyCode(clientID, phoneNumber, code, keepTime, outClientID, e);
}

bool ZG6000::ZGSPUserManagerI::logout(std::string clientID,
                                      std::string userID,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
{
    return false;
}

bool ZG6000::ZGSPUserManagerI::sendVerifyCode(std::string phoneNumber,
                                              std::string& seqNo,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->sendVerifyCode(phoneNumber, seqNo, e);
}

bool ZG6000::ZGSPUserManagerI::getAvaiableUser(std::string clientID,
                                               std::string appNodeID,
                                               std::string powerID,
                                               ListStringMap& lstUser,
                                               ErrorInfo& e,
                                               const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->getAvaiableUser(clientID, appNodeID, powerID, lstUser, e);
}

bool ZG6000::ZGSPUserManagerI::verifyByPassword(std::string clientID,
                                                std::string userID,
                                                std::string password,
                                                std::string appNodeID,
                                                std::string powerID,
                                                ErrorInfo& e,
                                                const Ice::Current& current)
{
    return false;
    // return ZGSPUserManagerMng::instance()->verifyByPassword(clientID, userID, password, appNodeID, powerID, e);
}

bool ZG6000::ZGSPUserManagerI::verifyByPasswordNoClient(std::string userID,
                                                        std::string password,
                                                        std::string appNodeID,
                                                        std::string powerID,
                                                        ErrorInfo& e,
                                                        const Ice::Current& current)
{
    return false;
    // return ZGSPUserManagerMng::instance()->verifyByPasswordNoClient(userID, password, appNodeID, powerID, e);
}

bool ZG6000::ZGSPUserManagerI::verifyByCard(std::string clientID,
                                            std::string userID,
                                            std::string authModeID,
                                            std::string cardID,
                                            std::string appNodeID,
                                            std::string powerID,
                                            std::string& realUserID,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
{
    return false;
    // return ZGSPUserManagerMng::instance()->verifyByCard(clientID, userID, authModeID, cardID, appNodeID, powerID, realUserID, e);
}

bool ZG6000::ZGSPUserManagerI::verifyByCardNoClient(std::string userID,
                                                    std::string authModeID,
                                                    std::string cardID,
                                                    std::string appNodeID,
                                                    std::string powerID,
                                                    std::string& realUserID,
                                                    ErrorInfo& e,
                                                    const Ice::Current& current)
{
    return false;
    // return ZGSPUserManagerMng::instance()->verifyByCardNoClient(userID, authModeID, cardID, appNodeID, powerID, realUserID, e);
}

bool ZG6000::ZGSPUserManagerI::verifyByVerifyCode(std::string clientID,
                                                  std::string phoneNumber,
                                                  std::string code,
                                                  std::string appNodeID,
                                                  std::string powerID,
                                                  ErrorInfo& e,
                                                  const Ice::Current& current)
{
    return false;
    // return ZGSPUserManagerMng::instance()->verifyByVerifyCode(clientID, phoneNumber, code, e);
}

void ZG6000::ZGSPUserManagerI::cancelAuth(std::string clientID,
                                          const Ice::Current& current)
{
    ZGSPUserManagerMng::instance()->cancelRequest(clientID);
}

bool ZG6000::ZGSPUserManagerI::loginByAuthDev(std::string clientID,
                                              std::string userID,
                                              std::string authModeID,
                                              int keepTime,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->loginByAuthDev(clientID, userID, authModeID, keepTime, e);
}

bool ZG6000::ZGSPUserManagerI::verifyByAuthDev(std::string clientID,
                                               std::string userID,
                                               std::string authModeID,
                                               std::string appNodeID,
                                               std::string powerID,
                                               ErrorInfo& e,
                                               const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->verifyByAuthDev(clientID, userID, authModeID, appNodeID, powerID, e);
}

bool ZG6000::ZGSPUserManagerI::verifyByAuthDevNoClient(std::string userID,
                                                       std::string authModeID,
                                                       std::string appNodeID,
                                                       std::string powerID,
                                                       ErrorInfo& e,
                                                       const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->verifyByAuthDevNoClient(userID, authModeID, appNodeID, powerID, e);
}

bool ZG6000::ZGSPUserManagerI::updateUserFace(std::string userID,
                                              std::string faceData,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->updateUserFace(userID, std::move(faceData), e);
}

bool ZG6000::ZGSPUserManagerI::deleteUserFace(std::string userID,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->deleteUserFace(userID, e);
}

bool ZG6000::ZGSPUserManagerI::updateUserFinger(std::string userID,
                                                int fingerNo,
                                                std::string fingerData,
                                                ErrorInfo& e,
                                                const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->updateUserFinger(userID, fingerNo, fingerData, e);
}

bool ZG6000::ZGSPUserManagerI::deleteUserFinger(std::string userID,
                                                int fingerNo,
                                                ErrorInfo& e,
                                                const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->deleteUserFinger(userID, fingerNo, e);
}

bool ZG6000::ZGSPUserManagerI::addUserCard(std::string userID,
                                           std::string cardNo,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->addUserCard(userID, cardNo, e);
}

bool ZG6000::ZGSPUserManagerI::deleteUserCard(std::string cardNo,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->deleteUserCard(cardNo, e);
}

bool ZG6000::ZGSPUserManagerI::sendRandomPassword(std::string userID,
                                                  ErrorInfo& e,
                                                  const Ice::Current& current)
{
    return ZGSPUserManagerMng::instance()->sendRandomPassword(userID, e);
}

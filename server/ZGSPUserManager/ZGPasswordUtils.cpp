#include "ZGPasswordUtils.h"
#include "ZGDebugMng.h"
#include <random>
#include <chrono>
#include <algorithm>
#include <cctype>

// 生成随机密码
std::string generatePassword(const PasswordOptions& options) {
    // 使用当前时间作为随机数种子
    std::mt19937 generator(static_cast<unsigned int>(
        std::chrono::system_clock::now().time_since_epoch().count()));  // 使用显式类型转换

    // 定义各类字符集
    const std::string uppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const std::string lowercaseChars = "abcdefghijklmnopqrstuvwxyz";
    const std::string digitChars = "0123456789";
    const std::string& specialChars = options.allowedSpecial;

    // 创建可用字符集
    std::string availableChars;
    if (options.requireUppercase) availableChars += uppercaseChars;
    if (options.requireLowercase) availableChars += lowercaseChars;
    if (options.requireDigits) availableChars += digitChars;
    if (options.requireSpecial) availableChars += specialChars;

    // 确定密码长度（至少为minLength）
    int passwordLength = options.minLength;

    // 计算必须包含的各类字符的最小总数
    int requiredChars = 0;
    if (options.requireUppercase) requiredChars += options.minUppercase;
    if (options.requireLowercase) requiredChars += options.minLowercase;
    if (options.requireDigits) requiredChars += options.minDigits;
    if (options.requireSpecial) requiredChars += options.minSpecial;

    // 如果必须包含的字符数量大于最小长度，则调整密码长度
    passwordLength = std::max(passwordLength, requiredChars);

    // 创建均匀分布，用于从可用字符中随机选择
    std::uniform_int_distribution<std::size_t> charDist(0, availableChars.length() - 1);

    // 生成初始密码
    std::string password;
    password.reserve(static_cast<std::size_t>(passwordLength));

    // 确保包含必要的各类字符
    if (options.requireUppercase && options.minUppercase > 0) {
        std::uniform_int_distribution<std::size_t> upperDist(0, uppercaseChars.length() - 1);
        for (int i = 0; i < options.minUppercase; ++i) {
            password += uppercaseChars[upperDist(generator)];
        }
    }

    if (options.requireLowercase && options.minLowercase > 0) {
        std::uniform_int_distribution<std::size_t> lowerDist(0, lowercaseChars.length() - 1);
        for (int i = 0; i < options.minLowercase; ++i) {
            password += lowercaseChars[lowerDist(generator)];
        }
    }

    if (options.requireDigits && options.minDigits > 0) {
        std::uniform_int_distribution<std::size_t> digitDist(0, digitChars.length() - 1);
        for (int i = 0; i < options.minDigits; ++i) {
            password += digitChars[digitDist(generator)];
        }
    }

    if (options.requireSpecial && options.minSpecial > 0) {
        std::uniform_int_distribution<std::size_t> specialDist(0, specialChars.length() - 1);
        for (int i = 0; i < options.minSpecial; ++i) {
            password += specialChars[specialDist(generator)];
        }
    }

    // 添加剩余的随机字符，直到达到所需的密码长度
    while (static_cast<int>(password.length()) < passwordLength) {
        password += availableChars[charDist(generator)];
    }

    // 打乱密码字符顺序，使其更随机
    std::shuffle(password.begin(), password.end(), generator);

    return password;
}

// 验证密码是否符合要求
PasswordValidationResult validatePassword(const std::string& password, const PasswordOptions& options) {
    PasswordValidationResult result;
    result.isValid = true; // 默认为有效，遇到不符合的要求时设为false

    // 检查密码长度
    if (static_cast<int>(password.length()) < options.minLength) {
        result.addError("密码长度不足，最小长度要求：" + std::to_string(options.minLength));
    }

    // 计数各类字符
    int uppercaseCount = 0;
    int lowercaseCount = 0;
    int digitCount = 0;
    int specialCount = 0;

    for (char c : password) {
        if (std::isupper(c)) {
            uppercaseCount++;
        } else if (std::islower(c)) {
            lowercaseCount++;
        } else if (std::isdigit(c)) {
            digitCount++;
        } else if (options.allowedSpecial.find(c) != std::string::npos) {
            specialCount++;
        }
    }

    // 检查各类字符要求
    if (options.requireUppercase && uppercaseCount < options.minUppercase) {
        result.addError(u8"至少需要" + std::to_string(options.minUppercase) + u8"个大写字母");
    }

    if (options.requireLowercase && lowercaseCount < options.minLowercase) {
        result.addError(u8"至少需要" + std::to_string(options.minLowercase) + u8"个小写字母");
    }

    if (options.requireDigits && digitCount < options.minDigits) {
        result.addError(u8"至少需要" + std::to_string(options.minDigits) + u8"个数字");
    }

    if (options.requireSpecial && specialCount < options.minSpecial) {
        result.addError(u8"至少需要" + std::to_string(options.minSpecial) + u8"个特殊字符");
    }

    return result;
}

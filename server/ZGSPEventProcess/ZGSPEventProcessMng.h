#ifndef ZGSPEVENTPROCESSMNG_H
#define ZGSPEVENTPROCESSMNG_H

#include <QTimer>
#include <QReadWriteLock>
#include <deque>
#include "ZGProxyMng.h"

struct ZGMPEvent
{
    ZG6000::StringList listAppNodeID;
    std::string isPublishEvent;
    ZG6000::StringMap event;
};

struct ZGSPEvent
{
    std::string isPublishEvent;
    std::string event;
};

class ZGMqttClient;
class ZGRedisClient;

class ZGSPEventProcessMng : public QObject
{
    Q_OBJECT

public:
    static ZGSPEventProcessMng* instance();
    void init();

    bool checkState();

    /**
     * @brief   处理监控平台事件
     *
     * @param   listEvent           事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param   listListAppNodeID   每个事件关联的应用节点ID列表的列表
     * @param   listIsPublishEvent  是否发布事件列表
     * @attention 该接口既可以处理数据集事件，也可以处理其他监控事件
     */
    void processZGMPEvents(ZG6000::ListStringMap& listEvent,
                           ZG6000::ListStringList& listListAppNodeID,
                           ZG6000::StringList& listIsPublishEvent);

    /**
     * @brief   处理监控平台数据集事件
     *
     * @param   listEvent       事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param   listAppNodeID   事件列表中的所有事件关联的应用节点ID列表
     * @param   listIsPublishEvent  是否发布事件列表
     * @attention 该接口只能用于处理数据集事件
     */
    void processZGMPDatasetEvents(ZG6000::ListStringMap& listEvent,
                                  ZG6000::StringList& listAppNodeID,
                                  ZG6000::StringList& listIsPublishEvent);

    /**
     * @brief   处理系统平台事件
     *
     * @param   listEvent           事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param   listIsPublishEvent  是否发布事件列表
     */
    void processZGSPEvents(ZG6000::ListStringMap& listEvent,
                           ZG6000::StringList& listIsPublishEvent);

    /**
     * @brief	处理设备管理平台事件
     *
     * @param [in,out]	listEvent		  	事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param [in,out]	listIsPublishEvent	是否发布事件列表
     */
    void processZGDPEvents(ZG6000::ListStringMap& listEvent,
                           ZG6000::StringList& listIsPublishEvent);

    /**
     * @brief	处理运维管理平台事件
     *
     * @param [in,out]	listEvent		  	事件列表，每个事件的字段值应与历史库对应数据表中的字段名保持一致（不包含ID）
     * @param [in,out]	listIsPublishEvent	是否发布事件列表
     */
    void processZGOPEvents(ZG6000::ListStringMap& listEvent,
                           ZG6000::StringList& listIsPublishEvent);

    /**
     * @brief	处理事件(其他服务调用)
     *
     * @param 	event	事件
     */
    void processEvent(ZG6000::StringMap event);

    /**
     * @brief	确认事件
     *
     * @param 		  	listEvent	事件列表
     * @param 		  	userID   	用户ID
     * @param 		  	userName 	用户名称
     * @param [in,out]	e		 	错误信息
     *
     * @return	调用成功返回true，失败返回false。
     */
    bool confirmEvents(const ZG6000::ListStringMap& listEvent,
                       const std::string& userID,
                       const std::string& userName,
                       ZG6000::ErrorInfo& e);

    /**
     * @brief	确认设备事件
     *
     * @param 		  	listEvent	事件列表
     * @param 		  	userID   	用户ID
     * @param 		  	userName 	用户名称
     * @param [in,out]	e		 	错误信息
     * @param 		  	current
     *
     * @return	调用成功返回true，失败返回false。
     */
    bool confirmDPEvent(ZG6000::ListStringMap listEvent,
                        std::string userID,
                        std::string userName,
                        ZG6000::ErrorInfo& e,
                        const Ice::Current& current);

    bool getUnconfirmedEvent(std::string appNodeID,
                        std::string subsystemID,
                        ZG6000::ListStringMap& listEvent,
                        ZG6000::ErrorInfo& e,
                        const Ice::Current& current);

    bool getUnconfirmedPosEvent(const std::string& appNodeID,
                            const std::string& subsystemID,
                            const std::string& position,
                            ZG6000::ListStringMap& listEvent,
                            ZG6000::ErrorInfo& e,
                            const Ice::Current& current);

    bool getUnconfirmedEventNum(ZG6000::StringMap& appNodeEventNum,
                                    ZG6000::ErrorInfo& e);

private:
    explicit ZGSPEventProcessMng(QObject* parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initAlarmLevel();
    bool initEventType();
    bool initAppNode();
    bool initMajor();
    bool initSubsystem();
    bool initDataCategory();
    bool initServerInstInfo();
    bool initRedisTopic();
    bool initMqttTopic();
    bool initParams();
    bool tryProcessEvent(const std::string& tableName,
                         ZG6000::ListStringMap& listEvent);
    void publishEvents(const std::unordered_map<std::string, ZG6000::ListStringMap>& mapDataEvents);
    void publishEvents(const std::string& topic,
                       const ZG6000::ListStringMap& listEvent);
    bool convertToPublishEvent(ZG6000::ListStringMap& listEvent,
                               ZG6000::ListStringList& listListAppNodeId,
                               ZG6000::StringList& listIsPublishEvent,
                               ZG6000::ListStringMap& listPublishEvent,
                               ZG6000::ListStringList& listPublishAppNodeId);
    bool convertToPublishEvent(ZG6000::ListStringMap& listEvent,
                               ZG6000::StringList& listIsPublishEvent,
                               ZG6000::ListStringMap& listPublishEvent);
    void allocateEventsToAppNode(const ZG6000::ListStringMap& listEvent,
                                 ZG6000::ListStringList& listListAppNodeId,
                                 std::unordered_map<std::string, ZG6000::ListStringMap>& mapDataEvents);
    void allocateEventsToAppNode(const ZG6000::ListStringMap& listEvent,
                                 ZG6000::StringList& listAppNodeID,
                                 std::unordered_map<std::string, ZG6000::ListStringMap>& mapDataEvents);
    void cacheZGMPEvents(ZG6000::ListStringMap& listEvent,
                         ZG6000::ListStringList& listListAppNodeID,
                         ZG6000::StringList& listIsPublishEvent);
    void checkAndSetWarnDO(const ZG6000::StringMap& record,
                           const QDateTime& currTime);
    void checkAndSetWarnGPIO(const ZG6000::StringMap& record,
                             const QDateTime& currTime);
    void checkAndResetWarnDO(const ZG6000::StringMap& record,
                             const QDateTime& currTime);
    void checkAndResetWarnGPIO(const ZG6000::StringMap& record,
                               const QDateTime& currTime);
    void sendYk(const std::string& ykID,
                const std::string& value);
    void sendGPIOCtrl(const std::string& gpioWarnTypeId,
                      int value);
    bool confirmEvents(const std::string& tableName,
                       const ZG6000::ListStringMap& listEvent,
                       const std::string& userID,
                       const std::string& userName,
                       const std::string& topicName,
                       ZG6000::ErrorInfo& e);
    ZG6000::StringList getAssocAppnodes(const std::string& appNodeID);
    bool generateEventID(ZG6000::ListStringMap& listEvent);
    void cachePublishEvents(ZG6000::ListStringMap& listPublishEvents);
    void confirmCachedEvents(const std::string& userID,
                             const std::string& userName,
                             const ZG6000::ListStringMap& listEvent);
    bool getUnconfirmEvents(ZG6000::ListStringMap& listEvent,
                            ZG6000::ErrorInfo& e);
    bool getAppNodePosEventNum(std::map<std::string, int>& mapAppnodePosEventNum,
                               ZG6000::ErrorInfo& e);

    void publishAppnodesEvents();

private slots:
    void onCheckStatus();

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    int m_initInterval{10};
    int m_checkInterval{5};
    int m_checkTickCount{0};
    QTimer m_initTimer;
    QTimer m_checkTimer;
    int m_tickCount{0};
    int m_currentStep{0};
    ZGRedisClient* m_pRedisRtQueue{nullptr};
    ZGRedisClient* m_pRedisRtTopic{nullptr};
    // ZGRedisClient* m_pRedisRtQueue{nullptr};
    ZGMqttClient* m_pMqttClient{nullptr};
    ZG6000::MapStringMap m_mapAlarmLevel;
    ZG6000::MapStringMap m_mapEventType;
    ZG6000::MapStringMap m_mapMajor;
    ZG6000::MapStringMap m_mapSubsystem;
    ZG6000::MapStringMap m_mapAppNode;
    ZG6000::MapStringMap m_mapDataCategory;
    std::deque<ZGMPEvent> m_queueZGMPEvent;
    std::deque<ZGSPEvent> m_queueZGSPEvent;
    ZG6000::MapStringMap m_mapDeviceParam;
    QReadWriteLock m_lock;
    QMutex m_mutex;
};

#endif // ZGSPEVENTPROCESSMNG_H

#include <ZGSPEventProcessI.h>
#include "ZGSPEventProcessMng.h"

ZG6000::ZGSPEventProcessI::ZGSPEventProcessI()
{
    ZGSPEventProcessMng::instance()->init();
}

bool ZG6000::ZGSPEventProcessI::checkState(const Ice::Current&)
{
    return ZGSPEventProcessMng::instance()->checkState();
}

void ZG6000::ZGSPEventProcessI::processZGMPEvents(ListStringMap listEvent,
                                                  ListStringList listListAppNodeID,
                                                  StringList listIsPublishEvent,
                                                  const Ice::Current&)
{
    ZGSPEventProcessMng::instance()->processZGMPEvents(listEvent, listListAppNodeID, listIsPublishEvent);
}

void ZG6000::ZGSPEventProcessI::processZGMPDatasetEvents(ListStringMap listEvent,
                                                         StringList listAppNodeID,
                                                         StringList listIsPublishEvent,
                                                         const Ice::Current&)
{
    ZGSPEventProcessMng::instance()->processZGMPDatasetEvents(listEvent, listAppNodeID, listIsPublishEvent);
}

void ZG6000::ZGSPEventProcessI::processZGSPEvents(ListStringMap listEvent,
                                                  StringList listIsPublishEvent,
                                                  const Ice::Current&)
{
    ZGSPEventProcessMng::instance()->processZGSPEvents(listEvent, listIsPublishEvent);
}

void ZG6000::ZGSPEventProcessI::processZGDPEvents(ListStringMap listEvent,
                                                  StringList listIsPublishEvent,
                                                  const Ice::Current& current)
{
    ZGSPEventProcessMng::instance()->processZGDPEvents(listEvent, listIsPublishEvent);
}

void ZG6000::ZGSPEventProcessI::processZGOPEvents(ListStringMap listEvent,
                                                  StringList listIsPublishEvent,
                                                  const Ice::Current& current)
{
    ZGSPEventProcessMng::instance()->processZGOPEvents(listEvent, listIsPublishEvent);
}

void ZG6000::ZGSPEventProcessI::processEvent(StringMap event,
                                             const Ice::Current& current)
{
    ZGSPEventProcessMng::instance()->processEvent(event);
}

bool ZG6000::ZGSPEventProcessI::confirmEvents(ListStringMap listEvent,
                                              std::string userID,
                                              std::string userName,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
{
    return ZGSPEventProcessMng::instance()->confirmEvents(listEvent, userID, userName, e);
}

bool ZG6000::ZGSPEventProcessI::confirmDPEvent(ListStringMap listEvent,
                                               std::string userID,
                                               std::string userName,
                                               ErrorInfo& e,
                                               const Ice::Current& current)
{
    return ZGSPEventProcessMng::instance()->confirmDPEvent(std::move(listEvent), userID, userName, e, current);
}

bool ZG6000::ZGSPEventProcessI::getUnconfirmedEvent(std::string appNodeID,
                                                    std::string subsystemID,
                                                    ListStringMap& listEvent,
                                                    ErrorInfo& e,
                                                    const Ice::Current& current)
{
    return ZGSPEventProcessMng::instance()->getUnconfirmedEvent(std::move(appNodeID), std::move(subsystemID),
        listEvent, e, current);
}

bool ZG6000::ZGSPEventProcessI::getUnconfirmedPosEvent(std::string appNodeID,
                                                       std::string subsystemID,
                                                       std::string position,
                                                       ListStringMap& listEvent,
                                                       ErrorInfo& e,
                                                       const Ice::Current& current)
{
    return ZGSPEventProcessMng::instance()->getUnconfirmedPosEvent(appNodeID, subsystemID,
        position, listEvent, e, current);
}

bool ZG6000::ZGSPEventProcessI::getUnconfirmedEventNum(StringMap& appNodeEventNum,
                                                       ErrorInfo& e,
                                                       const Ice::Current& current)
{
    return ZGSPEventProcessMng::instance()->getUnconfirmedEventNum(appNodeEventNum, e);
}

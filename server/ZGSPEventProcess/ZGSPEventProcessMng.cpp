#include "ZGSPEventProcessMng.h"

#include <QDateTime>
#include <QRandomGenerator>
#include <QJsonDocument>
#include <QtConcurrent>
#include <unordered_set>
#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "zgerror/ZGSPEventProcessError.h"

ZGSPEventProcessMng* g_pEventProcessMng = nullptr;

const int MAX_CACHED_MESSAGE_NUM = 200;

ZGSPEventProcessMng* ZGSPEventProcessMng::instance()
{
    if (g_pEventProcessMng == nullptr)
        g_pEventProcessMng = new ZGSPEventProcessMng;
    return g_pEventProcessMng;
}

void ZGSPEventProcessMng::init()
{
    initEvents();
    initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    QThread::sleep(QRandomGenerator::global()->bounded(1, 5));
    while (!initRedisTopic())
    {
        ZGLOG_ERROR("initRedisTopic error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initAlarmLevel())
    {
        ZGLOG_ERROR("initAlarmLevel error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initEventType())
    {
        ZGLOG_ERROR("initEventType error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initAppNode())
    {
        ZGLOG_ERROR("initAppNode error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initDataCategory())
    {
        ZGLOG_ERROR("initDataCategory error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initMajor())
    {
        ZGLOG_ERROR("initMajor error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initSubsystem())
    {
        ZGLOG_ERROR("initSubsystem error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initParams())
    {
        ZGLOG_ERROR("initParams error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initMqttTopic())
    {
        ZGLOG_ERROR("initMqttTopic error.");
        QThread::msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    ZGLOG_INFO("ZGSPEventProcess init finished");
    m_checkTimer.start(1000);
}

bool ZGSPEventProcessMng::initAlarmLevel()
{
    std::string sql = "SELECT * FROM sp_dict_alarm_level";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapAlarmLevel))
    {
        ZGLOG_ERROR("initAlarmLevel error.");
        return false;
    }
    return true;
}

bool ZGSPEventProcessMng::initEventType()
{
    std::string sql = "SELECT id, name, nameL2 FROM sp_dict_event_type";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapEventType))
    {
        ZGLOG_ERROR("initEventType error.");
        return false;
    }
    return true;
}

bool ZGSPEventProcessMng::initAppNode()
{
    std::string sql = "SELECT id, name, nameL2 FROM sp_param_appnode WHERE appNodeTypeID = 'ZG_AT_STATION'";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapAppNode))
    {
        ZGLOG_ERROR("initAppNode error.");
        return false;
    }
    return true;
}

bool ZGSPEventProcessMng::initMajor()
{
    std::string sql = "SELECT id, name, nameL2 FROM sp_param_major";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapMajor))
    {
        ZGLOG_ERROR("initMajor error.");
        return false;
    }
    return true;
}

bool ZGSPEventProcessMng::initSubsystem()
{
    std::string sql = "SELECT id, name, nameL2 FROM sp_param_subsystem";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapSubsystem))
    {
        ZGLOG_ERROR("initSubsystem error.");
        return false;
    }
    return true;
}

bool ZGSPEventProcessMng::initDataCategory()
{
    std::string sql = "SELECT id, name, nameL2 FROM mp_dict_data_category";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapDataCategory))
    {
        ZGLOG_ERROR("initDataCategory error.");
        return false;
    }
    return true;
}

bool ZGSPEventProcessMng::initRedisTopic()
{
    QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
    listClientType << ZGRuntime::REDIS_RT_TOPIC;
    listClientType << ZGRuntime::REDIS_RT_QUEUE;
    if (!ZGRuntime::instance()->initRedisClient(listClientType))
    {
        ZGLOG_ERROR("initRedisClient error.");
        return false;
    }
    m_pRedisRtTopic = ZGRuntime::instance()->getRedisClientRTTopic();
    if (m_pRedisRtTopic == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTTopic error.");
        return false;
    }
    m_pRedisRtQueue = ZGRuntime::instance()->getRedisClientRTQueue();
    if (m_pRedisRtQueue == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTQueue error.");
        return false;
    }
    QThread::sleep(5);
    ZGLOG_INFO(QString("redis connected: %1").arg(m_pRedisRtQueue->connected()));
    return true;
}

bool ZGSPEventProcessMng::initMqttTopic()
{
    if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
    {
        ZGLOG_ERROR("initMqttClient error.");
        return false;
    }
    m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
    if (m_pMqttClient == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage error.");
        return false;
    }
    m_pMqttClient->connectToHost();
    return true;
}

bool ZGSPEventProcessMng::initParams()
{
    QString sql = "SELECT id, position FROM mp_param_device WHERE isEnable = 1";
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapDeviceParam))
    {
        ZGLOG_ERROR("init device param error.");
        return false;
    }
    return true;
}

bool ZGSPEventProcessMng::tryProcessEvent(const std::string& tableName,
                                          ZG6000::ListStringMap& listEvent)
{
    QDateTime currTime = QDateTime::currentDateTime();
    for (size_t i = 0; i < listEvent.size(); ++i)
    {
        const auto& it = listEvent[i].find("alarmLevelID");
        const auto& alarmLevelID = it->second;
        if (alarmLevelID.empty())
            continue;
        const auto& pair = m_mapAlarmLevel.find(alarmLevelID);
        if (pair == m_mapAlarmLevel.end())
        {
            ZGLOG_WARN(QString("Can't find alarm level %1").arg(alarmLevelID.c_str()));
            continue;
        }
        checkAndSetWarnDO(pair->second, currTime);
        checkAndSetWarnGPIO(pair->second, currTime);
    }
    QDateTime now = QDateTime::currentDateTime();
    std::string realTableName = tableName + "_" + now.toString("yyyy").toStdString();
    ZGProxyCommon::insertDataByTable(realTableName, listEvent, true, true);
    return true;
}

void ZGSPEventProcessMng::publishEvents(const std::unordered_map<std::string, ZG6000::ListStringMap>& mapDataEvents)
{
    for (const auto& appNodeEvent : mapDataEvents)
    {
        std::string topic = appNodeEvent.first + "/event";
        if (appNodeEvent.second.empty())
            continue;
        const auto& json = ZGJson::convertToJson(appNodeEvent.second);
        ZGLOG_DEBUG(QString("%1:%2").arg(topic.c_str()).arg(json.c_str()));
        long long subscriberNum;
        std::string errMsg;
        if (!m_pRedisRtTopic->publish(topic, json, subscriberNum, errMsg))
            ZGLOG_ERROR(errMsg.c_str());
        m_pMqttClient->sendPublish(topic.c_str(), json.c_str());
    }
}

void ZGSPEventProcessMng::publishEvents(const std::string& topic,
                                        const ZG6000::ListStringMap& listEvent)
{
    const auto& json = ZGJson::convertToJson(listEvent);
    long long subscriberNum;
    std::string errMsg;
    if (!m_pRedisRtTopic->publish(topic, json, subscriberNum, errMsg))
        ZGLOG_ERROR(errMsg.c_str());
    m_pMqttClient->sendPublish(topic.c_str(), json.c_str());
}

void ZGSPEventProcessMng::onCheckStatus()
{
    QDateTime currTime = QDateTime::currentDateTime();
    for (auto& pair : m_mapAlarmLevel)
    {
        checkAndResetWarnDO(pair.second, currTime);
        checkAndResetWarnGPIO(pair.second, currTime);
    }
    // 每10秒检查并发布一次事件消息
    if (++m_tickCount >= 10)
    {
        m_tickCount = 0;
        auto future = QtConcurrent::run([this]()
        {
            publishAppnodesEvents();
        });
    }
}

bool ZGSPEventProcessMng::checkState()
{
    return m_initialized;
}

bool ZGSPEventProcessMng::convertToPublishEvent(ZG6000::ListStringMap& listEvent,
                                                ZG6000::ListStringList& listListAppNodeId,
                                                ZG6000::StringList& listIsPublishEvent,
                                                ZG6000::ListStringMap& listPublishEvent,
                                                ZG6000::ListStringList& listPublishAppNodeId)
{
    if ((listEvent.size() != listListAppNodeId.size()) || (listEvent.size() != listIsPublishEvent.size()))
    {
        ZGLOG_ERROR("Invalid input param.");
        return false;
    }
    for (size_t i = 0; i < listEvent.size(); ++i)
    {
        if (listIsPublishEvent[i] == "1")
        {
            listPublishEvent.emplace_back(std::move(listEvent[i]));
            listPublishAppNodeId.emplace_back(std::move(listListAppNodeId[i]));
        }
    }
    for (auto& publishEvent : listPublishEvent)
    {
        try
        {
            const auto& alarmLevelID = ZGUtils::get(publishEvent, "alarmLevelID");
            const auto& alarmLevel = m_mapAlarmLevel.find(alarmLevelID);
            if (alarmLevel != m_mapAlarmLevel.end())
            {
                publishEvent.insert({"isPlayFile", alarmLevel->second.at("isPlayFile")});
                publishEvent.insert({"isPlayTTS", alarmLevel->second.at("isPlayTTS")});
                publishEvent.insert({"playCount", alarmLevel->second.at("playCount")});
                publishEvent.insert({"color", alarmLevel->second.at("color")});
            }
            else
            {
                publishEvent.insert({"isPlayFile", "0"});
                publishEvent.insert({"isPlayTTS", "0"});
                publishEvent.insert({"playCount", "0"});
                publishEvent.insert({"color", ""});
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
    return true;
}

bool ZGSPEventProcessMng::convertToPublishEvent(ZG6000::ListStringMap& listEvent,
                                                ZG6000::StringList& listIsPublishEvent,
                                                ZG6000::ListStringMap& listPublishEvent)
{
    if (listEvent.size() != listIsPublishEvent.size())
    {
        ZGLOG_ERROR("Invalid input param.");
        return false;
    }
    for (size_t i = 0; i < listEvent.size(); ++i)
    {
        if (listIsPublishEvent[i] == "1")
        {
            listPublishEvent.emplace_back(listEvent[i]);
        }
        auto it = listEvent[i].find("playTTSTypeID");
        if (it != listEvent[i].end())
            listEvent[i].erase(it);
        it = listEvent[i].find("alarmColor");
        if (it != listEvent[i].end())
            listEvent[i].erase(it);
    }
    for (auto& publishEvent : listPublishEvent)
    {
        try
        {
            const auto& alarmLevelID = ZGUtils::get(publishEvent, "alarmLevelID");
            const auto& pair = m_mapAlarmLevel.find(alarmLevelID);
            if (pair != m_mapAlarmLevel.end())
            {
                const auto& alarmLevel = pair->second;
                publishEvent["isPlayFile"] = ZGUtils::get(alarmLevel, "isPlayFile", "0");
                publishEvent["isPlayTTS"] = ZGUtils::get(alarmLevel, "isPlayTTS", "0");
                publishEvent["playCount"] = ZGUtils::get(alarmLevel, "playCount", "0");
                publishEvent["color"] = ZGUtils::get(alarmLevel, "color", "");
            }
            else
            {
                publishEvent["isPlayFile"] = "0";
                publishEvent["isPlayTTS"] = "0";
                publishEvent["playCount"] = "0";
                publishEvent["color"] = "";
            }
            auto it = publishEvent.find("playTTSTypeID");
            if (it != publishEvent.end())
            {
                if (it->second == "ZG_PTT_NO_PLAY")
                {
                    publishEvent["isPlayTTS"] = "0";
                    publishEvent["playCount"] = "0";
                }
                if (it->second == "ZG_PTT_PLAY")
                {
                    publishEvent["isPlayTTS"] = "1";
                    publishEvent["playCount"] = "1";
                }
                publishEvent.erase(it);
            }
            it = publishEvent.find("alarmColor");
            if (it != publishEvent.end() && (!it->second.empty()))
            {
                publishEvent["color"] = it->second;
                publishEvent.erase(it);
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
    return true;
}

void ZGSPEventProcessMng::allocateEventsToAppNode(const ZG6000::ListStringMap& listEvent,
                                                  ZG6000::ListStringList& listListAppNodeId,
                                                  std::unordered_map<std::string, ZG6000::ListStringMap>& mapDataEvents)
{
    for (size_t i = 0; i < listEvent.size(); ++i)
    {
        for (size_t j = 0; j < listListAppNodeId[i].size(); ++j)
        {
            const auto& appNodeID = listListAppNodeId[i][j];
            mapDataEvents[appNodeID].push_back(listEvent[i]);
        }
    }
}

void ZGSPEventProcessMng::allocateEventsToAppNode(const ZG6000::ListStringMap& listEvent,
                                                  ZG6000::StringList& listAppNodeID,
                                                  std::unordered_map<std::string, ZG6000::ListStringMap>& mapDataEvents)
{
    for (const auto& appNodeId : listAppNodeID)
    {
        mapDataEvents.insert(std::make_pair(appNodeId, listEvent));
    }
}

void ZGSPEventProcessMng::cacheZGMPEvents(ZG6000::ListStringMap& listEvent,
                                          ZG6000::ListStringList& listListAppNodeID,
                                          ZG6000::StringList& listIsPublishEvent)
{
    if ((listEvent.size() != listListAppNodeID.size()) || (listEvent.size() != listIsPublishEvent.size()))
    {
        ZGLOG_ERROR("Invalid input param.");
        return;
    }
    for (size_t i = 0; i < listEvent.size(); ++i)
    {
        ZGMPEvent zgmpEvent;
        zgmpEvent.event = std::move(listEvent[i]);
        zgmpEvent.listAppNodeID = std::move(listListAppNodeID[i]);
        zgmpEvent.isPublishEvent = std::move(listIsPublishEvent[i]);
    }
}

void ZGSPEventProcessMng::checkAndSetWarnDO(const ZG6000::StringMap& record,
                                            const QDateTime& currTime)
{
    try
    {
        const auto& startWarnDO = ZGUtils::get(record, "startWarnDO");
        if (startWarnDO.empty())
            return;
        const auto& id = ZGUtils::get(record, "id");
        std::string rtWarnTimeDO;
        if (!ZGProxyCommon::getDataByField("sp_dict_alarm_level", id, "rtWarnTimeDO", rtWarnTimeDO))
        {
            ZGLOG_ERROR(QStringLiteral("获取告警等级'%1'控制输出更新时间失败").arg(id.c_str()));
            return;
        }
        if (rtWarnTimeDO.empty())
        {
            ZGLOG_DEBUG(QString("sendYk, id = %1").arg(startWarnDO.c_str()));
            sendYk(startWarnDO, "2");
        }
        const auto& endWarnDO = ZGUtils::get(record, "endWarnDO");
        if (!endWarnDO.empty())
        {
            const auto& rtNewWarnTime = ZGUtils::DateTimeToString(currTime).toStdString();
            ZGProxyCommon::updateDataByField("sp_dict_alarm_level", id, "rtWarnTimeDO", rtNewWarnTime);
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGSPEventProcessMng::checkAndSetWarnGPIO(const ZG6000::StringMap& record,
                                              const QDateTime& currTime)
{
    try
    {
        const auto& gpioWarnTypeID = ZGUtils::get(record, "gpioWarnTypeID");
        if (gpioWarnTypeID.empty())
            return;
        const auto& id = ZGUtils::get(record, "id");
        const auto& keepTimeWarnGPIO = ZGUtils::get(record, "keepTimeWarnGPIO");
        if (!keepTimeWarnGPIO.empty())
        {
            std::string rtWarnTimeGPIO;
            if (!ZGProxyCommon::getDataByField("sp_dict_alarm_level", id, "rtWarnTimeGPIO", rtWarnTimeGPIO))
            {
                ZGLOG_ERROR(QStringLiteral("获取告警等级'%1'GPIO更新时间失败").arg(id.c_str()));
                return;
            }
            if (rtWarnTimeGPIO.empty())
                sendGPIOCtrl(gpioWarnTypeID, 2);
            const auto& rtNewWarnTime = ZGUtils::DateTimeToString(currTime).toStdString();
            ZGProxyCommon::updateDataByField("sp_dict_alarm_level", id, "rtWarnTimeGPIO", rtNewWarnTime);
        }
        else
            sendGPIOCtrl(gpioWarnTypeID, 2);
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGSPEventProcessMng::checkAndResetWarnDO(const ZG6000::StringMap& record,
                                              const QDateTime& currTime)
{
    try
    {
        const auto& endWarnDO = ZGUtils::get(record, "endWarnDO");
        if (endWarnDO.empty())
            return;
        const auto& id = ZGUtils::get(record, "id");
        std::string rtWarnTimeDO;
        if (!ZGProxyCommon::getDataByField("sp_dict_alarm_level", id, {"rtWarnTimeDO"}, rtWarnTimeDO))
        {
            ZGLOG_ERROR(QStringLiteral("获取告警等级'%1'控制输出更新时间失败").arg(id.c_str()));
            return;
        }
        if (rtWarnTimeDO.empty())
            return;
        const auto& keepTimeWarnDO = ZGUtils::get(record, "keepTimeWarnDO");
        int keepTime = std::atoi(keepTimeWarnDO.c_str());
        QDateTime doWarnTime;
        if (!ZGUtils::StringToDateTime(rtWarnTimeDO.c_str(), doWarnTime))
        {
            ZGLOG_DEBUG(QStringLiteral("Invalid time format: '%1'").arg(rtWarnTimeDO.c_str()));
            return;
        }
        if (std::abs(doWarnTime.secsTo(currTime)) < keepTime)
            return;
        ZGLOG_DEBUG(QString("sendYk, id = %1").arg(endWarnDO.c_str()));
        sendYk(endWarnDO, "2");
        ZGProxyCommon::updateDataByField("sp_dict_alarm_level", id, "rtWarnTimeDO", "");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGSPEventProcessMng::checkAndResetWarnGPIO(const ZG6000::StringMap& record,
                                                const QDateTime& currTime)
{
    try
    {
        const auto& gpioWarnTypeID = ZGUtils::get(record, "gpioWarnTypeID");
        if (gpioWarnTypeID.empty())
            return;
        const auto& keepTimeWarnGPIO = ZGUtils::get(record, "keepTimeWarnGPIO");
        if (keepTimeWarnGPIO.empty())
            return;
        const auto& id = ZGUtils::get(record, "id");
        std::string rtWarnTimeGPIO;
        if (!ZGProxyCommon::getDataByField("sp_dict_alarm_level", id, {"rtWarnTimeGPIO"}, rtWarnTimeGPIO))
        {
            ZGLOG_ERROR(QStringLiteral("获取告警等级'%1'GPIO更新时间失败").arg(id.c_str()));
            return;
        }
        if (rtWarnTimeGPIO.empty())
            return;
        QDateTime goioWarnTime;
        if (!ZGUtils::StringToDateTime(rtWarnTimeGPIO.c_str(), goioWarnTime))
        {
            ZGLOG_DEBUG(QStringLiteral("Invalid time format: '%1'").arg(rtWarnTimeGPIO.c_str()));
            return;
        }
        int keepTime = std::atoi(keepTimeWarnGPIO.c_str());
        if (std::abs(goioWarnTime.secsTo(currTime)) < keepTime)
            return;
        sendGPIOCtrl(gpioWarnTypeID, 1);
        ZGProxyCommon::updateDataByField("sp_dict_alarm_level", id, "rtWarnTimeGPIO", "");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGSPEventProcessMng::sendYk(const std::string& ykID,
                                 const std::string& value)
{
    std::string sql =
        "SELECT mp_param_model_yk.isSelectCtrl FROM mp_param_model_yk LEFT JOIN mp_param_dataset_yk ON mp_param_dataset_yk.dataModelID = mp_param_model_yk.id "
        "WHERE mp_param_dataset_yk.id = '" + ykID + "'";
    std::string result;
    if (!ZGProxyCommon::execQuerySqlField(sql, result))
    {
        ZGLOG_ERROR("execQuerySqlField error.");
        return;
    }
    QString commandID = "ZG_DC_YK_EXEC";
    if (result == "1")
        commandID = "ZG_DC_YK_SELECT";
    QJsonArray array;
    QJsonObject object;
    object["id"] = ykID.c_str();
    object["commandID"] = commandID;
    object["srcType"] = "auto";
    object["srcID"] = "-1";
    object["rtCode"] = QString::number(ZGUtils::genNumber(1, 100000));
    object["rtValue"] = value.c_str();
    object["rtCommandTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
    array.append(object);
    QJsonDocument doc(array);
    long long size;
    QString errMsg;
    m_pRedisRtQueue->rpush("ZG_Q_SYSTEM_YK", doc.toJson(QJsonDocument::Compact), size, errMsg);
}

void ZGSPEventProcessMng::sendGPIOCtrl(const std::string& gpioWarnTypeId,
                                       int value)
{
    auto gpioPrx = ZGProxyMng::instance()->getProxySPGPIOServer();
    if (gpioPrx == nullptr)
    {
        ZGLOG_ERROR(u8"获取GPIO服务代理对象失败");
        return;
    }
    try
    {
        ZGLOG_DEBUG(QString("setPinState, type = %1, value = %2").arg(gpioWarnTypeId.c_str()).arg(value));
        if (!gpioPrx->setPinState(gpioWarnTypeId, value))
            ZGLOG_ERROR(QStringLiteral("设置告警类型'%1'值失败").arg(gpioWarnTypeId.c_str()));
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

bool ZGSPEventProcessMng::confirmEvents(const std::string& tableName,
                                        const ZG6000::ListStringMap& listEvent,
                                        const std::string& userID,
                                        const std::string& userName,
                                        const std::string& topicName,
                                        ZG6000::ErrorInfo& e)
{
    try
    {
        ZG6000::StringList listSql;
        for (const auto& event : listEvent)
        {
            const auto& id = ZGUtils::get(event, "id");
            const auto& eventTime = ZGUtils::get(event, "eventTime");
            QDateTime dateTime;
            if (!ZGUtils::StringToDateTime(eventTime.c_str(), dateTime))
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPEventProcess::ZG_ERR_INTERNAL);
                e.errDetail = u8"无效的事件日期: '" + eventTime + u8"', 事件ID: '" + id + "'";
                ZGLOG_WARN(e);
                dateTime = QDateTime::currentDateTime();
            }
            QString wholeTableName = QString("%1_%2").arg(tableName.c_str()).arg(dateTime.toString("yyyy"));
            QString confirmTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
            QString sql = QString(
                              "UPDATE %1 SET isConfirm = '1', confirmUserID = '%2', confirmUserName = '%3', confirmTime = '%4' WHERE id = '%5'")
                          .arg(wholeTableName).arg(userID.c_str()).arg(userName.c_str()).arg(confirmTime).arg(
                              id.c_str());
            listSql.push_back(sql.toStdString());
        }
        if (!ZGProxyCommon::execBatchSql(listSql, true))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPEventProcess::ZG_ERR_INTERNAL);
            e.errDetail = u8"更新历史事件确认信息失败";
            ZGLOG_ERROR(e);
            return false;
        }
        std::string json = ZGJson::convertToJson(listEvent);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        QJsonArray array = doc.array();
        QJsonObject obj;
        obj["userID"] = userID.c_str();
        obj["userName"] = userName.c_str();
        obj["events"] = array;
        QJsonDocument publicDoc(obj);
        long long subscriberNum;
        QString errMsg;
        if (!m_pRedisRtTopic->publish(topicName.c_str(), publicDoc.toJson(), subscriberNum, errMsg))
            ZGLOG_ERROR(errMsg);
        m_pMqttClient->sendPublish(topicName.c_str(), publicDoc.toJson());
        confirmCachedEvents(userID, userName, listEvent);
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPEventProcess::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

ZG6000::StringList ZGSPEventProcessMng::getAssocAppnodes(const std::string& appNodeID)
{
    ZG6000::StringList listAppNode;
    listAppNode.push_back(appNodeID);
    std::string sql = "SELECT parentAppNodeID FROM sp_param_appnode_layer WHERE appNodeID = '" + appNodeID + "'";
    ZG6000::StringList listParentAppNodeID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listParentAppNodeID))
        return listAppNode;
    for (const auto& parentAppNodeID : listParentAppNodeID)
    {
        ZG6000::StringList listAncestorAppNodeID = getAssocAppnodes(parentAppNodeID);
        std::move(listAncestorAppNodeID.begin(), listAncestorAppNodeID.end(), std::back_inserter(listAppNode));
    }
    return listAppNode;
}

bool ZGSPEventProcessMng::generateEventID(ZG6000::ListStringMap& listEvent)
{
    ZG6000::StringList listUUID;
    if (!ZGProxyCommon::createUUID(static_cast<int>(listEvent.size()), listUUID, true))
    {
        ZGLOG_ERROR("create uuid error");
        return false;
    }
    for (int i = 0; i < listEvent.size(); ++i)
    {
        listEvent[i]["id"] = listUUID[i];
    }
    return true;
}

void ZGSPEventProcessMng::cachePublishEvents(ZG6000::ListStringMap& listPublishEvents)
{
    QWriteLocker locker(&m_lock);
    for (auto& event : listPublishEvents)
    {
        event["isConfirm"] = "0";
        const auto& json = ZGJson::convertToJson(event);
        if (json.empty())
            continue;
        ZGLOG_TRACE(json.c_str());
        long long size;
        QString errMsg;
        if (!m_pRedisRtQueue->lpush("ZG_Q_SYSTEM_EVENT", json.c_str(), size, errMsg))
            ZGLOG_ERROR(errMsg);
        if (size > MAX_CACHED_MESSAGE_NUM)
            m_pRedisRtQueue->ltrim("ZG_Q_SYSTEM_EVENT", 0, MAX_CACHED_MESSAGE_NUM - 1, errMsg);
    }
}

void ZGSPEventProcessMng::confirmCachedEvents(const std::string& userID,
                                              const std::string& userName,
                                              const ZG6000::ListStringMap& listEvent)
{
    QWriteLocker locker(&m_lock);
    QString errMsg;
    // 从redis缓存中获取所有事件，调用lrange命令
    QStringList listCachedEvent;
    // 从队列中单条获取数据，防止服务器切换导致写入队列数据异常时获取所有队列数据导致阻塞问题
    for (int i = 0; i < MAX_CACHED_MESSAGE_NUM; ++i)
    {
        QStringList partValues;
        m_pRedisRtQueue->lrange("ZG_Q_SYSTEM_EVENT", i, i, partValues, errMsg);
        if (partValues.empty())
            break;
        listCachedEvent.push_back(partValues[0]);
    }
    // 删除所有事件
    long long number;
    if (!m_pRedisRtQueue->del("ZG_Q_SYSTEM_EVENT", number, errMsg))
    {
        ZGLOG_ERROR(errMsg);
        return;
    }
    ZGLOG_TRACE(QString("delete %1 keys").arg(number));
    // 重新添加未被确认的事件
    try
    {
        std::unordered_set<std::string> eventIds;
        for (const auto& event : listEvent)
        {
            if (const auto& it = event.find("id"); it != event.end())
            {
                eventIds.insert(it->second);
            }
        }
        for (const auto& cachedEventJson : listCachedEvent)
        {
            bool shouldKeep = true;
            for (const auto& eventId : eventIds)
            {
                if (cachedEventJson.contains(eventId.c_str()))
                {
                    shouldKeep = false;
                    break;
                }
            }
            if (shouldKeep)
            {
                std::string errorString;
                m_pRedisRtQueue->rpush("ZG_Q_SYSTEM_EVENT", cachedEventJson.toStdString(), number, errorString);
            }
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

bool ZGSPEventProcessMng::getUnconfirmEvents(ZG6000::ListStringMap& listEvent,
                                             ZG6000::ErrorInfo& e)
{
    QDateTime currTime = QDateTime::currentDateTime();
    QDateTime startTime = currTime.addDays(-1);
    QString startYear = startTime.toString("yyyy");
    QString currYear = currTime.toString("yyyy");

    auto dbProxy = ZGProxyMng::instance()->getProxySPDBDataHis();
    if (dbProxy == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPEventProcess::ZG_ERR_INTERNAL);
        e.errDetail = u8"获取数据服务代理对象失败";
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        // 准备查询语句模板
        QString sqlTemplate;
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
            sqlTemplate = QString("SELECT TOP 1000 * FROM sp_his_event_%1 "
                "WHERE isConfirm IS NULL OR isConfirm = 0 ORDER BY eventTime DESC");
        else
            // MySQL数据库查询前1000条未确认事件
            sqlTemplate = QString("SELECT * FROM sp_his_event_%1 "
                "WHERE isConfirm IS NULL OR isConfirm = 0 ORDER BY eventTime DESC LIMIT 1000");
        // 先查询所有数据
        if (startYear == currYear)
        {
            // 如果是同一年，只需查询一次
            QString sql = sqlTemplate.arg(currYear);
            ZGLOG_TRACE(sql);
            if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listEvent, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
        }
        else
        {
            // 跨年情况，分别查询
            QString sqlStartYear = sqlTemplate.arg(startYear);
            QString sqlCurrYear = sqlTemplate.arg(currYear);

            // 使用UNION ALL合并两个查询，并在最外层进行排序
            QString sql;
            if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
                sql = QString("SELECT TOP 1000 * FROM (%1 UNION ALL %2) AS combined_events")
                          .arg(sqlStartYear).arg(sqlCurrYear);
            else
                sql = QString("SELECT * FROM ((%1) UNION ALL (%2)) AS combined_events LIMIT 1000")
                          .arg(sqlStartYear).arg(sqlCurrYear);
            ZGLOG_TRACE(sql);
            if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listEvent, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
        }
        for (auto& event: listEvent)
        {
            const auto& alarmLevelID = ZGUtils::get(event, "alarmLevelID");
            const auto& pair = m_mapAlarmLevel.find(alarmLevelID);
            if (pair != m_mapAlarmLevel.end())
            {
                const auto& alarmLevel = pair->second;
                event["isPlayFile"] = ZGUtils::get(alarmLevel, "isPlayFile", "0");
                event["isPlayTTS"] = ZGUtils::get(alarmLevel, "isPlayTTS", "0");
                event["playCount"] = ZGUtils::get(alarmLevel, "playCount", "0");
                event["color"] = ZGUtils::get(alarmLevel, "color", "");
            }
            else
            {
                event["isPlayFile"] = "0";
                event["isPlayTTS"] = "0";
                event["playCount"] = "0";
                event["color"] = "";
            }
        }
        return true;
    }
    catch (const Ice::Exception& ie)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPEventProcess::ZG_ERR_INTERNAL);
        e.errDetail = ie.what();
        ZGLOG_ERROR(e);
        return false;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPEventProcess::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPEventProcessMng::getUnconfirmedEventNum(ZG6000::StringMap& appNodeEventNum,
                                                 ZG6000::ErrorInfo& e)
{
    std::map<std::string, int> mapAppnodePosEventNum;
    if (!getAppNodePosEventNum(mapAppnodePosEventNum, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    for (const auto& pair : mapAppnodePosEventNum)
    {
        appNodeEventNum[pair.first] = std::to_string(pair.second);
    }
    return true;
}

bool ZGSPEventProcessMng::getAppNodePosEventNum(std::map<std::string, int>& mapAppnodePosEventNum,
                                                ZG6000::ErrorInfo& e)
{
    ZG6000::ListStringMap listEvent;
    if (!getUnconfirmEvents(listEvent, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    // 先将所有应用节点上下行的事件数量初始化为0
    for (const auto& [appNodeID, appNodeParam] : m_mapAppNode)
    {
        std::string appNodePos = appNodeID + "/up";
        mapAppnodePosEventNum[appNodePos] = 0;
        appNodePos = appNodeID + "/down";
        mapAppnodePosEventNum[appNodePos] = 0;
    }
    for (auto&& event : listEvent)
    {
        const auto& appNodeID = event["srcNodeID"];
        const auto& deviceID = event["deviceID"];
        const auto& eventPowers = event["eventPowers"];
        // 如果在eventPowers中没有找到ZG_HP_PSC权限，则不发布
        if (eventPowers.find("ZG_HP_PSC") == std::string::npos)
            continue;
        // 从设备参数中获取位置信息
        auto pairDevice = m_mapDeviceParam.find(deviceID);
        if (pairDevice == m_mapDeviceParam.end())
            continue;
        const auto& deviceParam = pairDevice->second;
        const auto& position = ZGUtils::get(deviceParam, "position");
        const auto& appNodePos = appNodeID + "/" + position;
        mapAppnodePosEventNum[appNodePos]++;
    }
    return true;
}

void ZGSPEventProcessMng::publishAppnodesEvents()
{
    QMutexLocker locker(&m_mutex);
    std::map<std::string, int> mapAppnodePosEventNum;
    ZG6000::ErrorInfo e;
    if (!getAppNodePosEventNum(mapAppnodePosEventNum, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    // 按照应用节点接地位置发布事件数量
    for (const auto& pair : mapAppnodePosEventNum)
    {
        std::string topic = "ZG_APPNODE_POS_EVENT/" + pair.first + "/eventNum";
        m_pMqttClient->sendPublish(topic.c_str(), std::to_string(pair.second).c_str());
    }
}

void ZGSPEventProcessMng::processZGMPEvents(ZG6000::ListStringMap& listEvent,
                                            ZG6000::ListStringList& listListAppNodeID,
                                            ZG6000::StringList& listIsPublishEvent)
{
    if (!generateEventID(listEvent))
        return;
    if (tryProcessEvent("sp_his_event", listEvent))
    {
        ZG6000::ListStringMap listPublishEvent;
        ZG6000::ListStringList listPublishAppNodeID;
        if (!convertToPublishEvent(listEvent, listListAppNodeID, listIsPublishEvent, listPublishEvent,
            listPublishAppNodeID))
            return;
        std::unordered_map<std::string, ZG6000::ListStringMap> mapDataEvents;
        allocateEventsToAppNode(listPublishEvent, listPublishAppNodeID, mapDataEvents);
        publishEvents(mapDataEvents);
    }
}

void ZGSPEventProcessMng::processZGMPDatasetEvents(ZG6000::ListStringMap& listEvent,
                                                   ZG6000::StringList& listAppNodeID,
                                                   ZG6000::StringList& listIsPublishEvent)
{
    // 先转换，将模型中的playTTSTypeID和alarmColor字段去除
    if (!generateEventID(listEvent))
        return;
    for (auto& event : listEvent)
    {
        event["appNodes"] = ZGUtils::join(listAppNodeID, ",");
    }
    ZG6000::ListStringMap listPublishEvent;
    if (!convertToPublishEvent(listEvent, listIsPublishEvent, listPublishEvent))
        return;
    if (tryProcessEvent("sp_his_event", listEvent))
    {
        std::unordered_map<std::string, ZG6000::ListStringMap> mapDataEvents;
        allocateEventsToAppNode(listPublishEvent, listAppNodeID, mapDataEvents);
        publishEvents(mapDataEvents);
        // // 将每个事件的应用节点ID列表加入到事件中
        // for (auto& publishEvent : listPublishEvent)
        // {
        //     publishEvent["appNodes"] = ZGUtils::join(listAppNodeID, ",");
        // }
        cachePublishEvents(listPublishEvent);
    }
}

void ZGSPEventProcessMng::processZGSPEvents(ZG6000::ListStringMap& listEvent,
                                            ZG6000::StringList& listIsPublishEvent)
{
    if (!generateEventID(listEvent))
        return;
    if (tryProcessEvent("sp_his_event", listEvent))
    {
        ZG6000::ListStringMap listPublishEvent;
        if (!convertToPublishEvent(listEvent, listIsPublishEvent, listPublishEvent))
            return;
        if (listPublishEvent.empty())
            return;
        publishEvents("ZG_T_SYS_EVENT", listPublishEvent);
        cachePublishEvents(listPublishEvent);
    }
}

void ZGSPEventProcessMng::processZGDPEvents(ZG6000::ListStringMap& listEvent,
                                            ZG6000::StringList& listIsPublishEvent)
{
    if (!generateEventID(listEvent))
        return;
    if (tryProcessEvent("sp_his_event", listEvent))
    {
        ZG6000::ListStringMap listPublishEvent;
        if (!convertToPublishEvent(listEvent, listIsPublishEvent, listPublishEvent))
            return;
        publishEvents("ZG_T_DP_EVENT", listPublishEvent);
    }
}

void ZGSPEventProcessMng::processZGOPEvents(ZG6000::ListStringMap& listEvent,
                                            ZG6000::StringList& listIsPublishEvent)
{
    if (!generateEventID(listEvent))
        return;
    if (tryProcessEvent("sp_his_event", listEvent))
    {
        ZG6000::ListStringMap listPublishEvent;
        if (!convertToPublishEvent(listEvent, listIsPublishEvent, listPublishEvent))
            return;
        publishEvents("ZG_T_OP_EVENT", listPublishEvent);
    }
}

void ZGSPEventProcessMng::processEvent(ZG6000::StringMap event)
{
    if (const auto& it = event.find("isChangeStore"); it != event.end())
    {
        std::string isChangeStore = it->second;
        event.erase(it);
        if (isChangeStore != "1")
            return;
    }
    if (const auto& it = event.find("eventTypeID"); it != event.end())
    {
        if (const auto& itEvent = m_mapEventType.find(it->second); itEvent != m_mapEventType.end())
        {
            event.insert(std::make_pair("eventTypeName", itEvent->second["name"]));
            event.insert(std::make_pair("eventTypeNameL2", itEvent->second["nameL2"]));
        }
        else
        {
            event.insert(std::make_pair("eventTypeName", ""));
            event.insert(std::make_pair("eventTypeNameL2", ""));
        }
    }
    if (const auto& it = event.find("alarmLevelID"); it != event.end())
    {
        if (const auto& itAlarmLevel = m_mapAlarmLevel.find(it->second); itAlarmLevel != m_mapAlarmLevel.end())
        {
            event.insert(std::make_pair("alarmLevelName", itAlarmLevel->second["name"]));
            event.insert(std::make_pair("alarmLevelNameL2", itAlarmLevel->second["nameL2"]));
        }
        else
        {
            event.insert(std::make_pair("alarmLevelName", ""));
            event.insert(std::make_pair("alarmLevelNameL2", ""));
        }
    }
    if (const auto& it = event.find("appNodeID"); it != event.end())
    {
        std::string appNodeID = it->second;
        event.erase(it);
        event.insert(std::make_pair("srcNodeID", appNodeID));
        if (const auto& itNode = m_mapAppNode.find(appNodeID); itNode != m_mapAppNode.end())
        {
            event.insert(std::make_pair("srcNodeName", itNode->second["name"]));
            event.insert(std::make_pair("srcNodeNameL2", itNode->second["nameL2"]));
        }
        else
        {
            event.insert(std::make_pair("srcNodeName", ""));
            event.insert(std::make_pair("srcNodeNameL2", ""));
        }
    }
    if (const auto& it = event.find("dataCategoryID"); it != event.end())
    {
        if (const auto& itDataCategory = m_mapDataCategory.find(it->second); itDataCategory != m_mapDataCategory.end())
        {
            event.insert(std::make_pair("dataCategoryName", itDataCategory->second["name"]));
            event.insert(std::make_pair("dataCategoryNameL2", itDataCategory->second["nameL2"]));
        }
        else
        {
            event.insert(std::make_pair("dataCategoryName", ""));
            event.insert(std::make_pair("dataCategoryNameL2", ""));
        }
    }
    if (const auto& it = event.find("majorID"); it != event.end())
    {
        if (const auto& itMajor = m_mapMajor.find(it->second); itMajor != m_mapMajor.end())
        {
            event.insert(std::make_pair("majorName", itMajor->second["name"]));
            event.insert(std::make_pair("majorNameL2", itMajor->second["nameL2"]));
        }
        else
        {
            event.insert(std::make_pair("majorName", ""));
            event.insert(std::make_pair("majorNameL2", ""));
        }
    }
    if (const auto& it = event.find("subsystemID"); it != event.end())
    {
        if (const auto& itSubsystem = m_mapSubsystem.find(it->second); itSubsystem != m_mapSubsystem.end())
        {
            event.insert(std::make_pair("subsystemName", itSubsystem->second["name"]));
            event.insert(std::make_pair("subsystemNameL2", itSubsystem->second["nameL2"]));
        }
        else
        {
            event.insert(std::make_pair("subsystemName", ""));
            event.insert(std::make_pair("subsystemNameL2", ""));
        }
    }
    ZG6000::StringList listIsPublishEvent;
    if (const auto& it = event.find("isPublishEvent"); it != event.end())
    {
        listIsPublishEvent.push_back(it->second);
        event.erase(it);
    }
    else
        listIsPublishEvent.push_back("1");
    ZG6000::ListStringMap listEvent{event};
    ZG6000::ListStringMap listPublishEvent;
    if (!generateEventID(listEvent))
        return;
    for (auto& aEvent : listEvent)
    {
        if (aEvent.find("srcNodeID") != aEvent.end() &&
            aEvent.find("subsystemID") != aEvent.end())
        {
            const auto& appNodeID = ZGUtils::get(aEvent, "srcNodeID");
            ZG6000::StringList listAppNode = getAssocAppnodes(appNodeID);
            const auto& subsystemID = ZGUtils::get(aEvent, "subsystemID");
            ZG6000::StringList listAppNodeSubsystem;
            for (const auto& appNode : listAppNode)
            {
                if (!appNode.empty())
                    listAppNodeSubsystem.push_back(appNode + "/" + subsystemID);
            }
            aEvent["appNodes"] = ZGUtils::join(listAppNodeSubsystem, ",");
        }
    }
    if (!convertToPublishEvent(listEvent, listIsPublishEvent, listPublishEvent))
        return;
    if (tryProcessEvent("sp_his_event", listEvent))
    {
        for (auto& publishEvent : listPublishEvent)
        {
            if (publishEvent.find("srcNodeID") != publishEvent.end() &&
                publishEvent.find("subsystemID") != publishEvent.end())
            {
                const auto& appNodes = ZGUtils::get(publishEvent, "appNodes");
                // 分解出应用节点子系统列表
                ZG6000::StringList listAppNode;
                const auto& topicSize = ZGUtils::splitString(appNodes, ",", listAppNode);
                if (topicSize == 0)
                    continue;
                for (const auto& appNode : listAppNode)
                {
                    std::string topicName = appNode + "/event";
                    publishEvents(topicName, {publishEvent});
                }
            }
            else
                publishEvents("ZG_T_SYS_EVENT", {publishEvent});
        }
        cachePublishEvents(listPublishEvent);
    }
}

bool ZGSPEventProcessMng::confirmEvents(const ZG6000::ListStringMap& listEvent,
                                        const std::string& userID,
                                        const std::string& userName,
                                        ZG6000::ErrorInfo& e)
{
    return confirmEvents("sp_his_event", listEvent, userID, userName, "ZG_T_SYS_EVENT_CONFIRM", e);
}

bool ZGSPEventProcessMng::confirmDPEvent(ZG6000::ListStringMap listEvent,
                                         std::string userID,
                                         std::string userName,
                                         ZG6000::ErrorInfo& e,
                                         const Ice::Current& current)
{
    return confirmEvents("sp_his_event", listEvent, userID, userName, "ZG_T_DP_EVENT_CONFIRM", e);
}

bool ZGSPEventProcessMng::getUnconfirmedEvent(std::string appNodeID,
                                              std::string subsystemID,
                                              ZG6000::ListStringMap& listEvent,
                                              ZG6000::ErrorInfo& e,
                                              const Ice::Current& current)
{
    ZG6000::ListStringMap listUnconfirmEvent;
    // 获取所有未确认事件
    if (!getUnconfirmEvents(listUnconfirmEvent, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    // 从未确认事件中找出应用节点相同的事件，加入listEvent中
    for (auto&& event : listUnconfirmEvent)
    {
        const auto& appNodeSubsystem = appNodeID + "/" + subsystemID;
        if (event["appNodes"].find(appNodeSubsystem) != std::string::npos)
            listEvent.push_back(std::move(event));
    }
    return true;
}

bool ZGSPEventProcessMng::getUnconfirmedPosEvent(const std::string& appNodeID,
                                                 const std::string& subsystemID,
                                                 const std::string& position,
                                                 ZG6000::ListStringMap& listEvent,
                                                 ZG6000::ErrorInfo& e,
                                                 const Ice::Current& current)
{
    ZG6000::ListStringMap listTotleEvent;
    if (!getUnconfirmEvents(listTotleEvent, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        for (auto&& event : listTotleEvent)
        {
            if (event["srcNodeID"] == appNodeID && event["subsystemID"] == subsystemID)
            {
                const auto& deviceID = event["deviceID"];
                auto pairDevice = m_mapDeviceParam.find(deviceID);
                if (pairDevice == m_mapDeviceParam.end())
                    continue;
                const auto eventPowers = event["eventPowers"];
                // 如果eventPowers中包含ZG_HP_PSC权限，则发布
                if (eventPowers.find("ZG_HP_PSC") == std::string::npos)
                    continue;
                const auto& deviceParam = pairDevice->second;
                // 从设备参数中获取position
                const auto& devicePosition = ZGUtils::get(deviceParam, "position");
                if (devicePosition == position)
                    listEvent.push_back(std::move(event));
            }
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPEventProcess::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

// bool ZGSPEventProcessMng::getCachedEvent(std::string appNodeID,
//                                          std::string subsystemID,
//                                          std::string majorID,
//                                          ZG6000::ListStringMap& listEvent,
//                                          ZG6000::ErrorInfo& e,
//                                          const Ice::Current& current)
// {
//     // 从redis缓存中获取所有事件，调用lrange命令
//     ZG6000::StringList values;
//     std::string errMsg;
//     // 从队列中单条获取数据，防止服务器切换导致写入队列数据异常时获取所有队列数据导致阻塞问题
//     {
//         QReadLocker locker(&m_lock);
//         ZGLOG_INFO(QString("begin getCachedEvent ") + QString::number(reinterpret_cast<quint64>(QThread::currentThreadId())));
//         for (int i = 0; i < MAX_CACHED_MESSAGE_NUM; ++i)
//         {
//             ZG6000::StringList partValues;
//             m_pRedisRtQueue->lrange("ZG_Q_SYSTEM_EVENT", i, i, partValues, errMsg);
//             if (partValues.empty())
//                 break;
//             values.push_back(partValues[0]);
//         }
//         ZGLOG_INFO(QString("end getCachedEvent") + QString::number(reinterpret_cast<quint64>(QThread::currentThreadId())));
//     }
//     for (const auto& value : values)
//     {
//         ZG6000::StringMap event;
//         if (!ZGJson::convertFromJson(value, event, errMsg))
//         {
//             ZGLOG_ERROR(errMsg.c_str());
//             continue;
//         }
//         if (const auto& it = event.find("appNodes"); it != event.end())
//         {
//             ZG6000::StringList listAppNode;
//             const auto& size = ZGUtils::splitString(it->second, ",", listAppNode);
//             if (std::find(listAppNode.begin(), listAppNode.end(), appNodeID + "/" + subsystemID) != listAppNode.end())
//                 listEvent.push_back(event);
//         }
//         else
//             listEvent.push_back(event);
//     }
//     return true;
// }

ZGSPEventProcessMng::ZGSPEventProcessMng(QObject* parent) : QObject(parent)
{
}

void ZGSPEventProcessMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPEventProcessMng::onCheckStatus);
}

void ZGSPEventProcessMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGSPEventProcessMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

#ifndef __ZGSPEventProcessI_h__
#define __ZGSPEventProcessI_h__

#include <ZGSPEventProcess.h>

namespace ZG6000
{
    class ZGSPEventProcessI : public virtual ZGSPEventProcess
    {
    public:
        ZGSPEventProcessI();

        bool checkState(const Ice::Current&) override;

        void processZGMPEvents(ListStringMap,
                               ListStringList,
                               StringList,
                               const Ice::Current&) override;

        void processZGMPDatasetEvents(ListStringMap,
                                      StringList,
                                      StringList,
                                      const Ice::Current&) override;

        void processZGSPEvents(ListStringMap,
                               StringList,
                               const Ice::Current&) override;

        void processZGDPEvents(ListStringMap listEvent,
                               StringList listIsPublishEvent,
                               const Ice::Current& current) override;

        void processZGOPEvents(ListStringMap listEvent,
                               StringList listIsPublishEvent,
                               const Ice::Current& current) override;

        void processEvent(StringMap event,
                          const Ice::Current& current) override;

        bool confirmEvents(ListStringMap listEvent,
                           std::string userID,
                           std::string userName,
                           ErrorInfo& e,
                           const Ice::Current& current) override;

        bool confirmDPEvent(ListStringMap listEvent,
                            std::string userID,
                            std::string userName,
                            ErrorInfo& e,
                            const Ice::Current& current) override;

        bool getUnconfirmedEvent(std::string appNodeID,
                                 std::string subsystemID,
                                 ListStringMap& listEvent,
                                 ErrorInfo& e,
                                 const Ice::Current& current) override;

        bool getUnconfirmedPosEvent(std::string appNodeID,
                                    std::string subsystemID,
                                    std::string position,
                                    ListStringMap& listEvent,
                                    ErrorInfo& e,
                                    const Ice::Current& current) override;

        bool getUnconfirmedEventNum(StringMap& appNodeEventNum,
                                    ErrorInfo& e,
                                    const Ice::Current& current) override;
    };
}

#endif

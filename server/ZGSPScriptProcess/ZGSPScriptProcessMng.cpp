#include "ZGSPScriptProcessMng.h"
#include "ZGRuntime.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"
#include <QDebug>
#include <QThread>
#include <QReadWriteLock>
#include <QRandomGenerator>


static ZGSPScriptProcessMng* g_pScriptProcessMng = nullptr;

ZGSPScriptProcessMng* ZGSPScriptProcessMng::instance()
{
	if (g_pScriptProcessMng == nullptr)
		g_pScriptProcessMng = new ZGSPScriptProcessMng;
	return g_pScriptProcessMng;
}

ZGSPScriptProcessMng::ZGSPScriptProcessMng(QObject* parent) : QObject(parent)
{
}

void ZGSPScriptProcessMng::init()
{
	m_pScript = new ZGScript;
	if (!m_pScript->init())
	{
		ZGLOG_ERROR("m_pScript->init failed.");
		return;
	}
	initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    QThread::sleep(QRandomGenerator::global()->bounded(3, 5));
    ZG6000::ListStringMap listExpress;
    while (!getAllExpresses(listExpress))
    {
        ZGLOG_ERROR("get express error.");
        QThread::msleep(m_initInterval * 1000);
    }
    for (const auto& mapExpress: listExpress)
    {
        evaluateExpress(mapExpress);
    }
    while (!initRuleParam())
    {
        ZGLOG_ERROR("init rule param error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initRtTopicQueue())
    {
        ZGLOG_ERROR("initRtTopicQueue error.");
        QThread::msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    ZGLOG_INFO(QString("ZGSPScriptProcessMng init finished."));
    m_checkTimer.start(m_checkInterval * 1000);
}

bool ZGSPScriptProcessMng::checkState()
{
	return m_initialized;
}

bool ZGSPScriptProcessMng::evaluate(::std::string funcName, ZG6000::StringList params, ::std::string content, ZG6000::ErrorInfo& e)
{
	QStringList listParam;
	for (const auto& param: params)
	{
		listParam.append(param.c_str());
	}
	QString errMsg;
	if (!m_pScript->evaluate(funcName.c_str(), listParam, content.c_str(), errMsg))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_EVALUATE);
		e.errDetail = errMsg.toStdString();
		return false;
	}
	return true;
}

bool ZGSPScriptProcessMng::evaluate(const std::string& funcName, const std::string& jsonParam, const std::string& content, ZG6000::ErrorInfo& e)
{
	QString errMsg;
	ZGScript script;
	if (!m_pScript->evaluate(funcName.c_str(), jsonParam.c_str(), content.c_str(), errMsg))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_EVALUATE);
		e.errDetail = errMsg.toStdString();
		return false;
	}
	return true;
}

void ZGSPScriptProcessMng::invokeBatch(const ZG6000::StringList listExpressInstID)
{
	for (const auto& expressInstId: listExpressInstID)
	{
		ZG6000::ErrorInfo e;
		if (!invoke(expressInstId, e))
			ZGLOG_ERROR(e);
	}
}

bool ZGSPScriptProcessMng::updateValuesToKeys(const ZG6000::StringList& listKey, const ZG6000::StringList& listValue, ZG6000::ErrorInfo& e)
{
	auto rtProxy = ZGProxyMng::instance()->getProxySPRTData();
	if (rtProxy == nullptr)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
		e.errDetail = "getProxySPRTData error.";
		ZGLOG_ERROR(e);
		return false;
	}
	try
	{
		ZG6000::ErrorInfo ex;
		if (!rtProxy->msetDataByKeyFromList(listKey, listValue, ex))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
			e.errDetail = ex.errDetail;
			ZGLOG_ERROR("msetDataByKeyFromList error");
			ZGLOG_ERROR(e);
			return false;
		}
	}
	catch (const Ice::Exception& ie)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
		e.errDetail = ie.what();
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGSPScriptProcessMng::initRuleParam()
{
	std::string sql = "SELECT id, expressionID, expressionParam, returnKey, rtExecResult FROM sp_param_rule";
	ZG6000::ListStringMap listMapInst;
	if (!ZGProxyCommon::execQuerySql(sql, listMapInst))
		return false;
	for (const auto& mapInst: listMapInst)
	{
		RuleInfo instInfo;
		instInfo.expressID = mapInst.at("expressionID");
		instInfo.expressParam = mapInst.at("expressionParam");
		instInfo.returnKey = mapInst.at("returnKey");
		instInfo.rtExecResult = mapInst.at("rtExecResult");
		std::string instID = mapInst.at("id");
		m_mapRuleParam.insert(std::make_pair(instID, instInfo));
	}
	return true;
}

ZGScript* ZGSPScriptProcessMng::getThreadScript()
{
	Qt::HANDLE threadID = QThread::currentThreadId();
	ZGScript* script = findThreadScript(threadID);
	if (script)
		return script;
	ZGLOG_INFO("Can't find script object, create one...");
	script = new ZGScript;
	script->init();
	QString errMsg;
	for (const auto& pair: m_hashFunc)
	{
		if (!script->evaluate(pair.second.funcName.c_str(), pair.second.paramDefine.c_str(), pair.second.content.c_str(), errMsg))
			ZGLOG_WARN(errMsg);
	}
	setThreadScript(threadID, script);
	return script;
}

ZGScript* ZGSPScriptProcessMng::findThreadScript(Qt::HANDLE threadID)
{
	QReadLocker locker(&m_lock);
	auto pair = m_mapThreadScript.find(threadID);
	if (pair != m_mapThreadScript.end())
		return pair->second;
	return nullptr;
}

void ZGSPScriptProcessMng::setThreadScript(Qt::HANDLE threadID, ZGScript* script)
{
	QWriteLocker locker(&m_lock);
	m_mapThreadScript[threadID] = script;
}

bool ZGSPScriptProcessMng::invoke(std::string ruleId, ZG6000::ErrorInfo& e)
{
	ZGScript* pScript = getThreadScript();
	return doInvoke(ruleId, e, [&](const std::string& funcName, const std::string& jsonParam, std::string& result)-> bool
	{
		QString errMsg;
		QVariant v;
		if (!pScript->invoke(funcName.c_str(), jsonParam.c_str(), v, errMsg))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
			e.errDetail = errMsg.toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
		result = v.toString().toStdString();
		return true;
	});
}

void ZGSPScriptProcessMng::callBatch(const ZG6000::ListStringMap& listExpress)
{
	ZGScript* pScript = getThreadScript();
	try
	{
		for (const auto& express: listExpress)
		{
			const auto& expressID = ZGUtils::get(express, "id");
			const auto& jsonParam = ZGUtils::get(express, "param");
			ZG6000::ErrorInfo e;
			doCall(expressID, jsonParam, e, [&](const std::string& funcName, const std::string& jsonParam)-> bool
			{
				QString errMsg;
				if (!pScript->invoke(funcName.c_str(), jsonParam.c_str(), errMsg))
				{
					e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
					e.errDetail = errMsg.toStdString();
					ZGLOG_ERROR(e);
					return false;
				}
				return true;
			});
		}
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
}

bool ZGSPScriptProcessMng::call(const std::string& expressID, const std::string& jsonParam, ZG6000::ErrorInfo& e)
{
	ZGScript* pScript = getThreadScript();
	ZGLOG_DEBUG(QStringLiteral("Call %1, param: %2").arg(expressID.c_str()).arg(jsonParam.c_str()));
	return doCall(expressID, jsonParam, e, [&](const std::string& funcName, const std::string& jsonParam)-> bool
	{
		QString errMsg;
		if (!pScript->invoke(funcName.c_str(), jsonParam.c_str(), errMsg))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
			e.errDetail = errMsg.toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
		return true;
	});
}

void ZGSPScriptProcessMng::onCheckStatus()
{
}

void ZGSPScriptProcessMng::evaluateExpress(const ZG6000::StringMap& mapExpress)
{
	ExpressInfo expressInfo;
	try
	{
		expressInfo.funcName = ZGUtils::get(mapExpress, "id");
		expressInfo.paramDefine = ZGUtils::get(mapExpress, "paramDefine");
		expressInfo.content = ZGUtils::get(mapExpress, "scriptContent");
		QString errMsg;
		if (!m_pScript->evaluate(expressInfo.funcName.c_str(), expressInfo.paramDefine.c_str(), expressInfo.content.c_str(), errMsg))
		{
			QString wholeErrMsg = QStringLiteral("evaluate %1 error: %2").arg(expressInfo.funcName.c_str()).arg(errMsg);
			ZGLOG_ERROR(wholeErrMsg);
			return;
		}
		m_hashFunc[expressInfo.funcName] = expressInfo;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
}

bool ZGSPScriptProcessMng::getAllExpresses(ZG6000::ListStringMap& listExpress)
{
	std::string sql = "SELECT id, paramDefine, scriptContent FROM sp_param_expression";
	if (!ZGProxyCommon::execQuerySql(sql, listExpress))
		return false;
	return true;
}

void ZGSPScriptProcessMng::initEvents()
{
	connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPScriptProcessMng::onCheckStatus);
}

void ZGSPScriptProcessMng::initServerInstConfig()
{
	const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
	QString errMsg;
	int value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_initInterval = value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_checkInterval = value;
}

bool ZGSPScriptProcessMng::initServerInstInfo()
{
	m_serverName = ZGRuntime::instance()->getServerID();
	if (m_serverName.isEmpty())
	{
		ZGLOG_ERROR("Empty server id.");
		return false;
	}
	m_instName = ZGRuntime::instance()->getInstanceID();
	if (m_instName.isEmpty())
	{
		ZGLOG_ERROR("Empty server instance id.");
		return false;
	}
	return true;
}

bool ZGSPScriptProcessMng::initRtTopicQueue()
{
	QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
	listClientType << ZGRuntime::REDIS_RT_TOPIC << ZGRuntime::REDIS_RT_QUEUE;
	if (!ZGRuntime::instance()->initRedisClient(listClientType))
	{
		ZGLOG_ERROR("initRedisClient error.");
		return false;
	}
	return true;
}

bool ZGSPScriptProcessMng::getRuleParam(const std::string& ruleId, std::string& expressId, std::string& jsonParam, std::string& returnKey)
{
	const auto& pair = m_mapRuleParam.find(ruleId);
	expressId = pair->second.expressID;
	returnKey = pair->second.returnKey;
	if (pair != m_mapRuleParam.end())
		jsonParam = pair->second.expressParam;
	return true;
}

bool ZGSPScriptProcessMng::doInvoke(const std::string& ruleId, ZG6000::ErrorInfo& e,
                                    const std::function<bool(const std::string& funcName, const std::string& jsonParam)>& func)
{
	if (!m_initialized)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SERVER_INIT);
		ZGLOG_ERROR(e);
		return false;
	}
	std::string expressId;
	std::string jsonParam;
	std::string returnKey;
	if (!getRuleParam(ruleId, expressId, jsonParam, returnKey))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
		e.errDetail = "getExpressInstParams error.";
		ZGLOG_ERROR(e);
		return false;
	}
	return doCall(expressId, jsonParam, e, func);
}

int splitString(const std::string& src, const std::string& comma, ZG6000::StringList& listString)
{
	int count = 0;
	size_t pos;
	std::string temp = src + comma;
	std::string_view view{temp.c_str()};
	while ((pos = view.find(comma)) != std::string::npos)
	{
		if (pos)
		{
			std::string str{view.substr(0, pos)};
			listString.push_back(str);
			++count;
		}
		view.remove_prefix(pos + 1);
	}
	return count;
}

bool ZGSPScriptProcessMng::doInvoke(const std::string& ruleId, ZG6000::ErrorInfo& e, const InvokeFunc& func)
{
	if (!m_initialized)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SERVER_INIT);
		ZGLOG_ERROR(e);
		return false;
	}
	std::string expressId;
	std::string jsonParam;
	std::string returnKey;
	if (!getRuleParam(ruleId, expressId, jsonParam, returnKey))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
		e.errDetail = "getExpressInstParams error.";
		ZGLOG_ERROR(e);
		return false;
	}
	std::string returnVal;
	if (!doCall(expressId, jsonParam, returnVal, e, func))
		return false;
	std::string errMsg;
	if (!returnKey.empty())
	{
		ZG6000::StringList listKey;
		ZG6000::StringList listVal;
		listVal.reserve(listKey.size());
		if (splitString(returnKey, ",", listKey) > 0)
		{
			for (size_t i = 0; i < listKey.size(); ++i)
			{
				listVal.emplace_back(returnVal);
			}
			return updateValuesToKeys(listKey, listVal, e);
		}
	}
	return true;
}

bool ZGSPScriptProcessMng::doCall(const std::string& expressID, const std::string& jsonParam, ZG6000::ErrorInfo& e,
                                  const std::function<bool(const std::string&, const std::string&)>& func)
{
	const auto& pair = m_hashFunc.find(expressID);
	if (pair == m_hashFunc.end())
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
		e.errDetail = "Can't find expressId: " + expressID;
		ZGLOG_ERROR(e);
		return false;
	}
	return func(pair->second.funcName, jsonParam);
}

bool ZGSPScriptProcessMng::doCall(const std::string& expressID, const std::string& jsonParam,
                                  std::string& returnVal, ZG6000::ErrorInfo& e, const InvokeFunc& func)
{
	const auto& pair = m_hashFunc.find(expressID);
	if (pair == m_hashFunc.end())
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
		e.errDetail = "Can't find expressId: " + expressID;
		ZGLOG_ERROR(e);
		return false;
	}
	return func(pair->second.funcName, jsonParam, returnVal);
}

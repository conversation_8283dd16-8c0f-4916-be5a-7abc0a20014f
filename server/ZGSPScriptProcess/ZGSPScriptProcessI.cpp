
#include <ZGSPScriptProcessI.h>
#include "ZGSPScriptProcessMng.h"

ZG6000::ZGSPScriptProcessI::ZGSPScriptProcessI()
{
    ZGSPScriptProcessMng::instance()->init();
}

bool ZG6000::ZGSPScriptProcessI::evaluateByJson(std::string funcName, std::string jsonParam, std::string script, ErrorInfo& e, const Ice::Current& /*current*/)
{
    return ZGSPScriptProcessMng::instance()->evaluate(funcName, jsonParam, script, e);
}

bool ZG6000::ZGSPScriptProcessI::evaluateByList(std::string funcName, StringList listParam, std::string script, ErrorInfo& e, const Ice::Current& /*current*/)
{
    return ZGSPScriptProcessMng::instance()->evaluate(funcName, listParam, script, e);
}

bool ZG6000::ZGSPScriptProcessI::callJson(std::string expressID, std::string jsonParam, ErrorInfo& e, const Ice::Current& /*current*/)
{
    return ZGSPScriptProcessMng::instance()->call(expressID, jsonParam, e);
}

bool ZG6000::ZGSPScriptProcessI::callToBool(std::string expressID, std::string jsonParam, bool& result, ErrorInfo& e, const Ice::Current& current)
{
    QVariant retValue;
    if (!ZGSPScriptProcessMng::instance()->call(expressID, jsonParam, retValue, e))
        return false;
    result = retValue.toBool();
    return true;
}

bool ZG6000::ZGSPScriptProcessI::callToInt(std::string expressID, std::string jsonParam, int& result, ErrorInfo& e, const Ice::Current& /*current*/)
{
    QVariant retValue;
    if (!ZGSPScriptProcessMng::instance()->call(expressID, jsonParam, retValue, e))
        return false;
    result = retValue.toInt();
    return true;
}

bool ZG6000::ZGSPScriptProcessI::callToDouble(std::string expressID, std::string jsonParam, double& result, ErrorInfo& e, const Ice::Current& /*current*/)
{
    QVariant retValue;
    if (!ZGSPScriptProcessMng::instance()->call(expressID, jsonParam, retValue, e))
        return false;
    result = retValue.toDouble();
    return true;
}

bool ZG6000::ZGSPScriptProcessI::callToString(std::string expressID, std::string jsonParam, std::string& result, ErrorInfo& e, const Ice::Current& /*current*/)
{
    QVariant retValue;
    if (!ZGSPScriptProcessMng::instance()->call(expressID, jsonParam, retValue, e))
        return false;
    result = retValue.toString().toStdString();
    return true;
}

bool
ZG6000::ZGSPScriptProcessI::checkState(const Ice::Current& /*current*/)
{
    return ZGSPScriptProcessMng::instance()->checkState();
}

/**
 * @brief   调用指定ID的表达式实例
 *
 * @param   listExpressInstID   表达式实例ID列表.
 *
 * @return  单向调用，不返回.
 */
void
ZG6000::ZGSPScriptProcessI::invokeOneway(StringList listExpressInstID,
                                         const Ice::Current& /*current*/)
{
    ZGSPScriptProcessMng::instance()->invokeBatch(listExpressInstID);
}

bool
ZG6000::ZGSPScriptProcessI::invoke(::std::string ruleID,
                                   ErrorInfo& e,
                                   const Ice::Current& /*current*/)
{
    return ZGSPScriptProcessMng::instance()->invoke(ruleID, e);
}

/**
 * @brief   调用指定ID的表达式实例，并返回执行结果
 *
 * @param           expressInstId   表达式实例ID
 * @param [in,out]  result          执行结果
 * @param [in,out]  e               错误信息
 *
 * @return  调用成功返回true，失败返回false。
 */
bool
ZG6000::ZGSPScriptProcessI::invokeToBool(std::string ruleId,
                                         bool& result,
                                         ErrorInfo& e,
                                         const Ice::Current& /*current*/)
{
    QVariant retValue;
    if (!ZGSPScriptProcessMng::instance()->invoke(ruleId, retValue, e))
        return false;
    result = retValue.toBool();
    return true;
}

/**
 * @brief   调用指定ID的表达式实例，并返回执行结果
 *
 * @param           expressInstId   表达式实例ID
 * @param [in,out]  result          执行结果
 * @param [in,out]  e               错误信息
 *
 * @return  调用成功返回true，失败返回false。
 */
bool
ZG6000::ZGSPScriptProcessI::invokeToInt(std::string ruleId,
                                 int& result,
                                 ErrorInfo& e,
                                 const Ice::Current& /*current*/)
{
    QVariant retValue;
    if (!ZGSPScriptProcessMng::instance()->invoke(ruleId, retValue, e))
        return false;
    result = retValue.toInt();
    return true;
}

/**
 * @brief   调用指定ID的表达式实例，并返回执行结果
 *
 * @param           expressInstId   表达式实例ID
 * @param [in,out]  result          执行结果
 * @param [in,out]  e               错误信息
 *
 * @return  调用成功返回true，失败返回false。
 */
bool
ZG6000::ZGSPScriptProcessI::invokeToDouble(std::string ruleId,
                                    double& result,
                                    ErrorInfo& e,
                                    const Ice::Current& /*current*/)
{
    QVariant retValue;
    if (!ZGSPScriptProcessMng::instance()->invoke(ruleId, retValue, e))
        return false;
    result = retValue.toDouble();
    return true;
}

/**
 * @brief   调用指定ID的表达式实例，并返回执行结果
 *
 * @param           expressInstId   表达式实例ID
 * @param [in,out]  result          执行结果
 * @param [in,out]  e               错误信息
 *
 * @return  调用成功返回true，失败返回false。
 */
bool
ZG6000::ZGSPScriptProcessI::invokeToString(std::string ruleId,
                                    ::std::string& result,
                                    ErrorInfo& e,
                                    const Ice::Current& /*current*/)
{
    QVariant retValue;
    if (!ZGSPScriptProcessMng::instance()->invoke(ruleId, retValue, e))
        return false;
    result = retValue.toString().toStdString();
    return true;
}

bool
ZG6000::ZGSPScriptProcessI::invokeToStringList(std::string ruleId,
                                               StringList& result,
                                               ErrorInfo& e,
                                               const Ice::Current& /*current*/)
{
    QVariant retValue;
    if (!ZGSPScriptProcessMng::instance()->invoke(ruleId, retValue, e))
        return false;
    QStringList listValue = retValue.toStringList();
    foreach (const QString& value, listValue)
    {
        result.push_back(value.toStdString());
    }
    return true;
}

void ZG6000::ZGSPScriptProcessI::callBatch(ListStringMap listExpress, const Ice::Current& current)
{
    ZGSPScriptProcessMng::instance()->callBatch(listExpress);
}

#ifndef ZGSPSCRIPTPROCESSMNG_H
#define ZGSPSCRIPTPROCESSMNG_H

#include <QTimer>
#include <QJSEngine>
#include <QVariant>
#include <QDebug>
#include <QReadWriteLock>
#include <unordered_map>
#include "zgerror/ZGSPScriptProcessError.h"
#include "ZGServerCommon.h"
#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGScript.h"

class ZGSPScriptProcessMng : public QObject
{
    Q_OBJECT
public:
    static ZGSPScriptProcessMng* instance();
    explicit ZGSPScriptProcessMng(QObject *parent = nullptr);

    void init();
    bool checkState();
    bool evaluate(::std::string funcName, ZG6000::StringList params, ::std::string content, ZG6000::ErrorInfo& e);
    bool evaluate(const std::string& funcName, const std::string& jsonParam, const std::string& script, ZG6000::ErrorInfo& e);
    void invokeBatch(const ZG6000::StringList listExpressInstID);
    bool invoke(std::string ruleId, ZG6000::ErrorInfo& e);
    bool invoke(std::string ruleId, QVariant& result, ZG6000::ErrorInfo& e)
    {
        ZGScript* pScript = getThreadScript();
        return doInvoke(ruleId, e, [&](const std::string& funcName, const std::string& jsonParam)->bool
        {
            QString errMsg;
            if (!pScript->invoke(funcName.c_str(), jsonParam.c_str(), result, errMsg))
	        {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
                e.errDetail = errMsg.toStdString();
                ZGLOG_ERROR(e);
                return false;
	        }
            return true;
        });
    }
    void callBatch(const ZG6000::ListStringMap& listExpress);
	bool call(const std::string& expressID, const std::string &jsonParam, ZG6000::ErrorInfo& e);
    bool call(const std::string& expressID, const std::string& jsonParam, QVariant& result, ZG6000::ErrorInfo& e)
    {
        ZGScript* pScript = getThreadScript();
        return doCall(expressID, jsonParam, e, [&](const std::string& funcName, const std::string& jsonParam)->bool
        {
            QString errMsg;
            if (!pScript->invoke(funcName.c_str(), jsonParam.c_str(), result, errMsg))
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPScriptProcess::ZG_ERR_SCRIPT_INVOKE);
                e.errDetail = errMsg.toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        });
    }

private slots:
    void onCheckStatus();

private:
    struct ExpressInfo
    {
        std::string funcName{""};
        std::string paramDefine{""};
        std::string content{""};
    };
    struct RuleInfo
    {
        std::string expressID{""};
        std::string expressParam{ "" };
        std::string returnKey{""};
        std::string rtExecResult{ "" };
    };

private:
    void initialize();
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initRtTopicQueue();
    using InvokeFunc = std::function<bool(const std::string& funcName, const std::string& jsonParam, std::string& result)>;
    bool getRuleParam(const std::string& ruleId, std::string& expressId, std::string &params, std::string& returnKey);
    bool doInvoke(const std::string& expressInstId, ZG6000::ErrorInfo& e, const std::function<bool (const std::string &, const std::string &)> &func);
    bool doInvoke(const std::string& ruleId, ZG6000::ErrorInfo& e, const InvokeFunc& func);
    bool doCall(const std::string& expressID, const std::string& jsonParam, ZG6000::ErrorInfo& e,
                const std::function<bool(const std::string&, const std::string&)>& func);
    bool doCall(const std::string& expressID, const std::string &jsonParam, std::string& returnVal, ZG6000::ErrorInfo& e, const InvokeFunc& func);
    void evaluateExpress(const ZG6000::StringMap& mapExpress);
    bool getAllExpresses(ZG6000::ListStringMap& listExpress);
    bool updateValuesToKeys(const ZG6000::StringList& listKey, const ZG6000::StringList& listValue, ZG6000::ErrorInfo& e);
    bool initRuleParam();
    ZGScript* getThreadScript();
    ZGScript* findThreadScript(Qt::HANDLE threadID);
    void setThreadScript(Qt::HANDLE threadID, ZGScript* script);

private:
    bool m_initialized{false};
    ZGScript *m_pScript{nullptr};
    QString m_serverName;
    QString m_instName;
    int m_initInterval{10};
    int m_checkInterval{10};
    bool m_masterInst{false};
    QTimer m_checkTimer;
    std::unordered_map<std::string, ExpressInfo> m_hashFunc;    
    std::unordered_map<std::string, RuleInfo> m_mapRuleParam;
    std::unordered_map<Qt::HANDLE, ZGScript*> m_mapThreadScript;
    QReadWriteLock m_lock;
    int m_currentStep{0};
    long long m_keyTTL{60};
};

#endif // ZGSPSCRIPTPROCESSMNG_H

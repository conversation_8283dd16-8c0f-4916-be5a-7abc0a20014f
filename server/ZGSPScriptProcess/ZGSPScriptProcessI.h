#ifndef __ZGSPScriptProcessI_h__
#define __ZGSPScriptProcessI_h__

#include <ZGSPScriptProcess.h>

namespace ZG6000
{
	class ZGSPScriptProcessI : public virtual ZGSPScriptProcess
	{
	public:
		ZGSPScriptProcessI();
		bool checkState(const Ice::Current& current) override;
		bool evaluateByJson(std::string funcName, std::string jsonParam, std::string script, ErrorInfo& e, const Ice::Current& current) override;
		bool evaluateByList(std::string funcName, StringList listParam, std::string script, ErrorInfo& e, const Ice::Current& current) override;
		void invokeOneway(StringList listRuleID, const Ice::Current& current) override;
		bool invoke(std::string ruleID, ErrorInfo& e, const Ice::Current& current) override;
		bool invokeToBool(std::string ruleID, bool& result, ErrorInfo& e, const Ice::Current& current) override;
		bool invokeToInt(std::string ruleID, int& result, ErrorInfo& e, const Ice::Current& current) override;
		bool invokeToDouble(std::string ruleID, double& result, ErrorInfo& e, const Ice::Current& current) override;
		bool invokeToString(std::string ruleID, std::string& result, ErrorInfo& e, const Ice::Current& current) override;
		bool invokeToStringList(std::string ruleID, StringList& result, ErrorInfo& e, const Ice::Current& current) override;
		void callBatch(ListStringMap listExpress, const Ice::Current& current) override;
		bool callJson(std::string expressID, std::string jsonParam, ErrorInfo& e, const Ice::Current& current) override;
		bool callToBool(std::string expressID, std::string jsonParam, bool& result, ErrorInfo& e, const Ice::Current& current) override;
		bool callToInt(std::string expressID, std::string jsonParam, int& result, ErrorInfo& e, const Ice::Current& current) override;
		bool callToDouble(std::string expressID, std::string jsonParam, double& result, ErrorInfo& e, const Ice::Current& current) override;
		bool callToString(std::string expressID, std::string jsonParam, std::string& result, ErrorInfo& e, const Ice::Current& current) override;
	};
}

#endif

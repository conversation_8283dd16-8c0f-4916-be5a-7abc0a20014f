#ifndef ZGMPHANDLE_H
#define ZGMPHANDLE_H

#include <QObject>
#include <QJsonDocument>
#include "ZGWebModule.h"
#include "ZGProxyCommon.h"

class ZGRedisClient;

class ZGMPHandle : public QObject
{
    Q_OBJECT

public:
    explicit ZGMPHandle(QObject* parent = nullptr);
    bool initialize();

public:
    ZGWebModule::Response on_mp_yk(const QString& clientID,
                                   const QVariantMap& headers,
                                   const QJsonValue& param,
                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ys(const QString& clientID,
                                   const QVariantMap& headers,
                                   const QJsonValue& param,
                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yt(const QString& clientID,
                                   const QVariantMap& headers,
                                   const QJsonValue& param,
                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yx_get(const QString& clientID,
                                       const QVariantMap& headers,
                                       const QJsonValue& param,
                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yx_desc_get(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yc_get(const QString& clientID,
                                       const QVariantMap& headers,
                                       const QJsonValue& param,
                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_text_get(const QString& clientID,
                                         const QVariantMap& headers,
                                         const QJsonValue& param,
                                         const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ym_get(const QString& clientID,
                                       const QVariantMap& headers,
                                       const QJsonValue& param,
                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_appnode_yv_get(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_appnode_yv_preset_get(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_preset_get(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_preset_set(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_preset_load(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_up(const QString& clientID,
                                      const QVariantMap& headers,
                                      const QJsonValue& param,
                                      const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_down(const QString& clientID,
                                        const QVariantMap& headers,
                                        const QJsonValue& param,
                                        const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_left(const QString& clientID,
                                        const QVariantMap& headers,
                                        const QJsonValue& param,
                                        const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_right(const QString& clientID,
                                         const QVariantMap& headers,
                                         const QJsonValue& param,
                                         const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_up_left(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_up_right(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_down_left(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_down_right(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_zoomin(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_zoomout(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_scan(const QString& clientID,
                                        const QVariantMap& headers,
                                        const QJsonValue& param,
                                        const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_near(const QString& clientID,
                                        const QVariantMap& headers,
                                        const QJsonValue& param,
                                        const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_far(const QString& clientID,
                                       const QVariantMap& headers,
                                       const QJsonValue& param,
                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_close(const QString& clientID,
                                         const QVariantMap& headers,
                                         const QJsonValue& param,
                                         const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_open(const QString& clientID,
                                        const QVariantMap& headers,
                                        const QJsonValue& param,
                                        const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_alarm_on(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_yv_alarm_off(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dev_yk(const QString& clientID,
                                       const QVariantMap& headers,
                                       const QJsonValue& param,
                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dev_batch_yk(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ctrl_act_get(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dev_act_get(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dev_get(const QString& clientID,
                                        const QVariantMap& headers,
                                        const QJsonValue& param,
                                        const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dev_ctrl_allow(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dev_update(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ctrl_rule_get(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_device_rule_get(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ctrl_rule_id_get(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ctrl_rule_add(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ctrl_rule_delete(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ctrl_rule_item_add(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ctrl_rule_item_update(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ctrl_rule_item_delete(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_ctrl_unlock_set(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dataid_get(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dataids_get(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_category_property_get(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_topics_get(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_event_topics_get(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dataset_get(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_store_yc_get(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_store_yx_get(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_store_text_get(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_statistic_data_get(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_data_simulate_set(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_data_position_set(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_device_position_set(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_start(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_stop(const QString& clientID,
                                          const QVariantMap& headers,
                                          const QJsonValue& param,
                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_pause(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_resume(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_reset(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_get(const QString& clientID,
                                         const QVariantMap& headers,
                                         const QJsonValue& param,
                                         const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_list_get(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_exec_rule_get(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_conf_rule_get(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_error_rule_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_task_item_get(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_devices_properties_all_get(const QString& clientID,
                                                           const QVariantMap& headers,
                                                           const QJsonValue& param,
                                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_device_properties_group_get(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_devices_properties_get(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_devices_properties_update(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_device_property_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_device_property_update(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_devices_property_values_get(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_devices_property_values_update(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_device_property_value_get(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_device_property_value_update(const QString& clientID,
                                                             const QVariantMap& headers,
                                                             const QJsonValue& param,
                                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dev_block_set(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_condition_exec_check(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_condition_confirm_check(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_finger_get(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_finger_capture(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_finger_add(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_finger_delete(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_face_capture(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_face_set(const QString& clientID,
                                         const QVariantMap& headers,
                                         const QJsonValue& param,
                                         const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_face_delete(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dev_user_sync(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_region_list(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_region_user_get(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_region_yv_get(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_region_access_get(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_region_user_clear(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_tts_group_play(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_tts_dev_play(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_tts_region_alarm_on(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_tts_region_alarm_off(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_realwarn_get(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_dev_relation_get(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req);
    ZGWebModule::Response on_mp_appnode_devices_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req);

private:
    bool initParams();
    ZGWebModule::Response sendMessageToQueue(const QJsonValue& param,
                                             const QHttpServerRequest& req,
                                             const std::function<ZGWebModule::Response (ZGRedisClient*,
                                                 const QJsonDocument&)>& func);
    ZGWebModule::Response deviceCall(const QJsonValue& param,
                                     const QHttpServerRequest& req,
                                     const std::function<ZGWebModule::Response(
                                         std::shared_ptr<ZG6000::ZGMPDevicePropertyPrx> deviceProxy)>& func);
    ZGWebModule::Response taskCall(const QString& clientID,
                                   const QVariantMap& headers,
                                   const QJsonValue& param,
                                   const QHttpServerRequest& req,
                                   const std::function<ZGWebModule::Response(
                                       std::shared_ptr<ZG6000::ZGMPTaskManagerPrx>)>& func);
    ZGWebModule::Response videoCall(const QString& clientID,
                                    const QVariantMap& headers,
                                    const QJsonValue& param,
                                    const QHttpServerRequest& req,
                                    const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGMPVideoHIKPrx>)>
                                    & func);
    ZGWebModule::Response ruleCall(const QString& clientID,
                                   const QVariantMap& headers,
                                   const QJsonValue& param,
                                   const QHttpServerRequest& req,
                                   const std::function<ZGWebModule::Response
                                       (std::shared_ptr<ZG6000::ZGMPRuleEnginePrx>)>& func);
    ZGWebModule::Response identCall(const QString& clientID,
                                    const QVariantMap& headers,
                                    const QJsonValue& param,
                                    const QHttpServerRequest& req,
                                    const std::function<ZGWebModule::Response(
                                        std::shared_ptr<ZG6000::ZGMPIdentifyManagerPrx>)>& func);
    ZGWebModule::Response regionCall(const QString& clientID,
                                     const QVariantMap& headers,
                                     const QJsonValue& param,
                                     const QHttpServerRequest& req,
                                     const std::function<ZGWebModule::Response(
                                         std::shared_ptr<ZG6000::ZGMPRegionManagerPrx>)>& func);
    ZGWebModule::Response broadcastCall(const QString& clientID,
                                        const QVariantMap& headers,
                                        const QJsonValue& param,
                                        const QHttpServerRequest& req,
                                        const std::function<ZGWebModule::Response(
                                            std::shared_ptr<ZG6000::ZGMPBroadcastServerPrx>)>& func);
    ZGWebModule::Response getCtrlActions(const std::string& clientID,
                                         const std::vector<std::pair<std::string, std::string>>& listCtrls,
                                         const std::string& runMode);
    ZGWebModule::Response convertRuleItem(const ZG6000::ListStringMap& listRecord);
    void addInheritedAppnodes(ZG6000::StringList& listAppNodeID,
                              const std::string& appNodeID);
    bool checkYkAuthorization(const QString& ykID,
                              bool& isAuth,
                              QString& errMsg);
    bool checkYsAuthorization(const QString& ysID,
                              bool& isAuth,
                              QString& errMsg);

signals:

private:
    ZG6000::MapStringMap m_mapAppNode;
    ZG6000::MapStringMap m_mapOperator;
    ZG6000::MapStringMap m_mapAlarmLevel;
    ZG6000::MapStringMap m_mapDataCategory;
    ZG6000::MapStringMap m_mapDevice;
    ZG6000::MapStringMap m_mapDataCategoryProperty;
};

#endif // ZGMPHANDLE_H

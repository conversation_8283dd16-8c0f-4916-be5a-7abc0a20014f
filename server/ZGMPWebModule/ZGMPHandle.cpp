#include "ZGMPHandle.h"

#include <execution>

#include "redis/ZGRedisClient.h"
#include "ZGWebModule.h"
#include "ZGRuntime.h"
#include "ZGJson.h"
#include "ZGUtils.h"
#include <QJsonArray>
#include <QRandomGenerator>
#include <Ice/Ice.h>

ZGMPHandle::ZGMPHandle(QObject* parent) : QObject(parent)
{
}

bool ZGMPHandle::initialize()
{
    return initParams();
}

ZGWebModule::Response ZGMPHandle::on_mp_yk(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonArray ykArray = param.toArray();
    bool authed = false;
    for (auto ykRef : ykArray)
    {
        const auto& ykObj = ykRef.toObject();
        const auto& ykID = ykObj["id"].toString();
        if (ykID.isEmpty())
            return ZGWebModule::errorObject(QStringLiteral("遥控ID不能为空"));
        bool isAuth;
        if (!checkYkAuthorization(ykID, isAuth, errMsg))
        {
            return ZGWebModule::errorObject(errMsg);
        }
        if (isAuth && (!authed))
        {
            QJsonArray tokenArray;
            if (!ZGWebModule::verifyAuthorization(clientID, headers, {"ZG_HP_CTRL"}, tokenArray, errMsg))
            {
                return ZGWebModule::errorObject(errMsg);
            }
            authed = true;
        }
    }
    ZGRedisClient* client = ZGRuntime::instance()->getRedisClientRTQueue();
    if (client == nullptr)
        return ZGWebModule::errorObject("getRedisClientRTQueue error.");
    QJsonDocument doc(param.toArray());
    QByteArray message = doc.toJson(QJsonDocument::Compact);
    long long subscribeNum;
    if (!client->rpush("ZG_Q_SYSTEM_YK", message, subscribeNum, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_ys(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonArray ysArray = param.toArray();
    bool authed = false;
    for (auto ysRef : ysArray)
    {
        const auto& ysObj = ysRef.toObject();
        const auto& ysID = ysObj["id"].toString();
        if (ysID.isEmpty())
            return ZGWebModule::errorObject(QStringLiteral("遥设ID不能为空"));
        bool isAuth;
        if (!checkYsAuthorization(ysID, isAuth, errMsg))
        {
            return ZGWebModule::errorObject(errMsg);
        }
        if (isAuth && (!authed))
        {
            QJsonArray tokenArray;
            if (!ZGWebModule::verifyAuthorization(clientID, headers, {"ZG_HP_CTRL"}, tokenArray, errMsg))
            {
                return ZGWebModule::errorObject(errMsg);
            }
            authed = true;
        }
    }
    ZGRedisClient* client = ZGRuntime::instance()->getRedisClientRTQueue();
    if (client == nullptr)
        return ZGWebModule::errorObject("getRedisClientRTQueue error.");
    QJsonDocument doc(param.toArray());
    QByteArray message = doc.toJson(QJsonDocument::Compact);
    long long subscribeNum;
    if (!client->rpush("ZG_Q_SYSTEM_YS", message, subscribeNum, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_yt(const QString& /*clientID*/,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req)
{
    return sendMessageToQueue(param, req, [](ZGRedisClient* client,
                                             const QJsonDocument& doc)-> ZGWebModule::Response
    {
        QByteArray message = doc.toJson(QJsonDocument::Compact);
        long long subscribeNum;
        QString errMsg;
        if (!client->rpush("ZG_Q_SYSTEM_YT", message, subscribeNum, errMsg))
            return ZGWebModule::errorObject(errMsg);
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGMPHandle::on_mp_yx_get(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req)
{
    ZG6000::StringList listID;
    if (param.isString())
    {
        const auto& datasetID = param.toString();
        QString sql = QString(
            "SELECT a.id FROM mp_param_dataset_yx a LEFT JOIN mp_param_dataset b ON a.datasetID = b.id "
            "LEFT JOIN mp_param_model_yx c ON a.dataModelID = c.id "
            "WHERE b.isEnable = 1 AND c.isEnable = 1 AND datasetID = '%1' ORDER BY a.id").arg(datasetID);
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
            return ZGWebModule::errorObject(QStringLiteral("获取数据集'%1'遥信失败").arg(datasetID));
    }
    else if (param.isArray())
    {
        const auto& array = param.toArray();
        for (auto itemRef : array)
        {
            listID.push_back(itemRef.toString().toStdString());
        }
    }
    else
        return ZGWebModule::errorObject(QStringLiteral("无效的元素类型"));
    ZG6000::ListStringMap listData;
    if (!ZGProxyCommon::mgetDataByFields("mp_param_dataset_yx", listID, {
            "id", "name", "dataModelID", "dataIndex", "deviceID",
            "rtRawValue", "rtNewValue", "rtSimulateValue", "rtQualityFlag", "rtStateFlag", "rtUpdateTime"
        },
        listData))
        return ZGWebModule::errorObject(u8"获取实时数据失败");
    ZG6000::StringList listModelID;
    for (const auto& data : listData)
    {
        listModelID.push_back(ZGUtils::get(data, "dataModelID"));
    }
    ZG6000::ListStringMap listModelData;
    if (!ZGProxyCommon::mgetDataByFields("mp_param_model_yx", listModelID,
        {"isEnable", "dataCategoryID", "alarmLevelID"}, listModelData))
        return ZGWebModule::errorObject(u8"获取模型参数失败");
    for (size_t i = 0; i < listData.size(); ++i)
    {
        listData[i].insert(listModelData[i].begin(), listModelData[i].end());
    }
    for (auto& data : listData)
    {
        const auto& dataCategoryID = ZGUtils::get(data, "dataCategoryID");
        auto pair = m_mapDataCategory.find(dataCategoryID);
        if (pair != m_mapDataCategory.end())
        {
            data.insert(std::make_pair("dataCategoryName", pair->second["name"]));
            data.insert(std::make_pair("dataCategoryNameL2", pair->second["nameL2"]));
        }
        const auto& rtNewValue = ZGUtils::get(data, "rtNewValue");
        pair = m_mapDataCategoryProperty.find(dataCategoryID + "/" + rtNewValue);
        if (pair != m_mapDataCategoryProperty.end())
        {
            data.insert(std::make_pair("rtValueDesc", pair->second["name"]));
            data.insert(std::make_pair("rtValueDescL2", pair->second["nameL2"]));
        }
        else
        {
            data.insert(std::make_pair("rtValueDesc", ""));
            data.insert(std::make_pair("rtValueDescL2", ""));
        }
        const auto& alarmLevelID = ZGUtils::get(data, "alarmLevelID");
        pair = m_mapAlarmLevel.find(alarmLevelID);
        if (pair != m_mapAlarmLevel.end())
        {
            data.insert(std::make_pair("alarmLevelName", pair->second["name"]));
            data.insert(std::make_pair("alarmLevelNameL2", pair->second["nameL2"]));
        }
        else
        {
            data.insert(std::make_pair("alarmLevelName", ""));
            data.insert(std::make_pair("alarmLevelNameL2", ""));
        }
        const auto& deviceID = ZGUtils::get(data, "deviceID");
        pair = m_mapDevice.find(deviceID);
        if (pair != m_mapDevice.end())
        {
            data.insert(std::make_pair("deviceName", pair->second["name"]));
            data.insert(std::make_pair("deviceNameL2", pair->second["nameL2"]));
        }
        else
        {
            data.insert(std::make_pair("deviceName", ""));
            data.insert(std::make_pair("deviceNameL2", ""));
        }
    }
    return ZGWebModule::replyObject(ZGJson::qconvertToJson(listData));
}

ZGWebModule::Response ZGMPHandle::on_mp_yx_desc_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& yxID = param.toString().toStdString();
    ZG6000::StringMap record;
    std::string dataModelID;
    if (!ZGProxyCommon::getDataByField("mp_param_dataset_yx", yxID, "dataModelID", dataModelID))
        return ZGWebModule::errorObject(u8"获取实时数据模型失败");
    try
    {
        ZG6000::StringMap modelRecord;
        std::string dataCategoryID;
        if (!ZGProxyCommon::getDataByField("mp_param_model_yx", dataModelID, "dataCategoryID", dataCategoryID))
            return ZGWebModule::errorObject(u8"获取模型类别失败");
        QJsonObject object;
        if (dataCategoryID.empty())
            return ZGWebModule::replyObject(object);
        std::string sql = "SELECT propValue, propName FROM mp_param_data_category_property WHERE dataCategoryID = '" +
            dataCategoryID + "'";
        return ZGWebModule::replyStringMap(sql);
    }
    catch (const std::exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_yc_get(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req)
{
    ZG6000::StringList listID;
    if (param.isString())
    {
        const auto& datasetID = param.toString();
        QString sql = QString("SELECT a.id FROM mp_param_dataset_yc a "
            "LEFT JOIN mp_param_dataset b ON a.datasetID = b.id "
            "LEFT JOIN mp_param_model_yc c ON a.dataModelID = c.id "
            "WHERE b.isEnable = 1 AND c.isEnable = 1 AND datasetID = '%1' ORDER BY a.id").arg(datasetID);
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
            return ZGWebModule::errorObject(QStringLiteral("获取数据集'%1'遥测失败").arg(datasetID));
    }
    else if (param.isArray())
    {
        const auto& array = param.toArray();
        for (auto itemRef : array)
        {
            listID.push_back(itemRef.toString().toStdString());
        }
    }
    else
        return ZGWebModule::errorObject(QStringLiteral("无效的元素类型"));
    ZG6000::ListStringMap listData;
    if (!ZGProxyCommon::mgetDataByFields("mp_param_dataset_yc", listID, {
            "id", "name", "dataModelID", "dataIndex", "deviceID",
            "rtRawValue", "rtNewValue", "rtSimulateValue", "rtQualityFlag", "rtStateFlag", "rtUpdateTime"
        },
        listData))
        return ZGWebModule::errorObject(u8"获取实时数据失败");
    ZG6000::StringList listModelID;
    for (const auto& data : listData)
    {
        listModelID.push_back(ZGUtils::get(data, "dataModelID"));
    }
    ZG6000::ListStringMap listModelData;
    if (!ZGProxyCommon::mgetDataByFields("mp_param_model_yc", listModelID,
        {"isEnable", "dataCategoryID", "dataUnitID", "alarmLevelID"}, listModelData))
        return ZGWebModule::errorObject(u8"获取模型参数失败");
    for (size_t i = 0; i < listData.size(); ++i)
    {
        listData[i].insert(listModelData[i].begin(), listModelData[i].end());
    }
    for (auto& data : listData)
    {
        const auto& dataCategoryID = ZGUtils::get(data, "dataCategoryID");
        auto pair = m_mapDataCategory.find(dataCategoryID);
        if (pair != m_mapDataCategory.end())
        {
            data.insert(std::make_pair("dataCategoryName", pair->second["name"]));
            data.insert(std::make_pair("dataCategoryNameL2", pair->second["nameL2"]));
        }
        const auto& rtNewValue = ZGUtils::get(data, "rtNewValue");
        pair = m_mapDataCategoryProperty.find(dataCategoryID + "/" + rtNewValue);
        if (pair != m_mapDataCategoryProperty.end())
        {
            data.insert(std::make_pair("rtValueDesc", pair->second["name"]));
            data.insert(std::make_pair("rtValueDescL2", pair->second["nameL2"]));
        }
        else
        {
            data.insert(std::make_pair("rtValueDesc", ""));
            data.insert(std::make_pair("rtValueDescL2", ""));
        }
        const auto& alarmLevelID = ZGUtils::get(data, "alarmLevelID");
        pair = m_mapAlarmLevel.find(alarmLevelID);
        if (pair != m_mapAlarmLevel.end())
        {
            data.insert(std::make_pair("alarmLevelName", pair->second["name"]));
            data.insert(std::make_pair("alarmLevelNameL2", pair->second["nameL2"]));
        }
        else
        {
            data.insert(std::make_pair("alarmLevelName", ""));
            data.insert(std::make_pair("alarmLevelNameL2", ""));
        }
        const auto& deviceID = ZGUtils::get(data, "deviceID");
        pair = m_mapDevice.find(deviceID);
        if (pair != m_mapDevice.end())
        {
            data.insert(std::make_pair("deviceName", pair->second["name"]));
            data.insert(std::make_pair("deviceNameL2", pair->second["nameL2"]));
        }
        else
        {
            data.insert(std::make_pair("deviceName", ""));
            data.insert(std::make_pair("deviceNameL2", ""));
        }
    }
    return ZGWebModule::replyObject(ZGJson::qconvertToJson(listData));
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_preset_set(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "index"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int index = object["index"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlSetPreset(yvID, index, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_preset_load(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "index"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int index = object["index"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlGotoPreset(yvID, index, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_up(const QString& clientID,
                                              const QVariantMap& headers,
                                              const QJsonValue& param,
                                              const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlUp(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_down(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlDown(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_left(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlLeft(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_right(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlRight(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_up_left(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlUpLeft(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_up_right(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlUpRight(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_down_left(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlDownLeft(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_down_right(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlDownRight(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_zoomin(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlZoomIn(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_zoomout(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlZoomOut(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_scan(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlAuto(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_near(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlFocusNear(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_far(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlFocusFar(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_close(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlIrisClose(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_open(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "stop"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& yvID = object["id"].toString().toStdString();
    int stop = object["stop"].toVariant().toInt();
    return videoCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPVideoHIKPrx> videoPrx) -> ZGWebModule::Response
        {
            ZG6000::ErrorInfo e;
            if (!videoPrx->ptzControlIrisOpen(yvID, stop, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_text_get(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    ZG6000::StringList listID;
    if (param.isString())
    {
        const auto& datasetID = param.toString();
        QString sql = QString(
            "SELECT a.id FROM mp_param_dataset_text a LEFT JOIN mp_param_dataset b ON a.datasetID = b.id "
            "LEFT JOIN mp_param_model_text c ON a.dataModelID = c.id "
            "WHERE b.isEnable = 1 AND c.isEnable = 1 AND datasetID = '%1' ORDER BY a.id").arg(datasetID);
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
            return ZGWebModule::errorObject(QStringLiteral("获取数据集'%1'文本失败").arg(datasetID));
    }
    else if (param.isArray())
    {
        const auto& array = param.toArray();
        for (auto itemRef : array)
        {
            listID.push_back(itemRef.toString().toStdString());
        }
    }
    else
        return ZGWebModule::errorObject(QStringLiteral("无效的元素类型"));
    ZG6000::ListStringMap listData;
    if (!ZGProxyCommon::mgetDataByFields("mp_param_dataset_text", listID, {
            "id", "name", "dataModelID", "dataIndex", "deviceID",
            "rtRawValue", "rtNewValue", "rtSimulateValue", "rtQualityFlag", "rtStateFlag", "rtUpdateTime"
        },
        listData))
        return ZGWebModule::errorObject(u8"获取实时数据失败");
    ZG6000::StringList listModelID;
    for (const auto& data : listData)
    {
        listModelID.push_back(ZGUtils::get(data, "dataModelID"));
    }
    ZG6000::ListStringMap listModelData;
    if (!ZGProxyCommon::mgetDataByFields("mp_param_model_text", listModelID,
        {"isEnable", "dataCategoryID", "alarmLevelID"}, listModelData))
        return ZGWebModule::errorObject(u8"获取模型参数失败");
    for (size_t i = 0; i < listData.size(); ++i)
    {
        listData[i].insert(listModelData[i].begin(), listModelData[i].end());
    }
    for (auto& data : listData)
    {
        const auto& dataCategoryID = ZGUtils::get(data, "dataCategoryID");
        auto pair = m_mapDataCategory.find(dataCategoryID);
        if (pair != m_mapDataCategory.end())
        {
            data.insert(std::make_pair("dataCategoryName", pair->second["name"]));
            data.insert(std::make_pair("dataCategoryNameL2", pair->second["nameL2"]));
        }
        const auto& rtNewValue = ZGUtils::get(data, "rtNewValue");
        pair = m_mapDataCategoryProperty.find(dataCategoryID + "/" + rtNewValue);
        if (pair != m_mapDataCategoryProperty.end())
        {
            data.insert(std::make_pair("rtValueDesc", pair->second["name"]));
            data.insert(std::make_pair("rtValueDescL2", pair->second["nameL2"]));
        }
        else
        {
            data.insert(std::make_pair("rtValueDesc", ""));
            data.insert(std::make_pair("rtValueDescL2", ""));
        }
        const auto& alarmLevelID = ZGUtils::get(data, "alarmLevelID");
        pair = m_mapAlarmLevel.find(alarmLevelID);
        if (pair != m_mapAlarmLevel.end())
        {
            data.insert(std::make_pair("alarmLevelName", pair->second["name"]));
            data.insert(std::make_pair("alarmLevelNameL2", pair->second["nameL2"]));
        }
        else
        {
            data.insert(std::make_pair("alarmLevelName", ""));
            data.insert(std::make_pair("alarmLevelNameL2", ""));
        }
        const auto& deviceID = ZGUtils::get(data, "deviceID");
        pair = m_mapDevice.find(deviceID);
        if (pair != m_mapDevice.end())
        {
            data.insert(std::make_pair("deviceName", pair->second["name"]));
            data.insert(std::make_pair("deviceNameL2", pair->second["nameL2"]));
        }
        else
        {
            data.insert(std::make_pair("deviceName", ""));
            data.insert(std::make_pair("deviceNameL2", ""));
        }
    }
    return ZGWebModule::replyObject(ZGJson::qconvertToJson(listData));
}

ZGWebModule::Response ZGMPHandle::on_mp_ym_get(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req)
{
    ZG6000::StringList listID;
    if (param.isString())
    {
        const auto& datasetID = param.toString();
        QString sql = QString(
            "SELECT a.id FROM mp_param_dataset_ym a LEFT JOIN mp_param_dataset b ON a.datasetID = b.id "
            "LEFT JOIN mp_param_model_ym c ON a.dataModelID = c.id "
            "WHERE b.isEnable = 1 AND c.isEnable = 1 AND datasetID = '%1' ORDER BY a.id").arg(datasetID);
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
            return ZGWebModule::errorObject(QStringLiteral("获取数据集'%1'遥脉失败").arg(datasetID));
    }
    else if (param.isArray())
    {
        const auto& array = param.toArray();
        for (auto itemRef : array)
        {
            listID.push_back(itemRef.toString().toStdString());
        }
    }
    else
        return ZGWebModule::errorObject(QStringLiteral("无效的元素类型"));
    ZG6000::ListStringMap listData;
    if (!ZGProxyCommon::mgetDataByFields("mp_param_dataset_ym", listID, {
            "id", "name", "dataModelID", "dataIndex",
            "rtRawValue", "rtNewValue", "rtSimulateValue", "rtQualityFlag", "rtStateFlag", "rtUpdateTime"
        },
        listData))
        return ZGWebModule::errorObject(u8"获取实时数据失败");
    ZG6000::StringList listModelID;
    for (const auto& data : listData)
    {
        listModelID.push_back(ZGUtils::get(data, "dataModelID"));
    }
    ZG6000::ListStringMap listModelData;
    if (!ZGProxyCommon::mgetDataByFields("mp_param_model_ym", listModelID,
        {"isEnable", "dataCategoryID", "alarmLevelID"}, listModelData))
        return ZGWebModule::errorObject(u8"获取模型参数失败");
    for (size_t i = 0; i < listData.size(); ++i)
    {
        listData[i].insert(listModelData[i].begin(), listModelData[i].end());
    }
    for (auto& data : listData)
    {
        const auto& alarmLevelID = ZGUtils::get(data, "alarmLevelID");
        auto pair = m_mapAlarmLevel.find(alarmLevelID);
        if (pair != m_mapAlarmLevel.end())
        {
            data.insert(std::make_pair("alarmLevelName", pair->second["name"]));
            data.insert(std::make_pair("alarmLevelNameL2", pair->second["nameL2"]));
        }
        else
        {
            data.insert(std::make_pair("alarmLevelName", ""));
            data.insert(std::make_pair("alarmLevelNameL2", ""));
        }
    }
    return ZGWebModule::replyObject(ZGJson::qconvertToJson(listData));
}

ZGWebModule::Response ZGMPHandle::on_mp_appnode_yv_get(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    const auto& appNodeID = param.toString();
    QString sql = QString("SELECT a.* FROM mp_param_dataset_yv a LEFT JOIN mp_param_model_yv b ON a.dataModelID = b.id "
        "WHERE a.appNodeID = '%1' AND b.isEnable = 1 AND a.isEnableChannel = 1 "
        "AND (b.isPrivate <> 1 OR b.isPrivate IS NULL) ORDER BY a.id").arg(appNodeID);
    ZG6000::ListStringMap listYv;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listYv))
        return ZGWebModule::errorObject(QStringLiteral("获取应用节点关联视频失败"));
    const auto& yvArray = ZGWebModule::listStringMapToArray(listYv);
    return ZGWebModule::replyObject(yvArray);
}

ZGWebModule::Response ZGMPHandle::on_mp_appnode_yv_preset_get(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    const auto& appNodeID = param.toString();
    QString sql = QString("SELECT a.* FROM mp_param_dataset_yv a LEFT JOIN mp_param_model_yv b ON a.dataModelID = b.id "
        "WHERE a.appNodeID = '%1' AND b.isEnable = 1 AND b.isPrivate <> 1 ORDER BY a.id").arg(appNodeID);
    ZG6000::ListStringMap listYv;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listYv))
        return ZGWebModule::errorObject(QStringLiteral("获取应用节点视频列表失败"));
    QJsonArray yvArray;
    for (const auto& yv : listYv)
    {
        auto yvObj = ZGWebModule::stringMapToObject(yv);
        const auto& yvID = ZGUtils::get(yv, "id");
        sql = QString("SELECT presetNo, name FROM mp_param_dataset_yv_preset WHERE yvID = '%1'").arg(yvID.c_str());
        ZG6000::ListStringMap listPreset;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listPreset))
            return ZGWebModule::errorObject(QStringLiteral("获取视频预置点失败"));
        auto presetArray = ZGWebModule::listStringMapToArray(listPreset);
        yvObj["presets"] = presetArray;
        yvArray.append(yvObj);
    }
    return ZGWebModule::replyObject(yvArray);
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_preset_get(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& yvID = param.toString();
    QString sql = QString("SELECT presetNo, name FROM mp_param_dataset_yv_preset WHERE yvID = '%1'").arg(yvID);
    ZG6000::ListStringMap listPreset;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listPreset))
        return ZGWebModule::errorObject(QStringLiteral("获取视频预置位失败"));
    const auto& presetArray = ZGWebModule::listStringMapToArray(listPreset);
    return ZGWebModule::replyObject(presetArray);
}

ZGWebModule::Response ZGMPHandle::on_mp_dev_yk(const QString& clientID,
                                               const QVariantMap& headers,
                                               const QJsonValue& param,
                                               const QHttpServerRequest& req)
{
    QString errMsg;
    return sendMessageToQueue(param, req, [&](ZGRedisClient* client,
                                              const QJsonDocument& doc)-> ZGWebModule::Response
    {
        return deviceCall(param, req,
            [&](std::shared_ptr<ZG6000::ZGMPDevicePropertyPrx> deviceProxy)-> ZGWebModule::Response
            {
                QJsonArray array = doc.array();
                for (auto ref : array)
                {
                    auto object = ref.toObject();
                    if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "property"}, errMsg))
                        return ZGWebModule::errorObject(errMsg);
                    const auto& deviceID = object["deviceID"].toString().toStdString();
                    const auto& property = object["property"].toString().toStdString();
                    std::string tableName, dataID;
                    ZG6000::ErrorInfo e;
                    if (!deviceProxy->getDataIDByProperty(deviceID, property, tableName, dataID, e))
                        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
                    bool isAuth;
                    if (!checkYkAuthorization(dataID.c_str(), isAuth, errMsg))
                        return ZGWebModule::errorObject(errMsg);
                    if (isAuth)
                    {
                        QJsonArray tokenArray;
                        if (!ZGWebModule::verifyAuthorization(clientID, headers, {"ZG_HP_CTRL"}, tokenArray, errMsg))
                            return ZGWebModule::errorObject(errMsg);
                    }
                    object.remove("deviceID");
                    object.remove("property");
                    object["id"] = dataID.c_str();
                    QJsonDocument newDoc(object);
                    QByteArray message = newDoc.toJson(QJsonDocument::Compact);
                    long long subscribeNum;
                    if (!client->rpush("ZG_Q_SYSTEM_YK", message, subscribeNum, errMsg))
                        return ZGWebModule::errorObject(errMsg);
                }
                return ZGWebModule::replyObject("");
            });
    });
}

ZGWebModule::Response ZGMPHandle::on_mp_dev_batch_yk(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonArray tokenArray;
    if (!ZGWebModule::verifyAuthorization(clientID, headers, {"ZG_HP_CTRL"}, tokenArray, errMsg))
        return ZGWebModule::errorObject(errMsg);
    ZGRedisClient* client = ZGRuntime::instance()->getRedisClientRTQueue();
    if (client == nullptr)
        return ZGWebModule::errorObject("getRedisClientRTQueue error.");
    QJsonObject object = param.toObject();
    if (!ZGWebModule::checkRequiredFields(object, {"devices", "property", "value"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& property = object["property"].toString().toStdString();
    const auto& value = object["value"].toString().toStdString();
    const auto& deviceArray = object["devices"].toArray();
    return deviceCall(param, req,
        [&](std::shared_ptr<ZG6000::ZGMPDevicePropertyPrx> deviceProxy)-> ZGWebModule::Response
        {
            ZG6000::ListStringMap listYk;
            for (auto deviceRef : deviceArray)
            {
                const auto& deviceID = deviceRef.toString().toStdString();
                std::string tableName, dataID;
                ZG6000::ErrorInfo e;
                if (!deviceProxy->getDataIDByProperty(deviceID, property, tableName, dataID, e))
                    return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
                ZG6000::StringMap yk;
                yk["commandID"] = "ZG_DC_YK_EXEC";
                yk["id"] = dataID;
                yk["rtCode"] = std::to_string(QRandomGenerator::global()->generate());
                yk["srcType"] = "auto";
                yk["srcID"] = "-1";
                yk["rtValue"] = value;
                yk["rtCommandTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
                listYk.push_back(std::move(yk));
            }
            const auto& json = ZGJson::convertToJson(listYk);
            long long subscribeNum;
            if (!client->rpush("ZG_Q_SYSTEM_YK", json.c_str(), subscribeNum, errMsg))
                return ZGWebModule::errorObject(errMsg);
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_ctrl_act_get(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"ctrls", "runMode"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& ctrlArray = object["ctrls"].toArray();
    auto runMode = object["runMode"].toString().toStdString();
    if (runMode.empty())
        runMode = "ZG_RM_NORMAL";
    std::vector<std::pair<std::string, std::string>> vecCtrl;
    for (auto ctrl : ctrlArray)
    {
        const auto& ctrlObj = ctrl.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(ctrlObj, {"id", "tableName"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        vecCtrl.emplace_back(std::make_pair(ctrlObj["tableName"].toString().toStdString(),
            ctrlObj["id"].toString().toStdString()));
    }
    return getCtrlActions(clientID.toStdString(), vecCtrl, runMode);
}

ZGWebModule::Response ZGMPHandle::on_mp_dev_act_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "runMode"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& deviceID = object["deviceID"].toString().toStdString();
    auto runMode = object["runMode"].toString().toStdString();
    if (runMode.empty())
        runMode = "ZG_RM_NORMAL";
    ZG6000::StringList listTableName{"mp_param_dataset_yk", "mp_param_dataset_ys", "mp_param_dataset_yt"};
    std::vector<std::pair<std::string, std::string>> vecCtrl;
    for (const auto& tableName : listTableName)
    {
        std::string sql = "SELECT id FROM " + tableName + " WHERE deviceID = '" + deviceID + "'";
        ZG6000::StringList listID;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listID))
            return ZGWebModule::errorObject(
                QStringLiteral("获取设备'%1'表'%2'属性失败").arg(deviceID.c_str()).arg(tableName.c_str()));
        for (const auto& id : listID)
        {
            vecCtrl.emplace_back(std::make_pair(tableName, id));
        }
    }
    return getCtrlActions(clientID.toStdString(), vecCtrl, runMode);
}

ZGWebModule::Response ZGMPHandle::on_mp_dev_ctrl_allow(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    const auto& deviceID = param.toString().toStdString();
    auto deviceProxy = ZGProxyMng::instance()->getProxyMPDeviceProperty();
    if (deviceProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取设备属性服务代理对象失败"));
    try
    {
        ZG6000::ErrorInfo e;
        bool allow;
        ZG6000::StringMap conditions;
        if (!deviceProxy->isAllowCtrl(deviceID, allow, conditions, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(allow);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_dev_update(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& devArray = param.toArray();
    ZG6000::StringList listSql;
    for (auto device : devArray)
    {
        const auto& devObj = device.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(devObj, {"id"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        if (devObj.size() == 1)
            return ZGWebModule::errorObject(u8"缺少更新字段");
        QString sql = "UPDATE mp_param_device SET ";
        auto it = devObj.begin();
        QString devID;
        while (it != devObj.end())
        {
            if (it.key() == "id")
                devID = it.value().toString();
            else
                sql += QString("%1='%2',").arg(it.key()).arg(it.value().toString());
            ++it;
        }
        sql.chop(1);
        sql += QString(" WHERE id = '%1';").arg(devID);
        ZGLOG_DEBUG(sql);
        listSql.push_back(sql.toStdString());
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(u8"更新设备参数失败");
    ZGProxyCommon::synchronize();
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_ctrl_rule_get(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "value", "type"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    auto rulePrx = ZGProxyMng::instance()->getProxyMPRuleEngine();
    if (rulePrx == nullptr)
        return ZGWebModule::errorObject(u8"获取规则引擎服务代理失败");
    const auto& id = object["id"].toString().toStdString();
    const auto& value = object["value"].toString().toStdString();
    const auto& type = object["type"].toString().toStdString();
    auto it = object.find("mode");
    ZG6000::ListStringMap listRecord;
    try
    {
        ZG6000::ErrorInfo e;
        if (it != object.end())
        {
            const auto& mode = object["mode"].toString().toStdString();
            if (!rulePrx->getCommandConditionWithRunMode(id, value, mode, type, listRecord, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        }
        else
        {
            if (!rulePrx->getCommandCondition(id, value, type, listRecord, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        }
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
    return convertRuleItem(listRecord);
}

ZGWebModule::Response ZGMPHandle::on_mp_device_rule_get(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "propertyName", "propertyValue", "type"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    auto rulePrx = ZGProxyMng::instance()->getProxyMPRuleEngine();
    if (rulePrx == nullptr)
        return ZGWebModule::errorObject(u8"获取规则引擎服务代理失败");
    const auto& deviceID = object["deviceID"].toString().toStdString();
    const auto& propertyName = object["propertyName"].toString().toStdString();
    const auto& propertyValue = object["propertyValue"].toString().toStdString();
    const auto& type = object["type"].toString().toStdString();
    auto it = object.find("mode");
    std::string tableName, dataID;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    ZG6000::ListStringMap listRecord;
    try
    {
        if (it != object.end())
        {
            const auto& mode = object["mode"].toString().toStdString();
            if (!rulePrx->getCommandConditionWithRunMode(dataID, propertyValue, type, mode, listRecord, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        }
        else
        {
            if (!rulePrx->getCommandCondition(dataID, propertyValue, type, listRecord, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        }
        return convertRuleItem(listRecord);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_ctrl_rule_id_get(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req)
{
    return ruleCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRuleEnginePrx> ruleProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"id", "value", "conditionTypeID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& id = object["id"].toString().toStdString();
            const auto& value = object["value"].toString().toStdString();
            const auto& conditionTypeID = object["conditionTypeID"].toString().toStdString();
            std::string ruleID;
            ZG6000::ErrorInfo e;
            if (!ruleProxy->getCommandRuleID(id, value, conditionTypeID, ruleID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject(ruleID.c_str());
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_ctrl_rule_add(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    return ruleCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRuleEnginePrx> ruleProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"id", "value", "conditionTypeID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& id = object["id"].toString().toStdString();
            const auto& value = object["value"].toString().toStdString();
            const auto& conditionTypeID = object["conditionTypeID"].toString().toStdString();
            std::string ruleID;
            ZG6000::ErrorInfo e;
            if (!ruleProxy->addCommandRule(id, value, conditionTypeID, ruleID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject(ruleID.c_str());
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_ctrl_rule_delete(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req)
{
    return ruleCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRuleEnginePrx> ruleProxy) -> ZGWebModule::Response
        {
            const auto& ruleID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!ruleProxy->deleteRule(ruleID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_ctrl_rule_item_add(const QString& clientID,
                                                           const QVariantMap& headers,
                                                           const QJsonValue& param,
                                                           const QHttpServerRequest& req)
{
    return ruleCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRuleEnginePrx> ruleProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"ruleID", "deviceID", "propertyName", "operatorID", "value"},
                errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& ruleID = object["ruleID"].toString().toStdString();
            const auto& deviceID = object["deviceID"].toString().toStdString();
            const auto& propertyName = object["propertyName"].toString().toStdString();
            const auto& operatorID = object["operatorID"].toString().toStdString();
            const auto& value = object["value"].toString().toStdString();
            ZG6000::ErrorInfo e;
            std::string ruleItemID;
            if (!ruleProxy->addRuleArgument(ruleID, deviceID, propertyName, operatorID, value, ruleItemID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject(ruleItemID.c_str());
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_ctrl_rule_item_update(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    return ruleCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRuleEnginePrx> ruleProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"itemID", "deviceID", "propertyName", "operatorID", "value"},
                errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& itemID = object["itemID"].toString().toStdString();
            const auto& deviceID = object["deviceID"].toString().toStdString();
            const auto& propertyName = object["propertyName"].toString().toStdString();
            const auto& operatorID = object["operatorID"].toString().toStdString();
            const auto& value = object["value"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!ruleProxy->updateRuleArgument(itemID, deviceID, propertyName, operatorID, value, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_ctrl_rule_item_delete(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    return ruleCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRuleEnginePrx> ruleProxy) -> ZGWebModule::Response
        {
            const auto& itemID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!ruleProxy->deleteRuleArgument(itemID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_ctrl_unlock_set(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    const auto& array = param.toArray();
    ZG6000::StringList listSql;
    for (auto ctrlRef : array)
    {
        const auto& ctrlObj = ctrlRef.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(ctrlObj, {"tableName", "dataID", "unlockCode"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        QString sql = QString("UPDATE %1 SET unlockCode = '%2' WHERE id = '%3'").arg(ctrlObj["tableName"].toString())
            .arg(ctrlObj["unlockCode"].toString()).arg(ctrlObj["dataID"].toString());
        listSql.push_back(sql.toStdString());
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
        return ZGWebModule::errorObject(QStringLiteral("更新遥控解锁码失败!"));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_dataid_get(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "property"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    auto deviceProxy = ZGProxyMng::instance()->getProxyMPDeviceProperty();
    if (deviceProxy == nullptr)
        return ZGWebModule::errorObject(u8"获取设备管理服务失败");
    try
    {
        const auto& deviceID = object["deviceID"].toString().toStdString();
        const auto& prop = object["property"].toString().toStdString();
        std::string tableName;
        std::string dataID;
        ZG6000::ErrorInfo e;
        if (!deviceProxy->getDataIDByProperty(deviceID, prop, tableName, dataID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(dataID.c_str());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_dataids_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    auto jsonObject = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(jsonObject, {"devices", "property"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    auto deviceArray = jsonObject["devices"].toArray();
    const auto& propertyName = jsonObject["property"].toString();
    auto deviceProxy = ZGProxyMng::instance()->getProxyMPDeviceProperty();
    if (deviceProxy == nullptr)
        return ZGWebModule::errorObject(u8"获取设备管理服务失败");
    try
    {
        ZG6000::ListStringMap listData;
        for (auto deviceIDRef : deviceArray)
        {
            const auto& deviceID = deviceIDRef.toString();
            std::string tableName, dataID;
            ZG6000::ErrorInfo e;
            if (!ZGProxyCommon::getDataIDByProperty(deviceID.toStdString(), propertyName.toStdString(), tableName,
                dataID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            ZG6000::StringMap data;
            data["tableName"] = tableName;
            data["dataID"] = dataID;
            listData.push_back(std::move(data));
        }
        const auto& json = ZGJson::convertToJson(listData);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_category_property_get(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    const auto& categoryID = param.toString();
    QString sql = QString(
            "SELECT propName, propNameL2, propValue FROM mp_param_data_category_property WHERE dataCategoryID = '%1'").
        arg(categoryID);
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
        return ZGWebModule::errorObject(QStringLiteral("获取数据类别'%1'属性失败").arg(categoryID));
    const auto& json = ZGJson::convertToJson(listRecord);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.array());
    //    const auto& object = param.toObject();
    //    QString errMsg;
    //    if (!ZGWebModule::checkRequiredFields(object, {"tableName", "dataID"}, errMsg))
    //        return ZGWebModule::errorObject(errMsg);
    //    const auto& tableName = object["tableName"].toString();
    //    QString modelTableName = tableName;
    //    modelTableName.replace("dataset", "model");
    //    const auto& dataID = object["dataID"].toString();
    //    QString sql = QString("SELECT mp_param_data_category_property.propName, mp_param_data_category_property.propValue FROM mp_param_data_category_property "
    //        "LEFT JOIN %1 ON mp_param_data_category_property.dataCategoryID = %1.dataCategoryID "
    //        "LEFT JOIN %2 ON %1.id = %2.dataModelID "
    //        "WHERE %2.id = '%3'").arg(modelTableName).arg(tableName).arg(dataID);
    //    ZG6000::ListStringMap listRecord;
    //    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
    //        return ZGWebModule::errorObject(QStringLiteral("获取表'%1'数据'%2'类别属性失败").arg(tableName).arg(dataID));
    //    const auto& json = ZGJson::convertToJson(listRecord);
    //    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    //    return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGMPHandle::on_mp_topics_get(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest&/*req*/)
{
    ZG6000::StringList listTopic;
    const auto& subsystemID = param.toString().toStdString();
    std::string sql = "SELECT a.datasetID FROM mp_param_appnode_dataset AS a LEFT JOIN sp_param_client_major AS c "
        "ON CONCAT(a.subsystemID,a.majorID) = CONCAT(c.subsystemID,c.majorID) WHERE c.clientID = '" + clientID.
        toStdString() + "'"
        " AND c.subsystemID = '" + subsystemID + "'" + " ORDER BY a.id";
    ZG6000::StringList listDataset;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listDataset))
    {
        return ZGWebModule::errorObject("get appNode dataset error.");
    }
    for (const auto& dataset : listDataset)
    {
        listTopic.push_back(dataset + "/yc");
        listTopic.push_back(dataset + "/yx");
        listTopic.push_back(dataset + "/text");
    }
    sql = "SELECT a.deviceID FROM mp_param_appnode_device AS a LEFT JOIN sp_param_client_major AS c "
        "ON CONCAT(a.subsystemID,a.majorID) = CONCAT(c.subsystemID,c.majorID) WHERE c.clientID = '" + clientID.
        toStdString() + "'"
        " AND c.subsystemID = '" + subsystemID + "'" + " ORDER BY a.id";
    ZG6000::StringList listDevice;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listDevice))
    {
        return ZGWebModule::errorObject("get appNode equipment error.");
    }
    for (const auto& device : listDevice)
    {
        std::string topic = "mp_param_device/" + device;
        listTopic.push_back(topic);
    }
    QJsonArray array;
    for (const auto& topic : listTopic)
    {
        array.append(topic.c_str());
    }
    return ZGWebModule::replyObject(array);
}

ZGWebModule::Response ZGMPHandle::on_mp_event_topics_get(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req)
{
    QString sql = QString("SELECT rtAppNodeID, rtSubsystemID FROM sp_param_client WHERE id = '%1'").arg(clientID);
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
        return ZGWebModule::errorObject(QStringLiteral("获取客户端子系统失败"));
    ZG6000::StringList listTopic;
    for (auto& record : listRecord)
    {
        listTopic.push_back(record["rtAppNodeID"] + "/" +
            record["rtSubsystemID"] + "/event");
    }
    ZGLOG_TRACE(QString("listTopic: %1").arg(listTopic.size()));
    auto array = ZGWebModule::stringListToArray(listTopic);
    return ZGWebModule::replyObject(array);
}

ZGWebModule::Response ZGMPHandle::on_mp_dev_get(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    QString sql = QString(
        "SELECT a.deviceID, b.name AS deviceName, b.nameL2 AS deviceNameL2 FROM mp_param_appnode_device a "
        "LEFT JOIN mp_param_device b ON a.deviceID = b.id ORDER BY a.id").arg(param.toString());
    const auto& object = param.toObject();
    if (param.isObject())
    {
        ZG6000::ListStringMap listRecord1, listRecord2;
        QStringList listCondition;
        listCondition.append(QString("b.isEnable = 1"));
        if (object.contains("appNodeID"))
            listCondition.append(QString("a.appNodeID = '%1'").arg(object["appNodeID"].toString()));
        if (object.contains("subsystemID"))
            listCondition.append(QString("a.subsystemID = '%1'").arg(object["subsystemID"].toString()));
        if (object.contains("majorID"))
            listCondition.append(QString("a.majorID = '%1'").arg(object["majorID"].toString()));
        QString condition = QString(" WHERE %1").arg(listCondition.join(" AND "));
        QString newSql = sql + condition;
        ZGLOG_TRACE(newSql);
        if (!ZGProxyCommon::execQuerySql(newSql.toStdString(), listRecord1))
            return ZGWebModule::errorObject(QStringLiteral("获取应用节点关联设备失败"));
        sql = "SELECT id AS deviceID, name AS deviceName, nameL2 AS deviceNameL2 FROM mp_param_device";
        listCondition.clear();
        listCondition.append(QString("isEnable = 1"));
        if (object.contains("appNodeID"))
            listCondition.append(QString("appNodeID = '%1'").arg(object["appNodeID"].toString()));
        if (object.contains("subsystemID"))
            listCondition.append(QString("subsystemID = '%1'").arg(object["subsystemID"].toString()));
        if (object.contains("majorID"))
            listCondition.append(QString("majorID = '%1'").arg(object["majorID"].toString()));
        condition = QString(" WHERE %1").arg(listCondition.join(" AND "));
        newSql = sql + condition;
        ZGLOG_TRACE(newSql);
        if (!ZGProxyCommon::execQuerySql(newSql.toStdString(), listRecord2))
            return ZGWebModule::errorObject(QStringLiteral("获取设备失败"));
        std::move(listRecord2.begin(), listRecord2.end(), std::back_inserter(listRecord1));
        const auto& json = ZGJson::convertToJson(listRecord1);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    }
    else
    {
        sql =
            "SELECT id AS deviceID, name AS deviceName, nameL2 AS deviceNameL2 FROM mp_param_device WHERE isEnable = 1";
        return ZGWebModule::replyStringMap(sql.toStdString());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_dataset_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    QString sql =
        "SELECT a.datasetID, b.name AS datasetName, b.nameL2 AS datasetNameL2 FROM mp_param_appnode_dataset a "
        "LEFT JOIN mp_param_dataset b ON a.datasetID = b.id ORDER BY a.id";
    const auto& object = param.toObject();
    if (!object.empty())
    {
        ZG6000::ListStringMap listRecord1, listRecord2;
        QStringList listCondition;
        listCondition.append(QString("b.isEnable = 1"));
        listCondition.append(QString("b.modelID <> 'dm_bt'"));
        if (object.contains("appNodeID"))
            listCondition.append(QString("a.appNodeID = '%1'").arg(object["appNodeID"].toString()));
        if (object.contains("subsystemID"))
            listCondition.append(QString("a.subsystemID = '%1'").arg(object["subsystemID"].toString()));
        if (object.contains("majorID"))
            listCondition.append(QString("a.majorID = '%1'").arg(object["majorID"].toString()));
        QString condition = QString(" WHERE %1").arg(listCondition.join(" AND "));
        QString newSql = sql + condition;
        ZGLOG_TRACE(newSql);
        if (!ZGProxyCommon::execQuerySql(newSql.toStdString(), listRecord1))
            return ZGWebModule::errorObject(QStringLiteral("获取应用节点关联数据集失败"));
        sql = "SELECT id AS datasetID, name AS datasetName, nameL2 AS datasetNameL2 FROM mp_param_dataset";
        listCondition.clear();
        listCondition.append(QString("isEnable = 1"));
        listCondition.append(QString("modelID <> 'dm_bt'"));
        if (object.contains("appNodeID"))
            listCondition.append(QString("appNodeID = '%1'").arg(object["appNodeID"].toString()));
        if (object.contains("subsystemID"))
            listCondition.append(QString("subsystemID = '%1'").arg(object["subsystemID"].toString()));
        if (object.contains("majorID"))
            listCondition.append(QString("majorID = '%1'").arg(object["majorID"].toString()));
        condition = QString(" WHERE %1").arg(listCondition.join(" AND "));
        newSql = sql + condition;
        ZGLOG_TRACE(newSql);
        if (!ZGProxyCommon::execQuerySql(newSql.toStdString(), listRecord2))
            return ZGWebModule::errorObject(QStringLiteral("获取数据集失败"));
        std::move(listRecord2.begin(), listRecord2.end(), std::back_inserter(listRecord1));
        const auto& json = ZGJson::convertToJson(listRecord1);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    }
    else
    {
        sql = "SELECT id AS datasetID, name AS datasetName, nameL2 AS datasetNameL2 FROM mp_param_dataset "
            "WHERE isEnable = 1 AND modelID <> 'dm_bt'";
        return ZGWebModule::replyStringMap(sql.toStdString());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_store_yc_get(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    QString datasetID = param.toString();
    if (datasetID.isEmpty())
        return ZGWebModule::errorObject(QStringLiteral("没有指定数据集ID"));
    QString sql = QString(
        "SELECT a.id, a.name, a.nameL2, a.datasetID FROM mp_param_dataset_yc a LEFT JOIN mp_param_device b ON a.deviceID = b.id "
        "WHERE b.isEnable = '1' AND a.datasetID = '%1' AND storeTypeID != '' ORDER BY a.id").arg(datasetID);
    return ZGWebModule::replyStringMap(sql.toStdString());
}

ZGWebModule::Response ZGMPHandle::on_mp_store_yx_get(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    QString datasetID = param.toString();
    if (datasetID.isEmpty())
        return ZGWebModule::errorObject(QStringLiteral("没有指定数据集ID"));
    QString sql = QString(
        "SELECT a.id, a.name, a.nameL2, a.datasetID FROM mp_param_dataset_yx a LEFT JOIN mp_param_device b ON a.deviceID = b.id "
        "WHERE b.isEnable = '1' AND a.datasetID = '%1' AND storeTypeID != '' ORDER BY a.id").arg(datasetID);
    return ZGWebModule::replyStringMap(sql.toStdString());
}

ZGWebModule::Response ZGMPHandle::on_mp_store_text_get(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    QString datasetID = param.toString();
    if (datasetID.isEmpty())
        return ZGWebModule::errorObject(QStringLiteral("没有指定数据集ID"));
    QString sql = QString(
        "SELECT a.id, a.name, a.nameL2, a.datasetID FROM mp_param_dataset_text a LEFT JOIN mp_param_device b ON a.deviceID = b.id "
        "WHERE b.isEnable = '1' AND a.datasetID = '%1' AND storeTypeID != '' ORDER BY a.id").arg(datasetID);
    return ZGWebModule::replyStringMap(sql.toStdString());
}

ZGWebModule::Response ZGMPHandle::on_mp_statistic_data_get(const QString& clientID,
                                                           const QVariantMap& headers,
                                                           const QJsonValue& param,
                                                           const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"tableName"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& tableName = object["tableName"].toString();
    QString sql = QString("SELECT a.id, a.name AS statisticName FROM sp_param_statistic a "
        "LEFT JOIN %1 b ON a.dataID = b.id "
        "LEFT JOIN mp_param_appnode_dataset c ON c.datasetID = b.datasetID ORDER BY a.id").arg(tableName);
    QStringList listCondition;
    if (object.contains("appNodeID"))
    {
        listCondition.append(QString("c.appNodeID = '%1'").arg(object["appNodeID"].toString()));
    }
    if (object.contains("subsystemID"))
    {
        listCondition.append(QString("c.subsystemID = '%1'").arg(object["subsystemID"].toString()));
    }
    if (object.contains("majorID"))
    {
        listCondition.append(QString("c.majorID = '%1'").arg(object["majorID"].toString()));
    }
    if (object.contains("statisticIntervalID"))
    {
        listCondition.append(QString("a.statisticIntervalID = '%1'").arg(object["statisticIntervalID"].toString()));
    }
    if (!listCondition.empty())
    {
        QString condition = QString(" WHERE %1").arg(listCondition.join(" AND "));
        sql += condition;
    }
    return ZGWebModule::replyStringMap(sql.toStdString());
}

ZGWebModule::Response ZGMPHandle::on_mp_data_simulate_set(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "tableName", "value"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& id = object["id"].toString().toStdString();
    const auto& tableName = object["tableName"].toString().toStdString();
    const auto& value = object["value"].toString().toStdString();
    std::string stateFlag;
    if (value.empty())
        stateFlag = "0";
    else
        stateFlag = "1";
    ZG6000::StringMap record;
    record["rtSimulateValue"] = value;
    record["rtStateFlag"] = stateFlag;
    if (!ZGProxyCommon::updateDataByID(tableName, id, record))
        return ZGWebModule::errorObject(QStringLiteral("更新数据'%1'值'%2'失败").arg(id.c_str()).arg(value.c_str()));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_data_position_set(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "tableName", "value"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& id = object["id"].toString().toStdString();
    const auto& tableName = object["tableName"].toString().toStdString();
    const auto& value = object["value"].toString().toStdString();
    ZG6000::StringMap record;
    record["id"] = id;
    record["rtNewValue"] = value;
    record["rtUpdateTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
    const auto& sql = ZGUtils::generateUpdateSql(tableName, record);
    if (!ZGProxyCommon::execSql(sql))
        return ZGWebModule::errorObject(QStringLiteral("更新数据'%1'值'%2'失败").arg(id.c_str()).arg(value.c_str()));
    if (!ZGProxyCommon::updateDataByID(tableName, id, record))
        return ZGWebModule::errorObject(QStringLiteral("更新数据'%1'值'%2'失败").arg(id.c_str()).arg(value.c_str()));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_device_position_set(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "propertyName", "propertyValue"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& deviceID = object["deviceID"].toString().toStdString();
    const auto& propertyName = object["propertyName"].toString().toStdString();
    const auto& propertyValue = object["propertyValue"].toVariant().toString().toStdString();
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::updatePropertyValue(deviceID, propertyName, propertyValue, e, true))
        return ZGWebModule::errorObject(
            QStringLiteral("更新设备'%1'属性'%2'失败").arg(deviceID.c_str()).arg(propertyName.c_str()));
    if (!ZGProxyCommon::updatePropertyValue(deviceID, propertyName, propertyValue, e, false))
        return ZGWebModule::errorObject(
            QStringLiteral("更新设备'%1'属性'%2'失败").arg(deviceID.c_str()).arg(propertyName.c_str()));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_task_start(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return taskCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPTaskManagerPrx> taskProxy)-> ZGWebModule::Response
        {
            const auto& taskID = object["taskID"].toString().toStdString();
            const auto& operUser = object["operator"].toString().toStdString();
            const auto& monUser = object["monitor"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!taskProxy->startTask(clientID.toStdString(), operUser, monUser, taskID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_task_stop(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return taskCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPTaskManagerPrx> taskProxy)-> ZGWebModule::Response
        {
            const auto& taskID = object["taskID"].toString().toStdString();
            const auto& operUser = object["operator"].toString().toStdString();
            const auto& monUser = object["monitor"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!taskProxy->stopTask(clientID.toStdString(), operUser, monUser, taskID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            std::string sql = "SELECT id FROM mp_param_task_item WHERE taskID = '" + taskID + "'";
            ZG6000::StringList listItemID;
            if (!ZGProxyCommon::execQuerySqlCol(sql, listItemID))
                return ZGWebModule::errorObject(QStringLiteral("获取任务'%1'任务项出错").arg(taskID.c_str()));
            if (!listItemID.empty())
            {
                ZG6000::StringMap itemState;
                itemState["rtStartTime"] = "";
                itemState["rtEndTime"] = "";
                itemState["rtTaskItemStateID"] = "ZG_TS_UNEXECUTE";
                if (!ZGProxyCommon::mupdateDataByFields("mp_param_task_item", listItemID, itemState))
                    return ZGWebModule::errorObject(QStringLiteral("更新任务'%1'任务项出错").arg(taskID.c_str()));
            }
            ZG6000::StringMap taskState;
            taskState["rtOperUserID"] = "";
            taskState["rtMonUserID"] = "";
            taskState["rtStartTime"] = "";
            taskState["rtEndTime"] = "";
            taskState["rtCurrentItemID"] = "-1";
            taskState["rtTaskStateID"] = "ZG_TS_UNEXECUTE";
            if (!ZGProxyCommon::updateDataByID("mp_param_task", taskID, taskState))
                return ZGWebModule::errorObject(QStringLiteral("更新任务'%1'出错").arg(taskID.c_str()));
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_task_pause(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return taskCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPTaskManagerPrx> taskProxy)-> ZGWebModule::Response
        {
            const auto& taskID = object["taskID"].toString().toStdString();
            const auto& operUser = object["operator"].toString().toStdString();
            const auto& monUser = object["monitor"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!taskProxy->pauseTask(clientID.toStdString(), operUser, monUser, taskID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_task_resume(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return taskCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPTaskManagerPrx> taskProxy)-> ZGWebModule::Response
        {
            const auto& taskID = object["taskID"].toString().toStdString();
            const auto& operUser = object["operator"].toString().toStdString();
            const auto& monUser = object["monitor"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!taskProxy->resumeTask(clientID.toStdString(), operUser, monUser, taskID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_task_reset(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& taskID = object["taskID"].toString().toStdString();
    std::string sql = "SELECT id FROM mp_param_task_item WHERE taskID = '" + taskID + "'";
    ZG6000::StringList listItemID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listItemID))
        return ZGWebModule::errorObject(QStringLiteral("获取任务'%1'任务项出错").arg(taskID.c_str()));
    if (!listItemID.empty())
    {
        ZG6000::StringMap itemState;
        itemState["rtStartTime"] = "";
        itemState["rtEndTime"] = "";
        itemState["rtTaskItemStateID"] = "ZG_TS_UNEXECUTE";
        if (!ZGProxyCommon::mupdateDataByFields("mp_param_task_item", listItemID, itemState))
            return ZGWebModule::errorObject(QStringLiteral("更新任务'%1'任务项出错").arg(taskID.c_str()));
    }
    ZG6000::StringMap taskState;
    taskState["rtOperUserID"] = "";
    taskState["rtMonUserID"] = "";
    taskState["rtStartTime"] = "";
    taskState["rtEndTime"] = "";
    taskState["rtCurrentItemID"] = "-1";
    taskState["rtTaskStateID"] = "ZG_TS_UNEXECUTE";
    if (!ZGProxyCommon::updateDataByID("mp_param_task", taskID, taskState))
        return ZGWebModule::errorObject(QStringLiteral("更新任务'%1'出错").arg(taskID.c_str()));
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_task_get(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& taskID = param.toString().toStdString();
    return taskCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPTaskManagerPrx> taskProxy)-> ZGWebModule::Response
        {
            ZG6000::StringMap task;
            ZG6000::ErrorInfo e;
            if (!taskProxy->getTask(taskID, task, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            const auto& json = ZGJson::convertToJson(task);
            QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
            return ZGWebModule::replyObject(doc.object());
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_task_list_get(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"appNodeID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    QString appNodeID, subsystemID, majorID, taskTypeID;
    appNodeID = object["appNodeID"].toString();
    if (object.contains("subsystemID"))
        subsystemID = object["subsystemID"].toString();
    if (object.contains("majorID"))
        majorID = object["majorID"].toString();
    if (object.contains("taskTypeID"))
        taskTypeID = object["taskTypeID"].toString();
    return taskCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPTaskManagerPrx> taskProxy)-> ZGWebModule::Response
        {
            ZG6000::ListStringMap listTask;
            ZG6000::ErrorInfo e;
            if (!taskProxy->getTaskList(appNodeID.toStdString(), subsystemID.toStdString(), majorID.toStdString(),
                taskTypeID.toStdString(), listTask, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            const auto& json = ZGJson::convertToJson(listTask);
            QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
            return ZGWebModule::replyObject(doc.array());
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_task_exec_rule_get(const QString& clientID,
                                                           const QVariantMap& headers,
                                                           const QJsonValue& param,
                                                           const QHttpServerRequest& req)
{
    const auto& taskID = param.toString().toStdString();
    const auto& taskProxy = ZGProxyMng::instance()->getProxyMPTaskManager();
    if (taskProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取任务管理服务代理对象失败"));
    try
    {
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getExecCondition(taskID, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return convertRuleItem(listRecord);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_task_conf_rule_get(const QString& clientID,
                                                           const QVariantMap& headers,
                                                           const QJsonValue& param,
                                                           const QHttpServerRequest& req)
{
    const auto& taskID = param.toString().toStdString();
    const auto& taskProxy = ZGProxyMng::instance()->getProxyMPTaskManager();
    if (taskProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取任务管理服务代理对象失败"));
    try
    {
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getConfirmCondition(taskID, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return convertRuleItem(listRecord);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_task_error_rule_get(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    const auto& taskID = param.toString().toStdString();
    const auto& taskProxy = ZGProxyMng::instance()->getProxyMPTaskManager();
    if (taskProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取任务管理服务代理对象失败"));
    try
    {
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getErrorCondition(taskID, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return convertRuleItem(listRecord);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_task_item_get(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    return taskCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPTaskManagerPrx> taskProxy)-> ZGWebModule::Response
        {
            const auto& taskID = param.toString();
            ZG6000::ErrorInfo e;
            ZG6000::ListStringMap listTaskItem;
            if (!taskProxy->getTaskItems(taskID.toStdString(), listTaskItem, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            const auto& json = ZGJson::convertToJson(listTaskItem);
            QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
            return ZGWebModule::replyObject(doc.array());
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_devices_properties_all_get(const QString& clientID,
                                                                   const QVariantMap& headers,
                                                                   const QJsonValue& param,
                                                                   const QHttpServerRequest& req)
{
    return deviceCall(param, req,
        [&](std::shared_ptr<ZG6000::ZGMPDevicePropertyPrx> deviceProxy)-> ZGWebModule::Response
        {
            const auto& array = param.toArray();
            ZG6000::StringList listDeviceID;
            for (auto element : array)
            {
                listDeviceID.push_back(element.toString().toStdString());
            }
            ZG6000::MapMapStringMap mapProperties;
            ZG6000::ErrorInfo e;
            if (!deviceProxy->mgetPropertiesAll(listDeviceID, mapProperties, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            QJsonObject root;
            for (const auto& [deviceID, propeties] : mapProperties)
            {
                QJsonObject deviceObj;
                for (const auto& [name, property] : propeties)
                {
                    QJsonObject propObj;
                    for (const auto& [key, value] : property)
                    {
                        propObj[key.c_str()] = value.c_str();
                    }
                    deviceObj[name.c_str()] = propObj;
                }
                root[deviceID.c_str()] = deviceObj;
            }
            return ZGWebModule::replyObject(root);
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_device_properties_group_get(const QString& clientID,
                                                                    const QVariantMap& headers,
                                                                    const QJsonValue& param,
                                                                    const QHttpServerRequest& req)
{
    return deviceCall(param, req,
        [&](std::shared_ptr<ZG6000::ZGMPDevicePropertyPrx> deviceProxy)-> ZGWebModule::Response
        {
            std::string deviceID = param.toString().toStdString();
            ZG6000::MapMapStringMap mapProperties;
            ZG6000::ErrorInfo e;
            if (!deviceProxy->getGroupProperties(deviceID, mapProperties, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            QJsonObject root;
            for (const auto& [groupType, propeties] : mapProperties)
            {
                QJsonObject groupObj;
                for (const auto& [name, property] : propeties)
                {
                    QJsonObject propObj;
                    for (const auto& [key, value] : property)
                    {
                        propObj[key.c_str()] = value.c_str();
                    }
                    groupObj[name.c_str()] = propObj;
                }
                root[groupType.c_str()] = groupObj;
            }
            return ZGWebModule::replyObject(root);
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_devices_properties_get(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    const auto& obj = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(obj, {"devices", "properties"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& devArr = obj["devices"].toArray();
    ZG6000::StringList listDeviceID;
    for (auto devRef : devArr)
    {
        listDeviceID.push_back(devRef.toString().toStdString());
    }
    const auto& propArr = obj["properties"].toArray();
    ZG6000::StringList listProp;
    for (auto propRef : propArr)
    {
        listProp.push_back(propRef.toString().toStdString());
    }
    ZG6000::MapMapStringMap mapProperties;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::mgetProperties(listDeviceID, listProp, mapProperties, e))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    QJsonObject root;
    for (const auto& [deviceID, properties] : mapProperties)
    {
        QJsonObject devObj;
        for (const auto& [propName, property] : properties)
        {
            QJsonObject propObj;
            for (const auto& [paramName, value] : property)
            {
                propObj[paramName.c_str()] = value.c_str();
            }
            devObj[propName.c_str()] = propObj;
        }
        root[deviceID.c_str()] = devObj;
    }
    return ZGWebModule::replyObject(root);
}

ZGWebModule::Response ZGMPHandle::on_mp_devices_properties_update(const QString& clientID,
                                                                  const QVariantMap& headers,
                                                                  const QJsonValue& param,
                                                                  const QHttpServerRequest& req)
{
    const auto& obj = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(obj, {"properties"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& devicesPropertiesObj = obj["properties"].toObject();
    ZG6000::MapMapStringMap mapProperties;
    for (auto itDev = devicesPropertiesObj.begin(); itDev != devicesPropertiesObj.end(); ++itDev)
    {
        ZG6000::MapStringMap properties;
        const auto& deviceID = itDev.key();
        const auto& propertiesObj = itDev.value().toObject();
        for (auto itProp = propertiesObj.begin(); itProp != propertiesObj.end(); ++itProp)
        {
            ZG6000::StringMap property;
            const auto& propName = itProp.key();
            const auto& propertyObj = itProp.value().toObject();
            for (auto it = propertyObj.begin(); it != propertyObj.end(); ++it)
            {
                const auto& paramName = it.key();
                const auto& paramValue = it.value().toString();
                property[paramName.toStdString()] = paramValue.toStdString();
            }
            properties[propName.toStdString()] = std::move(property);
        }
        mapProperties[deviceID.toStdString()] = std::move(properties);
    }
    ZG6000::ErrorInfo e;
    bool saveToDb = false;
    if (obj.contains("saveToDb"))
        saveToDb = obj["saveToDb"].toBool();
    if (!ZGProxyCommon::mupdateProperties(mapProperties, e, saveToDb))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_device_property_get(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    const auto& obj = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(obj, {"deviceID", "propertyName"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& deviceID = obj["deviceID"].toString().toStdString();
    const auto& propertyName = obj["propertyName"].toString().toStdString();
    ZG6000::StringMap property;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getProperty(deviceID, propertyName, property, e))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    const auto& json = ZGJson::convertToJson(property);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.object());
}

ZGWebModule::Response ZGMPHandle::on_mp_device_property_update(const QString& clientID,
                                                               const QVariantMap& headers,
                                                               const QJsonValue& param,
                                                               const QHttpServerRequest& req)
{
    const auto& obj = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(obj, {"deviceID", "propertyName", "property"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& deviceID = obj["deviceID"].toString().toStdString();
    const auto& propertyName = obj["propertyName"].toString().toStdString();
    const auto& propObj = obj["property"].toObject();
    ZG6000::StringMap property;
    for (auto it = propObj.begin(); it != propObj.end(); ++it)
    {
        property[it.key().toStdString()] = it.value().toString().toStdString();
    }
    ZG6000::ErrorInfo e;
    bool saveToDb = false;
    if (obj.contains("saveToDb"))
        saveToDb = obj["saveToDb"].toBool();
    if (!ZGProxyCommon::updateProperty(deviceID, propertyName, property, e, saveToDb))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_devices_property_values_get(const QString& clientID,
                                                                    const QVariantMap& headers,
                                                                    const QJsonValue& param,
                                                                    const QHttpServerRequest& req)
{
    const auto& obj = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(obj, {"devices", "properties"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& devArr = obj["devices"].toArray();
    ZG6000::StringList listDeviceID;
    for (auto devRef : devArr)
    {
        listDeviceID.push_back(devRef.toString().toStdString());
    }
    const auto& propArr = obj["properties"].toArray();
    ZG6000::StringList listProp;
    for (auto propRef : propArr)
    {
        listProp.push_back(propRef.toString().toStdString());
    }
    ZG6000::MapStringMap mapValues;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::mgetPropertyValues(listDeviceID, listProp, mapValues, e))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    const auto& json = ZGJson::convertToJson(mapValues);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.object());
}

ZGWebModule::Response ZGMPHandle::on_mp_devices_property_values_update(const QString& clientID,
                                                                       const QVariantMap& headers,
                                                                       const QJsonValue& param,
                                                                       const QHttpServerRequest& req)
{
    const auto& obj = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(obj, {"properties"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& devicesPropertiesObj = obj["properties"].toObject();
    ZG6000::MapStringMap mapValues;
    for (auto itDev = devicesPropertiesObj.begin(); itDev != devicesPropertiesObj.end(); ++itDev)
    {
        ZG6000::StringMap values;
        const auto& deviceID = itDev.key();
        const auto& propertiesObj = itDev.value().toObject();
        for (auto itProp = propertiesObj.begin(); itProp != propertiesObj.end(); ++itProp)
        {
            values[itProp.key().toStdString()] = itProp.value().toString().toStdString();
        }
        mapValues[deviceID.toStdString()] = std::move(values);
    }
    bool saveToDb = false;
    if (obj.contains("saveToDb"))
        saveToDb = obj["saveToDb"].toBool();
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::mupdatePropertyValues(mapValues, e, saveToDb))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_device_property_value_get(const QString& clientID,
                                                                  const QVariantMap& headers,
                                                                  const QJsonValue& param,
                                                                  const QHttpServerRequest& req)
{
    const auto& obj = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(obj, {"deviceID", "propertyName"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& deviceID = obj["deviceID"].toString().toStdString();
    const auto& propertyName = obj["propertyName"].toString().toStdString();
    std::string propertyValue;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(deviceID, propertyName, propertyValue, e))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    return ZGWebModule::replyObject(propertyValue.c_str());
}

ZGWebModule::Response ZGMPHandle::on_mp_device_property_value_update(const QString& clientID,
                                                                     const QVariantMap& headers,
                                                                     const QJsonValue& param,
                                                                     const QHttpServerRequest& req)
{
    const auto& obj = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(obj, {"deviceID", "propertyName", "propertyValue"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& deviceID = obj["deviceID"].toString().toStdString();
    const auto& propertyName = obj["propertyName"].toString().toStdString();
    const auto& propertyValue = obj["propertyValue"].toString().toStdString();
    bool saveToDb = false;
    if (obj.contains("saveToDb"))
        saveToDb = obj["saveToDb"].toBool();
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::updatePropertyValue(deviceID, propertyName, propertyValue, e, saveToDb))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGMPHandle::on_mp_dev_block_set(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    return deviceCall(param, req,
        [&](std::shared_ptr<ZG6000::ZGMPDevicePropertyPrx> deviceProxy)-> ZGWebModule::Response
        {
            const auto& deviceObject = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(deviceObject, {"id", "propertyName", "propertyValue"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& deviceID = deviceObject["id"].toString().toStdString();
            const auto& propertyName = deviceObject["propertyName"].toString().toStdString();
            const auto& propertyValue = deviceObject["propertyValue"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!deviceProxy->updatePropertyValue(deviceID, propertyName, propertyValue, true, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_condition_exec_check(const QString& clientID,
                                                             const QVariantMap& headers,
                                                             const QJsonValue& param,
                                                             const QHttpServerRequest& req)
{
    return ruleCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRuleEnginePrx> ruleProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "termID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& deviceID = object["deviceID"].toString().toStdString();
            const auto& termID = object["termID"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!ruleProxy->checkDeviceExecCondition(deviceID, termID, 0, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_condition_confirm_check(const QString& clientID,
                                                                const QVariantMap& headers,
                                                                const QJsonValue& param,
                                                                const QHttpServerRequest& req)
{
    return ruleCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRuleEnginePrx> ruleProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "termID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& deviceID = object["deviceID"].toString().toStdString();
            const auto& termID = object["termID"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!ruleProxy->checkDeviceConfirmCondition(deviceID, termID, 0, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_finger_get(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    return identCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPIdentifyManagerPrx> identProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"userID", "deviceID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& userID = object["userID"].toString().toStdString();
            const auto& deviceID = object["deviceID"].toString().toStdString();
            ZG6000::ErrorInfo e;
            ZG6000::StringList listFingerNo;
            if (!identProxy->getEmployeeFingerprint(deviceID, userID, listFingerNo, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            QJsonArray array;
            for (const auto& fingerNo : listFingerNo)
            {
                array.append(fingerNo.c_str());
            }
            return ZGWebModule::replyObject(array);
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_finger_capture(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    return identCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPIdentifyManagerPrx> identProxy) -> ZGWebModule::Response
        {
            const auto& deviceID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            std::string fingerData;
            if (!identProxy->caputureFingerprint(deviceID, fingerData, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject(fingerData.c_str());
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_finger_add(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    return identCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPIdentifyManagerPrx> identProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"userID", "deviceID", "fingerData"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& userID = object["userID"].toString().toStdString();
            const auto& deviceID = object["deviceID"].toString().toStdString();
            const auto& fingerData = object["fingerData"].toString().toStdString();
            ZG6000::ErrorInfo e;
            int fingerNo;
            if (!identProxy->addEmployeeFingerprint(deviceID, userID, fingerData, fingerNo, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject(std::to_string(fingerNo).c_str());
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_finger_delete(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    return identCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPIdentifyManagerPrx> identProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"userID", "deviceID", "fingerNo"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& userID = object["userID"].toString().toStdString();
            const auto& deviceID = object["deviceID"].toString().toStdString();
            const auto& fingerNo = object["fingerNo"].toVariant().toInt();
            ZG6000::ErrorInfo e;
            if (!identProxy->deleteEmployeeFingerprint(deviceID, userID, fingerNo, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_face_capture(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    return identCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPIdentifyManagerPrx> identProxy) -> ZGWebModule::Response
        {
            const auto& deviceID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            std::string imgPath;
            if (!identProxy->caputureFace(deviceID, imgPath, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_face_set(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    return identCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPIdentifyManagerPrx> identProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"userID", "deviceID", "filePath"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& userID = object["userID"].toString().toStdString();
            const auto& deviceID = object["deviceID"].toString().toStdString();
            const auto& filePath = object["filePath"].toVariant().toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!identProxy->setEmployeeFace(deviceID, userID, filePath, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            auto userProxy = ZGProxyMng::instance()->getProxySPUserManager();
            if (userProxy == nullptr)
                return ZGWebModule::errorObject(QStringLiteral("获取用户管理服务代理对象失败"));
            try
            {
                if (!userProxy->updateUserFace(userID, filePath, e))
                    return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            }
            catch (const Ice::Exception& ie)
            {
                return ZGWebModule::errorObject(ie.what());
            }
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_face_delete(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return identCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPIdentifyManagerPrx> identProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"userID", "deviceID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& userID = object["userID"].toString().toStdString();
            const auto& deviceID = object["deviceID"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!identProxy->deleteEmployeeFace(deviceID, userID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            auto userProxy = ZGProxyMng::instance()->getProxySPUserManager();
            if (userProxy == nullptr)
                return ZGWebModule::errorObject(QStringLiteral("获取用户管理服务代理对象失败"));
            try
            {
                if (!userProxy->deleteUserFace(userID, e))
                    return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            }
            catch (const Ice::Exception& ie)
            {
                return ZGWebModule::errorObject(ie.what());
            }
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_dev_user_sync(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    return identCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPIdentifyManagerPrx> identProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"srcDevice", "dstDevice"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& srcDevice = object["srcDevice"].toString().toStdString();
            const auto& dstDevice = object["dstDevice"].toArray();
            ZG6000::StringList listDstDeviceID;
            for (auto deviceRef : dstDevice)
            {
                listDstDeviceID.push_back(deviceRef.toString().toStdString());
            }
            ZG6000::ErrorInfo e;
            if (!identProxy->syncDeviceUser(srcDevice, listDstDeviceID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_region_list(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return regionCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRegionManagerPrx> regionProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            ZG6000::StringMap queryParam;
            for (auto it = object.begin(); it != object.end(); ++it)
            {
                queryParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            ZG6000::ListStringMap listRegion;
            ZG6000::ErrorInfo e;
            if (!regionProxy->getRegionList(queryParam, listRegion, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            const auto& json = ZGJson::convertToJson(listRegion);
            QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
            return ZGWebModule::replyObject(doc.array());
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_region_user_get(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    return regionCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRegionManagerPrx> regionProxy) -> ZGWebModule::Response
        {
            const auto& regionID = param.toString().toStdString();
            ZG6000::ListStringMap listPeople;
            ZG6000::ErrorInfo e;
            if (!regionProxy->getRegionPeople(regionID, listPeople, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject(ZGWebModule::listStringMapToArray(listPeople));
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_region_yv_get(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    return regionCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRegionManagerPrx> regionProxy) -> ZGWebModule::Response
        {
            const auto& regionID = param.toString().toStdString();
            ZG6000::ListStringMap listYv;
            ZG6000::ErrorInfo e;
            if (!regionProxy->getRegionYv(regionID, listYv, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject(ZGWebModule::listStringMapToArray(listYv));
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_region_access_get(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    return regionCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRegionManagerPrx> regionProxy) -> ZGWebModule::Response
        {
            const auto& regionID = param.toString().toStdString();
            ZG6000::ListStringMap listAccess;
            ZG6000::ErrorInfo e;
            if (!regionProxy->getRegionAccess(regionID, listAccess, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject(ZGWebModule::listStringMapToArray(listAccess));
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_region_user_clear(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    return regionCall(clientID, headers, param, req,
        [&](std::shared_ptr<ZG6000::ZGMPRegionManagerPrx> regionProxy) -> ZGWebModule::Response
        {
            const auto& regionID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!regionProxy->clearRegionPeople(regionID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_tts_group_play(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    return broadcastCall(clientID, headers, param, req,
        [&](const std::shared_ptr<ZG6000::ZGMPBroadcastServerPrx>& broadcastProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"groupID", "text"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& groupID = object["groupID"].toString().toStdString();
            const auto& text = object["text"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!broadcastProxy->playGroupTTS(groupID, text, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_tts_dev_play(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    return broadcastCall(clientID, headers, param, req,
        [&](const std::shared_ptr<ZG6000::ZGMPBroadcastServerPrx>& broadcastProxy) -> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            QString errMsg;
            if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "text"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            const auto& deviceID = object["deviceID"].toString().toStdString();
            const auto& text = object["text"].toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!broadcastProxy->playDeviceTTS(deviceID, text, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_tts_region_alarm_on(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    return regionCall(clientID, headers, param, req,
        [&](const std::shared_ptr<ZG6000::ZGMPRegionManagerPrx>& regionProxy)
        {
            const auto& regionID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            auto onewayProxy = regionProxy->ice_oneway();
            onewayProxy->setupAlarm(regionID);
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_tts_region_alarm_off(const QString& clientID,
                                                             const QVariantMap& headers,
                                                             const QJsonValue& param,
                                                             const QHttpServerRequest& req)
{
    return regionCall(clientID, headers, param, req,
        [&](const std::shared_ptr<ZG6000::ZGMPRegionManagerPrx>& regionProxy)
        {
            const auto& regionID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            auto onewayProxy = regionProxy->ice_oneway();
            onewayProxy->closeAlarm(regionID);
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_realwarn_get(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    auto realwarnProxy = ZGProxyMng::instance()->getProxyMPRealWarn();
    if (realwarnProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取实时告警服务代理对象失败"));
    try
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"appNodeID", "subsystemID"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& appNodeID = object["appNodeID"].toString().toStdString();
        const auto& subsystemID = object["subsystemID"].toString().toStdString();
        ZG6000::ListStringMap listRealWarn;
        ZG6000::ErrorInfo e;
        if (!realwarnProxy->getRealWarnByAppNode(appNodeID, subsystemID, listRealWarn, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(ZGWebModule::listStringMapToArray(listRealWarn));
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::on_mp_dev_relation_get(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "relationType"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& deviceID = object["deviceID"].toString().toStdString();
    const auto& relationType = object["relationType"].toString().toStdString();
    QString sql = QString("SELECT a.dstDeviceID AS deviceID, b.name AS deviceName, b.typeID, a.relationTypeID, "
                      "c.name AS relationTypeName FROM mp_param_device_relation a "
                      "LEFT JOIN mp_param_device b ON a.dstDeviceID = b.id "
                      "LEFT JOIN sp_dict_relation_type c ON a.relationTypeID = c.id "
                      "WHERE a.srcDeviceID = '%1' AND a.relationTypeID = '%2' ORDER BY a.id")
                  .arg(deviceID.c_str()).arg(relationType.c_str());
    ZG6000::ListStringMap listRelation;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRelation))
        return ZGWebModule::errorObject(u8"获取设备关联关系失败");
    return ZGWebModule::replyObject(ZGWebModule::listStringMapToArray(listRelation));
}

ZGWebModule::Response ZGMPHandle::on_mp_appnode_devices_get(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"appNodeID"}, errMsg))
    {
        return ZGWebModule::errorObject(errMsg);
    }
    const auto& appNodeID = object["appNodeID"].toString().toStdString();
    ZG6000::StringList listAppNodeID{appNodeID};
    addInheritedAppnodes(listAppNodeID, appNodeID);
    ZG6000::StringList listDeviceID;
    ZG6000::StringList listCondition;
    // 枚举object对象，除了appNodeID以外的字段作为查询条件
    for (auto it = object.begin(); it != object.end(); ++it)
    {
        if (it.key() == "appNodeID")
            continue;
        QString condition = QString("%1 = '%2'").arg(it.key()).arg(it.value().toString());
        listCondition.push_back(condition.toStdString());
    }
    const auto& appNodes = ZGUtils::join(listAppNodeID, ",", "'", "'");
    std::string conditions;
    if (!listCondition.empty())
        conditions = "AND " + ZGUtils::join(listCondition, " AND ");
    QString sql = QString(
                      "SELECT id FROM mp_param_device WHERE appNodeID IN (%1) %2 AND isEnable = 1 ORDER BY deviceIndex")
                  .arg(appNodes.c_str()).arg(conditions.c_str());
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
        return ZGWebModule::errorObject(QStringLiteral("获取应用节点设备ID失败"));
    const auto& array = ZGWebModule::stringListToArray(listDeviceID);
    return ZGWebModule::replyObject(array);
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_alarm_on(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return videoCall(clientID, headers, param, req,
        [&](const std::shared_ptr<ZG6000::ZGMPVideoHIKPrx>& videoProxy) -> ZGWebModule::Response
        {
            const auto& yvID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!videoProxy->setupAlarm(yvID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

ZGWebModule::Response ZGMPHandle::on_mp_yv_alarm_off(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    return videoCall(clientID, headers, param, req,
        [&](const std::shared_ptr<ZG6000::ZGMPVideoHIKPrx>& videoProxy) -> ZGWebModule::Response
        {
            const auto& yvID = param.toString().toStdString();
            ZG6000::ErrorInfo e;
            if (!videoProxy->closeAlarm(yvID, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            return ZGWebModule::replyObject("");
        });
}

bool ZGMPHandle::initParams()
{
    std::string sql = "SELECT id, name, nameL2 FROM sp_dict_operator";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapOperator))
    {
        ZGLOG_ERROR(u8"获取操作符表信息失败");
        return false;
    }
    sql = "SELECT id, name, nameL2 FROM mp_dict_data_category";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapDataCategory))
    {
        ZGLOG_ERROR(u8"获取数据类别失败");
        return false;
    }
    if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
        sql =
            "SELECT CONCAT(dataCategoryID, '/', propValue) AS id, propName AS name, propNameL2 AS nameL2 FROM mp_param_data_category_property";
    if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
        sql =
            "SELECT dataCategoryID + '/' + propValue AS id, propName AS name, propNameL2 AS nameL2 FROM mp_param_data_category_property";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapDataCategoryProperty))
    {
        ZGLOG_ERROR(u8"获取数据类别属性失败");
        return false;
    }
    sql = "SELECT id, name, nameL2 FROM sp_dict_alarm_level";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapAlarmLevel))
    {
        ZGLOG_ERROR(u8"获取告警等级失败");
        return false;
    }
    sql = "SELECT id, name, nameL2 FROM mp_param_device";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapDevice))
    {
        ZGLOG_ERROR(u8"获取设备信息失败");
        return false;
    }
    sql = "SELECT id, name, nameL2 FROM sp_param_appnode";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapAppNode))
    {
        ZGLOG_ERROR(u8"获取应用节点信息失败");
        return false;
    }
    return true;
}

ZGWebModule::Response ZGMPHandle::sendMessageToQueue(const QJsonValue& param,
                                                     const QHttpServerRequest&/*req*/,
                                                     const std::function<ZGWebModule::Response (ZGRedisClient*,
                                                         const QJsonDocument&)>& func)
{
    ZGRedisClient* client = ZGRuntime::instance()->getRedisClientRTQueue();
    if (client == nullptr)
        return ZGWebModule::errorObject("getRedisClientRTQueue error.");
    QJsonDocument doc(param.toArray());
    return func(client, doc);
}

ZGWebModule::Response ZGMPHandle::deviceCall(const QJsonValue& param,
                                             const QHttpServerRequest& req,
                                             const std::function<ZGWebModule::Response(
                                                 std::shared_ptr<ZG6000::ZGMPDevicePropertyPrx> equipProxy)>& func)
{
    auto deviceProxy = ZGProxyMng::instance()->getProxyMPDeviceProperty();
    if (deviceProxy == nullptr)
        return ZGWebModule::errorObject(u8"获取设备属性服务代理对象失败");
    try
    {
        return func(deviceProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
    catch (const std::exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::taskCall(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req,
                                           const std::function<ZGWebModule::Response(
                                               std::shared_ptr<ZG6000::ZGMPTaskManagerPrx>)>& func)
{
    const auto& taskProxy = ZGProxyMng::instance()->getProxyMPTaskManager();
    if (taskProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取任务管理服务代理对象失败"));
    try
    {
        return func(taskProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::videoCall(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req,
                                            const std::function<ZGWebModule::Response (
                                                std::shared_ptr<ZG6000::ZGMPVideoHIKPrx>)>& func)
{
    const auto& hikProxy = ZGProxyMng::instance()->getProxyMPVideoHIK();
    if (hikProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取海康视频服务代理对象失败"));
    try
    {
        return func(hikProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::ruleCall(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req,
                                           const std::function<ZGWebModule::Response (
                                               std::shared_ptr<ZG6000::ZGMPRuleEnginePrx>)>& func)
{
    const auto& ruleProxy = ZGProxyMng::instance()->getProxyMPRuleEngine();
    if (ruleProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取规则引擎服务代理对象失败"));
    try
    {
        return func(ruleProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::identCall(const QString& clientID,
                                            const QVariantMap& headers,
                                            const QJsonValue& param,
                                            const QHttpServerRequest& req,
                                            const std::function<ZGWebModule::Response(
                                                std::shared_ptr<ZG6000::ZGMPIdentifyManagerPrx>)>& func)
{
    auto identProxy = ZGProxyMng::instance()->getProxyMPIdentifyManager();
    if (identProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取识别管理服务代理对象失败"));
    try
    {
        return func(identProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::regionCall(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req,
                                             const std::function<ZGWebModule::Response (
                                                 std::shared_ptr<ZG6000::ZGMPRegionManagerPrx>)>& func)
{
    auto regionProxy = ZGProxyMng::instance()->getProxyMPRegionManager();
    if (regionProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取区域管理服务代理对象失败"));
    try
    {
        return func(regionProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::broadcastCall(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req,
                                                const std::function<ZGWebModule::Response (
                                                    std::shared_ptr<ZG6000::ZGMPBroadcastServerPrx>)>& func)
{
    auto broadcastProxy = ZGProxyMng::instance()->getProxyMPBroadcastServer();
    if (broadcastProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取广播服务代理对象失败"));
    try
    {
        return func(broadcastProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGMPHandle::getCtrlActions(const std::string& clientID,
                                                 const std::vector<std::pair<std::string, std::string>>& listCtrls,
                                                 const std::string& runMode)
{
    QJsonArray array;
    for (const auto& [datasetName, ctrlID] : listCtrls)
    {
        std::string modelName = datasetName;
        ZGUtils::replaceString(modelName, "dataset", "model");
        QString sql = QString(
                "SELECT %1.id, %1.name, %1.nameL2, %2.isEnable, %1.deviceID, %1.unlockCode, %2.propertyName, %2.isSelectCtrl, %2.overtime, %2.isAuth, "
                "%2.isCheckExecRule, %2.isCheckConfirmRule, %2.isCheckErrorRule FROM "
                "%1 LEFT JOIN %2 ON %1.dataModelID = %2.id WHERE %1.id = '%3' ORDER BY %1.id").arg(datasetName.c_str()).
            arg(modelName.c_str()).arg(ctrlID.c_str());
        ZG6000::StringMap record;
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), record))
            return ZGWebModule::errorObject(QStringLiteral("获取控制'%1'属性失败").arg(ctrlID.c_str()));
        try
        {
            ZG6000::ErrorInfo e;
            QJsonObject object;
            const auto& isEnable = ZGUtils::get(record, "isEnable");
            if (isEnable != "1")
                continue;
            object["id"] = ZGUtils::get(record, "id").c_str();
            object["tableName"] = datasetName.c_str();
            object["name"] = ZGUtils::get(record, "name").c_str();
            object["propetyName"] = ZGUtils::get(record, "propertyName").c_str();
            object["isEnable"] = isEnable.c_str();
            object["unlockCode"] = ZGUtils::get(record, "unlockCode").c_str();
            const auto& isCheckExecRule = ZGUtils::get(record, "isCheckExecRule");
            object["isCheckExecRule"] = isCheckExecRule.c_str();
            object["isCheckConfirmRule"] = ZGUtils::get(record, "isCheckConfirmRule").c_str();
            object["isCheckErrorRule"] = ZGUtils::get(record, "isCheckErrorRule").c_str();
            object["isSelectCtrl"] = ZGUtils::get(record, "isSelectCtrl").c_str();
            object["overtime"] = ZGUtils::get(record, "overtime").c_str();
            object["isAuth"] = ZGUtils::get(record, "isAuth").c_str();
            bool isAllowCtrl = true;
            // std::string rtAppNodeID;
            // if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtAppNodeID", rtAppNodeID))
            //     return ZGWebModule::errorObject(QStringLiteral("获取客户端'%1'关联应用节点失败").arg(clientID.c_str()));
            // ZG6000::StringMap appNode;
            // if (!ZGProxyCommon::getDataByFields("sp_param_appnode", rtAppNodeID, {"isEnableCtrl", "rtGroundLock", "rtYkBlock", "rtRepairBlock", "rtForbid", "rtAuthPosID"},
            //     appNode))
            //     return ZGWebModule::errorObject(QStringLiteral("获取应用节点'%1'参数失败").arg(rtAppNodeID.c_str()));
            // if (ZGUtils::get(appNode, "isEnableCtrl") != "1")
            //     isAllowCtrl = false;
            // else
            // {
            //     const auto& deviceID = ZGUtils::get(record, "deviceID");
            //     if (!deviceID.empty())
            //     {
            //         const auto& deviceProxy = ZGProxyMng::instance()->getProxyMPDeviceProperty();
            //         if (deviceProxy == nullptr)
            //             return ZGWebModule::errorObject(u8"获取一次设备服务代理对象失败");
            //         ZG6000::ErrorInfo e;
            //         ZG6000::StringMap conditions;
            //         if (!deviceProxy->isAllowCtrl(deviceID, isAllowCtrl, conditions, e))
            //             return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            //     }
            //     else
            //     {
            //         if ((ZGUtils::get(appNode, "rtGroundLock") == "2")
            //             || (ZGUtils::get(appNode, "rtYkBlock") == "2") || (ZGUtils::get(appNode, "rtRepairBlock") == "2")
            //             || (ZGUtils::get(appNode, "rtForbid") == "2") || (ZGUtils::get(appNode, "rtAuthPosID") != "ZG_AP_LOCAL"))
            //             isAllowCtrl = false;
            //     }
            // }
            object["isAllowCtrl"] = isAllowCtrl;
            sql = QString("SELECT a.propName, a.propNameL2, a.propValue FROM mp_param_data_category_property a "
                "LEFT JOIN %2 ON a.dataCategoryID = %2.dataCategoryID "
                "LEFT JOIN %1 ON %2.id = %1.dataModelID "
                "WHERE %1.id = '%3' ORDER BY a.id").arg(datasetName.c_str()).arg(modelName.c_str()).arg(ctrlID.c_str());
            ZG6000::ListStringMap listRecord;
            if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
                return ZGWebModule::errorObject(QStringLiteral("获取控制'%1'数据类别属性失败").arg(ctrlID.c_str()));
            QJsonArray arrayAct;
            auto ruleProxy = ZGProxyMng::instance()->getProxyMPRuleEngine();
            if (ruleProxy == nullptr)
                return ZGWebModule::errorObject(QStringLiteral("获取命令规则服务代理对象失败"));
            for (const auto& elem : listRecord)
            {
                QJsonObject actObj;
                const auto& propName = ZGUtils::get(elem, "propName");
                const auto& propNameL2 = ZGUtils::get(elem, "propNameL2");
                const auto& propValue = ZGUtils::get(elem, "propValue");
                actObj["name"] = propName.c_str();
                actObj["nameL2"] = propNameL2.c_str();
                if (isCheckExecRule == "1")
                {
                    bool result = ruleProxy->checkCommandExecConditionWithRunMode(ctrlID, propValue, runMode, e);
                    actObj["allow"] = result;
                }
                else
                {
                    actObj["allow"] = true;
                }
                actObj["value"] = propValue.c_str();
                arrayAct.append(actObj);
            }
            object["item"] = arrayAct;
            array.append(object);
        }
        catch (const Ice::Exception& e)
        {
            return ZGWebModule::errorObject(e.what());
        }
        catch (const std::exception& e)
        {
            return ZGWebModule::errorObject(e.what());
        }
    }
    return ZGWebModule::replyObject(array);
}

ZGWebModule::Response ZGMPHandle::convertRuleItem(const ZG6000::ListStringMap& listRecord)
{
    try
    {
        QJsonArray array;
        for (const auto& record : listRecord)
        {
            QJsonObject ruleObj;
            const auto& id = ZGUtils::get(record, "id");
            const auto& deviceRuleID = ZGUtils::get(record, "deviceRuleID");
            ruleObj["deviceRuleID"] = deviceRuleID.c_str();
            const auto& dataID = ZGUtils::get(record, "dataID");
            ruleObj["dataID"] = dataID.c_str();
            const auto& operatorID = ZGUtils::get(record, "operatorID");
            ruleObj["operatorID"] = operatorID.c_str();
            const auto& expectValue = ZGUtils::get(record, "value");
            ruleObj["expectValue"] = expectValue.c_str();
            const auto& actualValue = ZGUtils::get(record, "rtNewValue");
            ruleObj["actualValue"] = actualValue.c_str();
            const auto& result = ZGUtils::get(record, "result");
            ruleObj["id"] = id.c_str();
            auto pair = m_mapOperator.find(operatorID);
            if (pair != m_mapOperator.end())
            {
                ruleObj["operator"] = pair->second["name"].c_str();
                ruleObj["operatorL2"] = pair->second["nameL2"].c_str();
            }
            else
            {
                ruleObj["operator"] = u8"未知";
                // TODO: 根据语言环境获取
                ruleObj["operatorL2"] = u8"未知";
            }
            const auto& name = ZGUtils::get(record, "name");
            ruleObj["dataName"] = name.c_str();
            const auto& nameL2 = ZGUtils::get(record, "nameL2");
            ruleObj["dataNameL2"] = nameL2.c_str();
            const auto& dataCategoryID = ZGUtils::get(record, "dataCategoryID");
            std::string expectProp = dataCategoryID + "/" + expectValue;
            std::string actualProp = dataCategoryID + "/" + actualValue;
            pair = m_mapDataCategoryProperty.find(expectProp);
            if (pair != m_mapDataCategoryProperty.end())
            {
                ruleObj["expectValueDesc"] = pair->second["name"].c_str();
                ruleObj["expectValueDescL2"] = pair->second["nameL2"].c_str();
            }
            else
            {
                ruleObj["expectValueDesc"] = expectValue.c_str();
                ruleObj["expectValueDescL2"] = expectValue.c_str();
            }
            pair = m_mapDataCategoryProperty.find(actualProp);
            if (pair != m_mapDataCategoryProperty.end())
            {
                ruleObj["actualValueDesc"] = pair->second["name"].c_str();
                ruleObj["actualValueDescL2"] = pair->second["nameL2"].c_str();
            }
            else
            {
                ruleObj["actualValueDesc"] = actualValue.c_str();
                ruleObj["actualValueDescL2"] = actualValue.c_str();
            }
            auto pairInterlock = record.find("interlockInfo");
            if (pairInterlock != record.end())
            {
                const auto& interlockInfo = pairInterlock->second;
                ruleObj["interlockInfo"] = interlockInfo.c_str();
            }
            const auto& appNodeID = ZGUtils::get(record, "appNodeID");
            ruleObj["appNodeID"] = appNodeID.c_str();
            pair = m_mapAppNode.find(appNodeID);
            if (pair != m_mapAppNode.end())
            {
                ruleObj["appNodeName"] = pair->second["name"].c_str();
                ruleObj["appNodeNameL2"] = pair->second["nameL2"].c_str();
            }
            else
            {
                ruleObj["appNodeName"] = "";
                ruleObj["appNodeNameL2"] = "";
            }
            ruleObj["result"] = result.c_str();
            array.append(ruleObj);
        }
        return ZGWebModule::replyObject(array);
    }
    catch (const std::exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

void ZGMPHandle::addInheritedAppnodes(ZG6000::StringList& listAppNodeID,
                                      const std::string& appNodeID)
{
    QString sql = QString(
            "SELECT appNodeID FROM sp_param_appnode_layer WHERE parentAppNodeID = '%1' ORDER BY itemIndex")
        .arg(appNodeID.c_str());
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
    {
        ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'子节点失败"));
        return;
    }
    for (const auto& id : listID)
    {
        listAppNodeID.push_back(id);
        addInheritedAppnodes(listAppNodeID, id);
    }
}

bool ZGMPHandle::checkYkAuthorization(const QString& ykID,
                                      bool& isAuth,
                                      QString& errMsg)
{
    isAuth = false;
    QString sql = QString("SELECT b.isAuth FROM mp_param_dataset_yk a "
        "LEFT JOIN mp_param_model_yk b ON a.dataModelID = b.id "
        "WHERE a.id = '%1' ORDER BY a.id").arg(ykID);
    ZG6000::StringList listAuth;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listAuth))
    {
        errMsg = QStringLiteral("查询遥控授权失败");
        return false;
    }
    if (listAuth.empty())
    {
        errMsg = QStringLiteral("遥控ID'%1'不存在").arg(ykID);
        return false;
    }
    isAuth = listAuth.front() == "1";
    return true;
}

bool ZGMPHandle::checkYsAuthorization(const QString& ysID,
                                      bool& isAuth,
                                      QString& errMsg)
{
    isAuth = false;
    QString sql = QString("SELECT b.isAuth FROM mp_param_dataset_ys a "
        "LEFT JOIN mp_param_model_ys b ON a.dataModelID = b.id "
        "WHERE a.id = '%1' ORDER BY b.isAuth").arg(ysID);
    ZG6000::StringList listAuth;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listAuth))
    {
        errMsg = QStringLiteral("查询遥设授权失败");
        return false;
    }
    if (listAuth.empty())
    {
        errMsg = QStringLiteral("遥设ID'%1'不存在").arg(ysID);
        return false;
    }
    isAuth = listAuth.front() == "1";
    return true;
}

#ifndef ZGMPWEBMODULE_H
#define ZGMPWEBMODULE_H

#include "ZGWebModule.h"

class ZGMPHandle;
class ZGMPWebModule : public ZGWebModule
{
    Q_OBJECT
    Q_PLUGIN_METADATA(IID ZGWebModule_iid FILE "ZGMPWebModule.json")
    Q_INTERFACES(ZGWebModule)

public:
    explicit ZGMPWebModule(QObject *parent = nullptr);

    // ZGWebModule interface
public:
    bool initialize() override;
    QString prefix() override;

private:
    ZGMPHandle* m_pHandle;

    // ZGWebModule interface
public:

};

#endif // ZGMPWEBMODULE_H

#include "ZGMPWebModule.h"
#include "ZGMPHandle.h"

ZGMPWebModule::ZGMPWebModule(QObject *parent)
    : ZGWebModule(parent)
{
    m_pHandle = new ZGMPHandle(this);
    registerHandle("mp/yk", m_pHandle, &ZGMPHandle::on_mp_yk);
    registerHandle("mp/dev/yk", m_pHandle, &ZGMPHandle::on_mp_dev_yk);
    registerHandle("mp/dev/batch/yk", m_pHandle, &ZGMPHandle::on_mp_dev_batch_yk);
    registerHandle("mp/dev/update", m_pHandle, &ZGMPHandle::on_mp_dev_update);
    registerHandle("mp/ys", m_pHandle, &ZGMPHandle::on_mp_ys);
    registerHandle("mp/yt", m_pHandle, &ZGMPHandle::on_mp_yt);
    registerHandle("mp/yc/get", m_pHandle, &ZGMPHandle::on_mp_yc_get);
    registerHandle("mp/yx/get", m_pHandle, &ZGMPHandle::on_mp_yx_get);
    registerHandle("mp/ym/get", m_pHandle, &ZGMPHandle::on_mp_ym_get);
    registerHandle("mp/appnode/yv/get", m_pHandle, &ZGMPHandle::on_mp_appnode_yv_get);
    registerHandle("mp/appnode/yv/preset/get", m_pHandle, &ZGMPHandle::on_mp_appnode_yv_preset_get);
    registerHandle("mp/yx/desc/get", m_pHandle, &ZGMPHandle::on_mp_yx_desc_get);
    registerHandle("mp/yv/preset/get", m_pHandle, &ZGMPHandle::on_mp_yv_preset_get);
    registerHandle("mp/yv/preset/set", m_pHandle, &ZGMPHandle::on_mp_yv_preset_set);
    registerHandle("mp/yv/preset/load", m_pHandle, &ZGMPHandle::on_mp_yv_preset_load);
    registerHandle("mp/yv/ctrl/up", m_pHandle, &ZGMPHandle::on_mp_yv_up);
    registerHandle("mp/yv/ctrl/down", m_pHandle, &ZGMPHandle::on_mp_yv_down);
    registerHandle("mp/yv/ctrl/left", m_pHandle, &ZGMPHandle::on_mp_yv_left);
    registerHandle("mp/yv/ctrl/right", m_pHandle, &ZGMPHandle::on_mp_yv_right);
    registerHandle("mp/yv/ctrl/zoomin", m_pHandle, &ZGMPHandle::on_mp_yv_zoomin);
    registerHandle("mp/yv/ctrl/zoomout", m_pHandle, &ZGMPHandle::on_mp_yv_zoomout);
    registerHandle("mp/yv/ctrl/upleft", m_pHandle, &ZGMPHandle::on_mp_yv_up_left);
    registerHandle("mp/yv/ctrl/upright", m_pHandle, &ZGMPHandle::on_mp_yv_up_right);
    registerHandle("mp/yv/ctrl/downleft", m_pHandle, &ZGMPHandle::on_mp_yv_down_left);
    registerHandle("mp/yv/ctrl/downright", m_pHandle, &ZGMPHandle::on_mp_yv_down_right);
    registerHandle("mp/yv/ctrl/scan", m_pHandle, &ZGMPHandle::on_mp_yv_scan);
    registerHandle("mp/yv/ctrl/near", m_pHandle, &ZGMPHandle::on_mp_yv_near);
    registerHandle("mp/yv/ctrl/far", m_pHandle, &ZGMPHandle::on_mp_yv_far);
    registerHandle("mp/yv/ctrl/close", m_pHandle, &ZGMPHandle::on_mp_yv_close);
    registerHandle("mp/yv/ctrl/open", m_pHandle, &ZGMPHandle::on_mp_yv_open);
    registerHandle("mp/yv/alarm/on", m_pHandle, &ZGMPHandle::on_mp_yv_alarm_on);
    registerHandle("mp/yv/alarm/off", m_pHandle, &ZGMPHandle::on_mp_yv_alarm_off);
    registerHandle("mp/text/get", m_pHandle, &ZGMPHandle::on_mp_text_get);
    registerHandle("mp/dev/get", m_pHandle, &ZGMPHandle::on_mp_dev_get);
    registerHandle("mp/dev/act/get", m_pHandle, &ZGMPHandle::on_mp_dev_act_get);
    registerHandle("mp/ctrl/act/get", m_pHandle, &ZGMPHandle::on_mp_ctrl_act_get);
    registerHandle("mp/ctrl/rule/get", m_pHandle, &ZGMPHandle::on_mp_ctrl_rule_get);
    registerHandle("mp/device/rule/get", m_pHandle, &ZGMPHandle::on_mp_device_rule_get);
    registerHandle("mp/ctrl/rule/id/get", m_pHandle, &ZGMPHandle::on_mp_ctrl_rule_id_get);
    registerHandle("mp/ctrl/rule/add", m_pHandle, &ZGMPHandle::on_mp_ctrl_rule_add);
    registerHandle("mp/ctrl/rule/delete", m_pHandle, &ZGMPHandle::on_mp_ctrl_rule_delete);
    registerHandle("mp/ctrl/rule/item/add", m_pHandle, &ZGMPHandle::on_mp_ctrl_rule_item_add);
    registerHandle("mp/ctrl/rule/item/update", m_pHandle, &ZGMPHandle::on_mp_ctrl_rule_item_update);
    registerHandle("mp/ctrl/rule/item/delete", m_pHandle, &ZGMPHandle::on_mp_ctrl_rule_item_delete);
    registerHandle("mp/ctrl/unlock/set", m_pHandle, &ZGMPHandle::on_mp_ctrl_unlock_set);
    registerHandle("mp/dataid/get", m_pHandle, &ZGMPHandle::on_mp_dataid_get);
    registerHandle("mp/dataids/get", m_pHandle, &ZGMPHandle::on_mp_dataids_get);
    registerHandle("mp/category/property/get", m_pHandle, &ZGMPHandle::on_mp_category_property_get);
    registerHandle("mp/topics/get", m_pHandle, &ZGMPHandle::on_mp_topics_get);
    registerHandle("mp/event/topics/get", m_pHandle, &ZGMPHandle::on_mp_event_topics_get);
    registerHandle("mp/devices/properties/all/get", m_pHandle, &ZGMPHandle::on_mp_devices_properties_all_get);
    registerHandle("mp/device/properties/group/get", m_pHandle, &ZGMPHandle::on_mp_device_properties_group_get);
    registerHandle("mp/devices/properties/get", m_pHandle, &ZGMPHandle::on_mp_devices_properties_get);
    registerHandle("mp/devices/properties/update", m_pHandle, &ZGMPHandle::on_mp_devices_properties_update);
    registerHandle("mp/device/property/get", m_pHandle, &ZGMPHandle::on_mp_device_property_get);
    registerHandle("mp/device/property/update", m_pHandle, &ZGMPHandle::on_mp_device_property_update);
    registerHandle("mp/devices/properties/value/get", m_pHandle, &ZGMPHandle::on_mp_devices_property_values_get);
    registerHandle("mp/devices/properties/value/update", m_pHandle, &ZGMPHandle::on_mp_devices_property_values_update);
    registerHandle("mp/device/property/value/get", m_pHandle, &ZGMPHandle::on_mp_device_property_value_get);
    registerHandle("mp/device/property/value/update", m_pHandle, &ZGMPHandle::on_mp_device_property_value_update);
    registerHandle("mp/device/block/set", m_pHandle, &ZGMPHandle::on_mp_dev_block_set);
    registerHandle("mp/data/simulate/set", m_pHandle, &ZGMPHandle::on_mp_data_simulate_set);
    registerHandle("mp/data/position/set", m_pHandle, &ZGMPHandle::on_mp_data_position_set);
    registerHandle("mp/device/position/set", m_pHandle, &ZGMPHandle::on_mp_device_position_set);
    registerHandle("mp/dataset/get", m_pHandle, &ZGMPHandle::on_mp_dataset_get);
    registerHandle("mp/store/yc/get", m_pHandle, &ZGMPHandle::on_mp_store_yc_get);
    registerHandle("mp/store/yx/get", m_pHandle, &ZGMPHandle::on_mp_store_yx_get);
    registerHandle("mp/store/text/get", m_pHandle, &ZGMPHandle::on_mp_store_text_get);
    registerHandle("mp/statistic/data/get", m_pHandle, &ZGMPHandle::on_mp_statistic_data_get);
    registerHandle("mp/task/list/get", m_pHandle, &ZGMPHandle::on_mp_task_list_get);
    registerHandle("mp/task/get", m_pHandle, &ZGMPHandle::on_mp_task_get);
    registerHandle("mp/task/item/get", m_pHandle, &ZGMPHandle::on_mp_task_item_get);
    registerHandle("mp/task/start", m_pHandle, &ZGMPHandle::on_mp_task_start);
    registerHandle("mp/task/stop", m_pHandle, &ZGMPHandle::on_mp_task_stop);
    registerHandle("mp/task/pause", m_pHandle, &ZGMPHandle::on_mp_task_pause);
    registerHandle("mp/task/resume", m_pHandle, &ZGMPHandle::on_mp_task_resume);
    registerHandle("mp/task/reset", m_pHandle, &ZGMPHandle::on_mp_task_reset);
    registerHandle("mp/task/exec/rule/get", m_pHandle, &ZGMPHandle::on_mp_task_exec_rule_get);
    registerHandle("mp/task/conf/rule/get", m_pHandle, &ZGMPHandle::on_mp_task_conf_rule_get);
    registerHandle("mp/task/error/rule/get", m_pHandle, &ZGMPHandle::on_mp_task_error_rule_get);
    registerHandle("mp/condition/exec/check", m_pHandle, &ZGMPHandle::on_mp_condition_exec_check);
    registerHandle("mp/condition/confirm/check", m_pHandle, &ZGMPHandle::on_mp_condition_confirm_check);
    registerHandle("mp/finger/get", m_pHandle, &ZGMPHandle::on_mp_finger_get);
    registerHandle("mp/finger/capture", m_pHandle, &ZGMPHandle::on_mp_finger_capture);
    registerHandle("mp/finger/add", m_pHandle, &ZGMPHandle::on_mp_finger_add);
    registerHandle("mp/finger/delete", m_pHandle, &ZGMPHandle::on_mp_finger_delete);
    registerHandle("mp/face/capture", m_pHandle, &ZGMPHandle::on_mp_face_capture);
    registerHandle("mp/face/set", m_pHandle, &ZGMPHandle::on_mp_face_set);
    registerHandle("mp/face/delete", m_pHandle, &ZGMPHandle::on_mp_face_delete);
    registerHandle("mp/dev/user/sync", m_pHandle, &ZGMPHandle::on_mp_dev_user_sync);
    registerHandle("mp/region/list", m_pHandle, &ZGMPHandle::on_mp_region_list);
    registerHandle("mp/region/access/get", m_pHandle, &ZGMPHandle::on_mp_region_access_get);
    registerHandle("mp/region/user/get", m_pHandle, &ZGMPHandle::on_mp_region_user_get);
    registerHandle("mp/region/yv/get", m_pHandle, &ZGMPHandle::on_mp_region_yv_get);
    registerHandle("mp/region/user/clear", m_pHandle, &ZGMPHandle::on_mp_region_user_clear);
    registerHandle("mp/region/alarm/on", m_pHandle, &ZGMPHandle::on_mp_tts_region_alarm_on);
    registerHandle("mp/region/alarm/off", m_pHandle, &ZGMPHandle::on_mp_tts_region_alarm_off);
    registerHandle("mp/tts/group/play", m_pHandle, &ZGMPHandle::on_mp_tts_group_play);
    registerHandle("mp/tts/dev/play", m_pHandle, &ZGMPHandle::on_mp_tts_group_play);
    registerHandle("mp/real/warn/get", m_pHandle, &ZGMPHandle::on_mp_realwarn_get);
    registerHandle("mp/dev/relation/get", m_pHandle, &ZGMPHandle::on_mp_dev_relation_get);
    registerHandle("mp/appnode/devices/get", m_pHandle, &ZGMPHandle::on_mp_appnode_devices_get);
}

bool ZGMPWebModule::initialize()
{
    if (!m_pHandle->initialize())
        return false;
    return true;
}

QString ZGMPWebModule::prefix()
{
    return "mp";
}

#ifndef ZG6000_ZGMPDATASETPUBLISHMNG_H
#define ZG6000_ZGMPDATASETPUBLISHMNG_H

#include <QObject>
#include <QTimer>
#include <QReadWriteLock>
#include <ZGServerCommon.h>

class ZGMqttClient;
class ZGRedisClient;
namespace ZG6000 {
class ZGMPDatasetPublishMng : public QObject
{
    Q_OBJECT
public:
    explicit ZGMPDatasetPublishMng(QObject *parent = nullptr);
    static ZGMPDatasetPublishMng* instance();
    void init();

public:
    bool checkState();
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord);

private slots:
    void onPeriodTask();

private:
    void initEvents();
    void initServerInstConfig();
    void initExtendFunc();
    bool initServerInstInfo();
    bool initMqttClient();
    bool initRedisClient();
    bool initModelParam();
    bool initDatasetParam();
    bool initDataParam();
    bool initTableDataParam(const std::string& tableName);
    bool initDataCategoryProperty();
    bool initDatasetTickcount();
    bool initModelDataParam(const std::string& tableName);
    std::string getTypeFromTableName(const std::string& tableName);
    StringList getRecordListID(const ListRecord& listRecord);
    void publishToRedis(const std::string& dataTableName, const std::string& datasetID);
    void publishToMqtt(const std::string& dataTableName, const std::string& datasetID);
    void publishDatasetData(const std::string& datasetID);
    ListRecord convertToListRecord(ZG6000::ListStringMap listData);
    std::string generatePublishMessage(const std::string& dataTableName, const ZG6000::StringList& listID);

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    int m_initInterval{ 10 };
    QTimer m_timer;
    QReadWriteLock m_lock;
    ZGMqttClient* m_pMqttClient{nullptr};
    ZGRedisClient* m_pRedisClient{nullptr};
    MapStringMap m_mapModelParam;
    MapStringMap m_mapDatasetParam;
    MapStringMap m_mapDataCategoryProperty;
    std::unordered_map<std::string, std::function<void(const std::string&, MapField&)>> m_mapExtend;
    std::unordered_map<std::string, int> m_mapDatasetTickout;    
    StringList m_listDataTable {
        "mp_param_dataset_bt", "mp_param_dataset_yc", "mp_param_dataset_yx",
        "mp_param_dataset_text", "mp_param_dataset_ym", "mp_param_dataset_param", "mp_param_dataset_yk",
        "mp_param_dataset_ys", "mp_param_dataset_yt", "mp_param_dataset_yv"
    }; // 数据集数据表
    StringList m_listUpdateFields{"id", "name", "rtNewValue", "rtUpdateTime"};
    std::map<std::string, StringList> m_mapDataFields{
        {"mp_param_dataset_bt", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
        {"mp_param_dataset_yc", {"id", "name",  "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
        {"mp_param_dataset_yx", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
        {"mp_param_dataset_text", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
        {"mp_param_dataset_ym", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
        {"mp_param_dataset_param", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
        {"mp_param_dataset_yk", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
        {"mp_param_dataset_ys", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
        {"mp_param_dataset_yt", {"id", "name", "nameL2", "rtNewValue", "rtSimulateFlag", "rtSimulateValue", "rtUpdateTime"}},
        {"mp_param_dataset_yv", {"id", "name", "nameL2", "rtspAddr", "rtUpdateTime"}}
    };
    std::map<std::string, StringList> m_mapModelFields{
        {"mp_param_model_bt", {"id", "isEnable", "isPublishMQ", "dataCategoryID"}},
        {"mp_param_model_yc", {"id", "isEnable", "isPublishMQ", "dataUnitID"}},
        {"mp_param_model_yx", {"id", "isEnable", "isPublishMQ", "dataCategoryID"}},
        {"mp_param_model_text", {"id", "isEnable", "isPublishMQ"}},
        {"mp_param_model_ym", {"id", "isEnable", "isPublishMQ", "dataUnitID"}},
        {"mp_param_model_param", {"id", "isEnable", "isPublishMQ", "dataUnitID"}},
        {"mp_param_model_yk", {"id", "isEnable", "isPublishMQ", "dataCategoryID"}},
        {"mp_param_model_ys", {"id", "isEnable", "isPublishMQ", "dataCategoryID"}},
        {"mp_param_model_yt", {"id", "isEnable", "isPublishMQ", "dataCategoryID"}},
        {"mp_param_model_yv", {"id", "isEnable", "isPublishMQ", "dataCategoryID"}}
    };
    MapStringMap m_mapDataParam; // 数据参数
    std::map<std::string, StringList> m_mapDatasetData;
};

inline static ZGMPDatasetPublishMng* g_pInstance = nullptr;

} // namespace ZG6000

#endif // ZG6000_ZGMPDATASETPUBLISHMNG_H

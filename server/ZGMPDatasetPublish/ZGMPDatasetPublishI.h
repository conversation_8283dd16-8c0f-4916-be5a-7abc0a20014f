#ifndef ZG6000_ZGMPDATASETPUBLISHI_H
#define ZG6000_ZGMPDATASETPUBLISHI_H

#include <ZGMPDatasetPublish.h>

namespace ZG6000 {

class ZGMPDatasetPublishI : public ZG6000::ZGMPDatasetPublish
{
public:
    ZGMPDatasetPublishI();

public:
    bool checkState(const Ice::Current &current) override;
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGMPDATASETPUBLISHI_H

#include "ZGMPDatasetPublishI.h"
#include "ZGMPDatasetPublishMng.h"

namespace ZG6000 {

ZGMPDatasetPublishI::ZGMPDatasetPublishI()
{
    ZGMPDatasetPublishMng::instance()->init();
}

bool ZGMPDatasetPublishI::checkState(const Ice::Current &current)
{
    return ZGMPDatasetPublishMng::instance()->checkState();
}

void ZGMPDatasetPublishI::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current)
{
    ZGMPDatasetPublishMng::instance()->dispatchData(std::move(tableName), std::move(oper), std::move(reason), std::move(time), std::move(listRecord));
}

} // namespace ZG6000

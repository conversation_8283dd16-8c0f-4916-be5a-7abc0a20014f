#include "ZGMPDatasetPublishMng.h"

#include <QtConcurrent>

#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "redis/ZGRedisClient.h"

#include <QThread>

namespace ZG6000
{
	ZGMPDatasetPublishMng::ZGMPDatasetPublishMng(QObject* parent)
		: QObject{parent}
	{
	}

	ZGMPDatasetPublishMng* ZGMPDatasetPublishMng::instance()
	{
		if (g_pInstance == nullptr)
			g_pInstance = new ZGMPDatasetPublishMng;
		return g_pInstance;
	}

	void ZGMPDatasetPublishMng::init()
	{
		initEvents();
		initServerInstConfig();
		initExtendFunc();
		while (!initServerInstInfo())
		{
			ZGLOG_ERROR("initServerInstInfo error.");
			QThread::msleep(m_initInterval * 1000);
		}
		QThread::sleep(QRandomGenerator::global()->bounded(1, 5));
		while (!initRedisClient())
		{
			ZGLOG_ERROR("initRedisClient error.");
			QThread::msleep(m_initInterval * 1000);
		}
		while (!initMqttClient())
		{
			ZGLOG_ERROR("initMqttClient error.");
			QThread::msleep(m_initInterval * 1000);
		}
		while (!initDatasetParam())
		{
			ZGLOG_ERROR("initDatasetParam error.");
			QThread::msleep(m_initInterval * 1000);
		}
		while (!initModelParam())
		{
			ZGLOG_ERROR("initModelParam error.");
			QThread::msleep(m_initInterval * 1000);
		}
		ZGLOG_TRACE("initDataParam");
        while (!initDataParam())
        {
            m_mapDatasetData.clear();
            m_mapDataParam.clear();
			ZGLOG_ERROR("initDataParam error.");
			QThread::msleep(m_initInterval * 1000);
        }
		ZGLOG_TRACE("initDataCategoryProperty");
		while (!initDataCategoryProperty())
		{
			ZGLOG_ERROR("initModelParam error.");
			QThread::msleep(m_initInterval * 1000);
		}
		ZGLOG_TRACE("initDatasetTickcount");
		while (!initDatasetTickcount())
		{
			ZGLOG_ERROR("initDatasetTickcount error.");
			QThread::msleep(m_initInterval * 1000);
		}
		m_initialized = true;
		ZGLOG_INFO(" ZGMPDatasetPublish init finished.");
		m_timer.start(1000);
	}

	bool ZGMPDatasetPublishMng::checkState()
	{
		return m_initialized;
	}

	void ZGMPDatasetPublishMng::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord)
	{
		if (!m_initialized)
			return;
		if (oper != "update")
			return;
		const auto& typeName = getTypeFromTableName(tableName);
		const auto& listID = getRecordListID(listRecord);
		std::map<std::string, ListRecord> mapRedisRecord, mapMqttRecord;
		auto pairExtend = m_mapExtend.find(tableName);
        try
        {
            for (size_t i = 0; i < listID.size(); ++i)
            {
                auto pair = m_mapDataParam.find(listID[i]);
                if (pair == m_mapDataParam.end())
                    continue;
                const auto& dataParam = pair->second;
                const auto& datasetID = ZGUtils::get(dataParam, "datasetID");
                auto pairDataset = m_mapDatasetParam.find(datasetID);
                if (pairDataset == m_mapDatasetParam.end())
                    continue;
                const auto & datasetParam = pairDataset->second;
                const auto & datasetEnable = ZGUtils::get(datasetParam, "isEnable", "");
                if (datasetEnable != "1")
                    continue;
                const auto & dataModelID = ZGUtils::get(dataParam, "dataModelID");
                auto pairModel = m_mapModelParam.find(dataModelID);
                if (pairModel == m_mapModelParam.end())
                    continue;
                const auto & modelParam = pairModel->second;
                const auto & modelEnable = ZGUtils::get(modelParam, "isEnable", "");
                if (modelEnable != "1")
                    continue;
                const auto& isDatasetPublishMQ = ZGUtils::get(datasetParam, "isPublishMQ", "");
                const auto & isPublishMQ = ZGUtils::get(modelParam, "isPublishMQ", "");
                if ((isDatasetPublishMQ) == "1" && (isPublishMQ == "1"))
                {
                    if (pairExtend != m_mapExtend.end())
                        pairExtend->second(dataModelID, listRecord[i]);
                    mapMqttRecord[datasetID].push_back(listRecord[i]);
                }
                mapRedisRecord[datasetID].push_back(std::move(listRecord[i]));
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
            return;
        }
		for (const auto& [datasetID, listDatasetRecord] : mapRedisRecord)
		{
			const auto& json = ZGJson::convertFromListRecordToJson(tableName, oper, reason, listDatasetRecord);
			long long subscribeNum;
			std::string errMsg;
			const auto& topic = datasetID + "/" + typeName;
			if (!m_pRedisClient->publish(topic, json, subscribeNum, errMsg))
				ZGLOG_ERROR(errMsg.c_str());
		}
		for (const auto& [datasetID, listDatasetRecord] : mapMqttRecord)
		{
			const auto& json = ZGJson::convertFromListRecordToJson(tableName, oper, reason, listDatasetRecord);
			QString topic = QString("%1/%2").arg(datasetID.c_str()).arg(typeName.c_str());
			m_pMqttClient->sendPublish(topic, json.c_str());
		}
	}

	void ZGMPDatasetPublishMng::onPeriodTask()
	{
		if (!ZGRuntime::instance()->isMaster())
			return;
		for (auto& [datasetID, datasetParam] : m_mapDatasetParam)
		{
			if (datasetParam["isEnable"] != "1")
				continue;
            if (datasetParam["isPublishMQ"] != "1")
                continue;
			int publishInterval = std::atoi(datasetParam["publishInterval"].c_str());
			if (publishInterval <= 0)
				continue;
			if (m_mapDatasetTickout.find(datasetID) == m_mapDatasetTickout.end())
				continue;
			if (m_mapDatasetTickout[datasetID] < (publishInterval - 1))
			{
				m_mapDatasetTickout[datasetID]++;
				continue;
			}
			auto future = QtConcurrent::run([&](std::string id)
			{
				publishDatasetData(id);
			}, datasetID);
			m_mapDatasetTickout[datasetID] = 0;
		}
	}

	void ZGMPDatasetPublishMng::initEvents()
	{
        m_timer.setTimerType(Qt::PreciseTimer);
		connect(&m_timer, &QTimer::timeout, this, &ZGMPDatasetPublishMng::onPeriodTask);
	}

	void ZGMPDatasetPublishMng::initServerInstConfig()
	{
		const auto& serverGroup = ZGRuntime::instance()->getServerConfig("system");
		QString errMsg;
		int value;
		if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
			ZGLOG_WARN(errMsg);
		else
			m_initInterval = value;
		auto pair = serverGroup.find("updateFields");
		if (pair != serverGroup.end())
		{
			auto listFields = pair.value().split("&");
			m_listUpdateFields.clear();
			for (const auto& field : listFields)
			{
				m_listUpdateFields.push_back(field.toStdString());
			}
		}
	}

	bool ZGMPDatasetPublishMng::initMqttClient()
	{
		if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
		{
			ZGLOG_ERROR("initMqttClient error.");
			return false;
		}
		m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
		if (m_pMqttClient == nullptr)
		{
			ZGLOG_ERROR("getMqttClientMessage return null.");
			return false;
		}
		m_pMqttClient->connectToHost();
		return true;
	}

	bool ZGMPDatasetPublishMng::initRedisClient()
	{
		QList listClientType{ZGRuntime::REDIS_RT_TOPIC};
		if (!ZGRuntime::instance()->initRedisClient(listClientType))
		{
			ZGLOG_ERROR("initRedisClient error.");
			return false;
		}
		m_pRedisClient = ZGRuntime::instance()->getRedisClientRTTopic();
		if (m_pRedisClient == nullptr)
		{
			ZGLOG_ERROR("getRedisClientRTTopic return null.");
			return false;
		}
		return true;
	}

	bool ZGMPDatasetPublishMng::initModelParam()
	{
        for (const auto& tableName: m_listDataTable)
        {
            std::string modelTable = tableName;
            ZGUtils::replaceString(modelTable, "dataset", "model");
            if (!initModelDataParam(modelTable))
                return false;
        }
        return true;
	}

	bool ZGMPDatasetPublishMng::initDatasetParam()
	{
		QString sql = QString("SELECT id, isEnable, isPublishMQ, publishInterval FROM mp_param_dataset WHERE isEnable = 1");
		return ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapDatasetParam);
	}

    bool ZGMPDatasetPublishMng::initDataParam()
    {
		for (const auto& tableName : m_listDataTable)
		{
			ZGLOG_TRACE(QString("init table %1 data param").arg(tableName.c_str()));
		    if (!initTableDataParam(tableName))
				return false;
		}
		return true;
    }

    bool ZGMPDatasetPublishMng::initTableDataParam(const std::string& tableName)
    {
        QString sql = QString("SELECT a.id, a.datasetID, a.dataModelID FROM %1 a "
                              "LEFT JOIN mp_param_dataset b ON a.datasetID = b.id "
                              "WHERE b.isEnable = 1 ORDER BY a.id").arg(tableName.c_str());
        ListStringMap listResult;
		if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listResult))
		{
		    ZGLOG_ERROR(QStringLiteral("获取表'%1'数据参数失败").arg(tableName.c_str()));
			return false;
		}
        for (auto& result: listResult)
        {
			const auto & dataModelID = result["dataModelID"];
			auto pairModel = m_mapModelParam.find(dataModelID);
			if (pairModel == m_mapModelParam.end())
				continue;
			const auto & isEnable = m_mapModelParam[dataModelID]["isEnable"];
			if (isEnable != "1")
				continue;
			result["tableName"] = tableName;
			m_mapDatasetData[result["datasetID"]].push_back(result["id"]);
			auto pair = m_mapDataParam.find(result["id"]);
			if (pair != m_mapDataParam.end())
			{
			    ZGLOG_WARN(QStringLiteral("重复的数据ID'%1'").arg(result["id"].c_str()));
				return false;
			}
            m_mapDataParam[result["id"]] = std::move(result);
        }
		return true;
    }

    bool ZGMPDatasetPublishMng::initDataCategoryProperty()
	{
        std::string sql = "SELECT CONCAT(dataCategoryID, '/', propValue) AS id, propName, propNameL2 FROM mp_param_data_category_property";
        return ZGProxyCommon::execQuerySql(sql, m_mapDataCategoryProperty);
	}

	bool ZGMPDatasetPublishMng::initDatasetTickcount()
	{
		QString sql = QString("SELECT id, publishInterval FROM mp_param_dataset WHERE isEnable = 1 AND publishInterval > 0");
		StringMap datasetParam;
		if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), datasetParam))
		{
			ZGLOG_ERROR("initDatasetTickcount error.");
			return false;
		}
		for (const auto& [datasetID, interval] : datasetParam)
		{
			m_mapDatasetTickout[datasetID] = QRandomGenerator::global()->bounded(1, std::atoi(interval.c_str()));
		}
		return true;
	}

	bool ZGMPDatasetPublishMng::initModelDataParam(const std::string& tableName)
	{
        std::string fields = ZGUtils::join(m_mapModelFields[tableName]);
        QString sql = QString("SELECT %1 FROM %2").arg(fields.c_str()).arg(tableName.c_str());
		ZG6000::MapStringMap mapModelDataParam;
		if (!ZGProxyCommon::execQuerySql(sql.toStdString(), mapModelDataParam))
		{
			ZGLOG_ERROR(QStringLiteral("初始化模型表'%1'参数失败").arg(tableName.c_str()));
			return false;
		}
		m_mapModelParam.insert(mapModelDataParam.begin(), mapModelDataParam.end());
		return true;
	}

	void ZGMPDatasetPublishMng::initExtendFunc()
	{
        m_mapExtend["mp_param_dataset_bt"] = [this](const std::string& dataModelID, MapField& record)
        {
            auto pairValue = record.find("rtNewValue");
            if (pairValue != record.end())
            {
                record["rtValueDesc"] = ZG6000::FieldValue{};
                const auto& rtNewValue = pairValue->second;
                QReadLocker locker(&m_lock);
                auto pair = m_mapModelParam.find(dataModelID);
                if (pair != m_mapModelParam.end())
                {
                    std::string propValue = pair->second["dataCategoryID"] + "/" + rtNewValue.newValue;
                    auto it = m_mapDataCategoryProperty.find(propValue);
                    if (it != m_mapDataCategoryProperty.end())
                    {
                        auto dataCategoryProperty = it->second;
                        record["rtValueDesc"].newValue = dataCategoryProperty["propName"];
                        record["rtValueDescL2"].newValue = dataCategoryProperty["propNameL2"];
                    }
                }
            }
        };
		m_mapExtend["mp_param_dataset_yc"] = [this](const std::string& dataModelID, MapField& record)
		{
            auto pairValue = record.find("rtNewValue");
            if (pairValue != record.end())
            {
                record["unitID"] = ZG6000::FieldValue{};
                QReadLocker locker(&m_lock);
                auto pair = m_mapModelParam.find(dataModelID);
                if (pair != m_mapModelParam.end())
                    record["unitID"].newValue = pair->second["unitID"];
            }
		};
		m_mapExtend["mp_param_dataset_yx"] = [this](const std::string& dataModelID, MapField& record)
		{
            auto pairValue = record.find("rtNewValue");
            if (pairValue != record.end())
            {
                record["rtValueDesc"] = ZG6000::FieldValue{};
                const auto& rtNewValue = pairValue->second;
                QReadLocker locker(&m_lock);
                auto pair = m_mapModelParam.find(dataModelID);
                if (pair != m_mapModelParam.end())
                {
                    std::string propValue = pair->second["dataCategoryID"] + "/" + rtNewValue.newValue;
                    auto it = m_mapDataCategoryProperty.find(propValue);
                    if (it != m_mapDataCategoryProperty.end())
                    {
                        auto dataCategoryProperty = it->second;
                        record["rtValueDesc"].newValue = dataCategoryProperty["propName"];
                        record["rtValueDescL2"].newValue = dataCategoryProperty["propNameL2"];
                    }
                }
            }
		};
		m_mapExtend["mp_param_dataset_param"] = [this](const std::string& dataModelID, MapField& record)
		{
            auto pairValue = record.find("rtNewValue");
            if (pairValue != record.end())
            {
                record["unitID"] = ZG6000::FieldValue{};
                QReadLocker locker(&m_lock);
                auto pair = m_mapModelParam.find(dataModelID);
                if (pair != m_mapModelParam.end())
                {
                    record["unitID"].newValue = pair->second["unitID"];
                }
            }
		};
        m_mapExtend["mp_param_dataset_ym"] = [this](const std::string& dataModelID, MapField& record)
        {
            auto pairValue = record.find("rtNewValue");
            if (pairValue != record.end())
            {
                record["unitID"] = ZG6000::FieldValue{};
                QReadLocker locker(&m_lock);
                auto pair = m_mapModelParam.find(dataModelID);
                if (pair != m_mapModelParam.end())
                    record["unitID"].newValue = pair->second["unitID"];
            }
        };
	}

	bool ZGMPDatasetPublishMng::initServerInstInfo()
	{
		m_serverName = ZGRuntime::instance()->getServerID();
		if (m_serverName.isEmpty())
		{
			ZGLOG_ERROR("Empty server id.");
			return false;
		}
		m_instName = ZGRuntime::instance()->getInstanceID();
		if (m_instName.isEmpty())
		{
			ZGLOG_ERROR("Empty server instance id.");
			return false;
		}
		return true;
	}

	std::string ZGMPDatasetPublishMng::getTypeFromTableName(const std::string& tableName)
	{
		std::string typeName;
		size_t pos = tableName.find_last_of("_");
		if (pos != std::string::npos)
			typeName = tableName.substr(pos + 1);
		return typeName;
	}

	ZG6000::StringList ZGMPDatasetPublishMng::getRecordListID(const ListRecord& listRecord)
	{
		ZG6000::StringList listID;
		try
		{
			for (const auto& record : listRecord)
			{
				listID.push_back(ZGUtils::get(record, "id").newValue);
			}
		}
		catch (const std::exception& e)
		{
			ZGLOG_ERROR(e.what());
			listID.clear();
		}
		return listID;
	}

	void ZGMPDatasetPublishMng::publishToRedis(const std::string& dataTableName, const std::string& datasetID)
	{
		std::string modelTableName = dataTableName;
		ZGUtils::replaceString(modelTableName, "dataset", "model");
		QString sql = QString("SELECT a.id FROM %1 a "
			              "LEFT JOIN %2 b ON a.dataModelID = b.id "
			              "LEFT JOIN mp_param_dataset c ON a.datasetID = c.id "
			              "WHERE a.datasetID = '%3' AND b.isEnable = 1 AND c.isEnable = 1 ORDER BY a.id")
		              .arg(dataTableName.c_str())
		              .arg(modelTableName.c_str())
		              .arg(datasetID.c_str());
		ZG6000::StringList listID;
		if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
			return;
		if (listID.empty())
			return;
        std::string json = generatePublishMessage(dataTableName, listID);
		if (json.empty())
			return;
		const auto& typeName = getTypeFromTableName(dataTableName);
		long long subscribeNum;
		std::string errMsg;
		if (!m_pRedisClient->publish(datasetID + "/" + typeName, json, subscribeNum, errMsg))
			ZGLOG_ERROR(errMsg.c_str());
	}

	void ZGMPDatasetPublishMng::publishToMqtt(const std::string& dataTableName, const std::string& datasetID)
	{
		std::string modelTableName = dataTableName;
		ZGUtils::replaceString(modelTableName, "dataset", "model");
		QString sql = QString("SELECT a.id FROM %1 a "
			              "LEFT JOIN %2 b ON a.dataModelID = b.id "
			              "LEFT JOIN mp_param_dataset c ON a.datasetID = c.id "
			              "WHERE a.datasetID = '%3' AND b.isEnable = 1 "
			              "AND b.isPublishMQ = 1 AND c.isEnable = 1 AND c.isPublishMQ = 1 ORDER BY a.id")
		              .arg(dataTableName.c_str())
		              .arg(modelTableName.c_str())
		              .arg(datasetID.c_str());
		ZG6000::StringList listID;
		if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
			return;
		if (listID.empty())
			return;
        std::string json = generatePublishMessage(dataTableName, listID);
		if (json.empty())
			return;
		QString topicName = QString("%1/%2").arg(datasetID.c_str()).arg(json.c_str());
        m_pMqttClient->sendPublish(topicName, json.c_str());
    }

    void ZGMPDatasetPublishMng::publishDatasetData(const std::string &datasetID)
    {
        auto pair = m_mapDatasetData.find(datasetID);
        if (pair == m_mapDatasetData.end())
            return;
        const auto& listDataID = pair->second;
        std::map<std::string, StringList> mapTableMqttData;
        for (const auto& dataID: listDataID)
        {
			StringMap dataParam = m_mapDataParam[dataID];
			const auto & tableName = dataParam["tableName"];
            if (tableName == "mp_param_dataset_yk" || tableName == "mp_param_dataset_ys" || tableName == "mp_param_dataset_yt" || tableName == "mp_param_dataset_yv")
                continue;
			const auto & dataModelID = dataParam["dataModelID"];
			auto pairModel = m_mapModelParam.find(dataModelID);
			if (pairModel == m_mapModelParam.end())
				continue;
			const auto& modelParam = pairModel->second;
			const auto & isEnable = ZGUtils::get(modelParam, "isEnable", "");
            const auto& isPublishMQ = ZGUtils::get(modelParam, "isPublishMQ", "");
            if ((isEnable == "1") && (isPublishMQ == "1"))
			    mapTableMqttData[tableName].push_back(dataID);
        }
        for (const auto& [tableName, listDataID]: mapTableMqttData)
        {
            std::string json = generatePublishMessage(tableName, listDataID);
            if (json.empty())
                continue;
			const auto& typeName = getTypeFromTableName(tableName);
			QString topicName = QString("%1/%2").arg(datasetID.c_str()).arg(typeName.c_str());
            ZGLOG_TRACE(QString("topicName: '%1', message: '%2'").arg(topicName).arg(json.c_str()));
			m_pMqttClient->sendPublish(topicName, json.c_str());
        }
    }

	ZG6000::ListRecord ZGMPDatasetPublishMng::convertToListRecord(ZG6000::ListStringMap listData)
	{
		ZG6000::ListRecord listRecord;
		for (auto&& data : listData)
		{
			ZG6000::MapField record;
			for (auto&& [name, value] : data)
			{
				record[std::move(name)].newValue = std::move(value);
			}
			listRecord.push_back(std::move(record));
		}
		return listRecord;
	}

    std::string ZGMPDatasetPublishMng::generatePublishMessage(const std::string& tableName, const ZG6000::StringList& listID)
	{
		ZG6000::ListStringMap listData;
        if (!ZGProxyCommon::mgetDataByFields(tableName, listID, m_mapDataFields[tableName], listData))
			return "";
        auto pairExtend = m_mapExtend.find(tableName);
		ZG6000::ListRecord listRecord = convertToListRecord(listData);
        for (int i = 0; i < listID.size(); ++i)
        {
            if (pairExtend != m_mapExtend.end())
                pairExtend->second(m_mapDataParam[listID[i]]["dataModelID"], listRecord[i]);
        }
        std::string json = ZGJson::convertFromListRecordToJson(tableName, "update", "peroid", listRecord);
		return json;
	}
} // namespace ZG6000

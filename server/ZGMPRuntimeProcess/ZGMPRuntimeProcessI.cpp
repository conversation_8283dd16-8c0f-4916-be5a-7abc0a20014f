#include "ZGMPRuntimeProcessI.h"
#include "ZGMPRuntimeProcessMng.h"

namespace ZG6000 {

ZGMPRuntimeProcessI::ZGMPRuntimeProcessI()
{
    ZGMPRuntimeProcessMng::instance()->init();
}

bool ZGMPRuntimeProcessI::checkState(const Ice::Current &current)
{
    return ZGMPRuntimeProcessMng::instance()->checkState();
}

void ZGMPRuntimeProcessI::dispatchData(std::string tableName, std::string oper, std::string reason,
                                       std::string time, ListRecord listRecord, const Ice::Current &current)
{
    return ZGMPRuntimeProcessMng::instance()->dispatchData(tableName, oper, reason, time, listRecord);
}

} // namespace ZG6000

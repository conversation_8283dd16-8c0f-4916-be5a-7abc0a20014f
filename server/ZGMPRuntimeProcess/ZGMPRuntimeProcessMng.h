#ifndef ZGMPRUNTIMEPROCESSMNG_H
#define ZGMPRUNTIMEPROCESSMNG_H

#include <QTimer>
#include <unordered_set>

#include "ZGServerCommon.h"

class ZGMPRuntimeProcessMng : public QObject
{
	Q_OBJECT
public:
	static ZGMPRuntimeProcessMng* instance();
	void init();
	bool checkState();
	void dispatchData(const std::string& tableName, const std::string& oper, const std::string& reason,
                      const std::string& time, const ZG6000::ListRecord& listRecord);

private:
	explicit ZGMPRuntimeProcessMng(QObject* parent = nullptr);
	void initEvents();
	void initServerInstConfig();
	bool initServerInstInfo();
	bool initModel();
	bool initParam();
	bool initCalcParam();
	bool initCalcTableParam(const std::string& tableName, ZG6000::ListStringMap& listCalcPoint);
	void processLimitYc(const std::string& time, const ZG6000::ListRecord& listRecord);
	using RangeMap = std::map<std::pair<double, double>, std::string>;
	bool calculateLimitRange(const std::string& upLimit, const std::string& upUpLimit, const std::string& lowLimit, const std::string& lowLowLimit,
	                         const std::string& upUpLimitDeadZone, const std::string& upLimitDeadZone, const std::string& lowLimitDeadZone, const std::string& lowLowLimitDeadZone,
	                         RangeMap& mapUpRange, RangeMap& mapDownRange, std::string& errMsg);
	std::string calculateLimitType(double oldValue, double newValue, const RangeMap& mapUpRange, const RangeMap& mapDownRange);
	void calcRuntimeYx();
	void calcExpression();
	void calcExpressionForTable(const std::string& tableName, ZG6000::ListStringMap& listCalcPoint);
    void extendExpressionParam(const std::string& pointID, QString &expressionParam);

private slots:
	void onTimer();

private:
	bool m_initialized{false};
	QString m_serverName{""};
	QString m_instName{""};
	int m_initInterval{10};
	int m_checkInterval{10};
	QTimer m_checkTimer;
    ZG6000::StringMap m_mapSystemParam;
	ZG6000::MapStringMap m_mapModelYx;
	ZG6000::MapStringMap m_mapParamYx;
	std::unordered_set<std::string> m_setExpression;
	ZG6000::ListStringMap m_listCalcYx;
    ZG6000::ListStringMap m_listCalcStateYx;
	ZG6000::ListStringMap m_listCalcYc;
    ZG6000::ListStringMap m_listCalcStateYc;
	ZG6000::ListStringMap m_listCalcText;
    ZG6000::ListStringMap m_listCalcStateText;
	std::unordered_map<std::string, bool> m_mapLimitYc;
	ZG6000::StringList listAutoResetYXID;
    size_t m_tickCount{0};
};

#endif // ZGMPRUNTIMEPROCESSMNG_H

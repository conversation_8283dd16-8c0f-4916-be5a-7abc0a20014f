#include "ZGMPRuntimeProcessMng.h"

#include <QRandomGenerator>
#include <QJsonDocument>
#include <QtConcurrent>
#include <cfloat>

#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"

ZGMPRuntimeProcessMng* g_pInstance = nullptr;

ZGMPRuntimeProcessMng* ZGMPRuntimeProcessMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGMPRuntimeProcessMng;
    return g_pInstance;
}

void ZGMPRuntimeProcessMng::init()
{
    initEvents();
    initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    QThread::sleep(QRandomGenerator::global()->bounded(5, 10));
    while (!initModel())
    {
        ZGLOG_ERROR("initModel error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initParam())
    {
        ZGLOG_ERROR("initParam error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initCalcParam())
    {
        ZGLOG_ERROR("initCalcParam error.");
        QThread::msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    ZGLOG_INFO("ZGMPRuntimeProcess init finished.");
    m_checkTimer.start(1000);
}

bool ZGMPRuntimeProcessMng::checkState()
{
    return m_initialized;
}

void ZGMPRuntimeProcessMng::dispatchData(const std::string& tableName, const std::string& oper, const std::string& reason, const std::string& time, const ZG6000::ListRecord& listRecord)
{
    if (oper != "update")
        return;
    if (reason != "change")
        return;
    ZG6000::ListRecord listNewRecord;
    if (tableName == "mp_param_dataset_yc")
    {
        processLimitYc(time, listRecord);
    }
}

ZGMPRuntimeProcessMng::ZGMPRuntimeProcessMng(QObject* parent)
    : QObject{parent}
{
}

void ZGMPRuntimeProcessMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGMPRuntimeProcessMng::onTimer);
}

void ZGMPRuntimeProcessMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGMPRuntimeProcessMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGMPRuntimeProcessMng::initModel()
{
    QString sql = QString("SELECT * FROM mp_param_model_yx");
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapModelYx))
    {
        ZGLOG_ERROR(QStringLiteral("获取遥信模型参数失败"));
        return false;
    }
    return true;
}

bool ZGMPRuntimeProcessMng::initParam()
{
    QString sql = QString("SELECT * FROM mp_param_system");
    ZG6000::ListStringMap listSystem;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listSystem))
    {
        ZGLOG_ERROR(QStringLiteral("获取监控系统参数失败"));
        return false;
    }
    if (listSystem.empty())
    {
        ZGLOG_ERROR(QStringLiteral("未配置监控系统参数"));
        return false;
    }
    m_mapSystemParam = std::move(listSystem[0]);
    sql = QString("SELECT a.id, a.dataModelID FROM mp_param_dataset_yx a "
        "LEFT JOIN mp_param_model_yx b ON a.dataModelID = b.id "
        "WHERE b.validTimeInterval > 0 ORDER BY a.id");
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapParamYx))
    {
        ZGLOG_ERROR(QStringLiteral("获取遥信参数失败"));
        return false;
    }
    sql = "SELECT id FROM mp_param_dataset_yc WHERE isCheckLimit = 1";
    ZG6000::StringList listYcID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listYcID))
    {
        ZGLOG_ERROR(QStringLiteral("获取越限遥测列表失败"));
        return false;
    }
    for (const auto& ycId : listYcID)
    {
        m_mapLimitYc.insert(std::make_pair(ycId, true));
    }
    return true;
}

bool ZGMPRuntimeProcessMng::initCalcParam()
{
    QString sql = QString("SELECT id FROM sp_param_expression");
    ZG6000::StringList listExpression;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listExpression))
    {
        ZGLOG_ERROR(QStringLiteral("获取表达式参数失败"));
        return false;
    }
    for (const auto& expressionID : listExpression)
    {
        m_setExpression.insert(expressionID);
    }
    if (!initCalcTableParam("mp_param_dataset_yx", m_listCalcYx))
    {
        ZGLOG_ERROR(QStringLiteral("初始化计算遥信参数失败"));
        return false;
    }
    if (!initCalcTableParam("mp_param_dataset_yc", m_listCalcYc))
    {
        ZGLOG_ERROR(QStringLiteral("初始化计算遥测参数失败"));
        return false;
    }
    if (!initCalcTableParam("mp_param_dataset_text", m_listCalcText))
    {
        ZGLOG_ERROR(QStringLiteral("初始化计算文本参数失败"));
        return false;
    }
    return true;
}

bool ZGMPRuntimeProcessMng::initCalcTableParam(const std::string& tableName, ZG6000::ListStringMap& listCalcPoint)
{
    QString sql = QString("SELECT id, expressionID, expressionParam, stateExpressionID, stateExpressionParam FROM %1 WHERE expressionID <> ''"
        " OR stateExpressionID <> ''").arg(tableName.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listCalcPoint))
    {
        ZGLOG_ERROR(QStringLiteral("获取表'%1'计算参数失败").arg(tableName.c_str()));
        return false;
    }
    for (const auto& calcPoint : listCalcPoint)
    {
        try
        {
            const auto& pointID = ZGUtils::get(calcPoint, "id");
            const auto& expressionID = ZGUtils::get(calcPoint, "expressionID");
            if (!expressionID.empty())
            {
                const auto& expressionParam = ZGUtils::get(calcPoint, "expressionParam");
                if (!expressionParam.empty())
                {
                    QJsonParseError jsonParseError;
                    QJsonDocument doc = QJsonDocument::fromJson(expressionParam.c_str(), &jsonParseError);
                    if (jsonParseError.error != QJsonParseError::NoError)
                    {
                        ZGLOG_ERROR(QStringLiteral("'%1'参数配置出错'%2'").arg(pointID.c_str()).arg(jsonParseError.errorString()));
                        return false;
                    }
                }
            }
            const auto& stateExpressionID = ZGUtils::get(calcPoint, "stateExpressionID");
            if (!stateExpressionID.empty())
            {
                const auto& stateExpressionParam = ZGUtils::get(calcPoint, "stateExpressionParam");
                if (!stateExpressionParam.empty())
                {
                    QJsonParseError jsonParseError;
                    QJsonDocument doc = QJsonDocument::fromJson(stateExpressionParam.c_str(), &jsonParseError);
                    if (jsonParseError.error != QJsonParseError::NoError)
                    {
                        ZGLOG_ERROR(QStringLiteral("'%1'状态参数配置出错'%2'").arg(pointID.c_str()).arg(jsonParseError.errorString()));
                        return false;
                    }
                }
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
            return false;
        }
    }
    return true;
}

void ZGMPRuntimeProcessMng::processLimitYc(const std::string& time, const ZG6000::ListRecord& listRecord)
{
    ZG6000::ListStringMap listHisRecord;
    try
    {
        for (const auto& record : listRecord)
        {
            const auto& id = ZGUtils::get(record, "id");
            if (m_mapLimitYc.find(id.newValue) == m_mapLimitYc.end())
                continue;
            if (record.find("rtNewValue") == record.end())
                continue;
            ZG6000::StringMap fieldValue;
            if (!ZGProxyCommon::getDataByID("mp_param_dataset_yc", id.newValue, fieldValue))
            {
                ZGLOG_ERROR(QStringLiteral("获取遥测'%1'数据失败"));
                continue;
            }
            const auto& upLimit = ZGUtils::get(fieldValue, "upLimit");
            const auto& upUpLimit = ZGUtils::get(fieldValue, "upUpLimit");
            const auto& lowLimit = ZGUtils::get(fieldValue, "lowLimit");
            const auto& lowLowLimit = ZGUtils::get(fieldValue, "lowLowLimit");
            const auto& upUpLimitDeadZone = ZGUtils::get(fieldValue, "upUpLimitDeadZone");
            const auto& upLimitDeadZone = ZGUtils::get(fieldValue, "upLimitDeadZone");
            const auto& lowLimitDeadZone = ZGUtils::get(fieldValue, "lowLimitDeadZone");
            const auto& lowLowLimitDeadZone = ZGUtils::get(fieldValue, "lowLowLimitDeadZone");
            RangeMap mapUpRange, mapDownRange;
            std::string errMsg;
            if (!calculateLimitRange(upLimit, upUpLimit, lowLimit, lowLowLimit, upUpLimitDeadZone, upLimitDeadZone, lowLimitDeadZone, lowLowLimitDeadZone,
                mapUpRange, mapDownRange, errMsg))
            {
                ZGLOG_ERROR(errMsg.c_str());
                continue;
            }
            const auto& rtNewValue = ZGUtils::get(record, "rtNewValue");
            double oldValue = std::atof(rtNewValue.oldValue.c_str());
            double newValue = std::atof(rtNewValue.newValue.c_str());
            const auto& oldOverLimitTypeID = ZGUtils::get(fieldValue, "rtOverLimitTypeID");
            std::string newOverLimitTypeID = calculateLimitType(oldValue, newValue, mapUpRange, mapDownRange);
            ZGLOG_DEBUG(newOverLimitTypeID.c_str());
            if (newOverLimitTypeID != oldOverLimitTypeID)
            {
                const auto& currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
                ZG6000::StringMap limitRecord;
                limitRecord["rtOverLimitTypeID"] = newOverLimitTypeID;
                limitRecord["rtOverLimitTime"] = currentTime;
                if (ZGProxyCommon::updateDataByID("mp_param_dataset_yc", id.newValue, limitRecord))
                {
                    std::string sql = "SELECT a.id AS datasetID, a.appNodeID, a.subsystemID, a.majorID FROM mp_param_dataset a "
                        "LEFT JOIN mp_param_dataset_yc b ON a.id = b.datasetID WHERE b.id = '" + id.newValue + "' ORDER BY a.id";
                    ZG6000::StringMap dataset;
                    ZGProxyCommon::execQuerySqlRow(sql, dataset);
                    ZG6000::StringMap hisRecord;
                    hisRecord["appNodeID"] = dataset["appNodeID"];
                    hisRecord["subsystemID"] = dataset["subsystemID"];
                    hisRecord["majorID"] = dataset["majorID"];
                    hisRecord["datasetID"] = dataset["datasetID"];
                    hisRecord["dataID"] = id.newValue;
                    hisRecord["rtOldValue"] = rtNewValue.oldValue;
                    hisRecord["rtNewValue"] = rtNewValue.newValue;
                    hisRecord["rtOldOverLimitTypeID"] = oldOverLimitTypeID;
                    hisRecord["rtNewOverLimitTypeID"] = newOverLimitTypeID;
                    hisRecord["rtOverLimitTime"] = currentTime;
                    listHisRecord.emplace_back(std::move(hisRecord));
                }
            }
        }
        if (!listHisRecord.empty())
        {
            std::string tableName = "mp_his_yc_limit_" + std::to_string(QDateTime::currentDateTime().date().year());
            ZGProxyCommon::insertDataByTable(tableName, listHisRecord, true, true);
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

bool ZGMPRuntimeProcessMng::calculateLimitRange(const std::string& upLimit, const std::string& upUpLimit, const std::string& lowLimit, const std::string& lowLowLimit,
                                                const std::string& upUpLimitDeadZone, const std::string& upLimitDeadZone, const std::string& lowLimitDeadZone, const std::string& lowLowLimitDeadZone,
                                                RangeMap& mapUpRange, RangeMap& mapDownRange, std::string& errMsg)
{
    if (!upUpLimit.empty() && upLimit.empty())
    {
        errMsg = u8"未定义上限值";
        return false;
    }
    if (!lowLowLimit.empty() && lowLimit.empty())
    {
        errMsg = u8"未定义下限值";
        return false;
    }
    try
    {
        mapUpRange.insert({
            {
                upLimit.empty() ? DBL_MAX : std::stod(upLimit),
                lowLimit.empty() ? -DBL_MAX : std::stod(lowLimit) + std::atof(lowLimitDeadZone.c_str())
            },
            "ZG_OLT_NORMAL"
        });
        mapDownRange.insert({
            {
                upLimit.empty() ? DBL_MAX : std::stod(upLimit) - std::atof(upLimitDeadZone.c_str()),
                lowLimit.empty() ? -DBL_MAX : std::stod(lowLimit)
            },
            "ZG_OLT_NORMAL"
        });
        if (!upLimit.empty())
        {
            mapUpRange.insert({{upUpLimit.empty() ? DBL_MAX : std::stod(upUpLimit), std::stod(upLimit)}, "ZG_OLT_UP"});
            mapDownRange.insert({
                {
                    upUpLimit.empty() ? DBL_MAX : std::stod(upUpLimit) - std::atof(upUpLimitDeadZone.c_str()),
                    std::stod(upLimit) - std::atof(upLimitDeadZone.c_str())
                },
                "ZG_OLT_UP"
            });
        }
        if (!upUpLimit.empty())
        {
            mapUpRange.insert({{DBL_MAX, std::stod(upUpLimit)}, "ZG_OLT_UPUP"});
            mapDownRange.insert({{DBL_MAX, std::stod(upUpLimit) - std::atof(upUpLimitDeadZone.c_str())}, "ZG_OLT_UPUP"});
        }
        if (!lowLimit.empty())
        {
            mapUpRange.insert({
                {
                    std::stod(lowLimit) + std::atof(lowLimitDeadZone.c_str()),
                    lowLowLimit.empty() ? -DBL_MAX : std::stod(lowLowLimit) + std::atof(lowLowLimitDeadZone.c_str())
                },
                "ZG_OLT_DOWN"
            });
            mapDownRange.insert({{std::stod(lowLimit), lowLowLimit.empty() ? -DBL_MAX : std::stod(lowLowLimit)}, "ZG_OLT_DOWN"});
        }
        if (!lowLowLimit.empty())
        {
            mapUpRange.insert({{std::stod(lowLowLimit) + std::atof(lowLowLimitDeadZone.c_str()), -DBL_MAX}, "ZG_OLT_DOWNDOWN"});
            mapDownRange.insert({{std::stod(lowLowLimit), -DBL_MAX}, "ZG_OLT_DOWNDOWN"});
        }
        // ZGLOG_DEBUG("mapUpRange");
        // for (const auto & upRange : mapUpRange)
        // {
        // 	ZGLOG_DEBUG(QString("%1:%2-%3").arg(upRange.second.c_str()).arg(upRange.first.second).arg(upRange.first.first));
        // }
        // ZGLOG_DEBUG("mapDownRange");
        // for (const auto & downRange : mapDownRange)
        // {
        // 	ZGLOG_DEBUG(QString("%1:%2-%3").arg(downRange.second.c_str()).arg(downRange.first.second).arg(downRange.first.first));
        // }
        return true;
    }
    catch (const std::exception& e)
    {
        errMsg = e.what();
        return false;
    }
}

std::string ZGMPRuntimeProcessMng::calculateLimitType(double oldValue, double newValue, const RangeMap& mapUpRange, const RangeMap& mapDownRange)
{
    std::string newOverLimitTypeID;
    if (newValue >= oldValue)
    {
        for (const auto& upRange : mapUpRange)
        {
            if (newValue < upRange.first.first && newValue >= upRange.first.second)
            {
                newOverLimitTypeID = upRange.second;
                break;
            }
        }
    }
    else
    {
        for (const auto& downRange : mapDownRange)
        {
            if (newValue < downRange.first.first && newValue >= downRange.first.second)
            {
                newOverLimitTypeID = downRange.second;
                break;
            }
        }
    }
    return newOverLimitTypeID;
}

void ZGMPRuntimeProcessMng::calcRuntimeYx()
{
    ZG6000::StringList listYxID;
    for (const auto& [id, _] : m_mapParamYx)
    {
        listYxID.push_back(id);
    }
    ZG6000::ListStringMap listYxValue;
    if (!ZGProxyCommon::mgetDataByFields("mp_param_dataset_yx", listYxID, {"id", "dataModelID", "rtNewValue", "rtUpdateTime"}, listYxValue))
    {
        ZGLOG_ERROR(QStringLiteral("获取遥信状态失败"));
        return;
    }
    QDateTime dt = QDateTime::currentDateTime();
    ZG6000::StringList listNewYxID;
    ZG6000::ListStringMap listNewYxValue;
    for (auto& yxValue : listYxValue)
    {
        if (yxValue["rtNewValue"] == "1")
            continue;
        if (yxValue["rtUpdateTime"].empty())
        {
            listNewYxID.push_back(yxValue["id"]);
            listNewYxValue.push_back({{"rtNewValue", "1"}, {"rtUpdateTime", ZGUtils::DateTimeToString(dt).toStdString()}});
            continue;
        }
        QDateTime oldDt;
        if (!ZGUtils::StringToDateTime(yxValue["rtUpdateTime"].c_str(), oldDt))
        {
            listNewYxID.push_back(yxValue["id"]);
            listNewYxValue.push_back({{"rtNewValue", "1"}, {"rtUpdateTime", ZGUtils::DateTimeToString(dt).toStdString()}});
            continue;
        }
        auto pair = m_mapModelYx.find(yxValue["dataModelID"]);
        if (pair == m_mapModelYx.end())
            continue;
        const auto& modelYx = pair->second;
        const auto& validTimeInterval = ZGUtils::get(modelYx, "validTimeInterval", "0");
        int interval = std::atoi(validTimeInterval.c_str());
        if (std::abs(oldDt.secsTo(dt)) >= interval)
        {
            ZGLOG_TRACE(QString("yx '%1' auto reset, interval = %2").arg(yxValue["id"].c_str()).arg(interval));
            listNewYxID.push_back(yxValue["id"]);
            listNewYxValue.push_back({{"rtNewValue", "1"}, {"rtUpdateTime", ZGUtils::DateTimeToString(dt).toStdString()}});
        }
    }
    if (!ZGProxyCommon::mupdateDataByFields("mp_param_dataset_yx", listNewYxID, listNewYxValue))
    {
        ZGLOG_ERROR(QStringLiteral("更新遥信复归状态失败"));
    }
}

void ZGMPRuntimeProcessMng::calcExpression()
{
    calcExpressionForTable("mp_param_dataset_yx", m_listCalcYx);
    calcExpressionForTable("mp_param_dataset_yc", m_listCalcYc);
    calcExpressionForTable("mp_param_dataset_text", m_listCalcText);
}

void ZGMPRuntimeProcessMng::calcExpressionForTable(const std::string& tableName, ZG6000::ListStringMap& listCalcPoint)
{
    try
    {
        auto scriptPrx = ZGProxyMng::instance()->getProxySPScriptProcess();
        if (scriptPrx == nullptr)
        {
            ZGLOG_ERROR(QStringLiteral("获取脚本处理服务代理对象失败"));
            return;
        }
        ZG6000::StringList listPointID;
        ZG6000::ListStringMap listValue;
        ZG6000::ListStringMap listExpression, listStateExpression;
        bool enableStateExpression = true;
        auto pair = m_mapSystemParam.find("isEnableStateCheck");
        if (pair != m_mapSystemParam.end())
        {
            if (pair->second != "1")
                enableStateExpression = false;
        }
        for (const auto& calcPoint : listCalcPoint)
        {
            const auto& pointID = ZGUtils::get(calcPoint, "id");
            auto expressionID = ZGUtils::get(calcPoint, "expressionID");
            ZGUtils::trimString(expressionID);
            if (!expressionID.empty())
            {
                QString expressionParam = ZGUtils::get(calcPoint, "expressionParam").c_str();
                extendExpressionParam(pointID, expressionParam);
                listExpression.emplace_back(ZG6000::StringMap{{"id", expressionID}, {"param", expressionParam.toStdString()}});
            }
            if (enableStateExpression)
            {
                auto stateExpressionID = ZGUtils::get(calcPoint, "stateExpressionID");
                ZGUtils::trimString(stateExpressionID);
                if (!stateExpressionID.empty())
                {
                    QString expressionStateParam = ZGUtils::get(calcPoint, "stateExpressionParam").c_str();
                    extendExpressionParam(pointID, expressionStateParam);
                    listStateExpression.emplace_back(ZG6000::StringMap{{"id", stateExpressionID}, {"param", expressionStateParam.toStdString()}});
                }
            }
        }
        auto onewayProxy = scriptPrx->ice_oneway();
        if (!listExpression.empty())
            onewayProxy->callBatch(listExpression);
        if (!listStateExpression.empty())
            onewayProxy->callBatch(listStateExpression);
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGMPRuntimeProcessMng::extendExpressionParam(const std::string& pointID, QString& expressionParam)
{
    expressionParam = expressionParam.trimmed();
    if (!expressionParam.isEmpty())
    {
        auto pos = expressionParam.indexOf("[");
        expressionParam.insert(pos + 1, QString("\"%1\", ").arg(pointID.c_str()));
    }
    else
    {
        expressionParam = QString("[\"%1\"]").arg(pointID.c_str());
    }
}

void ZGMPRuntimeProcessMng::onTimer()
{
    if (!ZGRuntime::instance()->isMaster())
        return;
    m_tickCount++;
    auto future1 = QtConcurrent::run([&]()
        {
            calcRuntimeYx();
        }
    );
    if (m_tickCount %2 == 0)
    {
        auto future2 = QtConcurrent::run([&]()
            {
                calcExpression();
            }
        );
    }
}

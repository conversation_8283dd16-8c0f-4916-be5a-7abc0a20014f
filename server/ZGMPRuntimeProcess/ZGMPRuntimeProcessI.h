#ifndef ZG6000_ZGMPRUNTIMEPROCESSI_H
#define ZG6000_ZGMPRUNTIMEPROCESSI_H

#include <ZGMPRuntimeProcess.h>

namespace ZG6000 {

class ZGMPRuntimeProcessI : public ZGMPRuntimeProcess
{
public:
    ZGMPRuntimeProcessI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;
    void dispatchData(std::string tableName, std::string oper, std::string reason,
                      std::string time, ListRecord listRecord, const Ice::Current &current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGMPRUNTIMEPROCESSI_H

#ifndef ZGDPEVENTPARSEDEVICE_H
#define ZGDPEVENTPARSEDEVICE_H

#include "ZGDPEventParseBase.h"

class ZGDPEventParseDevice : public ZGDPEventParseBase
{
	Q_OBJECT
public:
	explicit ZGDPEventParseDevice(QObject* parent = nullptr);
public:
	bool initParam() override;

protected:
	std::string getAddition() override;
	std::string getAlarmLevelID() override;
	std::string getEventInfo() override;
	std::string getEventTypeID() override;
	std::string getIsPublishEvent() override;
	bool checkDataValid(const ZG6000::MapField& record) override;

protected:
	bool parseRecord(const ZG6000::MapField& record) override;

private:
	void initFieldDesc();
	void initFieldForeign();

private:
	ZG6000::StringMap m_mapFieldDesc;
	ZG6000::StringMap m_mapFieldForeign;
	ZG6000::StringMap m_mapFieldEvent;
	std::string m_deviceID;
};

#endif // ZGDPEVENTPARSEDEVICE_H

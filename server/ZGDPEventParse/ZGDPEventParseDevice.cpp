#include "ZGDPEventParseDevice.h"

#include "ZGProxyCommon.h"
#include "ZGUtils.h"

std::string ZGDPEventParseDevice::getAddition()
{
	return "";
}

std::string ZGDPEventParseDevice::getAlarmLevelID()
{
	return "ZG_AL_LEVEL4";
}

std::string ZGDPEventParseDevice::getEventInfo()
{
	std::string eventInfo;
	std::string deviceName;
	ZGProxyCommon::getDataByField("dp_param_device", m_deviceID, "name", deviceName);
	eventInfo += deviceName + " ";
	for (auto& pair: m_mapFieldEvent)
	{
		const auto& fieldDesc = m_mapFieldDesc[pair.first];
		auto it = m_mapFieldForeign.find(pair.first);
		std::string fieldValue;
		if (it != m_mapFieldForeign.end())
			ZGProxyCommon::getDataByField(it->second, pair.second, "name", fieldValue);
		else
			fieldValue = std::move(pair.second);
		eventInfo += fieldDesc + ": " + fieldValue + ", ";
	}
	m_mapFieldEvent.clear();
	m_deviceID.clear();
	return eventInfo;
}

std::string ZGDPEventParseDevice::getEventTypeID()
{
    return "ZG_ET_DEVICE_MANAGER";
}

std::string ZGDPEventParseDevice::getIsPublishEvent()
{
	return "1";
}

bool ZGDPEventParseDevice::checkDataValid(const ZG6000::MapField& record)
{
	return true;
}

bool ZGDPEventParseDevice::initParam()
{
	initFieldDesc();
	initFieldForeign();
	return true;
}

bool ZGDPEventParseDevice::parseRecord(const ZG6000::MapField& record)
{
	try
	{
		m_deviceID = ZGUtils::get(record, "id").newValue;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
	for (const auto & pair: record)
	{
		auto it = m_mapFieldDesc.find(pair.first);
		if (it != m_mapFieldDesc.end())
		{
			m_mapFieldEvent.insert(std::make_pair(pair.first, pair.second.newValue));
		}
	}
	return true;
}

void ZGDPEventParseDevice::initFieldDesc()
{
	m_mapFieldDesc.insert(std::make_pair("name", u8"设备名称"));
	m_mapFieldDesc.insert(std::make_pair("typeID", u8"设备类型"));
	m_mapFieldDesc.insert(std::make_pair("modelID", u8"模型"));
	m_mapFieldDesc.insert(std::make_pair("productModel", u8"型号"));
	m_mapFieldDesc.insert(std::make_pair("manufactorID", u8"制造商"));
	m_mapFieldDesc.insert(std::make_pair("organID", u8"部门"));
	m_mapFieldDesc.insert(std::make_pair("productDate", u8"生产日期"));
	m_mapFieldDesc.insert(std::make_pair("unitID", u8"单位"));
	m_mapFieldDesc.insert(std::make_pair("rtStateID", u8"设备状态"));
	m_mapFieldDesc.insert(std::make_pair("rtCoordinates", u8"坐标位置"));
	m_mapFieldDesc.insert(std::make_pair("rtHealthRuleID", u8"健康规则"));
}

void ZGDPEventParseDevice::initFieldForeign()
{
	m_mapFieldForeign.insert(std::make_pair("typeID", "dp_dict_device_type"));
	m_mapFieldForeign.insert(std::make_pair("modelID", "dp_param_device_model"));
	m_mapFieldForeign.insert(std::make_pair("productModelID", "dp_dict_product_model"));
	m_mapFieldForeign.insert(std::make_pair("manufactorID", "dp_dict_device_manufactory"));
	m_mapFieldForeign.insert(std::make_pair("organID", "sp_param_hrm_organ"));
	m_mapFieldForeign.insert(std::make_pair("rtStateID", "dp_dict_device_state"));
	m_mapFieldForeign.insert(std::make_pair("rtHealthRuleID", "sp_param_rule"));
}

ZGDPEventParseDevice::ZGDPEventParseDevice(QObject *parent)
    : ZGDPEventParseBase{parent}
{

}

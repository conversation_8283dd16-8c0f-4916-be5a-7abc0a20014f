#ifndef ZGDPEVENTPARSEDEVICEATTRIBUTE_H
#define ZGDPEVENTPARSEDEVICEATTRIBUTE_H

#include "ZGDPEventParseBase.h"

class ZGDPEventParseDeviceAttribute : public ZGDPEventParseBase
{
	Q_OBJECT
public:
	explicit ZGDPEventParseDeviceAttribute(QObject* parent = nullptr);

public:
	bool initParam() override;

protected:
	bool parseRecord(const ZG6000::MapField& record) override;
	bool checkDataValid(const ZG6000::MapField& record) override;
	std::string getEventTypeID() override;
	std::string getAlarmLevelID() override;
	std::string getEventInfo() override;
	std::string getAddition() override;
	std::string getIsPublishEvent() override;

private:
	bool initModelProperty();

private:
	ZG6000::MapStringMap m_mapModelProperty;
	std::string m_modelProperty;
	std::string m_deviceID;
	std::string m_propertyDesc;
	std::string m_propertyValue;
	std::string m_unitID;
};

#endif // ZGDPEVENTPARSEDEVICEATTRIBUTE_H

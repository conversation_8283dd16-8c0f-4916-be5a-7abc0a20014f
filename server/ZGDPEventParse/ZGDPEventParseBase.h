#ifndef ZGDPEVENTPARSEBASE_H
#define ZGDPEVENTPARSEBASE_H

#include <QObject>
#include <mutex>
#include "ZGProxyMng.h"
#include "ZGDebugMng.h"

using HashParam = std::unordered_map<std::string, std::string>;

class ZGDPEventParseBase : public QObject
{
    Q_OBJECT
public:
    explicit ZGDPEventParseBase(QObject *parent = nullptr);
    void dispatchEvent(const std::string& time, const ::ZG6000::ListRecord& listRecord);
    static bool initFixedParam();
    static bool initialize();
    virtual bool initParam() = 0;

protected:   
    virtual void generateEvents(const std::string& time, const ::ZG6000::ListRecord& listRecord);
    virtual void afterGenerateEvents() {}
    virtual bool parseRecord(const ZG6000::MapField& record) = 0;
    virtual bool checkDataValid(const ZG6000::MapField& record) = 0;
    virtual std::string getEventTime();
    virtual std::string getMajorID();
    virtual std::string getSubsystemID();
    virtual std::string getEventTypeID() = 0;
    virtual std::string getAlarmLevelID() = 0;
    virtual std::string getEventInfo() = 0;
    virtual std::string getAddition() = 0;
    virtual std::string getIsPublishEvent() = 0;
    virtual void generateEvent();
    virtual void debugEvent();
    virtual void processEvents();
    virtual void saveEvent();

protected:
    static bool initParamToMap(const std::string& sql, std::unordered_map<std::string, HashParam>& mapParam);
    static std::string getNameByID(const std::unordered_map<std::string, HashParam>&mapParam, const std::string& id);
    
protected:
    static inline ZG6000::MapStringMap m_mapAlarmLevel;
    static inline ZG6000::MapStringMap m_mapEventType;
    static inline ZG6000::MapStringMap m_mapSubsystem;
    static inline ZG6000::MapStringMap m_mapMajor;
    std::string m_eventTime;
    ZG6000::StringMap m_currentEvent;
    std::mutex m_mutex;
    std::string m_isPublishEvent;
    ZG6000::ListStringMap m_listEvent;
    ZG6000::StringList m_listIsPublishEvent;
};

#endif // ZGDPEVENTPARSEBASE_H

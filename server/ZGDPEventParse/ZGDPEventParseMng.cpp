#include "ZGDPEventParseMng.h"

#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "ZGDPEventParseBase.h"
#include "ZGDPEventParseDevice.h"
#include "ZGDPEventParseDeviceAttribute.h"

static ZGDPEventParseMng* g_pInstance = nullptr;

ZGDPEventParseMng* ZGDPEventParseMng::instance()
{
	if (g_pInstance == nullptr)
		g_pInstance = new ZGDPEventParseMng;
	return g_pInstance;
}

void ZGDPEventParseMng::init()
{
	initEvents();
	initServerInstConfig();
	initEventParseObject();
	start();
	ZGLOG_INFO("ZGSPEventParse init start...");
}

bool ZGDPEventParseMng::checkState(const Ice::Current& current)
{
	return m_initialized;
}

void ZGDPEventParseMng::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ZG6000::ListRecord listRecord, const Ice::Current& current)
{
	if (oper != "update")
		return; 
	if (reason != "change")
		return;
	try
	{
		m_mapEventParse.at(tableName)->dispatchEvent(time, listRecord);
	}
	catch (const std::exception& e)
	{
		std::string errMsg = tableName + ": " + e.what();
		ZGLOG_ERROR(errMsg.c_str());
	}
}

void ZGDPEventParseMng::run()
{
	while (!initServerInstInfo())
	{
		ZGLOG_ERROR("initServerInstInfo error");
		msleep(m_initInterval * 1000);
	}
	while (!ZGDPEventParseBase::initialize())
	{
		ZGLOG_ERROR("ZGSPEventParseBase::initialize error.");
		msleep(m_initInterval * 1000);
	}
	while (!initParseParam())
	{
		ZGLOG_ERROR("initParseParam error.");
		msleep(m_initInterval * 1000);
	}
	m_masterInst = ZGRuntime::instance()->isMaster();
	m_initialized = true;
	emit initFinished();
	ZGLOG_INFO("ZGDPEventParse init finished.");
}

void ZGDPEventParseMng::initEvents()
{
	connect(&m_checkTimer, &QTimer::timeout, this, &ZGDPEventParseMng::onCheckStatus);
	connect(this, &ZGDPEventParseMng::initFinished, this, &ZGDPEventParseMng::onInitFinished);
}

void ZGDPEventParseMng::initServerInstConfig()
{
	const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
	QString errMsg;
	int value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_initInterval = value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_checkInterval = value;
}

bool ZGDPEventParseMng::initServerInstInfo()
{
	m_serverName = ZGRuntime::instance()->getServerID();
	if (m_serverName.isEmpty())
	{
		ZGLOG_ERROR("Empty server id.");
		return false;
	}
	m_instName = ZGRuntime::instance()->getInstanceID();
	if (m_instName.isEmpty())
	{
		ZGLOG_ERROR("Empty server instance id.");
		return false;
	}
	return true;
}

void ZGDPEventParseMng::initEventParseObject()
{
	m_mapEventParse.insert(std::make_pair("dp_param_device", new ZGDPEventParseDevice));
	m_mapEventParse.insert(std::make_pair("dp_param_device_attribute", new ZGDPEventParseDeviceAttribute));
}

bool ZGDPEventParseMng::initParseParam()
{
	for (const auto& pairEventParse : m_mapEventParse)
	{
		if (!pairEventParse.second->initParam())
		{
			ZGLOG_ERROR(QString("%1 initParam error.").arg(pairEventParse.first.c_str()));
			return false;
		}
	}
	return true;
}

void ZGDPEventParseMng::onInitFinished()
{
	m_checkTimer.start(m_checkInterval * 1000);
}

void ZGDPEventParseMng::onCheckStatus()
{
	m_masterInst = ZGRuntime::instance()->isMaster();
}

ZGDPEventParseMng::ZGDPEventParseMng(QObject *parent)
    : QThread{parent}
{
	
}

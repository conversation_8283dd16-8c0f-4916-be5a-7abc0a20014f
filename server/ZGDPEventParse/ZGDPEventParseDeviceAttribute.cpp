#include "ZGDPEventParseDeviceAttribute.h"

#include <ZGUtils.h>

#include "ZGProxyCommon.h"

bool ZGDPEventParseDeviceAttribute::initParam()
{
	if (!initModelProperty())
	{
		ZGLOG_ERROR(u8"初始化模型属性失败");
		return false;
	}
	return true;
}

bool ZGDPEventParseDeviceAttribute::parseRecord(const ZG6000::MapField& record)
{
	try
	{
		const auto& id = ZGUtils::get(record, "id");
		ZG6000::StringMap mapFieldValue;
		if (!ZGProxyCommon::getDataByFields("dp_param_device_attribute", id.newValue, {"deviceID", "modelID", "attributeName"},
			mapFieldValue))
		{
			ZGLOG_ERROR(QStringLiteral("获取设备属性'%1'失败").arg(id.newValue.c_str()));
			return false;
		}
		m_deviceID = mapFieldValue["deviceID"];
		const auto& modelID = mapFieldValue["modelID"];
		const auto& attributeName = mapFieldValue["attributeName"];
		m_modelProperty = modelID + "/" + attributeName;
		auto pair = m_mapModelProperty.find(m_modelProperty);
		if (pair != m_mapModelProperty.end())
		{
			m_propertyDesc = pair->second["description"];
			m_unitID = pair->second["unitID"];
		}
        ZGLOG_DEBUG(QString("property desc: %1").arg(m_propertyDesc.c_str()));
		const auto& attributeValue = ZGUtils::get(record, "attributeValue");
		m_propertyValue = attributeValue.newValue;
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

bool ZGDPEventParseDeviceAttribute::checkDataValid(const ZG6000::MapField& record)
{
	return record.find("attributeValue") != record.end();
}

std::string ZGDPEventParseDeviceAttribute::getEventTypeID()
{
	auto pair = m_mapModelProperty.find(m_modelProperty);
	if (pair != m_mapModelProperty.end())
		return pair->second["eventTypeID"];
	return "";
}

std::string ZGDPEventParseDeviceAttribute::getAlarmLevelID()
{
	auto pair = m_mapModelProperty.find(m_modelProperty);
	if (pair != m_mapModelProperty.end())
		return pair->second["alarmLevelID"];
	return "";
}

std::string ZGDPEventParseDeviceAttribute::getEventInfo()
{
	std::string deviceName;
	ZGProxyCommon::getDataByField("dp_param_device", m_deviceID, "name", deviceName);
	QString eventInfo = QString("%1 %2: %3%4").arg(deviceName.c_str())
		.arg(m_propertyDesc.c_str()).arg(m_propertyValue.c_str()).arg(m_unitID.c_str());
	m_modelProperty.clear();
	m_deviceID.clear();
	m_propertyDesc.clear();
	m_propertyValue.clear();
	m_unitID.clear();
	return eventInfo.toStdString();
}

std::string ZGDPEventParseDeviceAttribute::getAddition()
{
	return "";
}

std::string ZGDPEventParseDeviceAttribute::getIsPublishEvent()
{
	auto pair = m_mapModelProperty.find(m_modelProperty);
	if (pair != m_mapModelProperty.end())
		return pair->second["isPublishEvent"];
	return "";
}

bool ZGDPEventParseDeviceAttribute::initModelProperty()
{
	std::string sql = "SELECT CONCAT(modelID,'/',name) AS id, eventTypeID, alarmLevelID, isPublishEvent, description, unitID FROM dp_param_device_model_attribute";
	return ZGProxyCommon::execQuerySql(sql, m_mapModelProperty);
}

ZGDPEventParseDeviceAttribute::ZGDPEventParseDeviceAttribute(QObject *parent)
    : ZGDPEventParseBase{parent}
{

}

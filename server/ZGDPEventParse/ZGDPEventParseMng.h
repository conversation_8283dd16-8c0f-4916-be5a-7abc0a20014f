#ifndef ZGDPEVENTPARSEMNG_H
#define ZGDPEVENTPARSEMNG_H

#include <QThread>
#include <QTimer>
#include <Ice/Ice.h>
#include "ZGServerCommon.h"

class ZGDPEventParseBase;
class ZGDPEventParseMng : public QThread
{
	Q_OBJECT
public:
    static ZGDPEventParseMng* instance();
    void init();
    bool checkState(const Ice::Current& current);
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ZG6000::ListRecord listRecord, const Ice::Current& current);

protected:
    void run() override;
private:
    explicit ZGDPEventParseMng(QObject* parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    void initEventParseObject();
    bool initParseParam();

signals:
    void initFinished();

private slots:
    void onInitFinished();
    void onCheckStatus();

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    bool m_masterInst{false};
    int m_initInterval{10};
    int m_checkInterval{5};
    QTimer m_checkTimer;
    std::unordered_map<std::string, ZGDPEventParseBase*> m_mapEventParse;
};

#endif // ZGDPEVENTPARSEMNG_H

#include "ZGDPEventParseI.h"
#include "ZGDPEventParseMng.h"

namespace ZG6000 {

ZGDPEventParseI::ZGDPEventParseI()
{
	ZGDPEventParseMng::instance()->init();
}

bool ZG6000::ZGDPEventParseI::checkState(const Ice::Current& current)
{
	return ZGDPEventParseMng::instance()->checkState(current);
}

void ZG6000::ZGDPEventParseI::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current& current)
{
	ZGDPEventParseMng::instance()->dispatchData(tableName, oper, reason, time, std::move(listRecord), current);
}

} // namespace ZG6000
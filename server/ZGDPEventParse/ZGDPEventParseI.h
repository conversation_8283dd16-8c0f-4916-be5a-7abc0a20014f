#ifndef ZG6000_ZGDPEVENTPARSEI_H
#define ZG6000_ZGDPEVENTPARSEI_H

#include "ZGDPEventParse.h"

namespace ZG6000 {

class ZGDPEventParseI : public ZGDPEventParse
{
public:
    ZGDPEventParseI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current& current) override;
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current& current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGDPEVENTPARSEI_H

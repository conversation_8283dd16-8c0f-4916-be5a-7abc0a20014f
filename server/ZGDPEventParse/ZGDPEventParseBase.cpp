#include "ZGDPEventParseBase.h"
#include "ZGHeartMng.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "ZGJson.h"
#include "ZGProxyCommon.h"

ZGDPEventParseBase::ZGDPEventParseBase(QObject *parent) : QObject(parent)
{

}

void ZGDPEventParseBase::dispatchEvent(const std::string& time,
	const ::ZG6000::ListRecord& listRecord)
{
    std::unique_lock locker(m_mutex);
    generateEvents(time, listRecord);
    processEvents();
}

bool ZGDPEventParseBase::initFixedParam()
{
	try
	{
        std::string sql = "SELECT id, name, nameL2 from sp_dict_event_type";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapEventType))
        {
            ZGLOG_ERROR("init event type error.");
            return false;
        }
        sql = "SELECT id, name, nameL2 isPlayFile, isPlayTTS, playCount from sp_dict_alarm_level";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapAlarmLevel))
        {
            ZGLOG_ERROR("init alarm level error.");
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM sp_param_subsystem";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapSubsystem))
        {
            ZGLOG_ERROR("init subsystem error.");
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM sp_param_major";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapMajor))
        {
            ZGLOG_ERROR("init major error.");
            return false;
        }
        ZGLOG_DEBUG(QString("subsystem size: %1").arg(m_mapSubsystem.size()));
        return true;
	}
	catch (const std::exception& e)
	{
        ZGLOG_ERROR(e.what());
        return false;
	}
}

bool ZGDPEventParseBase::initialize()
{
    if (!initFixedParam())
        return false;
    return true;
}

std::string ZGDPEventParseBase::getNameByID(const std::unordered_map<std::string, HashParam>& mapParam,
    const std::string& id)
{
    if (id.empty())
        return "";
    try
    {
        const auto & param = ZGUtils::get(mapParam, id);
        return ZGUtils::get(param, "name");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

inline void convertMapToUnorderedMap(ZG6000::StringMap& mapParam, HashParam& unorderedMapParam)
{
    for (auto& param : mapParam)
    {
        unorderedMapParam.insert(std::move(param));
    }
}

bool ZGDPEventParseBase::initParamToMap(const std::string& sql, std::unordered_map<std::string, HashParam>& mapParam)
{
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql, listRecord))
        return false;
    for (auto& record : listRecord)
    {
        try
        {
            const auto& id = ZGUtils::get(record, "id");
            HashParam param;
            convertMapToUnorderedMap(record, param);
            mapParam.insert(std::make_pair(id, std::move(param)));
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
    return true;
}

void ZGDPEventParseBase::generateEvents(const std::string& time, const ::ZG6000::ListRecord& listRecord)
{
    m_eventTime = time;
    for (const auto& record : listRecord)
    {
        if (!checkDataValid(record))
            continue;
        if (!parseRecord(record))
            continue;
        generateEvent();
        saveEvent();
    }
    afterGenerateEvents();
}

std::string ZGDPEventParseBase::getEventTime()
{
    return m_eventTime;
}

std::string ZGDPEventParseBase::getMajorID()
{
    return "ZG_PT_DM";
}

std::string ZGDPEventParseBase::getSubsystemID()
{
	return "ZG_SS_DM";
}

void ZGDPEventParseBase::generateEvent()
{
    m_isPublishEvent = getIsPublishEvent();
    m_currentEvent.insert(std::make_pair("eventTime", getEventTime()));
    const auto& subsystemId = getSubsystemID();
    m_currentEvent.insert(std::make_pair("subsystemId", subsystemId));
    m_currentEvent.insert(std::make_pair("subsystemName", m_mapSubsystem[subsystemId]["name"]));
    m_currentEvent.insert(std::make_pair("subsystemNameL2", m_mapSubsystem[subsystemId]["nameL2"]));
    const auto& majorId = getMajorID();
    m_currentEvent.insert(std::make_pair("majorID", majorId));
    m_currentEvent.insert(std::make_pair("majorName", m_mapMajor[majorId]["name"]));
    m_currentEvent.insert(std::make_pair("majorNameL2", m_mapMajor[majorId]["nameL2"]));
    const auto & eventTypeId = getEventTypeID();
    m_currentEvent.insert(std::make_pair("eventTypeID", eventTypeId));
    m_currentEvent.insert(std::make_pair("eventTypeName", m_mapEventType[eventTypeId]["name"]));
    m_currentEvent.insert(std::make_pair("eventTypeNameL2", m_mapEventType[eventTypeId]["nameL2"]));
    const auto & alarmLevelId = getAlarmLevelID();
    m_currentEvent.insert(std::make_pair("alarmLevelID", alarmLevelId));
    m_currentEvent.insert(std::make_pair("alarmLevelName", m_mapAlarmLevel[alarmLevelId]["name"]));
    m_currentEvent.insert(std::make_pair("alarmLevelNameL2", m_mapAlarmLevel[alarmLevelId]["nameL2"]));
    m_currentEvent.insert(std::make_pair("eventInfo", getEventInfo()));
    m_currentEvent.insert(std::make_pair("addition", getAddition()));
}

void ZGDPEventParseBase::debugEvent()
{
    for (const auto& pair : m_currentEvent)
    {
        qDebug() << pair.first.c_str() << ": " << pair.second.c_str();
    }
}

void ZGDPEventParseBase::processEvents()
{
    auto eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
    if (eventProcessPrx == nullptr)
    {
        ZGLOG_ERROR("getProxySPEventProcess error.");
        return;
    }
    auto onewayEventProcessPrx = eventProcessPrx->ice_oneway();
    if (onewayEventProcessPrx == nullptr)
    {
        ZGLOG_ERROR("get one way eventProcessPrx error.");
        return;
    }
    try
    {
        onewayEventProcessPrx->processZGDPEvents(m_listEvent, m_listIsPublishEvent);
        m_listEvent.clear();
        m_listIsPublishEvent.clear();
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGDPEventParseBase::saveEvent()
{
    m_listEvent.push_back(m_currentEvent);
    m_listIsPublishEvent.push_back((m_isPublishEvent));
    m_currentEvent.clear();
}

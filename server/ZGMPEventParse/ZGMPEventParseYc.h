#ifndef ZGMPEVENTPARSEYC_H
#define ZGMPEVENTPARSEYC_H

#include "ZGMPEventParseData.h"
#include <unordered_map>

class ZGMPEventParseYc : public ZGMPEventParseData
{
    Q_OBJECT

public:
    explicit ZGMPEventParseYc(QObject *parent = nullptr);
    // ZGMPEventParseBase interface
protected:
    bool initOtherParam() override;
    ZG6000::StringList getModelFields() override;
    std::string getDataTableName() override;
    std::string getEventTypeID() override;
    bool checkDataValid() override;
    std::string processOverLimitEvent(const HashParam &modelParam, const ZG6000::FieldValue &value) override;
    std::string processOverLimitEventL2(const HashParam &modelParam, const ZG6000::FieldValue &value) override;

protected:
    ZG6000::MapStringMap m_mapLimitType;
};

#endif // ZGMPEVENTPARSEYC_H

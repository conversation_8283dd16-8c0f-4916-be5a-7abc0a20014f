#include "ZGMPEventParseBt.h"

ZGMPEventParseBt::ZGMPEventParseBt(QObject *parent)
    : ZGMPEventParseData{parent}
{

}

std::string ZGMPEventParseBt::getEventTypeID()
{
    return "ZG_ET_CHANGE_BT";
}

std::string ZGMPEventParseBt::getDataTableName()
{
    return "mp_param_dataset_bt";
}

ZG6000::StringList ZGMPEventParseBt::getModelFields()
{
    return ZG6000::StringList{"id", "dataCategoryID",
        "isPublishEvent", "isChangeStore", "alarmLevelID"};
}

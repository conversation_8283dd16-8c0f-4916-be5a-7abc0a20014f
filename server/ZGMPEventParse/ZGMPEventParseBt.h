#ifndef ZGMPEVENTPARSEBT_H
#define ZGMPEVENTPARSEBT_H

#include "ZGMPEventParseData.h"

class ZGMPEventParseBt : public ZGMPEventParseData
{
    Q_OBJECT

public:
    explicit ZGMPEventParseBt(QObject *parent = nullptr);

    // ZGMPEventParseBase interface
protected:
    std::string getEventTypeID() override;
    std::string getDataTableName() override;
    ZG6000::StringList getModelFields() override;
};

#endif // ZGMPEVENTPARSEBT_H

#include "ZGMPEventParseDevice.h"
#include "ZGJson.h"
#include "ZGMPEventParseData.h"
#include "ZGMPEventParseMng.h"

ZGMPEventParseDevice::ZGMPEventParseDevice(QObject *parent) : ZGMPEventParseBase(parent)
{

}

bool ZGMPEventParseDevice::initParam()
{
    if (!initAppNodeParam())
        return false;
    return initDeviceParam();
}

bool ZGMPEventParseDevice::parseRecord(const ZG6000::MapField& record)
{
    try
    {
        if ((record.find("rtState") == record.end()) && (record.find("rtMasterState") == record.end())
            && (record.find("rtANetState") == record.end()) && (record.find("rtBNetState") == record.end())
            && (record.find("rtCNetState") == record.end()) && (record.find("rtDNetState") == record.end()))
            return false;
        const auto & rtState = record.find("rtState");
        if (rtState != record.end())
            m_netState = rtState->second.newValue;
        const auto & rtMasterState = record.find("rtMasterState");
        if (rtMasterState != record.end())
            m_masterState = rtMasterState->second.newValue;
        const auto& rtANetState = record.find("rtANetState");
        if (rtANetState != record.end())
            m_netAState = rtANetState->second.newValue;
        const auto& rtBNetState = record.find("rtBNetState");
        if (rtBNetState != record.end())
            m_netBState = rtBNetState->second.newValue;
        const auto& rtCNetState = record.find("rtCNetState");
        if (rtCNetState != record.end())
            m_netCState = rtCNetState->second.newValue;
        const auto& rtDNetState = record.find("rtDNetState");
        if (rtDNetState != record.end())
            m_netDState = rtDNetState->second.newValue;
        m_deviceID = ZGUtils::get(record, "id").newValue;
        const auto & device = ZGUtils::get(m_mapDeviceParam, m_deviceID);
        m_deviceName = ZGUtils::get(device, "name");
        m_datasetID = ZGUtils::get(device, "datasetID");
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

bool ZGMPEventParseDevice::checkDataValid()
{
    return true;
}

std::string ZGMPEventParseDevice::getAppNodeID()
{
    try
    {
        const auto & device = ZGUtils::get(m_mapDeviceParam, m_deviceID);
        std::string appNodeID = ZGUtils::get(device, "appNodeID");
        ZGLOG_DEBUG(QString("appNodeID: %1").arg(appNodeID.c_str()));
        return ZGUtils::get(device, "appNodeID");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGMPEventParseDevice::getSubsystemID()
{
    try
    {
        const auto & device = ZGUtils::get(m_mapDeviceParam, m_deviceID);
        return ZGUtils::get(device, "subsystemID");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}


std::string ZGMPEventParseDevice::getMajorID()
{
    try
    {
        const auto & device = ZGUtils::get(m_mapDeviceParam, m_deviceID);
        return ZGUtils::get(device, "majorID");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGMPEventParseDevice::getEventTypeID()
{
    return "ZG_ET_DEVICE";
}

std::string ZGMPEventParseDevice::getAlarmLevelID()
{
    return "ZG_AL_LEVEL1";
}

std::string ZGMPEventParseDevice::getPlayTTSTypeID()
{
    return "";
}

std::string ZGMPEventParseDevice::getAlarmColor()
{
    return "";
}

std::string ZGMPEventParseDevice::getDataCategoryID()
{
    return "ZG_DC_DEVICE_STATUS";
}

std::string ZGMPEventParseDevice::getEventInfo()
{
    std::string eventName = m_deviceName;
    if (!m_netState.empty())
    {
        eventName += " ";
        eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "networkState") + ": ";
        if (m_netState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "normal");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "break");
    }
    if (!m_masterState.empty())
    {
        eventName += " ";
        eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "masterState") + ": ";
        if (m_masterState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "master");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "slave");
    }
    if (!m_netAState.empty())
    {
        eventName += " ";
        eventName += "A" + ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "netState") + ": ";
        if (m_netAState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "normal");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "break");
    }
    if (!m_netBState.empty())
    {
        eventName += " ";
        eventName += "B" + ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "netState") + ": ";
        if (m_netBState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "normal");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "break");
    }
    if (!m_netCState.empty())
    {
        eventName += " ";
        eventName += "C" + ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "netState") + ": ";
        if (m_netCState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "normal");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "break");
    }
    if (!m_netDState.empty())
    {
        eventName += " ";
        eventName += "D" + ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "netState") + ": ";
        if (m_netDState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "normal");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "break");
    }
    ZGLOG_TRACE(eventName.c_str());
    return eventName;
}

std::string ZGMPEventParseDevice::getEventInfoL2()
{
    std::string eventName = m_deviceName;
    if (!m_netState.empty())
    {
        eventName += " ";
        eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "networkState") + ": ";
        if (m_netState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "break");
    }
    if (!m_masterState.empty())
    {
        eventName += " ";
        eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "masterState") + ": ";
        if (m_masterState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "master");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "slave");
    }
    if (!m_netAState.empty())
    {
        eventName += " ";
        eventName += "A" + ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "netState") + ": ";
        if (m_netAState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "break");
    }
    if (!m_netBState.empty())
    {
        eventName += " ";
        eventName += "B" + ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "netState") + ": ";
        if (m_netBState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "break");
    }
    if (!m_netCState.empty())
    {
        eventName += " ";
        eventName += "C" + ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "netState") + ": ";
        if (m_netCState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "break");
    }
    if (!m_netDState.empty())
    {
        eventName += " ";
        eventName += "D" + ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "netState") + ": ";
        if (m_netDState == "2")
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventName += ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "break");
    }
    ZGLOG_TRACE(eventName.c_str());
    m_netState.clear();
    m_masterState.clear();
    m_netAState.clear();
    m_netBState.clear();
    m_netCState.clear();
    m_netDState.clear();
    return eventName;
}

std::string ZGMPEventParseDevice::getAddition()
{
    ZG6000::StringMap mapAddition;
    mapAddition.insert(std::make_pair("deviceID", m_deviceID));
    return ZGJson::convertToJson(mapAddition);
}

std::string ZGMPEventParseDevice::getDeviceID()
{
    return m_deviceID;
}

std::string ZGMPEventParseDevice::getPointID()
{
    return "";
}

void ZGMPEventParseDevice::saveEvent()
{
    ZGMPEventParseBase::saveEvent();
    m_listListAppNodeID.emplace_back(std::move(m_listAppNodeID));
    m_listAppNodeID.clear();
}

bool ZGMPEventParseDevice::initDeviceParam()
{
    try
    {
        std::string sql = "SELECT id, name, datasetID, appNodeID, subsystemID, majorID from mp_param_device";
        if (!initParamToMap(sql, m_mapDeviceParam))
        {
            ZGLOG_ERROR("init device error.");
            return false;
        }
        sql = "SELECT id FROM sp_param_appnode";
        if (!ZGProxyCommon::execQuerySqlCol(sql, m_listWholeAppNodeID))
        {
            ZGLOG_ERROR("init appnode error.");
            return false;
        }
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

std::string ZGMPEventParseDevice::getIsPublishEvent()
{
    return "1";
}

void ZGMPEventParseDevice::generateEvent()
{
    ZGMPEventParseBase::generateEvent();
    const auto & appNodeId = getAppNodeID();
    m_currentEvent.insert(std::make_pair("srcNodeID", appNodeId));
    m_currentEvent.insert(std::make_pair("srcNodeName", getNameByID(m_mapAppnode, appNodeId)));
    if (m_datasetID.empty())
        m_listAppNodeID = m_listWholeAppNodeID;
    else
        m_listAppNodeID = ZGUtils::get(ZGMPEventParseData::m_mapDatasetTopics, m_datasetID);
}

void ZGMPEventParseDevice::processEvents()
{
    if (m_listEvent.empty())
        return;
    auto eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
    if (eventProcessPrx == nullptr)
    {
        ZGLOG_ERROR("getProxySPEventProcess error.");
        return;
    }
    auto onewayEventProcessPrx = eventProcessPrx->ice_oneway();
    if (onewayEventProcessPrx == nullptr)
    {
        ZGLOG_ERROR("get one way eventProcessPrx error.");
        return;
    }
    try
    {
        onewayEventProcessPrx->processZGMPEvents(m_listEvent, m_listListAppNodeID, m_listIsPublishEvent);
        m_listEvent.clear();
        m_listListAppNodeID.clear();
        m_listIsPublishEvent.clear();
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

bool ZGMPEventParseDevice::getCheckPowers()
{
    return false;
}

std::string ZGMPEventParseDevice::getPowers()
{
    return "";
}

bool ZGMPEventParseDevice::initAppNodeParam()
{
    std::string sql = "SELECT id, name FROM sp_param_appnode";
    if (!initParamToMap(sql, m_mapAppnode))
    {
        ZGLOG_ERROR("init appnode error.");
        return false;
    }
    return true;
}

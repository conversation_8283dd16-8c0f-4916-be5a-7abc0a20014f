#ifndef ZGMPEVENTPARSEBASE_H
#define ZGMPEVENTPARSEBASE_H

#include <QObject>
#include <mutex>
#include "ZGUtils.h"
#include "ZGProxyMng.h"
#include "ZGDebugMng.h"

using HashParam = std::unordered_map<std::string, std::string>;

class ZGMPEventParseBase : public QObject
{
    Q_OBJECT
public:
    explicit ZGMPEventParseBase(QObject *parent = nullptr);
    void dispatchEvent(const std::string& time, const ::ZG6000::ListRecord& listRecord);
    void dispatchEvent(const ZG6000::ListStringMap& listRecord);
    static bool initFixedParam();
    static bool initialize();
    virtual bool initParam() = 0;

protected:   
    virtual void generateEvents(const std::string& time, const ::ZG6000::ListRecord& listRecord);
    virtual void generateEvents(const ZG6000::ListStringMap& listRecords);
    virtual void afterGenerateEvents() {}
    virtual bool parseRecord(const ZG6000::MapField& record) = 0;
    virtual bool parseRecord(const ZG6000::StringMap& record){Q_UNUSED(record) return true;}
    virtual bool checkDataValid() = 0;
    virtual std::string getEventTime();
    virtual std::string getAppNodeID() = 0;
    virtual std::string getEventTypeID() = 0;
    virtual std::string getAlarmLevelID() = 0;
    virtual std::string getPlayTTSTypeID() = 0;
    virtual std::string getAlarmColor() = 0;
    virtual std::string getMajorID() = 0;
    virtual std::string getSubsystemID() = 0;
    virtual std::string getDataCategoryID() = 0;
    virtual std::string getEventInfo() = 0;
    virtual std::string getEventInfoL2() = 0;
    virtual bool getCheckPowers() = 0;
    virtual std::string getPowers() = 0;
    virtual std::string getAddition() = 0;
    virtual std::string getIsPublishEvent() = 0;
    virtual std::string getDeviceID() = 0;
    virtual std::string getPointID() = 0;
    virtual void generateEvent();
    virtual void processEvents() = 0;
    virtual void saveEvent();
    virtual void debugEvent();

protected:
    static bool initParamToMap(const std::string& sql, std::unordered_map<std::string, HashParam>& mapParam);
    static std::string getNameByID(const std::unordered_map<std::string, HashParam>&mapParam, const std::string& id);
    static std::string getNameL2ByID(const std::unordered_map<std::string, HashParam>&mapParam, const std::string& id);
    
protected:
    static std::unordered_map<std::string, HashParam> m_mapAlarmLevel;
    static std::unordered_map<std::string, HashParam> m_mapEventType;
    static std::unordered_map<std::string, HashParam> m_mapMajor;
    static std::unordered_map<std::string, HashParam> m_mapSubsystem;
    static std::unordered_map<std::string, HashParam> m_mapDataCategory;
    std::string m_eventTime;
    ZG6000::StringMap m_currentEvent;
    std::mutex m_mutex;
    std::string m_isPublishEvent;
    ZG6000::ListStringMap m_listEvent;
    ZG6000::StringList m_listIsPublishEvent;
};

#endif // ZGMPEVENTPARSEBASE_H

#ifndef ZGMPEVENTPARSEYM_H
#define ZGMPEVENTPARSEYM_H

#include "ZGMPEventParseData.h"

class ZGMPEventParseYm : public ZGMPEventParseData
{
    Q_OBJECT

public:
    explicit ZGMPEventParseYm(QObject *parent = nullptr);

    // ZGMPEventParseBase interface
protected:
    std::string getDataTableName() override;
    ZG6000::StringList getModelFields() override;
    bool checkDataValid() override;
    std::string getEventTypeID() override;
};

#endif // ZGMPEVENTPARSEYM_H

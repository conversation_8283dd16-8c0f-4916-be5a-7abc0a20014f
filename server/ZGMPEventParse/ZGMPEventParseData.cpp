#include "ZGMPEventParseData.h"
#include "ZGJson.h"
#include "ZGMPEventParseMng.h"
#include "ZGUtils.h"

ZGMPEventParseData::ZGMPEventParseData(QObject* parent) : ZGMPEventParseBase(parent)
{
}

bool ZGMPEventParseData::initParam()
{
    if (!initAppNodeParam())
        return false;
    if (!initDatasetParam())
        return false;
    if (!initDeviceParam())
        return false;
    if (!initPointParam())
        return false;
    if (!initModelParam())
        return false;
    if (!initCategoryParam())
        return false;
    if (!initOtherParam())
        return false;
    initFieldProcessor();
    initFieldProcessorL2();
    return true;
}

bool ZGMPEventParseData::parseRecord(const ZG6000::MapField& record)
{
    try
    {
        m_mapFieldValue.clear();
        for (const auto & [field, value]: record)
        {
            if (field == "rtNewValue" || field == "rtStateValue" || field == "rtOverLimitTypeID")
            {
                m_mapFieldValue[field.c_str()] = value;
                ZGLOG_TRACE(QString("%1:%2").arg(field.c_str()).arg(value.newValue.c_str()));
            }
        }
        if (m_mapFieldValue.isEmpty())
            return false;
        const auto& id = ZGUtils::get(record, "id");
        m_pointID = id.newValue;
        const auto& point = ZGUtils::get(m_mapPointParam, m_pointID);
        m_pointName = ZGUtils::get(point, "name");
        m_datasetID = ZGUtils::get(point, "datasetID");
        m_deviceID = ZGUtils::get(point, "deviceID");
        m_modelID = ZGUtils::get(point, "dataModelID");
        ZGLOG_TRACE(QString("pointID: '%1'").arg(m_pointID.c_str()));
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

bool ZGMPEventParseData::parseRecord(const ZG6000::StringMap& record)
{
    try
    {
        m_mapFieldValue.clear();
        const auto& reason = ZGUtils::get(record, "reason");
        if (reason != "change")
            return false;
        m_pointID = ZGUtils::get(record, "id");
        std::string value = ZGUtils::get(record, "rtNewValue");
        m_mapFieldValue["rtNewValue"] = ZG6000::FieldValue{"", value};
        const auto& point = ZGUtils::get(m_mapPointParam, m_pointID);
        m_pointName = ZGUtils::get(point, "name");
        m_datasetID = ZGUtils::get(point, "datasetID");
        m_deviceID = ZGUtils::get(point, "deviceID");
        m_modelID = ZGUtils::get(point, "dataModelID");
        m_eventTime = ZGUtils::get(record, "rtUpdateTime");
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

bool ZGMPEventParseData::checkDataValid()
{
    try
    {
        const auto& dataset = ZGUtils::get(m_mapDataset, m_datasetID);
        const auto& isEnable = ZGUtils::get(dataset, "isEnable");
        if (isEnable != "1")
            return false;
        const auto& model = ZGUtils::get(m_mapModelParam, m_modelID);
        const auto& isChangeStore = ZGUtils::get(model, "isChangeStore");
        if (isChangeStore != "1")
            return false;
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

std::string ZGMPEventParseData::getAppNodeID()
{
    if (m_datasetID.empty())
        return "";
    try
    {
        return ZGUtils::get(m_mapDatasetAppNode, m_datasetID);
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGMPEventParseData::getSubsystemID()
{
    if (m_datasetID.empty())
        return "";
    try
    {
        return ZGUtils::get(m_mapDatasetSubsystem, m_datasetID);
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGMPEventParseData::getAlarmLevelID()
{
    try
    {
        const auto& model = ZGUtils::get(m_mapModelParam, m_modelID);
        return ZGUtils::get(model, "alarmLevelID");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGMPEventParseData::getPlayTTSTypeID()
{
    try
    {
        const auto& model = ZGUtils::get(m_mapModelParam, m_modelID);
        return ZGUtils::get(model, "playTTSTypeID", "");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGMPEventParseData::getAlarmColor()
{
    try
    {
        const auto& model = ZGUtils::get(m_mapModelParam, m_modelID);
        return ZGUtils::get(model, "alarmColor", "");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGMPEventParseData::getMajorID()
{
    if (m_datasetID.empty())
        return "";
    try
    {
        return ZGUtils::get(m_mapDatasetMajor, m_datasetID);
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGMPEventParseData::getDataCategoryID()
{
    try
    {
        const auto& model = ZGUtils::get(m_mapModelParam, m_modelID);
        return ZGUtils::get(model, "dataCategoryID");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGMPEventParseData::getEventInfo()
{
    std::string eventInfo{""};
    do
    {
        auto pairDataset = m_mapDataset.find(m_datasetID);
        if (pairDataset == m_mapDataset.end())
        {
            eventInfo = QStringLiteral("找不到数据集'%1'").arg(m_datasetID.c_str()).toStdString();
            break;
        }
        auto pairPoint = m_mapPointParam.find(m_pointID);
        if (pairPoint == m_mapPointParam.end())
        {
            eventInfo = QStringLiteral("找不到数据点'%1'").arg(m_pointID.c_str()).toStdString();
            break;
        }
        auto pairModel = m_mapModelParam.find(m_modelID);
        if (pairModel == m_mapModelParam.end())
        {
            eventInfo = QStringLiteral("找不到模型点'%1'").arg(m_modelID.c_str()).toStdString();
            break;
        }
        auto dataset = pairDataset->second;
        if (dataset["isEventPrefix"] == "1")
            eventInfo = dataset["name"] + " ";
        if (!m_deviceID.empty())
        {
            auto pairDevice = m_mapDevice.find(m_deviceID);
            if (pairDevice != m_mapDevice.end())
            {
                auto device = pairDevice->second;
                if (device["isEventPrefix"] == "1")
                    eventInfo += device["name"] + " ";
            }
        }
        auto point = pairPoint->second;
        const auto& pointName = point["name"];
        eventInfo += pointName;
        const auto& modelParam = pairModel->second;
        for (auto it = m_mapFieldValue.begin(); it != m_mapFieldValue.end(); ++it)
        {
            const auto& fieldName = it.key().toStdString();
            auto pair = m_mapFieldProcessor.find(fieldName);
            if (pair != m_mapFieldProcessor.end())
            {
                const auto& msg = pair->second(modelParam, it.value());
                eventInfo += " " + msg;
            }
        }
    }
    while (0);
    return eventInfo;
}

// 从各表的nameL2字段获取第二语言的名称，组装成事件信息
std::string ZGMPEventParseData::getEventInfoL2()
{
    std::string eventInfo{""};
    do
    {
        auto pairDataset = m_mapDataset.find(m_datasetID);
        if (pairDataset == m_mapDataset.end())
        {
            eventInfo = QStringLiteral("找不到数据集'%1'").arg(m_datasetID.c_str()).toStdString();
            break;
        }
        auto pairPoint = m_mapPointParam.find(m_pointID);
        if (pairPoint == m_mapPointParam.end())
        {
            eventInfo = QStringLiteral("找不到数据点'%1'").arg(m_pointID.c_str()).toStdString();
            break;
        }
        auto pairModel = m_mapModelParam.find(m_modelID);
        if (pairModel == m_mapModelParam.end())
        {
            eventInfo = QStringLiteral("找不到模型点'%1'").arg(m_modelID.c_str()).toStdString();
            break;
        }
        auto dataset = pairDataset->second;
        if (dataset["isEventPrefix"] == "1")
            eventInfo = dataset["nameL2"] + " ";
        if (!m_deviceID.empty())
        {
            auto pairDevice = m_mapDevice.find(m_deviceID);
            if (pairDevice != m_mapDevice.end())
            {
                auto device = pairDevice->second;
                if (device["isEventPrefix"] == "1")
                    eventInfo += device["nameL2"] + " ";
            }
        }
        auto point = pairPoint->second;
        const auto& pointName = point["nameL2"];
        ZGLOG_TRACE(QString("pointName: '%1'").arg(pointName.c_str()));
        eventInfo += pointName;
        const auto& modelParam = pairModel->second;
        for (auto it = m_mapFieldValue.begin(); it != m_mapFieldValue.end(); ++it)
        {
            const auto& fieldName = it.key().toStdString();
            auto pair = m_mapFieldProcessorL2.find(fieldName);
            if (pair != m_mapFieldProcessorL2.end())
            {
                const auto& msg = pair->second(modelParam, it.value());
                eventInfo += " " + msg;
            }
        }
    } while (0);
    return eventInfo;
}

bool ZGMPEventParseData::getCheckPowers()
{
    return true;
}

std::string ZGMPEventParseData::getPowers()
{
    try
    {
        const auto& model = ZGUtils::get(m_mapModelParam, m_modelID);
        return ZGUtils::get(model, "eventPowers", "");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}


//std::string ZGMPEventParseData::getEventInfo()
//{
//    std::string eventName;
//    if (m_mapDataset[m_datasetID]["isEventPrefix"] == "1")
//        eventName = m_mapDataset[m_datasetID]["name"] + " ";
//    eventName += m_pointName;
//    if (m_eventValue)
//    {
//        eventName += ": " + m_value.newValue;
//    }
//    if (m_eventStateValue)
//    {
//        try
//        {
//            const auto & model = ZGUtils::get(m_mapModelParam, m_modelID);
//            std::string dataCategoryID = ZGUtils::get(model, "stateDataCategoryID");
//            const auto key = dataCategoryID + "/" + m_value.newValue;
//            const auto& dataCategoryProperty = ZGUtils::get(m_mapDataCategoryProperty, key);
//            const auto& propertyName = ZGUtils::get(dataCategoryProperty, "propName");
//            eventName += QStringLiteral("状态: %1").arg(propertyName.c_str()).toStdString();
//        }
//        catch (const std::exception& e)
//        {
//            ZGLOG_ERROR(e.what());
//        }
//    }
//    return eventName;
//}

std::string ZGMPEventParseData::getAddition()
{
    return "";
}

std::string ZGMPEventParseData::getIsPublishEvent()
{
    auto pairModel = m_mapModelParam.find(m_modelID);
    if (pairModel == m_mapModelParam.end())
        return "0";
    const auto& modelParam = pairModel->second;
    const auto& isPublishEvent = ZGUtils::get(modelParam, "isPublishEvent", "0");
    if (isPublishEvent != "1")
        return "0";
    do
    {
        auto pairValue = m_mapFieldValue.find("rtNewValue");
        if (pairValue != m_mapFieldValue.end())
        {
            const auto& dataCategoryID = ZGUtils::get(modelParam, "dataCategoryID", "");
            if (dataCategoryID.empty())
                break;
            const auto& value = pairValue.value();
            const auto key = dataCategoryID + "/" + value.newValue;
            auto pairProp = m_mapDataCategoryProperty.find(key);
            if (pairProp == m_mapDataCategoryProperty.end())
                break;
            const auto& dataCategoryProperty = pairProp->second;
            const auto& propIsPublishEvent = ZGUtils::get(dataCategoryProperty, "isPublishEvent", "0");
            if (propIsPublishEvent != "1")
                return "0";
            return "1";
        }
        auto pairStateValue = m_mapFieldValue.find("rtStateValue");
        if (pairStateValue != m_mapFieldValue.end())
        {
            const auto& dataCategoryID = ZGUtils::get(modelParam, "stateDataCategoryID", "");
            if (dataCategoryID.empty())
                break;
            const auto& value = pairStateValue.value();
            const auto key = dataCategoryID + "/" + value.newValue;
            auto pairProp = m_mapDataCategoryProperty.find(key);
            if (pairProp == m_mapDataCategoryProperty.end())
                break;
            const auto& dataCategoryProperty = pairProp->second;
            const auto& propIsPublishEvent = ZGUtils::get(dataCategoryProperty, "isPublishEvent", "0");
            if (propIsPublishEvent != "1")
                return "0";
            return "1";
        }
    } while(0);
    return isPublishEvent;
}

std::string ZGMPEventParseData::getDeviceID()
{
    return m_deviceID;
}

std::string ZGMPEventParseData::getPointID()
{
    return m_pointID;
}

ZG6000::StringList ZGMPEventParseData::getModelFields()
{
    return ZG6000::StringList{"id", "dataCategoryID", "stateDataCategoryID", "playTTSTypeID", "alarmColor",
        "isPublishEvent", "isChangeStore", "alarmLevelID", "eventPowers"};
}

ZG6000::StringList ZGMPEventParseData::getDataFields()
{
    return ZG6000::StringList{"id", "name", "nameL2", "voice", "datasetID", "dataModelID", "deviceID"};
}

bool ZGMPEventParseData::initOtherParam()
{
    return true;
}

bool ZGMPEventParseData::initPointParam()
{
    const auto& listPointFields = getDataFields();
    const auto& fields = ZGUtils::join(listPointFields, ",");
    QString sql = QString("SELECT %1 FROM %2").arg(fields.c_str()).arg(getDataTableName().c_str());
    return initParamToMap(sql.toStdString(), m_mapPointParam);
}

bool ZGMPEventParseData::initModelParam()
{
    const auto& listModelFields = getModelFields();
    const auto& fields = ZGUtils::join(listModelFields, ",");
    std::string modelTableName = getDataTableName();
    ZGUtils::replaceString(modelTableName, "dataset", "model");
    QString sql = QString("SELECT %1 FROM %2").arg(fields.c_str()).arg(modelTableName.c_str());
    return initParamToMap(sql.toStdString(), m_mapModelParam);
}

bool ZGMPEventParseData::initCategoryParam()
{
    try
    {
        std::string sql = "SELECT id, dataCategoryID, propValue, propName, propNameL2, isPublishEvent from mp_param_data_category_property";
        std::unordered_map<std::string, HashParam> mapDataCategoryProperty;
        if (!initParamToMap(sql, mapDataCategoryProperty))
        {
            ZGLOG_ERROR("init data category property error.");
            return false;
        }
        for (const auto& [_, dataCategoryProperty]: mapDataCategoryProperty)
        {
            const auto& dataCategoryID = ZGUtils::get(dataCategoryProperty, "dataCategoryID");
            const auto& propValue = ZGUtils::get(dataCategoryProperty, "propValue");
            const auto key = dataCategoryID + "/" + propValue;
            HashParam hashParam;
            hashParam.insert(std::make_pair("propName", ZGUtils::get(dataCategoryProperty, "propName")));
            hashParam.insert(std::make_pair("propNameL2", ZGUtils::get(dataCategoryProperty, "propNameL2")));
            hashParam.insert(std::make_pair("isPublishEvent", ZGUtils::get(dataCategoryProperty, "isPublishEvent")));
            m_mapDataCategoryProperty.insert(std::make_pair(key, std::move(hashParam)));
        }
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

void ZGMPEventParseData::afterGenerateEvents()
{
    try
    {
        m_listAppNodeID.clear();
        if (!m_datasetID.empty() && m_mapDatasetTopics.find(m_datasetID) != m_mapDatasetTopics.end())
            m_listAppNodeID = m_mapDatasetTopics[m_datasetID];
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGMPEventParseData::generateEvent()
{
    ZGMPEventParseBase::generateEvent();
    const auto& appNodeId = getAppNodeID();
    m_currentEvent.insert(std::make_pair("srcNodeID", appNodeId));
    m_currentEvent.insert(std::make_pair("srcNodeName", getNameByID(m_mapAppnode, appNodeId)));
}

void ZGMPEventParseData::processEvents()
{
    if (m_listEvent.empty())
        return;
    auto eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
    if (eventProcessPrx == nullptr)
    {
        ZGLOG_ERROR("getProxySPEventProcess error.");
        return;
    }
    auto onewayEventProcessPrx = eventProcessPrx->ice_oneway();
    if (onewayEventProcessPrx == nullptr)
    {
        ZGLOG_ERROR("get one way eventProcessPrx error.");
        return;
    }
    try
    {
        onewayEventProcessPrx->processZGMPDatasetEvents(m_listEvent, m_listAppNodeID, m_listIsPublishEvent);
        m_listEvent.clear();
        m_listAppNodeID.clear();
        m_listIsPublishEvent.clear();
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

std::string ZGMPEventParseData::processValueEvent(const HashParam &modelParam, const ZG6000::FieldValue &value)
{
    std::string content = value.newValue;
    do
    {
        auto pairDataCategory = modelParam.find("dataCategoryID");
        if (pairDataCategory == modelParam.end())
            break;
        const auto& dataCategoryID = pairDataCategory->second;
        if (dataCategoryID.empty())
            break;
        const auto& key = dataCategoryID + "/" + value.newValue;
        auto pairProperty = m_mapDataCategoryProperty.find(key);
        if (pairProperty == m_mapDataCategoryProperty.end())
            break;
        const auto& dataCategoryProperty = pairProperty->second;
        const auto& propName = ZGUtils::get(dataCategoryProperty, "propName", "");
        content = propName;
    }
    while (0);
    const auto& unitID = ZGUtils::get(modelParam, "dataUnitID", "");
    if (!unitID.empty())
        content += "(" + unitID + ")";
    return content;
}

std::string ZGMPEventParseData::processValueEventL2(const HashParam& modelParam,
    const ZG6000::FieldValue& value)
{
    std::string content = value.newValue;
    do
    {
        auto pairDataCategory = modelParam.find("dataCategoryID");
        if (pairDataCategory == modelParam.end())
            break;
        const auto& dataCategoryID = pairDataCategory->second;
        if (dataCategoryID.empty())
            break;
        const auto& key = dataCategoryID + "/" + value.newValue;
        auto pairProperty = m_mapDataCategoryProperty.find(key);
        if (pairProperty == m_mapDataCategoryProperty.end())
            break;
        const auto& dataCategoryProperty = pairProperty->second;
        const auto& propName = ZGUtils::get(dataCategoryProperty, "propNameL2", "");
        content = propName;
    }
    while (0);
    const auto& unitID = ZGUtils::get(modelParam, "dataUnitID", "");
    if (!unitID.empty())
        content += "(" + unitID + ")";
    ZGLOG_TRACE(QString("content: %1").arg(content.c_str()));
    return content;
}

std::string ZGMPEventParseData::processStateValueEvent(const HashParam &modelParam, const ZG6000::FieldValue &value)
{
    std::string content = value.newValue;
    do
    {
        auto pairDataCategory = modelParam.find("stateDataCategoryID");
        if (pairDataCategory == modelParam.end())
            break;
        const auto& dataCategoryID = pairDataCategory->second;
        if (dataCategoryID.empty())
            break;
        const auto& key = dataCategoryID + "/" + value.newValue;
        auto pairProperty = m_mapDataCategoryProperty.find(key);
        if (pairProperty == m_mapDataCategoryProperty.end())
            break;
        const auto& dataCategoryProperty = pairProperty->second;
        const auto& propName = ZGUtils::get(dataCategoryProperty, "propName", "");
        content = propName;
    }
    while (0);
    content = ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "status") + content;
    return content;
}

std::string ZGMPEventParseData::processStateValueEventL2(const HashParam& modelParam,
    const ZG6000::FieldValue& value)
{
    std::string content = value.newValue;
    do
    {
        auto pairDataCategory = modelParam.find("stateDataCategoryID");
        if (pairDataCategory == modelParam.end())
            break;
        const auto& dataCategoryID = pairDataCategory->second;
        if (dataCategoryID.empty())
            break;
        const auto& key = dataCategoryID + "/" + value.newValue;
        auto pairProperty = m_mapDataCategoryProperty.find(key);
        if (pairProperty == m_mapDataCategoryProperty.end())
            break;
        const auto& dataCategoryProperty = pairProperty->second;
        const auto& propName = ZGUtils::get(dataCategoryProperty, "propNameL2", "");
        content = propName;
    }
    while (0);
    content = ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "status") + " " + content;
    return content;
}

std::string ZGMPEventParseData::processOverLimitEvent(const HashParam &modelParam, const ZG6000::FieldValue &value)
{
    return "";
}

std::string ZGMPEventParseData::processOverLimitEventL2(const HashParam& modelParam,
    const ZG6000::FieldValue& value)
{
    return "";
}

void ZGMPEventParseData::initFieldProcessor()
{
    m_mapFieldProcessor.insert({
        "rtNewValue", [this](const HashParam& modelParam, const ZG6000::FieldValue& value)
        {
            return processValueEvent(modelParam, value);
        }
    });
    m_mapFieldProcessor.insert({
        "rtStateValue", [this](const HashParam& modelParam, const ZG6000::FieldValue& value)
        {
            return processStateValueEvent(modelParam, value);
        }
    });
    m_mapFieldProcessor.insert({
        "rtOverLimitTypeID", [this](const HashParam& modelParam, const ZG6000::FieldValue& value)
        {
            return processOverLimitEvent(modelParam, value);
        }
    });
}

void ZGMPEventParseData::initFieldProcessorL2()
{
    m_mapFieldProcessorL2.insert({
        "rtNewValue", [this](const HashParam& modelParam, const ZG6000::FieldValue& value)
        {
            return processValueEventL2(modelParam, value);
        }
    });
    m_mapFieldProcessorL2.insert({
        "rtStateValue", [this](const HashParam& modelParam, const ZG6000::FieldValue& value)
        {
            return processStateValueEventL2(modelParam, value);
        }
    });
    m_mapFieldProcessorL2.insert({
        "rtOverLimitTypeID", [this](const HashParam& modelParam, const ZG6000::FieldValue& value)
        {
            return processOverLimitEventL2(modelParam, value);
        }
    });
}

bool ZGMPEventParseData::initAppNodeParam()
{
    if (!m_appNodeParamInited)
    {
        try
        {
            std::string sql = "SELECT id, name, nameL2 from sp_param_appnode";
            if (!initParamToMap(sql, m_mapAppnode))
            {
                ZGLOG_ERROR("init appnode error.");
                return false;
            }
            sql = "SELECT id, appNodeID, subsystemID FROM mp_param_dataset";
            ZG6000::ListStringMap listDataset;
            if (!ZGProxyCommon::execQuerySql(sql, listDataset))
            {
                ZGLOG_ERROR(QStringLiteral("获取数据集参数失败"));
                return false;
            }
            for (const auto& dataset: listDataset)
            {
                const auto& datasetID = ZGUtils::get(dataset, "id");
                const auto& appnodeID = ZGUtils::get(dataset, "appNodeID");
                const auto& subsystemID = ZGUtils::get(dataset, "subsystemID");
                std::string topicName = appnodeID + "/" + subsystemID;
                m_mapDatasetTopics[datasetID].push_back(topicName);
            }
            std::unordered_map<std::string, HashParam> mapAppnodeDataset;
            sql = "SELECT id, appNodeID, subsystemID, datasetID from mp_param_appnode_dataset";
            if (!initParamToMap(sql, mapAppnodeDataset))
            {
                ZGLOG_ERROR("init appnode dataset error.");
                return false;
            }
            for (const auto& [_, appnodeDataset]: mapAppnodeDataset)
            {
                const auto& appnodeID = ZGUtils::get(appnodeDataset, "appNodeID");
                const auto& subsystemID = ZGUtils::get(appnodeDataset, "subsystemID");
                const auto& datasetID = ZGUtils::get(appnodeDataset, "datasetID");
                std::string topicName = appnodeID + "/" + subsystemID;
                auto pair = m_mapDatasetTopics.find(datasetID);
                if (pair == m_mapDatasetTopics.end())
                    m_mapDatasetTopics.insert(std::make_pair(datasetID, ZG6000::StringList{topicName}));
                else
                {
                    const auto& listTopic = pair->second;
                    if (std::find(listTopic.begin(), listTopic.end(), topicName) == listTopic.end())
                        pair->second.push_back(topicName);
                }
            }
            m_appNodeParamInited = true;
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
            return false;
        }
    }
    return m_appNodeParamInited;
}

bool ZGMPEventParseData::initDatasetParam()
{
    m_mapDataset.clear();
    std::string sql = "SELECT id, name, nameL2, appNodeID, subsystemID, majorID, isEventPrefix, isEnable FROM mp_param_dataset";
    if (!initParamToMap(sql, m_mapDataset))
    {
        ZGLOG_ERROR("init appnode error.");
        return false;
    }
    try
    {
        for (const auto& [id, dataset]: m_mapDataset)
        {
            const auto& appNodeID = ZGUtils::get(dataset, "appNodeID");
            m_mapDatasetAppNode.insert(std::make_pair(id, appNodeID));
            const auto& subsystemID = ZGUtils::get(dataset, "subsystemID");
            m_mapDatasetSubsystem.insert(std::make_pair(id, subsystemID));
            const auto& majorID = ZGUtils::get(dataset, "majorID");
            m_mapDatasetMajor.insert(std::make_pair(id, majorID));
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
    return true;
}

bool ZGMPEventParseData::initDeviceParam()
{
    m_mapDevice.clear();
    QString sql = QString("SELECT id, name, nameL2 isEventPrefix FROM mp_param_device");
    if (!initParamToMap(sql.toStdString(), m_mapDevice))
    {
        ZGLOG_ERROR("init device error.");
        return false;
    }
    return true;
}

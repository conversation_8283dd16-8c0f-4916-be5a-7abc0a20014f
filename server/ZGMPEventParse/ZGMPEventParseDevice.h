#ifndef ZGMPEVENTPARSEDEVICE_H
#define ZGMPEVENTPARSEDEVICE_H

#include "ZGMPEventParseBase.h"

class ZGMPEventParseDevice : public ZGMPEventParseBase
{
    Q_OBJECT

public:
    explicit ZGMPEventParseDevice(QObject *parent = nullptr);

protected:
    bool initParam() override;
    bool parseRecord(const ZG6000::MapField& record) override;
    bool checkDataValid() override;
    std::string getAppNodeID() override;
    std::string getSubsystemID() override;
    std::string getMajorID() override;
    std::string getEventTypeID() override;
    std::string getAlarmLevelID() override;
    std::string getPlayTTSTypeID() override;
    std::string getAlarmColor() override;
    std::string getDataCategoryID() override;
    std::string getEventInfo() override;
    std::string getEventInfoL2() override;
    std::string getAddition() override;
    std::string getDeviceID() override;
    std::string getPointID() override;
    void saveEvent() override;
    bool initDeviceParam();
    std::string getIsPublishEvent() override;
    void generateEvent() override;
    void processEvents() override;
    bool getCheckPowers() override;
    std::string getPowers() override;

private:
    bool initAppNodeParam();

protected:
    std::unordered_map<std::string, HashParam> m_mapDeviceParam;
    std::unordered_map<std::string, HashParam> m_mapAppnode;
    std::string m_deviceID;
    std::string m_deviceName;
    std::string m_netState;
    std::string m_netAState;
    std::string m_netBState;
    std::string m_netCState;
    std::string m_netDState;
    std::string m_masterState;
    std::string m_datasetID;
    ZG6000::StringList m_listAppNodeID;
    ZG6000::StringList m_listWholeAppNodeID;
    ZG6000::ListStringList m_listListAppNodeID;
};

#endif // ZGMPEVENTPARSEDEVICE_H

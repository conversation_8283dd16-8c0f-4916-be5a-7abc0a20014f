
#include <ZGMPEventParseI.h>
#include "ZGMPEventParseMng.h"

ZG6000::ZGMPEventParseI::ZGMPEventParseI()
{
    ZGMPEventParseMng::instance()->init();
}

bool ZG6000::ZGMPEventParseI::checkState(const Ice::Current &)
{
    return ZGMPEventParseMng::instance()->checkState();
}

void
ZG6000::ZGMPEventParseI::dispatchData(::std::string tableName,
                                      ::std::string oper,
                                      ::std::string reason,
                                      ::std::string time,
                                      ListRecord listRecord,
                                      const Ice::Current&)
{
    ZGMPEventParseMng::instance()->dispatchData(tableName, oper, reason, time, listRecord);
}

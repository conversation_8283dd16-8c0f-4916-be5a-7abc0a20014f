#ifndef ZGMPEVENTPARSESOE_H
#define ZGMPEVENTPARSESOE_H

#include "ZGMPEventParseYx.h"

class ZGMPEventParseSOE : public ZGMPEventParseYx
{
    Q_OBJECT
public:
    explicit ZGMPEventParseSOE(QObject *parent = nullptr);
protected:
    std::string getEventTypeID() override;
    std::string processValueEvent(const HashParam &modelParam, const ZG6000::FieldValue &value) override;
    std::string processValueEventL2(const HashParam &modelParam, const ZG6000::FieldValue &value) override;
};

#endif // ZGMPEVENTPARSESOE_H

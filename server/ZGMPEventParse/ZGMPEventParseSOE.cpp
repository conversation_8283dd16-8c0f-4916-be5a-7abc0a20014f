#include "ZGMPEventParseSOE.h"

#include "ZGMPEventParseMng.h"

ZGMPEventParseSOE::ZGMPEventParseSOE(QObject *parent) : ZGMPEventParseYx(parent)
{

}

std::string ZGMPEventParseSOE::getEventTypeID()
{
    return "ZG_ET_SOE";
}

std::string ZGMPEventParseSOE::processValueEvent(const HashParam &modelParam, const ZG6000::FieldValue &value)
{
    if (value.newValue == "2")
        return ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "soeOn");
    return ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "soeOff");
}

std::string ZGMPEventParseSOE::processValueEventL2(const HashParam& model<PERSON>ara<PERSON>,
    const ZG6000::FieldValue& value)
{
    if (value.newValue == "2")
        return ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "soeOn");
    return ZGUtils::languageString(ZGMPEventParseMng::instance()->secondLanguage(), "soeOff");
}

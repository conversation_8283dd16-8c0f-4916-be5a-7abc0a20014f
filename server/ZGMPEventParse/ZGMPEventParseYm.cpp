#include "ZGMPEventParseYm.h"

ZGMPEventParseYm::ZGMPEventParseYm(QObject *parent)
    : ZGMPEventParseData{parent}
{

}

std::string ZGMPEventParseYm::getDataTableName()
{
    return "mp_param_dataset_ym";
}

ZG6000::StringList ZGMPEventParseYm::getModelFields()
{
    return ZG6000::StringList{"id", "dataCategoryID", "isPublishEvent", "isChangeStore",
        "alarmLevelID", "changeDiff"};
}

bool ZGMPEventParseYm::checkDataValid()
{
    if (!ZGMPEventParseData::checkDataValid())
        return false;
    try
    {
        const auto& model = ZGUtils::get(m_mapModelParam, m_modelID);
        auto pair = m_mapFieldValue.find("rtNewValue");
        if (pair != m_mapFieldValue.end())
        {
            const auto& value = pair.value();
            if (!value.newValue.empty())
            {
                double newValue = std::stod(value.newValue);
                double oldValue = std::stod(value.oldValue);
                double changeDiff = std::stod(ZGUtils::get(model, "changeDiff"));
                if (std::abs(newValue - oldValue) < changeDiff)
                    return false;
            }
        }
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

std::string ZGMPEventParseYm::getEventTypeID()
{
    return "ZG_ET_CHANGE_YM";
}

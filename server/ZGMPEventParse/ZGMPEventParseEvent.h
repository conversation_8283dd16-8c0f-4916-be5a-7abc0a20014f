#ifndef ZGMPEVENTPARSEEVENT_H
#define ZGMPEVENTPARSEEVENT_H

#include "ZGMPEventParseData.h"

class ZGMPEventParseEvent : public ZGMPEventParseData
{
    Q_OBJECT

public:
    explicit ZGMPEventParseEvent(QObject *parent = nullptr);
protected:
    bool parseRecord(const ZG6000::StringMap &record) override;
    std::string getEventTypeID() override;
    std::string getAlarmLevelID() override;
    std::string getPlayTTSTypeID() override;
    std::string getAlarmColor() override;
    bool initOtherParam() override;
    std::string getDataTableName() override;
    ZG6000::StringList getModelFields() override;
    ZG6000::StringList getDataFields() override;
    std::string getEventInfo() override;
    std::string getEventInfoL2() override;
    std::string processValueEvent(const HashParam &modelParam, const ZG6000::FieldValue &value) override;
    std::string processValueEventL2(const HashParam &modelParam, const ZG6000::FieldValue &value) override;
     

private:
    std::unordered_map<std::string, HashParam> m_mapPropertyParam;
    std::string m_propertyValue;
};

#endif // ZGMPEVENTPARSEEVENT_H

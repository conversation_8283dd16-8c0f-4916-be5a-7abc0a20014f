#include "ZGMPEventParseYc.h"

#include "ZGMPEventParseMng.h"
#include "ZGProxyCommon.h"

ZGMPEventParseYc::ZGMPEventParseYc(QObject *parent) : ZGMPEventParseData(parent)
{

}

bool ZGMPEventParseYc::initOtherParam()
{
    std::string sql = "SELECT id, name, nameL2 FROM mp_dict_over_limit_type";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapLimitType))
        return false;
    return true;
}

ZG6000::StringList ZGMPEventParseYc::getModelFields()
{
    return ZG6000::StringList{"id", "dataCategoryID", "stateDataCategoryID", "isPublishEvent", "isChangeStore",
        "alarmLevelID", "changeDiff", "dataUnitID"};
}

std::string ZGMPEventParseYc::getDataTableName()
{
    return "mp_param_dataset_yc";
}

std::string ZGMPEventParseYc::getEventTypeID()
{
    if (m_mapFieldValue.find("rtOverLimitTypeID") != m_mapFieldValue.end())
        return "ZG_ET_YC_OVER_LIMIT";
    else
        return "ZG_ET_CHANGE_YC";
}

bool ZGMPEventParseYc::checkDataValid()
{
    if (!ZGMPEventParseData::checkDataValid())
		return false;
    try
    {
        auto pair = m_mapFieldValue.find("rtNewValue");
        if (pair != m_mapFieldValue.end())
        {
            const auto& value = pair.value();
            if (!value.newValue.empty())
            {
                double newValue = std::stod(value.newValue);
                double oldValue = std::stod(value.oldValue);
                const auto& model = ZGUtils::get(m_mapModelParam, m_modelID);
                double changeDiff = std::stod(ZGUtils::get(model, "changeDiff"));
                if (std::abs(newValue - oldValue) < changeDiff)
                    return false;
            }
        }
		return true;
    }
    catch (const std::exception& e)
    {
		ZGLOG_ERROR(e.what());
		return false;
    }
}

std::string ZGMPEventParseYc::processOverLimitEvent(const HashParam &modelParam, const ZG6000::FieldValue &value)
{
    std::string content;
    if (value.newValue == "ZG_OLT_NORMAL")
        content += " " + ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "overlimitReset");
    else
        content += " " + m_mapLimitType[value.newValue]["name"];
    return content;
}

std::string ZGMPEventParseYc::processOverLimitEventL2(const HashParam& modelParam,
    const ZG6000::FieldValue& value)
{
    std::string content;
    if (value.newValue == "ZG_OLT_NORMAL")
        content += " " + ZGUtils::languageString(ZGMPEventParseMng::instance()->firstLanguage(), "overlimitReset");
    else
        content += " " + m_mapLimitType[value.newValue]["nameL2"];
    return content;
}

#ifndef ZGMPEVENTPARSEDATA_H
#define ZGMPEVENTPARSEDATA_H

#include "ZGMPEventParseBase.h"

class ZGMPEventParseData : public ZGMPEventParseBase
{
    friend class ZGMPEventParseDevice;
    Q_OBJECT
public:
    explicit ZGMPEventParseData(QObject *parent = nullptr);
protected:
    bool initParam() override;
    bool parseRecord(const ZG6000::MapField& record) override;
    bool parseRecord(const ZG6000::StringMap& record) override;
    bool checkDataValid() override;
    std::string getAppNodeID() override;
    std::string getSubsystemID() override;
    std::string getAlarmLevelID() override;
    std::string getPlayTTSTypeID() override;
    std::string getAlarmColor() override;
    std::string getMajorID() override;
    std::string getDataCategoryID() override;
    std::string getEventInfo() override;
    std::string getEventInfoL2() override;
    bool getCheckPowers() override;
    std::string getPowers() override;
    std::string getAddition() override;
    std::string getIsPublishEvent() override;
    std::string getDeviceID() override;
    std::string getPointID() override;
    virtual ZG6000::StringList getModelFields();
    virtual ZG6000::StringList getDataFields();
    virtual bool initOtherParam();
    bool initPointParam();
	bool initModelParam();
    virtual std::string getDataTableName() = 0;
    virtual bool initCategoryParam();
    void afterGenerateEvents() override;
    void generateEvent() override;
    void processEvents() override;
    virtual std::string processValueEvent(const HashParam& modelParam, const ZG6000::FieldValue& value);
    virtual std::string processValueEventL2(const HashParam& modelParam, const ZG6000::FieldValue& value);
    virtual std::string processStateValueEvent(const HashParam& modelParam, const ZG6000::FieldValue& value);
    virtual std::string processStateValueEventL2(const HashParam& modelParam, const ZG6000::FieldValue& value);
    virtual std::string processOverLimitEvent(const HashParam& modelParam, const ZG6000::FieldValue& value);
    virtual std::string processOverLimitEventL2(const HashParam& modelParam, const ZG6000::FieldValue& value);

private:
    void initFieldProcessor();
    void initFieldProcessorL2();
    static bool initAppNodeParam();
    static bool initDatasetParam();
    static bool initDeviceParam();
protected:
    inline static bool m_appNodeParamInited{false};
    inline static std::unordered_map<std::string, HashParam> m_mapAppnode;
    inline static std::unordered_map<std::string, ZG6000::StringList> m_mapDatasetTopics;
    inline static std::unordered_map<std::string, HashParam> m_mapDataset;
    inline static std::unordered_map<std::string, HashParam> m_mapDevice;
    inline static HashParam m_mapDatasetAppNode;
    inline static HashParam m_mapDatasetSubsystem;
    inline static HashParam m_mapDatasetMajor;
    std::unordered_map<std::string, HashParam> m_mapPointParam;
    std::unordered_map<std::string, HashParam> m_mapModelParam;
    std::unordered_map<std::string, HashParam> m_mapPointModel;
    std::unordered_map<std::string, HashParam> m_mapDataCategoryProperty;
    std::unordered_map<std::string, std::function<std::string(const HashParam&, const ZG6000::FieldValue&)>> m_mapFieldProcessor;
    std::unordered_map<std::string, std::function<std::string(const HashParam&, const ZG6000::FieldValue&)>> m_mapFieldProcessorL2;
    std::string m_pointID;
    std::string m_pointName;
    std::string m_datasetID;
    std::string m_deviceID;
    std::string m_modelID;
    QMap<QString, ZG6000::FieldValue> m_mapFieldValue;
    ZG6000::StringList m_listAppNodeID;
};

#endif // ZGMPEVENTPARSEDATA_H

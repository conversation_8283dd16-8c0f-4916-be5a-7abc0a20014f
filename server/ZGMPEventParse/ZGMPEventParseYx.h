#ifndef ZGMPEVENTPARSEYX_H
#define ZGMPEVENTPARSEYX_H

#include "ZGMPEventParseData.h"

class ZGMPEventParseYx : public ZGMPEventParseData
{
    Q_OBJECT

public:
    explicit ZGMPEventParseYx(QObject *parent = nullptr);
protected:
    std::string getEventTypeID() override;
    std::string getDataTableName() override;
    ZG6000::StringList getModelFields() override;
    void generateEvent() override;
};

#endif // ZGMPEVENTPARSEYX_H

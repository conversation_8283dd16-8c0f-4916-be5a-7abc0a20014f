#include "ZGMPEventParseYx.h"

ZGMPEventParseYx::ZGMPEventParseYx(QObject *parent) : ZGMPEventParseData(parent)
{

}

std::string ZGMPEventParseYx::getEventTypeID()
{
    return "ZG_ET_CHANGE_YX";
}

std::string ZGMPEventParseYx::getDataTableName()
{
    return "mp_param_dataset_yx";
}

ZG6000::StringList ZGMPEventParseYx::getModelFields()
{
    auto listFields = ZGMPEventParseData::getModelFields();
    listFields.push_back("isHaveVideo");
    return listFields;
}

void ZGMPEventParseYx::generateEvent()
{
    ZGMPEventParseData::generateEvent();
    try
    {
        const auto & pointParam = ZGUtils::get(m_mapPointParam, m_pointID);
        const auto& dataModelID = ZGUtils::get(pointParam, "dataModelID");
        const auto& modelParam = ZGUtils::get(m_mapModelParam, dataModelID);
        const auto& isHaveVideo = ZGUtils::get(modelParam, "isHaveVideo");
        if (isHaveVideo == "1")
        {
            std::string deviceID, propertyName;
            ZG6000::ErrorInfo e;
            if (!ZGProxyCommon::getPropertyByDataID(m_pointID, deviceID, propertyName, e))
            {
                ZGLOG_ERROR(e);
                return;
            }
            std::string tableID, videoID, videoID2, videos;
            if (!ZGProxyCommon::getDataIDByProperty(deviceID, "video", tableID, videoID, e))
            {
                ZGLOG_ERROR(e);
                return;
            }
            videos = videoID;
            if (ZGProxyCommon::getDataIDByProperty(deviceID, "video1", tableID, videoID2, e))
                videos += "," + videoID2;
            m_currentEvent.insert(std::make_pair("videoID", videos));
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

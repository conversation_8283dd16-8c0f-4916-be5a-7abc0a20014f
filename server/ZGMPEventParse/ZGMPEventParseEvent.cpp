#include "ZGMPEventParseEvent.h"
#include "ZGJson.h"

ZGMPEventParseEvent::ZGMPEventParseEvent(QObject *parent) : ZGMPEventParseData(parent)
{

}

bool ZGMPEventParseEvent::parseRecord(const ZG6000::StringMap &record)
{
    if (!ZGMPEventParseData::parseRecord(record))
        return false;
    try
    {
        m_propertyValue = ZGUtils::get(record, "rtPropertyValue");
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

std::string ZGMPEventParseEvent::getEventTypeID()
{
    return "ZG_ET_CHANGE_EVENT";
}

std::string ZGMPEventParseEvent::getAlarmLevelID()
{
    try
    {
        const auto & model = ZGUtils::get(m_mapModelParam, m_modelID);
        std::string alarmLevelID{ "" };
        auto pair = m_mapFieldValue.find("rtNewValue");
        if (pair != m_mapFieldValue.end())
        {
            const auto& value = pair.value();
            if (value.newValue == "2")
                alarmLevelID = ZGUtils::get(model, "closeAlarmLevelID");
            else
                alarmLevelID = ZGUtils::get(model, "openAlarmLevelID");
        }
        return alarmLevelID;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGMPEventParseEvent::getPlayTTSTypeID()
{
    return "";
}

std::string ZGMPEventParseEvent::getAlarmColor()
{
    return "";
}

bool ZGMPEventParseEvent::initOtherParam()
{
    std::string sql = "SELECT id, name, nameL2 FROM mp_param_model_property";
    if (!initParamToMap(sql, m_mapPropertyParam))
    {
        ZGLOG_ERROR("init property param error.");
        return false;
    }
    return true;
}

std::string ZGMPEventParseEvent::getDataTableName()
{
    return "mp_param_dataset_event";
}

ZG6000::StringList ZGMPEventParseEvent::getModelFields()
{
    return ZG6000::StringList{"id", "dataCategoryID", "isPublishEvent", "isChangeStore",
        "openAlarmLevelID", "closeAlarmLevelID"};
}

ZG6000::StringList ZGMPEventParseEvent::getDataFields()
{
    return ZG6000::StringList{"id", "name", "nameL2", "voice", "datasetID", "dataModelID"};
}

std::string ZGMPEventParseEvent::getEventInfo()
{
    auto pair = m_mapFieldValue.find("rtNewValue");
    if (pair == m_mapFieldValue.end())
        return ZGMPEventParseData::getEventInfo();
    const auto& value = pair.value();
    std::string eventInfo{ "" };
    try
    {
        const auto& point = ZGUtils::get(m_mapPointParam, m_pointID);
        const auto& model = ZGUtils::get(m_mapModelParam, m_modelID);
        std::string pointName = ZGUtils::get(point, "name");
        std::string dataCategoryID = ZGUtils::get(model, "dataCategoryID");
        eventInfo = pointName;
        const auto key = dataCategoryID + "/" + value.newValue;
        const auto& dataCategoryProperty = ZGUtils::get(m_mapDataCategoryProperty, key);
        const auto& propertyName = ZGUtils::get(dataCategoryProperty, "propName");
        eventInfo += ":" + propertyName;
        if (!m_propertyValue.empty())
        {
            ZG6000::ListStringMap listProperty;
            std::string errMsg;
            if (ZGJson::convertFromJson(m_propertyValue, listProperty, errMsg))
            {
                for (const auto& propery: listProperty)
                {
                    const auto& propertyId = ZGUtils::get(propery, "id");
                    auto pairParam = m_mapPropertyParam.find(propertyId);
                    if (pairParam == m_mapPropertyParam.end())
                        continue;
                    const auto& paramName = ZGUtils::get(pairParam->second, "name");
                    const auto& paramValue = ZGUtils::get(propery, "value");
                    eventInfo += " " + paramName + ":" + paramValue;
                }
            }
        }
        ZGLOG_INFO(eventInfo.c_str());
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
    return eventInfo;
}

std::string ZGMPEventParseEvent::getEventInfoL2()
{
    auto pair = m_mapFieldValue.find("rtNewValue");
    if (pair == m_mapFieldValue.end())
        return ZGMPEventParseData::getEventInfo();
    const auto& value = pair.value();
    std::string eventInfo{ "" };
    try
    {
        const auto& point = ZGUtils::get(m_mapPointParam, m_pointID);
        const auto& model = ZGUtils::get(m_mapModelParam, m_modelID);
        std::string pointName = ZGUtils::get(point, "nameL2");
        std::string dataCategoryID = ZGUtils::get(model, "dataCategoryID");
        eventInfo = pointName;
        const auto key = dataCategoryID + "/" + value.newValue;
        const auto& dataCategoryProperty = ZGUtils::get(m_mapDataCategoryProperty, key);
        const auto& propertyName = ZGUtils::get(dataCategoryProperty, "propNameL2");
        eventInfo += ":" + propertyName;
        if (!m_propertyValue.empty())
        {
            ZG6000::ListStringMap listProperty;
            std::string errMsg;
            if (ZGJson::convertFromJson(m_propertyValue, listProperty, errMsg))
            {
                for (const auto& propery: listProperty)
                {
                    const auto& propertyId = ZGUtils::get(propery, "id");
                    auto pairParam = m_mapPropertyParam.find(propertyId);
                    if (pairParam == m_mapPropertyParam.end())
                        continue;
                    const auto& paramName = ZGUtils::get(pairParam->second, "nameL2");
                    const auto& paramValue = ZGUtils::get(propery, "value");
                    eventInfo += " " + paramName + ":" + paramValue;
                }
            }
        }
        ZGLOG_INFO(eventInfo.c_str());
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
    return eventInfo;
}

std::string ZGMPEventParseEvent::processValueEvent(const HashParam &modelParam, const ZG6000::FieldValue &value)
{
    std::string content = value.newValue;
    do
    {
        auto pairDataCategory = modelParam.find("dataCategoryID");
        if (pairDataCategory == modelParam.end())
            break;
        const auto& dataCategoryID = pairDataCategory->second;
        if (dataCategoryID.empty())
            break;
        const auto& key = dataCategoryID + "/" + value.newValue;
        auto pairProperty = m_mapDataCategoryProperty.find(key);
        if (pairProperty == m_mapDataCategoryProperty.end())
            break;
        const auto& dataCategoryProperty = pairProperty->second;
        const auto& propName = ZGUtils::get(dataCategoryProperty, "propName", "");
        content = propName;
        if (!m_propertyValue.empty())
        {
            ZG6000::ListStringMap listProperty;
            std::string errMsg;
            if (ZGJson::convertFromJson(m_propertyValue, listProperty, errMsg))
            {
                for (const auto& propery: listProperty)
                {
                    const auto& propertyId = ZGUtils::get(propery, "id");
                    auto pair = m_mapPropertyParam.find(propertyId);
                    if (pair == m_mapPropertyParam.end())
                        continue;
                    const auto& propertyName = ZGUtils::get(pair->second, "name", "");
                    const auto& propertyValue = ZGUtils::get(propery, "value", "");
                    content += " " + propertyName + ":" + propertyValue;
                }
            }
        }
    }
    while (0);
    return content;
}

std::string ZGMPEventParseEvent::processValueEventL2(const HashParam& modelParam,
    const ZG6000::FieldValue& value)
{
    std::string content = value.newValue;
    do
    {
        auto pairDataCategory = modelParam.find("dataCategoryID");
        if (pairDataCategory == modelParam.end())
            break;
        const auto& dataCategoryID = pairDataCategory->second;
        if (dataCategoryID.empty())
            break;
        const auto& key = dataCategoryID + "/" + value.newValue;
        auto pairProperty = m_mapDataCategoryProperty.find(key);
        if (pairProperty == m_mapDataCategoryProperty.end())
            break;
        const auto& dataCategoryProperty = pairProperty->second;
        const auto& propName = ZGUtils::get(dataCategoryProperty, "propNameL2", "");
        content = propName;
        if (!m_propertyValue.empty())
        {
            ZG6000::ListStringMap listProperty;
            std::string errMsg;
            if (ZGJson::convertFromJson(m_propertyValue, listProperty, errMsg))
            {
                for (const auto& propery: listProperty)
                {
                    const auto& propertyId = ZGUtils::get(propery, "id");
                    auto pair = m_mapPropertyParam.find(propertyId);
                    if (pair == m_mapPropertyParam.end())
                        continue;
                    const auto& propertyName = ZGUtils::get(pair->second, "nameL2", "");
                    const auto& propertyValue = ZGUtils::get(propery, "value", "");
                    content += " " + propertyName + ":" + propertyValue;
                }
            }
        }
    }
    while (0);
    return content;
}

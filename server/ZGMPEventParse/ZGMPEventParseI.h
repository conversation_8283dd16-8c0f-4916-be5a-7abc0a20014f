#ifndef __ZGMPEventParseI_h__
#define __ZGMPEventParseI_h__

#include <ZGMPEventParse.h>

namespace ZG6000
{

class ZGMPEventParseI : public virtual ZGMPEventParse
{
public:
    ZGMPEventParseI();

    bool checkState(const Ice::Current&) override;

    void dispatchData(::std::string,
                              ::std::string,
                              ::std::string,
                              ::std::string,
                              ListRecord,
                              const Ice::Current&) override;
};

}

#endif

#include "ZGMPEventParseBase.h"
#include "ZGHeartMng.h"
#include "ZGRuntime.h"
#include "ZGJson.h"
#include "ZGProxyCommon.h"

std::unordered_map<std::string, HashParam> ZGMPEventParseBase::m_mapAlarmLevel{};
std::unordered_map<std::string, HashParam> ZGMPEventParseBase::m_mapEventType{};
std::unordered_map<std::string, HashParam> ZGMPEventParseBase::m_mapMajor{};
std::unordered_map<std::string, HashParam> ZGMPEventParseBase::m_mapSubsystem{};
std::unordered_map<std::string, HashParam> ZGMPEventParseBase::m_mapDataCategory{};

ZGMPEventParseBase::ZGMPEventParseBase(QObject *parent) : QObject(parent)
{

}

void ZGMPEventParseBase::dispatchEvent(const std::string& time,
	const ::ZG6000::ListRecord& listRecord)
{
    std::unique_lock locker(m_mutex);
    generateEvents(time, listRecord);
    processEvents();
}

void ZGMPEventParseBase::dispatchEvent(const ZG6000::ListStringMap& listRecord)
{
    std::unique_lock locker(m_mutex);
	generateEvents(listRecord);
    processEvents();
}

bool ZGMPEventParseBase::initFixedParam()
{
	try
	{
        std::string sql = "SELECT id, name, nameL2 from sp_dict_event_type";
        if (!initParamToMap(sql, m_mapEventType))
        {
            ZGLOG_ERROR("init event type error.");
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM sp_param_major";
        if (!initParamToMap(sql, m_mapMajor))
        {
            ZGLOG_ERROR("init major error.");
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM sp_param_subsystem";
        if (!initParamToMap(sql, m_mapSubsystem))
        {
            ZGLOG_ERROR("init subsystem error.");
            return false;
        }
        sql = "SELECT id, name, nameL2, colorName from mp_dict_data_category";
        if (!initParamToMap(sql, m_mapDataCategory))
        {
            ZGLOG_ERROR("init data category error.");
            return false;
        }
        sql = "SELECT id, name, nameL2, isPlayFile, isPlayTTS, playCount from sp_dict_alarm_level";
        if (!initParamToMap(sql, m_mapAlarmLevel))
        {
            ZGLOG_ERROR("init alarm level error.");
            return false;
        }
        return true;
	}
	catch (const std::exception& e)
	{
        ZGLOG_ERROR(e.what());
        return false;
	}
}

bool ZGMPEventParseBase::initialize()
{
    if (!initFixedParam()) 
        return false;
    return true;
}

std::string ZGMPEventParseBase::getNameByID(const std::unordered_map<std::string, HashParam>& mapParam,
    const std::string& id)
{
    if (id.empty())
        return "";
    auto pair = mapParam.find(id);
    if (pair == mapParam.end())
        return "";
    return ZGUtils::get(pair->second, "name", "");
}

std::string ZGMPEventParseBase::getNameL2ByID(const std::unordered_map<std::string, HashParam>& mapParam,
    const std::string& id)
{
    if (id.empty())
        return "";
    auto pair = mapParam.find(id);
    if (pair == mapParam.end())
        return "";
    return ZGUtils::get(pair->second, "nameL2", "");
}

inline void convertMapToUnorderedMap(ZG6000::StringMap& mapParam, HashParam& unorderedMapParam)
{
    for (auto& param : mapParam)
    {
        unorderedMapParam.insert(param);
    }
}

bool ZGMPEventParseBase::initParamToMap(const std::string& sql, std::unordered_map<std::string, HashParam>& mapParam)
{
    ZG6000::ListStringMap listRecord;
    ZGLOG_TRACE(sql.c_str());
    if (!ZGProxyCommon::execQuerySql(sql, listRecord))
        return false;
    for (auto& record : listRecord)
    {
        try
        {
            const auto& id = ZGUtils::get(record, "id");
            HashParam param;
            convertMapToUnorderedMap(record, param);
            mapParam.insert(std::make_pair(id, param));
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
    return true;
}

void ZGMPEventParseBase::generateEvents(const std::string& time, const ::ZG6000::ListRecord& listRecord)
{
    m_eventTime = time;
    for (const auto& record : listRecord)
    {
        if (!parseRecord(record))
            continue;
        if (!checkDataValid())
            continue;
        generateEvent();
        saveEvent();
    }
    afterGenerateEvents();
}

void ZGMPEventParseBase::generateEvents(const ZG6000::ListStringMap& listRecords)
{
    for (const auto& record : listRecords)
    {
        if (!parseRecord(record))
            continue;
        if (!checkDataValid())
            continue;
        generateEvent();
        saveEvent();
    }
    afterGenerateEvents();
}

std::string ZGMPEventParseBase::getEventTime()
{
    return m_eventTime;
}

void ZGMPEventParseBase::generateEvent()
{
    m_currentEvent.insert(std::make_pair("eventTime", getEventTime()));
    const auto & eventTypeId = getEventTypeID();
    m_currentEvent.insert(std::make_pair("eventTypeID", eventTypeId));
    m_currentEvent.insert(std::make_pair("eventTypeName", getNameByID(m_mapEventType, eventTypeId)));
    m_currentEvent.insert(std::make_pair("eventTypeNameL2", getNameL2ByID(m_mapEventType, eventTypeId)));
    const auto& majorId = getMajorID();
    m_currentEvent.insert(std::make_pair("majorID", majorId));
    const auto& subsystemId = getSubsystemID();
    m_currentEvent.insert(std::make_pair("subsystemID", subsystemId));
    m_currentEvent.insert(std::make_pair("subsystemName", getNameByID(m_mapSubsystem, subsystemId)));
    m_currentEvent.insert(std::make_pair("subsystemNameL2", getNameL2ByID(m_mapSubsystem, subsystemId)));
    m_currentEvent.insert(std::make_pair("majorName", getNameByID(m_mapMajor, majorId)));
    m_currentEvent.insert(std::make_pair("majorNameL2", getNameL2ByID(m_mapMajor, majorId)));
    bool checkPowers = getCheckPowers();
    if (checkPowers)
    {
        const auto& powers = getPowers();
        m_currentEvent.insert(std::make_pair("eventPowers", powers));
    }
    const auto& deviceID = getDeviceID();
    m_currentEvent.insert(std::make_pair("deviceID", deviceID));
    const auto& dataID = getPointID();
    m_currentEvent.insert(std::make_pair("dataID", dataID));
    const auto & alarmLevelId = getAlarmLevelID();
    m_currentEvent.insert(std::make_pair("alarmLevelID", alarmLevelId));
    m_currentEvent.insert(std::make_pair("alarmLevelName", getNameByID(m_mapAlarmLevel, alarmLevelId)));
    m_currentEvent.insert(std::make_pair("alarmLevelNameL2", getNameL2ByID(m_mapAlarmLevel, alarmLevelId)));
    const auto& playTTSTypeID = getPlayTTSTypeID();
    if (playTTSTypeID == "ZG_PTT_NO_PLAY" || playTTSTypeID == "ZG_PTT_PLAY")
        m_currentEvent.insert(std::make_pair("playTTSTypeID", playTTSTypeID));
    const auto& alarmColor = getAlarmColor();
    if (!alarmColor.empty())
        m_currentEvent.insert(std::make_pair("alarmColor", alarmColor));
    const auto & dataCategoryId = getDataCategoryID();
    m_currentEvent.insert(std::make_pair("dataCategoryID", dataCategoryId));
    m_currentEvent.insert(std::make_pair("dataCategoryName", getNameByID(m_mapDataCategory, dataCategoryId)));
    m_currentEvent.insert(std::make_pair("dataCategoryNameL2", getNameL2ByID(m_mapDataCategory, dataCategoryId)));
    m_currentEvent.insert(std::make_pair("eventInfo", getEventInfo()));
    m_currentEvent.insert(std::make_pair("eventInfoL2", getEventInfoL2()));
    m_currentEvent.insert(std::make_pair("addition", getAddition()));
    m_isPublishEvent = getIsPublishEvent();
}

void ZGMPEventParseBase::saveEvent()
{
    m_listEvent.emplace_back(std::move(m_currentEvent));
    m_listIsPublishEvent.emplace_back(std::move(m_isPublishEvent));
    m_currentEvent.clear();
}

void ZGMPEventParseBase::debugEvent()
{
    for (const auto& pair : m_currentEvent)
    {
        qDebug() << pair.first.c_str() << ": " << pair.second.c_str();
    }
}


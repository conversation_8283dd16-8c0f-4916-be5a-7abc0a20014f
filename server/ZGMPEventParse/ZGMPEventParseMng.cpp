#include "ZGMPEventParseMng.h"

#include "ZGRuntime.h"
#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGUtils.h"
#include "redis/ZGRedisClient.h"
#include "ZGMPEventParseBase.h"
#include "ZGMPEventParseBt.h"
#include "ZGMPEventParseYc.h"
#include "ZGMPEventParseYx.h"
#include "ZGMPEventParseText.h"
#include "ZGMPEventParseYm.h"
#include "ZGMPEventParseEvent.h"
#include "ZGMPEventParseSOE.h"
#include "ZGMPEventParseDevice.h"

#include <QCoreApplication>
#include <QRandomGenerator>
#include <QThread>

static ZGMPEventParseMng* g_pEventParse = nullptr;

ZGMPEventParseMng *ZGMPEventParseMng::instance()
{
    if (g_pEventParse == nullptr)
        g_pEventParse = new ZGMPEventParseMng;
    return g_pEventParse;
}

void ZGMPEventParseMng::init()
{
    initEvents();
    initServerInstConfig();
    initEventParseObject();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    QThread::sleep(QRandomGenerator::global()->bounded(1, 5));
    while (!ZGMPEventParseBase::initialize())
    {
        ZGLOG_ERROR("ZGMPEventParseBase::initialize error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initParseParam())
    {
        ZGLOG_ERROR("initParseParam error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initRedisClient())
    {
        ZGLOG_ERROR("initRedisClient error.");
        QThread::msleep(m_initInterval * 1000);
    }
    QThread::msleep(1000);
    while (!initRedisRtTopic())
    {
        ZGLOG_ERROR("initRedisRtTopic error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initLanguage())
    {
        ZGLOG_ERROR("initLanguage error.");
        QThread::msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    ZGLOG_INFO("ZGMPEventParse init finished.");
    m_dateTimeStart = QDateTime::currentDateTime();
    m_checkTimer.start(m_checkInterval * 1000);
}

bool ZGMPEventParseMng::checkState()
{
    return m_initialized;
}

void ZGMPEventParseMng::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, const ZG6000::ListRecord &listRecord)
{
    if (!m_initialized)
        return;
    QDateTime dt = QDateTime::currentDateTime();
    if (m_dateTimeStart.secsTo(dt) < 90)
        return;
    if (oper != "update")
        return;
    if (reason != "change")
        return;
    try
    {
        auto parser = ZGUtils::get(m_mapEventParse, tableName, nullptr);
        if (parser)
            parser->dispatchEvent(time, listRecord);
    }
    catch (const std::exception& e)
    {
        std::string errMsg = tableName + ": " + e.what();
        ZGLOG_ERROR(errMsg.c_str());
    }
}

ZGMPEventParseMng::ZGMPEventParseMng(QObject *parent) : QObject(parent)
{

}

void ZGMPEventParseMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGMPEventParseMng::onCheckStatus);
}

void ZGMPEventParseMng::initServerInstConfig()
{
    const auto & serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

void ZGMPEventParseMng::initEventParseObject()
{
    m_mapEventParse.insert(std::make_pair("mp_param_dataset_bt", new ZGMPEventParseBt));
    m_mapEventParse.insert(std::make_pair("mp_param_dataset_yc", new ZGMPEventParseYc));
    m_mapEventParse.insert(std::make_pair("mp_param_dataset_yx", new ZGMPEventParseYx));
    m_mapEventParse.insert(std::make_pair("mp_param_dataset_text", new ZGMPEventParseText));  
    m_mapEventParse.insert(std::make_pair("mp_param_dataset_ym", new ZGMPEventParseYm));
    m_mapEventParse.insert(std::make_pair("mp_param_device", new ZGMPEventParseDevice));
    m_mapEventParse.insert(std::make_pair("ZG_T_SOE", new ZGMPEventParseSOE));
    m_mapEventParse.insert(std::make_pair("ZG_T_EVENT", new ZGMPEventParseEvent));
}

bool ZGMPEventParseMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGMPEventParseMng::initParseParam()
{
    for (const auto& pairEventParse : m_mapEventParse)
    {
        if (!pairEventParse.second->initParam())
        {
            ZGLOG_ERROR(QString("%1 initParam error.").arg(pairEventParse.first.c_str()));
            return false;
        }
    }
    return true;
}

bool ZGMPEventParseMng::initRedisClient()
{
    QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
    listClientType << ZGRuntime::REDIS_RT_QUEUE << ZGRuntime::REDIS_RT_TOPIC;
    if (!ZGRuntime::instance()->initRedisClient(listClientType))
    {
        ZGLOG_ERROR("initRedisClient error.");
        return false;
    }
    m_pRedisRtQueue = ZGRuntime::instance()->getRedisClientRTQueue();
    if (m_pRedisRtQueue == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTQueue error.");
        return false;
    }
    m_pRedisRtTopic = ZGRuntime::instance()->getRedisClientRTTopic();
    if (m_pRedisRtTopic == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTTopic error.");
        return false;
    }
    return true;
}

bool ZGMPEventParseMng::initRedisRtTopic()
{
    if (!m_pRedisRtTopic->connected(true))
    {
        ZGLOG_ERROR("Redis RT topic is not connected.");
        return false;
    }
    connect(m_pRedisRtTopic, &ZGRedisClient::receivedMessage, this, &ZGMPEventParseMng::onReceivedMessage);
    ZG6000::StringList listTopic;
    listTopic.push_back("ZG_T_SOE");
    listTopic.push_back("ZG_T_EVENT");
    m_pRedisRtTopic->subscribe(listTopic);
    m_pRedisRtTopic->consume();
    return true;
}

bool ZGMPEventParseMng::initLanguage()
{
    QString sql = QString("SELECT firstLanguageID, secondLanguageID FROM sp_param_system");
    ZG6000::StringMap mapLanguage;
    if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), mapLanguage))
    {
        ZGLOG_ERROR("获取系统语言参数失败");
        return false;
    }
    m_firstLanguage = mapLanguage["firstLanguageID"];
    if (m_firstLanguage.empty())
        m_firstLanguage = "ZG_DL_CN";
    m_secondLanguage = mapLanguage["secondLanguageID"];
    if (!ZGUtils::initLanguage(m_serverName))
    {
        ZGLOG_ERROR(QStringLiteral("初始化语言配置失败"));
        return false;
    }
    ZGLOG_INFO(QString("first language: %1, second language: %2").arg(m_firstLanguage.c_str()).arg(m_secondLanguage.c_str()));
    return true;
}

void ZGMPEventParseMng::onCheckStatus()
{

}

void ZGMPEventParseMng::onReceivedMessage(const QString &topic, const QString &message)
{   
    ZG6000::ListStringMap listRecord;
    std::string errMsg;
    if (!ZGJson::convertFromJson(message.toStdString(), listRecord, errMsg))
        return;
    m_mapEventParse.at(topic.toStdString())->dispatchEvent(listRecord);
}

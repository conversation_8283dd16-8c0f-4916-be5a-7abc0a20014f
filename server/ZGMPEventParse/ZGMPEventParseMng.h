#ifndef ZGMPEVENTPARSEMNG_H
#define ZGMPEVENTPARSEMNG_H

#include <QObject>
#include <QThread>
#include <QTimer>
#include "ZGDebugMng.h"
#include "ZGProxyMng.h"

class ZGRedisClient;
class ZGMqttClient;
class ZGMPEventParseBase;

class ZGMPEventParseMng : public QObject
{
    Q_OBJECT

public:
    static ZGMPEventParseMng* instance();
    void init();
    bool checkState();
    void dispatchData(::std::string tableName,
                      ::std::string oper,
                      ::std::string reason,
                      ::std::string time,
                      const ::ZG6000::ListRecord& listRecord);

    [[nodiscard]] std::string firstLanguage() const
    {
        return m_firstLanguage;
    }

    [[nodiscard]] std::string secondLanguage() const
    {
        return m_secondLanguage;
    }

private slots:
    void onCheckStatus();
    void onReceivedMessage(const QString& topic,
                           const QString& message);

private:
    explicit ZGMPEventParseMng(QObject* parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    void initEventParseObject();
    bool initServerInstInfo();
    bool initParseParam();
    bool initRedisClient();
    bool initRedisRtTopic();
    bool initLanguage();

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    int m_initInterval{10};
    int m_checkInterval{5};
    int m_commandTimeout{5};
    QTimer m_checkTimer;
    int m_currentStep{0};
    std::unordered_map<std::string, ZGMPEventParseBase*> m_mapEventParse;
    ZGRedisClient* m_pRedisRtQueue{nullptr};
    ZGRedisClient* m_pRedisRtTopic{nullptr};
    QDateTime m_dateTimeStart;
    std::string m_firstLanguage;
    std::string m_secondLanguage;
};

#endif // ZGMPEVENTPARSEMNG_H

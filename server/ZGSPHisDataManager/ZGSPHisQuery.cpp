#include "ZGSPHisQuery.h"

#include "ZGProxyCommon.h"

ZGSPHisQuery::ZGSPHisQuery(QObject* parent)
    : ZGQueryBase{parent}
{
}

bool ZGSPHisQuery::initialize()
{
	std::string sql = "SELECT id, name FROM sp_param_node";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapNode))
	{
		ZGLOG_ERROR("initialize node error.");
		return false;
	}
	sql = "SELECT id, name FROM sp_param_node_server";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapServer))
	{
		ZGLOG_ERROR("initialize node error.");
		return false;
	}
	sql = "SELECT id, name FROM sp_param_node_service";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapService))
	{
		ZGLOG_ERROR("initialize service error.");
		return false;
	}
	sql = "SELECT id, name FROM sp_param_node_service_instance";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapServiceInst))
	{
		ZGLOG_ERROR("initialize service instance error.");
		return false;
	}
	sql = "SELECT id, name FROM sp_param_appnode";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapAppNode))
	{
		ZGLOG_ERROR("initialize appNode error.");
		return false;
	}
	sql = "SELECT id, name FROM sp_param_client";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapClient))
	{
		ZGLOG_ERROR("initialize client error.");
		return false;
	}
	sql = "SELECT id, name FROM mp_dict_auth_pos";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapAuthPos))
	{
		ZGLOG_ERROR("initialize authPos error.");
		return false;
	}
	sql = "SELECT id, name FROM sp_dict_event_type";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapEventType))
	{
		ZGLOG_ERROR("initialize eventType error.");
		return false;
	}
	return true;
}

bool ZGSPHisQuery::extendNode(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertDataName(listTitle, listValues, m_mapNode))
		return false;
	if (!extendFieldDesc(listTitle, listValues, [&](const std::string& fieldName, std::string& fieldValue)
	{
		if (fieldName == "fieldName")
		{
			if (fieldValue == "rtANetState")
				fieldValue = u8"A网状态";
			if (fieldValue == "rtBNetState")
				fieldValue = u8"B网状态";
		}
		if ((fieldName == "rtANetState") || (fieldName == "rtBNetState"))
		{
			if (fieldValue == "2")
				fieldValue = u8"正常";
			else
				fieldValue = u8"中断";
		}
	}))
		return false;
	return true;
}

bool ZGSPHisQuery::extendServer(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertDataName(listTitle, listValues, m_mapServer))
		return false;
	if (!extendFieldDesc(listTitle, listValues, [&](const std::string& fieldName, std::string& fieldValue)
	{
		if (fieldName == "fieldName")
		{
			if (fieldValue == "rtMasterState")
				fieldValue = u8"主备状态";
			if (fieldName == "rtANetState")
				fieldValue = u8"A网状态";
			if (fieldName == "rtBNetState")
				fieldValue = u8"B网状态";
		}
		if ((fieldName == "rtANetState") || (fieldName == "rtBNetState"))
		{
			if (fieldValue == "2")
				fieldValue = u8"正常";
			else
				fieldValue = u8"中断";
		}
		if (fieldName == "rtMasterState")
		{
			if (fieldValue == "2")
				fieldValue = u8"主服务器";
			else
				fieldValue = u8"备服务器";
		}
	}))
		return false;
	return true;
}

bool ZGSPHisQuery::extendService(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertDataName(listTitle, listValues, m_mapService))
		return false;
	if (!extendFieldDesc(listTitle, listValues, [&](const std::string& fieldName, std::string& fieldValue)
	{
		if (fieldName == "fieldName")
		{
			if (fieldValue == "rtState")
				fieldValue = u8"服务状态";
		}
		if (fieldName == "rtState")
		{
			if (fieldValue == "2")
				fieldValue = u8"正常";
			else
				fieldValue = u8"中断";
		}
	}))
		return false;
	return true;
}

bool ZGSPHisQuery::extendServiceInst(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertDataName(listTitle, listValues, m_mapServiceInst))
		return false;
	if (!extendFieldDesc(listTitle, listValues, [&](const std::string& fieldName, std::string& fieldValue)
	{
		if (fieldName == "fieldName")
		{
			if (fieldValue == "rtState")
				fieldValue = u8"服务实例状态";
			if (fieldValue == "rtMasterState")
				fieldValue = u8"主备状态";
			if (fieldValue == "rtRunState")
				fieldValue = u8"运行状态";
		}
		if (fieldName == "rtState")
		{
			if (fieldValue == "2")
				fieldValue = u8"正常";
			else
				fieldValue = u8"中断";
		}
		if (fieldName == "rtMasterState")
		{
			if (fieldValue == "2")
				fieldValue = u8"主服务实例";
			else
				fieldValue = u8"备服务实例";
		}
		if (fieldName == "rtRunState")
		{
			if (fieldValue == "2")
				fieldValue = u8"运行";
			else
				fieldValue = u8"停止";
		}
	}))
		return false;
	return true;
}

bool ZGSPHisQuery::extendAppNode(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertDataName(listTitle, listValues, m_mapAppNode))
		return false;
	if (!extendFieldDesc(listTitle, listValues, [&](const std::string& fieldName, std::string& fieldValue)
	{
		if (fieldName == "fieldName")
		{
			if (fieldValue == "rtGroundLock")
				fieldValue = u8"接地闭锁";
			if (fieldValue == "rtYkBlock")
				fieldValue = u8"遥控屏蔽";
			if (fieldValue == "rtRepairBlock")
				fieldValue = u8"检修屏蔽";
			if (fieldValue == "rtForbid")
				fieldValue = u8"禁止操作";
			if (fieldValue == "rtAuthPosID")
				fieldValue = u8"授权位置";
		}
		if (fieldName == "rtGroundLock")
		{
			if (fieldValue == "1")
				fieldValue = u8"闭锁";
			else
				fieldValue = u8"解锁";
		}
		if (fieldName == "rtYkBlock" || fieldName == "rtRepairBlock")
		{
			if (fieldValue == "1")
				fieldValue = u8"屏蔽";
			else
				fieldValue = u8"未屏蔽";
		}
		if (fieldName == "rtForbid")
		{
			if (fieldValue == "1")
				fieldValue = u8"禁止";
			else
				fieldValue = u8"允许";
		}
		if (fieldName == "rtAuthPosID")
		{
			const auto& pair = m_mapAuthPos.find(fieldValue);
			if (pair != m_mapAuthPos.end())
				fieldValue = pair->second;
		}
	}))
		return false;
	return true;
}

bool ZGSPHisQuery::extendClient(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertDataName(listTitle, listValues, m_mapClient))
		return false;
	if (!extendFieldDesc(listTitle, listValues, [&](const std::string& fieldName, std::string& fieldValue)
	{
		if (fieldName == "fieldName")
		{
			if (fieldValue == "rtIsActivate")
				fieldValue = u8"激活状态";
			if (fieldValue == "rtState")
				fieldValue = u8"客户端状态";
			if (fieldValue == "rtLoginUserID")
				fieldValue = u8"登录用户";
		}
		if (fieldName == "rtIsActivate")
		{
			if (fieldValue == "1")
				fieldValue = u8"已激活";
			else
				fieldValue = u8"未激活";
		}
		if (fieldName == "rtState")
		{
			if (fieldValue == "2")
				fieldValue = u8"正常";
			else
				fieldValue = u8"中断";
		}
		if (fieldName == "rtLoginUserID")
		{
			std::string userName;
			if (ZGProxyCommon::getDataByField("sp_param_hrm_user", fieldValue, "name", userName))
				fieldValue = userName;
		}
	}))
		return false;
	return true;
}

bool ZGSPHisQuery::extendEvent(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	int isConfirmIndex = findIndexByName(listTitle, "isConfirm");
	if (isConfirmIndex == -1)
		return false;
	for (auto& values : listValues)
	{
		values[isConfirmIndex] = (values[isConfirmIndex] == "1") ? u8"是" : u8"否";
	}
	return true;
}

bool ZGSPHisQuery::extendLog(ZG6000::StringList& listTitle,
	ZG6000::ListStringList& listValues)
{
	int nodeIndex = findIndexByName(listTitle, "nodeID");
	if (nodeIndex == -1)
		return false;
	int eventTypeIndex = findIndexByName(listTitle, "eventType");
	if (eventTypeIndex == -1)
		return false;
	listTitle.push_back("nodeName");
	listTitle.push_back("eventTypeName");
	for (auto& values : listValues)
	{
		const auto& nodeID = values[nodeIndex];
		auto pair = m_mapNode.find(nodeID);
		values.push_back((pair != m_mapNode.end()) ? pair->second : "");
		const auto& eventTypeID = values[eventTypeIndex];
		pair = m_mapEventType.find(eventTypeID);
		values.push_back((pair != m_mapEventType.end()) ? pair->second : "");
	}
	return true;
}

#ifndef ZGDPHISQUERY_H
#define ZGDPHISQUERY_H

#include <set>

#include "ZGQueryBase.h"

class ZGDPHisQuery : public ZGQueryBase
{
    Q_OBJECT
public:
    explicit ZGDPHisQuery(QObject *parent = nullptr);
    bool initialize();
    bool extendDevice(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendDeviceResume(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);

private:
    void initForeignField();
    void initDevField();

private:
    ZG6000::StringMap m_mapDevField;
    ZG6000::StringMap m_mapDevState;
    ZG6000::StringMap m_mapModelProperty;
    ZG6000::StringMap m_mapForeignField;
};

#endif // ZGDPHISQUERY_H

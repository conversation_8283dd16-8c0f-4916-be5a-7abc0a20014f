#ifndef ZGOPHISQUERY_H
#define ZGOPHISQUERY_H

#include "ZGQueryBase.h"

class ZGOPHisQuery : public ZGQueryBase
{
    Q_OBJECT
public:
    explicit ZGOPHisQuery(QObject *parent = nullptr);
    bool initialize();

private:
    ZG6000::StringMap m_mapAppNode;
    ZG6000::StringMap m_mapSubsystem;
    ZG6000::StringMap m_mapMajor;
    ZG6000::StringMap m_mapTaskStage;
    ZG6000::StringMap m_mapTaskState;
};

#endif // ZGOPHISQUERY_H

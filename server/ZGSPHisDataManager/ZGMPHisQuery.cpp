#include "ZGMPHisQuery.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"

ZGMPHisQuery::ZGMPHisQuery(QObject *parent)
    : ZGQueryBase{parent}
{

}

bool ZGMPHisQuery::initialize()
{
	std::string sql = "SELECT id, name from sp_param_appnode";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapAppNode))
	{
		ZGLOG_ERROR("initialize appnode error.");
		return false;
	}
    sql = "SELECT id, name FROM sp_param_major";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapMajor))
	{
		ZGLOG_ERROR("initialize major error.");
		return false;
	}
    sql = "SELECT id, name FROM sp_param_subsystem";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapSubsystem))
	{
		ZGLOG_ERROR("initialize subsystem error.");
		return false;
	}
    sql = "SELECT id, name FROM mp_param_dataset";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDataset))
    {
        ZGLOG_ERROR("initialize dataset error.");
        return false;
    }
    sql = "SELECT id, name FROM mp_param_device";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDevice))
    {
        ZGLOG_ERROR("initialize device error.");
        return false;
    }
	sql = "SELECT id, name FROM mp_dict_store_type";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapStoreType))
	{
		ZGLOG_ERROR("initialize storeType error.");
		return false;
	}
	sql = "SELECT id, name FROM mp_dict_over_limit_type";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapLimitType))
	{
		ZGLOG_ERROR("initialize limitType error.");
		return false;
	}
	sql = "SELECT id, name FROM sp_param_statistic";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapStatistic))
	{
		ZGLOG_ERROR("initialize statistic error.");
		return false;
	}
	sql = "SELECT * FROM mp_param_model_yx";
	if (!ZGProxyCommon::execQuerySql(sql, m_mapModel))
	{
		ZGLOG_ERROR("initialize yx model error.");
		return false;
	}
	sql = "SELECT * FROM mp_param_model_yc";
	ZG6000::MapStringMap mapModelYc;
	if (!ZGProxyCommon::execQuerySql(sql, mapModelYc))
	{
		ZGLOG_ERROR("initialize yc model error.");
		return false;
	}
	m_mapModel.insert(mapModelYc.begin(), mapModelYc.end());
	sql = "SELECT * FROM mp_param_model_text";
	ZG6000::MapStringMap mapModelText;
	if (!ZGProxyCommon::execQuerySql(sql, mapModelText))
	{
		ZGLOG_ERROR("initialize text model error.");
		return false;
	}
	m_mapModel.insert(mapModelText.begin(), mapModelText.end());
    sql = "SELECT * FROM mp_param_model_ym";
    ZG6000::MapStringMap mapModelYm;
    if (!ZGProxyCommon::execQuerySql(sql, mapModelYm))
    {
        ZGLOG_ERROR("initialize ym model error.");
        return false;
    }
    m_mapModel.insert(mapModelYm.begin(), mapModelYm.end());
	sql = "SELECT * FROM mp_param_data_category_property";
	ZG6000::ListStringMap listRecord;
	if (!ZGProxyCommon::execQuerySql(sql, listRecord))
	{
		ZGLOG_ERROR("initialize category property error.");
		return false;
	}
	try
	{
		for (const auto& record : listRecord)
		{
			const auto& dataCategoryID = ZGUtils::get(record, "dataCategoryID");
			const auto& propValue = ZGUtils::get(record, "propValue");
			const auto& propName = ZGUtils::get(record, "propName");
			m_mapCategoryProperty.insert({ dataCategoryID + propValue, propName });
		}
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
	return true;
}

bool ZGMPHisQuery::extendDatasetYx(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertAppnodeExtend(listTitle, listValues))
		return false;
	if (!insertDatasetExtend(listTitle, listValues))
		return false;
    if (!insertDeviceExtend(listTitle, listValues))
        return false;
	if (!insertDataFieldExtend("mp_param_dataset_yx", listTitle, listValues))
		return false;
	return true;
}

bool ZGMPHisQuery::extendDatasetYc(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertAppnodeExtend(listTitle, listValues))
		return false;
	if (!insertDatasetExtend(listTitle, listValues))
		return false;
    if (!insertDeviceExtend(listTitle, listValues))
        return false;
	if (!insertDataFieldExtend("mp_param_dataset_yc", listTitle, listValues))
		return false;
	return true;
}

bool ZGMPHisQuery::extendDatasetText(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertAppnodeExtend(listTitle, listValues))
		return false;
	if (!insertDatasetExtend(listTitle, listValues))
		return false;
    if (!insertDeviceExtend(listTitle, listValues))
        return false;
	if (!insertDataFieldExtend("mp_param_dataset_text", listTitle, listValues))
		return false;
    return true;
}

bool ZGMPHisQuery::extendDatasetYm(ZG6000::StringList &listTitle, ZG6000::ListStringList &listValues)
{
    if (!insertAppnodeExtend(listTitle, listValues))
        return false;
    if (!insertDatasetExtend(listTitle, listValues))
        return false;
    if (!insertDeviceExtend(listTitle, listValues))
        return false;
    if (!insertDataFieldExtend("mp_param_dataset_ym", listTitle, listValues))
        return false;
    return true;
}

bool ZGMPHisQuery::extendStoreYx(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertAppnodeExtend(listTitle, listValues))
		return false;
	if (!insertDatasetExtend(listTitle, listValues))
		return false;
    if (!insertDeviceExtend(listTitle, listValues))
        return false;
    if (!insertDataExtend("mp_param_dataset_yx", listTitle, listValues))
		return false;
	if (!insertStoreTypeExtend(listTitle, listValues))
		return false;
	return true;
}

bool ZGMPHisQuery::extendStoreYc(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertAppnodeExtend(listTitle, listValues))
		return false;
	if (!insertDatasetExtend(listTitle, listValues))
		return false;
    if (!insertDeviceExtend(listTitle, listValues))
        return false;
    if (!insertDataExtend("mp_param_dataset_yc", listTitle, listValues))
		return false;
	if (!insertStoreTypeExtend(listTitle, listValues))
		return false;
	return true;
}

bool ZGMPHisQuery::extendStoreText(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertAppnodeExtend(listTitle, listValues))
		return false;
	if (!insertDatasetExtend(listTitle, listValues))
		return false;
    if (!insertDeviceExtend(listTitle, listValues))
        return false;
	if (!insertDataExtend("mp_param_dataset_text", listTitle, listValues))
		return false;
	if (!insertStoreTypeExtend(listTitle, listValues))
		return false;
    return true;
}

bool ZGMPHisQuery::extendStoreYm(ZG6000::StringList &listTitle, ZG6000::ListStringList &listValues)
{
    if (!insertAppnodeExtend(listTitle, listValues))
        return false;
    if (!insertDatasetExtend(listTitle, listValues))
        return false;
    if (!insertDeviceExtend(listTitle, listValues))
        return false;
    if (!insertDataExtend("mp_param_dataset_ym", listTitle, listValues))
        return false;
    if (!insertStoreTypeExtend(listTitle, listValues))
        return false;
    return true;
}

bool ZGMPHisQuery::extendStatisticData(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	int statisticIndex = findIndexByName(listTitle, "statisticID");
	listTitle.push_back("statisticName");
	for (auto& values : listValues)
	{
		const auto& statisticID = values[statisticIndex];
		auto pair = m_mapStatistic.find(statisticID);
		values.push_back((pair != m_mapStatistic.end()) ? pair->second : "");
	}
	return true;
}

bool ZGMPHisQuery::extendYcLimit(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertAppnodeExtend(listTitle, listValues))
		return false;
	if (!insertDatasetExtend(listTitle, listValues))
		return false;
	if (!insertDataExtend("mp_param_dataset_yc", listTitle, listValues))
		return false;
	int oldOverLimitTypeIndex = findIndexByName(listTitle, "rtOldOverLimitTypeID");
	if (oldOverLimitTypeIndex == -1)
		return false;
	int newOverLimitTypeIndex = findIndexByName(listTitle, "rtNewOverLimitTypeID");
	if (newOverLimitTypeIndex == -1)
		return false;
	listTitle.push_back("rtOldOverLimitTypeName");
	listTitle.push_back("rtNewOverLimitTypeName");
	for (size_t i = 0; i < listValues.size(); ++i)
	{
		const auto& oldTypeLimitType = listValues[i][oldOverLimitTypeIndex];
		auto pair = m_mapLimitType.find(oldTypeLimitType);
		listValues[i].push_back((pair != m_mapLimitType.end()) ? pair->second : "");
		const auto& newTypeLimitType = listValues[i][newOverLimitTypeIndex];
		pair = m_mapLimitType.find(newTypeLimitType);
		listValues[i].push_back((pair != m_mapLimitType.end()) ? pair->second : "");
	}
	return true;
}

bool ZGMPHisQuery::extendCtrlError(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	if (!insertAppnodeExtend(listTitle, listValues))
		return false;
	int tableNameIndex = findIndexByName(listTitle, "tableName");
	if (tableNameIndex == -1)
		return false;
	int dataIndex = findIndexByName(listTitle, "dataID");
	if (dataIndex == -1)
		return false;
	int deviceIndex = findIndexByName(listTitle, "deviceID");
	if (deviceIndex == -1)
		return false;
	ZG6000::StringMap mapData, mapDevice;
	listTitle.push_back("dataName");
	listTitle.push_back("deviceName");
	for (auto& values : listValues)
	{
		const auto& tableName = values[tableNameIndex];
		const auto& dataId = values[dataIndex];
		auto pair = mapData.find(dataId);
		if (pair == mapData.end())
		{
			std::string dataName;
			ZGProxyCommon::getDataByField(tableName, dataId, "name", dataName);
			mapData[dataId] = dataName;
			values.push_back(dataName);
		}
		else
		{
			values.push_back(pair->second);
		}
		const auto& deviceId = values[deviceIndex];
		pair = mapDevice.find(deviceId);
		if (pair == mapDevice.end())
		{
			std::string deviceName;
			ZGProxyCommon::getDataByField("mp_param_device", deviceId, "name", deviceName);
			mapDevice[deviceId] = deviceName;
			values.push_back(deviceName);
		}
		else
		{
			values.push_back(pair->second);
		}
	}
	return true;
}

bool ZGMPHisQuery::insertAppnodeExtend(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	int appNodeIndex = findIndexByName(listTitle, "appNodeID");
	if (appNodeIndex == -1)
		return false;
	int subsystemIndex = findIndexByName(listTitle, "subsystemID");
	if (subsystemIndex == -1)
		return false;
	int majorIndex = findIndexByName(listTitle, "majorID");
	if (majorIndex == -1)
		return false;
	listTitle.push_back("appNodeName");
	listTitle.push_back("subsystemName");
	listTitle.push_back("majorName");
	for (auto& values : listValues)
	{
		const auto& appNodeID = values[appNodeIndex];
		auto pair = m_mapAppNode.find(appNodeID);
		values.push_back((pair != m_mapAppNode.end()) ? pair->second : "");
		const auto& subsystemID = values[subsystemIndex];
		pair = m_mapSubsystem.find(subsystemID);
		values.push_back((pair != m_mapSubsystem.end()) ? pair->second : "");
		const auto& majorID = values[majorIndex];
		pair = m_mapMajor.find(majorID);
		values.push_back((pair != m_mapMajor.end()) ? pair->second : "");
	}
	return true;
}

bool ZGMPHisQuery::insertDatasetExtend(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	int datasetIndex = findIndexByName(listTitle, "datasetID");
	if (datasetIndex == -1)
		return false;
	listTitle.push_back("datasetName");
	for (auto& values: listValues)
	{
		const auto& datasetID = values[datasetIndex];
		auto pair = m_mapDataset.find(datasetID);
		values.push_back((pair != m_mapDataset.end()) ? pair->second : "");
	}
    return true;
}

bool ZGMPHisQuery::insertDeviceExtend(ZG6000::StringList &listTitle, ZG6000::ListStringList &listValues)
{
    int deviceIndex = findIndexByName(listTitle, "deviceID");
    if (deviceIndex == -1)
        return false;
    listTitle.push_back("deviceName");
    for (auto& values: listValues)
    {
        const auto& deviceID = values[deviceIndex];
        auto pair = m_mapDevice.find(deviceID);
        values.push_back((pair != m_mapDevice.end()) ? pair->second : "");
    }
    return true;
}

bool ZGMPHisQuery::insertDataExtend(const std::string& tableName, ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	int dataIndex = findIndexByName(listTitle, "dataID");
	if (dataIndex == -1)
		return false;
	ZG6000::StringList listDataID;
	for (const auto& values : listValues)
	{
		listDataID.push_back(values[dataIndex]);
	}
	ZG6000::StringList listDataName;
	if (!ZGProxyCommon::mgetDataByField(tableName, listDataID, "name", listDataName))
	{
		ZGLOG_ERROR(QStringLiteral("获取'%1'参数失败").arg(tableName.c_str()));
		return false;
	}
	listTitle.push_back("dataName");
	for (size_t i = 0; i < listValues.size(); ++i)
	{
		listValues[i].push_back(listDataName[i]);
	}
	return true;
}

bool ZGMPHisQuery::insertDataFieldExtend(const std::string& tableName, ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	int dataIndex = findIndexByName(listTitle, "dataID");
	if (dataIndex == -1)
		return false;
	int fieldNameIndex = findIndexByName(listTitle, "fieldName");
	if (fieldNameIndex == -1)
		return false;
	int oldValueIndex = findIndexByName(listTitle, "oldValue");
	if (oldValueIndex == -1)
		return false;
	int newValueIndex = findIndexByName(listTitle, "newValue");
	if (newValueIndex == -1)
		return false;
	ZG6000::StringList listDataID;
	for (const auto & values : listValues)
	{
		listDataID.push_back(values[dataIndex]);
	}
	ZG6000::ListStringMap listRecord;
	if (!ZGProxyCommon::mgetDataByFields(tableName, listDataID, {"name", "dataModelID"}, listRecord))
	{
		ZGLOG_ERROR(QStringLiteral("获取'%1'参数失败").arg(tableName.c_str()));
		return false;
	}
	listTitle.push_back("dataName");
	for (size_t i = 0; i < listValues.size(); ++i)
	{
		if (listValues[i][fieldNameIndex] == "rtNewValue")
		{
			const auto& dataModelID = listRecord[i]["dataModelID"];
			const auto& oldValue = listValues[i][oldValueIndex];
			const auto& newValue = listValues[i][newValueIndex];
			ZG6000::MapStringMap::iterator it = m_mapModel.find(dataModelID);
			if (it != m_mapModel.end())
			{
				const auto& pair = it->second.find("dataCategoryID");
				if ((pair != it->second.end()) && (!pair->second.empty()))
				{
					std::string oldPropValue = pair->second + oldValue;
					std::string newPropValue = pair->second + newValue;
					auto itProp = m_mapCategoryProperty.find(oldPropValue);
					if (itProp != m_mapCategoryProperty.end())
                        listValues[i][oldValueIndex] = itProp->second + "(" + oldValue + ")";
					itProp = m_mapCategoryProperty.find(newPropValue);
					if (itProp != m_mapCategoryProperty.end())
                        listValues[i][newValueIndex] = itProp->second + "(" + newValue + ")";
				}
			}
			listValues[i][fieldNameIndex] = u8"数据值";
		}
		listValues[i].push_back(listRecord[i]["name"]);
	}
	return true;
}

bool ZGMPHisQuery::insertStoreTypeExtend(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	int storeTypeIndex = findIndexByName(listTitle, "storeTypeID");
	if (storeTypeIndex == -1)
	{
		ZGLOG_ERROR("Can't find storeType");
		return false;
	}
	listTitle.push_back("storeTypeName");
	for (auto& values : listValues)
	{
		const auto& storeTypeID = values[storeTypeIndex];
		auto pair = m_mapStoreType.find(storeTypeID);
		values.push_back((pair != m_mapStoreType.end()) ? pair->second : "");
	}
	return true;
}

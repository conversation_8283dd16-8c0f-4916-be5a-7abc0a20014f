#include "ZGDPHisQuery.h"
#include "ZGProxyCommon.h"

ZGDPHisQuery::ZGDPHisQuery(QObject *parent)
    : ZGQueryBase{parent}
{

}

bool ZGDPHisQuery::initialize()
{
	initDevField();
	initForeignField();
	std::string sql = "SELECT CONCAT(modelID, '/', name) AS id, description FROM dp_param_device_model_property";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapModelProperty))
	{
		ZGLOG_ERROR(QStringLiteral("获取模型属性失败"));
		return false;
	}
	sql = "SELECT id, name FROM dp_dict_device_state";
	if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDevState))
	{
		ZGLOG_ERROR(QStringLiteral("获取设备状态失败"));
		return false;
	}
	return true;
}

bool ZGDPHisQuery::extendDevice(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	int deviceIdIndex = findIndexByName(listTitle, "deviceID");
	if (deviceIdIndex == -1)
		return false;
	int propertyNameIndex = findIndexByName(listTitle, "propertyName");
	if (propertyNameIndex == -1)
		return false;
	int oldValueIndex = findIndexByName(listTitle, "oldValue");
	if (oldValueIndex == -1)
		return false;
	int newValueIndex = findIndexByName(listTitle, "newValue");
	if (newValueIndex == -1)
		return false;
	listTitle.push_back("deviceName");
	listTitle.push_back("propertyDesc");
	ZG6000::MapStringMap mapDevField, mapForeignField;
	for (auto& values : listValues)
	{
		const auto& deviceId = values[deviceIdIndex];
		auto fieldPair = mapDevField.find(deviceId);
		ZG6000::StringMap deviceField;
		if (fieldPair == mapDevField.end())
		{
			ZGProxyCommon::getDataByFields("dp_param_device", deviceId, { "name", "modelID" }, deviceField);
			mapDevField[deviceId] = deviceField;
		}
		else
			deviceField = fieldPair->second;
		values.push_back(deviceField["name"]);
		const auto& propertyName = values[propertyNameIndex];
		auto pair = m_mapDevField.find(propertyName);
		if (pair != m_mapDevField.end())
			values.push_back((pair != m_mapDevField.end()) ? pair->second : "");
		else
		{
			std::string modelAttribute = deviceField["modelID"] + "/" + propertyName;
			auto propPair = m_mapModelProperty.find(modelAttribute);
			values.push_back((propPair != m_mapModelProperty.end()) ? propPair->second : "");
		}
		pair = m_mapForeignField.find(propertyName);
		if (pair != m_mapForeignField.end())
		{
			auto& oldValue = values[oldValueIndex];
			auto& newValue = values[newValueIndex];
			auto foreignPair = mapForeignField.find(propertyName);
			std::string oldValueName, newValueName;
			if (foreignPair == mapForeignField.end())
			{
				ZG6000::StringMap foreign;
				ZGProxyCommon::getDataByField(pair->second, oldValue, "name", oldValueName);
				foreign.insert(std::make_pair(oldValue, oldValueName));
				if (newValue != oldValue)
				{
					ZGProxyCommon::getDataByField(pair->second, newValue, "name", newValueName);
					foreign.insert(std::make_pair(newValue, oldValueName));
				}
			}
			else
			{
				auto it = foreignPair->second.find(oldValue);
				if (it == foreignPair->second.end())
				{
					ZGProxyCommon::getDataByField(pair->second, oldValue, "name", oldValueName);
					foreignPair->second.insert(std::make_pair(oldValue, oldValueName));
				}
				else
					oldValueName = it->second;
				it = foreignPair->second.find(newValue);
				if (it == foreignPair->second.end())
				{
					ZGProxyCommon::getDataByField(pair->second, newValue, "name", newValueName);
					foreignPair->second.insert(std::make_pair(newValue, newValueName));
				}
				else
					newValueName = it->second;
			}
			oldValue = oldValueName;
			newValue = newValueName;
		}
	}
	return true;
}

bool ZGDPHisQuery::extendDeviceResume(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues)
{
	int deviceIdIndex = findIndexByName(listTitle, "deviceID");
	if (deviceIdIndex == -1)
		return false;
	int oldStateIndex = findIndexByName(listTitle, "oldStateID");
	if (oldStateIndex == -1)
		return false;
	int newStateIndex = findIndexByName(listTitle, "newStateID");
	if (newStateIndex == -1)
		return false;
	listTitle.push_back("deviceName");
	listTitle.push_back("oldStateName");
	listTitle.push_back("newStateName");
	ZG6000::StringMap mapDevName;
	for (auto& values: listValues)
	{
		const auto& deviceId = values[deviceIdIndex];
		auto pair = mapDevName.find(deviceId);
		std::string deviceName;
		if (pair == mapDevName.end())
		{
			ZGProxyCommon::getDataByField("dp_param_device", deviceId, "name", deviceName);
			mapDevName[deviceId] = deviceName;
		}
		else
			deviceName = pair->second;
		values.push_back(deviceName);
		const auto& oldStateId = values[oldStateIndex];
		pair = m_mapDevState.find(oldStateId);
		values.push_back((pair != m_mapDevState.end()) ? pair->second : "");
		const auto& newStateId = values[newStateIndex];
		pair = m_mapDevState.find(newStateId);
		values.push_back((pair != m_mapDevState.end()) ? pair->second : "");
	}
	return true;
}

void ZGDPHisQuery::initForeignField()
{
	m_mapForeignField.insert(std::make_pair("typeID", "dp_dict_device_type"));
	m_mapForeignField.insert(std::make_pair("modelID", "dp_param_device_model"));
	m_mapForeignField.insert(std::make_pair("productModelID", "dp_dict_product_model"));
	m_mapForeignField.insert(std::make_pair("manufactorID", "dp_dict_device_manufactory"));
	m_mapForeignField.insert(std::make_pair("organID", "sp_param_hrm_organ"));
	m_mapForeignField.insert(std::make_pair("rtStateID", "dp_dict_device_state"));
	m_mapForeignField.insert(std::make_pair("rtHealthRuleID", "sp_param_rule"));
}

void ZGDPHisQuery::initDevField()
{
	m_mapDevField.insert(std::make_pair("name", u8"设备名称"));
	m_mapDevField.insert(std::make_pair("typeID", u8"设备类型"));
	m_mapDevField.insert(std::make_pair("modelID", u8"模型"));
	m_mapDevField.insert(std::make_pair("productModelID", u8"型号"));
	m_mapDevField.insert(std::make_pair("manufactorID", u8"制造商"));
	m_mapDevField.insert(std::make_pair("organID", u8"部门"));
	m_mapDevField.insert(std::make_pair("productDate", u8"生产日期"));
	m_mapDevField.insert(std::make_pair("unitID", u8"单位"));
	m_mapDevField.insert(std::make_pair("rtStateID", u8"设备状态"));
	m_mapDevField.insert(std::make_pair("rtCoordinates", u8"坐标位置"));
	m_mapDevField.insert(std::make_pair("rtHealthRuleID", u8"健康规则"));
}

#include "ZGSPHisDataManagerMng.h"

#include <QRandomGenerator>
#include <QThread>

#include "ZGDebugMng.h"
#include "ZGMPHisQuery.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGSPHisQuery.h"
#include "ZGDPHisQuery.h"
#include "ZGOPHisQuery.h"
#include "ZGUtils.h"
#include "zgerror/ZGSPHisDataManagerError.h"

static ZGSPHisDataManagerMng* g_pInstance = nullptr;

ZGSPHisDataManagerMng* ZGSPHisDataManagerMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGSPHisDataManagerMng;
    return g_pInstance;
}

void ZGSPHisDataManagerMng::init()
{
    initEvents();
    initServerInstConfig();
    initHandle();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    QThread::sleep(QRandomGenerator::global()->bounded(5, 10));
    while (!m_pZGMPQuery->initialize())
    {
        ZGLOG_ERROR("m_pZGMPQuery initialize error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!m_pZGSPQuery->initialize())
    {
        ZGLOG_ERROR("m_pZGSPQuery initialize error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!m_pZGDPQuery->initialize())
    {
        ZGLOG_ERROR("m_pZGDPQuery initialize error.");
        QThread::msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    ZGLOG_INFO("ZGSPHisDataManager init finished.");
    m_checkTimer.start(m_checkInterval * 1000);
}

bool ZGSPHisDataManagerMng::checkState()
{
    return m_initialized;
}

bool ZGSPHisDataManagerMng::queryTableData(const std::string& tableName,
                                           const std::string& condition,
                                           int offset,
                                           int limit,
                                           const std::string& orderField,
                                           const std::string& orderType,
                                           ZG6000::StringList& listTitle,
                                           ZG6000::ListStringList& listValues,
                                           ZG6000::ErrorInfo& e)
{
    const auto& hisProxy = ZGProxyMng::instance()->getProxySPDBDataHis();
    if (hisProxy == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPHisDataManager::ZG_ERR_INTERNAL);
        e.errDetail = u8"获取历史数据服务代理对象失败";
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        if (!hisProxy->getTableFieldNameToList(tableName, listTitle, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql;
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
            sql = QString("SELECT * FROM %1 WHERE %2 ORDER BY %3 %4 LIMIT %5, %6").arg(tableName.c_str())
                .arg(condition.c_str())
                .arg(orderField.c_str())
                .arg(orderType.c_str())
                .arg(offset)
                .arg(limit);
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
            sql = QString("SELECT * FROM %1 WHERE %2 ORDER BY %3 %4 OFFSET %5 ROWS FETCH NEXT %6 ROWS ONLY").arg(
                    tableName.c_str())
                .arg(condition.c_str())
                .arg(orderField.c_str())
                .arg(orderType.c_str())
                .arg(offset)
                .arg(limit);
        ZGLOG_TRACE(sql);
        if (!hisProxy->execQuerySqlToListList(sql.toStdString(), listValues, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        auto it = m_mapFunc.find(tableName);
        if (it != m_mapFunc.end())
            it->second(listTitle, listValues);
        else
        {
            it = m_mapFunc.begin();
            while (it != m_mapFunc.end())
            {
                if (tableName.find(it->first) != std::string::npos)
                    it->second(listTitle, listValues);
                ++it;
            }
        }
        ZGLOG_TRACE(QString("end query, tableName: %1").arg(tableName.c_str()));
        return true;
    }
    catch (const Ice::Exception& ie)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPHisDataManager::ZG_ERR_INTERNAL);
        e.errDetail = ie.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPHisDataManagerMng::queryTableCount(const std::string& tableName,
                                            const std::string& condition,
                                            int& count,
                                            ZG6000::ErrorInfo& e)
{
    const auto& hisProxy = ZGProxyMng::instance()->getProxySPDBDataHis();
    if (hisProxy == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPHisDataManager::ZG_ERR_INTERNAL);
        e.errDetail = u8"获取历史数据服务代理对象失败";
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        QString sql = QString("SELECT COUNT(id) FROM %1 WHERE %2").arg(tableName.c_str()).arg(condition.c_str());
        std::string result;
        if (!hisProxy->execQuerySqlFieldToValue(sql.toStdString(), result, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        count = std::atoi(result.c_str());
        return true;
    }
    catch (const Ice::Exception& ie)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPHisDataManager::ZG_ERR_INTERNAL);
        e.errDetail = ie.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPHisDataManagerMng::queryStoreYc(const ZG6000::StringList& listDevice,
                                         const ZG6000::StringList& listProperty,
                                         const std::string& startTime,
                                         const std::string& endTime,
                                         ZG6000::ListStringMap& listResult,
                                         ZG6000::ErrorInfo& e)
{
    return queryStore("mp_his_dataset_store_yc", listDevice, listProperty, startTime, endTime, listResult, e);
}

bool ZGSPHisDataManagerMng::queryStoreYx(const ZG6000::StringList& listDevice,
                                         const ZG6000::StringList& listProperty,
                                         const std::string& startTime,
                                         const std::string& endTime,
                                         ZG6000::ListStringMap& listResult,
                                         ZG6000::ErrorInfo& e)
{
    return queryStore("mp_his_dataset_store_yx", listDevice, listProperty, startTime, endTime, listResult, e);
}

bool ZGSPHisDataManagerMng::queryStoreText(const ZG6000::StringList& listDevice,
                                           const ZG6000::StringList& listProperty,
                                           const std::string& startTime,
                                           const std::string& endTime,
                                           ZG6000::ListStringMap& listResult,
                                           ZG6000::ErrorInfo& e)
{
    return queryStore("mp_his_dataset_store_text", listDevice, listProperty, startTime, endTime, listResult, e);
}

bool ZGSPHisDataManagerMng::queryStoreYm(const ZG6000::StringList& listDevice,
                                         const ZG6000::StringList& listProperty,
                                         const std::string& startTime,
                                         const std::string& endTime,
                                         ZG6000::ListStringMap& listResult,
                                         ZG6000::ErrorInfo& e)
{
    return queryStore("mp_his_dataset_store_ym", listDevice, listProperty, startTime, endTime, listResult, e);
}

ZGSPHisDataManagerMng::ZGSPHisDataManagerMng(QObject* parent)
    : QObject{parent},
      m_pZGMPQuery(new ZGMPHisQuery(this)),
      m_pZGSPQuery(new ZGSPHisQuery(this)),
      m_pZGDPQuery(new ZGDPHisQuery(this))
{
}

void ZGSPHisDataManagerMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPHisDataManagerMng::onCheckStatus);
}

void ZGSPHisDataManagerMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGSPHisDataManagerMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

void ZGSPHisDataManagerMng::initHandle()
{
    m_mapFunc.insert({
        "mp_his_dataset_yx", [this](ZG6000::StringList& listTitle,
                                    ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendDatasetYx(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "mp_his_dataset_yc", [this](ZG6000::StringList& listTitle,
                                    ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendDatasetYc(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "mp_his_dataset_text", [this](ZG6000::StringList& listTitle,
                                      ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendDatasetText(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "mp_his_dataset_ym", [this](ZG6000::StringList& listTitle,
                                    ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendDatasetYm(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "mp_his_dataset_store_yx", [this](ZG6000::StringList& listTitle,
                                          ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendStoreYx(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "mp_his_dataset_store_yc", [this](ZG6000::StringList& listTitle,
                                          ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendStoreYc(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "mp_his_dataset_store_text", [this](ZG6000::StringList& listTitle,
                                            ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendStoreText(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "mp_his_statistic_yc", [this](ZG6000::StringList& listTitle,
                                      ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendStatisticData(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "mp_his_statistic_yx", [this](ZG6000::StringList& listTitle,
                                      ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendStatisticData(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "mp_his_yc_limit", [this](ZG6000::StringList& listTitle,
                                  ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendYcLimit(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "mp_his_ctrl_error", [this](ZG6000::StringList& listTitle,
                                    ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGMPQuery->extendCtrlError(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "op_his_ot", [this](ZG6000::StringList& listTitle,
                            ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGSPQuery->extendEvent(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "sp_his_event", [this](ZG6000::StringList& listTitle,
                               ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGSPQuery->extendEvent(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "sp_his_node", [this](ZG6000::StringList& listTitle,
                              ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGSPQuery->extendNode(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "sp_his_node_server", [this](ZG6000::StringList& listTitle,
                                     ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGSPQuery->extendServer(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "sp_his_node_service", [this](ZG6000::StringList& listTitle,
                                      ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGSPQuery->extendService(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "sp_his_node_service_instance", [this](ZG6000::StringList& listTitle,
                                               ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGSPQuery->extendServiceInst(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "sp_his_appnode", [this](ZG6000::StringList& listTitle,
                                 ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGSPQuery->extendAppNode(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "sp_his_client", [this](ZG6000::StringList& listTitle,
                                ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGSPQuery->extendClient(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "sp_his_log", [this](ZG6000::StringList& listTitle,
                             ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGSPQuery->extendLog(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "dp_his_device", [this](ZG6000::StringList& listTitle,
                                ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGDPQuery->extendDevice(listTitle, listValues);
        }
    });
    m_mapFunc.insert({
        "dp_his_device_resume", [this](ZG6000::StringList& listTitle,
                                       ZG6000::ListStringList& listValues)-> bool
        {
            return m_pZGDPQuery->extendDeviceResume(listTitle, listValues);
        }
    });
}

bool ZGSPHisDataManagerMng::queryStore(const std::string& tableName,
                                       const ZG6000::StringList& listDevice,
                                       const ZG6000::StringList& listProperty,
                                       const std::string& startTime,
                                       const std::string& endTime,
                                       ZG6000::ListStringMap& listResult,
                                       ZG6000::ErrorInfo& e)
{
    std::string devices;
    for (const auto& device : listDevice)
    {
        devices += "'" + device + "',";
    }
    if (!devices.empty())
        devices.pop_back();
    std::string properties;
    for (const auto& property : listProperty)
    {
        properties += "'" + property + "',";
    }
    if (!properties.empty())
        properties.pop_back();
    QDateTime dt;
    ZGUtils::StringToDateTime(startTime.c_str(), dt, true);
    std::string newTableName = tableName + "_" + std::to_string(dt.date().year());
    QString sql = QString("SELECT * FROM %1 WHERE deviceID IN (%2) "
                      "AND propertyName IN (%3) AND rtStoreTime > '%4' AND rtStoreTime < '%5'")
                  .arg(newTableName.c_str()).arg(devices.c_str()).arg(properties.c_str())
                  .arg(startTime.c_str()).arg(endTime.c_str());
    ZGLOG_TRACE(sql);
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listResult, true))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPHisDataManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("查询历史数据失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZGLOG_TRACE("query store end");
    return true;
}

void ZGSPHisDataManagerMng::onCheckStatus()
{
}

#ifndef ZGQUERYBASE_H
#define ZGQUERYBASE_H

#include <QObject>
#include <ZGServerCommon.h>

class ZGQueryBase : public QObject
{
    Q_OBJECT
public:
    explicit ZGQueryBase(QObject *parent = nullptr);
    static int findIndexByName(const ZG6000::StringList& listValue, const std::string& value);
    static bool insertDataName(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues,
                        const ZG6000::StringMap& mapData);
    static bool extendFieldDesc(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues,
                         std::function<void(const std::string&, std::string&)> func);
};

#endif // ZGQUERYBASE_H

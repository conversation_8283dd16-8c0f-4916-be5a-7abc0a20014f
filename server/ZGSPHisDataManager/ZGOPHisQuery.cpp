#include "ZGOPHisQuery.h"
#include "ZGProxyCommon.h"
#include "ZGDebugMng.h"

ZGOPHisQuery::ZGOPHisQuery(QObject *parent)
    : ZGQueryBase{parent}
{

}

bool ZGOPHisQuery::initialize()
{
    std::string sql = "SELECT id, name from sp_param_appnode";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapAppNode))
    {
        ZGLOG_ERROR("initialize appnode error.");
        return false;
    }
    sql = "SELECT id, name FROM sp_param_major";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapMajor))
    {
        ZGLOG_ERROR("initialize major error.");
        return false;
    }
    sql = "SELECT id, name FROM sp_param_subsystem";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapSubsystem))
    {
        ZGLOG_ERROR("initialize subsystem error.");
        return false;
    }
    sql = "SELECT id, name FROM op_dict_task_stage";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTaskStage))
    {
        ZGLOG_ERROR(QStringLiteral("初始化任务阶段参数失败"));
        return false;
    }
    sql = "SELECT id, name FROM op_dict_task_state";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTaskState))
    {
        ZGLOG_ERROR(QStringLiteral("初始化任务状态参数失败"));
        return false;
    }
    return true;
}

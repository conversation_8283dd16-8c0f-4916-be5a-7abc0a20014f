#ifndef ZGSPHISQUERY_H
#define ZGSPHISQUERY_H

#include <QObject>
#include "ZGServerCommon.h"
#include "ZGQueryBase.h"

class ZGSPHisQuery : public ZGQueryBase
{
	Q_OBJECT
public:
	explicit ZGSPHisQuery(QObject* parent = nullptr);
	bool initialize();
	bool extendNode(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
	bool extendServer(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
	bool extendService(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
	bool extendServiceInst(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
	bool extendAppNode(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
	bool extendClient(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
	bool extendEvent(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
	bool extendLog(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);

private:
	ZG6000::StringMap m_mapNode;
	ZG6000::StringMap m_mapServer;
	ZG6000::StringMap m_mapService;
	ZG6000::StringMap m_mapServiceInst;
	ZG6000::StringMap m_mapAppNode;
	ZG6000::StringMap m_mapClient;
	ZG6000::StringMap m_mapEventType;
	ZG6000::StringMap m_mapAuthPos;
};

#endif // ZGSPHISQUERY_H

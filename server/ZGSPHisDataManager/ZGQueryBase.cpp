#include "ZGQueryBase.h"
#include "ZGDebugMng.h"

ZGQueryBase::ZGQueryBase(QObject *parent)
    : QObject{parent}
{

}

int ZGQueryBase::findIndexByName(const ZG6000::StringList &listValue, const std::string &value)
{
    int distance = -1;
    auto it = std::find(listValue.begin(), listValue.end(), value);
    if (it != listValue.end())
        distance = static_cast<int>(std::distance(listValue.begin(), it));
    if (distance == -1)
        ZGLOG_ERROR(QStringLiteral("Can't find '%1'").arg(value.c_str()));
    return distance;
}

bool ZGQueryBase::insertDataName(ZG6000::StringList &listTitle, ZG6000::ListStringList &listValues, const ZG6000::StringMap &mapData)
{
    int dataIndex = findIndexByName(listTitle, "dataID");
    if (dataIndex == -1)
    {
        ZGLOG_ERROR("Can't find data.");
        return false;
    }
    listTitle.push_back("dataName");
    for (auto& values : listValues)
    {
        const auto& dataID = values[dataIndex];
        auto pair = mapData.find(dataID);
        values.push_back((pair != mapData.end()) ? pair->second : "");
    }
    return true;
}

bool ZGQueryBase::extendFieldDesc(ZG6000::StringList &listTitle, ZG6000::ListStringList &listValues, std::function<void (const std::string &, std::string &)> func)
{
    int fieldNameIndex = findIndexByName(listTitle, "fieldName");
    if (fieldNameIndex == -1)
        return false;
    int oldValueIndex = findIndexByName(listTitle, "oldValue");
    if (oldValueIndex == -1)
        return false;
    int newValueIndex = findIndexByName(listTitle, "newValue");
    if (newValueIndex == -1)
        return false;
    for (auto& values : listValues)
    {
        const auto& fieldName = values[fieldNameIndex];
        func(fieldName, values[oldValueIndex]);
        func(fieldName, values[newValueIndex]);
        func("fieldName", values[fieldNameIndex]);
    }
    return true;
}

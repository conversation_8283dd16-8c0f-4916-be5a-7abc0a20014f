#ifndef ZGMPHISQUERY_H
#define ZGMPHISQUERY_H

#include <QObject>
#include <ZGServerCommon.h>
#include "ZGQueryBase.h"

class ZGMPHisQuery : public ZGQueryBase
{
    Q_OBJECT
public:
    explicit ZGMPHisQuery(QObject *parent = nullptr);
    bool initialize();
    bool extendDatasetYx(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendDatasetYc(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendDatasetText(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendDatasetYm(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendStoreYx(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendStoreYc(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendStoreText(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendStoreYm(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendStatisticData(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendYcLimit(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool extendCtrlError(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);

private:
    bool insertAppnodeExtend(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool insertDatasetExtend(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool insertDeviceExtend(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool insertDataExtend(const std::string& tableName, ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool insertDataFieldExtend(const std::string& tableName, ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);
    bool insertStoreTypeExtend(ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues);

private:
    ZG6000::StringMap m_mapAppNode;
    ZG6000::StringMap m_mapSubsystem;
    ZG6000::StringMap m_mapMajor;
    ZG6000::StringMap m_mapDataset;
    ZG6000::StringMap m_mapDevice;
    ZG6000::StringMap m_mapStoreType;
    ZG6000::MapStringMap m_mapModel;
    ZG6000::StringMap m_mapCategoryProperty;
    ZG6000::StringMap m_mapLimitType;
    ZG6000::StringMap m_mapStatistic;
};

#endif // ZGMPHISQUERY_H

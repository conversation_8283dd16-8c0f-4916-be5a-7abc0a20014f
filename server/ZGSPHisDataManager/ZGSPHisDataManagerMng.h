#ifndef ZGSPHISDATAMANAGERMNG_H
#define ZGSPHISDATAMANAGERMNG_H

#include <QTimer>
#include <ZGServerCommon.h>

class ZGMPHisQuery;
class ZGSPHisQuery;
class ZGDPHisQuery;
class ZGOPHisQuery;
class ZGSPHisDataManagerMng : public QObject
{
	Q_OBJECT

public:
	static ZGSPHisDataManagerMng* instance();
	void init();
	bool checkState();
	bool queryTableData(const std::string& tableName, const std::string& condition, int offset, int limit, const std::string& orderField, const std::string& orderType,
	                    ZG6000::StringList& listTitle, ZG6000::ListStringList& listValues, ZG6000::ErrorInfo& e);
	bool queryTableCount(const std::string& tableName, const std::string& condition, int& count, ZG6000::ErrorInfo& e);
	bool queryStoreYc(const ZG6000::StringList& listDevice, const ZG6000::StringList& listProperty, const std::string& startTime, const std::string& endTime,
		ZG6000::ListStringMap& listResult, ZG6000::ErrorInfo& e);
	bool queryStoreYx(const ZG6000::StringList& listDevice, const ZG6000::StringList& listProperty, const std::string& startTime, const std::string& endTime,
		ZG6000::ListStringMap& listResult, ZG6000::ErrorInfo& e);
	bool queryStoreText(const ZG6000::StringList& listDevice, const ZG6000::StringList& listProperty, const std::string& startTime, const std::string& endTime,
		ZG6000::ListStringMap& listResult, ZG6000::ErrorInfo& e);
	bool queryStoreYm(const ZG6000::StringList& listDevice, const ZG6000::StringList& listProperty, const std::string& startTime, const std::string& endTime,
		ZG6000::ListStringMap& listResult, ZG6000::ErrorInfo& e);

private:
	explicit ZGSPHisDataManagerMng(QObject* parent = nullptr);
	void initEvents();
	void initServerInstConfig();
	bool initServerInstInfo();
	void initHandle();
    bool initialParam();
	bool queryStore(const std::string& tableName, const ZG6000::StringList& listDevice, const ZG6000::StringList& listProperty, const std::string& startTime, const std::string& endTime,
		ZG6000::ListStringMap& listResult, ZG6000::ErrorInfo& e);

private slots:
	void onCheckStatus();

private:
	bool m_initialized{false};
	QString m_serverName{""};
	QString m_instName{""};
	int m_initInterval{10};
	int m_checkInterval{10};
	QTimer m_checkTimer;
	ZGMPHisQuery* m_pZGMPQuery{nullptr};
	ZGSPHisQuery* m_pZGSPQuery{nullptr};
	ZGDPHisQuery* m_pZGDPQuery{nullptr};
    ZGOPHisQuery* mp_ZGOPQuery{nullptr};
	using ExtendedFunc = std::function<bool(ZG6000::StringList&, ZG6000::ListStringList&)>;
	std::unordered_map<std::string, ExtendedFunc> m_mapFunc;
};

#endif // ZGSPHISDATAMANAGERMNG_H

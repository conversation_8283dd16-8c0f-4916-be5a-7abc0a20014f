#include "ZGSPHisDataManagerI.h"
#include "ZGSPHisDataManagerMng.h"

namespace ZG6000 {

ZGSPHisDataManagerI::ZGSPHisDataManagerI()
{
    ZGSPHisDataManagerMng::instance()->init();
}

bool ZGSPHisDataManagerI::checkState(const Ice::Current &current)
{
    return ZGSPHisDataManagerMng::instance()->checkState();
}

bool ZGSPHisDataManagerI::queryTableData(std::string tableName, std::string condition, int offset, int limit, std::string orderField, std::string orderType, StringList& listTitle, ListStringList& listValues, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSPHisDataManagerMng::instance()->queryTableData(tableName, condition, offset, limit, orderField, orderType, listTitle, listValues, e);
}

bool ZGSPHisDataManagerI::queryTableCount(std::string tableName, std::string condition, int &count, ErrorInfo &e, const Ice::Current &current)
{
    return ZGSPHisDataManagerMng::instance()->queryTableCount(tableName, condition, count, e);
}

bool ZGSPHisDataManagerI::queryStoreYc(StringList listDevice, StringList listProperty, std::string startTime, std::string endTime, ListStringMap& listResult, ErrorInfo& e, const Ice::Current& current)
{
	return ZGSPHisDataManagerMng::instance()->queryStoreYc(listDevice, listProperty, startTime, endTime, listResult, e);
}

bool ZGSPHisDataManagerI::queryStoreYx(StringList listDevice, StringList listProperty, std::string startTime, std::string endTime, ListStringMap& listResult, ErrorInfo& e, const Ice::Current& current)
{
	return ZGSPHisDataManagerMng::instance()->queryStoreYx(listDevice, listProperty, startTime, endTime, listResult, e);
}

bool ZGSPHisDataManagerI::queryStoreText(StringList listDevice, StringList listProperty, std::string startTime, std::string endTime, ListStringMap& listResult, ErrorInfo& e, const Ice::Current& current)
{
	return ZGSPHisDataManagerMng::instance()->queryStoreText(listDevice, listProperty, startTime, endTime, listResult, e);
}

bool ZGSPHisDataManagerI::queryStoreYm(StringList listDevice, StringList listProperty, std::string startTime, std::string endTime, ListStringMap& listResult, ErrorInfo& e, const Ice::Current& current)
{
	return ZGSPHisDataManagerMng::instance()->queryStoreYm(listDevice, listProperty, startTime, endTime, listResult, e);
}
} // namespace ZG6000

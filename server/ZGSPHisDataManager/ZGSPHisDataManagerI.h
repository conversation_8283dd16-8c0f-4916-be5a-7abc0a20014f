#ifndef ZG6000_ZGSPHISDATAMANAGERI_H
#define ZG6000_ZGSPHISDATAMANAGERI_H

#include <ZGSPHisDataManager.h>

namespace ZG6000
{
	class ZGSPHisDataManagerI : public ZGSPHisDataManager
	{
	public:
		ZGSPHisDataManagerI();

		bool checkState(const Ice::Current& current) override;

		bool queryTableData(std::string tableName, std::string condition, int offset, int limit, std::string orderField,
		                    std::string orderType, StringList& listTitle, ListStringList& listValues, ErrorInfo& e, const Ice::Current& current) override;
		bool queryTableCount(std::string tableName, std::string condition, int& count, ErrorInfo& e,
		                     const Ice::Current& current) override;
		bool queryStoreYc(StringList listDevice, StringList listProperty, std::string startTime, std::string endTime,
		                  ListStringMap& listResult, ErrorInfo& e, const Ice::Current& current) override;
		bool queryStoreYx(StringList listDevice, StringList listProperty, std::string startTime, std::string endTime,
		                  ListStringMap& listResult, ErrorInfo& e, const Ice::Current& current) override;
		bool queryStoreText(StringList listDevice, StringList listProperty, std::string startTime, std::string endTime,
		                    ListStringMap& listResult, ErrorInfo& e, const Ice::Current& current) override;
		bool queryStoreYm(StringList listDevice, StringList listProperty, std::string startTime, std::string endTime,
		                  ListStringMap& listResult, ErrorInfo& e, const Ice::Current& current) override;
	};
} // namespace ZG6000

#endif // ZG6000_ZGSPHISDATAMANAGERI_H

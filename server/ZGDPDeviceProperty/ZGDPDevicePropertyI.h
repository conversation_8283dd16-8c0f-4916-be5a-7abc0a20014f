#ifndef ZG6000_ZGDPDEVICEPROPERTYI_H
#define ZG6000_ZGDPDEVICEPROPERTYI_H

#include <ZGDPDeviceProperty.h>

namespace ZG6000 {

class ZGDPDevicePropertyI : public ZG6000::ZGDPDeviceProperty
{
public:
    

    ZGDPDevicePropertyI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current) override;
    // ZGDPDeviceProperty interface
public:
    bool getStaticProperties(std::string modelID, ListStringMap &properties, ErrorInfo &e, const Ice::Current &current) override;
    bool getFieldsProperties(std::string deviceID, ListStringMap &properties, ErrorInfo &e, const Ice::Current &current) override;
    bool getDynamicProperties(std::string deviceID, ListStringMap &properties, ErrorInfo &e, const Ice::Current &current) override;
    bool getWholeProperties(std::string deviceID, ListStringMap &properties, ErrorInfo &e, const Ice::Current &current) override;
    bool mgetProperty(StringList listDeviceID, std::string propertyName, MapStringMap &properties, ErrorInfo &e, const Ice::Current &current) override;
    bool getProperty(std::string deviceID, std::string propertyName, StringMap &property, ErrorInfo &e, const Ice::Current &current) override;
    bool mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& propertyValues, ErrorInfo& e, const Ice::Current& current) override;
    bool mgetPropertyValue(StringList listDeviceID, std::string propertyName, StringMap &propertyValues, ErrorInfo &e, const Ice::Current &current) override;
    bool getPropertyValue(std::string deviceID, std::string propertyName, std::string &propertyValue, ErrorInfo &e, const Ice::Current &current) override;
    bool getPropertyValues(std::string deviceID, StringList listName, StringMap &values, ErrorInfo &e, const Ice::Current &current) override;
    bool getRuntimePropertyValue(std::string deviceID, std::string propertyName, std::string &propertyValue, ErrorInfo &e, const Ice::Current &current) override;
    bool getRuntimePropertyValues(std::string deviceID, StringList listName, StringMap &propertyValues, ErrorInfo &e, const Ice::Current &current) override;
    bool updatePropertyValue(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo &e, const Ice::Current &current) override;
    bool updatePropertyValues(std::string deviceID, StringMap values, ErrorInfo &e, const Ice::Current &current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGDPDEVICEPROPERTYI_H

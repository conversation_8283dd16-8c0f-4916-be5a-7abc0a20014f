#include "ZGDPDevicePropertyMng.h"

#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "zgerror/ZGDPDevicePropertyError.h"

#include <QThread>

namespace ZG6000
{
    ZGDPDevicePropertyMng::ZGDPDevicePropertyMng(QObject* parent)
        : QObject{parent}
    {
    }

    void ZGDPDevicePropertyMng::initDeviceFields()
    {
        m_mapDeviceFields = {
            {"typeID", u8"设备类型ID"}, {"typeName", u8"设备类型名称"}, {"modelID", u8"设备模型ID"}, {"modelName", u8"设备模型名称"},
            {"productModelID", u8"产品型号ID"}, {"productModelName", u8"产品型号名称"}, {"manufactorID", u8"制造商ID"}, {"manufactorName", u8"制造商名称"},
            {"organID", u8"所属部门ID"}, {"organName", u8"所属部门名称"}, {"productDate", u8"生产日期"}, {"unitID", u8"单位"},
            {"rfid", "RFID"}, {"qrcode", u8"二维码"}, {"brcode", u8"条形码"}, {"image", "图像"},
            {"model3D", u8"3D模型"}, {"rtDeviceID", u8"监控设备ID"}, {"rtStateID", u8"设备状态"}, {"rtCoordinates", u8"坐标"}
        };
    }

    void ZGDPDevicePropertyMng::initServerInstConfig()
    {
        const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
        QString errMsg;
        int value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
            ZGLOG_WARN(errMsg);
        else
            m_initInterval = value;
    }

    bool ZGDPDevicePropertyMng::initServerInstInfo()
    {
        m_serverName = ZGRuntime::instance()->getServerID();
        if (m_serverName.isEmpty())
        {
            ZGLOG_ERROR("Empty server id.");
            return false;
        }
        m_instName = ZGRuntime::instance()->getInstanceID();
        if (m_instName.isEmpty())
        {
            ZGLOG_ERROR("Empty server instance id.");
            return false;
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::mgetFieldValues(const StringList& listDeviceID, const StringList& listName, MapStringMap& propertyValues)
    {
        QString sql = QString("SELECT id, %1 FROM dp_param_device WHERE id IN (%2)")
            .arg(ZGUtils::join(listName, ", ").c_str(), ZGUtils::join(listDeviceID, ", ").c_str());
        ZG6000::ListStringMap listDevicePropertyValue;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevicePropertyValue))
        {
            ZGLOG_ERROR(QStringLiteral("获取字段属性值失败"));
            return false;
        }
        for (auto& devicePropertyValue : listDevicePropertyValue)
        {
            auto deviceID = devicePropertyValue["id"];
            propertyValues[deviceID] = std::move(devicePropertyValue);
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::mgetStaticValues(const StringList& listDeviceID, const StringList& listName, MapStringMap& propertyValues)
    {
        StringList listModelID;
        if (!ZGProxyCommon::mgetDataByField("dp_param_device", listDeviceID, "modelID", listModelID))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备模型ID失败"));
            return false;
        }
        // 将listDeviceID和listModelID用StringMap建立关联
        StringMap mapDeviceModel;
        for (size_t i = 0; i < listDeviceID.size(); ++i)
        {
            mapDeviceModel[listDeviceID[i]] = listModelID[i];
        }
        // 将listModelID排序并去重
        std::sort(listModelID.begin(), listModelID.end());
        listModelID.erase(std::unique(listModelID.begin(), listModelID.end()), listModelID.end());
        // 获取模型属性
        QString sql = QString("SELECT modelID, name, defaultValue FROM dp_param_device_model_property "
            "WHERE modelID IN ('%1') AND name IN ('%2')")
            .arg(ZGUtils::join(listModelID, ", ").c_str(), ZGUtils::join(listName, ", ").c_str());
        ZG6000::ListStringMap modelsValues;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), modelsValues))
        {
            ZGLOG_ERROR(QStringLiteral("获取静态属性值失败"));
            return false;
        }
        // 将modelsValues按modelID分组，转换为MapStringMap
        MapStringMap mapModelValues;
        for (auto& modelValue : modelsValues)
        {
            mapModelValues[modelValue["modelID"]][modelValue["name"]] = modelValue["defaultValue"];
        }
        // 为每个设备关联其对应的模型属性
        for (const auto& [deviceID, modelID]: mapDeviceModel)
        {
            propertyValues[deviceID] = mapModelValues[modelID];
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::mgetDynamicValues(const StringList& listDeviceID, const StringList& listName, MapStringMap& propertyValues)
    {
        QString sql = QString("SELECT deviceID, propertyName AS name, rtValue AS value FROM dp_param_device_property "
                              "WHERE deviceID IN (%1) AND propertyName IN (%2)")
            .arg(ZGUtils::join(listDeviceID, ", ").c_str(), ZGUtils::join(listName, ", ").c_str());
        ListStringMap listDevicePropertyValue;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevicePropertyValue))
        {
            ZGLOG_ERROR(QStringLiteral("获取动态属性失败"));
            return false;
        }
        for (auto& devicePropertyValue : listDevicePropertyValue)
        {
            auto deviceID = devicePropertyValue["deviceID"];
            propertyValues[devicePropertyValue["deviceID"]][devicePropertyValue["name"]] = devicePropertyValue["value"];
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::getFieldValues(const std::string& deviceID, const StringList& listName, StringMap& values)
    {
        QString sql = QString("SELECT %1 FROM dp_param_device WHERE id = '%2'")
            .arg(ZGUtils::join(listName, ", ").c_str(), deviceID.c_str());
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), values))
        {
            ZGLOG_ERROR(QStringLiteral("获取字段属性值失败"));
            return false;
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::getStaticValues(const std::string& deviceID, const StringList& listName, StringMap& values)
    {
        std::string modelID;
        if (!ZGProxyCommon::getDataByField("dp_param_device", std::move(deviceID), "modelID", modelID))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备模型ID失败"));
            return false;
        }
        QString sql = QString("SELECT name, defaultValue FROM dp_param_device_model_property "
                              "WHERE modelID = '%1' AND name IN (%2)")
            .arg(modelID.c_str(), ZGUtils::join(listName, ", ").c_str());
        ListStringMap listValue;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listValue))
        {
            ZGLOG_ERROR(QStringLiteral("获取静态属性失败"));
            return false;
        }
        for (auto& nameValue : listValue)
        {
            values[nameValue["name"]] = nameValue["defaultValue"];
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::getDynamicValues(const std::string& deviceID, const StringList& listName, StringMap& values)
    {
        QString sql = QString("SELECT propertyName AS name, rtValue AS value FROM dp_param_device_property "
                              "WHERE deviceID = '%1' AND propertyName IN (%2)")
            .arg(deviceID.c_str(), ZGUtils::join(listName, ", ").c_str());
        ListStringMap listValue;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listValue))
        {
            ZGLOG_ERROR(QStringLiteral("获取动态属性失败"));
            return false;
        }
        for (auto& nameValue : listValue)
        {
            values[nameValue["name"]] = nameValue["value"];
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::updateFieldValues(const std::string& deviceID, const StringMap& values)
    {
        std::string fields;
        for (const auto& [name, value] : values)
        {
            fields += QString("%1 = '%2',").arg(name.c_str(), value.c_str()).toStdString();
        }
        fields.pop_back();
        QString sql = QString("UPDATE dp_param_device SET %1 WHERE id = '%2'").arg(fields.c_str(), deviceID.c_str());
        if (!ZGProxyCommon::execSql(sql.toStdString()))
        {
            ZGLOG_ERROR(QStringLiteral("更新字段属性失败"));
            return false;
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::updateDynamicValues(const std::string& deviceID, const StringMap& values)
    {
        ZG6000::StringList listSql;
        for (const auto& [name, value] : values)
        {
            listSql.push_back(QString("UPDATE dp_param_device_property SET rtValue = '%1' WHERE deviceID = '%2' AND propertyName = '%3'")
                .arg(value.c_str(), deviceID.c_str(), name.c_str()).toStdString());
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            ZGLOG_ERROR(QStringLiteral("更新动态属性失败"));
            return false;
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::getFieldProperty(const std::string& deviceID, const std::string& propertyName, StringMap& property)
    {
        QString sql = QString("SELECT %1 FROM dp_param_device WHERE id = '%2'")
            .arg(propertyName.c_str(), deviceID.c_str());
        std::string propertyValue;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), propertyValue))
        {
            ZGLOG_ERROR(QStringLiteral("获取字段属性失败"));
            return false;
        }
        property["name"] = propertyName;
        property["value"] = propertyValue;
        property["desc"] = "";
        property["typeID"] = "";
        property["typeName"] = u8"公共属性";
        return true;
    }

    bool ZGDPDevicePropertyMng::getStaticProperty(const std::string& deviceID, const std::string& propertyName, StringMap& property)
    {
        std::string modelID;
        if (!ZGProxyCommon::getDataByField("dp_param_device", std::move(deviceID), "modelID", modelID))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备模型ID失败"));
            return false;
        }
        QString sql = QString("SELECT a.name, a.defaultValue AS value, a.description AS `desc`, a.propertyTypeID AS typeID, "
            "b.name AS typeName, a.unitID FROM dp_param_device_model_property a "
            "LEFT JOIN dp_dict_device_type b ON a.propertyTypeID = b.id "
            "WHERE a.modelID = '%1' AND a.name = '%2'").arg(modelID.c_str()).arg(propertyName.c_str());
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), property))
        {
            ZGLOG_ERROR(QStringLiteral("获取模型属性失败"));
            return false;
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::getDynamicProperty(const std::string& deviceID, const std::string& propertyName, StringMap& property)
    {
        QString sql = QString("SELECT a.propertyName AS name, a.rtValue AS value, b.description AS `desc`, "
            "b.propertyTypeID as typeID, c.name AS typeName, b.unitID "
            "FROM dp_param_device_property a LEFT JOIN dp_param_device_model_property b ON a.modelID = b.modelID AND a.propertyName = b.name "
            "LEFT JOIN dp_dict_device_property_type c ON b.propertyTypeID = c.id "
            "WHERE b.isDynamic = 1 AND a.deviceID = '%1' AND a.propertyName = '%2'").arg(deviceID.c_str(), propertyName.c_str());
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), property))
        {
            ZGLOG_ERROR(QStringLiteral("获取动态属性失败"));
            return false;
        }
        return true;
    }

    ZGDPDevicePropertyMng* ZGDPDevicePropertyMng::instance()
    {
        if (g_pInstance == nullptr)
            g_pInstance = new ZGDPDevicePropertyMng;
        return g_pInstance;
    }

    void ZGDPDevicePropertyMng::init()
    {
        initDeviceFields();
        initServerInstConfig();
        while (!initServerInstInfo())
        {
            ZGLOG_ERROR("initServerInstInfo error.");
            QThread::sleep(m_initInterval);
        }
        m_initialized = true;
        ZGLOG_INFO("ZGDPDeviceProperty init finished.");
    }

    bool ZGDPDevicePropertyMng::checkState()
    {
        return m_initialized;
    }

    void ZGDPDevicePropertyMng::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord)
    {
    }

    bool ZGDPDevicePropertyMng::getStaticProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e)
    {
        std::string modelID;
        if (!ZGProxyCommon::getDataByField("dp_param_device", std::move(deviceID), "modelID", modelID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取设备模型ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString("SELECT a.name, a.defaultValue AS value, a.description AS `desc`, a.propertyTypeID AS typeID, "
            "b.name AS typeName, a.unitID FROM dp_param_device_model_property a "
            "LEFT JOIN dp_dict_device_type b ON a.propertyTypeID = b.id "
            "WHERE a.modelID = '%1'").arg(modelID.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), properties))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取模型属性失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::getFieldsProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e)
    {
        QString sql = QString("SELECT a.id AS id, a.name AS name, a.typeID, b.name AS typeName, a.modelID, c.name AS modelName, "
            "a.productModelID, d.name AS productModelName, a.manufactorID, e.name AS manufactorName, "
            "a.organID, f.name AS organName, a.productDate, a.unitID, g.name AS unitName, a.rfid, a.qrcode, a.brcode, a.image, a.model3D, "
            "a.rtDeviceID, a.rtStateID, h.name AS rtStateName, a.rtCoordinates, a.rtHealth, a.rtExpectedLife FROM dp_param_device a "
            "LEFT JOIN dp_dict_device_type b ON a.typeID = b.id LEFT JOIN dp_param_device_model c ON a.modelID = c.id "
            "LEFT JOIN dp_dict_product_model d ON a.productModelID = d.id LEFT JOIN dp_dict_device_manufactory e ON a.manufactorID = e.id "
            "LEFT JOIN sp_param_hrm_organ f ON a.organID = f.id "
            "LEFT JOIN sp_dict_unit g ON a.unitID = g.id "
            "LEFT JOIN dp_dict_device_state h ON a.rtStateID = h.id WHERE a.id = '%1'").arg(deviceID.c_str());
        ZG6000::StringMap fieldsProperty;
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), fieldsProperty))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取字段属性失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (auto& [name, value] : fieldsProperty)
        {
            ZG6000::StringMap property;
            property["name"] = name;
            property["value"] = value;
            const auto& it = m_mapDeviceFields.find(name);
            property["desc"] = (it == m_mapDeviceFields.end() ? "" : value);
            property["typeID"] = "";
            property["typeName"] = u8"公共属性";
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::getDynamicProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e)
    {
        QString sql = QString("SELECT a.propertyName AS name, a.rtValue AS value, b.description AS `desc`, "
            "b.propertyTypeID as typeID, c.name AS typeName, b.unitID "
            "FROM dp_param_device_property a LEFT JOIN dp_param_device_model_property b ON a.modelID = b.modelID AND a.propertyName = b.name "
            "LEFT JOIN dp_dict_device_property_type c ON b.propertyTypeID = c.id "
            "WHERE b.isDynamic = 1 AND a.deviceID = '%1'").arg(deviceID.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), properties))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取动态属性失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::getWholeProperties(std::string deviceID, ListStringMap& properties, ErrorInfo& e)
    {
        if (!getFieldsProperties(deviceID, properties, e))
            return false;
        ListStringMap modelProperties;
        if (!getStaticProperties(deviceID, modelProperties, e))
            return false;
        if (!modelProperties.empty())
            std::move(modelProperties.begin(), modelProperties.end(), std::back_inserter(properties));
        ListStringMap dynamicProperties;
        if (!getDynamicProperties(std::move(deviceID), dynamicProperties, e))
            return false;
        if (!dynamicProperties.empty())
            std::move(dynamicProperties.begin(), dynamicProperties.end(), std::back_inserter(properties));
        return true;
    }

    bool ZGDPDevicePropertyMng::mgetProperty(StringList listDeviceID, std::string propertyName, MapStringMap& properties, ErrorInfo& e)
    {
        return false;
    }

    bool ZGDPDevicePropertyMng::getProperty(std::string deviceID, std::string propertyName, StringMap& property, ErrorInfo& e)
    {
        if (m_mapDeviceFields.find(propertyName) != m_mapDeviceFields.end())
        {
            if (!getFieldProperty(deviceID, propertyName, property))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取字段属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        if (propertyName.find_first_of("dy") == 0)
        {
            if (!getDynamicProperty(deviceID, propertyName, property))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取动态属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        if (propertyName.find_first_of("st") == 0)
        {
            if (!getStaticProperty(deviceID, propertyName, property))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取模型属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("未知的属性名'%1'").arg(propertyName.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }

    bool ZGDPDevicePropertyMng::mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& propertyValues, ErrorInfo& e)
    {
        StringList fields, models, dynamics;
        for (const auto& name : listName)
        {
            if (m_mapDeviceFields.find(name) != m_mapDeviceFields.end())
                fields.push_back(name);
            else if (name.find_first_of("dy") == 0)
                dynamics.push_back(name);
            else if (name.find_first_of("st") == 0)
                models.push_back(name);
        }
        if (!fields.empty() && !mgetFieldValues(listDeviceID, fields, propertyValues))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取字段属性值失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!models.empty() && !mgetStaticValues(listDeviceID, models, propertyValues))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取模型属性值失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!dynamics.empty() && !mgetDynamicValues(listDeviceID, dynamics, propertyValues))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取动态属性值失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::mgetPropertyValue(StringList listDeviceID, std::string propertyName, StringMap& deviceValues, ErrorInfo& e)
    {
        MapStringMap devicesValues;
        if (m_mapDeviceFields.find(propertyName) != m_mapDeviceFields.end())
        {
            if (!mgetFieldValues(listDeviceID, {propertyName}, devicesValues))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取字段属性值失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            for (auto& [deviceID, values]: devicesValues)
            {
                deviceValues[deviceID] = values[propertyName];
            }
            return true;
        }
        if (propertyName.find_first_of("dy") == 0)
        {
            if (!mgetDynamicValues(listDeviceID, {propertyName}, devicesValues))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取动态属性值失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            for (auto& [deviceID, values]: devicesValues)
            {
                deviceValues[deviceID] = values[propertyName];
            }
            return true;
        }
        if (propertyName.find_first_of("st") == 0)
        {
            if (!mgetStaticValues(listDeviceID, {propertyName}, devicesValues))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取模型属性值失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            for (auto& [deviceID, values]: devicesValues)
            {
                deviceValues[deviceID] = values[propertyName];
            }
            return true;
        }
        e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("未知的属性名'%1'").arg(propertyName.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }

    bool ZGDPDevicePropertyMng::getPropertyValue(std::string deviceID, std::string propertyName, std::string& propertyValue, ErrorInfo& e)
    {
        StringMap values;
        if (m_mapDeviceFields.find(propertyName) != m_mapDeviceFields.end())
        {
            if (!getFieldValues(deviceID, {propertyName}, values))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取字段属性值失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            propertyValue = values[propertyName];
            return true;
        }
        if (propertyName.find_first_of("dy") == 0)
        {
            if (!getDynamicValues(deviceID, {propertyName}, values))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取动态属性值失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            propertyValue = values[propertyName];
            return true;
        }
        if (propertyName.find_first_of("st") == 0)
        {
            if (!getStaticValues(deviceID, {propertyName}, values))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取模型属性值失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            propertyValue = values[propertyName];
            return true;
        }
        e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("未知的属性名'%1'").arg(propertyName.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }

    bool ZGDPDevicePropertyMng::getPropertyValues(std::string deviceID, StringList listName, StringMap& values, ErrorInfo& e)
    {
        StringList fields, models, dynamics;
        for (const auto& name : listName)
        {
            if (m_mapDeviceFields.find(name) != m_mapDeviceFields.end())
                fields.push_back(name);
            else if (name.find_first_of("dy") == 0)
                dynamics.push_back(name);
            else if (name.find_first_of("st") == 0)
                models.push_back(name);
        }
        if (!fields.empty() && !getFieldValues(deviceID, fields, values))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取字段属性值失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!models.empty() && !getStaticValues(deviceID, models, values))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取模型属性值失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!dynamics.empty() && !getDynamicValues(deviceID, dynamics, values))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取动态属性值失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGDPDevicePropertyMng::getRuntimePropertyValue(std::string deviceID, std::string propertyName, std::string& propertyValue, ErrorInfo& e)
    {
        auto deviceProxy = ZGProxyMng::instance()->getProxyMPDeviceProperty();
        if (deviceProxy == nullptr)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取设备属性代理失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            if (!deviceProxy->getPropertyValue(std::move(deviceID), std::move(propertyName), propertyValue, e))
                return false;
            return true;
        }
        catch (const Ice::Exception& ie)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = ie.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGDPDevicePropertyMng::getRuntimePropertyValues(std::string deviceID, StringList listName, StringMap& propertyValues, ErrorInfo& e)
    {
        auto deviceProxy = ZGProxyMng::instance()->getProxyMPDeviceProperty();
        if (deviceProxy == nullptr)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取设备属性代理失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            if (!deviceProxy->getPropertyValues(std::move(deviceID), listName, propertyValues, e))
                return false;
            return true;
        }
        catch (const Ice::Exception& ie)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_INTERNAL);
            e.errDetail = ie.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGDPDevicePropertyMng::updatePropertyValue(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo& e)
    {
        if (m_mapDeviceFields.find(propertyName) != m_mapDeviceFields.end())
        {
            if (!updateFieldValues(deviceID, {{propertyName, propertyValue}}))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新字段属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        if (propertyName.find_first_of("dy") == 0)
        {
            if (!updateDynamicValues(deviceID, {{propertyName, propertyValue}}))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
                e.errDetail = QStringLiteral("更新动态属性失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("属性名'%1'只读或不存在").arg(propertyName.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }

    bool ZGDPDevicePropertyMng::updatePropertyValues(std::string deviceID, StringMap values, ErrorInfo& e)
    {
        StringMap fields, dynamics;
        for (const auto& [name, value] : values)
        {
            if (m_mapDeviceFields.find(name) != m_mapDeviceFields.end())
                fields[name] = value;
            else if (name.find_first_of("dy") == 0)
                dynamics[name] = value;
        }
        if (!fields.empty() && !updateFieldValues(deviceID, fields))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("更新字段属性失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!dynamics.empty() && !updateDynamicValues(deviceID, dynamics))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGDPDeviceProperty::ZG_ERR_RT);
            e.errDetail = QStringLiteral("更新动态属性失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }
} // namespace ZG6000

#ifndef ZG6000_ZGDPDEVICEPROPERTYMNG_H
#define ZG6000_ZGDPDEVICEPROPERTYMNG_H

#include <QObject>
#include <QTimer>
#include <Ice/Ice.h>
#include "ZGServerCommon.h"

namespace ZG6000 {

class ZGDPDevicePropertyMng : public QObject
{
    Q_OBJECT
public:
    static ZGDPDevicePropertyMng* instance();
    void init();

    bool checkState();
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord);

    bool getStaticProperties(std::string deviceID, ListStringMap &properties, ErrorInfo &e);
    bool getFieldsProperties(std::string deviceID, ListStringMap &properties, ErrorInfo &e);
    bool getDynamicProperties(std::string deviceID, ListStringMap &properties, ErrorInfo &e);
    bool getWholeProperties(std::string deviceID, ListStringMap &properties, ErrorInfo &e);
    bool mgetProperty(StringList listDeviceID, std::string propertyName, MapStringMap &properties, ErrorInfo &e);
    bool getProperty(std::string deviceID, std::string propertyName, StringMap &property, ErrorInfo &e);
    bool mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& propertyValues, ErrorInfo& e);
    bool mgetPropertyValue(StringList listDeviceID, std::string propertyName, StringMap &deviceValues, ErrorInfo &e);
    bool getPropertyValue(std::string deviceID, std::string propertyName, std::string &propertyValue, ErrorInfo &e);
    bool getPropertyValues(std::string deviceID, StringList listName, StringMap &values, ErrorInfo &e);
    bool getRuntimePropertyValue(std::string deviceID, std::string propertyName, std::string &propertyValue, ErrorInfo &e);
    bool getRuntimePropertyValues(std::string deviceID, StringList listName, StringMap &propertyValues, ErrorInfo &e);
    bool updatePropertyValue(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo &e);
    bool updatePropertyValues(std::string deviceID, StringMap values, ErrorInfo &e);

private:
    explicit ZGDPDevicePropertyMng(QObject* parent = nullptr);
    void initDeviceFields();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool mgetFieldValues(const StringList& listDeviceID, const StringList& listName, MapStringMap &propertyValues);
    bool mgetStaticValues(const StringList& listDeviceID, const StringList& listName, MapStringMap &propertyValues);
    bool mgetDynamicValues(const StringList& listDeviceID, const StringList& listName, MapStringMap &propertyValues);
    bool getFieldValues(const std::string& deviceID, const StringList& listName, StringMap &values);
    bool getStaticValues(const std::string& deviceID, const StringList& listName, StringMap &values);
    bool getDynamicValues(const std::string& deviceID, const StringList& listName, StringMap &values);
    bool updateFieldValues(const std::string& deviceID, const StringMap& values);
    bool updateDynamicValues(const std::string& deviceID, const StringMap& values);
    bool getFieldProperty(const std::string& deviceID, const std::string& propertyName, StringMap &property);
    bool getStaticProperty(const std::string& deviceID, const std::string& propertyName, StringMap &property);
    bool getDynamicProperty(const std::string& deviceID, const std::string& propertyName, StringMap &property);

private:
    bool m_initialized{ false };
    int m_initInterval{ 10 };
    QString m_serverName{ "" };
    QString m_instName{ "" };
    StringMap m_mapDeviceFields;
};

inline static ZGDPDevicePropertyMng* g_pInstance = nullptr;

} // namespace ZG6000

#endif // ZG6000_ZGDPDEVICEPROPERTYMNG_H

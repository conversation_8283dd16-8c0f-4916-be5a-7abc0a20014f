#include "ZGDPDevicePropertyI.h"
#include "ZGDPDevicePropertyMng.h"

namespace ZG6000 {

ZGDPDevicePropertyI::ZGDPDevicePropertyI()
{
    ZGDPDevicePropertyMng::instance()->init();
}

bool ZGDPDevicePropertyI::checkState(const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->checkState();
}

void ZGDPDevicePropertyI::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current)
{
    ZGDPDevicePropertyMng::instance()->dispatchData(std::move(tableName), std::move(oper), std::move(reason), std::move(time), std::move(listRecord));
}

bool ZGDPDevicePropertyI::getStaticProperties(std::string modelID, ListStringMap& properties, ErrorInfo& e, const Ice::Current& current)
{
    return ZGDPDevicePropertyMng::instance()->getStaticProperties(std::move(modelID), properties, e);
}

bool ZGDPDevicePropertyI::getFieldsProperties(std::string deviceID, ListStringMap &properties, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->getFieldsProperties(std::move(deviceID), properties, e);
}

bool ZGDPDevicePropertyI::getDynamicProperties(std::string deviceID, ListStringMap &properties, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->getDynamicProperties(std::move(deviceID), properties, e);
}

bool ZGDPDevicePropertyI::getWholeProperties(std::string deviceID, ListStringMap &properties, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->getWholeProperties(std::move(deviceID), properties, e);
}

bool ZGDPDevicePropertyI::mgetProperty(StringList listDeviceID, std::string propertyName, MapStringMap &properties, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->mgetProperty(std::move(listDeviceID), std::move(propertyName), properties, e);
}

bool ZGDPDevicePropertyI::getProperty(std::string deviceID, std::string propertyName, StringMap &property, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->getProperty(std::move(deviceID), std::move(propertyName), property, e);
}

bool ZGDPDevicePropertyI::mgetPropertyValues(StringList listDeviceID, StringList listName, MapStringMap& propertyValues, ErrorInfo& e, const Ice::Current& current)
{
    return ZGDPDevicePropertyMng::instance()->mgetPropertyValues(std::move(listDeviceID), std::move(listName), propertyValues, e);
}

bool ZGDPDevicePropertyI::mgetPropertyValue(StringList listDeviceID, std::string propertyName, StringMap &propertyValues, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->mgetPropertyValue(std::move(listDeviceID), std::move(propertyName), propertyValues, e);
}

bool ZGDPDevicePropertyI::getPropertyValue(std::string deviceID, std::string propertyName, std::string &propertyValue, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->getPropertyValue(std::move(deviceID), std::move(propertyName), propertyValue, e);
}

bool ZGDPDevicePropertyI::getPropertyValues(std::string deviceID, StringList listName, StringMap &values, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->getPropertyValues(std::move(deviceID), std::move(listName), values, e);
}

bool ZGDPDevicePropertyI::getRuntimePropertyValue(std::string deviceID, std::string propertyName, std::string &propertyValue, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->getRuntimePropertyValue(std::move(deviceID), std::move(propertyName), propertyValue, e);
}

bool ZGDPDevicePropertyI::getRuntimePropertyValues(std::string deviceID, StringList listName, StringMap &propertyValues, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->getRuntimePropertyValues(std::move(deviceID), std::move(listName), propertyValues, e);
}

bool ZGDPDevicePropertyI::updatePropertyValue(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->updatePropertyValue(std::move(deviceID), std::move(propertyName), std::move(propertyValue), e);
}

bool ZGDPDevicePropertyI::updatePropertyValues(std::string deviceID, StringMap values, ErrorInfo &e, const Ice::Current &current)
{
    return ZGDPDevicePropertyMng::instance()->updatePropertyValues(std::move(deviceID), std::move(values), e);
}

} // namespace ZG6000

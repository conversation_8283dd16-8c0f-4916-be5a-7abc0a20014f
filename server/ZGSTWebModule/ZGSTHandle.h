#ifndef ZGSTHANDLE_H
#define ZGSTHANDLE_H

#include <QObject>
#include <QJsonObject>
#include "QtHttpServer/QHttpServer"
#include "ZGWebModule.h"
#include "ZGSTStraySystem.h"

class ZGSTHandle : public QObject
{
    Q_OBJECT
public:
    explicit ZGSTHandle(QObject *parent = nullptr);

public:
    ZGWebModule::Response on_st_stations_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_mc_stations_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_station_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_offset_calc(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_calc_start(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_calc_stop(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_devices_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_device_relation_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_device_param_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_plzl_assoc_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_system_param_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_st_system_param_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);

private:
    ZGWebModule::Response systemCall(std::function<ZGWebModule::Response (std::shared_ptr<ZG6000::ZGSTStraySystemPrx>)> func);
};

#endif // ZGSTHANDLE_H

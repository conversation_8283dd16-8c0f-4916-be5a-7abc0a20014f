#include "ZGSTWebModule.h"
#include "ZGSTHandle.h"

ZGSTWebModule::ZGSTWebModule(QObject *parent)
    : ZGWebModule(parent)
{
    ZGSTHandle* pHandle = new ZGSTHandle(this);
    registerHandle("st/stations/get", pHandle, &ZGSTHandle::on_st_stations_get);
    registerHandle("st/mc/stations/get", pHandle, &ZGSTHandle::on_st_mc_stations_get);
    registerHandle("st/station/set", pHandle, &ZGSTHandle::on_st_station_set);
    registerHandle("st/offset/calc", pHandle, &ZGSTHandle::on_st_offset_calc);
    registerHandle("st/calc/start", pHandle, &ZGSTHandle::on_st_calc_start);
    registerHandle("st/calc/stop", pHandle, &ZGSTHandle::on_st_calc_stop);
    registerHandle("st/devices/get", pHandle, &ZGSTHandle::on_st_devices_get);
    registerHandle("st/device/relation/update", pHandle, &ZGSTHandle::on_st_device_relation_update);
    registerHandle("st/device/param/get", pHandle, &ZGSTHandle::on_st_device_param_get);
    registerHandle("st/plzl/assoc/get", pHandle, &ZGSTHandle::on_st_plzl_assoc_get);
    registerHandle("st/system/param/get", pHandle, &ZGSTHandle::on_st_system_param_get);
    registerHandle("st/system/param/set", pHandle, &ZGSTHandle::on_st_system_param_set);
}


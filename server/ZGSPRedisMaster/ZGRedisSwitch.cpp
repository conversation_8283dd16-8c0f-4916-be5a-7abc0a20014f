#include "ZGRedisSwitch.h"
#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGRuntime.h"
#include "ZGSPRedisMasterMng.h"
#include "ZGSwitchCondition.h"
#include "zgerror/ZGSPRedisMasterError.h"
#include <QtConcurrent>

ZGRedisSwitch::ZGRedisSwitch(QObject *parent) : QObject(parent)
{

}

std::vector<ConnectionOption> ZGRedisSwitch::connectOptions(const std::string& addressA, int portA,
                                                            const std::string& addressB, int portB, const std::string& password)
{
    std::vector<ConnectionOption> connOptions;
    if (!addressA.empty())
    {
        connOptions.emplace_back(addressA, portA, password, 2);
    }
    if (!addressB.empty())
    {
        connOptions.emplace_back(addressB, portB, password, 2);
    }
    return connOptions;
}

void ZGRedisSwitch::initialize(ZGSPRedisMasterMng* pRedisMasterMng, 
                               const std::pair<std::string, int>& remoteANet,
                               const std::pair<std::string, int>& remoteBNet,
                               const std::string& remotePassword,
                               const std::pair<std::string, int>& localANet,
                               const std::pair<std::string, int>& localBNet,
                               const std::string& localPassword)
{
    m_pRedisMasterMng = pRedisMasterMng;
    m_remoteANet = remoteANet;
    m_remoteBNet = remoteBNet;
    m_remotePassword = remotePassword;
    m_localANet = localANet;
    m_localBNet = localBNet;
    m_localPassword = localPassword;
    m_pRedisRemote = new ZGRedisClient(connectOptions(m_remoteANet.first, m_remoteANet.second, m_remoteBNet.first, m_remoteBNet.second, m_remotePassword));
    m_pRedisLocal = new ZGRedisClient(connectOptions(m_localANet.first, m_localANet.second, m_localBNet.first, m_localBNet.second, m_localPassword));
    if (ZGSPRedisMasterMng::instance()->deployModeID() == "ZG_DM_MASTER_SLAVE")
    {
        m_conditions.push_back(new BothMasters(this));
        m_conditions.push_back(new BothSlaves(this));
    }
    else
    {
        m_conditions.push_back(new BothMastersBiggerAddress(this));
        m_conditions.push_back(new BothSlavesSmallerAddress(this));
    }
    m_conditions.push_back(new RemoteNetBBroken(this));
    m_conditions.push_back(new RemoteNetABroken(this));
    m_conditions.push_back(new LocalNetBroken(this));
    m_conditions.push_back(new RemoteNetBroken(this));
    m_conditions.push_back(new RemoteRedisBroken(this));
}

void ZGRedisSwitch::onCheckAndSwitch()
{
    auto future1 = QtConcurrent::run([&](){
        m_localANetOnline = ZGSPRedisMasterMng::checkLocalAddress(m_localANet.first);
        m_remoteANetOnline = ZGSPRedisMasterMng::checkRemoteAddress(m_remoteANet.first);
    });
    auto future2 = QtConcurrent::run([&](){
        m_localBNetOnline = ZGSPRedisMasterMng::checkLocalAddress(m_localBNet.first);
        m_remoteBNetOnline = ZGSPRedisMasterMng::checkRemoteAddress(m_remoteBNet.first);
    });
    future1.waitForFinished();
    future2.waitForFinished();
    ZGLOG_TRACE(QString("localANetOnline: %1, localBNetOnline: %2, remoteANetOnline: %3, remoteBNetOnline: %4")
                .arg(m_localANetOnline).arg(m_localBNetOnline).arg(m_remoteANetOnline).arg(m_remoteBNetOnline).toStdString().c_str());
    for (const auto pCondition : m_conditions)
    {
        if (pCondition->check())
        {
            if (m_lastConditon && (pCondition != m_lastConditon))
                m_lastConditon->resetCounter();
            m_lastConditon = pCondition;
            m_lastConditon->increaseCounter();
            if (m_lastConditon->counter() >= m_lastConditon->threshold())
            {
                m_lastConditon->execute();
                m_lastConditon->resetCounter();
                m_lastConditon = nullptr;
            }
            return;
        }
    }
    if (m_lastConditon)
    {
        m_lastConditon->resetCounter();
        m_lastConditon = nullptr;
    }
}

bool ZGRedisSwitch::getSlaveOf(ZGRedisClient* pRedisClient, std::string& slaveOf)
{
    std::string errMsg;
    if (!pRedisClient->config_get("slaveof", slaveOf, errMsg))
    {
        ZGLOG_ERROR(errMsg.c_str());
        return false;
    }
    return true;
}

void ZGRedisSwitch::switchToMaster() const
{
    std::string errMsg;
    if (!m_pRedisLocal->slaveof("", "", errMsg))
        ZGLOG_ERROR(errMsg.c_str());
    else
    {
        ZG6000::StringMap message{{"nodeID", m_pRedisMasterMng->localNetNodeID()},
            {"oldState", "slave"},
            {"newState", "master"}};
        const auto& json = ZGJson::convertToJson(message);
        m_pRedisMasterMng->publishSwitchMessage(json);
    }
}

void ZGRedisSwitch::switchToSlave(const std::string& masterAddress, int masterPort) const
{
    std::string errMsg;
    if (!m_pRedisLocal->slaveof(masterAddress, std::to_string(masterPort), errMsg))
        ZGLOG_ERROR(errMsg.c_str());
    else
    {
        ZG6000::StringMap message{{"nodeID", m_pRedisMasterMng->localNetNodeID()},
            {"oldState", "master"},
            {"newState", "slave"}};
        const auto& json = ZGJson::convertToJson(message);
        m_pRedisMasterMng->publishSwitchMessage(json);
    }
}

bool ZGRedisSwitch::getLocalState(bool& isMaster, ZG6000::ErrorInfo& e)
{
    std::string slaveof;
    if (!getSlaveOf(m_pRedisLocal, slaveof))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRedisMaster::ZG_ERR_INTERNAL);
        e.errDetail = u8"获取本地redis状态失败";
        ZGLOG_ERROR(e);
        return false;
    }
    isMaster = slaveof.empty();
    return true;
}

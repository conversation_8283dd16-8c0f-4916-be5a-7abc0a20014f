#include "ZGSPRedisMasterI.h"
#include "ZGSPRedisMasterMng.h"

ZG6000::ZGSPRedisMasterI::ZGSPRedisMasterI()
{
	ZGSPRedisMasterMng::instance()->init();
}

bool
ZG6000::ZGSPRedisMasterI::checkState(const Ice::Current& current)
{
	Q_UNUSED(current)
	return ZGSPRedisMasterMng::instance()->checkState();
}

bool ZG6000::ZGSPRedisMasterI::getLocalState(bool& isMaster, ErrorInfo& e, const Ice::Current& current)
{
	return false;
}

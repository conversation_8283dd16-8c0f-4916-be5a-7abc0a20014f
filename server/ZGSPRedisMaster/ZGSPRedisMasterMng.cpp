#include "ZGSPRedisMasterMng.h"

#include "ZGDebugMng.h"
#include "ZGRedisSwitch.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"

#include <QThread>
#include <QProcess>
#include <QNetworkInterface>
#include <QHostAddress>
#include <QRandomGenerator>
#include <QFile>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>

#include <cstdlib>
#include <sys/types.h>
#include <string>

ZGSPRedisMasterMng* ZGSPRedisMasterMng::instance()
{
    if (g_pRedisMasterMng == nullptr)
        g_pRedisMasterMng = new ZGSPRedisMasterMng;
    return g_pRedisMasterMng;
}

bool ZGSPRedisMasterMng::checkState() const
{
    return m_initialized;
}

void ZGSPRedisMasterMng::init()
{
    initServerInstConfig();
    while (!initSystemInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initDatabaseParam())
    {
        ZGLOG_ERROR("initDatabaseParam error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initSystemParam())
    {
        ZGLOG_ERROR("initSystemParam error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initRedisParam())
    {
        ZGLOG_ERROR("initRedisParam error.");
        QThread::msleep(m_initInterval * 1000);
    }
    initRedisSwitchObject();
    emit checking();
    m_timer.start(m_checkInterval * 1000);
    m_initialized = true;
    ZGLOG_INFO("ZGSPRedisMaster init finished.");
}

bool ZGSPRedisMasterMng::checkLocalAddress(const std::string& address)
{
    if (address.empty())
        return false;
    const auto& allInterfaces = QNetworkInterface::allInterfaces();
    for (const auto& networkInterface : allInterfaces)
    {
        const auto& allAddresses = networkInterface.addressEntries();
        if (std::any_of(allAddresses.begin(), allAddresses.end(), [&address](const QNetworkAddressEntry& entry)
        {
            return entry.ip().toString().toStdString() == address;
        }))
        {
            auto flags = networkInterface.flags();
            return flags.testFlag(QNetworkInterface::IsUp) && flags.testFlag(QNetworkInterface::IsRunning);
        }
    }
    return false;
}

bool ZGSPRedisMasterMng::checkRemoteAddress(const std::string& address)
{
    std::string command;
#ifdef Q_OS_WIN
    command = "ping -n 1 -w 1000 > nul " + address;
#endif
#ifdef Q_OS_LINUX
	command = "ping -c 1 -w 1 -q " + address;
#endif
    return system(command.c_str()) == 0;
}

void ZGSPRedisMasterMng::publishSwitchMessage(const std::string& message)
{
    if (!m_isMqttInitialized)
        m_isMqttInitialized = initMqttParam();
    if (m_isMqttInitialized)
        m_pMqttClient->sendPublish("system/redis/switch", message.c_str());
}

ZGSPRedisMasterMng::ZGSPRedisMasterMng(QObject* parent) : QObject(parent)
{
}

void ZGSPRedisMasterMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 1, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
    m_localNetNodeID = ZGPubFun::getLocalNodeID().toStdString();
    ZGLOG_TRACE(m_localNetNodeID.c_str());
}

bool ZGSPRedisMasterMng::initSystemInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGSPRedisMasterMng::initDatabaseParam()
{
    QMap<QString, QString> mapCfgParam;
    QList<QMap<QString, QString>> listMapParam;
    if (!ZGPubFun::getParamCfgDB(mapCfgParam, listMapParam))
    {
        ZGLOG_ERROR("ZGPubFun::getParamCfgDB error.");
        return false;
    }
    if (listMapParam.empty())
    {
        ZGLOG_ERROR("Can't find any database config param.");
        return false;
    }
    QString dbType = listMapParam[0]["db_0_type"];
    ZGLOG_INFO(QString("dbType: %1").arg(dbType));
    auto db_password_encrypt = mapCfgParam["db_password_encrypt"];
    auto password = listMapParam[0]["db_0_password"];
    if (db_password_encrypt == "1")
        password = ZGPubFun::aesDecrypt(listMapParam[0]["db_0_password"]);
    ZGLOG_INFO(QString("password: %1").arg(password));
    QSqlDatabase db;
    if (dbType == "QMYSQL")
    {
        db = QSqlDatabase::addDatabase("QMYSQL");
        db.setHostName("127.0.0.1");
        db.setDatabaseName(listMapParam[0]["db_0_dbname"]);
        db.setUserName(listMapParam[0]["db_0_user"]);
        db.setPassword(password);
        db.setPort(listMapParam[0]["db_0_port"].toInt());
    }
    else if (dbType == "QODBC")
    {
        db = QSqlDatabase::addDatabase("QODBC");
        db.setDatabaseName(
            QString("DRIVER={SQL Server};SERVER=127.0.0.1;DATABASE=%1;UID=%2;PWD=%3;Connection Timeout=%4;")
            .arg(listMapParam[0]["db_0_dbname"]).arg(listMapParam[0]["db_0_user"]).arg(password).arg(
                listMapParam[0]["db_0_port"].toInt()));
    }
    else
    {
        ZGLOG_ERROR(QString("Unknown database type: %1").arg(dbType));
        return false;
    }
    if (!db.open())
    {
        ZGLOG_ERROR(QString("Open database error: %1").arg(db.lastError().text()));
        return false;
    }
    ZGLOG_INFO("Open database ok.");
    return true;
}

bool ZGSPRedisMasterMng::initSystemParam()
{
    QString sql = "SELECT deployModeID FROM sp_param_system";
    QSqlQuery query(sql);
    if (!query.next())
    {
        ZGLOG_ERROR(QStringLiteral("获取系统参数失败"));
        return false;
    }
    m_deployModeID = query.value("deployModeID").toString().toStdString();
    ZGLOG_INFO(QString("deployModeID: %1").arg(m_deployModeID.c_str()));
    return true;
}

bool ZGSPRedisMasterMng::initRedisParam()
{
    const auto& mapParam = ZGRuntime::instance()->getInstanceConfig("serverInst");
    const auto& serverType = mapParam.value("serverTypeID");
    ZG6000::StringList listString;
    const auto count = ZGUtils::splitString(serverType.toStdString(), "&", listString);
    if (count == 0)
    {
        ZGLOG_ERROR(QStringLiteral("服务实例配置文件中未定义服务器类型"));
        return false;
    }
    std::vector<MasterSlaveInfo> listMasterSlaveInfo;
    for (const auto& serverTypeID : listString)
    {
        QString sql = QString("SELECT nodeID, aNetAddr, aNetPort, bNetAddr, bNetPort, password FROM sp_param_node_server "
            "WHERE serverTypeID = '%1'").arg(serverTypeID.c_str());
        ZG6000::ListStringMap listMapResult;
        QSqlQuery query(sql);
        while (query.next())
        {
            ZG6000::StringMap mapResult;
            mapResult["nodeID"] = query.value("nodeID").toString().toStdString();
            mapResult["aNetAddr"] = query.value("aNetAddr").toString().toStdString();
            mapResult["aNetPort"] = query.value("aNetPort").toString().toStdString();
            mapResult["bNetAddr"] = query.value("bNetAddr").toString().toStdString();
            mapResult["bNetPort"] = query.value("bNetPort").toString().toStdString();
            mapResult["password"] = query.value("password").toString().toStdString();
            ZGLOG_INFO(QString("nodeID: %1, aNetAddr: %2, aNetPort: %3, bNetAddr: %4, bNetPort: %5, password: %6")
                .arg(mapResult["nodeID"].c_str()).arg(mapResult["aNetAddr"].c_str()).arg(mapResult["aNetPort"].c_str())
                .arg(mapResult["bNetAddr"].c_str()).arg(mapResult["bNetPort"].c_str()).arg(mapResult["password"].c_str()));
            listMapResult.push_back(std::move(mapResult));
        }
        //    	if (!ZGProxyCommon::execQuerySql(sql, listMapResult))
        //    	{
        //    		ZGLOG_ERROR(QStringLiteral("获取节点服务器参数失败"));
        //    		return false;
        //    	}
        if (listMapResult.size() != 2)
        {
            ZGLOG_ERROR(
                QStringLiteral("serverTypeID '%1' does not match master-slave config.").arg(serverTypeID.c_str()));
            return false;
        }
        MasterSlaveInfo masterSlaveInfo;
        if (!convertToMasterSlaveInfo(listMapResult, masterSlaveInfo))
            return false;
        if (std::find(listMasterSlaveInfo.begin(), listMasterSlaveInfo.end(), masterSlaveInfo) == listMasterSlaveInfo.
            end())
        {
            listMasterSlaveInfo.push_back(std::move(masterSlaveInfo));
        }
    }
    ZGLOG_TRACE(QString("listMasterSlaveInfo size: %1").arg(listMasterSlaveInfo.size()));
    for (const auto& masterSlaveInfo : listMasterSlaveInfo)
    {
        ZGLOG_TRACE(
            QString("'%1:%2' '%3:%4' '%5:%6' '%7:%8' '%9' '%10'").arg(masterSlaveInfo.remoteANetAddr.c_str()).arg(masterSlaveInfo.
                remoteANetPort)
            .arg(masterSlaveInfo.remoteBNetAddr.c_str()).arg(masterSlaveInfo.remoteBNetPort)
            .arg(masterSlaveInfo.localANetAddr.c_str()).arg(masterSlaveInfo.localANetPort)
            .arg(masterSlaveInfo.localBNetAddr.c_str()).arg(masterSlaveInfo.localBNetPort)
            .arg(masterSlaveInfo.remotePassword.c_str()).arg(masterSlaveInfo.localPassword.c_str()));
        ZGRedisSwitch* pSwitch = new ZGRedisSwitch;
        pSwitch->initialize(this, std::make_pair(masterSlaveInfo.remoteANetAddr, masterSlaveInfo.remoteANetPort),
            std::make_pair(masterSlaveInfo.remoteBNetAddr, masterSlaveInfo.remoteBNetPort),
            masterSlaveInfo.remotePassword,
            std::make_pair(masterSlaveInfo.localANetAddr, masterSlaveInfo.localANetPort),
            std::make_pair(masterSlaveInfo.localBNetAddr, masterSlaveInfo.localBNetPort),
            masterSlaveInfo.localPassword);
        QThread* pThread = new QThread;
        m_listRedisSwitch.emplace_back(pSwitch, pThread);
    }
    return true;
}

bool ZGSPRedisMasterMng::initMqttParam()
{
    if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
    {
        ZGLOG_ERROR("initMqttClient error.");
        return false;
    }
    m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
    if (m_pMqttClient == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage return null.");
        return false;
    }
    m_pMqttClient->connectToHost();
    return true;
}

void ZGSPRedisMasterMng::initRedisSwitchObject()
{
    for (const auto& pair : m_listRedisSwitch)
    {
        pair.first->moveToThread(pair.second);
        connect(&m_timer, &QTimer::timeout, pair.first, &ZGRedisSwitch::onCheckAndSwitch);
        connect(this, &ZGSPRedisMasterMng::checking, pair.first, &ZGRedisSwitch::onCheckAndSwitch);
        pair.second->start();
    }
}

bool ZGSPRedisMasterMng::convertToMasterSlaveInfo(const ZG6000::ListStringMap& listRecord,
                                                  MasterSlaveInfo& masterSlaveInfo) const
{
    try
    {
        for (const auto& record : listRecord)
        {
            const auto& nodeID = ZGUtils::get(record, "nodeID");
            if (nodeID == m_localNetNodeID)
            {
                masterSlaveInfo.localANetAddr = ZGUtils::get(record, "aNetAddr");
                masterSlaveInfo.localANetPort = QString(ZGUtils::get(record, "aNetPort").c_str()).toInt();
                masterSlaveInfo.localBNetAddr = ZGUtils::get(record, "bNetAddr");
                masterSlaveInfo.localBNetPort = QString(ZGUtils::get(record, "bNetPort").c_str()).toInt();
                masterSlaveInfo.localPassword = ZGUtils::get(record, "password");
            }
            else
            {
                masterSlaveInfo.remoteANetAddr = ZGUtils::get(record, "aNetAddr");
                masterSlaveInfo.remoteANetPort = QString(ZGUtils::get(record, "aNetPort").c_str()).toInt();
                masterSlaveInfo.remoteBNetAddr = ZGUtils::get(record, "bNetAddr");
                masterSlaveInfo.remoteBNetPort = QString(ZGUtils::get(record, "bNetPort").c_str()).toInt();
                masterSlaveInfo.remotePassword = ZGUtils::get(record, "password");
            }
        }
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

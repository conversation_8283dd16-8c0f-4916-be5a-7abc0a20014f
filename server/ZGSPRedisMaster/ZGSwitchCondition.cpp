#include "ZGSwitchCondition.h"

#include <ZGUtils.h>

#include "ZGRedisSwitch.h"
#include "ZGSPRedisMasterMng.h"
#include "ZGDebugMng.h"

void ZGSwitchCondition::increaseCounter()
{
    ++m_counter;
    ZGLOG_TRACE(QString("%1 increaseCounter: %2").arg(name().c_str()).arg(counter()).toStdString().c_str());
}

void ZGSwitchCondition::resetCounter()
{
    m_counter = 0;
    ZGLOG_TRACE(QString("%1 resetCounter").arg(name().c_str()));
}

size_t ZGSwitchCondition::counter() const
{
    return m_counter;
}

size_t ZGSwitchCondition::threshold() const
{
    return m_threshold;
}

bool ZGSwitchCondition::localSlaveOf(std::string& slaveOf) const
{
    return m_pRedisSwitch->getSlaveOf(m_pRedisSwitch->m_pRedisLocal, slaveOf);
}

bool ZGSwitchCondition::remoteSlaveOf(std::string& slaveOf) const
{
    return m_pRedisSwitch->getSlaveOf(m_pRedisSwitch->m_pRedisRemote, slaveOf);
}

bool ZGSwitchCondition::localRedisConnected() const
{
    return m_pRedisSwitch->m_pRedisLocal->connected();
}

bool ZGSwitchCondition::remoteRedisConnected() const
{
    return m_pRedisSwitch->m_pRedisRemote->connected();
}

bool ZGSwitchCondition::localANetOnline() const
{
    return m_pRedisSwitch->m_localANetOnline;
}

bool ZGSwitchCondition::localBNetOnline() const
{
    return m_pRedisSwitch->m_localBNetOnline;
}

bool ZGSwitchCondition::remoteANetOnline() const
{
    return m_pRedisSwitch->m_remoteANetOnline;
}

bool ZGSwitchCondition::remoteBNetOnline() const
{
    return m_pRedisSwitch->m_remoteBNetOnline;
}

std::pair<std::string, int> ZGSwitchCondition::remoteANet() const
{
    return m_pRedisSwitch->m_remoteANet;
}

std::pair<std::string, int> ZGSwitchCondition::remoteBNet() const
{
    return m_pRedisSwitch->m_remoteBNet;
}

bool ZGSwitchCondition::localANetBiggerAddress() const
{
    return m_pRedisSwitch->m_localANet.first > m_pRedisSwitch->m_remoteANet.first;
}

bool ZGSwitchCondition::parseHost(const std::string& host, std::string& address, int& port)
{
    const size_t blankPos = host.find_first_of(' ');        
    if (blankPos == std::string::npos)
        address = host;
    else
    {
        address = host.substr(0, blankPos);
        try
        {
            port = std::stoi(host.substr(blankPos + 1));
        }
        catch (const std::exception&)
        {
            ZGLOG_ERROR("Invalid port number.");
            return false;
        }        
    }
    return true;
}

bool BothMasters::check()
{
    if ((localANetOnline() || localBNetOnline()) && (remoteANetOnline() || remoteBNetOnline()))
    {
        if (localRedisConnected() && remoteRedisConnected())
        {
            std::string localSlave, remoteSlave;
            if (localSlaveOf(localSlave) && remoteSlaveOf(remoteSlave))
            {
                if (localSlave.empty() && remoteSlave.empty())
                    return true;
            }
        }
    }
    return false;
}

void BothMasters::execute()
{
    ZGLOG_INFO("switch to slave due to duplicated master.");
    if (remoteANetOnline())
        m_pRedisSwitch->switchToSlave(remoteANet().first, remoteANet().second);
    else if (remoteBNetOnline())
        m_pRedisSwitch->switchToSlave(remoteBNet().first, remoteBNet().second);
}

bool BothMastersBiggerAddress::check()
{
    if ((localANetOnline() || localBNetOnline()) && (remoteANetOnline() || remoteBNetOnline()))
    {
        if (localRedisConnected() && remoteRedisConnected())
        {
            std::string localSlave, remoteSlave;
            if (localSlaveOf(localSlave) && remoteSlaveOf(remoteSlave))
            {
                if (localSlave.empty() && remoteSlave.empty())
                {
                    if (localANetBiggerAddress())
                    {
                        return true;
                    }
                }
            }
        }
    }
    return false;
}

void BothMastersBiggerAddress::execute()
{
    ZGLOG_INFO("switch to slave due to duplicated master.");
    if (remoteANetOnline())
        m_pRedisSwitch->switchToSlave(remoteANet().first, remoteANet().second);
    else if (remoteBNetOnline())
        m_pRedisSwitch->switchToSlave(remoteBNet().first, remoteBNet().second);
}

bool BothSlaves::check()
{
    if ((localANetOnline() || localBNetOnline()) && (remoteANetOnline() || remoteBNetOnline()))
    {
        if (localRedisConnected() && remoteRedisConnected())
        {
            std::string localSlave, remoteSlave;
            if (localSlaveOf(localSlave) && remoteSlaveOf(remoteSlave))
            {
                if (!localSlave.empty() && !remoteSlave.empty())
                    return true;
            }
        }
    }
    return false;
}

void BothSlaves::execute()
{
    ZGLOG_INFO("switch to master due to duplicated slave.");
    m_pRedisSwitch->switchToMaster();
}

bool BothSlavesSmallerAddress::check()
{
    if ((localANetOnline() || localBNetOnline()) && (remoteANetOnline() || remoteBNetOnline()))
    {
        if (localRedisConnected() && remoteRedisConnected())
        {
            std::string localSlave, remoteSlave;
            if (localSlaveOf(localSlave) && remoteSlaveOf(remoteSlave))
            {
                if (!localSlave.empty() && !remoteSlave.empty())
                {
                    if (!localANetBiggerAddress())
                    {
                        return true;
                    }
                }
            }
        }
    }
    return false;
}

void BothSlavesSmallerAddress::execute()
{
    ZGLOG_INFO("switch to master due to duplicated slave.");
    m_pRedisSwitch->switchToMaster();
}

bool RemoteNetBBroken::check()
{
    if ((!remoteBNet().first.empty()) && (!remoteBNetOnline()) && localANetOnline() && remoteANetOnline())
    {
        if (localRedisConnected() && remoteRedisConnected())
        {
            std::string slaveOf;
            if (localSlaveOf(slaveOf))
            {
                if (!slaveOf.empty())
                {
                    std::string address;
                    int port;
                    if (parseHost(slaveOf, address, port))
                    {
                        if ((address == remoteBNet().first) && (port == remoteBNet().second))
                            return true;
                    }
                }
            }
        }
    }
    return false;
}

void RemoteNetBBroken::execute()
{
    ZGLOG_INFO("change slave to master network a address.");
    m_pRedisSwitch->switchToSlave(remoteANet().first, remoteANet().second);
}

bool RemoteNetABroken::check()
{
    if ((!remoteANet().first.empty()) && (!remoteANetOnline()) && localBNetOnline() && remoteBNetOnline())
    {
        if (localRedisConnected() && remoteRedisConnected())
        {
            std::string slaveOf;
            if (localSlaveOf(slaveOf))
            {
                if (!slaveOf.empty())
                {
                    std::string address;
                    int port;
                    if (parseHost(slaveOf, address, port))
                    {
                        if ((address == remoteANet().first) && (port == remoteANet().second))
                            return true;
                    }
                }
            }
        }
    }
    return false;
}

void RemoteNetABroken::execute()
{
    ZGLOG_INFO("change slave to master network b address.");
    m_pRedisSwitch->switchToSlave(remoteBNet().first, remoteBNet().second);
}

bool LocalNetBroken::check()
{
    if ((!localANetOnline()) && (!localBNetOnline()))
    {
        if (localRedisConnected())
        {
            std::string slaveOf;
            if (localSlaveOf(slaveOf))
            {
                if (slaveOf.empty())
                    return true;
            }
        }
    }
    return false;
}

void LocalNetBroken::execute()
{
    ZGLOG_INFO("switch to slave due to offline.");
    m_pRedisSwitch->switchToSlave(remoteANet().first, remoteANet().second);
}

bool RemoteNetBroken::check()
{
    if ((!remoteANetOnline()) && (!remoteBNetOnline()) && (localANetOnline() || localBNetOnline()))
    {
        if (localRedisConnected())
        {
            std::string slaveOf;
            if (localSlaveOf(slaveOf))
            {
                if (!slaveOf.empty())
                    return true;
            }
        }
    }
    return false;
}

void RemoteNetBroken::execute()
{
    ZGLOG_INFO("switch to master due to peer offline.");
    m_pRedisSwitch->switchToMaster();
}

bool RemoteRedisBroken::check()
{
    if ((localANetOnline() || localBNetOnline()) && (remoteANetOnline() || remoteBNetOnline()))
    {
        if (!remoteRedisConnected() && localRedisConnected())
        {
            std::string slaveOf;
            if (localSlaveOf(slaveOf))
            {
                if (!slaveOf.empty())
                    return true;
            }
        }
    }
    return false;
}

void RemoteRedisBroken::execute()
{
    ZGLOG_INFO("switch to master due to peer redis offline.");
    m_pRedisSwitch->switchToMaster();
}

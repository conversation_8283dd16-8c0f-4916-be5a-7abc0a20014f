#pragma once

#include <string>

class ZGRedisSwitch;
class ZGSwitchCondition
{
public:
    explicit ZGSwitchCondition(ZGRedisSwitch* redisSwitch): m_pRedisSwitch(redisSwitch) {}
    virtual ~ZGSwitchCondition() = default;

    virtual bool check() = 0;

    virtual void execute() = 0;

    virtual std::string name() = 0;

    void increaseCounter();

    void resetCounter();

    [[nodiscard]] size_t counter() const;
    
    [[nodiscard]] size_t threshold() const;

protected:

    bool localSlaveOf(std::string& slaveOf) const;
    
    bool remoteSlaveOf(std::string& slaveOf) const;
    
    bool localRedisConnected() const;
    
    bool remoteRedisConnected() const;
    
    bool localANetOnline() const;
    
    bool localBNetOnline() const;
    
    bool remoteANetOnline() const;
    
    bool remoteBNetOnline() const;

    std::pair<std::string, int> remoteANet() const;

    std::pair<std::string, int> remoteBNet() const;

    bool localANetBiggerAddress() const;

    static bool parseHost(const std::string& host, std::string& address, int& port);

protected:
    size_t m_counter{0};
    size_t m_threshold{1};
    ZGRedisSwitch* m_pRedisSwitch{nullptr};
};

class BothMasters: public ZGSwitchCondition
{
public:
    BothMasters(ZGRedisSwitch* redisSwitch): ZGSwitchCondition(redisSwitch){}
    std::string name() override
    {
        return "BothMasters";
    }
    bool check() override;
    void execute() override;
};

class BothMastersBiggerAddress: public ZGSwitchCondition
{
public:
    BothMastersBiggerAddress(ZGRedisSwitch* redisSwitch): ZGSwitchCondition(redisSwitch){}
    std::string name() override
    {
        return "BothMastersBiggerAddress";
    }
    bool check() override;
    void execute() override;
};

class BothSlaves: public ZGSwitchCondition
{
public:
    BothSlaves(ZGRedisSwitch* redisSwitch): ZGSwitchCondition(redisSwitch){}
    std::string name() override
    {
        return "BothSlaves";
    }
    bool check() override;
    void execute() override;
};

class BothSlavesSmallerAddress: public ZGSwitchCondition
{
public:
    BothSlavesSmallerAddress(ZGRedisSwitch* redisSwitch): ZGSwitchCondition(redisSwitch){}
    std::string name() override
    {
        return "BothSlavesSmallerAddress";
    }
    bool check() override;
    void execute() override;
};

class RemoteNetBBroken: public ZGSwitchCondition
{
public:
    RemoteNetBBroken(ZGRedisSwitch* redisSwitch): ZGSwitchCondition(redisSwitch){ m_threshold = 3; }
    std::string name() override
    {
        return "RemoteNetBBroken";
    }
    bool check() override;
    void execute() override;
};

class RemoteNetABroken: public ZGSwitchCondition
{
public:
    RemoteNetABroken(ZGRedisSwitch* redisSwitch): ZGSwitchCondition(redisSwitch){ m_threshold = 3; }
    std::string name() override
    {
        return "RemoteNetABroken";
    }
    bool check() override;
    void execute() override;
};

class LocalNetBroken: public ZGSwitchCondition
{
public:
    LocalNetBroken(ZGRedisSwitch* redisSwitch): ZGSwitchCondition(redisSwitch){}
    std::string name() override
    {
        return "LocalNetBroken";
    }
    bool check() override;
    void execute() override;
};

class RemoteNetBroken: public ZGSwitchCondition
{
public:
    RemoteNetBroken(ZGRedisSwitch* redisSwitch): ZGSwitchCondition(redisSwitch){ m_threshold = 3; }
    std::string name() override
    {
        return "RemoteNetBroken";
    }
    bool check() override;
    void execute() override;
};

class RemoteRedisBroken: public ZGSwitchCondition
{
public:
    explicit RemoteRedisBroken(ZGRedisSwitch* redisSwitch): ZGSwitchCondition(redisSwitch) {m_threshold = 3;}
    bool check() override;
    void execute() override;
    std::string name() override
    {
        return "RemoteRedisBroken";
    }
};

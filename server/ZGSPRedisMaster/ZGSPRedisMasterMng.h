#ifndef ZGSPREDISMASTERMNG_H
#define ZGSPREDISMASTERMNG_H

#include <QObject>
#include <QTimer>
#include <QThread>
#include "ZGProxyMng.h"

struct MasterSlaveInfo
{
    std::string remoteANetAddr;
    int remoteANetPort{0};
    std::string remoteBNetAddr;
    int remoteBNetPort{0};
    std::string remotePassword;
    std::string localANetAddr;
    int localANetPort{0};
    std::string localBNetAddr;
    int localBNetPort{0};
    std::string localPassword;
    bool operator == (const MasterSlaveInfo& other) const
    {
        return (remoteANetAddr == other.remoteANetAddr) && (remoteANetPort == other.remoteANetPort) &&
            (remoteBNetAddr == other.remoteBNetAddr) && (remoteBNetPort == other.remoteBNetPort) &&
            (localANetAddr == other.localANetAddr) && (localANetPort == other.localANetPort) &&
            (localBNetAddr == other.localBNetAddr) && (localBNetPort == other.localBNetPort);
    }

    bool operator != (const MasterSlaveInfo& other) const
    {
        return (((remoteANetAddr == other.remoteANetAddr && remoteANetPort != other.remoteANetPort) || remoteANetAddr.empty()) &&
            ((remoteBNetAddr == other.remoteBNetAddr && remoteBNetPort != other.remoteBNetPort) || remoteBNetAddr.empty()) &&
            ((localANetAddr == other.localANetAddr && localANetPort != other.localANetPort) || localANetAddr.empty()) &&
            ((localBNetAddr == other.localBNetAddr && localBNetPort != other.localBNetPort) || localBNetAddr.empty()));
    }
};

class ZGRedisSwitch;
class ZGMqttClient;
class ZGSPRedisMasterMng : public QObject
{
    Q_OBJECT
#ifdef Q_OS_WIN
    const QString dbPath = "C:/ZG/software/Redis-x64-3.2.100/dump.rdb";
#else
    const QString dbPath = "/var/lib/redis/dump.rdb";
#endif
public:
    static ZGSPRedisMasterMng* instance();
    bool checkState() const;
    void init();
    static bool checkLocalAddress(const std::string& address);
    static bool checkRemoteAddress(const std::string& address);
    [[nodiscard]] std::string localNetNodeID() const
    {
        return m_localNetNodeID;
    }
    [[nodiscard]] std::string deployModeID() const
    {
        return m_deployModeID;
    }
    void publishSwitchMessage(const std::string& message);

private:
    explicit ZGSPRedisMasterMng(QObject* parent = nullptr);
    void initServerInstConfig();
    bool initSystemInfo();
    bool initDatabaseParam();
    bool initSystemParam();
    bool initRedisParam();
    bool initMqttParam();
    void initRedisSwitchObject();
    bool convertToMasterSlaveInfo(const ZG6000::ListStringMap& listRecord, MasterSlaveInfo& masterSlaveInfo) const;

signals:
    void checking();

private:
    bool m_initialized{ false };
    QString m_nodeID;
    QString m_serverName;
    QString m_instName;
    int m_initInterval{ 10 };
    int m_checkInterval{ 2 };
    QTimer m_timer;
    int m_currentStep{ 0 };
    std::vector<std::pair<ZGRedisSwitch*, QThread*>> m_listRedisSwitch;
    ZGMqttClient* m_pMqttClient{ nullptr };
    bool m_isMqttInitialized{ false };
    std::string m_localNetNodeID;
    std::string m_deployModeID;
};

inline static ZGSPRedisMasterMng* g_pRedisMasterMng = nullptr;

#endif // ZGSPREDISMASTERMNG_H

#ifndef ZGREDISSWITCH_H
#define ZGREDISSWITCH_H

#include <QObject>
#include "redis/ZGRedisClient.h"
#include "ZGSwitchCondition.h"

class ZGSPRedisMasterMng;
class ZGRedisSwitch : public QObject
{
    Q_OBJECT
    friend class ZGSwitchCondition;
public:
    explicit ZGRedisSwitch(QObject *parent = nullptr);
    static std::vector<ConnectionOption> connectOptions(const std::string& addressA, int portA, const std::string& addressB, int portB, const std::string &password);
    void initialize(ZGSPRedisMasterMng* pRedisMasterMng, const std::pair<std::string, int>& remoteANet, const std::pair<std::string, int>& remoteBNet, const std::string &passwordA,
                    const std::pair<std::string, int>& localANet, const std::pair<std::string, int>& localBNet, const std::string &passwordB);
    void switchToMaster() const;
    void switchToSlave(const std::string& masterAddress, int masterPort) const;
    bool getLocalState(bool& isMaster, ZG6000::ErrorInfo& e);

public slots:
    void onCheckAndSwitch();

private:
    static bool getSlaveOf(ZGRedisClient* pRedisClient, std::string& slaveOf);
    

private:
    std::pair<std::string, int> m_remoteANet;
    std::pair<std::string, int> m_remoteBNet;
    std::string m_remotePassword;
    ZGRedisClient* m_pRedisRemote{nullptr};
    std::pair<std::string, int> m_localANet;
    std::pair<std::string, int> m_localBNet;
    std::string m_localPassword;
    ZGRedisClient* m_pRedisLocal{nullptr};
    ZGSPRedisMasterMng* m_pRedisMasterMng{nullptr};
    ZGSwitchCondition* m_lastConditon{nullptr};
    std::vector<ZGSwitchCondition*> m_conditions;
    bool m_localANetOnline{false};
    bool m_localBNetOnline{false};
    bool m_remoteANetOnline{false};
    bool m_remoteBNetOnline{false};
};

#endif // ZGREDISSWITCH_H

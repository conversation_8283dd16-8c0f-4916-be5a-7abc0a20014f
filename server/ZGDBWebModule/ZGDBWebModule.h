#ifndef ZGDBWEBMODULE_H
#define ZGDBWEBMODULE_H

#include "ZGWebModule.h"
#include "ZGProxyMng.h"

class ZGDBWebModule : public ZGWebModule
{
    Q_OBJECT
    Q_PLUGIN_METADATA(IID ZGWebModule_iid FILE "ZGDBWebModule.json")
    Q_INTERFACES(ZGWebModule)

public:
    explicit ZGDBWebModule(QObject* parent = nullptr);

private:
    Response on_db_field_query(const QString& clientID,
                               const QVariantMap& headers,
                               const QJsonValue& param,
                               const QHttpServerRequest& req);
    Response on_db_list_query(const QString& clientID,
                              const QVariantMap& headers,
                              const QJsonValue& param,
                              const QHttpServerRequest& req);
    Response on_db_map_query(const QString& clientID,
                             const QVariantMap& headers,
                             const QJsonValue& param,
                             const QHttpServerRequest& req);
    Response on_db_list_list_query(const QString& clientID,
                                   const QVariantMap& headers,
                                   const QJsonValue& param,
                                   const QHttpServerRequest& req);
    Response on_db_list_map_query(const QString& clientID,
                                  const QVariantMap& headers,
                                  const QJsonValue& param,
                                  const QHttpServerRequest& req);
    Response on_db_map_map_query(const QString& clientID,
                                 const QVariantMap& headers,
                                 const QJsonValue& param,
                                 const QHttpServerRequest& req);

    Response on_db_update_batch(const QString& clientID,
                                const QVariantMap& headers,
                                const QJsonValue& param,
                                const QHttpServerRequest& req);

    Response on_db_insert_batch(const QString& clientID,
                                const QVariantMap& headers,
                                const QJsonValue& param,
                                const QHttpServerRequest& req);

    Response on_db_delete_batch(const QString& clientID,
                                const QVariantMap& headers,
                                const QJsonValue& param,
                                const QHttpServerRequest& req);

    Response on_db_uuid_create(const QString& clientID,
                               const QVariantMap& headers,
                               const QJsonValue& param,
                               const QHttpServerRequest& req);

private:
    bool generateQuerySql(const QJsonObject& object,
                          QString& sql,
                          QString& errMsg);

    bool generateUpdateSqlBatch(const QJsonObject& object,
                                QStringList& sqlList,
                                QString& errMsg);

    bool generateInsertSqlBatch(const QJsonObject& object,
                                QStringList& sqlList,
                                QString& errMsg);

    bool generateDeleteSqlBatch(const QJsonObject& object,
                                QStringList& sqlList,
                                QString& errMsg);

    Response dbCall(
        std::function<Response(std::shared_ptr<ZG6000::ZGSPDBDataPrx>)> func);

    // ZGWebModule interface
public:
    bool initialize() override;
    QString prefix() override;
};

#endif // ZGDBWEBMODULE_H

#include "ZGDBWebModule.h"

#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGJson.h"

ZGDBWebModule::ZGDBWebModule(QObject* parent)
    : ZGWebModule(parent)
{
    registerHandle("db/field/query", this, &ZGDBWebModule::on_db_field_query);
    registerHandle("db/list/query", this, &ZGDBWebModule::on_db_list_query);
    registerHandle("db/map/query", this, &ZGDBWebModule::on_db_map_query);
    registerHandle("db/list/list/query", this, &ZGDBWebModule::on_db_list_list_query);
    registerHandle("db/list/map/query", this, &ZGDBWebModule::on_db_list_map_query);
    registerHandle("db/map/map/query", this, &ZGDBWebModule::on_db_map_map_query);
    registerHandle("db/update/batch", this, &ZGDBWebModule::on_db_update_batch);
    registerHandle("db/insert/batch", this, &ZGDBWebModule::on_db_insert_batch);
    registerHandle("db/delete/batch", this, &ZGDBWebModule::on_db_delete_batch);
    registerHandle("db/uuid/create", this, &ZGDBWebModule::on_db_uuid_create);
}

ZGWebModule::Response ZGDBWebModule::on_db_field_query(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString sql, errMsg;
    if (!generateQuerySql(object, sql, errMsg))
        return errorObject(errMsg);
    return dbCall([&](std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
                  {
                      ZG6000::ErrorInfo e;
                      std::string value;
                      if (!dbProxy->execQuerySqlFieldToValue(sql.toStdString(), value, e))
                      {
                          ZGLOG_ERROR(e);
                          return errorObject(ZGJson::convertToJson(e).c_str());
                      }
                      return replyObject(value.c_str());
                  });
}

ZGWebModule::Response ZGDBWebModule::on_db_list_query(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString sql, errMsg;
    if (!generateQuerySql(object, sql, errMsg))
        return errorObject(errMsg);
    return dbCall([&](std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
                  {
                      ZG6000::ErrorInfo e;
                      ZG6000::StringList listValue;
                      if (!dbProxy->execQuerySqlColToList(sql.toStdString(), listValue, e))
                      {
                          ZGLOG_ERROR(e);
                          return errorObject(ZGJson::convertToJson(e).c_str());
                      }
                      return replyObject(stringListToArray(listValue));
                  });
}

ZGWebModule::Response ZGDBWebModule::on_db_map_query(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString sql, errMsg;
    if (!generateQuerySql(object, sql, errMsg))
        return errorObject(errMsg);
    return dbCall([&](std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
                  {
                      ZG6000::ErrorInfo e;
                      ZG6000::StringMap record;
                      if (!dbProxy->execQuerySqlRowToMap(sql.toStdString(), record, e))
                      {
                          ZGLOG_ERROR(e);
                          return errorObject(ZGJson::convertToJson(e).c_str());
                      }
                      return replyObject(stringMapToObject(record));
                  });
}

ZGWebModule::Response ZGDBWebModule::on_db_list_list_query(const QString& clientID,
                                                           const QVariantMap& headers,
                                                           const QJsonValue& param,
                                                           const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString sql, errMsg;
    if (!generateQuerySql(object, sql, errMsg))
        return errorObject(errMsg);
    return dbCall([&](std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
                  {
                      ZG6000::ErrorInfo e;
                      ZG6000::ListStringList listRecord;
                      if (!dbProxy->execQuerySqlToListList(sql.toStdString(), listRecord, e))
                      {
                          ZGLOG_ERROR(e);
                          return errorObject(ZGJson::convertToJson(e).c_str());
                      }
                      return replyObject(listStringListToArray(listRecord));
                  });
}

ZGWebModule::Response ZGDBWebModule::on_db_list_map_query(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString sql, errMsg;
    if (!generateQuerySql(object, sql, errMsg))
        return errorObject(errMsg);
    return dbCall([&](std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
                  {
                      ZG6000::ErrorInfo e;
                      ZG6000::ListStringMap listRecord;
                      if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listRecord, e))
                      {
                          ZGLOG_ERROR(e);
                          return errorObject(ZGJson::convertToJson(e).c_str());
                      }
                      return replyObject(listStringMapToArray(listRecord));
                  });
}

ZGWebModule::Response ZGDBWebModule::on_db_map_map_query(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString sql, errMsg;
    if (!generateQuerySql(object, sql, errMsg))
        return errorObject(errMsg);
    return dbCall([&](std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
                  {
                      ZG6000::ErrorInfo e;
                      ZG6000::MapStringMap mapRecord;
                      if (!dbProxy->execQuerySqlToMapMap(sql.toStdString(), mapRecord, e))
                      {
                          ZGLOG_ERROR(e);
                          return errorObject(ZGJson::convertToJson(e).c_str());
                      }
                      return replyObject(mapStringMapToObject(mapRecord));
                  });
}

ZGWebModule::Response ZGDBWebModule::on_db_update_batch(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QStringList sqlList;
    QString errMsg;
    if (!generateUpdateSqlBatch(object, sqlList, errMsg))
        return errorObject(errMsg);

    return dbCall([&](std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
                  {
                      ZG6000::ErrorInfo e;
                      ZG6000::StringList sqlStdList;
                      // 转换为std::string列表
                      for (const auto& sql : sqlList)
                      {
                          sqlStdList.push_back(sql.toStdString());
                      }
                      if (!dbProxy->execBatchSql(sqlStdList, e))
                      {
                          ZGLOG_ERROR(e);
                          return errorObject(ZGJson::convertToJson(e).c_str());
                      }
                      return replyObject(QJsonObject{{"success", true}, {"count", sqlList.size()}});
                  });
}

ZGWebModule::Response ZGDBWebModule::on_db_insert_batch(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QStringList sqlList;
    QString errMsg;
    if (!generateInsertSqlBatch(object, sqlList, errMsg))
        return errorObject(errMsg);

    return dbCall([&](std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
                  {
                      ZG6000::ErrorInfo e;
                      ZG6000::StringList sqlStdList;
                      for (const auto& sql : sqlList)
                      {
                          sqlStdList.push_back(sql.toStdString());
                      }

                      if (!dbProxy->execBatchSql(sqlStdList, e))
                      {
                          ZGLOG_ERROR(e);
                          return errorObject(ZGJson::convertToJson(e).c_str());
                      }
                      return replyObject(QJsonObject{{"success", true}, {"count", sqlList.size()}});
                  });
}

ZGWebModule::Response ZGDBWebModule::on_db_delete_batch(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QStringList sqlList;
    QString errMsg;
    if (!generateDeleteSqlBatch(object, sqlList, errMsg))
        return errorObject(errMsg);

    return dbCall([&](std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
                  {
                      ZG6000::ErrorInfo e;
                      ZG6000::StringList sqlStdList;
                      for (const auto& sql : sqlList)
                      {
                          sqlStdList.push_back(sql.toStdString());
                      }

                      if (!dbProxy->execBatchSql(sqlStdList, e))
                      {
                          ZGLOG_ERROR(e);
                          return errorObject(ZGJson::convertToJson(e).c_str());
                      }
                      return replyObject(QJsonObject{{"success", true}, {"count", sqlList.size()}});
                  });
}

ZGWebModule::Response ZGDBWebModule::on_db_uuid_create(const QString &clientID, const QVariantMap &headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    int count = param.toVariant().toString().toInt();
    if (count <= 0)
        return errorObject("Invalid count.");
    ZG6000::ErrorInfo e;
    ZG6000::StringList uuids;
    return dbCall([&](std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
                  {
                      if (!dbProxy->createUUIDs(count, uuids, e))
                      {
                          ZGLOG_ERROR(e);
                          return errorObject(ZGJson::convertToJson(e).c_str());
                      }
                      return replyObject(stringListToArray(uuids));
                  });
}

QString parseCondition(const QJsonValue& condition)
{
    if (!condition.isObject())
        return "";
    const auto& condObj = condition.toObject();
    // 检查对象中是否存在"operator"字段，没有则返回空字符串
    if (!condObj.contains("operator"))
        return "";
    const auto& op = condObj["operator"].toString();
    // 如果op为AND或者OR，表示是逻辑组
    if (op == "AND" || op == "OR")
    {
        if (!condObj.contains("conditions"))
            return "";
        const auto& conditions = condObj["conditions"].toArray();
        QStringList conditionList;
        for (auto subCond : conditions)
        {
            QString parsedCond = parseCondition(subCond);
            if (!parsedCond.isEmpty())
                conditionList << parsedCond;
        }
        return "(" + conditionList.join(" " + op + " ") + ")";
    }
    // 如果是其他操作符，代表为基本条件，组合为基本条件返回
    if (condObj.contains("field") && condObj.contains("value"))
    {
        QString field = condObj["field"].toString();
        QString value = condObj["value"].toVariant().toString();
        return QString("%1 %2 '%3'").arg(field).arg(op).arg(value);
    }
    return "";
}

bool ZGDBWebModule::generateQuerySql(const QJsonObject& object,
                                     QString& sql,
                                     QString& errMsg)
{
    // 判断json对象中是否包含tables参数
    if (!object.contains("tables"))
    {
        errMsg = "Can't find tables param.";
        return false;
    }

    QJsonArray tableArray = object["tables"].toArray();
    // 判断tables参数是否为空
    if (tableArray.isEmpty())
    {
        errMsg = "Empty tables param.";
        return false;
    }

    QList<QPair<QString, QString>> tables;
    QStringList fieldsList;
    QStringList joinsList;

    // 遍历tables参数
    for (auto tableRef : tableArray)
    {
        const auto& tableObject = tableRef.toObject();
        // 判断table参数是否存在
        if (!tableObject.contains("table"))
        {
            errMsg = "Can't find table param.";
            return false;
        }

        const QString tableName = tableObject["table"].toString();
        QString alias = tableObject.value("alias").toString();
        tables.append({tableName, alias});

        // 判断fields参数是否存在
        if (!tableObject.contains("fields"))
        {
            errMsg = "Can't find fields param.";
            return false;
        }

        const QJsonArray fieldsArray = tableObject["fields"].toArray();
        // 遍历fields参数
        for (auto fieldRef : fieldsArray)
        {
            // 判断fields参数是否为字符串
            if (fieldRef.isString())
            {
                QString fieldName = fieldRef.toString();
                fieldsList << (alias.isEmpty() ? tableName : alias) + "." + fieldName;
            }
            // 判断fields参数是否为对象
            else if (fieldRef.isObject())
            {
                const QJsonObject fieldObject = fieldRef.toObject();
                // 遍历对象
                for (auto itField = fieldObject.begin(); itField != fieldObject.end(); ++itField)
                {
                    fieldsList << (alias.isEmpty() ? tableName : alias) + "." + itField.key() + " AS " + itField.
                                                                                                             value().toString();
                }
            }
        }

        // 判断join参数是否存在
        if (!tableObject.contains("join") && tables.size() > 1)
        {
            errMsg = "Can't find join param.";
            return false;
        }

        // 判断tables参数的长度是否大于1
        if (tables.size() > 1)
        {
            const QJsonArray joinArray = tableObject["join"].toArray();
            // 判断join参数的长度是否为2或3
            if (joinArray.size() != 2 && joinArray.size() != 3)
            {
                errMsg = "Invalid join param.";
                return false;
            }

            QString aliaTableName = alias.isEmpty() ? tableName : tableName + " AS " + alias;
            QString join;
            // 判断join参数的长度是否为3
            if (joinArray.size() == 3)
                join = QString(" %1 JOIN %2 ON %3 = %4")
                           .arg(joinArray[0].toString())
                           .arg(aliaTableName)
                           .arg(joinArray[1].toString())
                           .arg(joinArray[2].toString());
            else
                join = QString(" LEFT JOIN %2 ON %3 = %4")
                           .arg(aliaTableName)
                           .arg(joinArray[0].toString())
                           .arg(joinArray[1].toString());

            joinsList << join;
        }
    }

    // 将fieldsList中的元素用逗号连接起来
    QString fields = fieldsList.join(", ");
    qDebug() << "fields:" << fields;
    // 将joinsList中的元素用空格连接起来
    QString joins = joinsList.join(" ");

    // 判断查询主表的别名是否为空
    if (tables[0].second.isEmpty())
        sql = QString("SELECT %1 FROM %2").arg(fields).arg(tables[0].first);
    else
        sql = QString("SELECT %1 FROM %2 AS %3").arg(fields).arg(tables[0].first).arg(tables[0].second);

    sql += joins;

    // 判断json对象中是否包含conditions参数
    if (object.contains("condition"))
    {
        // 解析condition对象，生成条件字符串
        const auto& conditionRef = object["condition"];
        QString condition = parseCondition(conditionRef);
        if (!condition.isEmpty())
            sql += " WHERE " + condition;
    }

    // 判断json对象中是否包含order参数
    if (object.contains("order"))
    {
        const QJsonObject orderObject = object["order"].toObject();
        // 判断order参数中是否包含field和type参数
        if (!orderObject.contains("field") || !orderObject.contains("type"))
        {
            errMsg = "Invalid order param.";
            return false;
        }

        const QString orderField = orderObject["field"].toString();
        const QString orderType = orderObject["type"].toString();

        // 判断orderType是否为ASC或DESC
        if (orderType != "ASC" && orderType != "DESC")
        {
            errMsg = "Invalid order type.";
            return false;
        }

        sql += QString(" ORDER BY %1 %2").arg(orderField).arg(orderType);
    }

    // 判断json对象中是否包含pagination参数
    if (object.contains("pagination"))
    {
        const QJsonObject paginationObject = object["pagination"].toObject();
        // 判断pagination参数中是否包含page和pageSize参数
        if (!paginationObject.contains("page") || !paginationObject.contains("pageSize"))
        {
            errMsg = "Invalid pagination param.";
            return false;
        }

        int page = paginationObject["page"].toInt();
        int pageSize = paginationObject["pageSize"].toInt();

        // 判断page和pageSize是否大于0
        if (page <= 0 || pageSize <= 0)
        {
            errMsg = "Invalid page or pageSize value.";
            return false;
        }

        int offset = (page - 1) * pageSize;

        // 判断databaseType是否为mysql或sqlserver
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
        {
            sql += QString(" LIMIT %1, %2").arg(offset).arg(pageSize);
        }
        else if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
        {
            sql += QString(" OFFSET %1 ROWS FETCH NEXT %2 ROWS ONLY").arg(offset).arg(pageSize);
        }
        else
        {
            errMsg = "Unsupported database type.";
            return false;
        }
    }

    return true;
}

bool ZGDBWebModule::generateUpdateSqlBatch(const QJsonObject& object,
                                           QStringList& sqlList,
                                           QString& errMsg)
{
    if (!object.contains("updates"))
    {
        errMsg = "Can't find updates param.";
        return false;
    }

    QJsonArray updatesArray = object["updates"].toArray();
    if (updatesArray.isEmpty())
    {
        errMsg = "Empty updates array.";
        return false;
    }

    // 遍历每个更新请求
    for (auto updateRef : updatesArray)
    {
        const QJsonObject updateObj = updateRef.toObject();

        // 检查表名
        if (!updateObj.contains("table"))
        {
            errMsg = "Can't find table param.";
            return false;
        }

        QString tableName = updateObj["table"].toString();
        if (tableName.isEmpty())
        {
            errMsg = "Empty table name.";
            return false;
        }

        // 检查更新字段
        if (!updateObj.contains("sets"))
        {
            errMsg = "Can't find sets param.";
            return false;
        }

        QJsonArray setsArray = updateObj["sets"].toArray();
        if (setsArray.isEmpty())
        {
            errMsg = "Empty sets param.";
            return false;
        }

        QStringList setsList;
        // 遍历设置字段
        for (auto setRef : setsArray)
        {
            const QJsonObject setObject = setRef.toObject();
            if (!setObject.contains("field") || !setObject.contains("value"))
            {
                errMsg = "Invalid set param.";
                return false;
            }

            const QString field = setObject["field"].toString();
            const QVariant value = setObject["value"].toVariant();

            // 根据值类型处理SQL值格式
            if (value.typeId() == QMetaType::QString)
            {
                setsList << QString("%1 = '%2'").arg(field).arg(value.toString());
            }
            else if (value.isNull())
            {
                setsList << QString("%1 = NULL").arg(field);
            }
            else
            {
                setsList << QString("%1 = %2").arg(field).arg(value.toString());
            }
        }

        // 构造UPDATE语句
        QString sql = QString("UPDATE %1 SET %2").arg(tableName).arg(setsList.join(", "));

        // 添加WHERE条件
        if (updateObj.contains("condition"))
        {
            const auto& conditionRef = updateObj["condition"];
            QString condition = parseCondition(conditionRef);
            if (!condition.isEmpty())
                sql += " WHERE " + condition;
        }

        // 将SQL语句添加到结果列表
        sqlList << sql;
    }

    return true;
}

bool ZGDBWebModule::generateInsertSqlBatch(const QJsonObject& object,
                                           QStringList& sqlList,
                                           QString& errMsg)
{
    if (!object.contains("inserts"))
    {
        errMsg = "Can't find inserts param.";
        return false;
    }

    QJsonArray insertsArray = object["inserts"].toArray();
    if (insertsArray.isEmpty())
    {
        errMsg = "Empty inserts array.";
        return false;
    }

    // 遍历每个插入请求
    for (auto insertRef : insertsArray)
    {
        const QJsonObject insertObj = insertRef.toObject();

        // 检查表名
        if (!insertObj.contains("table"))
        {
            errMsg = "Can't find table param.";
            return false;
        }

        QString tableName = insertObj["table"].toString();
        if (tableName.isEmpty())
        {
            errMsg = "Empty table name.";
            return false;
        }

        // 检查字段列表
        if (!insertObj.contains("fields"))
        {
            errMsg = "Can't find fields param.";
            return false;
        }

        QJsonArray fieldsArray = insertObj["fields"].toArray();
        if (fieldsArray.isEmpty())
        {
            errMsg = "Empty fields param.";
            return false;
        }

        QStringList fieldNames;
        for (const auto& field : fieldsArray)
        {
            fieldNames << field.toString();
        }

        // 检查值列表
        if (!insertObj.contains("values"))
        {
            errMsg = "Can't find values param.";
            return false;
        }

        QJsonArray valuesArray = insertObj["values"].toArray();
        if (valuesArray.isEmpty())
        {
            errMsg = "Empty values param.";
            return false;
        }

        // 遍历每条记录
        for (const auto& valueRowRef : valuesArray)
        {
            QJsonArray valueRow = valueRowRef.toArray();

            // 检查值列表长度是否与字段列表一致
            if (valueRow.size() != fieldNames.size())
            {
                errMsg = "Fields and values count mismatch.";
                return false;
            }

            QStringList valueStrList;
            for (const auto& value : valueRow)
            {
                if (value.isNull())
                {
                    valueStrList << "NULL";
                }
                else if (value.isString())
                {
                    valueStrList << QString("'%1'").arg(value.toString());
                }
                else
                {
                    valueStrList << value.toVariant().toString();
                }
            }

            // 构造INSERT语句
            QString sql = QString("INSERT INTO %1 (%2) VALUES (%3)")
                              .arg(tableName)
                              .arg(fieldNames.join(", "))
                              .arg(valueStrList.join(", "));

            // 将SQL语句添加到结果列表
            sqlList << sql;
        }
    }

    return true;
}

bool ZGDBWebModule::generateDeleteSqlBatch(const QJsonObject& object,
                                           QStringList& sqlList,
                                           QString& errMsg)
{
    if (!object.contains("deletes"))
    {
        errMsg = "Can't find deletes param.";
        return false;
    }

    QJsonArray deletesArray = object["deletes"].toArray();
    if (deletesArray.isEmpty())
    {
        errMsg = "Empty deletes array.";
        return false;
    }

    // 遍历每个删除请求
    for (auto deleteRef : deletesArray)
    {
        const QJsonObject deleteObj = deleteRef.toObject();

        // 检查表名
        if (!deleteObj.contains("table"))
        {
            errMsg = "Can't find table param.";
            return false;
        }

        QString tableName = deleteObj["table"].toString();
        if (tableName.isEmpty())
        {
            errMsg = "Empty table name.";
            return false;
        }

        // 构造DELETE语句
        QString sql = QString("DELETE FROM %1").arg(tableName);

        // 添加WHERE条件
        if (deleteObj.contains("condition"))
        {
            const auto& conditionRef = deleteObj["condition"];
            QString condition = parseCondition(conditionRef);
            if (!condition.isEmpty())
                sql += " WHERE " + condition;
        }

        // 将SQL语句添加到结果列表
        sqlList << sql;
    }

    return true;
}

ZGWebModule::Response ZGDBWebModule::dbCall(
    std::function<ZGWebModule::Response(const std::shared_ptr<ZG6000::ZGSPDBDataPrx>)> func)
{
    auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
    if (dbProxy == nullptr)
        return errorObject("invalid db proxy.");
    try
    {
        return func(dbProxy);
    }
    catch (const Ice::Exception& e)
    {
        return errorObject(e.what());
    }
}

bool ZGDBWebModule::initialize()
{
    return true;
}

QString ZGDBWebModule::prefix()
{
    return "db";
}


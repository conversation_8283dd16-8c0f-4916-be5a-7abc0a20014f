// Copyright (c) 2025 Company Name. All rights reserved.
// Author: <PERSON>
// Date: 2025-01-22
// Description: 断电任务管理类
// Version: 1.0

#include "ZGOPTaskOutageMng.h"
#include "NodeNetwork.h"
#include <exception>
#include <QtConcurrent>
#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "ZGSecure.h"
#include "zgerror/ZGOPTaskOutageError.h"

bool checkDifferenceLessThan(const std::vector<double>& arr,
                             int threshold)
{
    if (arr.size() <= 1)
    {
        return true;
    }

    // 使用std::min_max_element来找到最小值和最大值
    auto minMaxPair = std::minmax_element(arr.begin(), arr.end());
    // 获取最小值和最大值
    int minValue = *minMaxPair.first;
    int maxValue = *minMaxPair.second;

    return (maxValue - minValue) <= threshold;
}

namespace ZG6000
{
    ZGOPTaskOutageMng* ZGOPTaskOutageMng::instance()
    {
        if (g_pInstance == nullptr)
        {
            g_pInstance = new ZGOPTaskOutageMng;
        }
        return g_pInstance;
    }

    void ZGOPTaskOutageMng::init()
    {
        ZGLOG_TRACE("initEvents");
        // 初始化事件
        initEvents();
        ZGLOG_TRACE("initServerInstConfig");
        // 初始化服务器实例配置
        initServerInstConfig();
        // 初始化规则变量
        initVariable();
        // 初始化服务器实例信息
        while (!initServerInstInfo())
        {
            ZGLOG_ERROR("initServerInstInfo error.");
            QThread::msleep(m_initInterval * 1000);
        }
        QThread::msleep(QRandomGenerator::global()->bounded(5, 10));
        while (!initKey())
        {
            ZGLOG_ERROR("initKey error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initParams())
        {
            ZGLOG_ERROR("initParams error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initNodeNetwork())
        {
            ZGLOG_ERROR("initNodeNetwork error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initRedisClient())
        {
            ZGLOG_ERROR("initRedisClient error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initMqttClient())
        {
            ZGLOG_ERROR("initMqttClient error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initLanguage())
        {
            ZGLOG_ERROR("initLanguage error.");
            QThread::msleep(m_initInterval * 1000);
        }
        m_initialized = true;
        ZGLOG_TRACE("begin delay 30 seconds");
        QTimer::singleShot(30000, &m_checkTimer, [this]
        {
            ZGLOG_TRACE("start timer");
            m_checkTimer.start(1000);
        });
        // m_checkTimer.start(1000);
        ZGLOG_INFO("ZGOPTaskOutage init finished.");
    }

    bool ZGOPTaskOutageMng::checkState()
    {
        return m_initialized;
    }

    void ZGOPTaskOutageMng::dispatchData(std::string tableName,
                                         std::string oper,
                                         std::string reason,
                                         std::string time,
                                         ListRecord listRecord,
                                         const Ice::Current& current)
    {
        if (!m_initialized)
        {
            return;
        }
        if (reason != "change")
        {
            return;
        }
        if (oper == "update")
        {
            for (const auto& record : listRecord)
            {
                if (tableName == "op_param_task")
                {
                    processTaskChange(record);
                }
                if (tableName == "op_param_outage_task")
                {
                    processOutageTaskChange(record);
                }
                if (tableName == "op_param_outage_task_device")
                {
                    processOutageTaskDeviceChange(record);
                }
                if (tableName == "op_param_outage_task_user")
                {
                    processOutageTaskUserChange(record);
                }
            }
        }
        if (oper == "insert")
        {
            for (const auto& record : listRecord)
            {
                if (tableName == "op_param_task")
                {
                    processTaskAdd(record);
                }
            }
        }
        if (oper == "delete")
        {
            for (const auto& record : listRecord)
            {
                if (tableName == "op_param_task")
                {
                    processTaskDelete(record);
                }
            }
        }
    }

    bool ZGOPTaskOutageMng::deleteTask(std::string taskID,
                                       StringMap param,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString("SELECT outageTaskTypeID FROM op_param_outage_task WHERE id = '%1'").arg(taskID.c_str());
        std::string outageTaskTypeID;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), outageTaskTypeID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取断电任务类型失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 如果是典型任务，直接对任务进行初始化
        if (outageTaskTypeID == "ZG_OTT_TYPICAL")
        {
            return initTask(taskID, e);
        }
        if (!deleteTask(taskID, e))
            return false;
        return true;
    }

    bool ZGOPTaskOutageMng::getTaskList(StringMap param,
                                        ListStringMap& listTask,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
    {
        // 生成查询SQL
        std::string condition = param.find("condition") != param.end() ? param["condition"] : "1=1";
        std::string orderType = param.find("order") != param.end() ? param["order"] : "ASC";
        std::string orderField = param.find("sort") != param.end() ? param["sort"] : "a.id";
        std::string offset = param.find("offset") != param.end() ? param["offset"] : "0";
        std::string limit = param.find("limit") != param.end() ? param["limit"] : "1000";
        // 根据数据库类型生成不同的SQL
        QString addition;
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
        {
            addition = QString(" ORDER BY %1 %2 LIMIT %3, %4").arg(orderField.c_str())
                                                              .arg(orderType.c_str()).arg(offset.c_str()).arg(
                                                                  limit.c_str());
        }
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
        {
            addition = QString(" ORDER BY %1 %2 OFFSET %3 ROWS FETCH NEXT %4 ROWS ONLY").arg(orderField.c_str())
                .arg(orderType.c_str())
                .arg(offset.c_str())
                .arg(limit.c_str());
        }
        QString sql = QString("SELECT * FROM op_param_task a "
            "WHERE %1").arg(condition.c_str()) + addition;
        auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
        if (dbProxy == nullptr)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取数据服务代理对象失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listTask, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            for (auto& task : listTask)
            {
                std::string operUserName;
                task["rtTaskStageName"] = ZGUtils::getName(m_mapTaskStage, task["rtTaskStageID"], "name");
                task["rtTaskStageNameL2"] = ZGUtils::getName(m_mapTaskStage, task["rtTaskStageID"], "nameL2");
                task["rtTaskStateName"] = ZGUtils::getName(m_mapTaskState, task["rtTaskStateID"], "name");
                task["rtTaskStateNameL2"] = ZGUtils::getName(m_mapTaskState, task["rtTaskStateID"], "nameL2");
                ZG6000::StringMap outage;
                sql = QString(
                        "SELECT outageTypeID, outageTaskTypeID, rtOutageStageID FROM op_param_outage_task WHERE id = '%1'")
                    .arg(task["id"].c_str());
                if (!dbProxy->execQuerySqlRowToMap(sql.toStdString(), outage, e))
                {
                    ZGLOG_ERROR(e);
                    return false;
                }
                std::string createUserName;
                if (!task["rtCreateUserID"].empty())
                    ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtCreateUserID"], "name", createUserName);
                task["rtCreateUserName"] = createUserName;
                task["outageTypeID"] = outage["outageTypeID"];
                task["outageTypeName"] = ZGUtils::getName(m_mapOutageType, outage["outageTypeID"], "name");
                task["outageTypeNameL2"] = ZGUtils::getName(m_mapOutageType, outage["outageTypeID"], "nameL2");
                task["outageTaskTypeID"] = outage["outageTaskTypeID"];
                task["rtOutageStageID"] = outage["rtOutageStageID"];
                auto pair = m_mapOutageStage.find(outage["outageTypeID"]);
                if (pair != m_mapOutageStage.end())
                {
                    auto outageStageName = pair->second;
                    task["rtOutageStageName"] = outageStageName["name"];
                    task["rtOutageStageNameL2"] = outageStageName["nameL2"];
                }
            }
            return true;
        }
        catch (const Ice::Exception& ie)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ie.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::startTask(std::string taskID,
                                      StringMap param,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        QDateTime currentTime = QDateTime::currentDateTime();
        if (!saveTask(taskID, currentTime, e))
        {
            ZGLOG_ERROR(QStringLiteral("保存任务失败"));
            return false;
        }
        StringMap taskParam;
        taskParam["id"] = taskID;
        taskParam["rtTaskStageID"] = "ZG_TS_EXECUTE"; // 任务阶段为执行阶段
        taskParam["rtTaskStateID"] = "ZG_TS_EXECUTING";
        taskParam["rtExecStartTime"] = ZGUtils::DateTimeToString(currentTime, true).toStdString();
        StringMap outageParam;
        outageParam["id"] = taskID;
        outageParam["rtOutageStageID"] = "ZG_OS_OUTAGE"; // 设置为断电阶段
        outageParam["rtOutageStateID"] = "ZG_OS_NORMAL";
        StringList listSql;
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskParam));
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task", outageParam));
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("初始化任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::abolishTask(std::string taskID,
                                        StringMap param,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 切换到终止状态
        if (!switchToAbortState(taskID, e))
        {
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::confirmTask(std::string taskID,
                                        StringMap param,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 获取任务阶段
        QString sql = QString("SELECT rtTaskStageID FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        std::string taskStageID;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), taskStageID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务阶段失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringList listSql;
        // 如果为创建阶段，将任务状态设置为执行准备状态
        if (taskStageID == "ZG_TS_CREATE")
        {
            QDateTime currentTime = QDateTime::currentDateTime();
            if (!saveTask(taskID, currentTime, e))
            {
                ZGLOG_ERROR(QStringLiteral("保存任务失败"));
                return false;
            }
            StringMap taskParam;
            taskParam["id"] = taskID;
            taskParam["rtTaskStageID"] = "ZG_TS_EXECUTE";
            taskParam["rtTaskStateID"] = "ZG_TS_EXECUTING";
            taskParam["rtExecStartTime"] = ZGUtils::DateTimeToString(currentTime, true).toStdString();
            StringMap outageParam;
            outageParam["id"] = taskID;
            outageParam["rtOutageStageID"] = "ZG_OS_OUTAGE"; // 设置为断电阶段
            outageParam["rtOutageStateID"] = "ZG_OS_NORMAL";
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskParam));
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task", outageParam));
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("确认任务失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        // 如果为执行阶段，执行完成后进入任务归档阶段
        if (taskStageID == "ZG_TS_EXECUTE")
        {
            // 检查隔离开关状态是否为未锁定
            if (!checkDeviceTypeState(taskID, "ZG_DT_DISCONNECTOR", "ZG_DOD_NORMAL", e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            // 检查隔离开关的位置状态是否与目标状态一致
            if (!checkDeviceTypeDstState(taskID, "ZG_DT_DISCONNECTOR", "Pos", e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            // 保存历史任务
            std::string year;
            if (!getHistorySaveYear(taskID, year, e))
            {
                ZGLOG_ERROR(QStringLiteral("获取任务保存年份失败"));
                return false;
            }
            const auto& historyOutageTable = QString("op_his_outage_task_%1").arg(year.c_str()).toStdString();
            StringMap hisOt;
            hisOt["id"] = taskID;
            hisOt["rtExecEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString();
            if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql(historyOutageTable, hisOt), true))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("保存历史任务失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& event = ZGUtils::languageString(firstLanguage(), "finishTask");
            const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "finishTask");
            // 保存事件
            if (!saveEvent(taskID, "", event, eventL2, e, current))
            {
                ZGLOG_ERROR(e);
            }
            // 获取断电任务类型
            sql = QString("SELECT outageTaskTypeID FROM op_param_outage_task WHERE id = '%1'").arg(taskID.c_str());
            std::string outageTaskTypeID;
            if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), outageTaskTypeID))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("获取断电任务类型失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            // 如果为典型任务，将任务初始化
            if (outageTaskTypeID == "ZG_OTT_TYPICAL")
            {
                return initTask(taskID, e);
            }
            // 删除任务
            return deleteTask(taskID, {}, e, current);
        }
        return true;
    }

    bool ZGOPTaskOutageMng::cancelTask(std::string taskID,
                                       StringMap param,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        QString sql = QString("SELECT rtOutageStageID FROM op_param_outage_task WHERE id = '%1'").arg(taskID.c_str());
        std::string rtOutageStageID;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtOutageStageID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务阶段失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 如果已经进入了解除断电阶段，不能取消任务
        if (rtOutageStageID == "ZG_OS_RESTORE")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            auto pair = m_mapOutageStage.find(rtOutageStageID);
            if (pair == m_mapOutageStage.end())
            {
                e.errDetail = QStringLiteral("任务已进入解除断电阶段，不能取消").toStdString();
            }
            else
            {
                auto outageStageName = pair->second;
                e.errDetail = QStringLiteral("任务已进入%1阶段，不能取消").arg(outageStageName["name"].c_str()).toStdString();
            }
            ZGLOG_ERROR(e);
            return false;
        }
        // 如果在工作许可阶段或测试许可阶段取消任务，需要验证OTP
        if (rtOutageStageID == "ZG_OS_PTW" || rtOutageStageID == "ZG_OS_CANCEL_SFT")
        {
            if (!param.empty())
            {
                if (!checkTaskOTP(taskID, param, e))
                {
                    ZGLOG_ERROR(QStringLiteral("验证任务OTP失败"));
                    return false;
                }
            }
        }
        // 切换到移除接地阶段
        if (!switchToRemoveGroundStage(taskID, e))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            auto pair = m_mapOutageStage.find("ZG_OS_REMOVE_GROUND");
            if (pair == m_mapOutageStage.end())
            {
                e.errDetail = QStringLiteral("切换到移除接地阶段失败").toStdString();
            }
            else
            {
                auto outageStageName = pair->second;
                e.errDetail = QStringLiteral("切换到%1阶段失败").arg(outageStageName["name"].c_str()).toStdString();
            }
            ZGLOG_ERROR(e);
            return false;
        }
        // 向任务用户发送取消任务通知
        StringList listMobileNumber;
        sql = QString("SELECT mobileNumber FROM op_param_outage_task_user WHERE taskID = '%1'").arg(taskID.c_str());
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listMobileNumber))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取用户手机号码失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        std::string taskName;
        ZGProxyCommon::getDataByField("op_param_task", taskID, "name", taskName);
        QString cancelMessage = QStringLiteral("【香港地铁】您参与的任务【%1】已被取消，请知悉。").arg(taskName.c_str());
        for (auto& mobileNumber : listMobileNumber)
        {
            if (mobileNumber.empty())
            {
                continue;
            }
            if (!sendSMSMessage(mobileNumber, cancelMessage.toStdString(), e))
            {
                ZGLOG_ERROR(e);
                ZGLOG_ERROR(QStringLiteral("向%1发送任务取消通知失败").arg(mobileNumber.c_str()));
            }
        }
        // 生成事件
        const auto& event = ZGUtils::languageString(firstLanguage(), "cancelTask");
        const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "cancelTask");
        if (!saveEvent(taskID, "", event, eventL2, e, current))
        {
            ZGLOG_ERROR(e);
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getTaskInfo(std::string taskID,
                                        StringMap& head,
                                        ListStringMap& devices,
                                        ListStringMap& users,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 获取任务信息
        if (!getOutageHead(taskID, head, e))
        {
            return false;
        }
        if (!getOutageDevices(taskID, devices, e))
        {
            return false;
        }
        if (!getOutageUsers(taskID, users, e))
        {
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::createTask(StringMap head,
                                       ListStringMap devices,
                                       ListStringMap users,
                                       std::string& taskID,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
    {
        auto it = head.find("outageTaskTypeID");
        if (it == head.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务类型为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 典型任务与其他任务的创建流程不同，分别处理
        if (it->second == "ZG_OTT_TYPICAL")
        {
            return createTypicalTask(head, devices, users, taskID, e);
        }
        return createOtherTask(head, devices, users, taskID, e);
    }

    bool ZGOPTaskOutageMng::editTask(std::string taskID,
                                     StringMap head,
                                     ListStringMap devices,
                                     ListStringMap users,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证设备参数
        if (!checkDevicesParam(devices, e))
        {
            return false;
        }
        // 验证用户参数
        if (!checkUsersParam(users, e))
        {
            return false;
        }
        // 验证用户是否相同
        if (!checkSameUser(head, users, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        // 验证是否为创建阶段
        if (!checkTaskStage(taskID, "ZG_TS_CREATE", e))
        {
            return false;
        }
        StringList listSql;
        listSql.push_back(
            QString("DELETE FROM op_param_outage_task_device WHERE taskID = '%1'").arg(taskID.c_str()).toStdString());
        listSql.push_back(
            QString("DELETE FROM op_param_outage_task_user WHERE taskID = '%1'").arg(taskID.c_str()).toStdString());
        try
        {
            StringMap taskParam, outageParam;
            taskParam["id"] = taskID;
            taskParam["rtStartTime"] = ZGUtils::get(head, "rtStartTime");
            taskParam["rtEndTime"] = ZGUtils::get(head, "rtEndTime");
            outageParam["id"] = taskID;
            outageParam["rtModifyTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString();
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskParam));
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task", outageParam));
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
        // 生成设备参数
        if (!generateDevicesParam(listSql, taskID, devices))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("编辑任务设备参数出错").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 生成用户参数
        if (!generateUsersParam(listSql, taskID, users))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("编辑任务用户参数出错").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("编辑任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::moveTask(std::string taskID,
                                     ListStringMap oldUsers,
                                     ListStringMap newUsers,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证用户参数
        if (oldUsers.empty() || newUsers.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务用户为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            StringMap otp;
            StringList listMobileNumber, listNewMobileNumber;
            for (const auto& oldUser : oldUsers)
            {
                const auto& mobileNumber = ZGUtils::get(oldUser, "mobileNumber");
                listMobileNumber.push_back(mobileNumber);
                const auto& rtOTP = ZGUtils::get(oldUser, "rtOTP");
                otp[mobileNumber] = rtOTP;
            }
            for (auto& newUser : newUsers)
            {
                const auto& mobileNumber = ZGUtils::get(newUser, "mobileNumber");
                listNewMobileNumber.push_back(mobileNumber);
                // 为新CP生成OTP
                const auto& originOTP = generateOTP();
                // 对OTP进行加密
                std::string encryptedOTP;
                std::string errorString;
                if (!encryptOTP(originOTP, encryptedOTP, errorString))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("加密OTP失败").toStdString();
                    e.errDesc = errorString;
                    ZGLOG_ERROR(e);
                    return false;
                }
                newUser["rtOTP"] = encryptedOTP;
            }
            // 检查旧CP提供的OTP是否正确
            if (!checkTaskOTP(taskID, otp, e))
            {
                ZGLOG_ERROR(QStringLiteral("验证任务OTP失败"));
                return false;
            }
            // 重新生成新CP记录
            StringList listSql;
            listSql.push_back(
                QString("DELETE FROM op_param_outage_task_user WHERE taskID = '%1'").arg(taskID.c_str()).toStdString());
            if (!generateUsersParam(listSql, taskID, newUsers))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("添加新用户出错").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("移动任务用户失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            // 向旧CP发送任务转移通知短信
            std::string taskName;
            ZGProxyCommon::getDataByField("op_param_task", taskID, "name", taskName);
            for (const auto& mobileNumber : listMobileNumber)
            {
                if (mobileNumber.empty())
                {
                    continue;
                }
                QDateTime currentTime = QDateTime::currentDateTime();
                QString strTime = currentTime.toString("hh:mm:ss");
                QString cancelMessage = QStringLiteral("【香港地铁】您参与的任务【%1】已被转移，请知悉。(%2)").arg(taskName.c_str()).arg(
                    strTime);
                if (!sendSMSMessage(mobileNumber, cancelMessage.toStdString(), e))
                {
                    ZGLOG_ERROR(e);
                    ZGLOG_ERROR(QStringLiteral("向%1发送任务转移通知失败").arg(mobileNumber.c_str()));
                }
            }
            // 向新CP发送OTP短信
            if (!sendOTP(taskID, listNewMobileNumber, e, current))
            {
                ZGLOG_ERROR(e);
                ZGLOG_ERROR(QStringLiteral("发送任务OTP失败"));
            }
            // 保存CP变更记录
            if (!saveTaskUserChange(taskID, oldUsers, newUsers, e))
            {
                ZGLOG_ERROR(e);
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::convertTask(std::string taskID,
                                        StringMap param,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
    {
        // 将任务转换为典型任务
        StringMap head;
        head["id"] = taskID;
        head["outageTaskTypeID"] = "ZG_OTT_TYPICAL";
        StringList listSql;
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task", head));
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("转换任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::confirmOutage(std::string taskID,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            ZGLOG_ERROR(QStringLiteral("任务'%1'不存在").arg(taskID.c_str()));
            return false;
        }
        // 获取任务头信息
        StringMap head;
        if (!getOutageHead(taskID, head, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务'%1'头信息失败").arg(taskID.c_str()));
            return false;
        }
        try
        {
            const auto& outageTypeID = ZGUtils::get(head, "outageTypeID");
            const auto& rtOutageStageID = ZGUtils::get(head, "rtOutageStageID");
            // 如果当前为终止阶段，生成历史任务事件后删除任务
            if (rtOutageStageID == "ZG_OS_ABORT")
            {
                std::string year;
                if (!getHistorySaveYear(taskID, year, e))
                {
                    ZGLOG_ERROR(QStringLiteral("获取任务保存年份失败"));
                    return false;
                }
                const auto& historyOutageTable = QString("op_his_outage_task_%1").arg(year.c_str()).toStdString();
                ZG6000::StringMap hisOt;
                hisOt["id"] = taskID;
                hisOt["rtExecEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString();
                if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql(historyOutageTable, hisOt), true))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                    e.errDetail = QStringLiteral("保存历史任务失败").toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                const auto& event = ZGUtils::languageString(firstLanguage(), "abortTask");
                const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "abortTask");
                if (!saveEvent(taskID, "", event, eventL2, e, current))
                {
                    ZGLOG_ERROR(e);
                }
                return deleteTask(taskID, {}, e, current);
            }
            // 根据任务类型和当前阶段切换到下一个阶段
            StringList listSql;
            StringMap outageParam{{"id", taskID}};
            auto pair = m_mapOutageTypeStage.find(outageTypeID);
            if (pair == m_mapOutageTypeStage.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("任务类型'%1'不存在").arg(outageTypeID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& listOutageStage = pair->second;
            auto it = std::find(listOutageStage.begin(), listOutageStage.end(), rtOutageStageID);
            if (it == listOutageStage.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("任务阶段'%1'不存在").arg(rtOutageStageID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            ++it;
            if (it == listOutageStage.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("任务无后续阶段").arg(rtOutageStageID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& nextOutageStageID = *it;
            if (nextOutageStageID == "ZG_OS_RESTORE")
            {
                return switchToRestoreStage(taskID, e);
            }
            if (nextOutageStageID == "ZG_OS_GROUND")
            {
                return switchToGroundStage(taskID, e);
            }
            if (nextOutageStageID == "ZG_OS_REMOVE_GROUND")
            {
                return switchToRemoveGroundStage(taskID, e);
            }
            if (nextOutageStageID == "ZG_OS_PTW")
            {
                return switchToPTWStage(taskID, e);
            }
            if (nextOutageStageID == "ZG_OS_REQUEST_SFT")
            {
                return switchToRequestSFTStage(taskID, e);
            }
            if (nextOutageStageID == "ZG_OS_TEST")
            {
                return switchToTestStage(taskID, e);
            }
            if (nextOutageStageID == "ZG_OS_CANCEL_SFT")
            {
                return switchToCancelSFTStage(taskID, e);
            }
            outageParam["rtOutageStageID"] = *it;
            const auto& sql = ZGUtils::generateUpdateSql("op_param_outage_task", outageParam);
            if (!ZGProxyCommon::execSql(sql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("更新任务阶段失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::sendOTP(std::string taskID,
                                    StringList listMobileNumber,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        if (listMobileNumber.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("手机号码为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& mobileNumbers = ZGUtils::join(listMobileNumber, "','");
        // 在任务用户表中查找出任务对应的用户手机号码和otp
        const auto& sql = QString(
            "SELECT id, mobileNumber, rtSafetyFileVerifyCode FROM op_param_outage_task_user WHERE taskID = '%1' "
            "AND mobileNumber IN ('%2')").arg(taskID.c_str()).arg(mobileNumbers.c_str());
        ListStringMap listUser;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listUser))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取用户信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            StringMap head;
            if (!getOutageHead(taskID, head, e))
            {
                return false;
            }
            const auto& taskName = head["name"];
            const auto& outageTypeName = head["outageTypeName"];
            const auto& rtOutageStageID = head["rtOutageStageID"];
            StringList listSql;
            // 如果是测试许可阶段，只能有一个用户持有OTP，因此需要清除其他用户的OTP
            if (rtOutageStageID != "ZG_OS_PTW")
            {
                // 清除任务中所有用户的OTP
                StringMap userParam;
                userParam["rtOTP"] = "";
                userParam["rtSavedOTP"] = "";
                userParam["sendFlag"] = "0";
                std::string condition = "taskID = '" + taskID + "'";
                listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task_user", userParam, condition));
            }
            for (auto& user : listUser)
            {
                const auto& id = ZGUtils::get(user, "id");
                // 为每个CP生成不同的OTP
                const auto& OTP = generateOTP();
                StringMap userParam;
                userParam["id"] = id;
                user["rtOTP"] = OTP;
                // 使用RSA加密OTP
                std::string encryptedOTP;
                std::string errMsg;
                if (!encryptOTP(OTP, encryptedOTP, errMsg))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                    e.errDetail = errMsg;
                    ZGLOG_ERROR(e);
                    return false;
                }
                userParam["rtOTP"] = encryptedOTP;
                userParam["sendFlag"] = "0";
                listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task_user", userParam));
            }
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("更新用户OTP失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            // 向每个CP发送OTP短信
            for (const auto& user : listUser)
            {
                const auto& mobileNumber = ZGUtils::get(user, "mobileNumber");
                const auto& OTP = ZGUtils::get(user, "rtOTP");
                const auto& safetyFileVerifyCode = ZGUtils::get(user, "rtSafetyFileVerifyCode");
                QString message = QStringLiteral("【香港地铁】断电任务(%1)的OTP为：%2，安全文件验证码为：%3。")
                                  .arg(taskName.c_str()).arg(OTP.c_str())
                                  .arg(safetyFileVerifyCode.c_str());
                if (!sendSMSMessage(mobileNumber, message.toStdString(), e))
                {
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
            // 生成事件
            const auto& event = ZGUtils::languageString(firstLanguage(), "sendSMS");
            const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "sendSMS");
            if (!saveEvent(taskID, "", event, eventL2, e, current))
            {
                ZGLOG_ERROR(e);
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::lockIsolator(std::string clientID,
                                         std::string taskID,
                                         std::string deviceID,
                                         std::string OTP,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证设备ID是否有效
        if (!checkDeviceExists(deviceID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        // 验证当前为断电阶段
        if (!checkOutageStage(taskID, "ZG_OS_OUTAGE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证断电阶段失败"));
            return false;
        }
        // 验证设备类型为隔离开关
        if (!checkDeviceType(deviceID, "ZG_DT_DISCONNECTOR", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证设备类型失败"));
            return false;
        }
        // 验证设备的当前状态与任务的目标状态一致
        if (!checkDeviceDstState(taskID, deviceID, "Pos", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证隔开开关目标状态失败"));
            return false;
        }
        // 向设备发送锁定指令，尝试锁定设备
        if (!lockDevice(clientID, taskID, deviceID, e))
        {
            ZGLOG_ERROR(QStringLiteral("锁定设备失败"));
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::unlockIsolator(std::string clientID,
                                           std::string taskID,
                                           std::string deviceID,
                                           std::string OTP,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证设备ID是否有效
        if (!checkDeviceExists(deviceID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        // 验证当前为解除断电阶段
        if (!checkOutageStage(taskID, "ZG_OS_RESTORE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证断电阶段失败"));
            return false;
        }
        // 验证设备类型为隔离开关
        if (!checkDeviceType(deviceID, "ZG_DT_DISCONNECTOR", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证设备类型失败"));
            return false;
        }
        if (!unlockDevice(clientID, taskID, deviceID, e))
        {
            ZGLOG_ERROR(QStringLiteral("解锁设备失败"));
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::lockIsolatorBatch(std::string clientID,
                                              std::string taskID,
                                              StringMap /*deviceOTP*/,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        // 验证当前为断电阶段
        if (!checkOutageStage(taskID, "ZG_OS_OUTAGE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证断电阶段失败"));
            return false;
        }
        // if (!checkDeviceTypeDstState(taskID, "ZG_DT_DISCONNECTOR", "Pos", e))
        // {
        //     ZGLOG_ERROR(QStringLiteral("验证隔开开关目标状态失败"));
        //     return false;
        // }
        QString sql = QString("SELECT a.deviceID, b.name AS deviceName FROM op_param_outage_task_device a "
                "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
                "WHERE a.taskID = '%1' AND a.rtDeviceStateID = 'ZG_DOD_NORMAL' AND b.typeID = 'ZG_DT_DISCONNECTOR' ORDER BY a.id")
            .arg(taskID.c_str());
        ListStringMap listDevice;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevice))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("查询任务隔离开关设备信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            StringList listDeviceName;
            for (const auto& device : listDevice)
            {
                const auto& deviceID = ZGUtils::get(device, "deviceID");
                // 获取隔离开关设备的当前状态，并与目标状态进行比较，如果不一致，则跳过该设备
                std::string pos;
                if (!ZGProxyCommon::getPropertyValue(deviceID, "Pos", pos, e))
                {
                    ZGLOG_ERROR(QStringLiteral("获取设备【%1】当前位置失败").arg(deviceID.c_str()));
                    continue;
                }
                // 从任务设备表中获取该设备的目标状态，并进行比较
                sql = QString("SELECT dstState FROM op_param_outage_task_device WHERE taskID = '%1' "
                    "AND deviceID = '%2'").arg(taskID.c_str()).arg(deviceID.c_str());
                std::string dstState;
                if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), dstState))
                {
                    ZGLOG_ERROR(QStringLiteral("获取设备【%1】目标状态失败").arg(deviceID.c_str()));
                    continue;
                }
                if (pos != dstState)
                {
                    const auto& deviceName = ZGUtils::get(device, "deviceName");
                    listDeviceName.push_back(deviceName);
                    ZGLOG_ERROR(QStringLiteral("设备【%1】当前位置与目标位置不一致").arg(deviceID.c_str()));
                    continue;
                }
                if (!lockDevice(clientID, taskID, deviceID, e))
                {
                    const auto& deviceName = ZGUtils::get(device, "deviceName");
                    listDeviceName.push_back(deviceName);
                    ZGLOG_ERROR(QStringLiteral("锁定设备【%1】失败").arg(deviceName.c_str()));
                }
            }
            if (!listDeviceName.empty())
            {
                const auto& deviceNames = ZGUtils::join(listDeviceName, ",", "【", "】");
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("以下设备操作失败：").toStdString();
                e.errDetail += deviceNames;
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::unlockIsolatorBatch(std::string clientID,
                                                std::string taskID,
                                                StringMap deviceOTP,
                                                ErrorInfo& e,
                                                const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        // if (!checkOutageStage(taskID, "ZG_OS_RESTORE", e))
        // {
        //     ZGLOG_ERROR(QStringLiteral("验证断电阶段失败"));
        //     return false;
        // }
        // 先获取任务下的所有接地开关设备，如果设备没有解锁，返回错误
        QString sql = QString(
                "SELECT a.deviceID, b.name AS deviceName, a.rtDeviceStateID FROM op_param_outage_task_device a "
                "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
                "WHERE a.taskID = '%1' AND b.typeID = 'ZG_DT_GROUND_SWITCH' ORDER BY a.id")
            .arg(taskID.c_str());
        ListStringMap listSwitch;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listSwitch))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("查询任务接地开关设备信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            for (const auto& groundSwitch : listSwitch)
            {
                // const auto& rtDeviceStateID = ZGUtils::get(groundSwitch, "rtDeviceStateID");
                // if (rtDeviceStateID != "ZG_DOD_NORMAL")
                // {
                //     const auto& deviceName = ZGUtils::get(groundSwitch, "deviceName");
                //     e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                //     e.errDetail = QStringLiteral("接地开关【%1】未解锁").arg(deviceName.c_str()).toStdString();
                //     ZGLOG_ERROR(e);
                //     return false;
                // }
                const auto& deviceID = ZGUtils::get(groundSwitch, "deviceID");
                // 检查设备当前位置是否为断开状态，如果不是，返回错误
                StringMap propertyValues;
                if (!ZGProxyCommon::getPropertyValues(deviceID, {"LockCount", "PosClose", "PosOpen"}, propertyValues,
                    e))
                {
                    ZGLOG_ERROR(e);
                    return false;
                }
                const auto& posClose = ZGUtils::get(propertyValues, "PosClose");
                const auto& posOpen = ZGUtils::get(propertyValues, "PosOpen");
                if (posClose != "1" || posOpen != "2")
                {
                    const auto& deviceName = ZGUtils::get(groundSwitch, "deviceName");
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("接地开关【%1】未处于断开状态").arg(deviceName.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
        sql = QString("SELECT a.deviceID, b.name AS deviceName FROM op_param_outage_task_device a "
                "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
                "WHERE a.taskID = '%1' AND a.rtDeviceStateID = 'ZG_DOD_LOCKED' AND b.typeID = 'ZG_DT_DISCONNECTOR' ORDER BY a.id")
            .arg(taskID.c_str());
        ListStringMap listDevice;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevice))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("查询任务隔离开关设备信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            // 尝试解锁所有隔离开关设备
            StringList listDeviceName;
            for (const auto& device : listDevice)
            {
                const auto& deviceID = ZGUtils::get(device, "deviceID");
                if (!unlockDevice(clientID, taskID, deviceID, e))
                {
                    const auto& deviceName = ZGUtils::get(device, "deviceName");
                    listDeviceName.push_back(deviceName);
                    ZGLOG_ERROR(QStringLiteral("解锁设备【%1】失败").arg(deviceName.c_str()));
                }
            }
            if (!listDeviceName.empty())
            {
                const auto& deviceNames = ZGUtils::join(listDeviceName, ",", "【", "】");
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("以下设备操作失败：").toStdString();
                e.errDetail += deviceNames;
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::lockSwitch(std::string clientID,
                                       std::string taskID,
                                       std::string deviceID,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证设备ID是否有效
        if (!checkDeviceExists(deviceID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        // 验证设备类型为接地开关
        if (!checkDeviceType(deviceID, "ZG_DT_GROUND_SWITCH", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证设备类型失败"));
            return false;
        }
        // 验证设备的当前状态与任务的目标状态是否一致
        if ((!checkDeviceDstState(taskID, deviceID, "PosClose", e))
            || (!checkDeviceDstState(taskID, deviceID, "PosOpen", e, true)))
        {
            ZGLOG_ERROR(QStringLiteral("验证设备目标状态失败"));
            return false;
        }
        if (!lockDevice(clientID, taskID, deviceID, e))
        {
            ZGLOG_ERROR(QStringLiteral("锁定设备失败"));
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::unlockSwitch(std::string clientID,
                                         std::string taskID,
                                         std::string deviceID,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证设备ID是否有效
        if (!checkDeviceExists(deviceID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        // 验证设备类型为接地开关
        if (!checkDeviceType(deviceID, "ZG_DT_GROUND_SWITCH", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证设备类型失败"));
            return false;
        }
        // 向安全闭锁装置发送解锁命令
        if (!unlockDevice(clientID, taskID, deviceID, e))
        {
            ZGLOG_ERROR(QStringLiteral("解锁设备失败"));
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::lockSwitchBatch(std::string clientID,
                                            std::string taskID,
                                            StringMap /*deviceOTP*/,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        // 验证当前为断电阶段
        if (!checkDeviceTypeState(taskID, "ZG_DT_DISCONNECTOR", "ZG_DOD_LOCKED", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证隔离开关锁定状态失败"));
            return false;
        }
        // 获取设备下的所有接地开关设备
        QString sql = QString(
                "SELECT a.deviceID, b.name AS deviceName, a.dstState, a.rtDeviceStateID FROM op_param_outage_task_device a "
                "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
                "WHERE a.taskID = '%1' AND b.typeID = 'ZG_DT_GROUND_SWITCH' ORDER BY a.id")
            .arg(taskID.c_str());
        ListStringMap listGroundSwitch;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listGroundSwitch))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("查询任务接地开关设备信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            StringList listDeviceName;
            for (const auto& groundSwitch : listGroundSwitch)
            {
                const auto& deviceID = ZGUtils::get(groundSwitch, "deviceID");
                const auto& dstState = ZGUtils::get(groundSwitch, "dstState");
                // 比较设备当前状态与任务目标状态是否一致
                if (!compareDeviceDstState(taskID, deviceID, "PosClose", dstState, e))
                {
                    continue;
                }
                std::string openDstState = dstState == "2" ? "1" : "2";
                if (!compareDeviceDstState(taskID, deviceID, "PosOpen", openDstState, e))
                {
                    continue;
                }
                const auto& rtDeviceStateID = ZGUtils::get(groundSwitch, "rtDeviceStateID");
                // 如果设备未锁定，则尝试锁定设备
                if (rtDeviceStateID == "ZG_DOD_NORMAL")
                {
                    if (!lockDevice(clientID, taskID, deviceID, e))
                    {
                        const auto& deviceName = ZGUtils::get(groundSwitch, "deviceName");
                        listDeviceName.push_back(deviceName);
                        ZGLOG_ERROR(QStringLiteral("解锁设备'%1'失败").arg(deviceID.c_str()));
                    }
                }
            }
            if (!listDeviceName.empty())
            {
                const auto& deviceNames = ZGUtils::join(listDeviceName, ",", "【", "】");
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("以下设备操作失败：").toStdString();
                e.errDetail += deviceNames;
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::unlockSwitchBatch(std::string clientID,
                                              std::string taskID,
                                              StringMap /*deviceOTP*/,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        QString sql = QString(
                "SELECT a.deviceID, b.name AS deviceName, a.dstState, a.rtDeviceStateID FROM op_param_outage_task_device a "
                "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
                "WHERE a.taskID = '%1' AND b.typeID = 'ZG_DT_GROUND_SWITCH' ORDER BY a.id")
            .arg(taskID.c_str());
        ListStringMap listGroundSwitch;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listGroundSwitch))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("查询任务接地开关设备信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            StringList listDeviceName;
            for (const auto& groundSwitch : listGroundSwitch)
            {
                const auto& deviceID = ZGUtils::get(groundSwitch, "deviceID");
                const auto& rtDeviceStateID = ZGUtils::get(groundSwitch, "rtDeviceStateID");
                // 如果设备已锁定，则尝试解锁设备
                if (rtDeviceStateID == "ZG_DOD_LOCKED")
                {
                    if (!unlockDevice(clientID, taskID, deviceID, e))
                    {
                        const auto& deviceName = ZGUtils::get(groundSwitch, "deviceName");
                        listDeviceName.push_back(deviceName);
                        ZGLOG_ERROR(QStringLiteral("解锁设备'%1'失败").arg(deviceID.c_str()));
                        ZGLOG_ERROR(e);
                    }
                }
            }
            if (!listDeviceName.empty())
            {
                const auto& deviceNames = ZGUtils::join(listDeviceName, ",", "【", "】");
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("以下设备操作失败：").toStdString();
                e.errDetail += deviceNames;
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::closeSwitch(std::string clientID,
                                        std::string taskID,
                                        std::string deviceID,
                                        std::string OTP,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证设备ID是否有效
        if (!checkDeviceExists(deviceID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        // 检查设备类型为接地开关
        if (!checkDeviceType(deviceID, "ZG_DT_GROUND_SWITCH", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证设备类型失败"));
            return false;
        }
        // 检查设备状态是否未锁定
        if (!checkDeviceState(taskID, deviceID, "ZG_DOD_NORMAL", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证设备状态失败"));
            return false;
        }
        // 检查设备关联的安全闭锁装置状态是否正常
        if (!checkDeviceLockDeviceState(deviceID, e))
        {
            ZGLOG_ERROR(QStringLiteral("验证安全闭锁装置状态失败"));
            return false;
        }
        StringMap deviceParam;
        if (!ZGProxyCommon::getPropertyValues(deviceID, {"name", "nameL2", "appNodeID", "LockCount"}, deviceParam, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备锁定次数失败"));
            return false;
        }
        // 检查设备是否已经在其他任务中被锁定
        if (deviceParam["LockCount"] != "0")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备在其他任务中已被锁定").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 检查联锁条件
        ListStringMap listCondition;
        if (!getSwitchCloseConditions(taskID, deviceID, listCondition, e, current))
        {
            ZGLOG_ERROR(QStringLiteral("获取接地开关合闸条件失败"));
            return false;
        }
        for (auto& condition : listCondition)
        {
            const auto& expectValue = condition["expectValue"];
            const auto& actualValue = condition["actualValue"];
            if (expectValue != actualValue)
            {
                ZGLOG_ERROR(QStringLiteral("验证规则条件失败"));
                return false;
            }
        }
        // if (!checkRuleCondition(deviceID, "CMD_PosClose", "2", e))
        // {
        //     ZGLOG_ERROR(QStringLiteral("验证规则条件失败"));
        //     return false;
        // }
        // 发送合闸命令
        if (!sendCtrlCommand(clientID, deviceID, "CMD_PosClose", "2"))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("发送控制命令失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 设置接地合闸标志
        StringMap deviceState;
        deviceState["rtIsSwitchClose"] = "1";
        std::string conditions = "taskID = '" + taskID + "' AND deviceID = '" + deviceID + "'";
        if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceState, conditions)))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新设备状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 生成事件
        const auto& deviceName = deviceParam["name"];
        const auto& deviceNameL2 = deviceParam["nameL2"];
        const auto& appNodeID = deviceParam["appNodeID"];
        const auto& appNodeName = ZGUtils::getName(m_mapAppNode, appNodeID, "name");
        const auto& appNodeNameL2 = ZGUtils::getName(m_mapAppNode, appNodeID, "nameL2");
        const auto& event = ZGUtils::languageString(firstLanguage(), "close") +
            QStringLiteral("【%1】【%2】").arg(appNodeName.c_str()).arg(deviceName.c_str()).toStdString();
        const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "close") +
            QStringLiteral("【%1】【%2】").arg(appNodeNameL2.c_str()).arg(deviceNameL2.c_str()).toStdString();
        if (!saveEvent(taskID, deviceID, event, eventL2, e, current))
        {
            ZGLOG_ERROR(e);
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getOutageType(const std::string& taskID,
                                          std::string& outageType,
                                          const ErrorInfo& errorInfo)
    {
        QString sql = QString("SELECT outageTypeID FROM op_param_outage_task WHERE id = '%1'").arg(taskID.c_str());
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), outageType))
        {
            ZGLOG_ERROR(QStringLiteral("获取断电任务类型失败"));
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::openSwitch(std::string clientID,
                                       std::string taskID,
                                       std::string deviceID,
                                       std::string OTP,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证设备ID是否有效
        if (!checkDeviceExists(deviceID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        // 检查设备类型为接地开关
        if (!checkDeviceType(deviceID, "ZG_DT_GROUND_SWITCH", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证设备类型失败"));
            return false;
        }
        // 检查设备状态是否未锁定
        if (!checkDeviceState(taskID, deviceID, "ZG_DOD_NORMAL", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证设备状态失败"));
            return false;
        }
        StringMap deviceParam;
        if (!ZGProxyCommon::getPropertyValues(deviceID, {"name", "nameL2", "appNodeID", "LockCount"}, deviceParam, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备锁定次数失败"));
            return false;
        }
        // 检查设备是否已经在其他任务中被锁定
        if (deviceParam["LockCount"] != "0")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备在其他任务中已被锁定").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 检查联锁条件
        if (!checkRuleCondition(deviceID, "CMD_PosOpen", "2", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证规则条件失败"));
            return false;
        }
        // 发送分闸命令
        if (!sendCtrlCommand(clientID, deviceID, "CMD_PosOpen", "2"))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("发送控制命令失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 生成事件
        const auto& deviceName = deviceParam["name"];
        const auto& deviceNameL2 = deviceParam["nameL2"];
        const auto& appNodeID = deviceParam["appNodeID"];
        const auto& appNodeName = ZGUtils::getName(m_mapAppNode, appNodeID, "name");
        const auto& appNodeNameL2 = ZGUtils::getName(m_mapAppNode, appNodeID, "nameL2");
        const auto& event = ZGUtils::languageString(firstLanguage(), "open") +
            QStringLiteral("【%1】【%2】").arg(appNodeName.c_str()).arg(deviceName.c_str()).toStdString();
        const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "open") +
            QStringLiteral("【%1】【%2】").arg(appNodeNameL2.c_str()).arg(deviceNameL2.c_str()).toStdString();
        if (!saveEvent(taskID, deviceID, event, eventL2, e, current))
        {
            ZGLOG_ERROR(e);
        }
        return true;
    }

    bool ZGOPTaskOutageMng::closeSwitchBatch(std::string clientID,
                                             std::string taskID,
                                             StringMap deviceOTP,
                                             ErrorInfo& e,
                                             const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证客户端ID是否有效
        if (!checkClientExists(clientID, e))
        {
            return false;
        }
        // 验证任务阶段为执行阶段
        if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务阶段失败"));
            return false;
        }
        // 检查当前为接地阶段
        if (!checkOutageStage(taskID, "ZG_OS_GROUND", e))
        {
            ZGLOG_ERROR(QStringLiteral("验证断电阶段失败"));
            return false;
        }
        // if (!checkInterlockCondition(taskID, m_mapSwitchCloseCondition, e))
        // {
        //     ZGLOG_ERROR(QStringLiteral("验证联锁条件失败"));
        //     return false;
        // }
        // 获取任务设备表中所有类型为接地开关的设备
        QString sql = QString(
                "SELECT a.deviceID, b.name AS deviceName, a.rtDeviceStateID FROM op_param_outage_task_device a "
                "LEFT JOIN mp_param_device b ON a.deviceID = b.id WHERE a.taskID = '%1' AND b.typeID = 'ZG_DT_GROUND_SWITCH' ORDER BY a.id")
            .arg(taskID.c_str());
        ListStringMap listDevices;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevices))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取接地开关设备列表失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            StringList listDeviceName;
            for (const auto& device : listDevices)
            {
                const auto& deviceID = ZGUtils::get(device, "deviceID");
                const auto& deviceName = ZGUtils::get(device, "deviceName");
                ZG6000::StringMap mapValues;
                if (!ZGProxyCommon::getPropertyValues(deviceID, {"PosClose", "PosOpen", "LockState"}, mapValues, e))
                {
                    ZGLOG_ERROR(QStringLiteral("获取设备'%1'属性值失败").arg(deviceID.c_str()));
                    listDeviceName.push_back(deviceName);
                    continue;
                }
                // 检查设备当前状态是否为合位
                if (mapValues["PosClose"] == "2" && mapValues["PosOpen"] == "1")
                {
                    continue;
                }
                // 检查设备当前是否已经锁定
                if (mapValues["LockState"] == "2")
                {
                    ZGLOG_ERROR(QStringLiteral("设备'%1'控制回路已断开").arg(deviceID.c_str()));
                    listDeviceName.push_back(deviceName);
                    continue;
                }
                // 检查联锁条件
                if (!checkRuleCondition(deviceID, "CMD_PosClose", "2", e))
                {
                    ZGLOG_ERROR(QStringLiteral("验证设备'%1'规则条件失败").arg(deviceID.c_str()));
                    listDeviceName.push_back(deviceName);
                    continue;
                }
                // 发送合闸命令
                if (!sendCtrlCommand(clientID, deviceID, "CMD_PosClose", "2"))
                {
                    ZGLOG_ERROR(QStringLiteral("设备'%1'发送控制命令失败").arg(deviceID.c_str()));
                    listDeviceName.push_back(deviceName);
                }
            }
            if (!listDeviceName.empty())
            {
                const auto& deviceNames = ZGUtils::join(listDeviceName, ",", "【", "】");
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("以下设备操作失败：").toStdString();
                e.errDetail += deviceNames;
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::openSwitchBatch(std::string clientID,
                                            std::string taskID,
                                            StringMap deviceOTP,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
    {
        return false;
    }

    bool ZGOPTaskOutageMng::lockTask(std::string taskID,
                                     StringMap deviceOTP,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        // if (!checkTaskExist(taskID, e))
        //     return false;
        // // 当前任务是否处于执行阶段
        // if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        //     return false;
        // if (!checkDeviceTypeState(taskID, "ZG_DT_DISCONNECTOR", "ZG_DOD_LOCKED", e))
        //     return false;
        // if (!checkDeviceTypeState(taskID, "ZG_DT_GROUND_SWITCH", "ZG_DOD_LOCKED", e))
        //     return false;
        // // 将断电任务表中的任务状态更新为已锁定
        // StringMap taskParam;
        // taskParam["id"] = taskID;
        // taskParam["rtOutageStateID"] = "ZG_OS_LOCK";
        // const auto& sql = ZGUtils::generateUpdateSql("op_param_outage_task", taskParam);
        // if (!ZGProxyCommon::execSql(sql))
        // {
        //     e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
        //     e.errDetail = QStringLiteral("锁定任务失败").toStdString();
        //     ZGLOG_ERROR(e);
        //     return false;
        // }
        // const auto& event = ZGUtils::languageString(firstLanguage(), "lockTask");
        // const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "lockTask");
        // if (!saveEvent(taskID, "", event, eventL2, e, current))
        //     ZGLOG_ERROR(e);
        return true;
    }

    bool ZGOPTaskOutageMng::unlockTask(std::string taskID,
                                       StringMap deviceOTP,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
    {
        // if (!checkTaskExist(taskID, e))
        //     return false;
        // // 当前任务是否处于执行阶段
        // if (!checkTaskStage(taskID, "ZG_TS_EXECUTE", e))
        //     return false;
        // // 将断电任务表中的任务状态更新为已解锁
        // StringMap taskParam;
        // taskParam["id"] = taskID;
        // taskParam["rtOutageStateID"] = "ZG_OS_NORMAL";
        // if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql("op_param_outage_task", taskParam)))
        // {
        //     e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
        //     e.errDetail = QStringLiteral("解锁任务失败").toStdString();
        //     ZGLOG_ERROR(e);
        //     return false;
        // }
        // const auto& event = ZGUtils::languageString(firstLanguage(), "unlockTask");
        // const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "unlockTask");
        // if (!saveEvent(taskID, "", event, eventL2, e, current))
        //     ZGLOG_ERROR(e);
        return true;
    }

    bool ZGOPTaskOutageMng::getMonitorDevices(std::string taskID,
                                              ListStringMap& listDevices,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 获取任务设备表中指定任务的所有设备ID
        StringList listDeviceID;
        std::string errMsg;
        if (!getListDeviceFromTask(taskID, listDeviceID, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = errMsg;
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& deviceIds = ZGUtils::join(listDeviceID, ",", "'", "'");
        QString sql = QString(
            "SELECT a.dstDeviceID AS id, b.name, b.typeID, c.name AS typeName, a.srcDeviceID FROM mp_param_device_relation a "
            "LEFT JOIN mp_param_device b ON a.dstDeviceID = b.id "
            "LEFT JOIN mp_dict_device_type c ON b.typeID = c.id "
            "WHERE a.srcDeviceID IN (%1) AND a.relationTypeID = 'ZG_RT_OWNED' ORDER BY id").arg(deviceIds.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevices))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取监控设备失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::applyPTW(std::string taskID,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取断电任务失败"));
            return false;
        }
        StringList listSql;
        StringMap head;
        head["id"] = taskID;
        // 更新断电任务表中的任务状态为已申请PTW
        head["rtIsPTWFinished"] = "1";
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task", head));
        if (!listSql.empty())
        {
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("更新任务信息失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        const auto& event = ZGUtils::languageString(firstLanguage(), "applyPTW");
        const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "applyPTW");
        if (!saveEvent(taskID, "", event, eventL2, e, current))
        {
            ZGLOG_ERROR(e);
        }
        // 保存任务用户开始作业时间
        if (!saveTaskUserStartTime(taskID, e))
        {
            ZGLOG_ERROR(e);
        }
        return true;
    }

    bool ZGOPTaskOutageMng::cancelPTW(std::string taskID,
                                      StringMap OTP,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证任务OTP是否有效
        if (!checkTaskOTP(taskID, OTP, e))
        {
            return false;
        }
        const auto& event = ZGUtils::languageString(firstLanguage(), "cancelPTW");
        const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "cancelPTW");
        if (!saveEvent(taskID, "", event, eventL2, e, current))
        {
            ZGLOG_ERROR(e);
        }
        // 保存任务用户结束作业时间
        if (!saveTaskUserEndTime(taskID, e))
        {
            ZGLOG_ERROR(e);
        }
        return true;
    }

    bool ZGOPTaskOutageMng::applySFT(std::string taskID,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取断电任务失败"));
            return false;
        }
        StringList listSql;
        StringMap head;
        head["id"] = taskID;
        // 更新断电任务表中的任务状态为已申请SFT
        head["rtIsSFTFinished"] = "1";
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task", head));
        if (!listSql.empty())
        {
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("更新任务信息失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        const auto& event = ZGUtils::languageString(firstLanguage(), "applySFT");
        const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "applySFT");
        if (!saveEvent(taskID, "", event, eventL2, e, current))
        {
            ZGLOG_ERROR(e);
        }
        std::string outageTypeID;
        if (!getOutageType(taskID, outageTypeID, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取断电任务类型失败"));
            return false;
        }
        if (outageTypeID == "ZG_OT_SFT")
        {
            // 保存任务用户开始作业时间
            if (!saveTaskUserStartTime(taskID, e))
            {
                ZGLOG_ERROR(e);
            }
        }
        return true;
    }

    bool ZGOPTaskOutageMng::cancelSFT(std::string taskID,
                                      StringMap OTP,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 验证任务OTP是否有效
        if (!checkTaskOTP(taskID, OTP, e))
        {
            return false;
        }
        const auto& event = ZGUtils::languageString(firstLanguage(), "cancelSFT");
        const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "cancelSFT");
        if (!saveEvent(taskID, "", event, eventL2, e, current))
        {
            ZGLOG_ERROR(e);
        }
        // 保存任务用户结束作业时间
        if (!saveTaskUserEndTime(taskID, e))
        {
            ZGLOG_ERROR(e);
        }
        return true;
    }

    bool ZGOPTaskOutageMng::saveOTP(std::string taskID,
                                    StringMap OTP,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 保存用户当前的OTP
        StringList listSql;
        for (const auto& otp : OTP)
        {
            StringMap userParam;
            userParam["userID"] = otp.first;
            // 使用RSA加密OTP
            std::string encryptedOTP;
            std::string errMsg;
            if (!encryptOTP(otp.second, encryptedOTP, errMsg))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = errMsg;
                ZGLOG_ERROR(e);
                return false;
            }
            userParam["rtSavedOTP"] = encryptedOTP;
            std::string condition = "taskID = '" + taskID + "' AND userID = '" + otp.first + "'";
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task_user", userParam, condition));
        }
        if (!listSql.empty())
        {
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("保存OTP失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGOPTaskOutageMng::changePhone(std::string taskID,
                                        std::string oldPhoneNumber,
                                        std::string newPhoneNumber,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
    {
        // 验证任务ID是否有效
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        // 根据旧手机号找到任务用户表中的ID
        QString sql = QString("SELECT id FROM op_param_outage_task_user WHERE taskID = '%1' AND mobileNumber = '%2'").
                      arg(taskID.c_str()).arg(oldPhoneNumber.c_str());
        std::string id;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), id))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务用户ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 向旧手机号发送一条手机号已经更改的短信
        std::string taskName;
        ZGProxyCommon::getDataByField("op_param_task", taskID, "name", taskName);
        QString content = QStringLiteral("【香港地铁】您在任务【%1】中的手机号码已经更改为%2。").arg(taskName.c_str()).arg(
            newPhoneNumber.c_str());
        if (!sendSMSMessage(oldPhoneNumber, content.toStdString(), e))
        {
            ZGLOG_ERROR(QStringLiteral("向%1发送手机号码更改短信失败").arg(oldPhoneNumber.c_str()));
            return false;
        }
        // 更新任务用户表中的手机号码
        StringMap userParam;
        userParam["id"] = id;
        userParam["mobileNumber"] = newPhoneNumber;
        if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql("op_param_outage_task_user", userParam)))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新任务用户手机号码失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 调用sendOTP向新手机号发送一条OTP
        if (!sendOTP(taskID, {newPhoneNumber}, e, current))
        {
            ZGLOG_ERROR(QStringLiteral("发送OTP失败"));
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::saveEvent(std::string taskID,
                                      std::string deviceID,
                                      std::string event,
                                      std::string eventL2,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        std::string year;
        if (!getHistorySaveYear(taskID, year, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务保存年份失败"));
            return false;
        }
        QString historyTableName = QString("op_his_outage_task_event_%1").arg(year.c_str());
        std::string uuid;
        if (!ZGProxyCommon::createUUID(uuid))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("生成UUID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringMap outageAction;
        outageAction["id"] = uuid;
        outageAction["taskID"] = taskID;
        outageAction["deviceID"] = deviceID;
        std::string rtOutageStageID;
        QString sql = QString("SELECT rtOutageStageID FROM op_param_outage_task WHERE id = '%1'").arg(taskID.c_str());
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtOutageStageID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务阶段失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        outageAction["rtOutageStageID"] = rtOutageStageID;
        if (!rtOutageStageID.empty())
        {
            auto pair = m_mapOutageStage.find(rtOutageStageID);
            if (pair != m_mapOutageStage.end())
            {
                auto outageStageName = pair->second;
                outageAction["rtOutageStageName"] = outageStageName["name"];
                outageAction["rtOutageStageNameL2"] = outageStageName["nameL2"];
            }
        }
        StringMap deviceName;
        if (!deviceID.empty())
        {
            ZGProxyCommon::getDataByFields("mp_param_device", deviceID, {"name", "nameL2"}, deviceName);
        }
        outageAction["deviceName"] = deviceName["name"];
        outageAction["deviceNameL2"] = deviceName["nameL2"];
        outageAction["event"] = event;
        outageAction["eventL2"] = eventL2;
        outageAction["rtExecTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        sql = ZGUtils::generateInsertSql(historyTableName.toStdString(), outageAction).c_str();
        if (!ZGProxyCommon::execSql(sql.toStdString(), true))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("保存事件失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::unlockExternalLock(std::string clientID,
                                               std::string taskID,
                                               std::string deviceID,
                                               ErrorInfo& e,
                                               const Ice::Current& current)
    {
        // 发送解除外部闭锁命令
        if (!sendCtrlCommand(clientID, deviceID, "CMD_OutInterlockOff", "2"))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("发送控制命令失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getSwitchCloseConditions(std::string taskID,
                                                     std::string deviceID,
                                                     ListStringMap& conditions,
                                                     ErrorInfo& e,
                                                     const Ice::Current& current)
    {
        if (!checkTaskExist(taskID, e))
        {
            return false;
        }
        if (!checkDeviceExists(deviceID, e))
        {
            return false;
        }
        // 获取任务中除本设备外的所有设备及其类型
        QString sql = QString(
                          "SELECT a.deviceID, a.dstState, b.name AS deviceName, b.typeID FROM op_param_outage_task_device a "
                          "LEFT JOIN mp_param_device b ON a.deviceID = b.id WHERE a.taskID = '%1' AND a.deviceID != '%2' ORDER BY a.id")
                      .arg(taskID.c_str()).arg(deviceID.c_str());
        ListStringMap listDevices;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevices))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务设备失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 计算本接地开关要满足的规则，加入到联锁条件中
        StringList listGroundProperty;
        for (const auto& [propertyName, expectValue] : m_mapGroundDeviceRule)
        {
            listGroundProperty.push_back(propertyName);
        }
        // 获取设备属性值
        MapStringMap mapProperties;
        if (!ZGProxyCommon::getProperties(deviceID, listGroundProperty, mapProperties, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备'%1'属性值失败").arg(deviceID.c_str()));
            return false;
        }
        // 与m_mapGroundDeviceRule中的预期值进行比较
        for (const auto& [propertyName, expectValue] : m_mapGroundDeviceRule)
        {
            const auto& deviceProperty = ZGUtils::get(mapProperties, propertyName);
            // 将预期值与实时值加入到conditions中
            StringMap condition;
            if (!composeCondition(condition, deviceID, propertyName, deviceProperty, expectValue, e))
            {
                ZGLOG_ERROR(QStringLiteral("组合条件失败"));
                return false;
            }
            conditions.push_back(std::move(condition));
        }
        StringMap condition;
        // 获取接地开关关联的分段开关
        auto pairDevice = m_mapGroundDeviceAssocOverZoneDevice.find(deviceID);
        if (pairDevice != m_mapGroundDeviceAssocOverZoneDevice.end())
        {
            const auto& overZoneDeviceID = pairDevice->second;
            // 获取分段开关的合闸状态
            std::string posState;
            if (!ZGProxyCommon::getPropertyValue(overZoneDeviceID, "Pos", posState, e))
            {
                ZGLOG_ERROR(QStringLiteral("获取分段开关'%1'属性值失败").arg(overZoneDeviceID.c_str()));
                return false;
            }
            // 如果分段开关是合位，加入接地开关的外部闭锁状态条件
            if (posState == "2")
            {
                StringMap outInterlockOff;
                if (!ZGProxyCommon::getProperty(deviceID, "OutInterlockOff", outInterlockOff, e))
                {
                    ZGLOG_ERROR(QStringLiteral("获取设备'%1'外部闭锁状态失败").arg(deviceID.c_str()));
                    return false;
                }
                if (!composeCondition(condition, deviceID, "OutInterlockOff", outInterlockOff, "2", e))
                {
                    ZGLOG_ERROR(QStringLiteral("组合条件失败"));
                    return false;
                }
                conditions.push_back(condition);
                condition.clear();
            }
        }
        // 对于任务内的其他设备，根据设备类型分别加入不同的联锁条件
        for (const auto& device : listDevices)
        {
            const auto& iterDeviceID = ZGUtils::get(device, "deviceID");
            const auto& typeID = ZGUtils::get(device, "typeID");
            if (typeID == "ZG_DT_DISCONNECTOR")
            {
                const auto& dstState = ZGUtils::get(device, "dstState");
                MapStringMap deviceProperties;
                if (!ZGProxyCommon::getProperties(iterDeviceID, {"Pos", "LockState"}, deviceProperties, e))
                {
                    ZGLOG_ERROR(QStringLiteral("获取设备'%1'属性值失败").arg(iterDeviceID.c_str()));
                    return false;
                }
                for (const auto& [propertyName, deviceProperty] : deviceProperties)
                {
                    if (propertyName == "LockState")
                    {
                        if (!composeCondition(condition, iterDeviceID, propertyName, deviceProperty, "2", e))
                        {
                            ZGLOG_ERROR(QStringLiteral("组合条件失败"));
                            return false;
                        }
                        conditions.push_back(condition);
                        condition.clear();
                    }
                    if (propertyName == "Pos")
                    {
                        if (!composeCondition(condition, iterDeviceID, propertyName, deviceProperty, dstState, e))
                        {
                            ZGLOG_ERROR(QStringLiteral("组合条件失败"));
                            return false;
                        }
                        conditions.push_back(condition);
                        condition.clear();
                    }
                }
            }
            // 如果为接地开关
            if (typeID == "ZG_DT_GROUND_SWITCH")
            {
                // 检查接地开关的通信状态
                StringMap deviceProperty;
                if (!ZGProxyCommon::getProperty(iterDeviceID, "CommState", deviceProperty, e))
                {
                    ZGLOG_ERROR(QStringLiteral("获取设备'%1'通信状态失败").arg(iterDeviceID.c_str()));
                    return false;
                }
                if (!composeCondition(condition, iterDeviceID, "CommState", deviceProperty, "2", e))
                {
                    ZGLOG_ERROR(QStringLiteral("组合条件失败"));
                    return false;
                }
                conditions.push_back(condition);
                condition.clear();
            }
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkSwitchCloseConditions(std::string taskID,
                                                       std::string deviceID,
                                                       bool& success,
                                                       ErrorInfo& e,
                                                       const Ice::Current& current)
    {
        if (!checkTaskExist(taskID, e))
            return false;
        if (!checkDeviceExists(deviceID, e))
            return false;
        ListStringMap listDevice;
        if (!getOutageDevices(taskID, listDevice, e))
            return false;
        try
        {
            for (const auto& device : listDevice)
            {
                const auto& taskDeviceID = ZGUtils::get(device, "deviceID");
                const auto& deviceTypeID = ZGUtils::get(device, "deviceTypeID");
                // 如果是本接地开关，判断其是否满足合闸条件
                if (taskDeviceID == deviceID)
                {
                    StringList listGroundProperty;
                    for (const auto& [propertyName, expectValue] : m_mapGroundDeviceRule)
                    {
                        listGroundProperty.push_back(propertyName);
                    }
                    MapStringMap mapProperties;
                    if (!ZGProxyCommon::getProperties(deviceID, listGroundProperty, mapProperties, e))
                    {
                        ZGLOG_ERROR(QStringLiteral("获取设备'%1'属性值失败").arg(deviceID.c_str()));
                        return false;
                    }
                    for (const auto& [propertyName, deviceProperty] : mapProperties)
                    {
                        const auto& expectValue = ZGUtils::get(m_mapGroundDeviceRule, propertyName);
                        const auto& realValue = ZGUtils::get(deviceProperty, "rtNewValue");
                        if (realValue != expectValue)
                        {
                            ZGLOG_TRACE(QString("device %1 property %2 value %3 not match %4")
                                .arg(taskDeviceID.c_str()).arg(propertyName.c_str()).arg(realValue.c_str())
                                .arg(expectValue.c_str()));
                            success = false;
                            return true;
                        }
                    }
                    auto pairDevice = m_mapGroundDeviceAssocOverZoneDevice.find(deviceID);
                    if (pairDevice != m_mapGroundDeviceAssocOverZoneDevice.end())
                    {
                        const auto& overZoneDeviceID = pairDevice->second;
                        std::string posState;
                        if (!ZGProxyCommon::getPropertyValue(overZoneDeviceID, "Pos", posState, e))
                        {
                            ZGLOG_ERROR(QStringLiteral("获取分段开关'%1'属性值失败").arg(overZoneDeviceID.c_str()));
                            return false;
                        }
                        if (posState == "2")
                        {
                            StringMap outInterlockOff;
                            if (!ZGProxyCommon::getProperty(deviceID, "OutInterlockOff", outInterlockOff, e))
                            {
                                ZGLOG_ERROR(QStringLiteral("获取设备'%1'外部闭锁状态失败").arg(deviceID.c_str()));
                                return false;
                            }
                            if (outInterlockOff["rtNewValue"] == "2")
                            {
                                ZGLOG_TRACE(QString("device %1 property OutInterlockOff value %2 not match %3")
                                    .arg(deviceID.c_str()).arg(outInterlockOff["rtNewValue"].c_str()).arg("2"));
                                success = false;
                                return true;
                            }
                        }
                    }
                }
                else
                {
                    if (deviceTypeID == "ZG_DT_DISCONNECTOR")
                    {
                        // 获取隔离开关的位置状态与锁定状态
                        StringMap deviceProperty;
                        if (!ZGProxyCommon::getPropertyValues(taskDeviceID, {"Pos", "LockState"}, deviceProperty, e))
                        {
                            ZGLOG_ERROR(QStringLiteral("获取设备'%1'属性值失败").arg(taskDeviceID.c_str()));
                            return false;
                        }
                        // 检查隔离开关的锁定状态是否为2，位置状态是否与任务中设备的目标状态一致
                        const auto& pos = ZGUtils::get(deviceProperty, "Pos");
                        const auto& lockState = ZGUtils::get(deviceProperty, "LockState");
                        const auto& dstState = ZGUtils::get(device, "dstState");
                        if (lockState != "2")
                        {
                            ZGLOG_TRACE(QString("device %1 property LockState value %2 not match 2")
                                .arg(taskDeviceID.c_str()).arg(lockState.c_str()));
                            success = false;
                            return true;
                        }
                        if (pos != dstState)
                        {
                            ZGLOG_TRACE(QString("device %1 property Pos value %2 not match %3")
                                .arg(taskDeviceID.c_str()).arg(pos.c_str()).arg(dstState.c_str()));
                            success = false;
                            return true;
                        }
                    }
                    if (deviceTypeID == "ZG_DT_GROUND_SWITCH")
                    {
                        // 检查接地开关的通信状态是否正常
                        StringMap deviceProperty;
                        if (!ZGProxyCommon::getProperty(taskDeviceID, "CommState", deviceProperty, e))
                        {
                            ZGLOG_ERROR(QStringLiteral("获取设备'%1'通信状态失败").arg(taskDeviceID.c_str()));
                            return false;
                        }
                        if (deviceProperty["rtNewValue"] != "2")
                        {
                            ZGLOG_TRACE(QString("device %1 property CommState value %2 not match 2")
                                .arg(taskDeviceID.c_str()).arg(deviceProperty["rtNewValue"].c_str()));
                            success = false;
                            return true;
                        }
                    }
                }
            }
            success = true;
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::deleteTypicalTask(std::string taskID,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
    {
        // 这里不能直接调用deleteTask，因为该函数针对典型表做了特殊处理，初始化状态而不是删除
        if (!checkTaskExist(taskID, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        if (!deleteTask(taskID, e))
            return false;
        return true;
    }

    bool ZGOPTaskOutageMng::getDevicesBoundaryType(StringMap inputDevice,
                                                   bool& isValidRegion,
                                                   ListStringMap& listOutputDevice,
                                                   ErrorInfo& e,
                                                   const Ice::Current& current)
    {
        StringList listDeviceID;
        for (const auto& [deviceID, subtypeID] : inputDevice)
        {
            // 如果是分段隔离开关，不加入设备列表
            if (subtypeID == "ZG_DS_DISCONNECTOR_OVERZONE")
                continue;
            listDeviceID.push_back(deviceID);
        }
        // 输出listDeviceID的调试信息
        for (const auto& deviceID : listDeviceID)
        {
            ZGLOG_TRACE(QString("deviceID: %1").arg(deviceID.c_str()));
        }
        // 根据设备列表获取应用节点列表
        StringList listAppNodeID;
        if (!getAppNodesFromDevices(listDeviceID, listAppNodeID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取设备所属应用节点失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listAppNodeID.empty())
        {
            ZGLOG_ERROR("listAppNodeID is empty");
            isValidRegion = false;
            return true;
        }
        for (const auto& appNodeID : listAppNodeID)
        {
            ZGLOG_TRACE(QString("appNodeID: %1").arg(appNodeID.c_str()));
        }
        m_pNodeNetwork->clear();
        // 构建供电分区网络
        for (const auto& appNodeID : listAppNodeID)
        {
            if (appNodeID.empty())
                continue;
            auto pair = m_mapAppNodeDevices.find(appNodeID);
            if (pair == m_mapAppNodeDevices.end())
                continue;
            const auto& appNodeDevices = pair->second;
            StringList listAppNodeDeviceID;
            for (const auto& [deviceID, subtypeID] : appNodeDevices)
            {
                // 如果设备ID不为空且子类型为分段隔离开关，则加入设备列表
                if (!deviceID.empty() && subtypeID == "ZG_DS_DISCONNECTOR_OVERZONE")
                {
                    ZGLOG_TRACE(QString("appNodeID: %1, deviceID: %2").arg(appNodeID.c_str()).arg(deviceID.c_str()));
                    listAppNodeDeviceID.push_back(deviceID);
                }
            }
            m_pNodeNetwork->addNodeVariables(appNodeID, listAppNodeDeviceID);
        }
        auto allNodes = m_pNodeNetwork->getNetworkNodes();
        if (allNodes.empty())
        {
            ZGLOG_ERROR("Network is empty");
            isValidRegion = false;
            return true;
        }
        std::unordered_set<std::string> unprocessedNodes(allNodes.begin(), allNodes.end());
        const int maxPathLength = 1000;
        ListStringList continuousPowerSupplyRegions;
        StringList publicVariables;
        StringList privateVariables;
        while (!unprocessedNodes.empty())
        {
            std::string startNode = *unprocessedNodes.begin();
            StringList nodes;
            NodeNetwork::VariablesPair variables;
            m_pNodeNetwork->getNodesAndVariables(startNode, maxPathLength, nodes, variables);
            continuousPowerSupplyRegions.push_back(nodes);
            publicVariables.insert(publicVariables.end(), variables.first.begin(), variables.first.end());
            privateVariables.insert(privateVariables.end(), variables.second.begin(), variables.second.end());
            for (const auto& node : nodes)
            {
                unprocessedNodes.erase(node);
            }
        }
        for (const auto& region : continuousPowerSupplyRegions)
        {
            std::string regions;
            for (const auto& node : region)
            {
                regions += node + " ";
            }
            ZGLOG_TRACE(QString("continuousPowerSupplyRegions: %1").arg(regions.c_str()));
        }
        ZGLOG_TRACE(QString("publicVariables: %1").arg(ZGUtils::join(publicVariables, ",", "", "").c_str()));
        ZGLOG_TRACE(QString("privateVariables: %1").arg(ZGUtils::join(privateVariables, ",", "", "").c_str()));
        // 先查找inputDevice中的所有分段隔离开关是否都是公共变量或私有变量，
        // 如果在公共变量或私有变量中都没有找到，说明该分段隔离开关是一个孤立的分段隔离开关，这种情况不是一个有效的供电区域，需要将isValidRegion置为false
        for (const auto& [deviceID, subtypeID] : inputDevice)
        {
            if (subtypeID != "ZG_DS_DISCONNECTOR_OVERZONE")
                continue;
            if (std::find(publicVariables.begin(), publicVariables.end(), deviceID) == publicVariables.end() &&
                std::find(privateVariables.begin(), privateVariables.end(), deviceID) == privateVariables.end())
            {
                ZGLOG_ERROR(QStringLiteral("存在孤立的分段隔离开关%1").arg(deviceID.c_str()));
                isValidRegion = false;
                return true;
            }
        }
        // 如果只有一个连续供电区域，则为有效的连续供电区域
        if (continuousPowerSupplyRegions.size() == 1)
        {
            isValidRegion = true;
        }
        // 如果有两个连续供电区域，且一个为上行线，一个为下行线，则为有效的连续供电区域
        else if (continuousPowerSupplyRegions.size() == 2)
        {
            // 分别取出两个供电区域
            const auto& region1 = continuousPowerSupplyRegions[0];
            const auto& region2 = continuousPowerSupplyRegions[1];
            auto pair1 = m_mapAppNode.find(*region1.begin());
            auto pair2 = m_mapAppNode.find(*region2.begin());
            if (pair1 == m_mapAppNode.end() || pair2 == m_mapAppNode.end())
            {
                isValidRegion = false;
                return true;
            }
            // 从供电区域1中取出任意应用节点，判断position属性，如果为up代表上行线，如果为down代表下行线
            std::string position1 = pair1->second["position"];
            std::string position2 = pair2->second["position"];
            if ((position1 == "up" && position2 == "down") || (position1 == "down" && position2 == "up"))
            {
                isValidRegion = true;
            }
        }
        else
        {
            ZGLOG_ERROR(QStringLiteral("不满足有效的连续供电区域"));
            isValidRegion = false;
        }
        ZGLOG_TRACE(QString("isValidRegion: %1").arg(isValidRegion));
        StringList allPublicVariables, allPrivateVariables;
        if (isValidRegion)
        {
            // 公共变量为任务中多个供电分区共用的分段隔离开关，也就是供电区域内部的分段隔离开关
            for (const auto& publicVariable: publicVariables)
            {
                // 在inputDevice中查找是否存在该设备，如果存在，则加入到outputDevice中
                auto pair = inputDevice.find(publicVariable);
                if (pair != inputDevice.end())
                {
                    StringMap outputDevice{{"id", publicVariable}, {"IsBoundarySwitch", "0"}};
                    listOutputDevice.push_back(outputDevice);
                }
            }
            // 私有变量为供电区域的边界分段隔离开关
            for (const auto& privateVariable: privateVariables)
            {
                // 在inputDevice中查找是否存在该设备，如果存在，则加入到outputDevice中
                auto pair = inputDevice.find(privateVariable);
                if (pair != inputDevice.end())
                {
                    StringMap outputDevice{{"id", privateVariable}, {"IsBoundarySwitch", "1"}};
                    listOutputDevice.push_back(outputDevice);
                }
            }
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getAppNodeDevicesConditions(ListStringMap& conditions,
                                                        const MapStringMap& taskDevices,
                                                        const std::string& appNodeID,
                                                        const std::string& currentDeviceID,
                                                        std::unordered_set<std::string>& setAppNodes,
                                                        std::unordered_set<std::string>& setDevices,
                                                        ErrorInfo& e)
    {
        // 如果该应用节点已经处理过，跳过
        if (setAppNodes.find(appNodeID) != setAppNodes.end())
        {
            return true;
        }
        const auto& appNodeDevices = ZGUtils::get(m_mapAppNodeDevices, appNodeID);
        for (const auto& [appNodeDeviceID, subtypeID] : appNodeDevices)
        {
            // 如果供电分区下的设备不在任务设备列表中，跳过
            auto pair = taskDevices.find(appNodeDeviceID);
            if (pair == taskDevices.end())
            {
                continue;
            }
            if (setDevices.find(appNodeDeviceID) != setDevices.end())
            {
                continue;
            }
            // 如果该设备就是要合闸的接地开关，计算本接地开关要满足的规则，加入到联锁条件中
            if (appNodeDeviceID == currentDeviceID)
            {
                StringList listGroundProperty;
                for (const auto& [propertyName, expectValue] : m_mapGroundDeviceRule)
                {
                    listGroundProperty.push_back(propertyName);
                }
                // 获取设备属性值
                MapStringMap mapProperties;
                if (!ZGProxyCommon::getProperties(currentDeviceID, listGroundProperty, mapProperties, e))
                {
                    ZGLOG_ERROR(QStringLiteral("获取设备'%1'属性值失败").arg(currentDeviceID.c_str()));
                    return false;
                }
                // 与m_mapGroundDeviceRule中的预期值进行比较
                for (const auto& [propertyName, expectValue] : m_mapGroundDeviceRule)
                {
                    const auto& deviceProperty = ZGUtils::get(mapProperties, propertyName);
                    // 将预期值与实时值加入到conditions中
                    StringMap condition;
                    if (!composeCondition(condition, currentDeviceID, propertyName, deviceProperty, expectValue, e))
                    {
                        ZGLOG_ERROR(QStringLiteral("组合条件失败"));
                        return false;
                    }
                    conditions.push_back(std::move(condition));
                }
                // 获取接地开关关联的分段开关
                auto pairDevice = m_mapGroundDeviceAssocOverZoneDevice.find(currentDeviceID);
                if (pairDevice != m_mapGroundDeviceAssocOverZoneDevice.end())
                {
                    const auto& overZoneDeviceID = pairDevice->second;
                    // 如果分段开关是合位，加入接地开关的外部闭锁状态条件
                    if (taskDevices.find(overZoneDeviceID) != taskDevices.end())
                    {
                        std::string posState;
                        if (!ZGProxyCommon::getPropertyValue(overZoneDeviceID, "Pos", posState, e))
                        {
                            ZGLOG_ERROR(QStringLiteral("获取分段开关'%1'属性值失败").arg(overZoneDeviceID.c_str()));
                            return false;
                        }
                        if (posState == "2")
                        {
                            StringMap outInterlockOff;
                            if (!ZGProxyCommon::getProperty(currentDeviceID, "OutInterlockOff", outInterlockOff, e))
                            {
                                ZGLOG_ERROR(QStringLiteral("获取设备'%1'外部闭锁状态失败").arg(currentDeviceID.c_str()));
                                return false;
                            }
                            StringMap condition;
                            if (!composeCondition(condition, currentDeviceID, "OutInterlockOff", outInterlockOff, "2",
                                e))
                            {
                                ZGLOG_ERROR(QStringLiteral("组合条件失败"));
                                return false;
                            }
                            conditions.push_back(std::move(condition));
                        }
                    }
                }
            }
            else
            {
                // 如果不是要合闸的接地开关，计算要满足的规则，加入到联锁条件中
                if (subtypeID == "ZG_DS_GROUND_SWITCH")
                {
                    // 检查通信状态是否正常，加入到条件表中
                    StringMap deviceProperty;
                    if (!ZGProxyCommon::getProperty(appNodeDeviceID, "CommState", deviceProperty, e))
                    {
                        ZGLOG_ERROR(QStringLiteral("获取设备'%1'通信状态失败").arg(appNodeDeviceID.c_str()));
                        return false;
                    }
                    StringMap condition;
                    if (!composeCondition(condition, appNodeDeviceID, "CommState", deviceProperty, "2", e))
                    {
                        ZGLOG_ERROR(QStringLiteral("组合条件失败"));
                        return false;
                    }
                    conditions.push_back(std::move(condition));
                }
                // 如果是隔离开关设备，计算要满足的规则，加入到联锁条件中
                if ((subtypeID == "ZG_DS_DISCONNECTOR") || (subtypeID == "ZG_DS_DISCONNECTOR_OVERZONE"))
                {
                    MapStringMap deviceProperties;
                    if (!ZGProxyCommon::getProperties(appNodeDeviceID, {"Pos", "LockState"}, deviceProperties, e))
                    {
                        ZGLOG_ERROR(QStringLiteral("获取设备'%1'属性值失败").arg(appNodeDeviceID.c_str()));
                        return false;
                    }
                    for (const auto& [propertyName, deviceProperty] : deviceProperties)
                    {
                        StringMap condition;
                        if (propertyName == "LockState")
                        {
                            if (!composeCondition(condition, appNodeDeviceID, propertyName, deviceProperty, "2", e))
                            {
                                ZGLOG_ERROR(QStringLiteral("组合条件失败"));
                                return false;
                            }
                        }
                        if (propertyName == "Pos")
                        {
                            const auto& taskDevice = ZGUtils::get(taskDevices, appNodeDeviceID);
                            const auto& expectValue = ZGUtils::get(taskDevice, "dstState");
                            if (!composeCondition(condition, appNodeDeviceID, propertyName, deviceProperty, expectValue,
                                e))
                            {
                                ZGLOG_ERROR(QStringLiteral("组合条件失败"));
                                return false;
                            }
                        }
                        conditions.push_back(std::move(condition));
                    }
                }
            }
            // 将已经处理过的设备加入到setDevices中
            setDevices.insert(appNodeDeviceID);
        }
        // 将已经处理过的应用节点加入到setAppNodes中
        setAppNodes.insert(appNodeID);
        return true;
    }

    bool ZGOPTaskOutageMng::encryptOTP(const std::string& otp,
                                       std::string& encryptedOTP,
                                       std::string& errMsg)
    {
        QByteArray baOTP = otp.c_str();
        QByteArray baEncryptedOTP;
        if (!ZGSecure::aesEncrypt(baOTP, baEncryptedOTP, m_aesKey, errMsg, ZGSecure::EncryptMode::ecb))
        {
            return false;
        }
        QByteArray base64OTP = baEncryptedOTP.toBase64();
        encryptedOTP = base64OTP.data();
        return true;
    }

    bool ZGOPTaskOutageMng::decryptOTP(const std::string& encryptedOTP,
                                       std::string& otp,
                                       std::string& errMsg)
    {
        QByteArray baEncryptedOTP = QByteArray::fromBase64(encryptedOTP.c_str());
        QByteArray baOTP;
        if (!ZGSecure::aesDecrypt(baEncryptedOTP, baOTP, m_aesKey, errMsg, ZGSecure::EncryptMode::ecb))
        {
            return false;
        }
        otp = baOTP.data();
        return true;
    }

    bool ZGOPTaskOutageMng::getOverZoneDeviceState(const StringList& listDeviceID,
                                                   ListStringMap& listDeviceState,
                                                   ErrorInfo& e)
    {
        MapStringMap mapDeviceState;
        if (!ZGProxyCommon::mgetPropertyValues(listDeviceID, {"Pos"}, mapDeviceState, e))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取分区设备属性值失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            for (const auto& deviceID : listDeviceID)
            {
                const auto& deviceState = ZGUtils::get(mapDeviceState, deviceID);
                StringMap device;
                device["deviceID"] = deviceID;
                device["dstState"] = ZGUtils::get(deviceState, "Pos");
                listDeviceState.push_back(std::move(device));
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::getAppNodesFromDevices(const StringList& listDeviceID,
                                                   StringList& listAppNodeID)
    {
        // 从mp_param_appnode_device表中根据设备ID获取关联的应用节点ID（供电分区），需要去重
        QString sql = QString("SELECT DISTINCT appNodeID FROM mp_param_appnode_device WHERE deviceID IN (%1) ORDER BY appNodeID")
            .arg(ZGUtils::join(listDeviceID, ",", "'", "'").c_str());
        ZGLOG_TRACE(sql);
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listAppNodeID))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备所属应用节点失败"));
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkClientExists(const std::string& clientID,
                                              ErrorInfo& e)
    {
        if (clientID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("客户端ID不能为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString("SELECT id FROM sp_param_client WHERE id = '%1'").arg(clientID.c_str());
        StringList listClientID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listClientID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取客户端失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listClientID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("客户端【%1】不存在").arg(clientID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkDeviceExists(const std::string& deviceID,
                                              ErrorInfo& e)
    {
        if (deviceID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备ID不能为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString("SELECT id FROM mp_param_device WHERE id = '%1'").arg(deviceID.c_str());
        StringList listDeviceID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取设备失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listDeviceID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备【%1】不存在").arg(deviceID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkTaskExist(const std::string& taskID,
                                           ErrorInfo& e)
    {
        if (taskID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务ID不能为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString("SELECT id FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        StringList listTaskID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listTaskID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listTaskID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务【%1】不存在").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkTaskStage(const std::string& taskID,
                                           const std::string& stageID,
                                           ErrorInfo& e)
    {
        if (m_mapTaskStage.find(stageID) == m_mapTaskStage.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务阶段【%1】不存在").arg(stageID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString("SELECT rtTaskStageID FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        std::string taskStageID;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), taskStageID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务阶段失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (taskStageID != stageID)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            const auto& stageName = ZGUtils::getName(m_mapTaskStage, stageID, "name");
            e.errDetail = QStringLiteral("任务不处于【%1】阶段").arg(stageName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkTaskOTP(const std::string& taskID,
                                         const StringMap& otp,
                                         ErrorInfo& e)
    {
        if (otp.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("OTP不能为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString(
            "SELECT mobileNumber AS id, userName, rtOTP FROM op_param_outage_task_user WHERE taskID = '%1'").arg(
            taskID.c_str());
        MapStringMap mapOTP;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), mapOTP))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取OTP失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 检查otp是否一致，并具体给出不一致的手机号
        try
        {
            for (const auto& pair : otp)
            {
                const auto& mobileNumber = pair.first;
                const auto& otpValue = pair.second;
                const auto& userInfo = ZGUtils::get(mapOTP, mobileNumber);
                const auto& userName = ZGUtils::get(userInfo, "userName");
                const auto& rtOTP = ZGUtils::get(userInfo, "rtOTP");
                // 对otp进行解密
                std::string decryptedOTP;
                std::string errMsg;
                if (!decryptOTP(rtOTP, decryptedOTP, errMsg))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                    e.errDetail = errMsg;
                    ZGLOG_ERROR(e);
                    return false;
                }
                ZGLOG_TRACE(QString("input otp = %1, real otp = %2").arg(otpValue.c_str()).arg(decryptedOTP.c_str()));
                if (decryptedOTP != otpValue)
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("【%1】（%2）的OTP不正确").arg(userName.c_str()).arg(mobileNumber.c_str()).
                                                                    toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkOutageStage(const std::string& taskID,
                                             const std::string& stageID,
                                             ErrorInfo& e)
    {
        if (m_mapOutageStage.find(stageID) == m_mapOutageStage.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务阶段【%1】不存在").arg(stageID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString("SELECT rtOutageStageID FROM op_param_outage_task WHERE id = '%1'").arg(taskID.c_str());
        std::string outageStageID;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), outageStageID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务阶段失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (outageStageID != stageID)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            auto pair = m_mapOutageStage.find(stageID);
            if (pair == m_mapOutageStage.end())
                e.errDetail = QStringLiteral("任务不处于【%1】阶段").arg(stageID.c_str()).toStdString();
            else
                e.errDetail = QStringLiteral("任务不处于【%1】阶段").arg(pair->second["name"].c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkOutageState(const std::string& taskID,
                                             const std::string& stateID,
                                             ErrorInfo& e)
    {
        if (m_mapOutageState.find(stateID) == m_mapOutageState.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务状态【%1】不存在").arg(stateID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString("SELECT rtOutageStateID FROM op_param_outage_task WHERE id = '%1'").arg(taskID.c_str());
        std::string outageStateID;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), outageStateID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (outageStateID != stateID)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            const auto& stateName = ZGUtils::getName(m_mapOutageState, stateID, "name");
            e.errDetail = QStringLiteral("任务不处于【%1】状态").arg(stateName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkDeviceType(const std::string& deviceID,
                                            const std::string& deviceTypeID,
                                            ErrorInfo& e)
    {
        if (m_mapDeviceType.find(deviceTypeID) == m_mapDeviceType.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备类型【%1】不存在").arg(deviceTypeID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString("SELECT typeID FROM mp_param_device WHERE id = '%1'").arg(deviceID.c_str());
        StringList listDeviceTypeID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceTypeID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取设备类型失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listDeviceTypeID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备【%1】不存在").arg(deviceID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& currentDeviceTypeID = listDeviceTypeID.front();
        if (currentDeviceTypeID != deviceTypeID)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            const auto& deviceTypeName = ZGUtils::getName(m_mapDeviceType, deviceTypeID, "name");
            e.errDetail = QStringLiteral("设备不是【%1】").arg(deviceTypeName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkDeviceState(const std::string& taskID,
                                             const std::string& deviceID,
                                             const std::string& taskDeviceStateID,
                                             ErrorInfo& e)
    {
        if (m_mapOutageDeviceState.find(taskDeviceStateID) == m_mapOutageDeviceState.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备状态【%1】不存在").arg(taskDeviceStateID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString(
                          "SELECT rtDeviceStateID FROM op_param_outage_task_device WHERE taskID = '%1' AND deviceID = '%2'")
                      .arg(taskID.c_str()).arg(deviceID.c_str());
        StringList listDeviceState;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceState))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取设备任务状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listDeviceState.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备不存在").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& deviceState = listDeviceState.front();
        if (deviceState != taskDeviceStateID)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            const auto& deviceStateName = ZGUtils::getName(m_mapOutageDeviceState, taskDeviceStateID, "name");
            e.errDetail = QStringLiteral("设备在任务中不是【%1】状态").arg(deviceStateName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkDeviceTypeState(const std::string& taskID,
                                                 const std::string& deviceTypeID,
                                                 const std::string& taskDeviceStateID,
                                                 ErrorInfo& e)
    {
        if (m_mapOutageDeviceState.find(taskDeviceStateID) == m_mapOutageDeviceState.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备状态【%1】不存在").arg(taskDeviceStateID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString(
                          "SELECT a.deviceID AS id, b.name AS deviceName, a.rtDeviceStateID FROM op_param_outage_task_device a "
                          "LEFT JOIN mp_param_device b ON a.deviceID = b.id WHERE a.taskID = '%1' AND b.typeID = '%2' ORDER BY a.id")
                      .arg(taskID.c_str()).arg(deviceTypeID.c_str());
        ListStringMap listDeviceState;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDeviceState))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取设备状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            for (const auto& device : listDeviceState)
            {
                const auto& deviceName = ZGUtils::get(device, "deviceName");
                const auto& deviceState = ZGUtils::get(device, "rtDeviceStateID");
                if (deviceState != taskDeviceStateID)
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                    const auto& deviceStateName = ZGUtils::getName(m_mapOutageDeviceState, taskDeviceStateID, "name");
                    e.errDetail = QStringLiteral("设备【%1】在任务中不是【%2】状态").arg(deviceName.c_str()).arg(
                        deviceStateName.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::checkDeviceTypeDstState(const std::string& taskID,
                                                    const std::string& deviceTypeID,
                                                    const std::string& propertyName,
                                                    ErrorInfo& e,
                                                    bool isInvert)
    {
        QString sql = QString(
                          "SELECT a.deviceID AS id, b.name AS deviceName, a.dstState FROM op_param_outage_task_device a "
                          "LEFT JOIN mp_param_device b ON a.deviceID = b.id WHERE a.taskID = '%1' AND b.typeID = '%2' ORDER BY a.id")
                      .arg(taskID.c_str()).arg(deviceTypeID.c_str());
        // 获取任务中所有指定类型设备的目标状态
        MapStringMap mapDeviceInfo;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), mapDeviceInfo))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取设备目标状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringList listDeviceID;
        for (const auto& [deviceID, _] : mapDeviceInfo)
        {
            listDeviceID.push_back(deviceID);
        }
        // 获取设备属性值
        MapMapStringMap deviceProperties;
        if (!ZGProxyCommon::mgetProperties(listDeviceID, {propertyName}, deviceProperties, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        for (const auto& [deviceID, properties] : deviceProperties)
        {
            for (const auto& [name, property] : properties)
            {
                const auto& rtNewValue = ZGUtils::get(property, "rtNewValue");
                const auto& rtNewValueDesc = ZGUtils::get(property, "rtNewValueDesc");
                const auto& deviceInfo = ZGUtils::get(mapDeviceInfo, deviceID);
                const auto& deviceName = ZGUtils::get(deviceInfo, "deviceName");
                const auto& dstState = ZGUtils::get(deviceInfo, "dstState");
                auto realState = dstState;
                if (isInvert)
                {
                    realState = (dstState == "1") ? "2" : "1";
                }
                if (rtNewValue != realState)
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("设备【%1】当前状态【%2】与目标状态不一致")
                                  .arg(deviceName.c_str()).arg(rtNewValueDesc.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkDeviceDstState(const std::string& taskID,
                                                const std::string& deviceID,
                                                const std::string& propertyName,
                                                ErrorInfo& e,
                                                bool isInvert)
    {
        QString sql = QString("SELECT b.name AS deviceName, a.dstState FROM op_param_outage_task_device a "
                          "LEFT JOIN mp_param_device b ON a.deviceID = b.id WHERE a.taskID = '%1' AND a.deviceID = '%2' ORDER BY a.id")
                      .arg(taskID.c_str()).arg(deviceID.c_str());
        ListStringMap listDevice;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevice))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取设备目标状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listDevice.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务中设备不存在").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& deviceInfo = listDevice.front();
        try
        {
            const auto& deviceName = ZGUtils::get(deviceInfo, "deviceName");
            const auto& dstState = ZGUtils::get(deviceInfo, "dstState");
            StringMap deviceProperty;
            if (!ZGProxyCommon::getProperty(deviceID, propertyName, deviceProperty, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            const auto& rtNewValue = ZGUtils::get(deviceProperty, "rtNewValue");
            const auto& rtNewValueDesc = ZGUtils::get(deviceProperty, "rtNewValueDesc");
            auto realState = dstState;
            if (isInvert)
            {
                realState = (dstState == "1") ? "2" : "1";
            }
            if (rtNewValue != realState)
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("设备【%1】当前状态【%2】与目标状态不一致")
                              .arg(deviceName.c_str()).arg(rtNewValueDesc.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::checkOutInterlockState(const std::string& taskID,
                                                   ErrorInfo& e)
    {
        QString sql = QString(
                "SELECT a.deviceID AS id, b.name AS deviceName FROM op_param_outage_task_device a "
                "LEFT JOIN mp_param_device b ON a.deviceID = b.id WHERE a.taskID = '%1' AND b.typeID = 'ZG_DT_GROUND_SWITCH' ORDER BY a.id")
            .arg(taskID.c_str());
        // 获取任务中所有接地开关的信息
        MapStringMap mapDeviceInfo;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), mapDeviceInfo))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取接地开关信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringList listDeviceID;
        for (const auto& [deviceID, _] : mapDeviceInfo)
        {
            listDeviceID.push_back(deviceID);
        }
        // 获取设备属性值
        MapStringMap propertyValues;
        if (!ZGProxyCommon::mgetPropertyValues(listDeviceID, {"OutInterlockOff"}, propertyValues, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            for (const auto& [deviceID, propertyValue] : propertyValues)
            {
                const auto& deviceInfo = ZGUtils::get(mapDeviceInfo, deviceID);
                const auto& deviceName = ZGUtils::get(deviceInfo, "deviceName");
                const auto& outInterlockOff = ZGUtils::get(propertyValue, "OutInterlockOff");
                if (outInterlockOff != "1")
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("接地开关【%1】外部闭锁未投入").arg(deviceName.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::checkDeviceLockDeviceState(const std::string& deviceID,
                                                       ErrorInfo& e)
    {
        // 获取设备关联的安全闭锁装置
        auto pair = m_mapDeviceAssocLockDevice.find(deviceID);
        if (pair == m_mapDeviceAssocLockDevice.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            std::string name;
            ZGProxyCommon::getDataByField("mp_param_device", deviceID, "name", name);
            e.errDetail = QStringLiteral("%1未关联安全闭锁装置").arg(name.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& lockDeviceID = pair->second;
        StringMap lockDeviceProperty;
        if (!ZGProxyCommon::getPropertyValues(lockDeviceID, {"name", "CommState", "InOutState"}, lockDeviceProperty, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& deviceName = ZGUtils::get(lockDeviceProperty, "name");
        const auto& commState = ZGUtils::get(lockDeviceProperty, "CommState");
        const auto& inOutState = ZGUtils::get(lockDeviceProperty, "InOutState");
        if (commState == "1")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("%1通信异常").arg(deviceName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (inOutState == "1")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("%1已退出").arg(deviceName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkContinuityDeviceState(const std::string& taskID,
                                                       ErrorInfo& e)
    {
        return false;
        // // 获取任务中的所有接地装置
        // StringList listDeviceID;
        // QString sql = QString("SELECT a.deviceID FROM op_param_outage_task_device a "
        //                       "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
        //                       "WHERE b.typeID = 'ZG_DT_GROUND_SWITCH' AND b.isEnable = 1 ORDER BY a.deviceID");
        // if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
        // {
        //     e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
        //     e.errDetail = QStringLiteral("获取接地装置失败").toStdString();
        //     ZGLOG_ERROR(e);
        //     return false;
        // }
        // // 根据m_mapGroundDeviceAssocContinuityDevice获取接地装置的关联连续性监测模块
        // StringList listContinuityDeviceID;
        // for (const auto& deviceID : listDeviceID)
        // {
        //     auto pair = m_mapGroundDeviceAssocContinuityDevice.find(deviceID);
        //     if (pair != m_mapGroundDeviceAssocContinuityDevice.end())
        //         listContinuityDeviceID.push_back(pair->second);
        // }
        // // 获取连续性监测模块的监测结果
    }

    bool ZGOPTaskOutageMng::checkSameUser(const StringMap& head,
                                          const ListStringMap& users,
                                          ErrorInfo& e)
    {
        try
        {
            // 从任务中获取创建人
            const auto& rtCreateUserID = ZGUtils::get(head, "rtCreateUserID");
            // 从用户表中获取该用户的手机号
            QString sql = QString("SELECT mobileNumber FROM sp_param_hrm_user WHERE id = '%1'").arg(
                rtCreateUserID.c_str());
            std::string mobileNumber;
            if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), mobileNumber))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("获取用户手机号失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            // 将任务人员(CP)的手机号与创建人员(AP)的手机号进行比较，如果相同返回false，否则返回true
            for (const auto& user : users)
            {
                const auto& userMobileNumber = ZGUtils::get(user, "mobileNumber");
                if (userMobileNumber == mobileNumber)
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("AP与CP手机号不能相同").toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    void ZGOPTaskOutageMng::checkZoneVoltage()
    {
        std::map<std::string, StringList> mapZoneGroundDevice;
        // 从m_mapAppNodeDevices中将子类型为接地开关的设备按照应用节点进行分组
        for (const auto& [appNodeID, device] : m_mapAppNodeDevices)
        {
            for (const auto& [deviceID, subtypeID] : device)
            {
                if (subtypeID == "ZG_DS_GROUND_SWITCH")
                {
                    mapZoneGroundDevice[appNodeID].push_back(deviceID);
                }
            }
        }
        for (const auto& [appNodeID, listDeviceID] : mapZoneGroundDevice)
        {
            // 获取接地开关的接触网电压值
            MapStringMap mapDeviceVol;
            ErrorInfo e;
            if (!ZGProxyCommon::mgetPropertyValues(listDeviceID, {"ContactVol", "LineVoltageInconsistent"},
                mapDeviceVol, e))
            {
                ZGLOG_ERROR(e);
                continue;
            }

            std::vector<double> vecVol;
            for (const auto& [deviceID, device] : mapDeviceVol)
            {
                const auto& contactVol = ZGUtils::get(device, "ContactVol");
                vecVol.push_back(std::atof(contactVol.c_str()));
            }
            bool isSame = checkDifferenceLessThan(vecVol, 200);
            // 更新所有设备的LineVoltageInconsistent状态，如果不一致，设置为2，否则设置为1
            MapStringMap mapDeviceState;
            try
            {
                for (const auto& [deviceID, device] : mapDeviceVol)
                {
                    const auto& oldState = ZGUtils::get(device, "LineVoltageInconsistent");
                    std::string newState = isSame ? "1" : "2";
                    if (oldState != newState)
                    {
                        StringMap deviceState;
                        deviceState["LineVoltageInconsistent"] = isSame ? "1" : "2";
                        mapDeviceState[deviceID] = deviceState;
                    }
                }
            }
            catch (const std::exception& ex)
            {
                ZGLOG_ERROR(ex.what());
                continue;
            }
            if (!mapDeviceState.empty())
            {
                ZGProxyCommon::mupdatePropertyValues(mapDeviceState, e);
            }
        }
    }

    bool ZGOPTaskOutageMng::checkDevicesParam(const ListStringMap& listDevice,
                                              ErrorInfo& e)
    {
        if (listDevice.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备列表为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkUsersParam(const ListStringMap& listUser,
                                            ErrorInfo& e)
    {
        if (listUser.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("用户列表为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::compareDeviceDstState(const std::string& taskID,
                                                  const std::string& deviceID,
                                                  const std::string& propertyName,
                                                  const std::string& expectValue,
                                                  ErrorInfo& e)
    {
        MapStringMap deviceState;
        if (!ZGProxyCommon::getProperties(deviceID, {"name", propertyName}, deviceState, e))
        {
            return false;
        }
        if (deviceState[propertyName]["rtNewValue"] != expectValue)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备【%1】【%2】当前状态【%3】与目标状态不一致")
                          .arg(deviceState["name"]["value"].c_str())
                          .arg(deviceState[propertyName]["name"].c_str())
                          .arg(deviceState[propertyName]["rtNewValueDesc"].c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkOtherTasksDeviceDstState(const std::string& taskID,
                                                          const std::string& deviceID,
                                                          const std::string& dstValue,
                                                          ErrorInfo& e)
    {
        // 检查其他任务中是否有设备的目标状态与当前任务的目标状态不一致，不一致返回false，否则返回true
        QString sql = QString(
                          "SELECT a.taskID, b.name AS taskName, c.name AS deviceName, a.dstState FROM op_param_outage_task_device a "
                          "LEFT JOIN op_param_task b ON a.taskID = b.id "
                          "LEFT JOIN mp_param_device c ON a.deviceID = c.id WHERE a.deviceID = '%1' AND a.taskID != '%2' ORDER BY a.id")
                      .arg(deviceID.c_str())
                      .arg(taskID.c_str());
        ListStringMap listDeviceState;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDeviceState))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取其他任务设备状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (const auto& device : listDeviceState)
        {
            const auto& dstState = ZGUtils::get(device, "dstState");
            if (dstState != dstValue)
            {
                const auto& taskName = ZGUtils::get(device, "taskName");
                const auto& deviceName = ZGUtils::get(device, "deviceName");
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDesc = QStringLiteral("任务【%1】中设备【%2】的目标状态与当前任务目标状态不一致")
                            .arg(taskName.c_str())
                            .arg(deviceName.c_str())
                            .toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkDeviceOTP(const std::string& taskID,
                                           const std::string& deviceID,
                                           const std::string& otp,
                                           ErrorInfo& e)
    {
        QString sql = QString("SELECT rtOTP FROM op_param_outage_task_device WHERE taskID = '%1' AND deviceID = '%2'").
                      arg(taskID.c_str()).arg(deviceID.c_str());
        StringList listOTP;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listOTP))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取设备OTP失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listOTP.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务中设备不存在").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& rtOTP = listOTP.front();
        if (rtOTP != otp)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("设备OTP验证错误").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::lockDevice(const std::string& clientID,
                                       const std::string& taskID,
                                       const std::string& deviceID,
                                       ErrorInfo& e)
    {
        StringMap deviceParam;
        if (!ZGProxyCommon::getPropertyValues(deviceID, {"name", "nameL2", "appNodeID", "LockState"}, deviceParam, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        if (deviceParam["lockState"] != "2")
        {
            // 向设备发送锁定命令
            if (!sendCtrlCommand(clientID, deviceID, "CMD_LockStateClose", "2"))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("发送锁定命令失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        // 更新任务设备状态为锁定中
        StringMap taskDeviceParam;
        taskDeviceParam["rtDeviceStateID"] = "ZG_DOD_LOCKING";
        taskDeviceParam["rtUpdateTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        QString condition = QString("taskID = '%1' AND deviceID = '%2'").arg(taskID.c_str()).arg(deviceID.c_str());
        QString sql = ZGUtils::generateUpdateSql("op_param_outage_task_device", taskDeviceParam,
            condition.toStdString()).c_str();
        if (!ZGProxyCommon::execSql(sql.toStdString()))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新锁定状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& deviceName = deviceParam["name"];
        const auto& deviceNameL2 = deviceParam["nameL2"];
        const auto& appNodeID = deviceParam["appNodeID"];
        const auto& appNodeName = ZGUtils::getName(m_mapAppNode, appNodeID, "name");
        const auto& appNodeNameL2 = ZGUtils::getName(m_mapAppNode, appNodeID, "nameL2");
        const auto& event = ZGUtils::languageString(firstLanguage(), "lockingDevice") +
            QStringLiteral("【%1】【%2】").arg(appNodeName.c_str()).arg(deviceName.c_str()).toStdString();
        const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "lockingDevice") +
            QStringLiteral("【%1】【%2】").arg(appNodeNameL2.c_str()).arg(deviceNameL2.c_str()).toStdString();
        if (!saveEvent(taskID, deviceID, event, eventL2, e, Ice::Current()))
        {
            ZGLOG_ERROR(e);
        }
        return true;
    }

    bool ZGOPTaskOutageMng::unlockDevice(const std::string& clientID,
                                         const std::string& taskID,
                                         const std::string& deviceID,
                                         ErrorInfo& e)
    {
        // 获取任务设备表中所有当前状态为锁定中的记录
        QString sql = QString(
                "SELECT id FROM op_param_outage_task_device WHERE deviceID = '%1' AND rtDeviceStateID = 'ZG_DOD_LOCKED'")
            .arg(deviceID.c_str());
        StringList listID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取锁定中设备ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringMap deviceParam;
        if (!ZGProxyCommon::getPropertyValues(deviceID, {"name", "nameL2", "appNodeID", "LockState"}, deviceParam, e))
        {
            return false;
        }
        if (listID.size() > 1)
        {
            StringMap taskDeviceParam;
            taskDeviceParam["rtDeviceStateID"] = "ZG_DOD_NORMAL";
            taskDeviceParam["rtUpdateTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
            QString condition = QString("taskID = '%1' AND deviceID = '%2'").arg(taskID.c_str()).arg(deviceID.c_str());
            sql = ZGUtils::generateUpdateSql("op_param_outage_task_device", taskDeviceParam, condition.toStdString()).
                c_str();
            if (!ZGProxyCommon::execSql(sql.toStdString()))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("更新解锁状态失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        else
        {
            // 向设备发送解锁命令
            if (!sendCtrlCommand(clientID, deviceID, "CMD_LockStateOpen", "2"))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("发送解锁命令失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            // 更新任务设备状态为解锁中
            StringMap taskDeviceParam;
            taskDeviceParam["rtDeviceStateID"] = "ZG_DOD_UNLOCKING";
            taskDeviceParam["rtUpdateTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
            QString condition = QString("taskID = '%1' AND deviceID = '%2'").arg(taskID.c_str()).arg(deviceID.c_str());
            sql = ZGUtils::generateUpdateSql("op_param_outage_task_device", taskDeviceParam, condition.toStdString()).
                c_str();
            if (!ZGProxyCommon::execSql(sql.toStdString()))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("更新解锁状态失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        const auto& deviceName = deviceParam["name"];
        const auto& deviceNameL2 = deviceParam["nameL2"];
        const auto& appNodeID = deviceParam["appNodeID"];
        const auto& appNodeName = ZGUtils::getName(m_mapAppNode, appNodeID, "name");
        const auto& appNodeNameL2 = ZGUtils::getName(m_mapAppNode, appNodeID, "nameL2");
        const auto& event = ZGUtils::languageString(firstLanguage(), "unlockDevice") +
            QStringLiteral("【%1】【%2】").arg(appNodeName.c_str()).arg(deviceName.c_str()).toStdString();
        const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "unlockDevice") +
            QStringLiteral("【%1】【%2】").arg(appNodeNameL2.c_str()).arg(deviceNameL2.c_str()).toStdString();
        if (!saveEvent(taskID, deviceID, event, eventL2, e, Ice::Current()))
        {
            ZGLOG_ERROR(e);
        }
        return true;
    }

    bool ZGOPTaskOutageMng::updateDeviceSubtypeState(const std::string& taskID,
                                                     const std::string& deviceSubtypeID,
                                                     const std::string& state,
                                                     ErrorInfo& e)
    {
        StringList listSql;
        if (!generateUpdateDeviceSubtypeStateSql(listSql, taskID, deviceSubtypeID, state, e))
        {
            return false;
        }
        if (!listSql.empty())
        {
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("更新设备状态失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getKeyIDFromTaskDevice(const std::string& taskID,
                                                   const std::string& deviceID,
                                                   std::string& id,
                                                   ErrorInfo& e)
    {
        StringList listDeviceID;
        listDeviceID.push_back(deviceID);
        StringList listID;
        if (!getKeyIDsFromTaskDevice(taskID, listDeviceID, listID, e))
        {
            return false;
        }
        id = listID.front();
        return true;
    }

    bool ZGOPTaskOutageMng::getKeyIDsFromTaskDevice(const std::string& taskID,
                                                    const StringList& listDeviceID,
                                                    StringList& listID,
                                                    ErrorInfo& e)
    {
        const auto& devices = ZGUtils::join(listDeviceID, ",", "'", "'");
        QString sql = QString("SELECT id FROM op_param_outage_task_device WHERE taskID = '%1' AND deviceID IN (%2)"
            " ORDER BY FIELD(deviceID, %2)").arg(taskID.c_str()).arg(devices.c_str());
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务设备ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listID.size() != listDeviceID.size())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务中设备不存在").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getKeyIDFromTaskUser(const std::string& taskID,
                                                 const std::string& userID,
                                                 std::string& id,
                                                 ErrorInfo& e)
    {
        StringList listUserID;
        listUserID.push_back(userID);
        StringList listID;
        if (!getKeyIDsFromTaskUser(taskID, listUserID, listID, e))
        {
            return false;
        }
        id = listID.front();
        return true;
    }

    bool ZGOPTaskOutageMng::getKeyIDsFromTaskUser(const std::string& taskID,
                                                  const StringList& listUserID,
                                                  StringList& listID,
                                                  ErrorInfo& e)
    {
        const auto& users = ZGUtils::join(listUserID, ",", "'", "'");
        QString sql = QString("SELECT id FROM op_param_outage_task_user WHERE taskID = '%1' AND userID IN (%2)"
            " ORDER BY FIELD(userID, %2)").arg(taskID.c_str()).arg(users.c_str());
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务用户ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listID.size() != listUserID.size())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务中用户不存在").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getDeviceTypeDevices(const std::string& taskID,
                                                 const std::string& deviceTypeID,
                                                 StringList& listDeviceID,
                                                 ErrorInfo& e)
    {
        // 在任务设备表中获取指定类型的设备ID列表
        QString sql = QString("SELECT a.deviceID AS id FROM op_param_outage_task_device a "
                          "LEFT JOIN mp_param_device b ON a.deviceID = b.id WHERE a.taskID = '%1' AND b.typeID = '%2' ORDER BY a.id")
                      .arg(taskID.c_str()).arg(deviceTypeID.c_str());
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取类型'%1'设备ID失败").arg(deviceTypeID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkRuleCondition(const std::string& deviceID,
                                               const std::string& propertyName,
                                               const std::string& propertyValue,
                                               ErrorInfo& e)
    {
        auto ruleProxy = ZGProxyMng::instance()->getProxyMPRuleEngine();
        if (!ruleProxy)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取规则引擎代理失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        std::string tableName, dataID;
        if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
        {
            ZGLOG_ERROR(QStringLiteral("根据设备属性获取数据ID失败"));
            return false;
        }
        // 检查命令的执行联锁条件
        try
        {
            if (!ruleProxy->checkCommandExecCondition(dataID, propertyValue, e))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("命令'%1'验证五防规则条件失败").arg(dataID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const Ice::Exception& ie)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ie.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::checkInterlockCondition(const std::string& taskID,
                                                    const MapStringMap& mapCondition,
                                                    ErrorInfo& e)
    {
        StringList listDeviceID;
        std::string errMsg;
        // 获取任务中的所有设备ID
        if (!getListDeviceFromTask(taskID, listDeviceID, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = errMsg;
            return false;
        }
        StringList listAppNodeID;
        // 获取设备所属的应用节点ID
        if (!getListAppNodeFromDevices(listDeviceID, listAppNodeID, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = errMsg;
            return false;
        }
        for (const auto& appNodeID : listAppNodeID)
        {
            // 获取设备的实时状态，并与条件值进行比较
            for (const auto& [deviceTypeID, condition] : mapCondition)
            {
                QString sql = QString(
                                  "SELECT id FROM mp_param_device WHERE appNodeID = '%1' AND typeID = '%2' AND isEnable = 1")
                              .arg(appNodeID.c_str()).arg(deviceTypeID.c_str());
                StringList listCheckDeviceID;
                if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listCheckDeviceID))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                    e.errDetail = QStringLiteral("获取设备失败").toStdString();
                    return false;
                }
                if (listCheckDeviceID.empty())
                {
                    continue;
                }
                for (const auto& [name, value] : condition)
                {
                    MapMapStringMap mapProperties;
                    if (!ZGProxyCommon::mgetProperties(listCheckDeviceID, {"name", name}, mapProperties, e))
                    {
                        ZGLOG_ERROR(e);
                        return false;
                    }
                    for (const auto& [deviceID, property] : mapProperties)
                    {
                        try
                        {
                            const auto& deviceName = ZGUtils::get(property, "name");
                            const auto& deviceNameValue = ZGUtils::get(deviceName, "value");
                            const auto& deviceProperty = ZGUtils::get(property, name);
                            const auto& devicePropertyDataName = ZGUtils::get(deviceProperty, "name");
                            const auto& devicePropertyDataValueDesc = ZGUtils::get(deviceProperty, "rtNewValueDesc");
                            const auto& devicePropertyDataValue = ZGUtils::get(deviceProperty, "rtNewValue");
                            if (devicePropertyDataValue != value)
                            {
                                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                                e.errDetail = QStringLiteral("设备【%1】【%2】的当前状态【%3】与预期状态不一致")
                                              .arg(deviceNameValue.c_str()).arg(devicePropertyDataName.c_str()).arg(
                                                  devicePropertyDataValueDesc.c_str()).toStdString();
                                ZGLOG_ERROR(e);
                                return false;
                            }
                        }
                        catch (const std::exception& ex)
                        {
                            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                            e.errDetail = QStringLiteral("获取设备%1属性失败，%2").arg(deviceID.c_str()).arg(ex.what()).
                                                                          toStdString();
                            ZGLOG_ERROR(e);
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    void ZGOPTaskOutageMng::saveOutageStageEvent(const std::string& taskID,
                                                 const std::string& stageID,
                                                 ErrorInfo& e)
    {
        std::string outageStageName = stageID, outageStageNameL2 = stageID;
        auto pair = m_mapOutageStage.find(stageID);
        if (pair != m_mapOutageStage.end())
        {
            auto outageStage = pair->second;
            outageStageName = outageStage["name"];
            outageStageNameL2 = outageStage["nameL2"];
        }
        const auto& event = ZGUtils::languageString(firstLanguage(), "enter") + outageStageName +
            ZGUtils::languageString(firstLanguage(), "stage");
        const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "enter") + outageStageNameL2 +
            ZGUtils::languageString(secondLanguage(), "stage");
        if (!saveEvent(taskID, "", event, eventL2, e, Ice::Current()))
        {
            ZGLOG_ERROR(e);
        }
    }

    void ZGOPTaskOutageMng::disableGroundInterlock(const std::string& appNodeID,
                                                   const MapStringMap& devices,
                                                   StringMap deviceInterlock)
    {
        auto pair = m_mapAppNodeDevices.find(appNodeID);
        if (pair == m_mapAppNodeDevices.end())
        {
            return;
        }
        const auto& appNodeDevices = pair->second;
        for (const auto& [id, subtypeID] : appNodeDevices)
        {
            // 如果是接地开关且设备在任务设备列表中，则设置为0
            if (subtypeID == "ZG_DS_GROUND_SWITCH")
            {
                if (devices.find(id) != devices.end())
                {
                    deviceInterlock[id] = "0";
                }
            }
        }
    }

    void ZGOPTaskOutageMng::checkDisconnectorInterlock(const std::string& deviceID,
                                                       const StringMap& deviceState,
                                                       const std::function<void()>& func)
    {
        try
        {
            StringMap disconnectorDeviceState;
            ErrorInfo e;
            if (!ZGProxyCommon::getPropertyValues(deviceID, {"Pos", "LockState"}, disconnectorDeviceState, e))
            {
                ZGLOG_ERROR(e);
                return;
            }
            const auto& pos = ZGUtils::get(disconnectorDeviceState, "Pos");
            const auto& lockState = ZGUtils::get(disconnectorDeviceState, "LockState");
            const auto& dstState = ZGUtils::get(deviceState, "dstState");
            if ((pos != dstState) || (lockState != "2"))
            {
                func();
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    bool ZGOPTaskOutageMng::composeCondition(StringMap& condition,
                                             const std::string& deviceID,
                                             const std::string& propertyName,
                                             const StringMap& deviceProperty,
                                             const std::string& expectValue,
                                             ErrorInfo& e)
    {
        try
        {
            condition["expectValue"] = expectValue;
            condition["actualValue"] = ZGUtils::get(deviceProperty, "rtNewValue");
            condition["actualValueDesc"] = ZGUtils::get(deviceProperty, "rtNewValueDesc");
            condition["actualValueDescL2"] = ZGUtils::get(deviceProperty, "rtNewValueDescL2");
            condition["dataID"] = ZGUtils::get(deviceProperty, "id");
            condition["dataName"] = ZGUtils::get(deviceProperty, "name");
            condition["dataNameL2"] = ZGUtils::get(deviceProperty, "nameL2");
            std::string appNodeID;
            ZGProxyCommon::getDataByField("mp_param_device", deviceID, "appNodeID", appNodeID);
            condition["appNodeID"] = appNodeID;
            const auto& appNodeName = ZGUtils::getName(m_mapAppNode, appNodeID, "name");
            const auto& appNodeNameL2 = ZGUtils::getName(m_mapAppNode, appNodeID, "nameL2");
            condition["appNodeName"] = appNodeName;
            condition["appNodeNameL2"] = appNodeName;
            const auto& dataCategoryID = ZGUtils::get(deviceProperty, "dataCategoryID");
            const auto& key = dataCategoryID + "/" + expectValue;
            const auto& expectValueDesc = ZGUtils::getName(m_mapDataCategoryProperty, key, "propName");
            const auto& expectValueDescL2 = ZGUtils::getName(m_mapDataCategoryProperty, key, "propNameL2");
            condition["expectValueDesc"] = expectValueDesc;
            condition["expectValueDescL2"] = expectValueDescL2;
            condition["interlockInfo"] = "";
            condition["operatorID"] = "==";
            const auto& operatorName = ZGUtils::getName(m_mapOperator, "==", "name");
            const auto& operatorNameL2 = ZGUtils::getName(m_mapOperator, "==", "nameL2");
            condition["operator"] = operatorName;
            condition["operatorL2"] = operatorNameL2;
            condition["result"] = (condition["expectValue"] == condition["actualValue"]) ? "1" : "0";
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::switchToRestoreStage(const std::string& taskID,
                                                 ErrorInfo& e)
    {
        // 先获取任务下的所有接地开关设备，如果设备没有解锁，返回错误
        if (!checkDeviceTypeState(taskID, "ZG_DT_GROUND_SWITCH", "ZG_DOD_NORMAL", e))
        {
            return false;
        }
        StringList listDeviceID;
        if (!getDeviceTypeDevices(taskID, "ZG_DT_GROUND_SWITCH", listDeviceID, e))
        {
            return false;
        }
        for (const auto& deviceID : listDeviceID)
        {
            if (!checkDeviceDstState(taskID, deviceID, "PosClose", e))
            {
                return false;
            }
            if (!checkDeviceDstState(taskID, deviceID, "PosOpen", e, true))
            {
                return false;
            }
        }
        StringList listSql;
        generateSwitchOutageSql(listSql, taskID, "ZG_OS_RESTORE");
        if (!generateUpdateDeviceSubtypeStateSql(listSql, taskID, "ZG_DS_DISCONNECTOR", "2", e))
        {
            return false;
        }
        // 获取任务设备表中所有设备子类型为分段隔离开关的设备
        QString sql = QString(
            "SELECT a.deviceID AS id FROM op_param_outage_task_device a LEFT JOIN mp_param_device b ON a.deviceID = b.id "
            "WHERE a.taskID = '%1' AND b.subtypeID = 'ZG_DS_DISCONNECTOR_OVERZONE'").arg(taskID.c_str());
        StringList listOverZoneDeviceID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listOverZoneDeviceID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取分段隔离开关设备ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 获取分段隔离开关的NormalState状态
        MapStringMap mapOverZoneDeviceState;
        if (!ZGProxyCommon::mgetPropertyValues(listOverZoneDeviceID, {"NormalState"}, mapOverZoneDeviceState, e))
        {
            return false;
        }
        // 从mapOverZoneDeviceState中获取分段隔离开关的NormalState状态，更新到任务设备表的dstState字段中
        for (const auto& deviceID : listOverZoneDeviceID)
        {
            const auto& overZoneDeviceState = ZGUtils::get(mapOverZoneDeviceState, deviceID);
            const auto& normalState = ZGUtils::get(overZoneDeviceState, "NormalState");
            // 直接用normalState更新任务设备表的dstState字段
            StringMap taskDeviceParam;
            taskDeviceParam["dstState"] = normalState;
            QString condition = QString("taskID = '%1' AND deviceID = '%2'").arg(taskID.c_str()).arg(deviceID.c_str());
            sql = ZGUtils::generateUpdateSql("op_param_outage_task_device", taskDeviceParam, condition.toStdString()).
                c_str();
            listSql.push_back(sql.toStdString());
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新恢复阶段失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        saveOutageStageEvent(taskID, "ZG_OS_RESTORE", e);
        return true;
    }

    bool ZGOPTaskOutageMng::switchToGroundStage(const std::string& taskID,
                                                ErrorInfo& e)
    {
        if (!checkDeviceTypeState(taskID, "ZG_DT_DISCONNECTOR", "ZG_DOD_LOCKED", e))
        {
            return false;
        }
        if (!checkDeviceTypeDstState(taskID, "ZG_DT_DISCONNECTOR", "Pos", e))
        {
            return false;
        }
        if (!switchOutageStage(taskID, "ZG_OS_GROUND", e))
        {
            return false;
        }
        saveOutageStageEvent(taskID, "ZG_OS_GROUND", e);
        return true;
    }

    bool ZGOPTaskOutageMng::switchToRemoveGroundTestStage(const std::string& taskID,
                                                          ErrorInfo& e)
    {
        if (!checkDeviceTypeDstState(taskID, "ZG_DT_DISCONNECTOR", "Pos", e))
        {
            return false;
        }
        if (!checkDeviceTypeDstState(taskID, "ZG_DT_GROUND_SWITCH", "PosClose", e))
        {
            return false;
        }
        if (!checkDeviceTypeDstState(taskID, "ZG_DT_GROUND_SWITCH", "PosOpen", e, true))
        {
            return false;
        }
        StringList listSql;
        generateSwitchOutageSql(listSql, taskID, "ZG_OS_REMOVE_GROUND_TEST");
        if (!generateUpdateDeviceSubtypeStateSql(listSql, taskID, "ZG_DS_GROUND_SWITCH", "1", e))
        {
            return false;
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新测试移除阶段失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        saveOutageStageEvent(taskID, "ZG_OS_REMOVE_GROUND_TEST", e);
        return true;
    }

    bool ZGOPTaskOutageMng::switchToRemoveGroundStage(const std::string& taskID,
                                                      ErrorInfo& e)
    {
        StringList listSql;
        generateSwitchOutageSql(listSql, taskID, "ZG_OS_REMOVE_GROUND");
        if (!generateUpdateDeviceSubtypeStateSql(listSql, taskID, "ZG_DS_GROUND_SWITCH", "1", e))
        {
            return false;
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新移除接地阶段失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        saveOutageStageEvent(taskID, "ZG_OS_REMOVE_GROUND", e);
        return true;
    }

    bool ZGOPTaskOutageMng::switchToPTWStage(const std::string& taskID,
                                             ErrorInfo& e)
    {
        if (!checkDeviceTypeDstState(taskID, "ZG_DT_DISCONNECTOR", "Pos", e))
        {
            return false;
        }
        if (!checkDeviceTypeState(taskID, "ZG_DT_GROUND_SWITCH", "ZG_DOD_LOCKED", e))
        {
            return false;
        }
        if (!checkDeviceTypeDstState(taskID, "ZG_DT_GROUND_SWITCH", "PosClose", e))
        {
            return false;
        }
        if (!checkDeviceTypeDstState(taskID, "ZG_DT_GROUND_SWITCH", "PosOpen", e, true))
        {
            return false;
        }
        if (!checkOutInterlockState(taskID, e))
        {
            return false;
        }
        if (!switchOutageStage(taskID, "ZG_OS_PTW", e))
        {
            return false;
        }
        saveOutageStageEvent(taskID, "ZG_OS_PTW", e);
        return true;
    }

    bool ZGOPTaskOutageMng::switchToRequestSFTStage(const std::string& taskID,
                                                    ErrorInfo& e)
    {
        if (!checkDeviceTypeState(taskID, "ZG_DT_DISCONNECTOR", "ZG_DOD_LOCKED", e))
        {
            return false;
        }
        if (!checkDeviceTypeDstState(taskID, "ZG_DT_DISCONNECTOR", "Pos", e))
        {
            return false;
        }
        // 清除op_param_outage_task_user表中该任务下的用户的sendFlag标志
        QString sql = QString("UPDATE op_param_outage_task_user SET sendFlag = 0 WHERE taskID = '%1'").arg(
            taskID.c_str());
        if (!ZGProxyCommon::execSql(sql.toStdString()))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("清除用户发送标志失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!switchOutageStage(taskID, "ZG_OS_REQUEST_SFT", e))
        {
            return false;
        }
        saveOutageStageEvent(taskID, "ZG_OS_REQUEST_SFT", e);
        return true;
    }

    bool ZGOPTaskOutageMng::switchToTestStage(const std::string& taskID,
                                              ErrorInfo& e)
    {
        if (!switchOutageStage(taskID, "ZG_OS_TEST", e))
        {
            return false;
        }
        StringList listDeviceID;
        if (!getDeviceTypeDevices(taskID, "ZG_DT_GROUND_SWITCH", listDeviceID, e))
        {
            return false;
        }
        ZGLOG_TRACE(QString("taskID: %1, listDevice size: %2").arg(taskID.c_str()).arg(listDeviceID.size()));
        // 将任务设备表中接地开关的目标状态设为-1
        StringList listSql;
        for (const auto& deviceID : listDeviceID)
        {
            StringMap deviceParam;
            deviceParam["dstState"] = "-1";
            std::string conditions = "taskID = '" + taskID + "' AND deviceID = '" + deviceID + "'";
            auto sql = ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceParam, conditions);
            ZGLOG_TRACE(sql.c_str());
            listSql.push_back(sql);
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新接地开关目标状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        saveOutageStageEvent(taskID, "ZG_OS_TEST", e);
        return true;
    }

    bool ZGOPTaskOutageMng::switchToCancelSFTStage(const std::string& taskID,
                                                   ErrorInfo& e)
    {
        if (!switchOutageStage(taskID, "ZG_OS_CANCEL_SFT", e))
        {
            return false;
        }
        // 将任务设备表中接地开关的目标状态设为1
        StringList listDeviceID;
        if (!getDeviceTypeDevices(taskID, "ZG_DT_GROUND_SWITCH", listDeviceID, e))
        {
            return false;
        }
        // // 将任务设备表中接地开关的目标状态设为1
        // StringList listSql;
        // for (const auto& deviceID : listDeviceID)
        // {
        //     StringMap deviceParam;
        //     deviceParam["dstState"] = "1";
        //     std::string conditions = "taskID = '" + taskID + "' AND deviceID = '" + deviceID + "'";
        //     auto sql = ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceParam, conditions);
        //     ZGLOG_TRACE(sql.c_str());
        //     listSql.push_back(sql);
        // }
        // if (!ZGProxyCommon::execBatchSql(listSql))
        // {
        //     e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
        //     e.errDetail = QStringLiteral("更新接地开关目标状态失败").toStdString();
        //     ZGLOG_ERROR(e);
        //     return false;
        // }
        saveOutageStageEvent(taskID, "ZG_OS_CANCEL_SFT", e);
        return true;
    }

    bool ZGOPTaskOutageMng::switchToAbortState(const std::string& taskID,
                                               ErrorInfo& e)
    {
        StringList listSql;
        generateSwitchOutageSql(listSql, taskID, "ZG_OS_ABORT");
        if (!generateUpdateDeviceSubtypeStateSql(listSql, taskID, "ZG_DS_GROUND_SWITCH", "1", e))
        {
            return false;
        }
        if (!generateUpdateDeviceSubtypeStateSql(listSql, taskID, "ZG_DS_DISCONNECTOR", "2", e))
        {
            return false;
        }
        for (const auto& sql : listSql)
        {
            ZGLOG_TRACE(sql.c_str());
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新作废阶段失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        saveOutageStageEvent(taskID, "ZG_OS_ABORT", e);
        return true;
    }

    bool ZGOPTaskOutageMng::switchOutageStage(const std::string& taskID,
                                              const std::string& stageID,
                                              ErrorInfo& e)
    {
        ZG6000::StringList listSql;
        generateSwitchOutageSql(listSql, taskID, stageID);
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新任务阶段失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    void ZGOPTaskOutageMng::processTaskChange(const MapField& record)
    {
        QJsonObject root;
        QJsonObject dataObj;
        // 保存任务事件，并发布任务状态变化信息
        try
        {
            for (const auto& field : record)
            {
                if (field.first == "rtTaskStageID")
                {
                    dataObj.insert("rtTaskStageName",
                        ZGUtils::getName(m_mapTaskStage, field.second.newValue, "name").c_str());
                    dataObj.insert("rtTaskStageNameL2",
                        ZGUtils::getName(m_mapTaskStage, field.second.newValue, "nameL2").c_str());
                }
                if (field.first == "rtTaskStateID")
                {
                    dataObj.insert("rtTaskStateName",
                        ZGUtils::getName(m_mapTaskState, field.second.newValue, "name").c_str());
                    dataObj.insert("rtTaskStateNameL2",
                        ZGUtils::getName(m_mapTaskState, field.second.newValue, "nameL2").c_str());
                }
                dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
            }
            root["head"] = dataObj;
            QJsonDocument doc(root);
            const auto& taskID = ZGUtils::get(record, "id").newValue;
            ZGLOG_TRACE(doc.toJson());
            m_pMqttClient->sendPublish(QString("op_param_outage_task/%1").arg(taskID.c_str()), doc.toJson());
            std::string taskTypeID;
            if (ZGProxyCommon::getDataByField("op_param_task", taskID, "taskTypeID", taskTypeID))
            {
                m_pMqttClient->sendPublish(
                    QString("op_param_task/%1/update").arg(taskTypeID.c_str()), QString("%1").arg(taskID.c_str()));
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    void ZGOPTaskOutageMng::processOutageTaskChange(const MapField& record)
    {
        QJsonObject root;
        QJsonObject dataObj;
        // 保存断电任务事件，并发布断电任务状态变化信息
        try
        {
            for (const auto& field : record)
            {
                if (field.first == "rtOutageStageID")
                {
                    auto pair = m_mapOutageStage.find(field.second.newValue);
                    if (pair != m_mapOutageStage.end())
                    {
                        dataObj.insert("rtOutageStageName", pair->second["name"].c_str());
                        dataObj.insert("rtOutageStageNameL2", pair->second["nameL2"].c_str());
                    }
                }
                dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
            }
            root["head"] = dataObj;
            QJsonDocument doc(root);
            QByteArray ba = doc.toJson();
            ZGLOG_TRACE(ba);
            const auto& taskID = ZGUtils::get(record, "id").newValue;
            m_pMqttClient->sendPublish(QString("op_param_outage_task/%1").arg(taskID.c_str()), ba);
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    void ZGOPTaskOutageMng::processOutageTaskDeviceChange(const MapField& record)
    {
        QJsonObject root;
        QJsonObject dataObj;
        // 保存任务设备事件，并发布设备状态变化信息
        try
        {
            for (const auto& field : record)
            {
                if (field.first == "rtDeviceStateID")
                {
                    std::string rtDeviceStateName = ZGUtils::getName(m_mapOutageDeviceState, field.second.newValue,
                        "name");
                    std::string rtDeviceStateNameL2 = ZGUtils::getName(m_mapOutageDeviceState, field.second.newValue,
                        "nameL2");
                    dataObj.insert("rtDeviceStateName", rtDeviceStateName.c_str());
                    dataObj.insert("rtDeviceStateNameL2", rtDeviceStateNameL2.c_str());
                    QString sql = QString(
                        "SELECT a.taskID, a.deviceID, b.appNodeID, b.name AS deviceName, b.nameL2 AS deviceNameL2 FROM op_param_outage_task_device a "
                        "LEFT JOIN mp_param_device b ON a.deviceID = b.id WHERE a.id = '%1' ORDER BY a.id").arg(
                        ZGUtils::get(record, "id").newValue.c_str());
                    StringMap device;
                    if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), device))
                    {
                        ZGLOG_ERROR(QStringLiteral("获取设备名称失败"));
                        continue;
                    }
                    const auto& taskID = ZGUtils::get(device, "taskID");
                    const auto& deviceName = ZGUtils::get(device, "deviceName");
                    const auto& deviceNameL2 = ZGUtils::get(device, "deviceNameL2");
                    const auto& appNodeID = ZGUtils::get(device, "appNodeID");
                    std::string appNodeName, appNodeNameL2;
                    auto pair = m_mapAppNode.find(appNodeID);
                    if (pair != m_mapAppNode.end())
                    {
                        appNodeName = pair->second["name"];
                        appNodeNameL2 = pair->second["nameL2"];
                    }
                    else
                    {
                        auto appNode = pair->second;
                        appNodeName = appNode["name"];
                        appNodeNameL2 = appNode["nameL2"];
                    }
                    QString event = QStringLiteral("【%1】【%2】%3【%4】")
                                    .arg(appNodeName.c_str())
                                    .arg(deviceName.c_str())
                                    .arg(ZGUtils::languageString(firstLanguage(), "changeInTask").c_str())
                                    .arg(rtDeviceStateName.c_str());
                    QString eventL2 = QStringLiteral("【%1】【%2】%3【%4】")
                                      .arg(appNodeNameL2.c_str())
                                      .arg(deviceNameL2.c_str())
                                      .arg(ZGUtils::languageString(secondLanguage(), "changeInTask").c_str())
                                      .arg(rtDeviceStateName.c_str());
                    ZG6000::ErrorInfo e;
                    if (!saveEvent(taskID, "", event.toStdString(), eventL2.toStdString(), e, Ice::Current()))
                    {
                        ZGLOG_ERROR(e);
                    }
                }
                dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
            }
            root["devices"] = dataObj;
            QJsonDocument doc(root);
            const auto& keyID = ZGUtils::get(record, "id").newValue;
            std::string taskID;
            if (!ZGProxyCommon::getDataByField("op_param_outage_task_device", keyID, "taskID", taskID))
            {
                return;
            }
            ZGLOG_TRACE(doc.toJson());
            m_pMqttClient->sendPublish(QString("op_param_outage_task/%1").arg(taskID.c_str()), doc.toJson());
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    void ZGOPTaskOutageMng::processOutageTaskUserChange(const MapField& record)
    {
        QJsonObject root;
        QJsonObject dataObj;
        try
        {
            for (const auto& field : record)
            {
                dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
            }
            root["users"] = dataObj;
            QJsonDocument doc(root);
            const auto& keyID = ZGUtils::get(record, "id").newValue;
            std::string taskID;
            if (!ZGProxyCommon::getDataByField("op_param_outage_task_user", keyID, "taskID", taskID))
            {
                return;
            }
            ZGLOG_TRACE(doc.toJson());
            m_pMqttClient->sendPublish(QString("op_param_outage_task/%1").arg(taskID.c_str()), doc.toJson());
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    void ZGOPTaskOutageMng::processTaskAdd(const MapField& record)
    {
        try
        {
            std::string taskID = ZGUtils::get(record, "id").newValue;
            std::string taskTypeID;
            if (ZGProxyCommon::getDataByField("op_param_task", taskID, "taskTypeID", taskTypeID))
            {
                m_pMqttClient->sendPublish(QString("op_param_task/%1/insert").arg(taskTypeID.c_str()),
                    QString("%1").arg(taskID.c_str()));
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    void ZGOPTaskOutageMng::processTaskDelete(const MapField& record)
    {
        try
        {
            std::string taskID = ZGUtils::get(record, "id").newValue;
            m_pMqttClient->sendPublish(QString("op_param_task/delete"), QString("%1").arg(taskID.c_str()));
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    std::string ZGOPTaskOutageMng::generateOTP(int length)
    {
        const QString possibleCharacters = "123456789";
        QRandomGenerator* generator = QRandomGenerator::global();

        QString captcha;
        for (int i = 0; i < length; ++i)
        {
            auto index = generator->bounded(possibleCharacters.size());
            QChar nextChar = possibleCharacters[index];
            captcha.append(nextChar);
        }
        return captcha.toStdString();
    }

    // 向任务中关联的设备发送任务信息，包括任务名称、任务开始时间、任务结束时间及任务相关人员
    bool ZGOPTaskOutageMng::sendTaskJsonInfoToDevice(const std::string& taskID,
                                                     ErrorInfo& e)
    {
        if (!checkTaskExist(taskID, e))
        {
            ZGLOG_ERROR(QStringLiteral("验证任务失败"));
            return false;
        }
        StringMap taskHead;
        if (!getOutageHead(taskID, taskHead, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务头信息失败"));
            return false;
        }
        ListStringMap devices;
        if (!getOutageDevices(taskID, devices, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务设备信息失败"));
            return false;
        }
        ListStringMap users;
        if (!getOutageUsers(taskID, users, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务用户信息失败"));
            return false;
        }
        QJsonObject root;
        QJsonObject headObj;
        QJsonArray userArray;
        // 生成向设备发送的任务信息
        headObj["name"] = ZGUtils::get(taskHead, "name").c_str();
        headObj["startTime"] = ZGUtils::get(taskHead, "startTime").c_str();
        headObj["endTime"] = ZGUtils::get(taskHead, "endTime").c_str();
        root["head"] = headObj;
        for (const auto& user : users)
        {
            QJsonObject userObj;
            userObj["name"] = ZGUtils::get(user, "userName").c_str();
            userObj["mobileNumber"] = ZGUtils::get(user, "mobileNumber").c_str();
            userArray.append(userObj);
        }
        root["users"] = userArray;
        QJsonDocument doc(root);
        for (const auto& device : devices)
        {
            const auto& deviceID = ZGUtils::get(device, "id");
            sendCtrlCommand("", deviceID, "CMD_TaskInfo", doc.toJson().toStdString());
        }
        return true;
    }

    bool ZGOPTaskOutageMng::sendSMSMessage(const std::string& mobileNumber,
                                           const std::string& message,
                                           ErrorInfo& e)
    {
        StringList listState;
        if (!ZGProxyCommon::mgetDataByField("mp_param_device", m_listSMSDeviceID, "rtState", listState))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取短信设备通信状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (std::all_of(listState.begin(), listState.end(), [](const std::string& state) { return state != "2"; }))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("短信设备通信状态中断").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringMap shortMessage;
        shortMessage["content"] = message;
        shortMessage["phoneNumber"] = mobileNumber;
        QDateTime dt = QDateTime::currentDateTime();
        shortMessage["timestamp"] = dt.toString("yyyy-MM-dd hh:mm:ss").toStdString();
        const auto& json = ZGJson::convertToJson(shortMessage);
        long long size;
        std::string errMsg;
        if (!m_pRedisQueue->rpush("ZG_Q_SHORT_MESSAGE", json, size, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = errMsg;
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::sendSMSWarnMessage(const std::string& taskID,
                                               const std::string& deviceID)
    {
        // 获取任务中的创建人员的手机号码
        QString sql = QString("SELECT b.mobileNumber AS id FROM op_param_task a "
            "LEFT JOIN sp_param_hrm_user b ON a.rtCreateUserID = b.id "
            "WHERE a.id = '%1'").arg(taskID.c_str());
        std::string mobileNumber;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), mobileNumber))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务创建人员手机号码失败"));
            return false;
        }
        // 获取设备名称
        std::string deviceName;
        ZGProxyCommon::getDataByField("mp_param_device", deviceID, "name", deviceName);
        // 获取任务名称
        std::string taskName;
        ZGProxyCommon::getDataByField("op_param_task", taskID, "name", taskName);
        QString message = QStringLiteral("【香港地铁】任务【%1】【%2】相关设备状态异常，请及时处理").arg(taskName.c_str()).
                                                                           arg(deviceName.c_str());
        ErrorInfo e;
        if (!sendSMSMessage(mobileNumber, message.toStdString(), e))
        {
            ZGLOG_ERROR(e);
            ZGLOG_ERROR(QStringLiteral("向%1发送设备状态异常短信失败").arg(mobileNumber.c_str()));
            return false;
        }
        return true;
    }

    /**
     * 生成安全文件验证码
     * @param safetyFileCode 安全文件码
     * @param reorder 乱序数组
     * @param safetyFileVerifyCode 安全文件验证码
     * @param e 错误信息
     * @return 是否生成成功
     */
    bool ZGOPTaskOutageMng::generateSafetyFileVerifyCode(const std::string& safetyFileCode,
                                                         const std::vector<int> reorder,
                                                         std::string& safetyFileVerifyCode,
                                                         ErrorInfo& e)
    {
        static const char printable[] = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        static const size_t num_printable = strlen(printable);
        int length = static_cast<int>(safetyFileCode.size());
        // 安全文件编码长度不能超过32位
        if (length > 32)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("安全文件编码长度超过32位").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 乱序数组长度必须为32
        if (reorder.size() != 32)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("乱序数组长度不为32").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QByteArray hashValue = QCryptographicHash::hash(safetyFileCode.c_str(), QCryptographicHash::Sha256);
        QByteArray recorderHash;
        // 根据乱序数组重新排列哈希值
        for (int i = 0; i < 32; ++i)
        {
            recorderHash.append(hashValue[reorder[i]]);
        }
        // 将乱序后的哈希值转换为可打印字符
        safetyFileVerifyCode.clear();
        for (int i = 0; i < length; ++i)
        {
            safetyFileVerifyCode.push_back(printable[(unsigned char)(recorderHash[i]) % num_printable]);
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getHistorySaveYear(const std::string& taskID,
                                               std::string& year,
                                               ErrorInfo& e)
    {
        QString sql = QString("SELECT rtCreateTime FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        std::string createTime;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), createTime))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务开始时间失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 转换任务开始时间
        QDateTime dtCreateTime;
        if (!ZGUtils::StringToDateTime(createTime.c_str(), dtCreateTime, true))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("无效的创建时间'%2'").arg(createTime.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        year = QString::number(dtCreateTime.date().year()).toStdString();
        return true;
    }

    void ZGOPTaskOutageMng::addTaskInfoToDevices(const std::string& taskID)
    {
        QString sql;
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
        {
            sql = QString(
                "SELECT a.id AS ID, a.name, b.name AS `user`, b.mobileNumber AS tele, c.name AS dept FROM op_param_task a "
                "LEFT JOIN sp_param_hrm_user b ON a.rtCreateUserID = b.id "
                "LEFT JOIN sp_param_hrm_organ c ON b.organID = c.id "
                "WHERE a.id = '%1' ORDER BY a.id").arg(taskID.c_str());
        }
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
        {
            sql = QString(
                "SELECT a.id AS ID, a.name, b.name AS [user], b.mobileNumber AS tele, c.name AS dept FROM op_param_task a "
                "LEFT JOIN sp_param_hrm_user b ON a.rtCreateUserID = b.id "
                "LEFT JOIN sp_param_hrm_organ c ON b.organID = c.id "
                "WHERE a.id = '%1' ORDER BY a.id").arg(taskID.c_str());
        }
        StringMap taskInfo;
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), taskInfo))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务信息失败"));
            return;
        }
        taskInfo["type"] = "WT_INFO";
        // 获取任务下的设备ID
        StringList listDeviceID;
        std::string errMsg;
        if (!getListDeviceFromTask(taskID, listDeviceID, errMsg))
        {
            ZGLOG_ERROR(errMsg.c_str());
            return;
        }
        QMutexLocker locker(&m_mutex);
        for (const auto& deviceID : listDeviceID)
        {
            // 对于每个设备，获取其关联的指示牌设备
            auto pair = m_mapDeviceAssocIndiDevice.find(deviceID);
            if (pair == m_mapDeviceAssocIndiDevice.end())
            {
                continue;
            }
            const auto& indiDeviceID = pair->second;
            m_mapDeviceTasks[indiDeviceID].push_back(taskInfo);
        }
    }

    void ZGOPTaskOutageMng::addTaskInfoToDevice(const std::string& taskID,
                                                const std::string& deviceID)
    {
        QString sql;
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
        {
            sql = QString(
                "SELECT a.id AS ID, a.name, b.name AS user, b.mobileNumber AS tele, c.name AS dept, d.name AS co FROM op_param_task a "
                "LEFT JOIN sp_param_hrm_user b ON a.rtCreateUserID = b.id "
                "LEFT JOIN sp_param_hrm_organ c ON b.organID = c.id "
                "LEFT JOIN sp_param_hrm_company d ON b.companyID = d.id "
                "WHERE a.id = '%1' ORDER BY a.id").arg(taskID.c_str());
        }
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
        {
            sql = QString(
                "SELECT a.id AS ID, a.name, b.name AS [user], b.mobileNumber AS tele, c.name AS dept, d.name AS co FROM op_param_task a "
                "LEFT JOIN sp_param_hrm_user b ON a.rtCreateUserID = b.id "
                "LEFT JOIN sp_param_hrm_organ c ON b.organID = c.id "
                "LEFT JOIN sp_param_hrm_company d ON b.companyID = d.id "
                "WHERE a.id = '%1' ORDER BY a.id").arg(taskID.c_str());
        }
        StringMap taskInfo;
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), taskInfo))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务信息失败"));
            return;
        }
        taskInfo["type"] = "WT_INFO";
        QMutexLocker locker(&m_mutex);
        m_mapDeviceTasks[deviceID].push_back(taskInfo);
    }

    void ZGOPTaskOutageMng::clearDeviceTaskInfo(const std::string& indiDeviceID)
    {
        QMutexLocker locker(&m_mutex);
        m_mapDeviceTasks[indiDeviceID].clear();
    }

    void ZGOPTaskOutageMng::sendTaskInfoToDevices()
    {
        QMutexLocker locker(&m_mutex);
        // 依次向每个告警警示牌(接地指示灯)设备发送一个任务信息，每个设备只发送一个，以免命令堆积
        for (auto& [deviceID, listTaskInfo] : m_mapDeviceTasks)
        {
            if (listTaskInfo.empty())
            {
                continue;
            }
            QJsonObject object = ZGUtils::stringMapToObject(listTaskInfo.front());
            QJsonDocument doc(object);
            QByteArray ba = doc.toJson();
            sendCtrlCommand("", deviceID, "CMD_TaskInfo", ba.toStdString());
            listTaskInfo.pop_front();
        }
    }

    StringList ZGOPTaskOutageMng::getTaskListFromTaskInfo(const std::string& taskInfo)
    {
        StringList listTaskID;
        if (taskInfo.empty())
        {
            return listTaskID;
        }
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(taskInfo.c_str(), &error);
        if (error.error != QJsonParseError::NoError)
        {
            return listTaskID;
        }
        QJsonObject object = doc.object();
        if (object.find("IDs") == object.end())
        {
            return listTaskID;
        }
        const auto& ids = object["IDs"].toArray();
        for (auto id : ids)
        {
            listTaskID.push_back(id.toString().toStdString());
        }
        return listTaskID;
    }

    bool ZGOPTaskOutageMng::getOutageHead(const std::string& taskID,
                                          StringMap& head,
                                          ErrorInfo& e)
    {
        // 从任务表中获取任务头信息
        QString sql = QString("SELECT * FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        StringMap taskHead;
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), taskHead))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务头信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& rtTaskStageID = ZGUtils::get(taskHead, "rtTaskStageID");
        const auto& rtTaskStageName = ZGUtils::getName(m_mapTaskStage, rtTaskStageID, "name");
        const auto& rtTaskStageNameL2 = ZGUtils::getName(m_mapTaskStage, rtTaskStageID, "nameL2");
        taskHead["rtTaskStageName"] = rtTaskStageName;
        taskHead["rtTaskStageNameL2"] = rtTaskStageName;
        const auto& rtTaskStateID = ZGUtils::get(taskHead, "rtTaskStateID");
        const auto& rtTaskStateName = ZGUtils::getName(m_mapTaskState, rtTaskStateID, "name");
        const auto& rtTaskStateNameL2 = ZGUtils::getName(m_mapTaskState, rtTaskStateID, "nameL2");
        taskHead["rtTaskStateName"] = rtTaskStateName;
        taskHead["rtTaskStateNameL2"] = rtTaskStateNameL2;
        const auto& rtCreateUserID = ZGUtils::get(taskHead, "rtCreateUserID");
        std::string rtCreateUserName;
        if (!rtCreateUserID.empty())
        {
            ZGProxyCommon::getDataByField("sp_param_hrm_user", rtCreateUserID, "name", rtCreateUserName);
        }
        taskHead["rtCreateUserName"] = rtCreateUserName;
        StringMap outageHead;
        sql = QString("SELECT * FROM op_param_outage_task WHERE id = '%1'").arg(taskID.c_str());
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), outageHead))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务头信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& outageTypeID = ZGUtils::get(outageHead, "outageTypeID");
        const auto& outageTypeName = ZGUtils::getName(m_mapOutageType, outageTypeID, "name");
        const auto& outageTypeNameL2 = ZGUtils::getName(m_mapOutageType, outageTypeID, "nameL2");
        outageHead["outageTypeName"] = outageTypeName;
        outageHead["outageTypeNameL2"] = outageTypeNameL2;
        const auto& rtOutageStageID = ZGUtils::get(outageHead, "rtOutageStageID");
        auto pair = m_mapOutageStage.find(rtOutageStageID);
        if (pair != m_mapOutageStage.end())
        {
            auto outageStageName = pair->second;
            outageHead["outageStageName"] = outageStageName["name"];
            outageHead["outageStageNameL2"] = outageStageName["nameL2"];
        }
        const auto& rtOutageStateID = ZGUtils::get(outageHead, "rtOutageStateID");
        const auto& rtOutageStateName = ZGUtils::getName(m_mapOutageState, rtOutageStateID, "name");
        const auto& rtOutageStateNameL2 = ZGUtils::getName(m_mapOutageState, rtOutageStateID, "nameL2");
        outageHead["rtOutageStateName"] = rtOutageStateName;
        outageHead["rtOutageStateNameL2"] = rtOutageStateNameL2;
        head = taskHead;
        head.insert(outageHead.begin(), outageHead.end());
        return true;
    }

    bool ZGOPTaskOutageMng::getOutageDevices(const std::string& taskID,
                                             ListStringMap& devices,
                                             ErrorInfo& e)
    {
        QString sql = QString(
            "SELECT a.id, a.deviceID, b.name AS deviceName, b.nameL2 AS deviceNameL2, b.typeID AS deviceTypeID, e.name AS deviceTypeName,"
            " e.nameL2 AS deviceTypeNameL2, b.subtypeID AS deviceSubtypeID, b.appNodeID, d.name AS appNodeName, d.nameL2 AS appNodeNameL2,"
            " a.dstState, a.rtOTP, a.rtDeviceStateID, a.rtIsUnlockEnable, a.rtIsAllowClose, a.rtIsSwitchClose, a.rtIsWarning, c.name "
            "AS rtDeviceStateName, c.nameL2 AS rtDeviceStateNameL2 FROM op_param_outage_task_device a LEFT JOIN mp_param_device b ON a.deviceID = b.id "
            "LEFT JOIN op_dict_outage_device_state c ON a.rtDeviceStateID = c.id "
            "LEFT JOIN sp_param_appnode d ON b.appNodeID = d.id "
            "LEFT JOIN mp_dict_device_type e ON b.typeID = e.id "
            "WHERE taskID = '%1' ORDER BY a.id").arg(taskID.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), devices))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务设备信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getOutageUsers(const std::string& taskID,
                                           ListStringMap& users,
                                           ErrorInfo& e)
    {
        QString sql = QString(
            "SELECT id, userID, userName, mobileNumber, sendFlag, rtSafetyFileCode, rtSafetyFileVerifyCode, rtOTP, rtSavedOTP FROM op_param_outage_task_user"
            " WHERE taskID = '%1'").arg(taskID.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), users))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务用户信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // for (auto& user : users)
        // {
        //     std::string otp = user["rtOTP"];
        //     std::string savedOTP = user["rtSavedOTP"];
        //     if (!otp.empty())
        //     {
        //         std::string decryptedOTP;
        //         std::string errMsg;
        //         if (!decryptOTP(otp, decryptedOTP, errMsg))
        //         {
        //             e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
        //             e.errDetail = errMsg;
        //             ZGLOG_ERROR(e);
        //             return false;
        //         }
        //         user["rtOTP"] = decryptedOTP;
        //     }
        //     if (!savedOTP.empty())
        //     {
        //         std::string decryptedSavedOTP;
        //         std::string errMsg;
        //         if (!decryptOTP(savedOTP, decryptedSavedOTP, errMsg))
        //         {
        //             e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
        //             e.errDetail = errMsg;
        //             ZGLOG_ERROR(e);
        //             return false;
        //         }
        //         user["rtSavedOTP"] = decryptedSavedOTP;
        //     }
        // }
        return true;
    }

    bool ZGOPTaskOutageMng::sendCtrlCommand(const std::string& clientID,
                                            const std::string& deviceID,
                                            const std::string& propertyName,
                                            const std::string& value)
    {
        // ZGLOG_TRACE(
        //     QString("sendCtrlCommand, deviceID = '%1', propertyName = '%2', value = '%3'").arg(deviceID.c_str()).arg(
        //         propertyName.c_str()).arg(value.c_str()));
        std::string tableName, dataID;
        ZG6000::ErrorInfo e;
        if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        QJsonObject command;
        command["id"] = dataID.c_str();
        QString commandID;
        if (tableName == "mp_param_dataset_yk")
        {
            commandID = "ZG_DC_YK_EXEC";
        }
        if (tableName == "mp_param_dataset_ys")
        {
            commandID = "ZG_DC_YS_EXEC";
        }
        command["commandID"] = commandID;
        command["isReturnValue"] = "0";
        command["srcType"] = "auto";
        command["srcID"] = clientID.c_str();
        command["rtCode"] = QString::number(ZGUtils::genNumber(0, 10000));
        command["rtValue"] = value.c_str();
        command["rtCommandTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
        QJsonArray array;
        array.append(command);
        QJsonDocument doc(array);
        long long size;
        QString errMsg;
        std::string topicName;
        if (tableName == "mp_param_dataset_yk")
        {
            topicName = "ZG_Q_SYSTEM_YK";
        }
        if (tableName == "mp_param_dataset_ys")
        {
            topicName = "ZG_Q_SYSTEM_YS";
        }
        if (!m_pRedisQueue->rpush(topicName.c_str(), doc.toJson(), size, errMsg))
        {
            ZGLOG_ERROR("Send command to ys queue error.");
        }
        return true;
    }

    ZGOPTaskOutageMng::ZGOPTaskOutageMng(QObject* parent)
        : QObject{parent}
    {
    }

    void ZGOPTaskOutageMng::initEvents()
    {
        connect(&m_checkTimer, &QTimer::timeout, this, &ZGOPTaskOutageMng::onTimer);
    }

    void ZGOPTaskOutageMng::initServerInstConfig()
    {
        const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
        QString errMsg;
        int value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        {
            ZGLOG_WARN(errMsg);
        }
        else
        {
            m_initInterval = value;
        }
        if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        {
            ZGLOG_WARN(errMsg);
        }
        else
        {
            m_checkInterval = value;
        }
    }

    bool ZGOPTaskOutageMng::initServerInstInfo()
    {
        m_serverName = ZGRuntime::instance()->getServerID();
        if (m_serverName.isEmpty())
        {
            ZGLOG_ERROR("Empty server id.");
            return false;
        }
        m_instName = ZGRuntime::instance()->getInstanceID();
        if (m_instName.isEmpty())
        {
            ZGLOG_ERROR("Empty server instance id.");
            return false;
        }
        m_localNodeID = ZGPubFun::getLocalNodeID().toStdString();
        return true;
    }

    bool ZGOPTaskOutageMng::initMqttClient()
    {
        if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
        {
            ZGLOG_ERROR("initMqttClient error.");
            return false;
        }
        m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
        if (m_pMqttClient == nullptr)
        {
            ZGLOG_ERROR("getMqttClientMessage error.");
            return false;
        }
        m_pMqttClient->connectToHost();
        return true;
    }

    bool ZGOPTaskOutageMng::initRedisClient()
    {
        QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
        listClientType << ZGRuntime::REDIS_RT_QUEUE << ZGRuntime::REDIS_RT_TOPIC;
        if (!ZGRuntime::instance()->initRedisClient(listClientType))
        {
            ZGLOG_ERROR("Init redis client error.");
            return false;
        }
        m_pRedisQueue = ZGRuntime::instance()->getRedisClientRTQueue();
        if (m_pRedisQueue == nullptr)
        {
            ZGLOG_ERROR("getRedisClientRTQueue error.");
            return false;
        }
        m_pRedisTopic = ZGRuntime::instance()->getRedisClientRTTopic();
        if (m_pRedisTopic == nullptr)
        {
            ZGLOG_ERROR("getRedisClientRTTopic error.");
            return false;
        }
        connect(m_pRedisTopic, &ZGRedisClient::receivedMessage, this, &ZGOPTaskOutageMng::onReceivedMessage);
        StringList listTopic;
        for (const auto& deviceID : m_listLockDeviceID)
        {
            listTopic.push_back("mp_param_device/" + deviceID);
        }
        listTopic.push_back("ZG_T_SYSTEM_SMS");
        m_pRedisTopic->subscribe(listTopic);
        m_pRedisTopic->consume();
        return true;
    }

    bool ZGOPTaskOutageMng::initParams()
    {
        std::string sql = "SELECT id, name, nameL2 FROM op_dict_task_state";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapTaskState))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务状态信息失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM op_dict_task_stage";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapTaskStage))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务阶段信息失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM op_dict_outage_type";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapOutageType))
        {
            ZGLOG_ERROR(QStringLiteral("获取断电类型信息失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM op_dict_outage_stage";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapOutageStage))
        {
            ZGLOG_ERROR(QStringLiteral("获取断电阶段信息失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM op_dict_outage_state";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapOutageState))
        {
            ZGLOG_ERROR(QStringLiteral("获取断电状态信息失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM mp_dict_device_type";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapDeviceType))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备类型信息失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM op_dict_outage_device_state";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapOutageDeviceState))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备状态信息失败"));
            return false;
        }
        sql = "SELECT id, name, nameL2 FROM sp_dict_operator";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapOperator))
        {
            ZGLOG_ERROR(QStringLiteral("获取操作符信息失败"));
            return false;
        }
        // 获取所有隔开和接地的设备ID
        sql =
            "SELECT id FROM mp_param_device WHERE (typeID = 'ZG_DT_DISCONNECTOR' OR typeID = 'ZG_DT_GROUND_SWITCH') AND isEnable = 1";
        if (!ZGProxyCommon::execQuerySqlCol(sql, m_listDeviceID))
        {
            ZGLOG_ERROR(QStringLiteral("获取隔开与接地设备ID失败"));
            return false;
        }
        sql = "SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_GROUND_SWITCH' AND isEnable = 1";
        if (!ZGProxyCommon::execQuerySqlCol(sql, m_listGroundDeviceID))
        {
            ZGLOG_ERROR(QStringLiteral("获取接地开关设备ID失败"));
            return false;
        }
        sql = "SELECT a.srcDeviceID AS id, a.dstDeviceID FROM mp_param_device_relation a "
            "LEFT JOIN mp_param_device b ON a.dstDeviceID = b.id "
            "WHERE b.subtypeID = 'ZG_DS_DISCONNECTOR_OVERZONE' AND a.relationTypeID = 'ZG_RT_INTERLOCK'";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapGroundDeviceAssocOverZoneDevice))
        {
            ZGLOG_ERROR(QStringLiteral("获取接地开关关联分段隔离开关失败"));
            return false;
        }
        sql = "SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_SAFE_LOCK' AND isEnable = 1";
        if (!ZGProxyCommon::execQuerySqlCol(sql, m_listLockDeviceID))
        {
            ZGLOG_ERROR(QStringLiteral("获取安全闭锁装置ID失败"));
            return false;
        }
        sql = "SELECT a.srcDeviceID AS id, a.dstDeviceID FROM mp_param_device_relation a "
            "LEFT JOIN mp_param_device b ON a.dstDeviceID = b.id "
            "WHERE b.typeID IN ('ZG_DT_GROUDING_INDICATOR', 'ZG_DT_DISCONNECTOR_INDICATIOR') "
            "AND b.isEnable = 1 ORDER BY id";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDeviceAssocIndiDevice))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备关联警示牌失败"));
            return false;
        }
        sql = "SELECT a.srcDeviceID AS id, a.dstDeviceID FROM mp_param_device_relation a "
            "LEFT JOIN mp_param_device b ON a.dstDeviceID = b.id "
            "WHERE b.typeID = 'ZG_DT_SAFE_LOCK' AND b.isEnable = 1 ORDER BY id";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDeviceAssocLockDevice))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备关联闭锁装置失败"));
            return false;
        }
        for (const auto& [deviceID, lockDeviceID] : m_mapDeviceAssocLockDevice)
        {
            m_mapLockDeviceAssocDevice[lockDeviceID] = deviceID;
        }
        sql = "SELECT a.srcDeviceID AS id, a.dstDeviceID FROM mp_param_device_relation a "
            "LEFT JOIN mp_param_device b ON a.dstDeviceID = b.id "
            "WHERE b.typeID = 'ZG_DT_CONTINUOUS_CHECK' AND b.isEnable = 1 ORDER BY id";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapGroundDeviceAssocContinuityDevice))
        {
            ZGLOG_ERROR(QStringLiteral("获取接地开关关联连续检测装置失败"));
            return false;
        }
        sql = QString("SELECT DISTINCT a.deviceID AS id, b.id AS ownerDeviceID FROM mp_param_dataset_yx a "
            "INNER JOIN mp_param_device b ON a.datasetID = b.datasetID "
            "WHERE a.deviceID <> '' AND b.typeID = 'ZG_DT_MC' AND b.isEnable = 1 ORDER BY id").toStdString();
        StringMap deviceOwner;
        if (!ZGProxyCommon::execQuerySqlPair(sql, deviceOwner))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备关联测控装置失败"));
            return false;
        }
        m_mapPlcAssocDevice.clear();
        for (const auto& [deviceID, ownerDeviceID] : deviceOwner)
        {
            m_mapPlcAssocDevice[ownerDeviceID].push_back(deviceID);
        }
        sql =
            "SELECT CONCAT(dataCategoryID, '/', propValue) AS id, propName, propNameL2 AS name FROM mp_param_data_category_property";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapDataCategoryProperty))
        {
            ZGLOG_ERROR(u8"获取数据类别属性失败");
            return false;
        }
        sql = "SELECT id, name, nameL2, position FROM sp_param_appnode";
        if (!ZGProxyCommon::execQuerySql(sql, m_mapAppNode))
        {
            ZGLOG_ERROR(u8"获取应用节点信息失败");
            return false;
        }
        sql = "SELECT id FROM mp_param_device WHERE isEnable = 1 AND subtypeID = 'ZG_DS_DISCONNECTOR_OVERZONE'";
        if (!ZGProxyCommon::execQuerySqlCol(sql, m_listOverZoneDeviceID))
        {
            ZGLOG_ERROR(u8"获取越区设备ID失败");
            return false;
        }
        sql = "SELECT id FROM mp_param_device WHERE isEnable = 1 AND typeID = 'ZG_DT_SMS'";
        if (!ZGProxyCommon::execQuerySqlCol(sql, m_listSMSDeviceID))
        {
            ZGLOG_ERROR(u8"获取短信设备ID失败");
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::initNodeNetwork()
    {
        m_pNodeNetwork = new NodeNetwork;
        QString sql = "SELECT a.appnodeID, a.deviceID, b.subtypeID FROM mp_param_appnode_device a "
            "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
            "WHERE b.isEnable = 1 ORDER BY a.appnodeID";
        ListStringMap listAppNodeDevice;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listAppNodeDevice))
        {
            ZGLOG_ERROR(QStringLiteral("获取应用节点设备信息失败"));
            return false;
        }
        for (const auto& appNodeDevice : listAppNodeDevice)
        {
            const auto& appNodeID = ZGUtils::get(appNodeDevice, "appnodeID");
            const auto& deviceID = ZGUtils::get(appNodeDevice, "deviceID");
            const auto& deviceSubtypeID = ZGUtils::get(appNodeDevice, "subtypeID");
            m_mapAppNodeDevices[appNodeID][deviceID] = deviceSubtypeID;
        }
        return true;
    }

    void ZGOPTaskOutageMng::initVariable()
    {
        m_mapOutageTypeStage = std::map<std::string, StringList>{
            {
                "ZG_OT_OUTAGE", StringList{
                    "ZG_OS_OUTAGE", "ZG_OS_GROUND", "ZG_OS_PTW", "ZG_OS_REMOVE_GROUND",
                    "ZG_OS_RESTORE"
                }
            },
            {
                "ZG_OT_OUTAGETEST", StringList{
                    "ZG_OS_OUTAGE", "ZG_OS_GROUND", "ZG_OS_PTW", "ZG_OS_REQUEST_SFT",
                    "ZG_OS_TEST", "ZG_OS_CANCEL_SFT", "ZG_OS_REMOVE_GROUND", "ZG_OS_RESTORE"
                }
            },
            {
                "ZG_OT_TEST",
                StringList{
                    "ZG_OS_OUTAGE", "ZG_OS_REQUEST_SFT", "ZG_OS_TEST", "ZG_OS_CANCEL_SFT", "ZG_OS_REMOVE_GROUND",
                    "ZG_OS_RESTORE"
                }
            }
        };
        m_mapGroundDeviceRule = StringMap{
            {"CommState", "2"}, {"ControlMode", "2"}, {"LockState", "1"},
            {"Elec", "1"}, {"LineVoltageInconsistent", "1"},
            {"MechanismFailure", "1"}, {"SensorFailure", "1"}
        };
    }

    bool ZGOPTaskOutageMng::initLanguage()
    {
        QString sql = QString("SELECT firstLanguageID, secondLanguageID FROM sp_param_system");
        ZG6000::StringMap mapLanguage;
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), mapLanguage))
        {
            ZGLOG_ERROR("获取系统语言参数失败");
            return false;
        }
        m_firstLanguage = mapLanguage["firstLanguageID"];
        if (m_firstLanguage.empty())
        {
            m_firstLanguage = "ZG_DL_CN";
        }
        m_secondLanguage = mapLanguage["secondLanguageID"];
        if (!ZGUtils::initLanguage(m_serverName))
        {
            ZGLOG_ERROR(QStringLiteral("初始化语言配置失败"));
            return false;
        }
        ZGLOG_INFO(
            QString("first language: %1, second language: %2").arg(m_firstLanguage.c_str()).arg(m_secondLanguage.c_str()
            ));
        return true;
    }

    bool ZGOPTaskOutageMng::initKey()
    {
        m_aesKey = QByteArray("Y2R6Z3lmYnpnNjAw");
        return true;
    }

    bool ZGOPTaskOutageMng::initTask(const std::string& taskID,
                                     ErrorInfo& e)
    {
        StringList listSql;
        // 删除任务用户表中的数据
        listSql.push_back(
            QString("DELETE FROM op_param_outage_task_user WHERE taskID = '%1'").arg(taskID.c_str()).toStdString());
        // 将任务头部的内容清空
        StringMap taskParam;
        taskParam["id"] = taskID;
        taskParam["rtTaskStageID"] = "ZG_TS_INIT";
        taskParam["rtTaskStateID"] = "ZG_TS_FINISHED";
        taskParam["rtCreateUserID"] = "";
        taskParam["rtCreateTime"] = "";
        taskParam["rtStartTime"] = "";
        taskParam["rtEndTime"] = "";
        taskParam["rtExecStartTime"] = "";
        taskParam["rtExecEndTime"] = "";
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskParam));
        StringMap outageParam;
        outageParam["id"] = taskID;
        outageParam["rtModifyTime"] = "";
        outageParam["rtOutageStageID"] = "";
        outageParam["rtOutageStateID"] = "";
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task", outageParam));
        StringMap deviceParam;
        deviceParam["rtDeviceStateID"] = "ZG_DOD_NORMAL";
        deviceParam["rtIsSwitchClose"] = "0";
        const auto& condition = "taskID = '" + taskID + "'";
        const auto& sql = ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceParam, condition);
        ZGLOG_TRACE(sql.c_str());
        listSql.push_back(sql);
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("初始化任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }

        return true;
    }

    bool ZGOPTaskOutageMng::deleteTask(const std::string& taskID,
                                       ErrorInfo& e)
    {
        StringList listSql;
        listSql.push_back(QString("DELETE FROM op_param_task WHERE id = '%1'").arg(taskID.c_str()).toStdString());
        listSql.push_back(
            QString("DELETE FROM op_param_outage_task WHERE id = '%1'").arg(taskID.c_str()).toStdString());
        listSql.push_back(
            QString("DELETE FROM op_param_outage_task_device WHERE taskID = '%1'").arg(taskID.c_str()).toStdString());
        listSql.push_back(
            QString("DELETE FROM op_param_outage_task_user WHERE taskID = '%1'").arg(taskID.c_str()).toStdString());
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("删除任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::saveTask(const std::string& taskID,
                                     const QDateTime& execStartTime,
                                     ErrorInfo& e)
    {
        // 获取任务开始时间
        std::string year;
        if (!getHistorySaveYear(taskID, year, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取保存年份失败"));
            return false;
        }
        std::string historyTaskTable = "op_his_outage_task_" + year;
        std::string historyTaskDeviceTable = "op_his_outage_task_device_" + year;
        std::string historyTaskUserTable = "op_his_outage_task_user_" + year;
        std::string historyTaskEventTable = "op_his_outage_task_event_" + year;
        StringMap head;
        ListStringMap devices;
        ListStringMap users;
        // 调用getTaskInfo获取任务信息
        if (!getTaskInfo(taskID, head, devices, users, e, Ice::Current()))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务信息失败"));
            return false;
        }
        // 保存任务
        StringList listSql;
        // 如果任务已经保存过，则删除原来的数据
        listSql.push_back("DELETE FROM " + historyTaskTable + " WHERE id = '" + taskID + "';");
        listSql.push_back("DELETE FROM " + historyTaskDeviceTable + " WHERE taskID = '" + taskID + "';");
        listSql.push_back("DELETE FROM " + historyTaskUserTable + " WHERE taskID = '" + taskID + "';");
        listSql.push_back("DELETE FROM " + historyTaskEventTable + " WHERE taskID = '" + taskID + "';");
        StringMap historyHead;
        try
        {
            historyHead["id"] = taskID;
            historyHead["name"] = ZGUtils::get(head, "name");
            historyHead["outageTypeID"] = ZGUtils::get(head, "outageTypeID");
            historyHead["outageTypeName"] = ZGUtils::get(head, "outageTypeName");
            historyHead["outageTypeNameL2"] = ZGUtils::get(head, "outageTypeNameL2");
            historyHead["rtCreateUserID"] = ZGUtils::get(head, "rtCreateUserID");
            historyHead["rtCreateUserName"] = ZGUtils::get(head, "rtCreateUserName");
            historyHead["rtCreateTime"] = ZGUtils::get(head, "rtCreateTime");
            historyHead["rtStartTime"] = ZGUtils::get(head, "rtStartTime");
            historyHead["rtEndTime"] = ZGUtils::get(head, "rtEndTime");
            historyHead["rtModifyTime"] = ZGUtils::get(head, "rtModifyTime");
            historyHead["rtExecStartTime"] = ZGUtils::DateTimeToString(execStartTime, true).toStdString();
            listSql.push_back(ZGUtils::generateInsertSql(historyTaskTable, historyHead));
            for (const auto& device : devices)
            {
                StringMap historyDevice;
                historyDevice["id"] = ZGUtils::get(device, "id");
                historyDevice["taskID"] = taskID;
                historyDevice["deviceID"] = ZGUtils::get(device, "deviceID");
                historyDevice["deviceName"] = ZGUtils::get(device, "deviceName");
                historyDevice["deviceNameL2"] = ZGUtils::get(device, "deviceNameL2");
                historyDevice["deviceTypeID"] = ZGUtils::get(device, "deviceTypeID");
                historyDevice["deviceTypeName"] = ZGUtils::get(device, "deviceTypeName");
                historyDevice["deviceTypeNameL2"] = ZGUtils::get(device, "deviceTypeNameL2");
                historyDevice["appNodeID"] = ZGUtils::get(device, "appNodeID");
                historyDevice["appNodeName"] = ZGUtils::get(device, "appNodeName");
                historyDevice["appNodeNameL2"] = ZGUtils::get(device, "appNodeNameL2");
                historyDevice["dstState"] = ZGUtils::get(device, "dstState");
                listSql.push_back(ZGUtils::generateInsertSql(historyTaskDeviceTable, historyDevice));
            }
            for (const auto& user : users)
            {
                StringMap historyUser;
                historyUser["id"] = ZGUtils::get(user, "id");
                historyUser["taskID"] = taskID;
                historyUser["userID"] = ZGUtils::get(user, "userID");
                historyUser["userName"] = ZGUtils::get(user, "userName");
                historyUser["mobileNumber"] = ZGUtils::get(user, "mobileNumber");
                historyUser["rtSafetyFileCode"] = ZGUtils::get(user, "rtSafetyFileCode");
                listSql.push_back(ZGUtils::generateInsertSql(historyTaskUserTable, historyUser));
            }
            const auto& event = ZGUtils::languageString(firstLanguage(), "startTask"); // 第一语言
            const auto& eventL2 = ZGUtils::languageString(secondLanguage(), "startTask"); // 第二语言
            std::string uuid;
            if (!ZGProxyCommon::createUUID(uuid))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("生成UUID失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            StringMap outageAction;
            outageAction["id"] = uuid;
            outageAction["taskID"] = taskID;
            outageAction["rtOutageStageID"] = "ZG_OS_OUTAGE";
            auto pair = m_mapOutageStage.find("ZG_OS_OUTAGE");
            if (pair != m_mapOutageStage.end())
            {
                auto outageStageName = pair->second;
                outageAction["rtOutageStageName"] = outageStageName["name"];
                outageAction["rtOutageStageNameL2"] = outageStageName["nameL2"];
            }
            outageAction["event"] = event;
            outageAction["eventL2"] = eventL2;
            outageAction["rtExecTime"] = ZGUtils::DateTimeToString(execStartTime, true).toStdString();
            listSql.push_back(ZGUtils::generateInsertSql(historyTaskEventTable, outageAction));
            if (!ZGProxyCommon::execBatchSql(listSql, true))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("保存任务信息失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::saveTaskUserChange(const std::string& taskID,
                                               const ListStringMap& oldUsers,
                                               const ListStringMap& newUsers,
                                               ErrorInfo& e)
    {
        QDateTime dt = QDateTime::currentDateTime();
        StringList listSql;
        std::string year;
        if (!getHistorySaveYear(taskID, year, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取保存年份失败"));
            return false;
        }
        std::string historyTaskUserTable = "op_his_outage_task_user_" + year;
        // 根据任务ID和用户ID更新旧用户的结束时间
        for (const auto& user : oldUsers)
        {
            StringMap userParam;
            userParam["rtEndTime"] = ZGUtils::DateTimeToString(dt).toStdString();
            std::string condition = "taskID = '" + taskID + "' AND userID = '" + ZGUtils::get(user, "userID") + "'";
            listSql.push_back(ZGUtils::generateUpdateSql(historyTaskUserTable, userParam, condition));
        }
        // 插入新用户
        StringList listUUID;
        if (!ZGProxyCommon::createUUID(static_cast<int>(newUsers.size()), listUUID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("生成UUID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (size_t i = 0; i < newUsers.size(); ++i)
        {
            const auto& user = newUsers[i];
            StringMap userParam;
            userParam["id"] = listUUID[i];
            userParam["taskID"] = taskID;
            userParam["userID"] = ZGUtils::get(user, "userID");
            userParam["userName"] = ZGUtils::get(user, "userName");
            userParam["mobileNumber"] = ZGUtils::get(user, "mobileNumber");
            userParam["rtSafetyFileCode"] = ZGUtils::get(user, "rtSafetyFileCode");
            userParam["rtStartTime"] = ZGUtils::DateTimeToString(dt, true).toStdString();
            listSql.push_back(ZGUtils::generateInsertSql(historyTaskUserTable, userParam));
        }
        if (!ZGProxyCommon::execBatchSql(listSql, true))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("保存任务用户信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::saveTaskUserStartTime(const std::string& taskID,
                                                  ErrorInfo& e)
    {
        StringList listID;
        QString sql = QString("SELECT id FROM op_param_outage_task_user WHERE taskID = '%1'").arg(taskID.c_str());
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务人员信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        std::string year;
        if (!getHistorySaveYear(taskID, year, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取保存年份失败"));
            return false;
        }
        std::string historyTaskUserTable = "op_his_outage_task_user_" + year;
        QDateTime dt = QDateTime::currentDateTime();
        StringList listSql;
        for (const auto& id : listID)
        {
            StringMap userParam;
            userParam["id"] = id;
            userParam["rtStartTime"] = ZGUtils::DateTimeToString(dt, true).toStdString();
            listSql.push_back(ZGUtils::generateUpdateSql(historyTaskUserTable, userParam));
        }
        if (!ZGProxyCommon::execBatchSql(listSql, true))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("保存任务用户信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::saveTaskUserEndTime(const std::string& taskID,
                                                ErrorInfo& e)
    {
        StringList listID;
        QString sql = QString("SELECT id FROM op_param_outage_task_user WHERE taskID = '%1'").arg(taskID.c_str());
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务人员信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        std::string year;
        if (!getHistorySaveYear(taskID, year, e))
        {
            ZGLOG_ERROR(QStringLiteral("获取保存年份失败"));
            return false;
        }
        std::string historyTaskUserTable = "op_his_outage_task_user_" + year;
        QDateTime dt = QDateTime::currentDateTime();
        StringList listSql;
        for (const auto& id : listID)
        {
            StringMap userParam;
            userParam["id"] = id;
            userParam["rtEndTime"] = ZGUtils::DateTimeToString(dt).toStdString();
            listSql.push_back(ZGUtils::generateUpdateSql(historyTaskUserTable, userParam, "id = '" + id + "'"));
        }
        if (!ZGProxyCommon::execBatchSql(listSql, true))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("保存任务用户信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    void ZGOPTaskOutageMng::calculateLockState()
    {
        // 获取任务中设备当前的锁定状态
        QString sql =
            "SELECT a.id, a.taskID, a.deviceID, a.rtDeviceStateID, a.rtUpdateTime FROM op_param_outage_task_device a "
            "LEFT JOIN op_param_task b ON a.taskID = b.id WHERE b.rtTaskStageID = 'ZG_TS_EXECUTE' ORDER BY a.id";
        ListStringMap listRecord;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
        {
            ZGLOG_ERROR(QStringLiteral("获取锁定和解锁设备失败"));
            return;
        }
        QDateTime currentTime = QDateTime::currentDateTime();
        try
        {
            StringList listSql;
            for (auto& record : listRecord)
            {
                const auto& keyID = ZGUtils::get(record, "id");
                const auto& taskID = ZGUtils::get(record, "taskID");
                const auto& deviceID = ZGUtils::get(record, "deviceID");
                const auto& rtDeviceStateID = ZGUtils::get(record, "rtDeviceStateID");
                if (rtDeviceStateID != "ZG_DOD_LOCKING" && rtDeviceStateID != "ZG_DOD_UNLOCKING")
                {
                    continue;
                }
                const auto& updateTime = ZGUtils::get(record, "rtUpdateTime");
                QDateTime dtUpdateTime;
                // 如果时间格式不正确，恢复原有状态
                if (!ZGUtils::StringToDateTime(updateTime.c_str(), dtUpdateTime))
                {
                    ZGLOG_ERROR(QStringLiteral("设备'%1'无效的时间'%2'").arg(deviceID.c_str()).arg(updateTime.c_str()));
                    StringMap deviceParam;
                    deviceParam["id"] = keyID;
                    if (rtDeviceStateID == "ZG_DOD_LOCKING")
                    {
                        deviceParam["rtDeviceStateID"] = "ZG_DOD_NORMAL";
                    }
                    else
                    {
                        deviceParam["rtDeviceStateID"] = "ZG_DOD_LOCKED";
                    }
                    deviceParam["rtUpdateTime"] = ZGUtils::DateTimeToString(currentTime).toStdString();
                    listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceParam));
                    continue;
                }
                // 如果执行超时，恢复原有状态
                if (dtUpdateTime.secsTo(currentTime) > 30)
                {
                    ZGLOG_TRACE(QString("Task %1 device %2 restore state").arg(taskID.c_str()).arg(deviceID.c_str()));
                    StringMap deviceParam;
                    deviceParam["id"] = keyID;
                    if (rtDeviceStateID == "ZG_DOD_LOCKING")
                    {
                        deviceParam["rtDeviceStateID"] = "ZG_DOD_NORMAL";
                    }
                    else
                    {
                        deviceParam["rtDeviceStateID"] = "ZG_DOD_LOCKED";
                    }
                    deviceParam["rtUpdateTime"] = ZGUtils::DateTimeToString(currentTime).toStdString();
                    listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceParam));
                    continue;
                }
                std::string deviceLockState;
                ErrorInfo e;
                if (!ZGProxyCommon::getPropertyValue(deviceID, "LockState", deviceLockState, e))
                {
                    ZGLOG_ERROR(e);
                    ZGLOG_ERROR(QStringLiteral("获取设备锁定状态失败"));
                    continue;
                }
                // 锁定成功，更新状态
                if (rtDeviceStateID == "ZG_DOD_LOCKING" && deviceLockState == "2")
                {
                    if (dtUpdateTime.secsTo(currentTime) > 3)
                    {
                        StringMap deviceParam;
                        deviceParam["id"] = keyID;
                        deviceParam["rtDeviceStateID"] = "ZG_DOD_LOCKED";
                        deviceParam["rtUpdateTime"] = ZGUtils::DateTimeToString(currentTime).toStdString();
                        listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceParam));
                        ZGLOG_TRACE(QString("task %1 device %2 locked").arg(taskID.c_str()).arg(deviceID.c_str()));
                    }
                }
                // 解锁成功，更新状态
                if (rtDeviceStateID == "ZG_DOD_UNLOCKING" && deviceLockState == "1")
                {
                    if (dtUpdateTime.secsTo(currentTime) > 3)
                    {
                        StringMap deviceParam;
                        deviceParam["id"] = keyID;
                        deviceParam["rtDeviceStateID"] = "ZG_DOD_NORMAL";
                        deviceParam["rtUpdateTime"] = ZGUtils::DateTimeToString(currentTime).toStdString();
                        listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceParam));
                        ZGLOG_TRACE(QString("task %1 device %2 unlocked").arg(taskID.c_str()).arg(deviceID.c_str()));
                    }
                }
            }
            if (!listSql.empty())
            {
                if (!ZGProxyCommon::execBatchSql(listSql))
                {
                    ZGLOG_ERROR(QStringLiteral("更新设备状态失败"));
                }
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    /**
     * 计算设备可用性状态
     */
    void ZGOPTaskOutageMng::calculateDeviceState()
    {
        // 获取设备的任务数量
        QString sql = "SELECT a.deviceID AS id, COUNT(*) AS CNT FROM op_param_outage_task_device a "
            "LEFT JOIN op_param_task b ON a.taskID = b.id "
            "LEFT JOIN mp_param_device c ON a.deviceID = c.id "
            "WHERE b.rtTaskStageID = 'ZG_TS_EXECUTE' AND c.isEnable = 1 GROUP BY a.deviceID ORDER BY id";
        StringMap deviceTaskCount;
        if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), deviceTaskCount))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备任务数量失败"));
            return;
        }
        // 计算最新的任务数量和锁定数量，如果发生变化则更新到实时库中
        MapStringMap deviceRawState;
        ErrorInfo e;
        if (!ZGProxyCommon::mgetPropertyValues(m_listDeviceID, {
            "LockCount", "TaskCount", "Availbility"
        }, deviceRawState, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
        MapStringMap deviceState;
        for (const auto& deviceID : m_listDeviceID)
        {
            auto pairRaw = deviceRawState.find(deviceID);
            if (pairRaw == deviceRawState.end())
            {
                continue;
            }
            const auto& oldTaskCount = ZGUtils::get(pairRaw->second, "TaskCount");
            std::string newTaskCount;
            const auto& it = deviceTaskCount.find(deviceID);
            if (it == deviceTaskCount.end())
            {
                newTaskCount = "0";
            }
            else
            {
                newTaskCount = it->second;
            }
            if (oldTaskCount != newTaskCount)
            {
                deviceState[deviceID]["TaskCount"] = newTaskCount;
                deviceRawState[deviceID]["TaskCount"] = newTaskCount;
            }
        }
        sql = "SELECT deviceID AS id, COUNT(*) AS CNT FROM op_param_outage_task_device "
            "WHERE rtDeviceStateID IN ('ZG_DOD_LOCKED', 'ZG_DOD_UNLOCKING') GROUP BY deviceID";
        StringMap deviceLockCount;
        if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), deviceLockCount))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备锁定数量失败"));
            return;
        }
        for (const auto& deviceID : m_listDeviceID)
        {
            auto pairRaw = deviceRawState.find(deviceID);
            if (pairRaw == deviceRawState.end())
            {
                continue;
            }
            const auto& oldLockCount = ZGUtils::get(pairRaw->second, "LockCount");
            std::string newLockCount;
            const auto& it = deviceLockCount.find(deviceID);
            if (it == deviceLockCount.end())
            {
                newLockCount = "0";
            }
            else
            {
                newLockCount = it->second;
            }
            if (oldLockCount != newLockCount)
            {
                deviceState[deviceID]["LockCount"] = newLockCount;
                deviceRawState[deviceID]["LockCount"] = newLockCount;
            }
        }
        sql = "SELECT a.deviceID AS id, COUNT(a.id) AS CNT FROM op_param_outage_task_device a "
            "LEFT JOIN op_param_task b ON a.taskID = b.id "
            "WHERE b.rtTaskStageID <> 'ZG_TS_INIT' GROUP BY deviceID ORDER BY id";
        StringMap deviceNormalCount;
        if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), deviceNormalCount))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备未锁定数量失败"));
            return;
        }
        // 根据设备的锁定数量和任务数量更新设备的可用性状态
        for (const auto& deviceID : m_listDeviceID)
        {
            auto pairRaw = deviceRawState.find(deviceID);
            if (pairRaw == deviceRawState.end())
            {
                continue;
            }
            const auto& oldAvailbility = ZGUtils::get(pairRaw->second, "Availbility");
            std::string newAvailbility;
            auto it = deviceLockCount.find(deviceID);
            if (it != deviceLockCount.end())
            {
                newAvailbility = "3";
            }
            else
            {
                it = deviceNormalCount.find(deviceID);
                if (it != deviceNormalCount.end())
                {
                    newAvailbility = "2";
                }
                else
                {
                    newAvailbility = "1";
                }
            }
            if (oldAvailbility != newAvailbility)
            {
                deviceState[deviceID]["Availbility"] = newAvailbility;
            }
        }
        if (!deviceState.empty())
        {
            if (!ZGProxyCommon::mupdatePropertyValues(deviceState, e))
            {
                ZGLOG_ERROR(e);
            }
        }
    }

    void ZGOPTaskOutageMng::updatePLCDeviceState()
    {
        MapStringMap deviceRawState;
        ErrorInfo e;
        if (!ZGProxyCommon::mgetPropertyValues(m_listDeviceID, {
            "LockCountPLC", "TaskCountPLC",
            "LockCount", "TaskCount"
        }, deviceRawState, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
        for (const auto& deviceID : m_listDeviceID)
        {
            auto pair = m_mapDeviceAssocLockDevice.find(deviceID);
            if (pair == m_mapDeviceAssocLockDevice.end())
            {
                continue;
            }
            const auto& lockDeviceID = pair->second;
            std::string commState;
            if (ZGProxyCommon::getPropertyValue(lockDeviceID, "CommState", commState, e))
            {
                deviceRawState[deviceID]["CommState"] = commState;
            }
        }
        try
        {
            for (const auto& [deviceID, property] : deviceRawState)
            {
                const auto& lockCountPLC = ZGUtils::get(property, "LockCountPLC");
                const auto& taskCountPLC = ZGUtils::get(property, "TaskCountPLC");
                auto pair = property.find("CommState");
                if (pair == property.end())
                {
                    continue;
                }
                const auto& commState = pair->second;
                if (commState != "2")
                {
                    continue;
                }
                const auto& taskCount = ZGUtils::get(deviceRawState[deviceID], "TaskCount");
                const auto& lockCount = ZGUtils::get(deviceRawState[deviceID], "LockCount");
                // 将PLC的任务数量与锁定数量与系统计算的值进行比对，如果不一致则向PLC发送最新的计算值
                if (lockCountPLC != lockCount)
                {
                    sendCtrlCommand("", deviceID, "CMD_LockCount", lockCount);
                }
                if (taskCountPLC != taskCount)
                {
                    sendCtrlCommand("", deviceID, "CMD_TaskCount", taskCount);
                }
            }
        }
        catch (const std::exception& ex)
        {
            ZGLOG_ERROR(ex.what());
        }
    }

    void ZGOPTaskOutageMng::recheckRebootContinueDevice()
    {
        for (const auto& [deviceID, continueDeviceID] : m_mapGroundDeviceAssocContinuityDevice)
        {
            // 获取连续性监测模块的初始化状态
            StringMap continueDeviceState;
            ErrorInfo e;
            if (!ZGProxyCommon::getPropertyValues(continueDeviceID,
                {"CommState", "InitState", "OHLTestInOutState", "RTTestInOutState"}, continueDeviceState, e))
            {
                ZGLOG_ERROR(e);
                continue;
            }
            const auto& commState = ZGUtils::get(continueDeviceState, "CommState");
            if (commState != "2")
            {
                continue;
            }
            const auto& initState = ZGUtils::get(continueDeviceState, "InitState");
            const auto& ohlTestInOutState = ZGUtils::get(continueDeviceState, "OHLTestInOutState");
            const auto& rtTestInOutState = ZGUtils::get(continueDeviceState, "RTTestInOutState");
            if (initState == "2")
            {
                // 获取接地开关的位置状态
                StringMap deviceState;
                if (!ZGProxyCommon::getPropertyValues(deviceID, {"CommState", "PosClose", "PosOpen"}, deviceState, e))
                {
                    ZGLOG_ERROR(e);
                    continue;
                }
                // 如果通信状态正常且位置状态为闭合，则向连续性监测模块发送测试命令
                const auto& groundSwitchCommState = ZGUtils::get(deviceState, "CommState");
                const auto& posClose = ZGUtils::get(deviceState, "PosClose");
                const auto& posOpen = ZGUtils::get(deviceState, "PosOpen");
                if (groundSwitchCommState == "2" && posClose == "2" && posOpen == "1")
                {
                    if (ohlTestInOutState == "2")
                    {
                        sendCtrlCommand("", continueDeviceID, "CMD_OHLTest", "2");
                    }
                    if (rtTestInOutState == "2")
                    {
                        sendCtrlCommand("", continueDeviceID, "CMD_RTTest", "2");
                    }
                }
            }
        }
    }

    void ZGOPTaskOutageMng::lockUnlockedDevice()
    {
        QString sql = QString(
            "SELECT DISTINCT deviceID FROM op_param_outage_task_device WHERE (rtDeviceStateID = 'ZG_DOD_LOCKED' "
            "OR rtDeviceStateID = 'ZG_DOD_LOCKING') ORDER BY deviceID");
        StringList listDeviceID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
        {
            ZGLOG_ERROR(QStringLiteral("获取锁定设备失败"));
            return;
        }
        if (listDeviceID.empty())
        {
            return;
        }
        MapStringMap deviceLockState;
        ErrorInfo e;
        if (!ZGProxyCommon::mgetPropertyValues(listDeviceID, {"LockState"}, deviceLockState, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
        for (const auto& [deviceID, property] : deviceLockState)
        {
            // 根据设备找到关联的闭锁装置
            auto it = m_mapDeviceAssocLockDevice.find(deviceID);
            if (it == m_mapDeviceAssocLockDevice.end())
            {
                continue;
            }
            const auto& lockDeviceID = it->second;
            StringMap lockDeviceState;
            if (!ZGProxyCommon::getPropertyValues(lockDeviceID, {"CommState", "InOutState"}, lockDeviceState, e))
            {
                ZGLOG_ERROR(e);
                continue;
            }
            if (lockDeviceState["CommState"] != "2")
            {
                continue;
            }
            if (lockDeviceState["InOutState"] != "2")
            {
                continue;
            }
            bool exist;
            if (!ZGProxyCommon::isPropertyExists(deviceID, "SuperControl", exist, e))
            {
                ZGLOG_ERROR(e);
                tryLockUnlockedDevice(deviceID, property);
                continue;
            }
            if (!exist)
            {
                tryLockUnlockedDevice(deviceID, property);
                continue;
            }
            StringMap superCtrlState;
            if (!ZGProxyCommon::getProperty(deviceID, "SuperControl", superCtrlState, e))
            {
                ZGLOG_ERROR(e);
                tryLockUnlockedDevice(deviceID, property);
                continue;
            }
            try
            {
                const auto& rtNewValue = ZGUtils::get(superCtrlState, "rtNewValue");
                const auto& rtUpdateTime = ZGUtils::get(superCtrlState, "rtUpdateTime");
                // 如果不为超级控制模式，尝试锁定未锁定的设备
                if (rtNewValue != "2")
                {
                    tryLockUnlockedDevice(deviceID, property);
                }
                else
                {
                    QDateTime dtUpdateTime;
                    if (!ZGUtils::StringToDateTime(rtUpdateTime.c_str(), dtUpdateTime))
                    {
                        if (!ZGProxyCommon::updatePropertyValue(deviceID, "SuperControl", "1", e))
                        {
                            ZGLOG_ERROR(e);
                        }
                        continue;
                    }
                    // 超级控制模式两分钟后自动复归
                    QDateTime currentTime = QDateTime::currentDateTime();
                    if ((rtNewValue == "2") && (dtUpdateTime.secsTo(currentTime) > 120))
                    {
                        if (!ZGProxyCommon::updatePropertyValue(deviceID, "SuperControl", "1", e))
                        {
                            ZGLOG_ERROR(e);
                        }
                    }
                }
            }
            catch (const std::exception& ex)
            {
                ZGLOG_ERROR(ex.what());
            }
        }
    }

    void ZGOPTaskOutageMng::unlockLockedDevice()
    {
        MapStringMap deviceLockState;
        ErrorInfo e;
        // 获取隔离开关与接地开关的锁定状态
        if (!ZGProxyCommon::mgetPropertyValues(m_listDeviceID, {"LockState"}, deviceLockState, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
        StringList listLockedDevice;
        for (const auto& [deviceID, deviceState] : deviceLockState)
        {
            try
            {
                // 获取隔离开关和接地开关关联的闭锁装置
                auto pair = m_mapDeviceAssocLockDevice.find(deviceID);
                if (pair == m_mapDeviceAssocLockDevice.end())
                {
                    continue;
                }
                const auto& lockDeviceID = pair->second;
                StringMap lockDeviceState;
                // 获取闭锁装置的通信状态
                if (!ZGProxyCommon::getPropertyValues(lockDeviceID, {"CommState"}, lockDeviceState, e))
                {
                    ZGLOG_ERROR(e);
                    continue;
                }
                if (lockDeviceState["CommState"] != "2")
                {
                    continue;
                }
                const auto& lockState = ZGUtils::get(deviceState, "LockState");
                if (lockState != "2")
                {
                    continue;
                }
                listLockedDevice.push_back(deviceID);
            }
            catch (const std::exception& ex)
            {
                ZGLOG_ERROR(ex.what());
            }
        }
        // 获取到任务中已经锁定或者正在锁定中的设备
        QString sql =
            "SELECT DISTINCT deviceID FROM op_param_outage_task_device WHERE (rtDeviceStateID = 'ZG_DOD_LOCKED' "
            "OR rtDeviceStateID = 'ZG_DOD_LOCKING') ORDER BY deviceID";
        StringList listTaskLockedDevice;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listTaskLockedDevice))
        {
            ZGLOG_ERROR(QStringLiteral("获取锁定设备失败"));
            return;
        }
        // 对于设备状态为锁定的设备，如果不在任务的锁定设备列表中，则解锁
        StringList listUnlockedDevice;
        std::set_difference(listLockedDevice.begin(), listLockedDevice.end(),
            listTaskLockedDevice.begin(), listTaskLockedDevice.end(), std::back_inserter(listUnlockedDevice));
        for (const auto& unlockedDevice : listUnlockedDevice)
        {
            ZGLOG_TRACE(QString("Try unlocking Device %1").arg(unlockedDevice.c_str()));
            // 发送解锁命令
            if (!sendCtrlCommand("", unlockedDevice, "CMD_LockStateOpen", "2"))
            {
                ZGLOG_ERROR(QStringLiteral("解锁设备失败"));
            }
        }
    }

    void ZGOPTaskOutageMng::tryLockUnlockedDevice(const std::string& deviceID,
                                                  const std::map<std::string, std::string>& property)
    {
        try
        {
            const auto& lockState = ZGUtils::get(property, "LockState");
            if (lockState != "2")
            {
                ZGLOG_TRACE(QString("Try locking Device %1").arg(deviceID.c_str()));
                if (!sendCtrlCommand("", deviceID, "CMD_LockStateClose", "2"))
                {
                    ZGLOG_ERROR(QStringLiteral("锁定设备失败"));
                }
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    void ZGOPTaskOutageMng::updateLEDTaskInfo()
    {
        QString sql;
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
            sql = "SELECT a.deviceID AS id, GROUP_CONCAT(a.taskID) AS taskID FROM op_param_outage_task_device a "
                "LEFT JOIN op_param_task b ON a.taskID = b.id "
                "WHERE b.rtTaskStageID = 'ZG_TS_EXECUTE' GROUP BY id";
        // SQL Server 使用STRING_AGG来实现
        if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
            sql = "SELECT a.deviceID AS id, STRING_AGG(a.taskID, ',') AS taskID FROM op_param_outage_task_device a "
                "LEFT JOIN op_param_task b ON a.taskID = b.id "
                "WHERE b.rtTaskStageID = 'ZG_TS_EXECUTE' GROUP BY a.deviceID ORDER BY id";
        StringMap deviceTasks;
        if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), deviceTasks))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备任务列表失败"));
            return;
        }
        StringMap mapTaskIndiDeviceAssocDevice;
        for (const auto& [deviceID, tasks] : deviceTasks)
        {
            auto pair = m_mapDeviceAssocIndiDevice.find(deviceID);
            if (pair == m_mapDeviceAssocIndiDevice.end())
            {
                continue;
            }
            mapTaskIndiDeviceAssocDevice[pair->second] = deviceID;
        }
        StringList listIndiDeviceID;
        for (const auto& [deviceID, indiDeviceID] : m_mapDeviceAssocIndiDevice)
        {
            listIndiDeviceID.push_back(indiDeviceID);
        }
        MapStringMap indiDevicesInfo;
        ErrorInfo e;
        if (!ZGProxyCommon::mgetPropertyValues(listIndiDeviceID, {"CommState", "TaskInfo"}, indiDevicesInfo, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
        for (const auto& [indiDeviceID, deviceInfo] : indiDevicesInfo)
        {
            try
            {
                const auto& commState = ZGUtils::get(deviceInfo, "CommState");
                const auto& taskInfo = ZGUtils::get(deviceInfo, "TaskInfo");
                if (commState != "2")
                {
                    continue;
                }
                auto listDeviceTaskID = getTaskListFromTaskInfo(taskInfo);
                auto it = mapTaskIndiDeviceAssocDevice.find(indiDeviceID);
                // 在当前任务设备列表中无该设备
                if (it == mapTaskIndiDeviceAssocDevice.end())
                {
                    if (!listDeviceTaskID.empty())
                    {
                        QJsonObject object;
                        object["type"] = "WT_LIST";
                        QJsonArray array;
                        object["IDs"] = array;
                        QJsonDocument doc(object);
                        QByteArray ba = doc.toJson();
                        sendCtrlCommand("", indiDeviceID, "CMD_TaskInfo", ba.toStdString());
                    }
                }
                else
                {
                    StringList listTaskID;
                    const auto& deviceID = it->second;
                    auto pair = deviceTasks.find(deviceID);
                    if (pair == deviceTasks.end())
                    {
                        continue;
                    }
                    ZGUtils::splitString(pair->second, ",", listTaskID);
                    std::sort(listTaskID.begin(), listTaskID.end());
                    std::sort(listDeviceTaskID.begin(), listDeviceTaskID.end());
                    StringList listMissingTaskID;
                    std::set_difference(listTaskID.begin(), listTaskID.end(),
                        listDeviceTaskID.begin(), listDeviceTaskID.end(), std::back_inserter(listMissingTaskID));
                    // 比较两个列表，如果不一致则使用sendCtrlCommand向设备发送命令
                    if (listTaskID != listDeviceTaskID)
                    {
                        QJsonObject newObject;
                        newObject["type"] = "WT_LIST";
                        QJsonArray newArray;
                        for (const auto& taskID : listTaskID)
                        {
                            newArray.append(taskID.c_str());
                        }
                        newObject["IDs"] = newArray;
                        QJsonDocument newDoc(newObject);
                        QByteArray ba = newDoc.toJson();
                        sendCtrlCommand("", indiDeviceID, "CMD_TaskInfo", ba.toStdString());
                    }
                    if (!listMissingTaskID.empty())
                    {
                        clearDeviceTaskInfo(indiDeviceID);
                        for (const auto& taskID : listMissingTaskID)
                        {
                            addTaskInfoToDevice(taskID, indiDeviceID);
                        }
                    }
                }
            }
            catch (const std::exception& ex)
            {
                ZGLOG_ERROR(ex.what());
                continue;
            }
        }
    }

    void ZGOPTaskOutageMng::updateDeviceCommState()
    {
        QString sql = QString("SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_MC' AND isEnable = 1");
        StringList listDeviceID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
        {
            ZGLOG_ERROR(QStringLiteral("获取测控装置失败"));
            return;
        }
        StringMap deviceState;
        if (!ZGProxyCommon::mgetDataByField("mp_param_device", listDeviceID, "rtState", deviceState))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备状态失败"));
            return;
        }
        // 根据deviceState中ownerDeviceID的状态更新关联设备的状态
        for (const auto& [deviceID, state] : deviceState)
        {
            auto it = m_mapPlcAssocDevice.find(deviceID);
            if (it == m_mapPlcAssocDevice.end())
            {
                continue;
            }
            const auto& listAssocDeviceID = it->second;
            MapStringMap assocDeviceState;
            ErrorInfo e;
            if (!ZGProxyCommon::mgetPropertyValuesEx(listAssocDeviceID, {"CommState"}, assocDeviceState, e))
            {
                ZGLOG_ERROR(e);
                continue;
            }
            MapStringMap changedDeviceState;
            if (state == "1") // 设备中断，关联设备也中断
            {
                for (const auto& assocDeviceID : listAssocDeviceID)
                {
                    if (assocDeviceState.find(assocDeviceID) == assocDeviceState.end())
                    {
                        continue;
                    }
                    if (assocDeviceState[assocDeviceID]["CommState"] != state)
                    {
                        changedDeviceState[assocDeviceID]["CommState"] = state;
                    }
                }
            }
            else if (state == "2")
            {
                MapStringMap deviceInnerState;
                if (!ZGProxyCommon::mgetPropertyValuesEx(listAssocDeviceID, {"InnerCommState"}, deviceInnerState, e))
                {
                    ZGLOG_ERROR(e);
                    continue;
                }
                // 与assocDeviceState进行比较，如果不一致则放入changedDeviceState中
                for (auto& [assocID, assocState] : assocDeviceState)
                {
                    auto pair = deviceInnerState.find(assocID);
                    if (pair == deviceInnerState.end())
                    {
                        if (assocDeviceState[assocID]["CommState"] != state)
                        {
                            changedDeviceState[assocID]["CommState"] = state;
                        }
                    }
                    else
                    {
                        auto innerState = pair->second;
                        // 外部通信状态是内部通信状态取反的结果，因此当内部状态为1外部状态为2或内部状态为2外部状态为1时，表示通信状态一致
                        if ((assocState["CommState"] == "2" && innerState["InnerCommState"] == "1") ||
                            (assocState["CommState"] == "1" && innerState["InnerCommState"] == "2"))
                            continue;
                        if (innerState["InnerCommState"] == "1")
                        {
                            changedDeviceState[assocID]["CommState"] = "2";
                        }
                        else
                        {
                            changedDeviceState[assocID]["CommState"] = "1";
                        }
                    }
                }
            }
            if (!changedDeviceState.empty())
            {
                if (!ZGProxyCommon::mupdatePropertyValues(changedDeviceState, e))
                {
                    ZGLOG_ERROR(e);
                }
            }
        }
    }

    void ZGOPTaskOutageMng::updateInterlockState()
    {
        QString sql = QString(
            "SELECT a.id, a.taskID, a.deviceID, a.dstState, c.subtypeID, a.rtIsUnlockEnable, a.rtIsAllowClose FROM op_param_outage_task_device a "
            "LEFT JOIN op_param_outage_task b ON a.taskID = b.id "
            "LEFT JOIN mp_param_device c ON a.deviceID = c.id "
            "WHERE b.rtOutageStageID IN ('ZG_OS_GROUND', 'ZG_OS_TEST') ORDER BY a.deviceID");
        ListStringMap listTaskDevice;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTaskDevice))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务设备失败"));
            return;
        }
        // std::map<std::string, MapStringMap> mapTaskDevices;
        // ListStringMap listOverZoneDevice;
        // for (auto& taskDevice : listTaskDevice)
        // {
        //     const auto& taskID = ZGUtils::get(taskDevice, "taskID");
        //     const auto& deviceID = ZGUtils::get(taskDevice, "deviceID");
        //     const auto& dstState = ZGUtils::get(taskDevice, "dstState");
        //     const auto& subtypeID = ZGUtils::get(taskDevice, "subtypeID");
        //     if (subtypeID == "ZG_DS_DISCONNECTOR_OVERZONE")
        //         listOverZoneDevice.push_back(taskDevice);
        //     const auto& isAllowClose = ZGUtils::get(taskDevice, "rtIsAllowClose");
        //     mapTaskDevices[taskID][deviceID]["subtypeID"] = subtypeID;
        //     mapTaskDevices[taskID][deviceID]["dstState"] = dstState;
        //     mapTaskDevices[taskID][deviceID]["rtIsAllowClose"] = isAllowClose;
        // }
        // refreshOverZoneDeviceState(listOverZoneDevice);
        // for (const auto& [taskID, devices] : mapTaskDevices)
        // {
        //     checkDevicesInterlock(taskID, devices);
        // }
        // 对于每个接地开关，从m_mapGroundDeviceAssocOverZoneDevice中获取其所关联的分段隔离开关
        StringList listSql;
        for (const auto& taskDevice : listTaskDevice)
        {
            const auto& subtypeID = ZGUtils::get(taskDevice, "subtypeID");
            if (subtypeID != "ZG_DS_GROUND_SWITCH")
            {
                continue;
            }
            const auto& recordID = ZGUtils::get(taskDevice, "id");
            const auto& taskID = ZGUtils::get(taskDevice, "taskID");
            const auto& deviceID = ZGUtils::get(taskDevice, "deviceID");
            const auto& isUnlockEnable = ZGUtils::get(taskDevice, "rtIsUnlockEnable");
            auto it = m_mapGroundDeviceAssocOverZoneDevice.find(deviceID);
            if (it == m_mapGroundDeviceAssocOverZoneDevice.end())
            {
                continue;
            }
            const auto& overZoneDeviceID = it->second;
            // 在该接地开关所属任务中查找是否存在该分段开关，如果不存在则跳过
            bool found = false;
            for (const auto& taskDevice : listTaskDevice)
            {
                const auto& iterDeviceID = ZGUtils::get(taskDevice, "deviceID");
                const auto& iterTaskID = ZGUtils::get(taskDevice, "taskID");
                if (iterDeviceID == overZoneDeviceID && iterTaskID == taskID)
                {
                    found = true;
                    break;
                }
            }
            if (!found)
            {
                continue;
            }
            // 获取分段隔离开关的位置状态
            std::string state;
            ErrorInfo e;
            if (!ZGProxyCommon::getPropertyValue(overZoneDeviceID, "Pos", state, e))
            {
                ZGLOG_ERROR(e);
                continue;
            }
            StringMap deviceParam;
            deviceParam["id"] = recordID;
            if ((state == "2") && (isUnlockEnable != "1"))
            {
                deviceParam["rtIsUnlockEnable"] = "1";
            }
            if (state != "2" && (isUnlockEnable == "1"))
            {
                deviceParam["rtIsUnlockEnable"] = "0";
            }
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceParam));
        }
        if (!listSql.empty())
        {
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                ZGLOG_ERROR(QStringLiteral("更新接地开关解锁状态失败"));
            }
        }
    }

    void ZGOPTaskOutageMng::calculateDeviceWarning()
    {
        std::map<std::string, ListStringMap> mapTaskDevices;
        QString sql =
            "SELECT a.deviceID AS id, a.taskID, b.typeID, a.dstState, a.rtIsWarning, c.rtOutageStageID FROM op_param_outage_task_device a "
            "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
            "LEFT JOIN op_param_outage_task c ON a.taskID = c.id "
            "WHERE c.rtOutageStageID IN ('ZG_OS_PTW', 'ZG_OS_TEST')";
        ListStringMap listTaskDevice;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTaskDevice))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务设备失败"));
            return;
        }
        for (auto& taskDevice : listTaskDevice)
        {
            auto taskID = taskDevice["taskID"];
            mapTaskDevices[taskID].push_back(std::move(taskDevice));
        }
        // 对于每个处于工作许可或测试许可中的任务，首先获取其在任务中的接地开关与隔离开关设备列表
        StringList listSql;
        for (const auto& [taskID, listTaskDevice] : mapTaskDevices)
        {
            // 获取op_param_task中的rtTaskStateID
            std::string rtTaskStateID;
            sql = QString("SELECT rtTaskStateID FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
            if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtTaskStateID))
            {
                ZGLOG_ERROR(QStringLiteral("获取任务状态失败"));
                continue;
            }
            std::string taskDeviceError = "0";
            for (const auto& taskDevice : listTaskDevice)
            {
                try
                {
                    const auto& deviceID = ZGUtils::get(taskDevice, "id");
                    const auto& typeID = ZGUtils::get(taskDevice, "typeID");
                    const auto& dstState = ZGUtils::get(taskDevice, "dstState");
                    const auto& isWarning = ZGUtils::get(taskDevice, "rtIsWarning");
                    const auto& outageStageID = ZGUtils::get(taskDevice, "rtOutageStageID");
                    std::string updateWarning = "0";
                    do
                    {
                        if (typeID == "ZG_DT_GROUND_SWITCH")
                        {
                            // 获取接地开关的位置状态、通信状态、锁定状态
                            StringMap deviceState;
                            ErrorInfo e;
                            if (!ZGProxyCommon::getPropertyValues(deviceID,
                                {"PosClose", "PosOpen", "CommState", "LockState", "Elec", "OutInterlockOff"},
                                deviceState, e))
                            {
                                ZGLOG_ERROR(e);
                                break;
                            }
                            const auto& posClose = ZGUtils::get(deviceState, "PosClose");
                            const auto& posOpen = ZGUtils::get(deviceState, "PosOpen");
                            const auto& commState = ZGUtils::get(deviceState, "CommState");
                            const auto& lockState = ZGUtils::get(deviceState, "LockState");
                            const auto& elec = ZGUtils::get(deviceState, "Elec");
                            const auto& outInterlockOff = ZGUtils::get(deviceState, "OutInterlockOff");
                            if (outageStageID == "ZG_OS_PTW")
                            {
                                if ((lockState != "2") || (outInterlockOff == "2") || (posClose != dstState) || (posOpen
                                    == dstState))
                                {
                                    updateWarning = "1";
                                    taskDeviceError = "1";
                                    break;
                                }
                            }
                            if ((commState != "2") || (elec == "2"))
                            {
                                updateWarning = "1";
                                taskDeviceError = "1";
                                break;
                            }
                            // 从m_mapDeviceAssocLockDevice获取接地开关关联的闭锁装置
                            auto it = m_mapDeviceAssocLockDevice.find(deviceID);
                            if (it != m_mapDeviceAssocLockDevice.end())
                            {
                                const auto& lockDeviceID = it->second;
                                // 获取锁定设备的投退状态、通信状态
                                StringMap lockDeviceState;
                                if (!ZGProxyCommon::getPropertyValues(lockDeviceID, {"InOutState", "CommState"},
                                    lockDeviceState, e))
                                {
                                    ZGLOG_ERROR(e);
                                    break;
                                }
                                const auto& inOutState = ZGUtils::get(lockDeviceState, "InOutState");
                                const auto& lockCommState = ZGUtils::get(lockDeviceState, "CommState");
                                if ((inOutState != "2") || (lockCommState != "2"))
                                {
                                    updateWarning = "1";
                                    taskDeviceError = "1";
                                    break;
                                }
                            }
                            it = m_mapDeviceAssocIndiDevice.find(deviceID);
                            if (it != m_mapDeviceAssocIndiDevice.end())
                            {
                                const auto& indiDeviceID = it->second;
                                StringMap indiDeviceState;
                                if (!ZGProxyCommon::getPropertyValues(indiDeviceID, {"CommState"}, indiDeviceState, e))
                                {
                                    ZGLOG_ERROR(e);
                                    break;
                                }
                                const auto& indiCommState = ZGUtils::get(indiDeviceState, "CommState");
                                if (indiCommState != "2")
                                {
                                    updateWarning = "1";
                                    taskDeviceError = "1";
                                    break;
                                }
                            }
                            it = m_mapGroundDeviceAssocContinuityDevice.find(deviceID);
                            if (it != m_mapGroundDeviceAssocContinuityDevice.end())
                            {
                                const auto& continuityDeviceID = it->second;
                                StringMap continuityDeviceState;
                                if (!ZGProxyCommon::getPropertyValues(continuityDeviceID,
                                    {
                                        "OHLTestInOutState", "RTTestInOutState",
                                        "CommState", "OHLTestingState", "RTTestingState"
                                    }, continuityDeviceState, e))
                                {
                                    ZGLOG_ERROR(e);
                                    break;
                                }
                                const auto& ohlTestInOutState =
                                    ZGUtils::get(continuityDeviceState, "OHLTestInOutState");
                                const auto& rtTestInOutState = ZGUtils::get(continuityDeviceState, "RTTestInOutState");
                                const auto& continuityCommState = ZGUtils::get(continuityDeviceState, "CommState");
                                const auto& ohlTestingState = ZGUtils::get(continuityDeviceState, "OHLTestingState");
                                const auto& rtTestingState = ZGUtils::get(continuityDeviceState, "RTTestingState");
                                // 如果接触网连续性投退状态为投入，则检查连续性模块的通信状态和接触网测试状态是否为故障或异常
                                if (ohlTestInOutState == "2")
                                {
                                    if (continuityCommState != "2" || ohlTestingState == "3" || ohlTestingState == "4")
                                    {
                                        updateWarning = "1";
                                        taskDeviceError = "1";
                                        break;
                                    }
                                }
                                // 如果回流轨连续性投退状态为投入，则检查连续性模块的通信状态和回流轨测试状态是否为故障或异常
                                if (rtTestInOutState == "2")
                                {
                                    if (continuityCommState != "2" || rtTestingState == "3" || rtTestingState == "4")
                                    {
                                        updateWarning = "1";
                                        taskDeviceError = "1";
                                        break;
                                    }
                                }
                            }
                        }
                        if (typeID == "ZG_DT_DISCONNECTOR")
                        {
                            // 获取隔离开关的位置状态、锁定状态
                            StringMap deviceState;
                            ErrorInfo e;
                            if (!ZGProxyCommon::getPropertyValues(deviceID, {"Pos", "LockState"}, deviceState, e))
                            {
                                ZGLOG_ERROR(e);
                                break;
                            }
                            const auto& pos = ZGUtils::get(deviceState, "Pos");
                            const auto& lockState = ZGUtils::get(deviceState, "LockState");
                            if ((lockState != "2") || (pos != dstState))
                            {
                                updateWarning = "1";
                                taskDeviceError = "1";
                                break;
                            }
                            // 获取隔离开关关联的闭锁装置
                            auto it = m_mapDeviceAssocLockDevice.find(deviceID);
                            if (it != m_mapDeviceAssocLockDevice.end())
                            {
                                const auto& lockDeviceID = it->second;
                                // 获取锁定设备的投退状态、通信状态
                                StringMap lockDeviceState;
                                if (!ZGProxyCommon::getPropertyValues(lockDeviceID, {"InOutState", "CommState"},
                                    lockDeviceState, e))
                                {
                                    ZGLOG_ERROR(e);
                                    break;
                                }
                                const auto& inOutState = ZGUtils::get(lockDeviceState, "InOutState");
                                const auto& lockCommState = ZGUtils::get(lockDeviceState, "CommState");
                                if ((inOutState != "2") || (lockCommState != "2"))
                                {
                                    updateWarning = "1";
                                    taskDeviceError = "1";
                                    break;
                                }
                            }
                            it = m_mapDeviceAssocIndiDevice.find(deviceID);
                            if (it != m_mapDeviceAssocIndiDevice.end())
                            {
                                const auto& indiDeviceID = it->second;
                                StringMap indiDeviceState;
                                if (!ZGProxyCommon::getPropertyValues(indiDeviceID, {"CommState"}, indiDeviceState, e))
                                {
                                    ZGLOG_ERROR(e);
                                    break;
                                }
                                const auto& indiCommState = ZGUtils::get(indiDeviceState, "CommState");
                                if (indiCommState != "2")
                                {
                                    updateWarning = "1";
                                    taskDeviceError = "1";
                                    break;
                                }
                            }
                        }
                    }
                    while (0);
                    std::string condition = "taskID = '" + taskID + "' AND deviceID = '" + deviceID + "'";
                    StringMap deviceWarn{{"rtIsWarning", updateWarning}};
                    // 如果设备的告警状态与当前状态不一致，则向AP发送告警短信
                    if (isWarning != updateWarning)
                    {
                        if (isWarning == "0" && updateWarning == "1")
                        {
                            ZGLOG_INFO(
                                QString("Sending SMS warn message for device %1 in task %2").arg(deviceID.c_str()).arg(
                                    taskID.c_str()));
                            sendSMSWarnMessage(taskID, deviceID);
                            StringMap taskState{{"id", taskID}, {"rtTaskStateID", "ZG_TS_ERROR"}};
                            listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskState));
                        }
                        const auto& strSql = ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceWarn,
                            condition);
                        ZGLOG_INFO(strSql.c_str());
                        listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task_device", deviceWarn,
                            condition));
                    }
                }
                catch (const std::exception& e)
                {
                    ZGLOG_ERROR(e.what());
                }
            }
            // 如果任务中设备的异常状态与当前任务状态不一致，则更新任务状态
            if (rtTaskStateID == "ZG_TS_EXECUTING" && taskDeviceError == "1")
            {
                StringMap taskState{{"id", taskID}, {"rtTaskStateID", "ZG_TS_ERROR"}};
                listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskState));
            }
            if (rtTaskStateID == "ZG_TS_ERROR" && taskDeviceError == "0")
            {
                StringMap taskState{{"id", taskID}, {"rtTaskStateID", "ZG_TS_EXECUTING"}};
                listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskState));
            }
        }
        if (!listSql.empty())
        {
            if (!ZGProxyCommon::execBatchSql(listSql))
                ZGLOG_ERROR(QStringLiteral("更新设备告警状态失败"));
        }
    }

    bool ZGOPTaskOutageMng::createTypicalTask(StringMap head,
                                              ListStringMap devices,
                                              ListStringMap users,
                                              std::string& taskID,
                                              ErrorInfo& e)
    {
        auto it = head.find("id");
        if (it == head.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务ID为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        taskID = it->second;
        if (users.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务用户为空").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!checkSameUser(head, users, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            StringList listSql;
            listSql.push_back(QString("DELETE FROM op_param_task WHERE id = '%1'").arg(taskID.c_str()).toStdString());
            listSql.push_back(
                QString("DELETE FROM op_param_outage_task WHERE id = '%1'").arg(taskID.c_str()).toStdString());
            listSql.push_back(
                QString("DELETE FROM op_param_outage_task_device WHERE taskID = '%1'").arg(taskID.c_str()).
                toStdString());
            if (!generateHeadParam(listSql, head, taskID))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("生成任务头参数出错").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (!generateDevicesParam(listSql, taskID, devices))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("创建任务设备参数出错").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            if (!generateUsersParam(listSql, taskID, users))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("创建任务用户参数出错").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            ZGLOG_INFO(
                QString("createTypicalTask: taskID=%1, listSql size=%2").arg(taskID.c_str()).arg(listSql.size()));
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
                e.errDetail = QStringLiteral("创建任务失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPTaskOutageMng::createOtherTask(StringMap head,
                                            ListStringMap devices,
                                            ListStringMap users,
                                            std::string& taskID,
                                            ErrorInfo& e)
    {
        QString errMsg;
        if (!ZGUtils::checkRequiredParam(head, {"outageTaskTypeID", "name"}, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = errMsg.toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 验证设备参数
        if (!checkDevicesParam(devices, e))
        {
            return false;
        }
        // 验证用户参数
        if (!checkUsersParam(users, e))
        {
            return false;
        }
        // 验证AP与CP是否为同一用户
        if (!checkSameUser(head, users, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        StringList listSql;
        if (!generateHeadParam(listSql, head, taskID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("创建任务头参数出错").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 调用getDevicesBoundaryType方法，验证设备是否为有效的连续供电区域
        bool isValidRegion;
        ListStringMap listOutputDevice;
        StringMap inputDevice;
        try
        {
            for (const auto& device : devices)
            {
                const auto& deviceID = ZGUtils::get(device, "id");
                const auto& subtypeID = ZGUtils::get(device, "subtypeID");
                inputDevice[deviceID] = subtypeID;
            }
            if (!getDevicesBoundaryType(inputDevice, isValidRegion, listOutputDevice, e, Ice::Current()))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            if (!isValidRegion)
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("不是有效的连续供电区域").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            StringMap outputDevice;
            for (const auto& device : listOutputDevice)
            {
                const auto& deviceID = ZGUtils::get(device, "id");
                const auto& isBoundarySwitch = ZGUtils::get(device, "IsBoundarySwitch");
                outputDevice[deviceID] = isBoundarySwitch;
            }
            // 将分段隔离开关的IsBoundarySwitch状态加入到devices中
            for (auto& device : devices)
            {
                const auto& deviceID = ZGUtils::get(device, "id");
                const auto& subtypeID = ZGUtils::get(device, "subtypeID");
                if (subtypeID != "ZG_DS_DISCONNECTOR_OVERZONE")
                    continue;
                if (outputDevice.find(deviceID) == outputDevice.end())
                    continue;
                device["rtIsBoundarySwitch"] = outputDevice[deviceID];
            }
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!generateDevicesParam(listSql, taskID, devices))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("创建任务设备参数出错").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!generateUsersParam(listSql, taskID, users))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("创建任务用户参数出错").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::checkDeviceDstState(const std::string& taskTypeID,
                                                const std::string& deviceID,
                                                const std::string& deviceTypeID,
                                                const std::string& state,
                                                std::string& errMsg)
    {
        // 接地开关目标状态与预期状态不一致
        if (deviceTypeID == "ZG_DT_GROUND_SWITCH")
        {
            std::string expectState = "2";
            if (taskTypeID == "ZG_OT_TEST")
            {
                expectState = "1";
            }
            if (state != expectState)
            {
                std::string deviceName;
                ZGProxyCommon::getDataByField("mp_param_device", deviceID, "name", deviceName);
                errMsg = QStringLiteral("接地开关【%1】目标状态与预期目标状态不一致").arg(deviceName.c_str()).toStdString();
                return false;
            }
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getListDeviceFromTask(const std::string& taskID,
                                                  StringList& listDeviceID,
                                                  std::string& errMsg)
    {
        QString sql = QString("SELECT deviceID FROM op_param_outage_task_device WHERE taskID = '%1'").arg(
            taskID.c_str());
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
        {
            errMsg = QStringLiteral("获取任务设备失败").toStdString();
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::getListAppNodeFromDevices(const StringList& listDeviceID,
                                                      StringList& listAppNodeID,
                                                      std::string& errMsg)
    {
        const auto& deviceIds = ZGUtils::join(listDeviceID, ",", "'", "'");
        QString sql = QString("SELECT DISTINCT appNodeID FROM mp_param_device WHERE id IN (%1)").arg(deviceIds.c_str());
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listAppNodeID))
        {
            errMsg = QStringLiteral("获取设备所属应用节点失败").toStdString();
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::generateHeadParam(StringList& listSql,
                                              const StringMap& headParam,
                                              std::string& newTaskID)
    {
        std::string uuid;
        if (!ZGProxyCommon::createUUID(uuid))
        {
            ZGLOG_ERROR(QStringLiteral("生成任务ID失败"));
            return false;
        }
        try
        {
            StringMap outageParam;
            StringMap taskParam;
            taskParam["id"] = uuid;
            taskParam["name"] = ZGUtils::get(headParam, "name");
            taskParam["taskTypeID"] = "ZG_TT_OUTAGE";
            taskParam["rtCreateUserID"] = ZGUtils::get(headParam, "rtCreateUserID");
            taskParam["rtCreateTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString();
            taskParam["rtStartTime"] = ZGUtils::get(headParam, "rtStartTime");
            taskParam["rtEndTime"] = ZGUtils::get(headParam, "rtEndTime");
            taskParam["rtExecStartTime"] = "";
            taskParam["rtExecEndTime"] = "";
            taskParam["rtTaskStageID"] = "ZG_TS_CREATE";
            taskParam["rtTaskStateID"] = "ZG_TS_FINISHED";
            outageParam["id"] = uuid;
            outageParam["outageTaskTypeID"] = ZGUtils::get(headParam, "outageTaskTypeID");
            outageParam["outageTypeID"] = ZGUtils::get(headParam, "outageTypeID");
            outageParam["rtModifyTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString();
            listSql.push_back(ZGUtils::generateInsertSql("op_param_task", taskParam));
            listSql.push_back(ZGUtils::generateInsertSql("op_param_outage_task", outageParam));
            newTaskID = uuid;
            return true;
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
            return false;
        }
    }

    bool ZGOPTaskOutageMng::generateDevicesParam(StringList& listSql,
                                                 const std::string& newTaskID,
                                                 const ListStringMap& listDeviceParam)
    {
        try
        {
            StringList listUuid;
            // 为任务设备表生成唯一ID
            if (!ZGProxyCommon::createUUID(static_cast<int>(listDeviceParam.size()), listUuid))
            {
                ZGLOG_ERROR(QStringLiteral("生成任务设备ID失败"));
                return false;
            }
            // 对于传入的设备参数，生成对应的插入SQL语句
            for (size_t i = 0; i < listDeviceParam.size(); ++i)
            {
                const auto& device = listDeviceParam[i]; // 从传入参数中获取设备信息
                StringMap deviceParam;
                deviceParam["id"] = listUuid[i]; // 复制记录ID
                deviceParam["taskID"] = newTaskID; // 复制任务ID
                deviceParam["deviceID"] = ZGUtils::get(device, "deviceID"); // 复制设备ID
                deviceParam["dstState"] = ZGUtils::get(device, "dstState"); // 复制设备目标状态
                deviceParam["rtDeviceStateID"] = "ZG_DOD_NORMAL";
                auto pair = device.find("isBoundarySwitch");
                if (pair != device.end())
                {
                    deviceParam["rtIsBoundarySwitch"] = pair->second;
                }
                listSql.push_back(ZGUtils::generateInsertSql("op_param_outage_task_device", deviceParam)); // 生成插入语句
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
            return false;
        }
        return true;
    }

    bool ZGOPTaskOutageMng::generateUsersParam(StringList& listSql,
                                               const std::string& taskID,
                                               const ListStringMap& listUserParam)
    {
        StringList listUuid;
        if (!ZGProxyCommon::createUUID(static_cast<int>(listUserParam.size()), listUuid))
        {
            ZGLOG_ERROR(QStringLiteral("生成任务用户ID失败"));
            return false;
        }
        // 从op_param_outage_system表中获取魔数，用于生成文件安全验证码
        std::string magicNumber;
        QString sql = "SELECT magicNumber FROM op_param_outage_system";
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), magicNumber))
        {
            ZGLOG_ERROR(QStringLiteral("获取魔数失败"));
            return false;
        }
        if (magicNumber.empty())
        {
            magicNumber = "12,1,9,24,0,27,25,31,29,15,18,28,14,13,23,16,26,3,20,17,8,19,22,10,30,7,6,11,5,4,2,21";
        }
        StringList listMagicNumber;
        if (!ZGUtils::splitString(magicNumber, ",", listMagicNumber))
        {
            ZGLOG_ERROR(QStringLiteral("无效的魔数'%1'").arg(magicNumber.c_str()));
            return false;
        }
        std::vector<int> reorder;
        for (const auto& orderIndex : listMagicNumber)
        {
            reorder.push_back(std::atoi(orderIndex.c_str()));
        }
        try
        {
            for (size_t i = 0; i < listUserParam.size(); ++i)
            {
                const auto& user = listUserParam[i];
                StringMap userParam;
                userParam["id"] = listUuid[i];
                userParam["taskID"] = taskID;
                // 如果是系统中的用户，记录用户ID和该用户的手机号
                if (user.find("userID") != user.end())
                {
                    userParam["userID"] = ZGUtils::get(user, "userID");
                }
                else
                {
                    userParam["userID"] = "";
                }
                if (user.find("rtOTP") != user.end())
                {
                    userParam["rtOTP"] = ZGUtils::get(user, "rtOTP");
                }
                else
                {
                    userParam["rtOTP"] = "";
                }
                userParam["userName"] = ZGUtils::get(user, "userName");
                userParam["mobileNumber"] = ZGUtils::get(user, "mobileNumber");
                userParam["rtSafetyFileCode"] = ZGUtils::get(user, "rtSafetyFileCode");
                ZG6000::ErrorInfo e;
                if (!generateSafetyFileVerifyCode(userParam["rtSafetyFileCode"], reorder,
                    userParam["rtSafetyFileVerifyCode"], e))
                {
                    ZGLOG_ERROR(e);
                    return false;
                }
                userParam["sendFlag"] = "0";
                sql = ZGUtils::generateInsertSql("op_param_outage_task_user", userParam).c_str();
                listSql.push_back(ZGUtils::generateInsertSql("op_param_outage_task_user", userParam));
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
            return false;
        }
        return true;
    }

    void ZGOPTaskOutageMng::generateSwitchOutageSql(StringList& listSql,
                                                    const std::string& taskID,
                                                    const std::string& stageID)
    {
        StringMap taskParam;
        taskParam["id"] = taskID;
        taskParam["rtTaskStateID"] = "ZG_TS_EXECUTING";
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskParam));
        StringMap outageParam;
        outageParam["id"] = taskID;
        outageParam["rtOutageStageID"] = stageID;
        // 进入工作许可或测试许可阶段时，将任务状态设置为锁定
        if (stageID == "ZG_OS_PTW")
        {
            outageParam["rtOutageStateID"] = "ZG_OS_LOCK";
        }
        else
        {
            outageParam["rtOutageStateID"] = "ZG_OS_NORMAL";
        }
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task", outageParam));
        StringMap taskDevice;
        taskDevice["rtIsWarning"] = "0";
        const auto& condition = "taskID = '" + taskID + "'";
        auto sql = ZGUtils::generateUpdateSql("op_param_outage_task_device", taskDevice, condition);
        listSql.push_back(sql);
        StringMap taskUser;
        // 申请测试许可时需要清除用户的OTP
        if (stageID == "ZG_OS_REQUEST_SFT")
        {
            taskUser["rtOTP"] = "";
            taskUser["rtSavedOTP"] = "";
            sql = ZGUtils::generateUpdateSql("op_param_outage_task_user", taskUser, condition);
            listSql.push_back(sql);
        }
    }

    bool ZGOPTaskOutageMng::generateUpdateDeviceSubtypeStateSql(StringList& listSql,
                                                                const std::string& taskID,
                                                                const std::string& deviceSubtypeID,
                                                                const std::string& state,
                                                                ErrorInfo& e)
    {
        // 获取任务设备表中所有子类型为指定值的记录
        QString sql = QString("SELECT a.id, a.deviceID FROM op_param_outage_task_device a "
                          "LEFT JOIN mp_param_device b ON a.deviceID = b.id WHERE a.taskID = '%1' AND b.subtypeID = '%2' ORDER BY a.id")
                      .arg(taskID.c_str()).arg(deviceSubtypeID.c_str());
        ListStringMap listDevice;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevice))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskOutage::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取设备状态失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (auto& device : listDevice)
        {
            device["dstState"] = state;
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_outage_task_device", device));
        }
        return true;
    }

    void ZGOPTaskOutageMng::onTimer()
    {
        m_tickCount++;
        if (m_tickCount % 2 == 1)
        {
            auto future = QtConcurrent::run([this]()
            {
                // 更新锁定状态
                calculateLockState();
                // 更新可用性状态
                calculateDeviceState();
            });
        }
        if (m_tickCount % 2 == 0)
        {
            auto future = QtConcurrent::run([this]()
            {
                // 发送任务信息
                sendTaskInfoToDevices();
                // 更新设备通信状态
                updateDeviceCommState();
            });
            future = QtConcurrent::run([this]()
            {
                updateInterlockState();
            });
        }
        if (m_tickCount % 5 == 0)
        {
            auto future = QtConcurrent::run([this]()
            {
                // 计算设备告警信号
                calculateDeviceWarning();
                // 计算设备连续性初始状态
                recheckRebootContinueDevice();
                // 计算分区电压不一致
                checkZoneVoltage();
            });
        }
        if (m_tickCount % 10 == 0)
        {
            auto future = QtConcurrent::run([this]()
            {
                // 更新PLC任务数量和锁定数量
                updatePLCDeviceState();
            });
        }
        // 根据任务状态尝试锁定或解锁设备
        if (m_tickCount % 20 == 0)
        {
            auto future = QtConcurrent::run([this]()
            {
                lockUnlockedDevice();
                unlockLockedDevice();
                // 更新LED显示任务信息
                updateLEDTaskInfo();
            });
        }
    }

    void ZGOPTaskOutageMng::onReceivedMessage(QString topic,
                                              QString message)
    {
        if (!ZGRuntime::instance()->isMaster())
        {
            return;
        }
        if (topic == "ZG_T_SYSTEM_SMS")
        {
            StringMap sms;
            std::string errMsg;
            ZGLOG_INFO(message);
            if (!ZGJson::convertFromJson(message.toStdString(), sms, errMsg))
            {
                ZGLOG_ERROR(errMsg.c_str());
                return;
            }
            const auto& phoneNo = ZGUtils::get(sms, "phoneNo", "");
            const auto& result = ZGUtils::get(sms, "result", "");
            if (phoneNo.empty())
            {
                return;
            }
            // 根据手机号从任务用户表中查询记录ID
            QString sql = QString(
                    "SELECT a.id FROM op_param_outage_task_user a LEFT JOIN op_param_outage_task b ON a.taskID = b.id "
                    "WHERE a.mobileNumber = '%1' AND (b.rtOutageStageID = 'ZG_OS_PTW' OR b.rtOutageStageID = 'ZG_OS_REQUEST_SFT' "
                    "OR b.rtOutageStageID = 'ZG_OS_TEST')")
                .arg(phoneNo.c_str());
            StringList listID;
            if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
            {
                ZGLOG_ERROR(QStringLiteral("获取任务用户记录ID失败"));
                return;
            }
            if (listID.empty())
            {
                ZGLOG_ERROR(QStringLiteral("未找到任务用户记录"));
                return;
            }
            if (listID.size() > 1)
            {
                ZGLOG_WARN(QStringLiteral("手机号存在于多个不同任务中"));
            }
            std::string recordID = listID.front();
            // 设置用户短信发送标志
            StringMap taskUser{{"id", recordID}};
            if (result == "SMS_SEND_SUCESS")
            {
                taskUser["sendFlag"] = "1";
            }
            else if (result == "SMS_SEND_FAIL")
            {
                taskUser["sendFlag"] = "2";
            }
            else
            {
                taskUser["sendFlag"] = "0";
            }
            sql = ZGUtils::generateUpdateSql("op_param_outage_task_user", taskUser).c_str();
            ZGLOG_INFO(sql);
            if (!ZGProxyCommon::execSql(sql.toStdString()))
            {
                ZGLOG_ERROR(QStringLiteral("更新任务用户发送标志失败"));
            }
        }
        else
        {
            // 根据安全闭锁装置上送的密码发送开锁命令，打开接地装置柜门
            StringList listString;
            if (!ZGUtils::splitString(topic.toStdString(), "/", listString))
            {
                ZGLOG_ERROR(QStringLiteral("无效的主题'%1'").arg(topic));
                return;
            }
            const auto& lockDeviceID = listString[1];
            MapStringMap properties;
            std::string errMsg;
            if (!ZGJson::convertFromJson(message.toStdString(), properties, errMsg))
            {
                ZGLOG_ERROR(errMsg.c_str());
                return;
            }
            auto it = properties.find("Password");
            if (it == properties.end())
            {
                return;
            }
            const auto& password = it->second;
            auto itValue = password.find("rtNewValue");
            if (itValue == password.end())
            {
                return;
            }
            const auto& passwordValue = itValue->second;
            if (passwordValue.empty())
            {
                return;
            }
            ZGLOG_TRACE(QString("device %1 password = %2").arg(lockDeviceID.c_str()).arg(passwordValue.c_str()));
            auto pair = m_mapLockDeviceAssocDevice.find(lockDeviceID);
            if (pair == m_mapLockDeviceAssocDevice.end())
            {
                ZGLOG_ERROR(QString("Can't find lock device %1 associate device").arg(lockDeviceID.c_str()));
                return;
            }
            const auto& deviceID = pair->second;
            std::string lockState;
            ErrorInfo e;
            if (!ZGProxyCommon::getPropertyValue(deviceID, "LockState", lockState, e))
            {
                ZGLOG_ERROR(e);
                return;
            }
            // 如果设备已经闭锁，无法打开接地装置
            if (lockState == "2")
            {
                // auto eventProxy = ZGProxyMng::instance()->getProxySPEventProcess();
                // StringMap eventParam;
                // QString sql = QString("SELECT name, nameL2, appNodeID, subsystemID, majorID FROM mp_param_device WHERE id = '%1'")
                //                   .arg(deviceID.c_str());
                // StringMap deviceInfo;
                // if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), deviceInfo))
                // {
                //     ZGLOG_ERROR(QStringLiteral("获取设备信息失败"));
                //     return;
                // }
                // eventParam["appNodeID"] = deviceInfo["appNodeID"];
                // eventParam["subsystemID"] = deviceInfo["subsystemID"];
                // eventParam["majorID"] = deviceInfo["majorID"];
                // eventParam["eventTypeID"] = "ZG_ET_OPER";
                // eventParam["alarmLevelID"] = "ZG_AL_LEVEL4";
                // std::string eventInfo = deviceInfo["name"] + ZGUtils::languageString(m_firstLanguage, "isLocked");
                // std::string eventInfoL2 = deviceInfo["nameL2"] + ZGUtils::languageString(m_secondLanguage, "isLocked");
                // eventParam["eventInfo"] = eventInfo;
                // eventParam["eventInfoL2"] = eventInfoL2;
                // try
                // {
                //     eventProxy->processEvent(eventParam);
                // }
                // catch (const Ice::Exception& e)
                // {
                //     ZGLOG_ERROR(e.what());
                // }
                return;
            }
            // 从设备表中获取密码，并进行比对
            QString sql = QString("SELECT password FROM mp_param_device WHERE id = '%1'").arg(lockDeviceID.c_str());
            std::string devicePassword;
            if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), devicePassword))
            {
                ZGLOG_ERROR(QStringLiteral("获取设备密码失败"));
                return;
            }
            if (devicePassword != passwordValue)
            {
                return;
            }
            if (!sendCtrlCommand("", lockDeviceID, "CMD_Unlock", "2"))
            {
                ZGLOG_ERROR(QStringLiteral("向设备发送开锁命令失败"));
                return;
            }
        }
    }
} // namespace ZG6000

// Copyright (c) 2025 Company Name. All rights reserved.
// Author: <PERSON>
// Date: 2025-01-22
// Description: 断电任务接口类，委托断电任务管理类实现
// Version: 1.0

#include "ZGOPTaskOutageI.h"
#include "ZGOPTaskOutageMng.h"

namespace ZG6000
{
    ZGOPTaskOutageI::ZGOPTaskOutageI()
    {
        ZGOPTaskOutageMng::instance()->init();
    }

    bool ZGOPTaskOutageI::checkState(const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->checkState();
    }

    void ZGOPTaskOutageI::dispatchData(std::string tableName,
                                       std::string oper,
                                       std::string reason,
                                       std::string time,
                                       ListRecord listRecord,
                                       const Ice::Current& current)
    {
        ZGOPTaskOutageMng::instance()->dispatchData(std::move(tableName), std::move(oper), std::move(reason),
            std::move(time), std::move(listRecord), current);
    }

    bool ZGOPTaskOutageI::deleteTask(std::string taskID,
                                     StringMap param,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->deleteTask(std::move(taskID), std::move(param), e, current);
    }

    bool ZGOPTaskOutageI::getTaskList(StringMap param,
                                      ListStringMap& listTask,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->getTaskList(std::move(param), listTask, e, current);
    }

    bool ZGOPTaskOutageI::startTask(std::string taskID,
                                    StringMap param,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->startTask(std::move(taskID), std::move(param), e, current);
    }

    bool ZGOPTaskOutageI::pauseTask(std::string taskID,
                                    StringMap param,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
    {
        // 断电任务中不适用此接口
        return false;
    }

    bool ZGOPTaskOutageI::resumeTask(std::string taskID,
                                     StringMap param,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        // 断电任务中不适用此接口
        return false;
    }

    bool ZG6000::ZGOPTaskOutageI::retryTask(std::string taskID,
                                            StringMap param,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
    {
        // 断电任务中不适用此接口
        return false;
    }

    bool ZGOPTaskOutageI::abolishTask(std::string taskID,
                                      StringMap param,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->abolishTask(std::move(taskID), std::move(param), e, current);
    }

    bool ZGOPTaskOutageI::confirmTask(std::string taskID,
                                      StringMap param,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->confirmTask(std::move(taskID), std::move(param), e, current);
    }

    bool ZGOPTaskOutageI::cancelTask(std::string taskID,
                                     StringMap param,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->cancelTask(std::move(taskID), std::move(param), e, current);
    }

    bool ZGOPTaskOutageI::getTaskInfo(std::string taskID,
                                      StringMap& head,
                                      ListStringMap& devices,
                                      ListStringMap& users,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->getTaskInfo(std::move(taskID), head, devices, users, e, current);
    }

    bool ZGOPTaskOutageI::createTask(StringMap head,
                                     ListStringMap devices,
                                     ListStringMap users,
                                     std::string& taskID,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->createTask(head, devices, users, taskID, e, current);
    }

    bool ZGOPTaskOutageI::editTask(std::string taskID,
                                   StringMap head,
                                   ListStringMap devices,
                                   ListStringMap users,
                                   ErrorInfo& e,
                                   const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->editTask(std::move(taskID), std::move(head), std::move(devices),
            std::move(users), e, current);
    }

    bool ZGOPTaskOutageI::moveTask(std::string taskID,
                                   ListStringMap oldUsers,
                                   ListStringMap newUsers,
                                   ErrorInfo& e,
                                   const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->moveTask(std::move(taskID), std::move(oldUsers), std::move(newUsers), e,
            current);
    }

    bool ZGOPTaskOutageI::convertTask(std::string taskID,
                                      StringMap param,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->convertTask(std::move(taskID), param, e, current);
    }

    bool ZGOPTaskOutageI::sendSMS(std::string taskID,
                                  StringList listPhone,
                                  ErrorInfo& e,
                                  const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->sendOTP(std::move(taskID), listPhone, e, current);
    }

    bool ZGOPTaskOutageI::lockIsolator(std::string clientID,
                                       std::string taskID,
                                       std::string deviceID,
                                       std::string OTP,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->lockIsolator(std::move(clientID), std::move(taskID), std::move(deviceID),
            std::move(OTP), e, current);
    }

    bool ZGOPTaskOutageI::unlockIsolator(std::string clientID,
                                         std::string taskID,
                                         std::string deviceID,
                                         std::string OTP,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->unlockIsolator(std::move(clientID), std::move(taskID),
            std::move(deviceID), std::move(OTP), e, current);
    }

    bool ZGOPTaskOutageI::lockIsolatorBatch(std::string clientID,
                                            std::string taskID,
                                            StringMap deviceOTP,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->lockIsolatorBatch(std::move(clientID), std::move(taskID), deviceOTP, e,
            current);
    }

    bool ZGOPTaskOutageI::unlockIsolatorBatch(std::string clientID,
                                              std::string taskID,
                                              StringMap deviceOTP,
                                              ErrorInfo& e,
                                              const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->unlockIsolatorBatch(std::move(clientID), std::move(taskID), deviceOTP, e,
            current);
    }

    bool ZGOPTaskOutageI::lockSwitch(std::string clientID,
                                     std::string taskID,
                                     std::string deviceID,
                                     std::string OTP,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->lockSwitch(std::move(clientID), std::move(taskID), std::move(deviceID), e,
            current);
    }

    bool ZGOPTaskOutageI::unlockSwitch(std::string clientID,
                                       std::string taskID,
                                       std::string deviceID,
                                       std::string OTP,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->unlockSwitch(std::move(clientID), std::move(taskID), std::move(deviceID),
            e, current);
    }

    bool ZGOPTaskOutageI::lockSwitchBatch(std::string clientID,
                                          std::string taskID,
                                          StringMap deviceOTP,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->lockSwitchBatch(std::move(clientID), std::move(taskID), deviceOTP, e,
            current);
    }

    bool ZGOPTaskOutageI::unlockSwitchBatch(std::string clientID,
                                            std::string taskID,
                                            StringMap deviceOTP,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->unlockSwitchBatch(std::move(clientID), std::move(taskID), deviceOTP, e,
            current);
    }

    bool ZGOPTaskOutageI::closeSwitch(std::string clientID,
                                      std::string taskID,
                                      std::string deviceID,
                                      std::string OTP,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->closeSwitch(std::move(clientID), std::move(taskID), std::move(deviceID),
            std::move(OTP), e, current);
    }

    bool ZGOPTaskOutageI::openSwitch(std::string clientID,
                                     std::string taskID,
                                     std::string deviceID,
                                     std::string OTP,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->openSwitch(std::move(clientID), std::move(taskID), std::move(deviceID),
            std::move(OTP), e, current);
    }

    bool ZGOPTaskOutageI::closeSwitchBatch(std::string clientID,
                                           std::string taskID,
                                           StringMap deviceOTP,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->closeSwitchBatch(std::move(clientID), std::move(taskID), deviceOTP, e,
            current);
    }

    bool ZGOPTaskOutageI::openSwitchBatch(std::string clientID,
                                          std::string taskID,
                                          StringMap deviceOTP,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->openSwitchBatch(std::move(clientID), std::move(taskID), deviceOTP, e,
            current);
    }

    bool ZGOPTaskOutageI::lockTask(std::string taskID,
                                   StringMap deviceOTP,
                                   ErrorInfo& e,
                                   const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->lockTask(std::move(taskID), deviceOTP, e, current);
    }

    bool ZGOPTaskOutageI::unlockTask(std::string taskID,
                                     StringMap deviceOTP,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->unlockTask(std::move(taskID), deviceOTP, e, current);
    }

    bool ZGOPTaskOutageI::getMonitorDevices(std::string taskID,
                                            ListStringMap& listDevices,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->getMonitorDevices(std::move(taskID), listDevices, e, current);
    }

    bool ZGOPTaskOutageI::applyPTW(std::string taskID,
                                   ErrorInfo& e,
                                   const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->applyPTW(std::move(taskID), e, current);
    }

    bool ZGOPTaskOutageI::cancelPTW(std::string taskID,
                                    StringMap OTP,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->cancelPTW(std::move(taskID), OTP, e, current);
    }

    bool ZGOPTaskOutageI::applySFT(std::string taskID,
                                   ErrorInfo& e,
                                   const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->applySFT(std::move(taskID), e, current);
    }

    bool ZGOPTaskOutageI::cancelSFT(std::string taskID,
                                    StringMap OTP,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->cancelSFT(std::move(taskID), OTP, e, current);
    }

    bool ZGOPTaskOutageI::saveOTP(std::string taskID,
                                  StringMap OTP,
                                  ErrorInfo& e,
                                  const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->saveOTP(std::move(taskID), OTP, e, current);
    }

    bool ZGOPTaskOutageI::changePhone(std::string taskID,
                                      std::string oldPhoneNumber,
                                      std::string newPhoneNumber,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->changePhone(std::move(taskID), std::move(oldPhoneNumber),
            std::move(newPhoneNumber), e, current);
    }

    bool ZGOPTaskOutageI::confirmOutage(std::string taskID,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->confirmOutage(std::move(taskID), e, current);
    }

    bool ZGOPTaskOutageI::saveEvent(std::string taskID,
                                    std::string deviceID,
                                    std::string event,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->saveEvent(std::move(taskID), std::move(deviceID), std::move(event), "", e,
            current);
    }

    bool ZGOPTaskOutageI::getTaskUsers(std::string taskID,
                                       ListStringMap& users,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
    {
        return false;
    }

    bool ZGOPTaskOutageI::setLockDevicePassword(StringMap params,
                                                ErrorInfo& e,
                                                const Ice::Current& current)
    {
        return false;
    }

    bool ZGOPTaskOutageI::unlockExternalLock(std::string clientID,
                                             std::string taskID,
                                             std::string deviceID,
                                             ErrorInfo& e,
                                             const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->unlockExternalLock(std::move(clientID), std::move(taskID),
            std::move(deviceID), e, current);
    }

    bool ZGOPTaskOutageI::isExternalLockEnable(std::string taskID,
                                               StringMap& deviceEnable,
                                               ErrorInfo& e,
                                               const Ice::Current& current)
    {
        return false;
    }

    bool ZGOPTaskOutageI::getSwitchCloseConditions(std::string taskID,
                                                   std::string deviceID,
                                                   ListStringMap& conditions,
                                                   ErrorInfo& e,
                                                   const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->getSwitchCloseConditions(std::move(taskID), std::move(deviceID),
            conditions, e, current);
    }

    bool ZGOPTaskOutageI::checkSwitchCloseConditions(std::string taskID,
                                                     std::string deviceID,
                                                     bool& success,
                                                     ErrorInfo& e,
                                                     const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->checkSwitchCloseConditions(std::move(taskID), std::move(deviceID),
            success, e, current);
    }

    bool ZGOPTaskOutageI::deleteTypicalTask(std::string taskID,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->deleteTypicalTask(std::move(taskID), e, current);
    }

    bool ZGOPTaskOutageI::getDevicesBoundaryType(StringMap inputDevice,
                                                 bool& isValidRegion,
                                                 ListStringMap& listOutputDevice,
                                                 ErrorInfo& e,
                                                 const Ice::Current& current)
    {
        return ZGOPTaskOutageMng::instance()->getDevicesBoundaryType(inputDevice, isValidRegion, listOutputDevice,
            e, current);
    }
} // namespace ZG6000

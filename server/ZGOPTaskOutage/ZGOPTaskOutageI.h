#ifndef ZG6000_ZGOPTASKOUTAGEI_H
#define ZG6000_ZGOPTASKOUTAGEI_H

#include <ZGOPTaskOutage.h>

namespace ZG6000
{
    class ZGOPTaskOutageI : public ZG6000::ZGOPTaskOutage
    {
    public:
        ZGOPTaskOutageI();

        // ZGServerBase interface
    public:
        /** 
         * @brief   检查服务状态
         *
         * @param current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool checkState(const Ice::Current& current) override;
        void dispatchData(std::string tableName,
                          std::string oper,
                          std::string reason,
                          std::string time,
                          ListRecord listRecord,
                          const Ice::Current& current) override;

        // ZGOPTaskBase interface
    public:
        /** 
         * @brief   删除任务
         *
         * @param           taskID  任务ID
         * @param           param   参数
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool deleteTask(std::string taskID,
                        StringMap param,
                        ErrorInfo& e,
                        const Ice::Current& current) override;

        /** 
         * @brief   获取任务列表
         *
         * @param           param     参数
         * @param [in,out]  listTask  任务列表
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool getTaskList(StringMap param,
                         ListStringMap& listTask,
                         ErrorInfo& e,
                         const Ice::Current& current) override;

        /** 
         * @brief   启动任务
         *
         * @param           taskID  任务ID
         * @param           param   参数
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool startTask(std::string taskID,
                       StringMap param,
                       ErrorInfo& e,
                       const Ice::Current& current) override;

        /** 
         * @brief   暂停任务
         *
         * @param           taskID  任务ID
         * @param           param   参数
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool pauseTask(std::string taskID,
                       StringMap param,
                       ErrorInfo& e,
                       const Ice::Current& current) override;

        /** 
         * @brief   继续任务
         *
         * @param           taskID  任务ID
         * @param           param   参数
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool resumeTask(std::string taskID,
                        StringMap param,
                        ErrorInfo& e,
                        const Ice::Current& current) override;

        /** 
         * @brief   重试任务
         *
         * @param           taskID  任务ID
         * @param           param   参数
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool retryTask(std::string taskID,
                       StringMap param,
                       ErrorInfo& e,
                       const Ice::Current& current) override;

        /** 
         * @brief   终止任务
         *
         * @param           taskID  任务ID
         * @param           param   参数
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool abolishTask(std::string taskID,
                         StringMap param,
                         ErrorInfo& e,
                         const Ice::Current& current) override;

        /** 
         * @brief   确认任务
         *
         * @param           taskID  任务ID
         * @param           param   参数
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool confirmTask(std::string taskID,
                         StringMap param,
                         ErrorInfo& e,
                         const Ice::Current& current) override;

        /** 
         * @brief   取消任务
         *
         * @param           taskID  任务ID
         * @param           param   参数
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool cancelTask(std::string taskID,
                        StringMap param,
                        ErrorInfo& e,
                        const Ice::Current& current) override;

        // ZGOPTaskOutage interface
    public:
        /** 
         * @brief   获取任务信息
         *
         * @param           taskID    任务ID
         * @param [in,out]  head      头信息
         * @param [in,out]  devices   设备信息
         * @param [in,out]  users     用户信息
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool getTaskInfo(std::string taskID,
                         StringMap& head,
                         ListStringMap& devices,
                         ListStringMap& users,
                         ErrorInfo& e,
                         const Ice::Current& current) override;

        /** 
         * @brief   创建任务
         *
         * @param           head      头信息
         * @param           devices   设备信息
         * @param           users     用户信息
         * @param [in,out]  taskID    任务ID
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool createTask(StringMap head,
                        ListStringMap devices,
                        ListStringMap users,
                        std::string& taskID,
                        ErrorInfo& e,
                        const Ice::Current& current) override;

        /** 
         * @brief   编辑任务
         *
         * @param           taskID    任务ID
         * @param           head      头信息
         * @param           devices   设备信息
         * @param           users     用户信息
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool editTask(std::string taskID,
                      StringMap head,
                      ListStringMap devices,
                      ListStringMap users,
                      ErrorInfo& e,
                      const Ice::Current& current) override;

        /** 
         * @brief   转移任务
         *
         * @param           taskID    任务ID
         * @param           oldUsers  旧用户信息
         * @param           newUsers  新用户信息
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool moveTask(std::string taskID,
                      ListStringMap oldUsers,
                      ListStringMap newUsers,
                      ErrorInfo& e,
                      const Ice::Current& current) override;

        /** 
         * @brief   转换任务
         *
         * @param           taskID    任务ID
         * @param           param     参数
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool convertTask(std::string taskID,
                         StringMap param,
                         ErrorInfo& e,
                         const Ice::Current& current) override;

        /**
         * 发送OTP短信
         * @param taskID 任务ID
         * @param listPhone 手机号列表
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool sendSMS(std::string taskID,
                     StringList listPhone,
                     ErrorInfo& e,
                     const Ice::Current& current) override;

        /** 
         * @brief   锁定隔离开关
         *
         * @param           clientID  客户端ID
         * @param           taskID    断电任务ID
         * @param           deviceID  设备ID
         * @param           OTP       OTP
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool lockIsolator(std::string clientID,
                          std::string taskID,
                          std::string deviceID,
                          std::string OTP,
                          ErrorInfo& e,
                          const Ice::Current& current) override;

        /** 
         * @brief   解锁隔离开关
         *
         * @param           clientID  客户端ID
         * @param           taskID    断电任务ID
         * @param           deviceID  设备ID
         * @param           OTP       OTP
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool unlockIsolator(std::string clientID,
                            std::string taskID,
                            std::string deviceID,
                            std::string OTP,
                            ErrorInfo& e,
                            const Ice::Current& current) override;

        /** 
         * @brief   批量锁定隔离开关
         *
         * @param           clientID   客户端ID
         * @param           taskID     断电任务ID
         * @param           deviceOTP  设备ID与OTP
         * @param [in,out]  e          错误信息
         * @param           current    Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool lockIsolatorBatch(std::string clientID,
                               std::string taskID,
                               StringMap deviceOTP,
                               ErrorInfo& e,
                               const Ice::Current& current) override;

        /** 
         * @brief   批量解锁隔离开关
         *
         * @param           clientID   客户端ID
         * @param           taskID     断电任务ID
         * @param           deviceOTP  设备ID与OTP
         * @param [in,out]  e          错误信息
         * @param           current    Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool unlockIsolatorBatch(std::string clientID,
                                 std::string taskID,
                                 StringMap deviceOTP,
                                 ErrorInfo& e,
                                 const Ice::Current& current) override;

        /** 
         * @brief   锁定接地开关
         *
         * @param           clientID  客户端ID
         * @param           taskID    断电任务ID
         * @param           deviceID  设备ID
         * @param           OTP       OTP
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool lockSwitch(std::string clientID,
                        std::string taskID,
                        std::string deviceID,
                        std::string OTP,
                        ErrorInfo& e,
                        const Ice::Current& current) override;

        /** 
         * @brief   解锁接地开关
         *
         * @param           clientID  客户端ID
         * @param           taskID    断电任务ID
         * @param           deviceID  设备ID
         * @param           OTP       OTP
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool unlockSwitch(std::string clientID,
                          std::string taskID,
                          std::string deviceID,
                          std::string OTP,
                          ErrorInfo& e,
                          const Ice::Current& current) override;

        /** 
         * @brief   批量锁定接地开关
         *
         * @param           clientID   客户端ID
         * @param           taskID     断电任务ID
         * @param           deviceOTP  设备ID与OTP
         * @param [in,out]  e          错误信息
         * @param           current    Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool lockSwitchBatch(std::string clientID,
                             std::string taskID,
                             StringMap deviceOTP,
                             ErrorInfo& e,
                             const Ice::Current& current) override;

        /** 
         * @brief   批量解锁接地开关
         *
         * @param           clientID   客户端ID
         * @param           taskID     断电任务ID
         * @param           deviceOTP  设备ID与OTP
         * @param [in,out]  e          错误信息
         * @param           current    Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool unlockSwitchBatch(std::string clientID,
                               std::string taskID,
                               StringMap deviceOTP,
                               ErrorInfo& e,
                               const Ice::Current& current) override;

        /** 
         * @brief   合上接地开关
         *
         * @param           clientID  客户端ID
         * @param           taskID    断电任务ID
         * @param           deviceID  设备ID
         * @param           OTP       OTP
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool closeSwitch(std::string clientID,
                         std::string taskID,
                         std::string deviceID,
                         std::string OTP,
                         ErrorInfo& e,
                         const Ice::Current& current) override;

        /** 
         * @brief   分开接地开关
         *
         * @param           clientID  客户端ID
         * @param           taskID    断电任务ID
         * @param           deviceID  设备ID
         * @param           OTP       OTP
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool openSwitch(std::string clientID,
                        std::string taskID,
                        std::string deviceID,
                        std::string OTP,
                        ErrorInfo& e,
                        const Ice::Current& current) override;

        /**
         * 批量合上接地开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceOTP 设备OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool closeSwitchBatch(std::string clientID,
                              std::string taskID,
                              StringMap deviceOTP,
                              ErrorInfo& e,
                              const Ice::Current& current) override;

        /**
         * 批量分开接地开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceOTP 设备OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool openSwitchBatch(std::string clientID,
                             std::string taskID,
                             StringMap deviceOTP,
                             ErrorInfo& e,
                             const Ice::Current& current) override;

        /** 
         * @brief   锁定断电任务
         *
         * @param           taskID     断电任务ID
         * @param           deviceOTP  设备OTP
         * @param [in,out]  e          错误信息
         * @param           current    Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool lockTask(std::string taskID,
                      StringMap deviceOTP,
                      ErrorInfo& e,
                      const Ice::Current& current) override;

        /** 
         * @brief   解锁断电任务
         *
         * @param           taskID     断电任务ID
         * @param           deviceOTP  设备OTP
         * @param [in,out]  e          错误信息
         * @param           current    Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool unlockTask(std::string taskID,
                        StringMap deviceOTP,
                        ErrorInfo& e,
                        const Ice::Current& current) override;

        /** 
         * @brief   获取监控设备
         *
         * @param           taskID      任务ID
         * @param [in,out]  listDevices 设备列表
         * @param [in,out]  e           错误信息
         * @param           current     Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool getMonitorDevices(std::string taskID,
                               ListStringMap& listDevices,
                               ErrorInfo& e,
                               const Ice::Current& current) override;

        /** 
         * @brief   申请工作许可
         *
         * @param           taskID    任务ID
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool applyPTW(std::string taskID,
                      ErrorInfo& e,
                      const Ice::Current& current) override;

        /** 
         * @brief   取消工作许可
         *
         * @param           taskID  任务ID
         * @param           OTP     OTP
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool cancelPTW(std::string taskID,
                       StringMap OTP,
                       ErrorInfo& e,
                       const Ice::Current& current) override;

        /** 
         * @brief   申请测试许可
         *
         * @param           taskID  任务ID
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool applySFT(std::string taskID,
                      ErrorInfo& e,
                      const Ice::Current& current) override;

        /** 
         * @brief   取消测试许可
         *
         * @param           taskID  任务ID
         * @param           OTP     OTP
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool cancelSFT(std::string taskID,
                       StringMap OTP,
                       ErrorInfo& e,
                       const Ice::Current& current) override;

        /** 
         * @brief   保存OTP
         *
         * @param           taskID  任务ID
         * @param           OTP     OTP
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool saveOTP(std::string taskID,
                     StringMap OTP,
                     ErrorInfo& e,
                     const Ice::Current& current) override;

        /**
         * 更改电话号码
         * @param taskID 断电任务ID
         * @param oldPhoneNumber 旧电话号码
         * @param newPhoneNumber 新电话号码
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool changePhone(std::string taskID,
                         std::string oldPhoneNumber,
                         std::string newPhoneNumber,
                         ErrorInfo& e,
                         const Ice::Current& current) override;

        /** 
         * @brief   确认断电阶段
         *
         * @param           taskID  任务ID
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool confirmOutage(std::string taskID,
                           ErrorInfo& e,
                           const Ice::Current& current) override;

        /** 
         * @brief   保存事件
         *
         * @param           taskID    任务ID
         * @param           deviceID  设备ID
         * @param           event     事件
         * @param [in,out]  e         错误信息
         * @param           current   Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool saveEvent(std::string taskID,
                       std::string deviceID,
                       std::string event,
                       ErrorInfo& e,
                       const Ice::Current& current) override;

        /** 
         * @brief   获取任务用户
         *
         * @param           taskID  任务ID
         * @param [in,out]  users   用户列表
         * @param [in,out]  e       错误信息
         * @param           current Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool getTaskUsers(std::string taskID,
                          ListStringMap& users,
                          ErrorInfo& e,
                          const Ice::Current& current) override;

        /**
         * 设置锁定设备密码
         * 
         * @param params 设备密码参数
         * @param e 错误信息
         * @param current Ice当前连接
         * 
         * @return 执行成功返回true，失败返回false。
         */
        bool setLockDevicePassword(StringMap params,
                                   ErrorInfo& e,
                                   const Ice::Current& current) override;

        /**
         * 解除外部闭锁
         * 
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceID 设备ID
         * @param e 错误信息
         * @param current Ice当前连接
         * 
         * @return 执行成功返回true，失败返回false。
         */
        bool unlockExternalLock(std::string clientID,
                                std::string taskID,
                                std::string deviceID,
                                ErrorInfo& e,
                                const Ice::Current& current) override;

        /**
         * 设备外部闭锁是否启用
         * 
         * @param taskID 断电任务ID
         * @param deviceEnable 外部闭锁设备是否启用
         * @param e 错误信息
         * @param current Ice当前连接
         * 
         * @return 执行成功返回true，失败返回false。
         */
        bool isExternalLockEnable(std::string taskID,
                                  StringMap& deviceEnable,
                                  ErrorInfo& e,
                                  const Ice::Current& current) override;

        /** 
         * @brief   获取接地开关合闸条件
         *
         * @param           taskID     任务ID
         * @param           deviceID   设备ID
         * @param [in,out]  conditions 条件
         * @param [in,out]  e          错误信息
         * @param           current    Ice当前连接
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool getSwitchCloseConditions(std::string taskID,
                                      std::string deviceID,
                                      ListStringMap& conditions,
                                      ErrorInfo& e,
                                      const Ice::Current& current) override;

        /**
         * @brief 检查接地开关合闸条件
         * 
         * @param taskID 任务ID
         * @param deviceID 设备ID
         * @param success 合闸条件是否满足
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool checkSwitchCloseConditions(std::string taskID,
                                        std::string deviceID,
                                        bool& success,
                                        ErrorInfo& e,
                                        const Ice::Current& current) override;
        /**
         * @brief 删除典型任务
         * 
         * @param taskID 任务ID
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool deleteTypicalTask(std::string taskID,
                               ErrorInfo& e,
                               const Ice::Current& current) override;

        /**
         * 根据传入的设备列表分析连续供电区域，分析结果并返回分段隔离开关的边界类型
         * @param inputDevice 传入的设备(key:设备ID value:设备子类型)
         * @param isValidRegion 是否为有效的连续供电区域
         * @param listOutputDevice 返回分段隔离开关列表，指明每个分段隔离开关是内部分段隔离开关还是边界分段
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool getDevicesBoundaryType(StringMap inputDevice,
                                    bool& isValidRegion,
                                    ListStringMap& listOutputDevice,
                                    ErrorInfo& e,
                                    const Ice::Current& current) override;
    };
} // namespace ZG6000

#endif // ZG6000_ZGOPTASKOUTAGEI_H

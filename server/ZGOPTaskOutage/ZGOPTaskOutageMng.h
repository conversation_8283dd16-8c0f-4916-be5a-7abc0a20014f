#ifndef ZG6000_ZGOPTASKOUTAGEMNG_H
#define ZG6000_ZGOPTASKOUTAGEMNG_H

#include <QObject>
#include <QTimer>
#include <QMutexLocker>
#include <unordered_set>
#include <Ice/LoggerUtil.h>

#include "Ice/Current.h"
#include "ZGServerCommon.h"

class ZGRedisClient;
class ZGMqttClient;
class NodeNetwork;

namespace ZG6000
{
    class ZGOPTaskOutageMng : public QObject
    {
        Q_OBJECT

    public:
        /**
         * 获取断电任务管理类实例
         * @return 断电任务管理类实例
         */
        static ZGOPTaskOutageMng* instance();

        /**
         * 执行对象初始化
         */
        void init();

        /**
         * 
         * @return 检测对象是否初始化成功
         */
        bool checkState();

        /**
         * 实现此接口用于底层框架调用，当实时数据发生变化时，自动调用此接口
         * @param tableName 表名
         * @param oper 操作（增加/删除/更新）
         * @param reason 原因
         * @param time 时间
         * @param listRecord 变化记录
         * @param current Ice当前连接
         */
        void dispatchData(std::string tableName,
                          std::string oper,
                          std::string reason,
                          std::string time,
                          ListRecord listRecord,
                          const Ice::Current& current);

        /**
         * 删除断电任务
         * @param taskID 断电任务ID
         * @param param 参数
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool deleteTask(std::string taskID,
                        StringMap param,
                        ErrorInfo& e,
                        const Ice::Current& current);

        /** 
         * 获取断电任务列表
         * @param param 参数
         * @param listTask 断电任务列表
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool getTaskList(StringMap param,
                         ListStringMap& listTask,
                         ErrorInfo& e,
                         const Ice::Current& current);

        /** 
         * 启动断电任务
         * @param taskID 断电任务ID
         * @param param 参数
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool startTask(std::string taskID,
                       StringMap param,
                       ErrorInfo& e,
                       const Ice::Current& current);
        /** 
         * 终止断电任务
         * @param taskID 断电任务ID
         * @param param 参数
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool abolishTask(std::string taskID,
                         StringMap param,
                         ErrorInfo& e,
                         const Ice::Current& current);

        /** 
         * 确认断电任务
         * @param taskID 断电任务ID
         * @param param 参数
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool confirmTask(std::string taskID,
                         StringMap param,
                         ErrorInfo& e,
                         const Ice::Current& current);

        /** 
         * 取消断电任务
         * @param taskID 断电任务ID
         * @param param 参数
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool cancelTask(std::string taskID,
                        StringMap param,
                        ErrorInfo& e,
                        const Ice::Current& current);

        /** 
         * 获取断电任务信息
         * @param taskID 断电任务ID
         * @param head 头信息
         * @param devices 设备信息
         * @param users 用户信息
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool getTaskInfo(std::string taskID,
                         StringMap& head,
                         ListStringMap& devices,
                         ListStringMap& users,
                         ErrorInfo& e,
                         const Ice::Current& current);

        /** 
         * 创建断电任务
         * @param head 头信息
         * @param devices 设备信息
         * @param users 用户信息
         * @param taskID 断电任务ID
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool createTask(StringMap head,
                        ListStringMap devices,
                        ListStringMap users,
                        std::string& taskID,
                        ErrorInfo& e,
                        const Ice::Current& current);

        /** 
         * 编辑断电任务
         * @param taskID 断电任务ID
         * @param head 头信息
         * @param devices 设备信息
         * @param users 用户信息
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool editTask(std::string taskID,
                      StringMap head,
                      ListStringMap devices,
                      ListStringMap users,
                      ErrorInfo& e,
                      const Ice::Current& current);

        /** 
         * 转移断电任务
         * @param taskID 断电任务ID
         * @param oldUsers 旧用户信息
         * @param newUsers 新用户信息
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool moveTask(std::string taskID,
                      ListStringMap oldUsers,
                      ListStringMap newUsers,
                      ErrorInfo& e,
                      const Ice::Current& current);

        /** 
         * 转换断电任务
         * @param taskID 断电任务ID
         * @param param 参数
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool convertTask(std::string taskID,
                         StringMap param,
                         ErrorInfo& e,
                         const Ice::Current& current);

        /** 
         * 确认断电阶段
         * @param taskID 断电任务ID
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool confirmOutage(std::string taskID,
                           ErrorInfo& e,
                           const Ice::Current& current);

        /** 
         * 发送OTP短信
         * @param taskID 断电任务ID
         * @param listMobileNumber 手机号码列表
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool sendOTP(std::string taskID,
                     StringList listMobileNumber,
                     ErrorInfo& e,
                     const Ice::Current& current);

        /** 
         * 锁定隔离开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceID 设备ID
         * @param OTP OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool lockIsolator(std::string clientID,
                          std::string taskID,
                          std::string deviceID,
                          std::string OTP,
                          ErrorInfo& e,
                          const Ice::Current& current);

        /** 
         * 解锁隔离开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceID 设备ID
         * @param OTP OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool unlockIsolator(std::string clientID,
                            std::string taskID,
                            std::string deviceID,
                            std::string OTP,
                            ErrorInfo& e,
                            const Ice::Current& current);

        /** 
         * 批量锁定隔离开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceOTP 设备ID与OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool lockIsolatorBatch(std::string clientID,
                               std::string taskID,
                               StringMap deviceOTP,
                               ErrorInfo& e,
                               const Ice::Current& current);

        /** 
         * 批量解锁隔离开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceOTP 设备ID与OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool unlockIsolatorBatch(std::string clientID,
                                 std::string taskID,
                                 StringMap deviceOTP,
                                 ErrorInfo& e,
                                 const Ice::Current& current);

        /** 
         * 锁定接地开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceID 设备ID
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool lockSwitch(std::string clientID,
                        std::string taskID,
                        std::string deviceID,
                        ErrorInfo& e,
                        const Ice::Current& current);

        /** 
         * 解锁接地开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceID 设备ID
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool unlockSwitch(std::string clientID,
                          std::string taskID,
                          std::string deviceID,
                          ErrorInfo& e,
                          const Ice::Current& current);

        /** 
         * 批量锁定接地开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceOTP 设备ID与OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool lockSwitchBatch(std::string clientID,
                             std::string taskID,
                             StringMap deviceOTP,
                             ErrorInfo& e,
                             const Ice::Current& current);

        /** 
         * 批量解锁接地开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceOTP 设备ID与OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool unlockSwitchBatch(std::string clientID,
                               std::string taskID,
                               StringMap deviceOTP,
                               ErrorInfo& e,
                               const Ice::Current& current);

        /** 
         * 合上接地开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceID 设备ID
         * @param OTP OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool closeSwitch(std::string clientID,
                         std::string taskID,
                         std::string deviceID,
                         std::string OTP,
                         ErrorInfo& e,
                         const Ice::Current& current);

        /**
        * 获取断电任务类型
        * @param taskID 断电任务ID
        * @param outageType 断电任务类型
        * @param errorInfo 错误信息
        * @return 执行成功返回true，失败返回false。
        */
        bool getOutageType(const std::string& taskID,
                           std::string& outageType,
                           const ErrorInfo& errorInfo);

        /** 
         * 分开接地开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceID 设备ID
         * @param OTP OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool openSwitch(std::string clientID,
                        std::string taskID,
                        std::string deviceID,
                        std::string OTP,
                        ErrorInfo& e,
                        const Ice::Current& current);

        /** 
         * 批量合上接地开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceOTP 设备OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool closeSwitchBatch(std::string clientID,
                              std::string taskID,
                              StringMap deviceOTP,
                              ErrorInfo& e,
                              const Ice::Current& current);

        /** 
         * 批量分开接地开关
         * @param clientID 客户端ID
         * @param taskID 断电任务ID
         * @param deviceOTP 设备OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool openSwitchBatch(std::string clientID,
                             std::string taskID,
                             StringMap deviceOTP,
                             ErrorInfo& e,
                             const Ice::Current& current);

        /** 
         * 锁定断电任务
         * @param taskID 断电任务ID
         * @param deviceOTP 设备OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool lockTask(std::string taskID,
                      StringMap deviceOTP,
                      ErrorInfo& e,
                      const Ice::Current& current);

        /** 
         * 解锁断电任务
         * @param taskID 断电任务ID
         * @param deviceOTP 设备OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool unlockTask(std::string taskID,
                        StringMap deviceOTP,
                        ErrorInfo& e,
                        const Ice::Current& current);

        /** 
         * 获取监控设备
         * @param taskID 断电任务ID
         * @param listDevices 设备信息
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool getMonitorDevices(std::string taskID,
                               ListStringMap& listDevices,
                               ErrorInfo& e,
                               const Ice::Current& current);
        /**
         * 申请工作许可
         * @param taskID 断电任务ID
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool applyPTW(std::string taskID,
                      ErrorInfo& e,
                      const Ice::Current& current);

        /** 
         * 取消工作许可
         * @param taskID 断电任务ID
         * @param OTP OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool cancelPTW(std::string taskID,
                       StringMap OTP,
                       ErrorInfo& e,
                       const Ice::Current& current);

        /**
         * 申请测试许可
         * @param taskID 断电任务ID
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool applySFT(std::string taskID,
                      ErrorInfo& e,
                      const Ice::Current& current);

        /** 
         * 取消测试许可
         * @param taskID 断电任务ID
         * @param OTP OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool cancelSFT(std::string taskID,
                       StringMap OTP,
                       ErrorInfo& e,
                       const Ice::Current& current);

        /**
         * 保存OTP
         * @param taskID 断电任务ID
         * @param OTP OTP
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool saveOTP(std::string taskID,
                     StringMap OTP,
                     ErrorInfo& e,
                     const Ice::Current& current);
        /**
         * 更改手机号码
         * @param taskID 断电任务ID
         * @param oldPhoneNumber 旧手机号码
         * @param newPhoneNumber 新手机号码
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool changePhone(std::string taskID,
                         std::string oldPhoneNumber,
                         std::string newPhoneNumber,
                         ErrorInfo& e,
                         const Ice::Current& current);

        /**
         * 保存事件
         * @param taskID 任务ID
         * @param deviceID 设备ID
         * @param event 第一语言事件
         * @param eventL2 第二语言事件
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool saveEvent(std::string taskID,
                       std::string deviceID,
                       std::string event,
                       std::string eventL2,
                       ErrorInfo& e,
                       const Ice::Current& current);

        /**
         * 解除外部闭锁
         * @param clientID 客户端ID
         * @param taskID 任务ID
         * @param deviceID 设备ID
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool unlockExternalLock(std::string clientID,
                                std::string taskID,
                                std::string deviceID,
                                ErrorInfo& e,
                                const Ice::Current& current);

        /**
         * 获取接地开关合闸联锁条件
         * @param taskID 任务ID
         * @param deviceID 设备ID
         * @param conditions 联锁条件
         * @param e 错误信息
         * @param current Ice当前连接
         * @return 执行成功返回true，失败返回false。
         */
        bool getSwitchCloseConditions(std::string taskID,
                                      std::string deviceID,
                                      ListStringMap& conditions,
                                      ErrorInfo& e,
                                      const Ice::Current& current);

        bool checkSwitchCloseConditions(std::string taskID,
                                        std::string deviceID,
                                        bool& success,
                                        ErrorInfo& e,
                                        const Ice::Current& current);

        bool deleteTypicalTask(std::string taskID,
                               ErrorInfo& e,
                               const Ice::Current& current);

        bool getDevicesBoundaryType(StringMap inputDevice,
                                    bool& isValidRegion,
                                    ListStringMap& listOutputDevice,
                                    ErrorInfo& e,
                                    const Ice::Current& current);

    private:
        explicit ZGOPTaskOutageMng(QObject* parent = nullptr);
        void initEvents();
        void initServerInstConfig();
        bool initServerInstInfo();
        bool initMqttClient();
        bool initRedisClient();
        bool initParams();
        bool initNodeNetwork();
        void initVariable();
        bool initLanguage();
        bool initKey();
        bool initTask(const std::string& taskID,
                      ErrorInfo& e);
        bool deleteTask(const std::string& taskID,
                        ErrorInfo& e);
        bool saveTask(const std::string& taskID,
                      const QDateTime& execStartTime,
                      ErrorInfo& e);
        bool saveTaskUserChange(const std::string& taskID,
                                const ListStringMap& oldUsers,
                                const ListStringMap& newUsers,
                                ErrorInfo& e);
        bool saveTaskUserStartTime(const std::string& taskID,
                                   ErrorInfo& e);
        bool saveTaskUserEndTime(const std::string& taskID,
                                 ErrorInfo& e);
        void calculateLockState();
        void calculateDeviceState();
        void updatePLCDeviceState();
        void recheckRebootContinueDevice();
        void lockUnlockedDevice();
        void unlockLockedDevice();
        void tryLockUnlockedDevice(const std::string& deviceID,
                                   const std::map<std::string, std::string>& property);
        void updateLEDTaskInfo();
        void updateDeviceCommState();
        void updateInterlockState();
        void calculateDeviceWarning();
        bool createTypicalTask(StringMap head,
                               ListStringMap devices,
                               ListStringMap users,
                               std::string& taskID,
                               ErrorInfo& e);
        bool createOtherTask(StringMap head,
                             ListStringMap devices,
                             ListStringMap users,
                             std::string& taskID,
                             ErrorInfo& e);
        bool checkDeviceDstState(const std::string& taskTypeID,
                                 const std::string& deviceID,
                                 const std::string& deviceTypeID,
                                 const std::string& state,
                                 std::string& errMsg);
        bool getListDeviceFromTask(const std::string& taskID,
                                   StringList& listDeviceID,
                                   std::string& errMsg);
        bool getListAppNodeFromDevices(const StringList& listDeviceID,
                                       StringList& listAppNodeID,
                                       std::string& errMsg);
        bool generateHeadParam(StringList& listSql,
                               const StringMap& headParam,
                               std::string& newTaskID);
        bool generateDevicesParam(StringList& listSql,
                                  const std::string& newTaskID,
                                  const ListStringMap& listDeviceParam);
        bool generateUsersParam(StringList& listSql,
                                const std::string& taskID,
                                const ListStringMap& listUserParam);
        void generateSwitchOutageSql(StringList& listSql,
                                     const std::string& taskID,
                                     const std::string& stageID);
        bool generateUpdateDeviceSubtypeStateSql(StringList& listSql,
                                                 const std::string& taskID,
                                                 const std::string& deviceSubtypeID,
                                                 const std::string& state,
                                                 ErrorInfo& e);
        bool getOutageHead(const std::string& taskID,
                           StringMap& head,
                           ErrorInfo& e);
        bool getOutageDevices(const std::string& taskID,
                              ListStringMap& devices,
                              ErrorInfo& e);
        bool getOutageUsers(const std::string& taskID,
                            ListStringMap& users,
                            ErrorInfo& e);
        bool sendCtrlCommand(const std::string& clientID,
                             const std::string& deviceID,
                             const std::string& propertyName,
                             const std::string& value);
        bool checkClientExists(const std::string& clientID,
                               ErrorInfo& e);
        bool checkDeviceExists(const std::string& deviceID,
                               ErrorInfo& e);
        bool checkTaskExist(const std::string& taskID,
                            ErrorInfo& e);
        bool checkTaskStage(const std::string& taskID,
                            const std::string& stageID,
                            ErrorInfo& e);
        bool checkTaskOTP(const std::string& taskID,
                          const StringMap& otp,
                          ErrorInfo& e);
        bool checkOutageStage(const std::string& taskID,
                              const std::string& stageID,
                              ErrorInfo& e);
        bool checkOutageState(const std::string& taskID,
                              const std::string& stateID,
                              ErrorInfo& e);
        bool checkDeviceType(const std::string& deviceID,
                             const std::string& deviceTypeID,
                             ErrorInfo& e);
        bool checkDeviceState(const std::string& taskID,
                              const std::string& deviceID,
                              const std::string& taskDeviceStateID,
                              ErrorInfo& e);
        bool checkDeviceTypeState(const std::string& taskID,
                                  const std::string& deviceTypeID,
                                  const std::string& taskDeviceStateID,
                                  ErrorInfo& e);
        bool checkDeviceTypeDstState(const std::string& taskID,
                                     const std::string& deviceTypeID,
                                     const std::string& propertyName,
                                     ErrorInfo& e,
                                     bool isInvert = false);
        bool checkDeviceDstState(const std::string& taskID,
                                 const std::string& deviceID,
                                 const std::string& propertyName,
                                 ErrorInfo& e,
                                 bool isInvert = false);
        bool checkOutInterlockState(const std::string& taskID,
                                    ErrorInfo& e);
        bool checkDeviceLockDeviceState(const std::string& deviceID,
                                        ErrorInfo& e);
        bool checkContinuityDeviceState(const std::string& taskID,
                                        ErrorInfo& e);
        bool checkSameUser(const StringMap& head,
                           const ListStringMap& users,
                           ErrorInfo& e);
        void checkZoneVoltage();
        bool checkDevicesParam(const ListStringMap& listDevice,
                               ErrorInfo& e);
        bool checkUsersParam(const ListStringMap& listUser,
                             ErrorInfo& e);
        bool compareDeviceDstState(const std::string& taskID,
                                   const std::string& deviceID,
                                   const std::string& propertyName,
                                   const std::string& expectValue,
                                   ErrorInfo& e);
        bool checkOtherTasksDeviceDstState(const std::string& taskID,
                                           const std::string& deviceID,
                                           const std::string& dstValue,
                                           ErrorInfo& e);
        bool checkDeviceOTP(const std::string& taskID,
                            const std::string& deviceID,
                            const std::string& otp,
                            ErrorInfo& e);
        bool lockDevice(const std::string& clientID,
                        const std::string& taskID,
                        const std::string& deviceID,
                        ErrorInfo& e);
        bool unlockDevice(const std::string& clientID,
                          const std::string& taskID,
                          const std::string& deviceID,
                          ErrorInfo& e);
        bool updateDeviceSubtypeState(const std::string& taskID,
                                      const std::string& deviceSubtypeID,
                                      const std::string& state,
                                      ErrorInfo& e);
        bool getKeyIDFromTaskDevice(const std::string& taskID,
                                    const std::string& deviceID,
                                    std::string& id,
                                    ErrorInfo& e);
        bool getKeyIDsFromTaskDevice(const std::string& taskID,
                                     const StringList& listDeviceID,
                                     StringList& listID,
                                     ErrorInfo& e);
        bool getKeyIDFromTaskUser(const std::string& taskID,
                                  const std::string& userID,
                                  std::string& id,
                                  ErrorInfo& e);
        bool getKeyIDsFromTaskUser(const std::string& taskID,
                                   const StringList& listUserID,
                                   StringList& listID,
                                   ErrorInfo& e);
        bool getDeviceTypeDevices(const std::string& taskID,
                                  const std::string& deviceTypeID,
                                  StringList& listDeviceID,
                                  ErrorInfo& e);
        bool checkRuleCondition(const std::string& deviceID,
                                const std::string& propertyName,
                                const std::string& propertyValue,
                                ErrorInfo& e);
        bool checkInterlockCondition(const std::string& taskID,
                                     const MapStringMap& mapCondition,
                                     ErrorInfo& e);
        bool switchToRestoreStage(const std::string& taskID,
                                  ErrorInfo& e);
        bool switchToGroundStage(const std::string& taskID,
                                 ErrorInfo& e);
        bool switchToRemoveGroundTestStage(const std::string& taskID,
                                           ErrorInfo& e);
        bool switchToRemoveGroundStage(const std::string& taskID,
                                       ErrorInfo& e);
        bool switchToPTWStage(const std::string& taskID,
                              ErrorInfo& e);
        bool switchToRequestSFTStage(const std::string& taskID,
                                     ErrorInfo& e);
        bool switchToTestStage(const std::string& taskID,
                               ErrorInfo& e);
        bool switchToCancelSFTStage(const std::string& taskID,
                                    ErrorInfo& e);
        bool switchToAbortState(const std::string& taskID,
                                ErrorInfo& e);
        bool switchOutageStage(const std::string& taskID,
                               const std::string& stageID,
                               ErrorInfo& e);
        void processTaskChange(const MapField& record);
        void processOutageTaskChange(const MapField& record);
        void processOutageTaskDeviceChange(const MapField& record);
        void processOutageTaskUserChange(const MapField& record);
        void processTaskAdd(const MapField& record);
        void processTaskDelete(const MapField& record);
        std::string generateOTP(int length = 4);
        bool sendTaskJsonInfoToDevice(const std::string& taskID,
                                      ErrorInfo& e);
        bool sendSMSMessage(const std::string& mobileNumber,
                            const std::string& message,
                            ErrorInfo& e);
        bool sendSMSWarnMessage(const std::string& taskID,
                                const std::string& deviceID);
        bool generateSafetyFileVerifyCode(const std::string& safetyFileCode,
                                          const std::vector<int> reorder,
                                          std::string& safetyFileVerifyCode,
                                          ErrorInfo& e);
        bool getHistorySaveYear(const std::string& taskID,
                                std::string& year,
                                ErrorInfo& e);
        void addTaskInfoToDevices(const std::string& taskID);
        void addTaskInfoToDevice(const std::string& taskID,
                                 const std::string& deviceID);
        void clearDeviceTaskInfo(const std::string& indiDeviceID);
        void sendTaskInfoToDevices();
        StringList getTaskListFromTaskInfo(const std::string& taskInfo);
        void saveOutageStageEvent(const std::string& taskID,
                                  const std::string& stageID,
                                  ErrorInfo& e);
        void disableGroundInterlock(const std::string& appNodeID,
                                    const MapStringMap& devices,
                                    StringMap deviceInterlock);
        void checkDisconnectorInterlock(const std::string& deviceID,
                                        const StringMap& deviceState,
                                        const std::function<void ()>& func);
        bool composeCondition(StringMap& condition,
                              const std::string& deviceID,
                              const std::string& propertyName,
                              const StringMap& deviceProperty,
                              const std::string& expectValue,
                              ErrorInfo& e);
        bool getAppNodeDevicesConditions(ListStringMap& conditions,
                                         const MapStringMap& taskDevices,
                                         const std::string& appNodeID,
                                         const std::string& currentDeviceID,
                                         std::unordered_set<std::string>& setAppNodes,
                                         std::unordered_set<std::string>& setDevices,
                                         ErrorInfo& e);

        bool encryptOTP(const std::string& otp,
                        std::string& encryptedOTP,
                        std::string& errMsg);

        bool decryptOTP(const std::string& encryptedOTP,
                        std::string& otp,
                        std::string& errMsg);

        bool getOverZoneDeviceState(const StringList& listDeviceID,
                                    ListStringMap& listDeviceState,
                                    ErrorInfo& e);

        // 根据设备列表获取设备所属的应用节点
        bool getAppNodesFromDevices(const StringList& listDeviceID, StringList& listAppNodeID);

        /**
         * 获取系统第一语言
         * @return 系统第一语言
         */
        [[nodiscard]] std::string firstLanguage() const
        {
            return m_firstLanguage;
        }

        /**
         * 获取系统第二语言
         * @return 系统第二语言
         */
        [[nodiscard]] std::string secondLanguage() const
        {
            return m_secondLanguage;
        }

    private slots:
        void onTimer();
        void onReceivedMessage(QString topic,
                               QString message);

    private:
        bool m_initialized{false}; // 是否初始化成功
        QString m_serverName{""}; // 服务器名称
        QString m_instName{""}; // 服务实例名称
        std::string m_localNodeID; // 本地节点ID
        int m_initInterval{10}; // 初始化间隔
        int m_checkInterval{10}; // 检测间隔
        size_t m_tickCount{0}; // 计数器
        ZGMqttClient* m_pMqttClient{nullptr}; // MQTT客户端
        ZGRedisClient* m_pRedisQueue{nullptr}; // Redis队列客户端
        ZGRedisClient* m_pRedisTopic{nullptr}; // Redis主题客户端
        NodeNetwork* m_pNodeNetwork{nullptr}; // 节点网络
        QTimer m_checkTimer; // 检测定时器
        StringList m_listSMSDeviceID; // 短信网关设备
        StringList m_listDeviceID; // 接地开关与隔离开关设备
        StringList m_listGroundDeviceID; // 接地开关设备
        StringList m_listLockDeviceID; // 安全闭锁设备
        StringList m_listOverZoneDeviceID; // 越区隔离开关
        MapStringMap m_mapAppNode; // 应用节点
        MapStringMap m_mapTaskStage; // 任务阶段
        MapStringMap m_mapTaskState; // 任务状态
        MapStringMap m_mapOutageType; // 断电类型
        MapStringMap m_mapOutageStage; // 断电阶段
        MapStringMap m_mapOutageState; // 断电状态
        MapStringMap m_mapDeviceType; // 设备类型
        MapStringMap m_mapOutageDeviceState; // 断电设备状态
        MapStringMap m_mapDataCategoryProperty; // 数据类别属性
        MapStringMap m_mapOperator; // 操作符
        StringMap m_mapDeviceAssocIndiDevice; // 设备关联警示牌(指示灯)设备
        StringMap m_mapDeviceAssocLockDevice; // 设备关联安全闭锁设备
        StringMap m_mapLockDeviceAssocDevice; // 安全闭锁设备关联设备
        StringMap m_mapGroundDeviceAssocOverZoneDevice; // 接地开关关联越区隔离开关
        StringMap m_mapGroundDeviceAssocContinuityDevice; // 接地开关关联连续性设备
        std::map<std::string, StringList> m_mapPlcAssocDevice; // PLC关联一次设备
        std::map<std::string, StringList> m_mapOutageTypeStage; // 断电类型阶段
        std::map<std::string, std::list<StringMap>> m_mapDeviceTasks; // 设备关联任务
        std::map<std::string, StringMap> m_mapAppNodeDevices; // 应用节点设备
        std::string m_firstLanguage; // 系统第一语言
        std::string m_secondLanguage; // 系统第二语言
        StringMap m_mapGroundDeviceRule; // 接地开关规则
        QMutex m_mutex; // 互斥锁
        QByteArray m_aesKey;
    };

    inline static ZGOPTaskOutageMng* g_pInstance = nullptr;
} // namespace ZG6000

#endif // ZG6000_ZGOPTASKOUTAGEMNG_H

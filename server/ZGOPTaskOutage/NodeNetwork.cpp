#include "NodeNetwork.h"
#include <algorithm>

NodeNetwork::NodeNetwork()
{
}

void NodeNetwork::addNodeVariables(const std::string& node,
                                   const StringList& variables)
{
    nodes_[node] = variables;
    for (const auto& var : variables)
    {
        variableToNodes_[var].insert(node);
        // 更新公共变量点信息
        updateCommonVariables(node, var);
    }
}

NodeNetwork::StringList NodeNetwork::getNodeVariables(const std::string& node) const
{
    if (nodes_.find(node) == nodes_.end())
        return {};
    return nodes_.at(node);
}

NodeNetwork::StringList NodeNetwork::getNetworkNodes() const
{
    StringList result;
    for (const auto& [node, _] : nodes_)
    {
        result.push_back(node);
    }
    return result;
}

void NodeNetwork::addNodeVariable(const std::string& node,
                                  const std::string& variable)
{
    nodes_[node].push_back(variable);
    variableToNodes_[variable].insert(node);
    // 更新公共变量点信息
    updateCommonVariables(node, variable);
}

void NodeNetwork::setVariableState(const std::string& variable,
                                   bool state)
{
    if (state)
        activeVariables_.insert(variable);
    else
        activeVariables_.erase(variable);
}

NodeNetwork::PathList NodeNetwork::getFinalConnectedPaths(const std::string& startNode,
                                                         int maxPathLength)
{
    StringSet visited;
    StringList currentPath;
    PathList finalPaths;
    dfs(startNode, visited, currentPath, finalPaths, maxPathLength);
    return finalPaths;
}

NodeNetwork::StringList NodeNetwork::getRegionTerminalNodes(const std::string& startNode,
                                                       int maxPathLength)
{
    // 首先找到从起始节点可达的所有节点
    StringSet reachableNodes;
    StringSet visited;
    dfsCollectReachableNodes(startNode, visited, reachableNodes, maxPathLength);

    // 然后在所有可达节点中找出终端节点（邻居数 <= 1）
    StringList endpoints;
    for (const auto& node : reachableNodes) {
        // 使用 getNeighbors 获取该节点的所有邻居
        StringList neighbors = getAllNeighborsByNode(node);

        // 如果邻居数 <= 1，则为终端节点
        if (neighbors.size() <= 1) {
            endpoints.push_back(node);
        }
    }

    // 排序终端节点列表以确保输出一致
    std::sort(endpoints.begin(), endpoints.end());

    return endpoints;
}

NodeNetwork::StringList NodeNetwork::getPrivateVariables(const std::string& node)
{
    StringList nonSharedVariables;

    if (nodes_.find(node) != nodes_.end()) {
        for (const auto& var : nodes_.at(node)) {
            // 检查这个变量是否与邻居节点共享
            bool isShared = false;
            if (variableToNodes_.find(var) != variableToNodes_.end()) {
                for (const auto& neighbor : variableToNodes_.at(var)) {
                    if (neighbor != node) {
                        isShared = true;
                        break;
                    }
                }
            }

            // 如果变量不与任何邻居共享，则为非公共变量
            if (!isShared) {
                nonSharedVariables.push_back(var);
            }
        }
    }

    // 排序非公共变量以便输出一致
    std::sort(nonSharedVariables.begin(), nonSharedVariables.end());
    return nonSharedVariables;
}

NodeNetwork::NodesAndTerminals NodeNetwork::getRegionNodes(const std::string& startNode,
                                                                     int maxPathLength)
{
    // 找到从起始节点可达的所有节点
    StringSet reachableNodes;
    StringSet visited;
    dfsCollectReachableNodes(startNode, visited, reachableNodes, maxPathLength);

    // 将所有节点转换为vector并排序
    StringList allNodes(reachableNodes.begin(), reachableNodes.end());
    std::sort(allNodes.begin(), allNodes.end());

    // 在可达节点中找出终端节点（邻居数 <= 1）
    StringList terminalNodes;
    for (const auto& node : reachableNodes) {
        // 使用 getNeighbors 获取该节点的所有邻居
        StringList neighbors = getAllNeighborsByNode(node);

        // 如果邻居数 <= 1，则为终端节点
        if (neighbors.size() <= 1) {
            terminalNodes.push_back(node);
        }
    }

    // 排序终端节点列表
    std::sort(terminalNodes.begin(), terminalNodes.end());

    return std::make_pair(allNodes, terminalNodes);
}

NodeNetwork::StringList NodeNetwork::getRegionAllNodes(const std::string& startNode,
                                                   int maxPathLength)
{
    // 找到从起始节点可达的所有节点
    StringSet reachableNodes;
    StringSet visited;
    dfsCollectReachableNodes(startNode, visited, reachableNodes, maxPathLength);

    // 将结果转换为vector并排序，以便输出一致
    StringList result(reachableNodes.begin(), reachableNodes.end());
    std::sort(result.begin(), result.end());

    return result;
}

NodeNetwork::StringList NodeNetwork::getAllActiveNodes(const std::string& startNode,
                                                        int maxPathLength)
{
    StringSet reachableNodes;
    StringSet visited;
    dfsCollectReachableNodes(startNode, visited, reachableNodes, maxPathLength, true);

    StringList result(reachableNodes.begin(), reachableNodes.end());
    std::sort(result.begin(), result.end());

    return result;
}

NodeNetwork::StringList NodeNetwork::getPublicVariablesBetweenNodes(const std::string& node1,
                                                        const std::string& node2)
{
    if (commonVariables_.find(node1) == commonVariables_.end() || commonVariables_[node1].find(node2) ==
        commonVariables_[node1].end())
        return {}; // 如果没有公共变量点，返回空列表
    return commonVariables_[node1][node2];
}

NodeNetwork::NodeVariablesMap NodeNetwork::getPublicVariablesByNode(const std::string& node)
{
    NodeVariablesMap result;
    if (commonVariables_.find(node) == commonVariables_.end())
        return result; // 如果节点不存在，返回空映射

    for (const auto& [otherNode, commonVars] : commonVariables_[node])
    {
        result[otherNode] = commonVars;
    }

    return result;
}

NodeNetwork::StringList NodeNetwork::getAllNeighborsByNode(const std::string& node) const
{
    // 如果该节点不存在，返回空列表
    if (nodes_.find(node) == nodes_.end())
        return {};
    // 获取该节点的所有邻居节点
    StringList neighbors;
    for (const auto& var : nodes_.at(node))
    {
        for (const auto& neighbor : variableToNodes_.at(var))
        {
            if (neighbor != node)
            {
                if (std::find(neighbors.begin(), neighbors.end(), neighbor) == neighbors.end())
                    neighbors.push_back(neighbor);
            }
        }
    }
    return neighbors;
}

NodeNetwork::StringList NodeNetwork::getNeighborsByNodeVariable(const std::string& node,
                                                   const std::string& variable) const
{
    // 如果该节点不存在，返回空列表
    if (nodes_.find(node) == nodes_.end())
        return {};
    // 获取该节点的所有邻居节点
    StringList neighbors;
    for (const auto& neighbor : variableToNodes_.at(variable))
    {
        if (neighbor != node)
            neighbors.push_back(neighbor);
    }
    return neighbors;
}

NodeNetwork::StringList NodeNetwork::getNodesByVariable(const std::string& variable) const
{
    if (variableToNodes_.find(variable) == variableToNodes_.end())
        return {};
    StringList nodes;
    for (const auto& node : variableToNodes_.at(variable))
    {
        nodes.push_back(node);
    }
    return nodes;
}

void NodeNetwork::getNodesAndVariables(const std::string& startNode,
                                           int maxPathLength,
                                           StringList& nodes,
                                           VariablesPair& variables)
{
    NodeNetwork::VariablesPair variablesPair;
    nodes = getRegionAllNodes(startNode, maxPathLength);
    if (nodes.empty()) {
        return;
    }
    //收集连通区域内所有变量及其使用计数
    std::unordered_map<std::string, int> variableUsageCount;
    for (const auto& node : nodes) {
        // 使用现有接口获取节点的变量列表
        auto nodeVars = getNodeVariables(node);

        for (const auto& var : nodeVars) {
            variableUsageCount[var]++;
        }
    }
    for (const auto& [variable, count] : variableUsageCount) {
        if (count >= 2) {
            // 被2个或更多节点使用的变量为公共变量
            variables.first.push_back(variable);
        } else {
            // 仅被1个节点使用的变量为私有变量
            variables.second.push_back(variable);
        }
    }
    // 排序变量列表以确保输出一致性
    std::sort(variables.first.begin(), variables.first.end());
    std::sort(variables.second.begin(), variables.second.end());
}                                           

void NodeNetwork::clear()
{
    // 清除所有节点到变量的映射
    nodes_.clear();

    // 清除所有变量到节点的反向映射
    variableToNodes_.clear();

    // 清除所有激活变量状态
    activeVariables_.clear();

    // 清除所有公共变量信息
    commonVariables_.clear();
}

void NodeNetwork::dfs(const std::string& currentNode,
                      StringSet& visited,
                      StringList& currentPath,
                      PathList& finalPaths,
                      size_t maxPathLength)
{
    visited.insert(currentNode);
    currentPath.push_back(currentNode);

    // 检查是否有未访问的邻居节点（不限于活跃变量）
    bool hasUnvisitedNeighbors = false;
    for (const auto& var : nodes_[currentNode])
    {
        for (const auto& neighbor : variableToNodes_[var])
        {
            if (neighbor != currentNode && visited.find(neighbor) == visited.end())
            {
                hasUnvisitedNeighbors = true;
                break;
            }
        }
        if (hasUnvisitedNeighbors)
            break;
    }

    // 如果没有未访问的邻居节点，或者路径长度达到限制，说明当前路径是一个最终路径
    if (!hasUnvisitedNeighbors || currentPath.size() >= maxPathLength)
    {
        finalPaths.push_back(currentPath);
    }
    else
    {
        for (const auto& var : nodes_[currentNode])
        {
            for (const auto& neighbor : variableToNodes_[var])
            {
                if (neighbor != currentNode && visited.find(neighbor) == visited.end())
                {
                    dfs(neighbor, visited, currentPath, finalPaths, maxPathLength);
                }
            }
        }
    }

    // 回溯前移除当前节点和标记
    currentPath.pop_back();
    visited.erase(currentNode);
}

void NodeNetwork::dfsCollectReachableNodes(const std::string& currentNode,
                                           StringSet& visited,
                                           StringSet& reachableNodes,
                                           size_t maxPathLength,
                                           bool onlyActiveVariables)
{
    // 只有当节点存在于网络中时才处理
    if (nodes_.find(currentNode) == nodes_.end()) {
        return;
    }

    visited.insert(currentNode);
    reachableNodes.insert(currentNode);

    // 如果已达到最大路径长度，停止搜索
    if (visited.size() >= maxPathLength) {
        visited.erase(currentNode);
        return;
    }

    // 遍历变量和邻居节点
    for (const auto& var : nodes_.at(currentNode)) {
        // 根据参数决定是否只考虑活跃变量
        bool shouldProcessVariable = true;
        if (onlyActiveVariables) {
            shouldProcessVariable = (activeVariables_.find(var) != activeVariables_.end());
        }

        if (variableToNodes_.find(var) != variableToNodes_.end() && shouldProcessVariable) {
            for (const auto& neighbor : variableToNodes_.at(var)) {
                if (neighbor != currentNode && visited.find(neighbor) == visited.end()) {
                    dfsCollectReachableNodes(neighbor, visited, reachableNodes, maxPathLength, onlyActiveVariables);
                }
            }
        }
    }

    visited.erase(currentNode);
}

void NodeNetwork::updateCommonVariables(const std::string& node,
                                        const std::string& var)
{
    for (const auto& otherNode : variableToNodes_[var])
    {
        if (otherNode == node)
            continue; // 跳过自身

        // 更新 node 和 otherNode 之间的公共变量点
        commonVariables_[node][otherNode].push_back(var);
        commonVariables_[otherNode][node].push_back(var);
    }
}

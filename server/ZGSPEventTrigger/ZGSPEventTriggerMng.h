#ifndef ZGSPEVENTTRIGGERMNG_H
#define ZGSPEVENTTRIGGERMNG_H

#include <QObject>
#include <QTimer>
#include <chrono>
#include <unordered_map>
#include "ZGProxyMng.h"

class ZGRedisClient;
class ZGSPEventTriggerMng : public QObject
{
    Q_OBJECT
public:
    static ZGSPEventTriggerMng* instance();
    void init();
    bool checkState();

private slots:
    void onPeriodTask();

private:
    explicit ZGSPEventTriggerMng(QObject* parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initEventParam();
    void doEventTask();

private:
    QString m_instName{""};
    QString m_serverName{""};
    int m_initInterval{10};
    int m_taskInterval{1};
    int m_checkInterval{10};
    QTimer m_taskTimer;
    bool m_initialized{false};
    ZG6000::MapStringMap m_mapEventParam;
    struct InstReal
    {
        bool trigger{false};
        QDateTime updateTime{QDateTime::currentDateTime()};
    };
    std::unordered_map<std::string, InstReal> m_mapInstReal;
};

#endif // ZGSPEVENTTRIGGERMNG_H

#include "ZGSPEventTriggerMng.h"

#include <QRandomGenerator>

#include "ZGUtils.h"
#include "redis/ZGRedisClient.h"
#include "ZGJson.h"
#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGHeartMng.h"
#include "ZGProxyCommon.h"

static ZGSPEventTriggerMng* g_pEventTriggerMng = nullptr;

ZGSPEventTriggerMng *ZGSPEventTriggerMng::instance()
{
    if (g_pEventTriggerMng == nullptr)
        g_pEventTriggerMng = new ZGSPEventTriggerMng;
    return g_pEventTriggerMng;
}

void ZGSPEventTriggerMng::init()
{
    initEvents();
    initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::sleep(m_initInterval);
    }
    QThread::sleep(QRandomGenerator::global()->bounded(1, 5));
    while (!initEventParam())
    {
        ZGLOG_ERROR("initEventParam error.");
        QThread::sleep(m_initInterval);
    }
    m_initialized = true;
    ZGLOG_INFO("ZGSPEventTrigger init finished.");
    m_taskTimer.start(1000);
}

bool ZGSPEventTriggerMng::checkState()
{
    return m_initialized;
}

bool strToBool(const std::string& str)
{
    return (str == "true" || str == "1");
}

void ZGSPEventTriggerMng::onPeriodTask()
{
    if (!ZGRuntime::instance()->isMaster())
        return;
    doEventTask();
}

void ZGSPEventTriggerMng::initEvents()
{
    connect(&m_taskTimer, &QTimer::timeout, this, &ZGSPEventTriggerMng::onPeriodTask);
}

void ZGSPEventTriggerMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
}

bool ZGSPEventTriggerMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGSPEventTriggerMng::initEventParam()
{
    QString sql = QString("SELECT * FROM sp_param_rule_event WHERE isEnable = 1 AND actionExpressionID <> ''");
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapEventParam))
    {
        ZGLOG_ERROR(QStringLiteral("获取触发事件参数失败"));
        return false;
    }
    for (const auto & [eventID, eventParam] : m_mapEventParam)
    {
        m_mapInstReal[eventID] = InstReal{false, QDateTime::currentDateTime() };
    }
    return true;
}

void ZGSPEventTriggerMng::doEventTask()
{
    ZG6000::ListStringMap listExpression;
    ZG6000::StringList listEventID;
    auto scriptProxy = ZGProxyMng::instance()->getProxySPScriptProcess();
    if (scriptProxy == nullptr)
    {
        ZGLOG_ERROR("getProxySPScriptProcess error.");
        return;
    }
    QDateTime dt = QDateTime::currentDateTime();
    ZGLOG_TRACE(QString("m_mapEventParam size: %1").arg(m_mapEventParam.size()));
    for (const auto& [eventID, eventParam] : m_mapEventParam)
    {
        QString strConditionExpressionID = ZGUtils::get(eventParam, "conditionExpressionID").c_str();
        strConditionExpressionID = strConditionExpressionID.trimmed();
        QString strActionExpressionID = ZGUtils::get(eventParam, "actionExpressionID").c_str();
        strActionExpressionID = strActionExpressionID.trimmed();
        const auto& conditionExpressionID = strConditionExpressionID.toStdString();
        const auto& conditionExpressionParam = ZGUtils::get(eventParam, "conditionExpressionParam");
        const auto& actionExpressionID = strActionExpressionID.toStdString();
        const auto& actionExpressionParam = ZGUtils::get(eventParam, "actionExpressionParam");
        const auto& interval = std::atoi(ZGUtils::get(eventParam, "execInterval").c_str());
        bool newValue{ false };
        if (actionExpressionParam.empty())
            continue;
        if (conditionExpressionID.empty())
            newValue = true;
        else
        {
            std::string result;
            ZG6000::ErrorInfo e;
            if (!scriptProxy->callToString(conditionExpressionID, conditionExpressionParam, result, e))
            {
                ZGLOG_ERROR(e);
                continue;
            }
            newValue = strToBool(result);
        }
        if (m_mapInstReal[eventID].trigger != newValue)
        {
            if (newValue)
            {
                listExpression.push_back({ {"id", actionExpressionID}, {"param", actionExpressionParam} });
                m_mapInstReal[eventID].updateTime = dt;
                listEventID.push_back(eventID);
            }
            m_mapInstReal[eventID].trigger = newValue;
        }
        else
        {
            if (newValue)
            {
                if ((interval > 0) && (std::abs(m_mapInstReal[eventID].updateTime.secsTo(dt)) >= interval))
                {
                    listExpression.push_back({ {"id", actionExpressionID}, {"param", actionExpressionParam} });
                    m_mapInstReal[eventID].updateTime = dt;
                    listEventID.push_back(eventID);
                }
            }
        }
    }
    try
    {
        scriptProxy->callBatch(listExpression);
        ZGProxyCommon::mupdateDataByField("sp_param_rule_event", listEventID, "rtUpdateTime", ZGUtils::DateTimeToString(dt).toStdString());
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

ZGSPEventTriggerMng::ZGSPEventTriggerMng(QObject *parent) : QObject(parent)
{

}

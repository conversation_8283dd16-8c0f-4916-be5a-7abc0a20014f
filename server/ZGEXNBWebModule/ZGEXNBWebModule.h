#ifndef ZGEXNBWEBMODULE_H
#define ZGEXNBWEBMODULE_H

#include "ZGWebModule.h"
#include "ZGServerCommon.h"
#include "QtHttpServer/QHttpServer"

class ZGMqttClient;
class ZGEXNBWebModule : public ZGWebModule
{
    Q_OBJECT
    Q_PLUGIN_METADATA(IID ZGWebModule_iid FILE "ZGEXNBWebModule.json")
    Q_INTERFACES(ZGWebModule)

public:
    explicit ZGEXNBWebModule(QObject *parent = nullptr);

    // ZGWebModule interface
public:
    bool initialize() override;

private:
    QHttpServerResponse on_ex_nb_user_sync(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_ex_nb_user_add(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_ex_nb_user_del(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_ex_nb_user_edit(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_ex_nb_ot_task_list(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_ex_nb_ot_task_create(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_ex_nb_ot_task_update(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_ex_nb_ot_task_cancel(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_ex_nb_appnode_user_set(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);
    QHttpServerResponse on_ex_nb_heartbeat(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req);

private:
    bool changeUser(const ZG6000::ListStringMap& listUser, QString& errMsg, bool newUser);

private:
    ZGMqttClient* m_pMqttClient{nullptr};
};

#endif // ZGEXNBWEBMODULE_H

#include "ZGEXNBWebModule.h"
#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"
#include "ZGMqttClient.h"

ZGEXNBWebModule::ZGEXNBWebModule(QObject* parent)
    : ZGWebModule(parent)
{
    registerHandle("ex/nb/user/sync", this, &ZGEXNBWebModule::on_ex_nb_user_sync);
    registerHandle("ex/nb/user/add", this, &ZGEXNBWebModule::on_ex_nb_user_add);
    registerHandle("ex/nb/user/del", this, &ZGEXNBWebModule::on_ex_nb_user_del);
    registerHandle("ex/nb/user/edit", this, &ZGEXNBWebModule::on_ex_nb_user_edit);
    registerHandle("ex/nb/ot/task/list", this, &ZGEXNBWebModule::on_ex_nb_ot_task_list);
    registerHandle("ex/nb/ot/task/create", this, &ZGEXNBWebModule::on_ex_nb_ot_task_create);
    registerHandle("ex/nb/ot/task/update", this, &ZGEXNBWebModule::on_ex_nb_ot_task_update);
    registerHandle("ex/nb/ot/task/cancel", this, &ZGEXNBWebModule::on_ex_nb_ot_task_cancel);
    registerHandle("ex/nb/appnode/user/set", this, &ZGEXNBWebModule::on_ex_nb_appnode_user_set);
    registerHandle("ex/nb/heartbeat", this, &ZGEXNBWebModule::on_ex_nb_heartbeat);
}

bool ZGEXNBWebModule::initialize()
{
    bool success = ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE);
    if (!success)
    {
        ZGLOG_ERROR(QStringLiteral("初始化MQTT客户端失败"));
        return false;
    }
    m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
    if (m_pMqttClient == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage return null.");
        return false;
    }
    m_pMqttClient->connectToHost();
    return true;
}

QHttpServerResponse ZGEXNBWebModule::on_ex_nb_user_sync(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
    const auto& userArray = param.toArray();
    const auto & listUser = arrayToListStringMap(userArray);
    ZG6000::StringList listSql;
}

QHttpServerResponse ZGEXNBWebModule::on_ex_nb_user_add(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
    const auto& userArray = param.toArray();
    const auto & listUser = arrayToListStringMap(userArray);
    QString errMsg;
    if (!changeUser(listUser, errMsg, true))
        return errorObject(errMsg);
    return replyObject("");
}

QHttpServerResponse ZGEXNBWebModule::on_ex_nb_user_del(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
    const auto& userArray = param.toArray();
    const auto & listUserID = arrayToStringList(userArray);
    const auto& userIDs = ZGUtils::join(listUserID, ",", "'", "'");
    QString sql = QString("delete from sp_param_hrm_user where id in (%1)").arg(userIDs.c_str());
    ZG6000::StringList listSql;
    listSql.push_back(sql.toStdString());
    sql = QString("delete from sp_param_hrm_user_role where userID in (%1)").arg(userIDs.c_str());
    listSql.push_back(sql.toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
        return errorObject(QStringLiteral("删除用户失败"));
    return replyObject("");
}

QHttpServerResponse ZGEXNBWebModule::on_ex_nb_user_edit(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
    const auto& userArray = param.toArray();
    const auto & listUser = arrayToListStringMap(userArray);
    QString errMsg;
    if (!changeUser(listUser, errMsg, false))
        return errorObject(errMsg);
    return replyObject("");
}

QHttpServerResponse ZGEXNBWebModule::on_ex_nb_ot_task_list(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
    QString sql = QString("SELECT a.id, a.destTypeID, b.name AS destTypeName, a.appNodeID, c.name AS appNodeName, a.number, a.name, a.operUserID, a.monUserID, a.startTime, a.endTime, a.otID, "
                          "d.name AS operUserName, e.name AS monUserName, f.name AS otName FROM op_param_ot_task a "
                          "LEFT JOIN op_dict_ot_dest_type b ON a.destTypeID = b.id "
                          "LEFT JOIN sp_param_appnode c ON a.appNodeID = c.id "
                          "LEFT JOIN sp_param_hrm_user d ON a.operUserID = d.id "
                          "LEFT JOIN sp_param_hrm_user e ON a.monUserID = e.id "
                          "LEFT JOIN op_param_task f ON a.otID = f.id");
    ZG6000::ListStringMap listOtTask;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listOtTask))
    {
        return errorObject(QStringLiteral("获取断送电任务失败"));
    }
    auto taskArray = listStringMapToArray(listOtTask);
    return replyObject(taskArray);
}

QHttpServerResponse ZGEXNBWebModule::on_ex_nb_ot_task_create(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!checkRequiredFields(object, {
        "destTypeID", "appNodeID", "number", "operUserID", "monUserID",
        "startTime", "endTime"
    }, errMsg))
        return errorObject(errMsg);
    ZG6000::StringMap otTask;
    std::string uuid;
    if (!ZGProxyCommon::createUUID(uuid))
        return errorObject(QStringLiteral("创建断送电任务ID失败"));
    otTask["id"] = uuid;
    // TODO: 根据对方的断送电目标类型，建立对应的映射关系
    const auto& destTypeID = object["destTypeID"].toString();
    const auto& appNodeID = object["appNodeID"].toString();
    // 先从op_param_ot_task表中查询有没有相同appNodeID和destType的记录
    QString sql = QString("select id from op_param_ot_task where appNodeID = '%1' and destTypeID = '%2'").arg(appNodeID, destTypeID);
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
        return errorObject(QStringLiteral("获取断送电任务ID失败"));
    if (!listID.empty())
        return errorObject(QStringLiteral("该节点已存在相同的断送电任务，请先等待该任务结束"));
    otTask["destTypeID"] = object["destTypeID"].toString().toStdString();
    otTask["appNodeID"] = object["appNodeID"].toString().toStdString();
    otTask["number"] = object["number"].toString().toStdString();
    otTask["operUserID"] = object["operUserID"].toString().toStdString();
    otTask["monUserID"] = object["monUserID"].toString().toStdString();
    otTask["startTime"] = object["startTime"].toString().toStdString();
    otTask["endTime"] = object["endTime"].toString().toStdString();
    if (!ZGProxyCommon::execSql(ZGUtils::generateInsertSql("op_param_ot_task", otTask)))
        return errorObject(QStringLiteral("创建断送电任务失败"));
    return replyObject("");
}

QHttpServerResponse ZGEXNBWebModule::on_ex_nb_ot_task_update(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!checkRequiredFields(object, {"id"}, errMsg))
        return errorObject(errMsg);
    auto task = objectToStringMap(object);
    auto sql = ZGUtils::generateUpdateSql("op_param_ot_task", task);
    if (!ZGProxyCommon::execSql(sql))
        return errorObject(QStringLiteral("更新断电任务失败"));
    return replyObject("");
}

QHttpServerResponse ZGEXNBWebModule::on_ex_nb_ot_task_cancel(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
    const auto& number = param.toString();
    if (number.isEmpty())
        return errorObject(QStringLiteral("任务编号不能为空"));
    QString sql = QString("SELECT * FROM op_param_ot_task WHERE rtNumber = '%1'").arg(number);
    ZG6000::ListStringMap listTask;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
        return errorObject(QStringLiteral("获取断送电任务失败"));
    if (listTask.empty())
        return errorObject(QStringLiteral("未找到该断送电任务"));
    auto task = listTask.front();
    if (!task["rtOtID"].empty())
    {
        auto otProxy = ZGProxyMng::instance()->getProxyOPTaskOT();
        if (!otProxy)
            return errorObject(QStringLiteral("获取操作票任务代理失败"));
        try
        {
            ZG6000::ErrorInfo e;
            if (!otProxy->abolishTask(task["rtOtID"], {}, e))
                return errorObject(e.errDetail.c_str());
        }
        catch (const Ice::Exception& e)
        {
            return errorObject(e.what());
        }
    }
    sql = QString("DELETE FROM op_param_ot_task WHERE rtNumber = '%1'").arg(number);
    if (!ZGProxyCommon::execSql(sql.toStdString()))
        return errorObject(QStringLiteral("删除断送电任务失败"));
    return replyObject("");
}

QHttpServerResponse ZGEXNBWebModule::on_ex_nb_appnode_user_set(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!checkRequiredFields(object, {
        "appNodeID", "users"
    }, errMsg))
        return errorObject(errMsg);
    const auto& appNodeID = object["appNodeID"].toString();
    if (appNodeID.isEmpty())
        return errorObject(QStringLiteral("应用节点ID不能为空"));
    const auto& userArray = object["users"].toArray();
    ZG6000::StringList listSql, listUUID;
    listSql.push_back(QStringLiteral("delete from sp_param_appnode_user where appNodeID = '%1'").arg(appNodeID).toStdString());
    if (!ZGProxyCommon::createUUID(static_cast<int>(userArray.size()), listUUID))
        return errorObject(QStringLiteral("生成用户ID失败"));
    const auto& listUserID = arrayToStringList(userArray);
    for (size_t i = 0; i < listUserID.size(); ++i)
    {
        ZG6000::StringMap user;
        user["id"] = listUUID[i];
        user["appNodeID"] = appNodeID.toStdString();
        user["userID"] = listUserID[i];
        listSql.push_back(ZGUtils::generateInsertSql("sp_param_appnode_user", user));
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
        return errorObject(QStringLiteral("更新应用节点用户失败"));
    return replyObject("");
}

QHttpServerResponse ZGEXNBWebModule::on_ex_nb_heartbeat(const QString& clientID, const QJsonValue& param, const QHttpServerRequest& req)
{
    QString message = QString("{\"id\":\"%1\",\"time\":\"%2\"}")
        .arg(clientID, ZGUtils::DateTimeToString(QDateTime::currentDateTime()));
    m_pMqttClient->sendPublish("ZG_T_CLIENT_HEART", message);
    return replyObject("");
}

bool ZGEXNBWebModule::changeUser(const ZG6000::ListStringMap& listUser, QString& errMsg, bool newUser)
{
    ZG6000::StringList listSql, listUserRoleID, listFileContentID;
    if (!ZGProxyCommon::createUUID(static_cast<int>(listUser.size()), listUserRoleID))
    {
        errMsg = QStringLiteral("生成用户ID失败");
        return false;
    }
    if (!ZGProxyCommon::createUUID(static_cast<int>(listUser.size()), listFileContentID))
    {
        errMsg = QStringLiteral("生成文件内容ID失败");
        return false;
    }
    int index = 0, fileContentIndex = 0;
    for (const auto& user : listUser)
    {
        ZG6000::StringMap userInfo{{"password", "123"}};
        ZG6000::StringMap userRole;
        if (user.find("name") != user.end())
            userInfo["name"] = ZGUtils::get(user, "name");
        if (user.find("id") == user.end())
        {
            errMsg = QStringLiteral("用户ID不能为空");
            return false;
        }
        userInfo["id"] = ZGUtils::get(user, "id");
        if (user.find("employeeNumber") != user.end())
            userInfo["employeeNumber"] = ZGUtils::get(user, "employeeNumber");
        if (user.find("mobileNumber") != user.end())
            userInfo["mobileNumber"] = ZGUtils::get(user, "mobileNumber");
        if (user.find("photo") != user.end())
        {
            // 用户表通过photoContentID字段将内容保存到单独的文件内容表中，
            // 因此，如果是新建用户，需要先将图片内容保存到文件内容表中，然后再将文件内容ID保存到用户表中
            // 如果是更新用户，根据photoContentID字段更新文件内容表中的内容
            if (newUser)
            {
                ZG6000::StringMap fileContent;
                fileContent["id"] = listFileContentID[fileContentIndex++];
                fileContent["content"] = ZGUtils::get(user, "photo");
                const auto& fileSize = fileContent["content"].size() * 4 / 3;
                if (fileSize >= static_cast<size_t>(200 * 1024))
                {
                    errMsg = QStringLiteral("用户'%1'的照片大小超过200KB").arg(userInfo["id"].c_str());
                    return false;
                }
                userInfo["photoHash"] = std::to_string(std::hash<std::string>()(fileContent["content"]));
                userInfo["photoContentID"] = fileContent["id"];
                listSql.push_back(ZGUtils::generateInsertSql("sp_param_file_content", fileContent));
            }
            else
            {
                // 从用户表中获取photoContentID字段的值，然后根据该值更新文件内容表中的内容
                QString sql = QString("select photoContentID from sp_param_hrm_user where id = '%1'").arg(userInfo["id"].c_str());
                ZG6000::StringList listPhotoContentID;
                if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listPhotoContentID))
                {
                    errMsg = QStringLiteral("获取用户头像内容ID失败");
                    return false;
                }
                if (listPhotoContentID.empty())
                {
                    errMsg = QStringLiteral("未找到用户头像内容ID");
                    return false;
                }
                ZG6000::StringMap fileContent;
                fileContent["id"] = listPhotoContentID.front();
                fileContent["content"] = ZGUtils::get(user, "photo");
                const auto& fileSize = fileContent["content"].size() * 4 / 3;
                if (fileSize >= static_cast<size_t>(200 * 1024))
                {
                    errMsg = QStringLiteral("用户'%1'的照片大小超过200KB").arg(userInfo["id"].c_str());
                    return false;
                }
                userInfo["photoHash"] = std::to_string(std::hash<std::string>()(fileContent["content"]));
                listSql.push_back(ZGUtils::generateUpdateSql("sp_param_file_content", fileContent));
            }
        }
        if (newUser)
            listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_user", userInfo));
        else
            listSql.push_back(ZGUtils::generateUpdateSql("sp_param_hrm_user", userInfo));
        QString sql = QString("delete from sp_param_hrm_user_role where userID = '%1'").arg(userInfo["id"].c_str());
        listSql.push_back(sql.toStdString());
        userRole["id"] = listUserRoleID[index++];
        userRole["userID"] = userInfo["id"];
        if (user.find("climblingLicense") == user.end() || (ZGUtils::get(user, "climblingLicense") != "true"))
            userRole["roleID"] = "ZG_HR_MAINTAINER";
        else
            userRole["roleID"] = "ZG_HR_OPERATOR";
        listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_user_role", userRole));
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("添加用户失败");
        return false;
    }
    return true;
}

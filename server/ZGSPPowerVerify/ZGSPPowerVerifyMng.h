#ifndef ZGSPPOWERVERIFYMNG_H
#define ZGSPPOWERVERIFYMNG_H

#include <QObject>
#include <QTimer>
#include "ZGServerCommon.h"

class ZGRedisClient;
class ZGMqttClient;

namespace ZG6000
{
    class ZGSPPowerVerifyMng : public QObject
    {
        Q_OBJECT

    public:
        // 单例
        static ZGSPPowerVerifyMng* instance();
        /** 
         * @brief   检查服务状态
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool checkState();

        /** 
         * @brief   判断用户是否具有指定权限
         *
         * @param           userID      用户ID
         * @param           powerID     权限ID
         * @param [in,out]  hasPower    是否具有权限
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool isUserHasPower(const std::string& userID,
                            const std::string& powerID,
                            bool& hasPower,
                            ErrorInfo& e);

        /** 
         * @brief   使用用户名密码登录
         *
         * @param           clientID    客户端ID
         * @param           userID      用户ID
         * @param           password    用户密码
         * @param           keepTime    保持时间
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool loginByPassword(const std::string& clientID,
                             const std::string& userID,
                             const std::string& password,
                             int keepTime,
                             ErrorInfo& e);

        /** 
         * @brief   使用用户名密码和验证码登录
         *
         * @param           clientID    客户端ID
         * @param           userID      用户ID
         * @param           password    用户密码
         * @param           verifyCode  验证码
         * @param           keepTime    保持时间
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool loginByPasswordAndVerifyCode(const std::string& clientID,
                                        const std::string& userID,
                                        const std::string& password,
                                        const std::string& verifyCode,
                                        int keepTime,
                                        ErrorInfo& e);

        /** 
         * @brief   使用卡登录
         *
         * @param           clientID    客户端ID
         * @param           userID      用户ID
         * @param           authModeID  授权模式ID
         * @param           cardID      用户卡ID
         * @param           keepTime    保持时间
         * @param realUserID
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool loginByCard(const std::string& clientID,
                         const std::string& userID,
                         const std::string& authModeID,
                         const std::string& cardID,
                         int keepTime,
                         std::string& realUserID,
                         ErrorInfo& e);

        /** 
         * @brief   使用认证设备登录
         *
         * @param           clientID    客户端ID
         * @param           userID      用户ID
         * @param           authModeID  授权模式ID
         * @param           keepTime    保持时间
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool loginByAuthDev(const std::string& clientID,
                            const std::string& userID,
                            const std::string& authModeID,
                            int keepTime,
                            ErrorInfo& e);

        /** 
         * @brief   用户注销
         *
         * @param           clientID    客户端ID
         * @param           userID      用户ID
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool logout(const std::string& clientID,
                    const std::string& userID,
                    ErrorInfo& e);

        /** 
         * @brief   根据指定的权限获取可用用户
         *
         * @param           clientID    客户端ID(如果为空，表示不限制客户端）
         * @param           appNodeID   应用节点ID(如果为空，表示不限制应用节点)
         * @param           powerID     权限ID(如果为空，表示不限制权限)
         * @param [in,out]  lstUser     可用用户列表
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，失败返回false。
         */
        bool getAvaiableUser(const std::string& clientID,
                             const std::string& appNodeID,
                             const std::string& powerID,
                             ListStringMap& lstUser,
                             ErrorInfo& e);

        /** 
         * @brief   使用用户名密码验证权限
         *
         * @param           clientID    客户端ID
         * @param           userID      用户ID
         * @param           password    用户密码
         * @param           appNodeID   应用节点ID
         * @param           powerID     权限ID
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，否则返回false。
         */
        bool verifyByPassword(const std::string& clientID,
                              const std::string& userID,
                              const std::string& password,
                              const std::string& appNodeID,
                              const std::string& powerID,
                              ErrorInfo& e);

        /** 
         * @brief   使用卡号验证权限
         *
         * @param           clientID    客户端ID
         * @param           userID      用户ID
         * @param           authModeID  授权模式ID
         * @param           cardID      卡ID
         * @param           appNodeID   应用节点ID
         * @param           powerID     权限ID
         * @param [in,out]  realUserID  实际用户ID
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，否则返回false。
         */
        bool verifyByCard(const std::string& clientID,
                          const std::string& userID,
                          const std::string& authModeID,
                          const std::string& cardID,
                          const std::string& appNodeID,
                          const std::string& powerID,
                          std::string& realUserID,
                          ErrorInfo& e);

        /** 
         * @brief   使用认证设备验证权限
         *
         * @param           clientID    客户端ID
         * @param           userID      用户ID
         * @param           authModeID  授权模式ID
         * @param           appNodeID   应用节点ID
         * @param           powerID     权限ID
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，否则返回false。
         */
        bool verifyByAuthDev(const std::string& clientID,
                             const std::string& userID,
                             const std::string& authModeID,
                             const std::string& appNodeID,
                             const std::string& powerID,
                             ErrorInfo& e);

        /** 
         * @brief   使用用户名密码和验证码验证权限
         *
         * @param           clientID    客户端ID
         * @param           userID      用户ID
         * @param           password    用户密码
         * @param           verifyCode  验证码
         * @param           appNodeID   应用节点ID
         * @param           powerID     权限ID
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，否则返回false。
         */
        bool verifyByPasswordAndVerifyCode(const std::string& clientID,
                                         const std::string& userID,
                                         const std::string& password,
                                         const std::string& verifyCode,
                                         const std::string& appNodeID,
                                         const std::string& powerID,
                                         ErrorInfo& e);

        /** 
         * @brief   发送验证码
         *
         * @param           userID      用户ID
         * @param [in,out]  e           执行出错时的错误信息
         *
         * @return  执行成功返回true，否则返回false。
         */
        bool sendVerifyCode(std::string userID,
                            ErrorInfo& e);

    public:
        void init();

    private:
        explicit ZGSPPowerVerifyMng(QObject* parent = nullptr);

        void initEvents();

        void initServerInstConfig();

        bool initServerInstInfo();

        bool initRedisClient();

        bool initMqttClient();

        bool initLanguage();

        bool initSystemParam();

        bool isClientExists(const std::string& clientID,
                            ErrorInfo& e);

        bool isClientValid(const std::string& clientID,
                           ErrorInfo& e);

        bool isClientOnline(const std::string& clientID,
                            ErrorInfo& e);

        bool isUserValid(const std::string& userID,
                         ErrorInfo& e);

        bool checkClientAuthState(const std::string& clientID,
                                  const std::string& authModeID,
                                  ErrorInfo& e);

        bool checkUserAuthState(const std::string& userID,
                                const std::string& authModeID,
                                ErrorInfo& e);

        bool checkClientLoginState(const std::string& clientID,
                                   const std::string& userID,
                                   ErrorInfo& e);

        bool checkUserAppNode(const std::string& userID,
                              const std::string& appNodeID,
                              ErrorInfo& e);

        bool doVerifyNormalLogin(const std::string& clientID,
                                 const std::string& userID,
                                 const std::string& authModeID,
                                 ErrorInfo& e,
                                 const std::function<bool()>& func);

        bool doVerifyNormalCheck(const std::string& clientID,
                                 const std::string& authModeID,
                                 ErrorInfo& e,
                                 const std::function<bool()>& func);

        bool doVerifyNormalCheck(const std::string& clientID,
                                 const std::string& userID,
                                 const std::string& authModeID,
                                 ErrorInfo& e,
                                 const std::function<bool()>& func);

        bool verifyUserPassword(const std::string& userID,
                                const std::string& password,
                                ErrorInfo& e);

        bool verifyUserVerifyCode(const std::string& userID,
                                   const std::string& verifyCode,
                                   ErrorInfo& e);

        bool verifyUserCard(const std::string& userID,
                            const std::string& cardID,
                            std::string& realUserID,
                            ErrorInfo& e);

        bool verifyUserPower(const std::string& userID,
                             const std::string& powerID,
                             ErrorInfo& e);

        /** 
         * @brief   发布用户事件
         *
         * @param           clientID    客户端ID
         * @param           eventInfo   事件信息
         * @param           eventInfoL2 事件信息2
         */
        void publishUserEvent(const std::string& clientID,
                              const std::string& eventInfo,
                              const std::string& eventInfoL2);

        bool addLogEvent(const QString& eventType,
                         const QString& userName,
                         const QString& logInfo,
                         bool isSuccess);

        bool logout(const std::string& clientID,
                    const std::string& userID,
                    bool publishEvent,
                    ErrorInfo& e);

        /**
         * @brief   发布客户端事件
         *
         * @param   clientID    客户端ID
         * @param   userID      用户ID
         * @param   userName    用户名
         * @param   type        事件类型
         */
        void publishClientEvent(const std::string& clientID,
                                const std::string& userID,
                                const std::string& userName,
                                const std::string& type);

        bool updateClientLogin(const std::string& clientID,
                               const std::string& userID,
                               const std::string& loginTime,
                               int keepTime,
                               ErrorInfo& e);

        void checkLoginStatus(const QDateTime& dateTime);

        void clearExpiredVerifyCode(const QDateTime& dateTime);

    private slots:
        void onCheckStatus();

    private:
        bool m_initialized{false};
        QString m_serverName{""};
        QString m_instName{""};
        QTimer m_initTimer;
        QTimer m_checkTimer;
        int m_initInterval{10};
        int m_checkInterval{10};
        int m_checkCount{0};
        int m_maxLoginError{5};
        int m_maxHashIterations{10000};
        ZGRedisClient* m_redisRtQueue{nullptr};
        ZGMqttClient* m_mqttMessage{nullptr};
        std::string m_firstLanguage;
        std::string m_secondLanguage;
        bool m_enableSafety{false};
    };

    inline static ZGSPPowerVerifyMng* g_pInstance = nullptr;
} // namespace ZG6000

#endif // ZGSPPOWERVERIFYMNG_H

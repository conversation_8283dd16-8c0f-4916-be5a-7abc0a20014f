#ifndef ZGSPPOWERVERIFYI_H
#define ZGSPPOWERVERIFYI_H

#include "ZGSPPowerVerify.h"

namespace ZG6000
{
    class ZGSPPowerVerifyI : public ZGSPPowerVerify
    {
    public:
        ZGSPPowerVerifyI();

        // ZGServerBase interface
    public:
        /**
         * 检查服务状态
         * @param current 当前会话
         * @return 执行成功返回true，否则返回false
         */
        bool checkState(const Ice::Current& current) override;

        // ZGSPPowerVerify interface
    public:
        /**
         * 判断用户是否具有指定权限
         * @param userID 用户ID
         * @param powerID 权限ID
         * @param hasPower 是否具有权限
         * @param e 错误信息
         * @param current 当前会话
         * @return 执行成功返回true，否则返回false
         */
        bool isUserHasPower(std::string userID,
                            std::string powerID,
                            bool& hasPower,
                            ErrorInfo& e,
                            const Ice::Current& current) override;

        /**
         * 使用用户名密码登录
         * @param clientID 客户端ID
         * @param userID 用户ID
         * @param password 用户密码
         * @param keepTime 保持时间 
         * @param e 错误信息
         * @param current 当前会话
         * @return 执行成功返回true，否则返回false
         */
        bool loginByPassword(std::string clientID,
                             std::string userID,
                             std::string password,
                             int keepTime,
                             ErrorInfo& e,
                             const Ice::Current& current) override;

        bool loginByPasswordAndVerifyCode(std::string clientID,
                                          std::string userID,
                                          std::string password,
                                          std::string verifyCode,
                                          int keepTime,
                                          ErrorInfo& e,
                                          const Ice::Current& current) override;

        /**
         * 使用卡登录
         * @param clientID 客户端ID
         * @param userID 用户ID
         * @param authModeID 授权模式ID
         * @param cardID 用户卡ID
         * @param keepTime 保持时间
         * @param realUserID
         * @param e 错误信息
         * @param current 当前会话
         * @return 执行成功返回true，否则返回false
         */
        bool loginByCard(std::string clientID,
                         std::string userID,
                         std::string authModeID,
                         std::string cardID,
                         int keepTime,
                         std::string& realUserID,
                         ErrorInfo& e,
                         const Ice::Current& current) override;

        /**
         * 使用授权设备登录
         * @param clientID 客户端ID
         * @param userID 用户ID
         * @param authModeID 授权模式ID
         * @param keepTime 保持时间
         * @param e 错误信息
         * @param current 当前会话
         * @return 执行成功返回true，否则返回false
         */
        bool loginByAuthDev(std::string clientID,
                            std::string userID,
                            std::string authModeID,
                            int keepTime,
                            ErrorInfo& e,
                            const Ice::Current& current) override;

        /** 
         * 用户注销
         * @param clientID 客户端ID
         * @param userID 用户ID
         * @param e 错误信息
         * @param current 当前会话
         * @return 执行成功返回true，否则返回false
         */
        bool logout(std::string clientID,
                    std::string userID,
                    ErrorInfo& e,
                    const Ice::Current& current) override;

        /** 
         * 根据指定的权限获取可用用户
         * @param clientID 客户端ID
         * @param appNodeID 应用节点ID
         * @param powerID 权限ID
         * @param lstUser 用户列表
         * @param e 错误信息
         * @param current 当前会话
         * @return 执行成功返回true，否则返回false
         */
        bool getAvaiableUser(std::string clientID,
                             std::string appNodeID,
                             std::string powerID,
                             ListStringMap& lstUser,
                             ErrorInfo& e,
                             const Ice::Current& current) override;

        /** 
         * 使用用户名密码验证权限
         * @param clientID 客户端ID
         * @param userID 用户ID
         * @param password 用户密码
         * @param appNodeID 应用节点ID
         * @param powerID 权限ID
         * @param e 错误信息
         * @param current 当前会话
         * @return 执行成功返回true，否则返回false
         */
        bool verifyByPassword(std::string clientID,
                              std::string userID,
                              std::string password,
                              std::string appNodeID,
                              std::string powerID,
                              ErrorInfo& e,
                              const Ice::Current& current) override;

        /** 
         * 使用卡验证权限
         * @param clientID 客户端ID
         * @param userID 用户ID
         * @param authModeID 授权模式ID
         * @param cardID 用户卡ID
         * @param appNodeID 应用节点ID
         * @param powerID 权限ID
         * @param realUserID 真实用户ID
         * @param e 错误信息
         * @param current 当前会话
         * @return 执行成功返回true，否则返回false
         */
        bool verifyByCard(std::string clientID,
                          std::string userID,
                          std::string authModeID,
                          std::string cardID,
                          std::string appNodeID,
                          std::string powerID,
                          std::string& realUserID,
                          ErrorInfo& e,
                          const Ice::Current& current) override;

        /** 
         * 使用授权设备验证权限
         * @param clientID 客户端ID
         * @param userID 用户ID
         * @param authModeID 授权模式ID
         * @param appNodeID 应用节点ID
         * @param powerID 权限ID
         * @param e 错误信息
         * @param current 当前会话
         * @return 执行成功返回true，否则返回false
         */
        bool verifyByAuthDev(std::string clientID,
                             std::string userID,
                             std::string authModeID,
                             std::string appNodeID,
                             std::string powerID,
                             ErrorInfo& e,
                             const Ice::Current& current) override;

        bool verifyByPasswordAndVerifyCode(std::string clientID,
                                           std::string userID,
                                           std::string password,
                                           std::string verifyCode,
                                           std::string appNodeID,
                                           std::string powerID,
                                           ErrorInfo& e,
                                           const Ice::Current& current) override;

        bool sendVerifyCode(std::string userID,
                            ErrorInfo& e,
                            const Ice::Current& current) override;
    };
} // namespace ZG6000

#endif // ZGSPPOWERVERIFYI_H

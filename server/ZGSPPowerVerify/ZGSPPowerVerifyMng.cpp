// Copyright (c) 2025 <PERSON><PERSON>ong ELECTRICAL Engineering Co. LTD. All rights reserved.
// Author: <PERSON>
// Date: 2025.1.20
// Description: ZGSPPowerVerifyI接口类实现
// Version: 1.0

#include "ZGSPPowerVerifyMng.h"

#include <QRandomGenerator>

#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGProxyCommon.h"
#include "ZGJson.h"
#include "ZGUtils.h"
#include "ZGSecure.h"
#include "zgerror/ZGSPPowerVerifyError.h"

namespace ZG6000
{
    ZGSPPowerVerifyMng::ZGSPPowerVerifyMng(QObject* parent)
        : QObject{parent}
    {
    }

    ZGSPPowerVerifyMng* ZGSPPowerVerifyMng::instance()
    {
        if (g_pInstance == nullptr)
        {
            g_pInstance = new ZGSPPowerVerifyMng();
        }
        return g_pInstance;
    }

    bool ZGSPPowerVerifyMng::checkState()
    {
        return m_initialized;
    }

    bool ZGSPPowerVerifyMng::isUserHasPower(const std::string& userID,
                                            const std::string& powerID,
                                            bool& hasPower,
                                            ErrorInfo& e)
    {
        if (!isUserValid(userID, e))
            return false;
        QString sql = QString("SELECT powerID FROM sp_param_hrm_role_power WHERE roleID in "
            "(SELECT roleID FROM sp_param_hrm_user_role WHERE userID = '%1')").arg(userID.c_str());
        StringList listPowerID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listPowerID))
        {
            ZGLOG_ERROR(QStringLiteral("获取用户权限失败"));
            return false;
        }
        hasPower = std::find(listPowerID.begin(), listPowerID.end(), powerID) != listPowerID.end();
        return true;
    }

    bool ZGSPPowerVerifyMng::loginByPassword(const std::string& clientID,
                                             const std::string& userID,
                                             const std::string& password,
                                             int keepTime,
                                             ErrorInfo& e)
    {
        return doVerifyNormalLogin(clientID, userID, "ZG_AM_PASSWORD", e, [&]()-> bool
        {
            QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
            if (!verifyUserPassword(userID, password, e))
            {
                ZGLOG_ERROR(QStringLiteral("登录用户'%1'密码验证失败").arg(userID.c_str()));
                return false;
            }
            ZGLOG_INFO(QStringLiteral("登录用户'%1'密码验证成功").arg(userID.c_str()));
            return updateClientLogin(clientID, userID, currentTime.toStdString(), keepTime, e);
        });
    }

    bool ZGSPPowerVerifyMng::loginByPasswordAndVerifyCode(const std::string& clientID,
                                                          const std::string& userID,
                                                          const std::string& password,
                                                          const std::string& verifyCode,
                                                          int keepTime,
                                                          ErrorInfo& e)
    {
        return doVerifyNormalLogin(clientID, userID, "ZG_AM_PASSWORD", e, [&]()-> bool
        {
            QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
            if (!verifyUserPassword(userID, password, e))
            {
                ZGLOG_ERROR(QStringLiteral("登录用户'%1'密码验证失败").arg(userID.c_str()));
                return false;
            }
            if (!verifyUserVerifyCode(userID, verifyCode, e))
            {
                ZGLOG_ERROR(QStringLiteral("登录用户'%1'验证码验证失败").arg(userID.c_str()));
                return false;
            }
            ZGLOG_INFO(QStringLiteral("登录用户'%1'密码和验证码验证成功").arg(userID.c_str()));
            return updateClientLogin(clientID, userID, currentTime.toStdString(), keepTime, e);
        });
    }

    bool ZGSPPowerVerifyMng::loginByCard(const std::string& clientID,
                                         const std::string& userID,
                                         const std::string& authModeID,
                                         const std::string& cardID,
                                         int keepTime,
                                         std::string& realUserID,
                                         ErrorInfo& e)
    {
        if (authModeID != "ZG_AM_USB_CARD" && authModeID != "ZG_AM_COMM_CARD")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("无效的授权卡类型'%1'").arg(authModeID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return doVerifyNormalLogin(clientID, userID, authModeID, e, [&]()-> bool
        {
            QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
            if (!verifyUserCard(userID, cardID, realUserID, e))
                return false;
            return updateClientLogin(clientID, realUserID, currentTime.toStdString(), keepTime, e);
        });
    }

    bool ZGSPPowerVerifyMng::loginByAuthDev(const std::string& clientID,
                                            const std::string& userID,
                                            const std::string& authModeID,
                                            int keepTime,
                                            ErrorInfo& e)
    {
        return doVerifyNormalLogin(clientID, userID, authModeID, e, [&]()-> bool
        {
            QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
            return updateClientLogin(clientID, userID, currentTime.toStdString(), keepTime, e);
        });
    }

    bool ZGSPPowerVerifyMng::logout(const std::string& clientID,
                                    const std::string& userID,
                                    ErrorInfo& e)
    {
        return logout(clientID, userID, true, e);
    }

    bool ZGSPPowerVerifyMng::getAvaiableUser(const std::string& clientID,
                                             const std::string& appNodeID,
                                             const std::string& powerID,
                                             ListStringMap& lstUser,
                                             ErrorInfo& e)
    {
        QString sql;
        if (appNodeID.empty() && powerID.empty())
        {
            sql = QString("SELECT id FROM sp_param_hrm_user");
        }
        else
        {
            sql = QString("SELECT a.id FROM sp_param_hrm_user a ");
            if (!powerID.empty())
                sql += QString("LEFT JOIN sp_param_hrm_user_role b ON a.id = b.userID "
                    "LEFT JOIN sp_param_hrm_role_power c ON b.roleID = c.roleID ");
            if (!appNodeID.empty())
                sql += QString("LEFT JOIN sp_param_hrm_user_appnode d ON a.id = d.userID ");
            sql += "WHERE ";
            ZG6000::StringList listCondition;
            if (!powerID.empty())
                listCondition.push_back(QString("c.powerID = '%1'").arg(powerID.c_str()).toStdString());
            if (!appNodeID.empty())
                listCondition.push_back(QString("d.appnodeID = '%1'").arg(appNodeID.c_str()).toStdString());
            const auto& conditions = ZGUtils::join(listCondition, " AND ");
            sql += conditions.c_str() + QString(" ORDER BY a.id");
        }
        ZG6000::StringList listUserID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取用户ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listUserID.empty())
        {
            return true;
        }
        const auto& userIDs = ZGUtils::join(listUserID, ",", "'", "'");
        sql = QString("SELECT a.id, a.name, a.organID, b.name AS organName FROM sp_param_hrm_user a "
            "LEFT JOIN sp_param_hrm_organ b ON a.organID = b.id "
            "WHERE a.id IN (%1) ORDER BY a.id").arg(userIDs.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), lstUser))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取用户信息失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::verifyByPassword(const std::string& clientID,
                                              const std::string& userID,
                                              const std::string& password,
                                              const std::string& appNodeID,
                                              const std::string& powerID,
                                              ErrorInfo& e)
    {
        return doVerifyNormalCheck(clientID, userID, "ZG_AM_PASSWORD", e, [&]()-> bool
        {
            if (!verifyUserPassword(userID, password, e))
            {
                ZGLOG_ERROR(QStringLiteral("用户'%1'密码验证失败").arg(userID.c_str()));
                return false;
            }
            if (!appNodeID.empty())
            {
                if (!checkUserAppNode(userID, appNodeID, e))
                {
                    return false;
                }
            }
            if (!powerID.empty())
            {
                if (!verifyUserPower(userID, powerID, e))
                {
                    return false;
                }
            }
            ZGLOG_INFO(QStringLiteral("用户'%1'密码验证成功").arg(userID.c_str()));
            return true;
        });
    }

    bool ZGSPPowerVerifyMng::verifyByCard(const std::string& clientID,
                                          const std::string& userID,
                                          const std::string& authModeID,
                                          const std::string& cardID,
                                          const std::string& appNodeID,
                                          const std::string& powerID,
                                          std::string& realUserID,
                                          ErrorInfo& e)
    {
        if (authModeID != "ZG_AM_USB_CARD" && authModeID != "ZG_AM_COMM_CARD")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("无效的授权卡类型'%1'").arg(authModeID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return doVerifyNormalCheck(clientID, authModeID, e, [&]()-> bool
        {
            if (!verifyUserCard(userID, cardID, realUserID, e))
            {
                return false;
            }
            if (!appNodeID.empty())
            {
                if (!checkUserAppNode(realUserID, appNodeID, e))
                {
                    return false;
                }
            }
            if (!powerID.empty())
            {
                if (!verifyUserPower(realUserID, powerID, e))
                {
                    return false;
                }
            }
            return true;
        });
    }

    bool ZGSPPowerVerifyMng::verifyByAuthDev(const std::string& clientID,
                                             const std::string& userID,
                                             const std::string& authModeID,
                                             const std::string& appNodeID,
                                             const std::string& powerID,
                                             ErrorInfo& e)
    {
        return doVerifyNormalCheck(clientID, userID, authModeID, e, [&]()-> bool
        {
            if (!appNodeID.empty())
            {
                if (!checkUserAppNode(userID, appNodeID, e))
                {
                    return false;
                }
            }
            if (!powerID.empty())
            {
                if (!verifyUserPower(userID, powerID, e))
                {
                    return false;
                }
            }
            return true;
        });
    }

    bool ZGSPPowerVerifyMng::verifyByPasswordAndVerifyCode(const std::string& clientID,
                                                           const std::string& userID,
                                                           const std::string& password,
                                                           const std::string& verifyCode,
                                                           const std::string& appNodeID,
                                                           const std::string& powerID,
                                                           ErrorInfo& e)
    {
        return doVerifyNormalCheck(clientID, userID, "ZG_AM_PASSWORD", e, [&]()-> bool
        {
            if (!verifyUserPassword(userID, password, e))
            {
                ZGLOG_ERROR(QStringLiteral("用户'%1'密码验证失败").arg(userID.c_str()));
                return false;
            }
            if (!verifyUserVerifyCode(userID, verifyCode, e))
            {
                ZGLOG_ERROR(QStringLiteral("用户'%1'验证码验证失败").arg(userID.c_str()));
                return false;
            }
            if (!appNodeID.empty())
            {
                if (!checkUserAppNode(userID, appNodeID, e))
                {
                    return false;
                }
            }
            if (!powerID.empty())
            {
                if (!verifyUserPower(userID, powerID, e))
                {
                    return false;
                }
            }
            ZGLOG_INFO(QStringLiteral("用户'%1'密码验证成功").arg(userID.c_str()));
            return true;
        });
    }

    bool ZGSPPowerVerifyMng::sendVerifyCode(std::string userID,
                                            ErrorInfo& e)
    {
        // 生成4位随机验证码
        QString verifyCode = QString::number(QRandomGenerator::global()->bounded(100000, 999999));
        ZGLOG_TRACE(QString("verifyCode: %1").arg(verifyCode));
        // 从用户表中获取盐值
        QString sql = QString("SELECT mobileNumber, salt FROM sp_param_hrm_user WHERE id = '%1'").arg(userID.c_str());
        ListStringMap listUserInfo;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listUserInfo))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取用户盐值失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listUserInfo.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = QStringLiteral("用户'%1'不存在").arg(userID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 计算验证码的哈希值
        auto userInfo = listUserInfo.front();
        QByteArray saltArray = userInfo["salt"].c_str();
        QString errMsg;
        QString hashValue;
        if (!ZGSecure::pbkdf2HMACSHA256(verifyCode, saltArray, m_maxHashIterations, hashValue, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("计算验证码哈希值失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 保存验证码和验证时间到数据库
        QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
        StringMap userVerify{{"id", userID},
                             {"rtVerifyCode", hashValue.toStdString()},
                             {"rtVerifyTime", currentTime.toStdString()}};
        sql = ZGUtils::generateUpdateSql("sp_param_hrm_user", userVerify).c_str();
        ZGLOG_TRACE(sql);
        if (!ZGProxyCommon::execSql(sql.toStdString()))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = QStringLiteral("保存验证码失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& phoneNumber = userInfo["mobileNumber"];
        if (phoneNumber.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("用户'%1'没有绑定手机号码").arg(userID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 发送验证码到用户手机
        ZG6000::StringMap shortMessage;
        shortMessage["content"] = QString("本次登录验证码为：%1（5分钟内有效）").arg(verifyCode).toStdString();
        shortMessage["phoneNumber"] = phoneNumber;
        QDateTime dt = QDateTime::currentDateTime();
        shortMessage["timestamp"] = dt.toString("yyyy-MM-dd hh:mm:ss").toStdString();
        const auto& json = ZGJson::convertToJson(shortMessage);
        long long size;
        std::string errorString;
        if (!m_redisRtQueue->rpush("ZG_Q_SHORT_MESSAGE", json, size, errorString))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_INTERNAL);
            e.errDetail = errorString;
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    void ZGSPPowerVerifyMng::init()
    {
        initEvents();
        initServerInstConfig();
        while (!initServerInstInfo())
        {
            ZGLOG_ERROR("initServerInstInfo error.");
            QThread::msleep(m_initInterval * 1000);
        }
        QThread::sleep(QRandomGenerator::global()->bounded(5, 10));
        while (!initRedisClient())
        {
            ZGLOG_ERROR("initRedisClient error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initMqttClient())
        {
            ZGLOG_ERROR("initMqttClient error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initLanguage())
        {
            ZGLOG_ERROR("initLanguage error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initSystemParam())
        {
            ZGLOG_ERROR("initSystemParam error.");
            QThread::msleep(m_initInterval * 1000);
        }
        m_initialized = true;
        ZGLOG_INFO(QString("init ZGSPPowerVerify finished."));
        m_checkTimer.start(m_checkInterval * 1000);
    }

    void ZGSPPowerVerifyMng::initEvents()
    {
        connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPPowerVerifyMng::onCheckStatus);
    }

    void ZGSPPowerVerifyMng::initServerInstConfig()
    {
        const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
        QString errMsg;
        int value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        {
            ZGLOG_WARN(errMsg);
        }
        else
        {
            m_initInterval = value;
        }
        if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        {
            ZGLOG_WARN(errMsg);
        }
        else
        {
            m_checkInterval = value;
        }
        if (!ZGUtils::getMapKeyValue(serverGroup, "max_login_error", value, 0, 5, errMsg))
        {
            ZGLOG_WARN(errMsg);
        }
        else
        {
            m_maxLoginError = value;
        }
    }

    bool ZGSPPowerVerifyMng::initServerInstInfo()
    {
        m_serverName = ZGRuntime::instance()->getServerID();
        if (m_serverName.isEmpty())
        {
            ZGLOG_ERROR("Empty server id.");
            return false;
        }
        m_instName = ZGRuntime::instance()->getInstanceID();
        if (m_instName.isEmpty())
        {
            ZGLOG_ERROR("Empty server instance id.");
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::initRedisClient()
    {
        QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
        listClientType << ZGRuntime::REDIS_RT_QUEUE;
        if (!ZGRuntime::instance()->initRedisClient(listClientType))
        {
            ZGLOG_ERROR("initRedisClient error.");
            return false;
        }
        m_redisRtQueue = ZGRuntime::instance()->getRedisClientRTQueue();
        if (m_redisRtQueue == nullptr)
        {
            ZGLOG_ERROR("getRedisClientRTQueue error.");
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::initMqttClient()
    {
        if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
        {
            ZGLOG_ERROR("initMqttClient error.");
            return false;
        }
        m_mqttMessage = ZGRuntime::instance()->getMqttClientMessage();
        if (m_mqttMessage == nullptr)
        {
            ZGLOG_ERROR("getMqttClientMessage error.");
            return false;
        }
        m_mqttMessage->connectToHost();
        return true;
    }

    bool ZGSPPowerVerifyMng::initLanguage()
    {
        QString sql = QString("SELECT firstLanguageID, secondLanguageID FROM sp_param_system");
        StringMap mapLanguage;
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), mapLanguage))
        {
            ZGLOG_ERROR("获取系统语言参数失败");
            return false;
        }
        m_firstLanguage = mapLanguage["firstLanguageID"];
        if (m_firstLanguage.empty())
        {
            m_firstLanguage = "ZG_DL_CN";
        }
        m_secondLanguage = mapLanguage["secondLanguageID"];
        if (!ZGUtils::initLanguage(m_serverName))
        {
            ZGLOG_ERROR(QStringLiteral("初始化语言配置失败"));
            return false;
        }
        ZGLOG_INFO(
            QString("first language: %1, second language: %2").arg(m_firstLanguage.c_str()).arg(m_secondLanguage.c_str()
            ));
        return true;
    }

    bool ZGSPPowerVerifyMng::initSystemParam()
    {
        QString sql = QString("SELECT isEnableSafety FROM sp_param_system");
        StringList listResult;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listResult))
        {
            ZGLOG_ERROR(QStringLiteral("获取系统参数失败"));
            return false;
        }
        if (listResult.empty())
        {
            ZGLOG_ERROR(QStringLiteral("系统参数不存在"));
            return false;
        }
        m_enableSafety = (listResult.front() == "1");
        return true;
    }

    bool ZGSPPowerVerifyMng::isClientExists(const std::string& clientID,
                                            ZG6000::ErrorInfo& e)
    {
        if (clientID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_CLIENT);
            e.errDetail = u8"客户端ID未指定";
            ZGLOG_ERROR(e);
            return false;
        }
        std::string sql = "SELECT id FROM sp_param_client WHERE id = '" + clientID + "'";
        StringList listID;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = u8"获取客户端'" + clientID + u8"'信息失败";
            ZGLOG_ERROR(e);
            return false;
        }
        if (listID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_RT);
            e.errDetail = u8"指定的客户端'" + clientID + u8"'不存在";
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::isClientValid(const std::string& clientID,
                                           ErrorInfo& e)
    {
        std::string clientActive;
        if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtIsActivate", clientActive))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_RT);
            e.errDetail = u8"获取客户端'" + clientID + u8"'激活信息失败";
            ZGLOG_ERROR(e);
            return false;
        }
        // 客户端未激活
        if (clientActive != "1")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_CLIENT);
            e.errDetail = u8"客户端" + clientID + u8"未激活";
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::isClientOnline(const std::string& clientID,
                                            ErrorInfo& e)
    {
        std::string clientOnline;
        if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtState", clientOnline))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_RT);
            e.errDetail = u8"获取客户端'" + clientID + u8"'在线信息失败";
            ZGLOG_ERROR(e);
            return false;
        }
        if (clientOnline != "2")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_CLIENT);
            e.errDetail = u8"客户端" + clientID + u8"不在线";
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::isUserValid(const std::string& userID,
                                         ErrorInfo& e)
    {
        if (userID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_USER);
            e.errDetail = u8"用户ID未指定";
            ZGLOG_ERROR(e);
            return false;
        }
        if (userID == "root")
            return true;
        std::string sql = "SELECT id FROM sp_param_hrm_user WHERE id = '" + userID + "'";
        StringList listID;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = u8"获取用户信息失败";
            ZGLOG_ERROR(e);
            return false;
        }
        if (listID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_USER);
            e.errDetail = u8"指定的用户ID'" + userID + u8"'不存在";
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::checkClientAuthState(const std::string& clientID,
                                                  const std::string& authModeID,
                                                  ErrorInfo& e)
    {
        // 获取客户端授权模式
        std::string sql = "SELECT authModeID FROM sp_param_client_auth WHERE clientID = '" + clientID + "'";
        StringList listResult;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = u8"获取客户端" + clientID + u8"授权模式失败";
            ZGLOG_ERROR(e);
            return false;
        }
        auto it = std::find(listResult.begin(), listResult.end(), authModeID);
        // 客户端不具备相关的授权模式
        if (it == listResult.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_CLIENT);
            e.errDetail = u8"客户端" + clientID + u8"不具备相关的授权模式" + authModeID;
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::checkUserAuthState(const std::string& userID,
                                                const std::string& authModeID,
                                                ErrorInfo& e)
    {
        if (userID == "root")
            return true;
        // 获取用户授权模式
        std::string sql = "SELECT authModeID FROM sp_param_hrm_user_auth WHERE userID = '" + userID + "'";
        StringList listResult;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = u8"获取用户授权模式失败";
            ZGLOG_ERROR(e);
            return false;
        }
        auto it = std::find(listResult.begin(), listResult.end(), authModeID);
        // 用户不具备相关的授权模式
        if (it == listResult.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_USER);
            e.errDetail = QStringLiteral("用户'%1'不具备相关的授权模式'%2'").arg(userID.c_str()).arg(authModeID.c_str()).
                                                                 toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::checkClientLoginState(const std::string& clientID,
                                                   const std::string& userID,
                                                   ErrorInfo& e)
    {
        std::string loginUserID;
        if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtLoginUserID", loginUserID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_RT);
            e.errDetail = u8"获取客户端'" + clientID + u8"登录用户失败";
            ZGLOG_ERROR(e);
            return false;
        }
        if (!loginUserID.empty() && (userID != loginUserID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_CLIENT);
            e.errDetail = u8"客户端'" + clientID + u8"'已经存在登录的用户'" + loginUserID + "'";
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::checkUserAppNode(const std::string& userID,
                                              const std::string& appNodeID,
                                              ErrorInfo& e)
    {
        QString sql = QString("SELECT appnodeID FROM sp_param_hrm_user_appnode WHERE userID = '%1' "
            "AND appnodeID = '%2'").arg(userID.c_str()).arg(appNodeID.c_str());
        StringList listResult;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listResult))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = u8"获取用户应用节点错误";
            ZGLOG_ERROR(e);
            return false;
        }
        if (listResult.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            std::string appNodeName;
            ZGProxyCommon::getDataByField("sp_param_appnode", appNodeID, "name", appNodeName);
            e.errDetail = QStringLiteral("用户未包含指定的节点'%2'").arg(appNodeName.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::doVerifyNormalLogin(const std::string& clientID,
                                                 const std::string& userID,
                                                 const std::string& authModeID,
                                                 ErrorInfo& e,
                                                 const std::function<bool()>& func)
    {
        // 客户端是否存在
        if (!isClientExists(clientID, e))
        {
            return false;
        }
        // 客户端是否激活
        if (!isClientValid(clientID, e))
        {
            return false;
        }
        // 用户是否存在
        if (!isUserValid(userID, e))
        {
            return false;
        }
        // 检查客户端授权模式
        if (!checkClientAuthState(clientID, authModeID, e))
        {
            return false;
        }
        // 检查用户授权模式
        if (!checkUserAuthState(userID, authModeID, e))
        {
            return false;
        }
        // 客户端是否在线
        if (!checkClientLoginState(clientID, userID, e))
        {
            return false;
        }
        // 执行回调
        if (!func())
        {
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::doVerifyNormalCheck(const std::string& clientID,
                                                 const std::string& authModeID,
                                                 ErrorInfo& e,
                                                 const std::function<bool()>& func)
    {
        // 客户端是否存在
        if (!isClientExists(clientID, e))
        {
            return false;
        }
        // 客户端是否激活
        if (!isClientValid(clientID, e))
        {
            return false;
        }
        // 客户端是否在线
        if (!isClientOnline(clientID, e))
        {
            return false;
        }
        if (!checkClientAuthState(clientID, authModeID, e))
        {
            return false;
        }
        if (!func())
        {
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::doVerifyNormalCheck(const std::string& clientID,
                                                 const std::string& userID,
                                                 const std::string& authModeID,
                                                 ErrorInfo& e,
                                                 const std::function<bool()>& func)
    {
        if (!clientID.empty())
        {
            // 客户端是否存在
            if (!isClientExists(clientID, e))
            {
                return false;
            }
            // 客户端是否激活
            if (!isClientValid(clientID, e))
            {
                return false;
            }
            // 客户端是否在线
            if (!isClientOnline(clientID, e))
            {
                return false;
            }
            if (!checkClientAuthState(clientID, authModeID, e))
            {
                return false;
            }
        }
        if (!userID.empty())
        {
            if (!isUserValid(userID, e))
            {
                return false;
            }
            if (!checkUserAuthState(userID, authModeID, e))
            {
                return false;
            }
        }
        if (!func())
        {
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::verifyUserPassword(const std::string& userID,
                                                const std::string& password,
                                                ErrorInfo& e)
    {
        // 验证内置超级管理员密码
        if ((userID == "root") && (password == "cdzg-admin028"))
        {
            ZGLOG_INFO(QStringLiteral("内置超级管理员登录成功"));
            return true;
        }
        ListStringMap listUserInfo;
        QString sql = QString("SELECT password, salt, rtErrorNum FROM sp_param_hrm_user WHERE id = '%1'").arg(
            userID.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listUserInfo))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取用户'%1'密码信息失败").arg(userID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listUserInfo.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_USER);
            e.errDetail = QStringLiteral("用户'%1'不存在").arg(userID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        auto userInfo = listUserInfo[0];
        QString salt = QString::fromStdString(userInfo["salt"]);
        QByteArray byteSalt = salt.toLatin1();
        QString dbPassword = QString::fromStdString(userInfo["password"]);
        int errorNum = std::stoi(userInfo["rtErrorNum"]);
        if (m_enableSafety)
        {
            // 如果密码错误次数超过限制，则锁定用户
            if (errorNum >= m_maxLoginError)
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_VERIFY);
                e.errDetail = QStringLiteral("用户'%1'账户已锁定，请系统管理员重置密码解锁").arg(userID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        QByteArray dbEncodePassword = dbPassword.toLatin1();
        QString hashedInputPassword;
        QString errMsg;
        if (!ZGSecure::pbkdf2HMACSHA256(password.c_str(), byteSalt, m_maxHashIterations, hashedInputPassword, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("密码加密失败: %1").arg(errMsg).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (hashedInputPassword != dbEncodePassword)
        {
            if (m_enableSafety)
            {
                // 密码错误次数加1
                sql = QString("UPDATE sp_param_hrm_user SET rtErrorNum = rtErrorNum + 1 WHERE id = '%1'").arg(
                    userID.c_str());
                if (!ZGProxyCommon::execSql(sql.toStdString()))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
                    e.errDetail = QStringLiteral("更新用户'%1'密码错误次数失败").arg(userID.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_VERIFY);
                std::string userName;
                ZGProxyCommon::getDataByField("sp_param_hrm_user", userID, "name", userName);
                if (errorNum + 1 >= m_maxLoginError)
                    e.errDetail = QStringLiteral("用户'%1'密码错误次数已达上限，账户已锁定").arg(userName.c_str()).toStdString();
                else
                    e.errDetail = QStringLiteral("用户'%1'密码验证失败").arg(userName.c_str()).toStdString();
                ZGLOG_ERROR(e);
                // 生成日志事件
                std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") +
                                        u8"【" + userName + u8"】" + ZGUtils::languageString(m_firstLanguage, "passwordError");
                addLogEvent("ZG_ET_SYSTEM", userName.c_str(), eventInfo.c_str(), false);
                // 取出用户密码错误次数，如果超过限制，生成账户已锁定日志事件
                if (errorNum + 1 >= m_maxLoginError)
                {
                    eventInfo = ZGUtils::languageString(m_firstLanguage, "user") +
                                u8"【" + userName + u8"】" + ZGUtils::languageString(m_firstLanguage, "accountLocked");
                    addLogEvent("ZG_ET_SYSTEM", userName.c_str(), eventInfo.c_str(), false);
                }
            }
            else
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_VERIFY);
                std::string userName;
                ZGProxyCommon::getDataByField("sp_param_hrm_user", userID, "name", userName);
                e.errDetail = QStringLiteral("用户'%1'密码验证失败").arg(userName.c_str()).toStdString();
            }
            return false;
        }
        // 密码验证成功，清除错误次数
        if (m_enableSafety)
        {
            sql = QString("UPDATE sp_param_hrm_user SET rtErrorNum = 0 WHERE id = '%1'").arg(userID.c_str());
            if (!ZGProxyCommon::execSql(sql.toStdString()))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
                std::string userName;
                ZGProxyCommon::getDataByField("sp_param_hrm_user", userID, "name", userName);
                e.errDetail = QStringLiteral("清除用户'%1'密码错误次数失败").arg(userID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::verifyUserVerifyCode(const std::string& userID,
                                                  const std::string& verifyCode,
                                                  ErrorInfo& e)
    {
        // 从用户表中获取验证码和验证时间
        ZGLOG_TRACE(QString("verify user verify code, userID: %1, verifyCode: %2").arg(userID.c_str()).arg(verifyCode.c_str()));
        QString sql = QString("SELECT rtVerifyCode, rtVerifyTime, salt FROM sp_param_hrm_user WHERE id = '%1'").arg(
            userID.c_str());
        ListStringMap listUserInfo;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listUserInfo))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取用户'%1'验证码信息失败").arg(userID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listUserInfo.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_USER);
            e.errDetail = QStringLiteral("用户'%1'不存在").arg(userID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        auto userInfo = listUserInfo.front();
        QString dbVerifyCode = QString::fromStdString(userInfo["rtVerifyCode"]);
        QString dbVerifyTime = QString::fromStdString(userInfo["rtVerifyTime"]);
        QString salt = QString::fromStdString(userInfo["salt"]);
        ZGLOG_TRACE(QString("dbVerifyCode: %1, dbVerifyCode: %2, salt: %3").arg(dbVerifyCode).arg(dbVerifyTime).arg(salt));
        if (dbVerifyCode.isEmpty() || dbVerifyTime.isEmpty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_VERIFY);
            e.errDetail = QStringLiteral("用户'%1'验证码信息不存在").arg(userID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 如果验证码的更新时间距当前时间超过5分钟，则验证码失效
        QDateTime verifyTime;
        if (!ZGUtils::StringToDateTime(dbVerifyTime, verifyTime, true))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("验证码时间转换失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QDateTime currentTime = QDateTime::currentDateTime();
        if (verifyTime.secsTo(currentTime) > 300)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_VERIFY);
            e.errDetail = QStringLiteral("验证码已过期，请重新发送验证码").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        // 对输入的验证码进行pbkdf2加密后与数据库中的验证码进行比较
        QString hashedInputVerifyCode;
        QString errMsg;
        if (!ZGSecure::pbkdf2HMACSHA256(verifyCode.c_str(), salt.toLatin1(), m_maxHashIterations,
            hashedInputVerifyCode, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("验证码加密失败: %1").arg(errMsg).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (hashedInputVerifyCode != dbVerifyCode)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_VERIFY);
            e.errDetail = QStringLiteral("验证码验证失败").toStdString();
            ZGLOG_ERROR(e);
            std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") +
                u8"【" + userID + u8"】" + ZGUtils::languageString(m_firstLanguage, "verifyCodeError");
            addLogEvent("ZG_ET_SYSTEM", userID.c_str(), eventInfo.c_str(), false);
            return false;
        }
        // 验证码验证成功，清除验证码和验证时间
        sql = QString("UPDATE sp_param_hrm_user SET rtVerifyCode = '', rtVerifyTime = '' WHERE id = '%1'").arg(
            userID.c_str());
        if (!ZGProxyCommon::execSql(sql.toStdString()))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = QStringLiteral("清除用户'%1'验证码信息失败").arg(userID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::verifyUserCard(const std::string& userID,
                                            const std::string& cardID,
                                            std::string& realUserID,
                                            ErrorInfo& e)
    {
        realUserID = userID;
        QString newCardID = cardID.c_str();
        newCardID = newCardID.toUpper();
        if (!userID.empty())
        {
            std::string sql = "SELECT cardID FROM sp_param_hrm_user_card WHERE userID = '" + userID + "'";
            StringList listResult;
            if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
                e.errDetail = u8"获取用户卡失败";
                ZGLOG_ERROR(e);
                return false;
            }
            auto it = std::find(listResult.begin(), listResult.end(), newCardID.toStdString());
            if (it == listResult.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_VERIFY);
                e.errDetail = u8"用户卡验证失败";
                ZGLOG_ERROR(e);
                return false;
            }
        }
        else
        {
            std::string sql = "SELECT userID FROM sp_param_hrm_user_card WHERE cardID = '" + newCardID.toStdString() +
                "'";
            StringList listResult;
            if (!ZGProxyCommon::execQuerySqlCol(sql, listResult))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
                e.errDetail = u8"获取卡" + newCardID.toStdString() + u8"关联用户失败";
                ZGLOG_ERROR(QString("Get card user error, card id = %1").arg(newCardID));
                return false;
            }
            if (listResult.empty())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_VERIFY);
                e.errDetail = u8"卡号" + newCardID.toStdString() + u8"未与用户绑定";
                ZGLOG_ERROR(e);
                return false;
            }
            realUserID = listResult[0];
        }
        return true;
    }

    bool ZGSPPowerVerifyMng::verifyUserPower(const std::string& userID,
                                             const std::string& powerID,
                                             ErrorInfo& e)
    {
        if (userID == "root")
            return true;
        QString sql = QString("SELECT DISTINCT a.powerID FROM sp_param_hrm_role_power a "
            "LEFT JOIN sp_param_hrm_user_role b ON a.roleID = b.roleID "
            "WHERE b.userID = '%1' ORDER BY a.powerID").arg(userID.c_str());
        ZG6000::StringList listPowerID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listPowerID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取用户权限失败").toStdString();
            ZGLOG_ERROR(QStringLiteral("获取用户'%1'权限失败").arg(userID.c_str()));
            return false;
        }
        if (std::find(listPowerID.begin(), listPowerID.end(), powerID) == listPowerID.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_VERIFY);
            e.errDetail = QStringLiteral("用户没有指定的权限").toStdString();
            ZGLOG_ERROR(QStringLiteral("用户%1没有指定的权限%2").arg(userID.c_str()).arg(powerID.c_str()));
            return false;
        }
        return true;
    }

    void ZGSPPowerVerifyMng::publishUserEvent(const std::string& clientID,
                                              const std::string& eventInfo,
                                              const std::string& eventInfoL2)
    {
        auto eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
        if (eventProcessPrx == nullptr)
        {
            ZGLOG_ERROR(QStringLiteral("获取事件处理服务代理失败"));
            return;
        }
        try
        {
            auto onewayPrx = eventProcessPrx->ice_oneway();
            // 组装事件信息
            std::string appNodeID;
            ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtAppNodeID", appNodeID);
            StringMap event{
                {"appNodeID", appNodeID},
                {"eventTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString()},
                {"eventTypeID", "ZG_ET_SYSTEM"}, {"alarmLevelID", "ZG_AL_LEVEL0"}, {"eventInfo", eventInfo},
                {"eventInfoL2", eventInfoL2}
            };
            onewayPrx->processEvent(event);
        }
        catch (const Ice::Exception& ex)
        {
            ZGLOG_WARN(ex.what());
        }
    }

    bool ZGSPPowerVerifyMng::addLogEvent(const QString &eventType, const QString &userName, const QString &logInfo, bool isSuccess)
    {
        QMap<QString, QString> mapParam;
        mapParam["logTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
        mapParam["eventType"] = eventType;
        mapParam["userName"] = userName;
        mapParam["logInfo"] = logInfo;
        mapParam["isSuccess"] = isSuccess ? "1" : "0";
        return ZGRuntime::instance()->auditLog(mapParam);
    }

    bool ZGSPPowerVerifyMng::logout(const std::string& clientID,
                                    const std::string& userID,
                                    bool publishEvent,
                                    ErrorInfo& e)
    {
        if (userID.empty())
        {
            return true;
        }
        std::string loginUserID;
        if (!isClientExists(clientID, e))
        {
            return false;
        }
        if (!ZGProxyCommon::getDataByField("sp_param_client", clientID, "rtLoginUserID", loginUserID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_RT);
            e.errDetail = u8"获取客户端'" + clientID + u8"'登录用户失败";
            ZGLOG_ERROR(e);
            return false;
        }
        if (loginUserID != userID)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_VERIFY);
            e.errDetail = u8"客户端" + clientID + u8"未与指定的用户" + userID + u8"绑定";
            ZGLOG_ERROR(e);
            return false;
        }
        StringMap mapFieldValue{
            {"rtLoginUserID", ""}, {"rtUserAuthID", ""}, {"rtUserStateID", ""},
            {"rtLoginTime", ""}, {"rtCookieID", ""}, {"rtKeepTime", ""}, {
                "rtUpdateTime",
                ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString()
            }
        };
        if (!ZGProxyCommon::updateDataByID("sp_param_client", clientID, mapFieldValue))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_RT);
            e.errDetail = u8"更新客户端'" + clientID + u8"'登录信息失败";
            ZGLOG_ERROR(e);
            return false;
        }
        if (publishEvent)
        {
            std::string userName, sql;
            if (userID == "root")
                userName = u8"超级管理员";
            else
            {
                sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + userID + "'";
                if (!ZGProxyCommon::execQuerySqlField(sql, userName))
                    ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(userID.c_str()));
            }
            std::string clientName;
            sql = "SELECT name FROM sp_param_client WHERE id = '" + clientID + "'";
            if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
                ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(clientID.c_str()));
            std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") +
                u8"【" + userName + u8"】" + ZGUtils::languageString(m_firstLanguage, "fromClient") +
                u8"【" + clientName + u8"】" + ZGUtils::languageString(m_firstLanguage, "logout");
            std::string eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") +
                u8"【" + userName + u8"】" + ZGUtils::languageString(m_secondLanguage, "fromClient") +
                u8"【" + clientName + u8"】" + ZGUtils::languageString(m_secondLanguage, "logout");
            publishUserEvent(clientID, eventInfo, eventInfoL2);
            addLogEvent("ZG_ET_SYSTEM", userName.c_str(), eventInfo.c_str(), true);
            publishClientEvent(clientID, userID, userName, "logout");
        }
        return true;
    }

    void ZGSPPowerVerifyMng::publishClientEvent(const std::string& clientID,
                                                const std::string& userID,
                                                const std::string& userName,
                                                const std::string& type)
    {
        StringMap clientEvent{{"userID", userID}, {"userName", userName}, {"type", type}};
        const auto& json = ZGJson::convertToJson(clientEvent);
        QString topic = QString("sp_param_client/%1/user").arg(clientID.c_str());
        m_mqttMessage->sendPublish(topic, json.c_str());
    }

    bool ZGSPPowerVerifyMng::updateClientLogin(const std::string& clientID,
                                               const std::string& userID,
                                               const std::string& loginTime,
                                               int keepTime,
                                               ErrorInfo& e)
    {
        std::string sql = "SELECT clientTypeID FROM sp_param_client WHERE id = '" + clientID + "'";
        std::string clientType;
        if (!ZGProxyCommon::execQuerySqlField(sql, clientType))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = u8"获取客户端'" + clientID + u8"'类型失败";
            ZGLOG_ERROR(e);
            return false;
        }
        sql = "SELECT id FROM sp_param_client WHERE clientTypeID = '" + clientType + "'";
        StringList listClientID;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listClientID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_DB);
            e.errDetail = u8"获取客户端信息失败";
            ZGLOG_ERROR(e);
            return false;
        }
        StringMap mapFieldValue{
            {"rtLoginUserID", userID}, {"rtLoginTime", loginTime},
            {"rtKeepTime", std::to_string(keepTime)},
            {"rtUpdateTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString()}
        };
        if (!ZGProxyCommon::updateDataByID("sp_param_client", clientID, mapFieldValue))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_RT);
            e.errDetail = u8"更新客户端登录信息错误, 客户端ID'" + clientID + u8"',用户ID'" + userID + "'";
            ZGLOG_ERROR(e);
            return false;
        }
        std::string userName;
        if (userID == "root")
            userName = u8"超级管理员";
        else
        {
            sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + userID + "'";
            if (!ZGProxyCommon::execQuerySqlField(sql, userName))
                ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(userID.c_str()));
        }
        std::string clientName;
        sql = "SELECT name FROM sp_param_client WHERE id = '" + clientID + "'";
        if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
            ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(clientID.c_str()));
        // std::string eventInfo = u8"用户【" + userName + u8"】登录至客户端【" + clientName + "】";
        std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") + u8"【" + userName + u8"】" +
            ZGUtils::languageString(m_firstLanguage, "loginToClient") + "【" + clientName + u8"】";
        std::string eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") + u8"【" + userName + u8"】" +
            ZGUtils::languageString(m_secondLanguage, "loginToClient") + "【" + clientName + u8"】";
        publishUserEvent(clientID, eventInfo, eventInfoL2);
        addLogEvent("ZG_ET_SYSTEM", userName.c_str(), eventInfo.c_str(), true);
        publishClientEvent(clientID, userID, userName, "login");
        if (!listClientID.empty())
        {
            ListStringMap listLoginUser;
            if (!ZGProxyCommon::mgetDataByFields("sp_param_client", listClientID, {"rtAppNodeID", "rtLoginUserID"},
                listLoginUser))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGSPPowerVerify::ZG_ERR_RT);
                e.errDetail = u8"获取客户端登录用户失败";
                ZGLOG_ERROR(e);
                return false;
            }
            for (size_t i = 0; i < listClientID.size(); ++i)
            {
                if ((listLoginUser[i]["rtLoginUserID"] == userID) && (listClientID[i] != clientID))
                {
                    if (!logout(listClientID[i], userID, false, e))
                    {
                        return false;
                    }
                    sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + userID + "'";
                    if (!ZGProxyCommon::execQuerySqlField(sql, userName))
                        ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(userID.c_str()));
                    sql = "SELECT name FROM sp_param_client WHERE id = '" + listClientID[i] + "'";
                    if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
                        ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(listClientID[i].c_str()));
                    // eventInfo = u8"用户【" + userName + u8"】已从客户端【" + clientName +
                    //     u8"】强制下线，原因：该用户在其他客户端登录";
                    eventInfo = ZGUtils::languageString(m_firstLanguage, "user") + u8"【" + userName + u8"】" +
                        ZGUtils::languageString(m_firstLanguage, "fromClient") + "【" + clientName + u8"】" +
                        ZGUtils::languageString(m_firstLanguage, "offlineReason") + u8"：" +
                        ZGUtils::languageString(m_firstLanguage, "loginOtherClient");
                    eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") + u8"【" + userName + u8"】" +
                        ZGUtils::languageString(m_secondLanguage, "fromClient") + "【" + clientName + u8"】" +
                        ZGUtils::languageString(m_secondLanguage, "offlineReason") + u8"：" +
                        ZGUtils::languageString(m_secondLanguage, "loginOtherClient");
                    publishUserEvent(clientID, eventInfo, eventInfoL2);
                    addLogEvent("ZG_ET_SYSTEM", userName.c_str(), eventInfo.c_str(), true);
                    publishClientEvent(listClientID[i], userID, userName, "logout");
                }
            }
        }
        return true;
    }

    void ZGSPPowerVerifyMng::checkLoginStatus(const QDateTime& dateTime)
    {
        std::string sql = "SELECT id FROM sp_param_client";
        StringList listID;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listID))
        {
            ZGLOG_ERROR(u8"获取客户端ID失败");
            return;
        }
        ListStringMap listRecord;
        if (!ZGProxyCommon::mgetDataByFields("sp_param_client", listID,
            {
                "id", "rtIsActivate", "rtState", "rtLoginUserID", "rtLoginTime",
                "rtKeepTime"
            },
            listRecord))
        {
            ZGLOG_ERROR(u8"获取客户端实时参数失败");
            return;
        }
        try
        {
            StringList listUpdateID;
            ListStringMap listUpdateRecord;
            for (const auto& record : listRecord)
            {
                const auto& id = ZGUtils::get(record, "id");
                const auto& rtIsActive = ZGUtils::get(record, "rtIsActivate");
                const auto& rtState = ZGUtils::get(record, "rtState");
                const auto& rtLoginUserID = ZGUtils::get(record, "rtLoginUserID");
                const auto& rtLoginTime = ZGUtils::get(record, "rtLoginTime");
                const auto& rtKeepTime = ZGUtils::get(record, "rtKeepTime");
                if (!rtLoginUserID.empty())
                {
                    if (rtIsActive != "1")
                    {
                        ZGLOG_INFO(QStringLiteral("客户端'%1'未激活，清除关联用户'%2'").arg(id.c_str()));
                        ErrorInfo e;
                        if (!logout(id, rtLoginUserID, false, e))
                        {
                            ZGLOG_ERROR(e);
                        }
                        else
                        {
                            std::string userName;
                            if (rtLoginUserID == "root")
                                userName = u8"超级管理员";
                            else
                            {
                                sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + rtLoginUserID + "'";
                                if (!ZGProxyCommon::execQuerySqlField(sql, userName))
                                    ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(rtLoginUserID.c_str()));
                            }
                            std::string clientName;
                            sql = "SELECT name FROM sp_param_client WHERE id = '" + id + "'";
                            if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
                                ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(id.c_str()));
                            // std::string eventInfo = u8"用户【" + userName + u8"】从客户端【" + clientName +
                            //     u8"】强制下线，原因：客户端未激活";
                            std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") +
                                u8"【" + userName + u8"】" + ZGUtils::languageString(m_firstLanguage, "fromClient") +
                                u8"【" + clientName + "】" + ZGUtils::languageString(m_firstLanguage, "offlineReason") +
                                u8"：" + ZGUtils::languageString(m_firstLanguage, "clientNotActivate");
                            std::string eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") +
                                u8"【" + userName + u8"】" + ZGUtils::languageString(m_secondLanguage, "fromClient") +
                                u8"【" + clientName + "】" + ZGUtils::languageString(m_secondLanguage, "offlineReason") +
                                u8"：" + ZGUtils::languageString(m_secondLanguage, "clientNotActivate");
                            publishUserEvent(id, eventInfo, eventInfoL2);
                            addLogEvent("ZG_ET_SYSTEM", userName.c_str(), eventInfo.c_str(), true);
                            publishClientEvent(id, rtLoginUserID, userName, "logout");
                        }
                    }
                    else if (rtState != "2")
                    {
                        QDateTime loginTime;
                        ZGUtils::StringToDateTime(rtLoginTime.c_str(), loginTime, true);
                        QDateTime currentTime = QDateTime::currentDateTime();
                        if (loginTime.secsTo(currentTime) > 30)
                        {
                            ZGLOG_INFO(
                                QStringLiteral("客户端'%1'未在线，清除关联用户'%2'").arg(id.c_str()).arg(rtLoginUserID.c_str()));
                            ErrorInfo e;
                            if (!logout(id, rtLoginUserID, false, e))
                            {
                                ZGLOG_ERROR(e);
                            }
                            else
                            {
                                std::string userName;
                                if (rtLoginUserID == "root")
                                    userName = u8"超级管理员";
                                else
                                {
                                    sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + rtLoginUserID + "'";
                                    if (!ZGProxyCommon::execQuerySqlField(sql, userName))
                                        ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(rtLoginUserID.c_str()));
                                }
                                std::string clientName;
                                sql = "SELECT name FROM sp_param_client WHERE id = '" + id + "'";
                                if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
                                    ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(id.c_str()));
                                // std::string eventInfo = u8"用户【" + userName + u8"】从客户端【" + clientName +
                                //     u8"】强制下线，原因：客户端离线";
                                std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") +
                                    u8"【" + userName + u8"】" + ZGUtils::languageString(m_firstLanguage, "fromClient") +
                                    u8"【" + clientName + "】" + ZGUtils::languageString(m_firstLanguage, "offlineReason")
                                    +
                                    u8"：" + ZGUtils::languageString(m_firstLanguage, "clientOffline");
                                std::string eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") +
                                    u8"【" + userName + u8"】" + ZGUtils::languageString(m_secondLanguage, "fromClient") +
                                    u8"【" + clientName + "】" + ZGUtils::languageString(m_secondLanguage,
                                        "offlineReason") +
                                    u8"：" + ZGUtils::languageString(m_secondLanguage, "clientOffline");
                                publishUserEvent(id, eventInfo, eventInfoL2);
                                addLogEvent("ZG_ET_SYSTEM", userName.c_str(), eventInfo.c_str(), true);
                                publishClientEvent(id, rtLoginUserID, userName, "logout");
                            }
                        }
                    }
                    // 如果用户登录时长大于keepTime，则强制下线
                    const auto& keepTime = std::atoi(rtKeepTime.c_str());
                    if (keepTime > 0)
                    {
                        QDateTime loginTime;
                        ZGUtils::StringToDateTime(rtLoginTime.c_str(), loginTime, true);
                        QDateTime currentTime = QDateTime::currentDateTime();
                        if (loginTime.secsTo(currentTime) > keepTime)
                        {
                            ZGLOG_INFO(QStringLiteral("客户端'%1'登录时长超过最大值，清除关联用户'%2'")
                                .arg(id.c_str()).arg(rtLoginUserID.c_str()));
                            ZG6000::ErrorInfo e;
                            if (!logout(id, rtLoginUserID, false, e))
                                ZGLOG_ERROR(e);
                            else
                            {
                                std::string userName;
                                if (rtLoginUserID == "root")
                                    userName = u8"超级管理员";
                                else
                                {
                                    sql = "SELECT name FROM sp_param_hrm_user WHERE id = '" + rtLoginUserID + "'";
                                    if (!ZGProxyCommon::execQuerySqlField(sql, userName))
                                        ZGLOG_WARN(QStringLiteral("获取用户'%1'名称失败").arg(rtLoginUserID.c_str()));
                                }
                                std::string clientName;
                                sql = "SELECT name FROM sp_param_client WHERE id = '" + id + "'";
                                if (!ZGProxyCommon::execQuerySqlField(sql, clientName))
                                    ZGLOG_WARN(QStringLiteral("获取客户端'%1'名称失败").arg(id.c_str()));
                                std::string eventInfo = ZGUtils::languageString(m_firstLanguage, "user") +
                                    u8"【" + userName + u8"】" + ZGUtils::languageString(m_firstLanguage, "fromClient") +
                                    u8"【" + clientName + "】" + ZGUtils::languageString(m_firstLanguage, "offlineReason")
                                    + u8"：" + ZGUtils::languageString(m_firstLanguage, "loginTimeout");
                                std::string eventInfoL2 = ZGUtils::languageString(m_secondLanguage, "user") +
                                    u8"【" + userName + u8"】" + ZGUtils::languageString(m_secondLanguage, "fromClient") +
                                    u8"【" + clientName + "】" + ZGUtils::languageString(m_firstLanguage, "offlineReason") +
                                    u8"：" + ZGUtils::languageString(m_firstLanguage, "loginTimeout");
                                publishUserEvent(id, eventInfo, eventInfoL2);
                                addLogEvent("ZG_ET_SYSTEM", userName.c_str(), eventInfo.c_str(), true);
                                publishClientEvent(id, rtLoginUserID, userName, "logout");
                            }
                        }
                    }
                }
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    void ZGSPPowerVerifyMng::clearExpiredVerifyCode(const QDateTime& dateTime)
    {
        // 清空用户表中5分钟前的验证码
        QString sql = QString("UPDATE sp_param_hrm_user SET rtVerifyCode = '', rtVerifyTime = '' "
            "WHERE rtVerifyTime < '%1'").arg(ZGUtils::DateTimeToString(dateTime.addSecs(-300)));
        if (!ZGProxyCommon::execSql(sql.toStdString()))
        {
            ZGLOG_ERROR(u8"清除过期验证码失败");
        }
    }

    void ZGSPPowerVerifyMng::onCheckStatus()
    {
        QDateTime currentTime = QDateTime::currentDateTime();
        checkLoginStatus(currentTime);
        clearExpiredVerifyCode(currentTime);
    }
} // namespace ZG6000

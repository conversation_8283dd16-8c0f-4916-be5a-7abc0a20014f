#include "ZGSPPowerVerifyI.h"

#include "ZGSPPowerVerifyMng.h"

namespace ZG6000
{
    ZGSPPowerVerifyI::ZGSPPowerVerifyI()
    {
        ZGSPPowerVerifyMng::instance()->init();
    }

    bool ZGSPPowerVerifyI::checkState(const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->checkState();
    }

    bool ZGSPPowerVerifyI::isUserHasPower(std::string userID,
                                          std::string powerID,
                                          bool& hasPower,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->isUserHasPower(userID, powerID, hasPower, e);
    }

    bool ZGSPPowerVerifyI::loginByPassword(std::string clientID,
                                           std::string userID,
                                           std::string password,
                                           int keepTime,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->loginByPassword(clientID, userID,
            password, keepTime, e);
    }

    bool ZGSPPowerVerifyI::loginByPasswordAndVerifyCode(std::string clientID,
                                                        std::string userID,
                                                        std::string password,
                                                        std::string verifyCode,
                                                        int keepTime,
                                                        ErrorInfo& e,
                                                        const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->loginByPasswordAndVerifyCode(clientID, userID,
            password, verifyCode, keepTime, e);
    }

    bool ZGSPPowerVerifyI::loginByCard(std::string clientID,
                                       std::string userID,
                                       std::string authModeID,
                                       std::string cardID,
                                       int keepTime,
                                       std::string& realUserID,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->loginByCard(clientID, userID,
            authModeID, cardID, keepTime, realUserID, e);
    }

    bool ZGSPPowerVerifyI::loginByAuthDev(std::string clientID,
                                          std::string userID,
                                          std::string authModeID,
                                          int keepTime,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->loginByAuthDev(clientID, userID,
            authModeID, keepTime, e);
    }

    bool ZGSPPowerVerifyI::logout(std::string clientID,
                                  std::string userID,
                                  ErrorInfo& e,
                                  const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->logout(clientID, userID, e);
    }

    bool ZGSPPowerVerifyI::getAvaiableUser(std::string clientID,
                                           std::string appNodeID,
                                           std::string powerID,
                                           ListStringMap& lstUser,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->getAvaiableUser(clientID, appNodeID,
            powerID, lstUser, e);
    }

    bool ZGSPPowerVerifyI::verifyByPassword(std::string clientID,
                                            std::string userID,
                                            std::string password,
                                            std::string appNodeID,
                                            std::string powerID,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->verifyByPassword(clientID, userID,
            password, appNodeID, powerID, e);
    }

    bool ZGSPPowerVerifyI::verifyByCard(std::string clientID,
                                        std::string userID,
                                        std::string authModeID,
                                        std::string cardID,
                                        std::string appNodeID,
                                        std::string powerID,
                                        std::string& realUserID,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->verifyByCard(clientID, userID,
            authModeID, cardID, appNodeID, powerID, realUserID, e);
    }

    bool ZGSPPowerVerifyI::verifyByAuthDev(std::string clientID,
                                           std::string userID,
                                           std::string authModeID,
                                           std::string appNodeID,
                                           std::string powerID,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->verifyByAuthDev(clientID, userID,
            authModeID, appNodeID, powerID, e);
    }

    bool ZGSPPowerVerifyI::verifyByPasswordAndVerifyCode(std::string clientID,
                                                         std::string userID,
                                                         std::string password,
                                                         std::string verifyCode,
                                                         std::string appNodeID,
                                                         std::string powerID,
                                                         ErrorInfo& e,
                                                         const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->verifyByPasswordAndVerifyCode(clientID, userID,
            password, verifyCode, appNodeID, powerID, e);
    }

    bool ZGSPPowerVerifyI::sendVerifyCode(std::string userID,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
    {
        return ZGSPPowerVerifyMng::instance()->sendVerifyCode(userID, e);
    }
} // namespace ZG6000

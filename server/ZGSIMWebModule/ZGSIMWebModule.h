#ifndef ZGSIMWEBMODULE_H
#define ZGSIMWEBMODULE_H

#include "ZGWebModule.h"

class ZGSIMHandle;
class ZGSIMWebModule : public ZGWebModule
{
    Q_OBJECT
    Q_PLUGIN_METADATA(IID ZGWebModule_iid FILE "ZGSIMWebModule.json")
    Q_INTERFACES(ZGWebModule)

public:
    explicit ZGSIMWebModule(QObject *parent = nullptr);
    bool initialize() override;

private:
    ZGSIMHandle* m_pHandle;
};

#endif // ZGSIMWEBMODULE_H

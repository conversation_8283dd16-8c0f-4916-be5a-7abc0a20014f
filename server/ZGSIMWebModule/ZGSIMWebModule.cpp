#include "ZGSIMWebModule.h"
#include "ZGSIMHandle.h"

ZGSIMWebModule::ZGSIMWebModule(QObject *parent)
    : ZGWebModule(parent)
{
    m_pHandle = new ZGSIMHandle(this);
    registerHandle("sim/server/state/get", m_pHandle, &ZGSIMHandle::on_server_state_get);
    registerHandle("sim/server/action/set", m_pHandle, &ZGSIMHandle::on_server_action_set);
    registerHandle("sim/device/state/get", m_pHandle, &ZGSIMHandle::on_device_state_get);
    registerHandle("sim/device/action/set", m_pHandle, &ZGSIMHandle::on_device_action_set);
    registerHandle("sim/equip/state/get", m_pHandle, &ZGSIMHandle::on_equip_state_get);
    registerHandle("sim/equip/action/set", m_pHandle, &ZGSIMHandle::on_equip_action_set);
    registerHandle("sim/port/state/get", m_pHandle, &ZGSIMHandle::on_port_state_get);
    registerHandle("sim/port/action/set", m_pHandle, &ZGSIMHandle::on_port_action_set);
    registerHandle("sim/dataset/state/get", m_pHandle, &ZGSIMHandle::on_dataset_state_get);
    registerHandle("sim/dataset/action/set", m_pHandle, &ZGSIMHandle::on_dataset_action_set);
    registerHandle("sim/usergroup/state/get", m_pHandle, &ZGSIMHandle::on_user_group_state_get);
    registerHandle("sim/usergroup/action/set", m_pHandle, &ZGSIMHandle::on_user_group_action_set);
    registerHandle("sim/signal/value/get", m_pHandle, &ZGSIMHandle::on_signal_value_get);
    registerHandle("sim/signal/value/set", m_pHandle, &ZGSIMHandle::on_signal_value_set);
    registerHandle("sim/device/data/get", m_pHandle, &ZGSIMHandle::on_device_data_get);
    registerHandle("sim/equip/data/get", m_pHandle, &ZGSIMHandle::on_equip_data_get);
    registerHandle("sim/port/data/get", m_pHandle, &ZGSIMHandle::on_port_data_get);
    registerHandle("sim/dataset/data/get", m_pHandle, &ZGSIMHandle::on_dataset_data_get);
    registerHandle("sim/usergroup/data/get", m_pHandle, &ZGSIMHandle::on_user_group_data_get);
    registerHandle("sim/device/data/num", m_pHandle, &ZGSIMHandle::on_device_data_num);
    registerHandle("sim/equip/data/num", m_pHandle, &ZGSIMHandle::on_equip_data_num);
    registerHandle("sim/port/data/num", m_pHandle, &ZGSIMHandle::on_port_data_num);
    registerHandle("sim/dataset/data/num", m_pHandle, &ZGSIMHandle::on_dataset_data_num);
    registerHandle("sim/usergroup/data/num", m_pHandle, &ZGSIMHandle::on_user_group_data_num);
    registerHandle("sim/net/intf/get", m_pHandle, &ZGSIMHandle::on_net_intf_get);
    registerHandle("sim/net/addr/get", m_pHandle, &ZGSIMHandle::on_net_addr_get);
    registerHandle("sim/net/addr/add", m_pHandle, &ZGSIMHandle::on_net_addr_add);
    registerHandle("sim/net/addr/delete", m_pHandle, &ZGSIMHandle::on_net_addr_delete);
    registerHandle("sim/net/addrs/add", m_pHandle, &ZGSIMHandle::on_net_addrs_add);
    registerHandle("sim/net/addrs/delete", m_pHandle, &ZGSIMHandle::on_net_addrs_delete);
    registerHandle("sim/net/intf/bind", m_pHandle, &ZGSIMHandle::on_net_intf_bind);
    registerHandle("sim/device/run/get", m_pHandle, &ZGSIMHandle::on_device_run_get);
    registerHandle("sim/device/run/set", m_pHandle, &ZGSIMHandle::on_device_run_set);
    registerHandle("sim/type/item/get", m_pHandle, &ZGSIMHandle::on_type_item_get);
    registerHandle("sim/task/item/add", m_pHandle, &ZGSIMHandle::on_task_item_add);
    registerHandle("sim/task/item/delete", m_pHandle, &ZGSIMHandle::on_task_item_delete);
    registerHandle("sim/task/item/clear", m_pHandle, &ZGSIMHandle::on_task_item_clear);
    registerHandle("sim/task/item/get", m_pHandle, &ZGSIMHandle::on_task_item_get);
    registerHandle("sim/task/action/set", m_pHandle, &ZGSIMHandle::on_task_action_set);
    registerHandle("sim/task/state/get", m_pHandle, &ZGSIMHandle::on_task_state_get);
}

bool ZGSIMWebModule::initialize()
{
    if (!ZGWebModule::initialize())
        return false;
    return true;
}

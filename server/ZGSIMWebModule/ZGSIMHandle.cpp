#include "ZGSIMHandle.h"
#include "ZGWebModule.h"
#include "ZGProxyMng.h"
#include "ZGJson.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

ZGSIMHandle::ZGSIMHandle(QObject *parent)
    : QObject{parent}
{

}

bool ZGSIMHandle::initialize()
{
    return true;
}

ZGWebModule::Response ZGSIMHandle::on_server_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        ZG6000::StringMap server;
        ZG6000::ErrorInfo e;
        if (!simProxy->getServerState(server, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertTo<PERSON>son(server);
        QJsonDocument doc = QJsonDocument::from<PERSON>son(json.c_str());
        return ZGWebModule::replyObject(doc.object());
    });
}

ZGWebModule::Response ZGSIMHandle::on_server_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        ZG6000::StringMap server;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            server[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->setServerAction(server, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_device_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getDeviceList(listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_device_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonArray array = param.toArray();
        ZG6000::ListStringMap listRecord;
        for (const auto& ref: array)
        {
            QJsonObject object = ref.toObject();
            ZG6000::StringMap record;
            for (auto it = object.begin(); it != object.end(); ++it)
            {
                record[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listRecord.push_back(std::move(record));
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->setDeviceAction(listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_equip_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getPrimaryList(listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_equip_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonArray array = param.toArray();
        ZG6000::ListStringMap listRecord;
        for (const auto& ref: array)
        {
            QJsonObject object = ref.toObject();
            ZG6000::StringMap record;
            for (auto it = object.begin(); it != object.end(); ++it)
            {
                record[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listRecord.push_back(std::move(record));
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->setPrimaryAction(listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_port_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getPortList(listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_port_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonArray array = param.toArray();
        ZG6000::ListStringMap listRecord;
        for (const auto& ref: array)
        {
            QJsonObject object = ref.toObject();
            ZG6000::StringMap record;
            for (auto it = object.begin(); it != object.end(); ++it)
            {
                record[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listRecord.push_back(std::move(record));
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->setPortAction(listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_dataset_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getDatasetList(listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_dataset_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonArray array = param.toArray();
        ZG6000::ListStringMap listRecord;
        for (const auto& ref: array)
        {
            QJsonObject object = ref.toObject();
            ZG6000::StringMap record;
            for (auto it = object.begin(); it != object.end(); ++it)
            {
                record[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listRecord.push_back(std::move(record));
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->setDatasetAction(listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_user_group_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getUserGroupList(listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_user_group_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonArray array = param.toArray();
        ZG6000::ListStringMap listRecord;
        for (const auto& ref: array)
        {
            QJsonObject object = ref.toObject();
            ZG6000::StringMap record;
            for (auto it = object.begin(); it != object.end(); ++it)
            {
                record[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listRecord.push_back(std::move(record));
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->setUserGroupAction(listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_signal_value_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "ids"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& idArray = object["ids"].toArray();
        ZG6000::StringList listDataID;
        for (const auto& ref: idArray)
        {
            listDataID.push_back(ref.toString().toStdString());
        }
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getSignalValue(dataType, listDataID, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_signal_value_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "items"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& itemArray = object["items"].toArray();
        ZG6000::ListStringMap listRecord;
        for (const auto& ref: itemArray)
        {
            const auto& itemObj = ref.toObject();
            ZG6000::StringMap record;
            for (auto it = itemObj.begin(); it != itemObj.end(); ++it)
            {
                record[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listRecord.push_back(std::move(record));
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->setSignalValue(dataType, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_device_data_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "id", "page", "limit"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& id = object["id"].toString().toStdString();
        const auto& page = object["page"].toVariant().toInt();
        const auto& limit = object["limit"].toVariant().toInt();
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getDeviceData(dataType, id, page, limit, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_equip_data_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "id", "page", "limit"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& id = object["id"].toString().toStdString();
        const auto& page = object["page"].toVariant().toInt();
        const auto& limit = object["limit"].toVariant().toInt();
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getPrimaryData(dataType, id, page, limit, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_port_data_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "id", "page", "limit"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& id = object["id"].toString().toStdString();
        const auto& page = object["page"].toVariant().toInt();
        const auto& limit = object["limit"].toVariant().toInt();
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getPortData(dataType, id, page, limit, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_dataset_data_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "id", "page", "limit"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& id = object["id"].toString().toStdString();
        const auto& page = object["page"].toVariant().toInt();
        const auto& limit = object["limit"].toVariant().toInt();
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getDatasetData(dataType, id, page, limit, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_user_group_data_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "id", "page", "limit"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& id = object["id"].toString().toStdString();
        const auto& page = object["page"].toVariant().toInt();
        const auto& limit = object["limit"].toVariant().toInt();
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getUserGroupData(dataType, id, page, limit, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_device_data_num(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "id"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& id = object["id"].toString().toStdString();
        int num = 0;
        ZG6000::ErrorInfo e;
        if (!simProxy->getDeviceDataNum(dataType, id, num, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(num);
    });
}

ZGWebModule::Response ZGSIMHandle::on_equip_data_num(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "id"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& id = object["id"].toString().toStdString();
        int num = 0;
        ZG6000::ErrorInfo e;
        if (!simProxy->getPrimaryDataNum(dataType, id, num, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(num);
    });
}

ZGWebModule::Response ZGSIMHandle::on_port_data_num(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "id"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& id = object["id"].toString().toStdString();
        int num = 0;
        ZG6000::ErrorInfo e;
        if (!simProxy->getPortDataNum(dataType, id, num, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(num);
    });
}

ZGWebModule::Response ZGSIMHandle::on_dataset_data_num(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "id"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& id = object["id"].toString().toStdString();
        int num = 0;
        ZG6000::ErrorInfo e;
        if (!simProxy->getDatasetDataNum(dataType, id, num, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(num);
    });
}

ZGWebModule::Response ZGSIMHandle::on_user_group_data_num(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"dataType", "id"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& dataType = object["dataType"].toVariant().toInt();
        const auto& id = object["id"].toString().toStdString();
        int num = 0;
        ZG6000::ErrorInfo e;
        if (!simProxy->getUserGroupDataNum(dataType, id, num, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(num);
    });
}

ZGWebModule::Response ZGSIMHandle::on_net_intf_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        ZG6000::StringList listName;
        ZG6000::ErrorInfo e;
        if (!simProxy->getNetInterface(listName, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listName);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_net_addr_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& name = param.toString().toStdString();
        ZG6000::ListStringMap listRecord;
        ZG6000::ErrorInfo e;
        if (!simProxy->getNetworkAddr(name, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_net_addr_add(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& name = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!simProxy->addNetworkAddr(name, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_net_addr_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& name = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!simProxy->removeNetworkAddr(name, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_net_addrs_add(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"name", "addrs"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& name = object["name"].toString().toStdString();
        const auto& addrArray = object["addrs"].toArray();
        ZG6000::ListStringMap listRecord;
        for (const auto& ref: addrArray)
        {
            const auto& addrObj = ref.toObject();
            ZG6000::StringMap record;
            for (auto it = addrObj.begin(); it != addrObj.end(); ++it)
            {
                record[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listRecord.push_back(std::move(record));
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->addNetworkAddrEx(name, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_net_addrs_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        QJsonObject object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"name", "addrs"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& name = object["name"].toString().toStdString();
        const auto& addrArray = object["addrs"].toArray();
        ZG6000::ListStringMap listRecord;
        for (const auto& ref: addrArray)
        {
            const auto& addrObj = ref.toObject();
            ZG6000::StringMap record;
            for (auto it = addrObj.begin(); it != addrObj.end(); ++it)
            {
                record[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listRecord.push_back(std::move(record));
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->removeNetworkAddrEx(name, listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_net_intf_bind(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& name = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!simProxy->bindNetInterface(name, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_device_run_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        ZG6000::ListStringMap listItem;
        ZG6000::ErrorInfo e;
        if (!simProxy->getDeviceRun(listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listItem);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_device_run_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& array = param.toArray();
        ZG6000::ListStringMap listItem;
        for (auto itemRef: array)
        {
            const auto& itemObj = itemRef.toObject();
            ZG6000::StringMap item;
            for (auto it = itemObj.begin(); it != itemObj.end(); ++it)
            {
                item[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listItem.push_back(std::move(item));
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->setDeviceRun(listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_type_item_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& type = param.toVariant().toInt();
        ZG6000::ListStringMap listItem;
        ZG6000::ErrorInfo e;
        if (!simProxy->getTypeList(type, listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listItem);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_task_item_add(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"type", "ids"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& type = object["type"].toVariant().toInt();
        const auto& array = object["ids"].toArray();
        ZG6000::StringList listID;
        for (const auto& idRef: array)
        {
            listID.push_back(idRef.toString().toStdString());
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->addTaskList(type, listID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_task_item_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"type", "ids"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& type = object["type"].toVariant().toInt();
        const auto& array = object["ids"].toArray();
        ZG6000::StringList listID;
        for (const auto& idRef: array)
        {
            listID.push_back(idRef.toString().toStdString());
        }
        ZG6000::ErrorInfo e;
        if (!simProxy->deleteTaskList(type, listID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_task_item_clear(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& type = param.toVariant().toInt();
        ZG6000::ErrorInfo e;
        if (!simProxy->clearTaskList(type, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_task_item_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& type = param.toVariant().toInt();
        ZG6000::ListStringMap listItem;
        ZG6000::ErrorInfo e;
        if (!simProxy->getTaskList(type, listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listItem);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGSIMHandle::on_task_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"type", "action", "option"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& type = object["type"].toVariant().toInt();
        const auto& action = object["action"].toVariant().toInt();
        const auto& option = object["option"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!simProxy->setTaskTest(type, action, option, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGSIMHandle::on_task_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest &req)
{
    return simCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGSIMServerPrx> simProxy) {
        const auto& type = param.toVariant().toInt();
        int state;
        ZG6000::ErrorInfo e;
        if (!simProxy->getTaskTest(type, state, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(state);
    });
}

ZGWebModule::Response ZGSIMHandle::simCall(const QString& clientID, const QJsonValue& param, const QHttpServerRequest &req, const std::function<ZGWebModule::Response (std::shared_ptr<ZG6000::ZGSIMServerPrx>)> &func)
{
    QJsonDocument jsonDocument = QJsonDocument::fromJson(req.body());
    const auto& rootObject = jsonDocument.object();
    auto it = rootObject.find("serviceID");
    if (it == rootObject.end())
        return ZGWebModule::errorObject(QStringLiteral("缺少字段'%1'").arg("serviceID"));
    const auto& serviceID = it->toString();
    auto simProxy = ZGProxyMng::instance()->getProxySIMServer(serviceID);
    if (simProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取模拟服务代理对象失败"));
    try
    {
        return func(simProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

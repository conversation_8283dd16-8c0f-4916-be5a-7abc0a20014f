#ifndef ZGSIMHANDLE_H
#define ZGSIMHANDLE_H

#include "ZGWebModule.h"
#include "ZGSIMServer.h"

class ZGSIMHandle : public QObject
{
    Q_OBJECT
public:
    explicit ZGSIMHandle(QObject *parent = nullptr);
    bool initialize();

public:
    ZGWebModule::Response on_server_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_server_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_device_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_device_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_equip_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_equip_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_port_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_port_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_dataset_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_dataset_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_user_group_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_user_group_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_signal_value_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_signal_value_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_device_data_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_equip_data_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_port_data_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_dataset_data_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_user_group_data_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_device_data_num(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_equip_data_num(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_port_data_num(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_dataset_data_num(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_user_group_data_num(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_net_intf_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_net_addr_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_net_addr_add(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_net_addr_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_net_addrs_add(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_net_addrs_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_net_intf_bind(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_device_run_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_device_run_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_type_item_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_task_item_add(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_task_item_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_task_item_clear(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_task_item_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_task_action_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_task_state_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);

private:
    ZGWebModule::Response simCall(const QString& clientID, const QJsonValue& param,
        const QHttpServerRequest& req, const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGSIMServerPrx>)>& func);
};

#endif // ZGSIMHANDLE_H

#include "ZGOPTaskITI.h"
#include "ZGOPTaskITMng.h"

namespace ZG6000 {

ZGOPTaskITI::ZGOPTaskITI()
{
    ZGOPTaskITMng::instance()->init();
}

bool ZGOPTaskITI::checkState(const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->checkState(current);
}

void ZGOPTaskITI::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current)
{
    ZGOPTaskITMng::instance()->dispatchData(tableName, oper, reason, time, listRecord, current);
}

bool ZGOPTaskITI::deleteTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->deleteTask(taskID, param, e, current);
}

bool ZGOPTaskITI::getTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->getTaskList(param, listTask, e, current);
}

bool ZGOPTaskITI::startTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->startTask(taskID, param, e, current);
}

bool ZGOPTaskITI::pauseTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->pauseTask(taskID, param, e, current);
}

bool ZGOPTaskITI::resumeTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->resumeTask(taskID, param, e, current);
}

bool ZGOPTaskITI::retryTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->retryTask(taskID, param, e, current);
}

bool ZGOPTaskITI::abolishTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->abolishTask(taskID, param, e, current);
}

bool ZGOPTaskITI::confirmTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->confirmTask(taskID, param, e, current);
}

bool ZGOPTaskITI::getTaskItems(std::string taskID, StringMap &task, ListStringMap &items, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->getTaskItems(taskID, task, items, e, current);
}

bool ZGOPTaskITI::getItemActions(std::string itemID, StringMap &item, ListStringMap &actions, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->getItemActions(itemID, item, actions, e, current);
}

bool ZGOPTaskITI::getTaskTypeObjects(StringMap params, ListStringMap& listObject, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskITMng::instance()->getTaskTypeObjects(params, listObject, e, current);
}

bool ZGOPTaskITI::createTypicalTask(StringMap params, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->createTypicalTask(params, e, current);
}

bool ZGOPTaskITI::createSpecialTask(std::string taskTypeID, StringList listObjectID, StringMap params, std::string &taskID, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->createSpecialTask(taskTypeID, listObjectID, params, taskID, e, current);
}

bool ZGOPTaskITI::createUAVTask(StringMap task, ListStringMap listPreset, ListStringMap listItem, ListStringMap listAction, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskITMng::instance()->createUAVTask(task, listPreset, listItem, listAction, e, current);
}

bool ZGOPTaskITI::createCustomTask(ListStringMap listItem, StringMap params, std::string &taskID, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->createCustomTask(listItem, params, taskID, e, current);
}

bool ZGOPTaskITI::editTask(std::string taskID, StringMap task, ListStringMap items, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->editTask(taskID, task, items, e, current);
}

bool ZGOPTaskITI::convertTask(std::string taskID, StringMap params, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskITMng::instance()->convertTask(taskID, params, e, current);
}

bool ZGOPTaskITI::skipItem(std::string itemID, StringMap params, ErrorInfo &e, const Ice::Current &current)
{
    return false;
}

bool ZGOPTaskITI::updateTask(ListStringMap listTask, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskITMng::instance()->updateTask(listTask, e, current);
}

bool ZGOPTaskITI::updateItem(ListStringMap listItem, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskITMng::instance()->updateItem(listItem, e, current);
}

bool ZGOPTaskITI::updateAction(ListStringMap listAction, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskITMng::instance()->updateAction(listAction, e, current);
}

bool ZGOPTaskITI::downloadTask(StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ListStringMap& listAction, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskITMng::instance()->downloadTask(listTaskID, listTask, listItem, listAction, e, current);
}
} // namespace ZG6000

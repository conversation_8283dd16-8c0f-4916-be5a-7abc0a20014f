#include "ZGOPTaskITTaskUAV.h"

#include <ZGUtils.h>

#include "ZGProxyCommon.h"
#include "ZGDebugMng.h"
#include "ZGOPTaskITDefine.h"
#include "ZGRuntime.h"

ZGOPTaskITTaskUAV::ZGOPTaskITTaskUAV(const std::string& id, QObject *parent)
    : ZGOPTaskITTask{id, parent}
{

}

void ZGOPTaskITTaskUAV::onTimer()
{
    QMutexLocker lock(&m_mutex);
    ZG6000::StringMap task;
    if (!ZGProxyCommon::getDataByID("op_param_task", m_id, task))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'信息失败").arg(m_id.c_str()));
        return;
    }
    try
    {
        const auto& rtExecStage = ZGUtils::get(task, "rtTaskStageID");
        if (rtExecStage == "ZG_TS_EXECUTE")
        {
            const auto& rtEndTime = ZGUtils::get(task, "rtEndTime");
            QDateTime endTime;
            if (ZGUtils::StringToDateTime(rtEndTime.c_str(), endTime, true))
            {
                QDateTime currTime = QDateTime::currentDateTime();
                if (currTime > endTime)
                {
                    setCurrentState("ZG_TS_TASK_TIMEOUT");
                    return;
                }
            }
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_DEBUG(e.what());
    }
}

void ZGOPTaskITTaskUAV::registerAction()
{
    fsm.on({ "ZG_TS_CREATE", "ZG_TS_FINISHED" }, ZGOPIT_CONFIRM) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onCreateConfirm(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXAM", "ZG_TS_FINISHED" }, ZGOPIT_CONFIRM) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onExamConfirm(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_READY" }, ZGOPIT_START) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onExecuteStart(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_EXECUTING" }, ZGOPIT_PAUSE) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onExecutePause(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_EXECUTING" }, ZGOPIT_STOP) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onExecuteStop(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_PAUSED" }, ZGOPIT_RESUME) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onExecuteResume(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_ERROR" }, ZGOPIT_RETRY) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onExecuteRetry(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_FINISHED" }, ZGOPIT_CONFIRM) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onExecuteConfirm(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_READY" }, ZGOPIT_ABOLISH) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_PAUSED" }, ZGOPIT_ABOLISH) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_ERROR" }, ZGOPIT_ABOLISH) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_ITEM_TIMEOUT" }, ZGOPIT_ABOLISH) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({ "ZG_TS_EXECUTE", "ZG_TS_TASK_TIMEOUT" }, ZGOPIT_ABOLISH) = [&](ZG6000::StringMap args, std::string& errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
}

bool ZGOPTaskITTaskUAV::onExecuteStop(ZG6000::StringMap args, std::string& errMsg)
{
    std::string deviceID;
    if (!getUAVDevice(deviceID, errMsg))
        return false;
    return (ctrlCall([&](std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx> ctrlPrx)
        {
            ZG6000::ErrorInfo e;
            if (!ctrlPrx->deviceYs(deviceID, "StopTask", m_id, e))
            {
                errMsg = e.errDetail;
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }));
}

bool ZGOPTaskITTaskUAV::onExecutePause(ZG6000::StringMap args, std::string& errMsg)
{
    std::string deviceID;
    if (!getUAVDevice(deviceID, errMsg))
        return false;
    return (ctrlCall([&](std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx> ctrlPrx)
        {
            ZG6000::ErrorInfo e;
            if (!ctrlPrx->deviceYs(deviceID, "PauseTask", m_id, e))
            {
                errMsg = e.errDetail;
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }));
}

bool ZGOPTaskITTaskUAV::onExecuteResume(ZG6000::StringMap args, std::string& errMsg)
{
    std::string deviceID;
    if (!getUAVDevice(deviceID, errMsg))
        return false;
    return (ctrlCall([&](std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx> ctrlPrx)
        {
            ZG6000::ErrorInfo e;
            if (!ctrlPrx->deviceYs(deviceID, "ResumeTask", m_id, e))
            {
                errMsg = e.errDetail;
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }));
}

bool ZGOPTaskITTaskUAV::onExecuteRetry(ZG6000::StringMap args, std::string &errMsg)
{
    return onExecuteStart(std::move(args), errMsg);
}

bool ZGOPTaskITTaskUAV::onExecuteStart(ZG6000::StringMap args, std::string& errMsg)
{
    std::string deviceID;
    if (!getUAVDevice(deviceID, errMsg))
        return false;
    return (ctrlCall([&](std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx> ctrlPrx)
        {
            ZG6000::ErrorInfo e;
            if (!ctrlPrx->deviceYs(deviceID, "StartTask", m_id, e))
            {
                errMsg = e.errDetail;
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }));
}

bool ZGOPTaskITTaskUAV::getUAVDevice(std::string &deviceID, std::string& errMsg)
{
    QString sql = QString("SELECT DISTINCT b.deviceID FROM op_param_it_task_item a "
                        "RIGHT JOIN mp_param_preset b ON a.presetID = b.id "
                        "WHERE a.taskID = '%1'").arg(m_id.c_str());
    ZG6000::StringList listDeviceID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
    {
        errMsg = QStringLiteral("获取任务'%1'关联巡视设备失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(errMsg.c_str());
		return false;
    }
    if (listDeviceID.empty())
    {
        errMsg = QStringLiteral("任务'%1'未关联巡视设备").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(errMsg.c_str());
        return false;
    }
    deviceID = listDeviceID.front();
    return true;
}

bool ZGOPTaskITTaskUAV::ctrlCall(const std::function<bool(std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx>)> &func)
{
    auto ctrlPrx = ZGProxyMng::instance()->getProxyOPPatrolDeviceCtrl();
    if (ctrlPrx == nullptr)
    {
        ZGLOG_ERROR(QStringLiteral("获取巡检设备控制代理失败"));
        return false;
    }
    try
    {
        return func(ctrlPrx);
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

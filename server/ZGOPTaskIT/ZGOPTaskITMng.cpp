#include "ZGOPTaskITMng.h"

#include <QtConcurrent>

#include "ZGDebugMng.h"
#include "ZGOPTaskITTask.h"
#include "ZGOPTaskITTaskUAV.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "zgerror/ZGOPTaskITError.h"

namespace ZG6000
{

ZGOPTaskITMng::ZGOPTaskITMng(QObject *parent) : QObject{parent}
{
}

void ZGOPTaskITMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGOPTaskITMng::onTimer);
}

void ZGOPTaskITMng::initServerInstConfig()
{
    const auto &serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGOPTaskITMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGOPTaskITMng::initTask()
{
    std::string sql = "SELECT id FROM op_param_task WHERE taskTypeID = 'ZG_TT_IT' OR taskTypeID = 'ZG_TT_IT_UAV'";
    StringList listTask;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listTask))
    {
        ZGLOG_ERROR(QString("获取智能巡检票任务列表失败"));
        return false;
    }
    ZGLOG_TRACE(QString("task size: %1").arg(listTask.size()));
    for (auto &taskID : listTask)
    {
        if (!addTask(taskID))
            return false;
    }
    return true;
}

bool ZGOPTaskITMng::initParams()
{
    std::string sql = "SELECT id, name FROM op_dict_task_state";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTaskState))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务状态信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM op_dict_task_stage";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTaskStage))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务阶段信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM op_dict_task_type";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTaskType))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务类型信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM op_param_it_task_type";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapITType))
    {
        ZGLOG_ERROR(QStringLiteral("获取巡检任务类型信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM op_dict_it_action_type";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapActionType))
    {
        ZGLOG_ERROR(QStringLiteral("获取动作类型信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM op_dict_it_category";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapCategory))
    {
        ZGLOG_ERROR(QStringLiteral("获取巡检类别信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM op_dict_it_defect_level";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDefectLevel))
    {
        ZGLOG_ERROR(QStringLiteral("获取缺陷等级信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM op_dict_it_exec_state";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapExecState))
    {
        ZGLOG_ERROR(QStringLiteral("获取执行状态信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM op_dict_it_item_type";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapItemType))
    {
        ZGLOG_ERROR(QStringLiteral("获取巡检项类型信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM op_dict_it_tour_mode";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapTourMode))
    {
        ZGLOG_ERROR(QStringLiteral("获取巡检模式信息失败"));
        return false;
    }
    sql = "SELECT id, name, voice FROM sp_param_appnode";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapAppNode))
    {
        ZGLOG_ERROR(QStringLiteral("获取应用节点信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM sp_param_subsystem";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapSubsystem))
    {
        ZGLOG_ERROR(QStringLiteral("获取子系统信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM sp_param_major";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapMajor))
    {
        ZGLOG_ERROR(QStringLiteral("获取专业信息失败"));
        return false;
    }
    sql = "SELECT * FROM op_param_it_model";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapModel))
    {
        ZGLOG_ERROR(QStringLiteral("获取巡检模型信息失败"));
        return false;
    }
    sql = "SELECT * FROM op_param_it_model_item ORDER BY modelID";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapItem))
    {
        ZGLOG_ERROR(QStringLiteral("获取巡检模型项信息失败"));
        return false;
    }
    for (auto &[id, item] : m_mapItem)
    {
        m_mapModelItem[item["modelID"]].push_back(id);
    }
    sql = "SELECT * FROM op_param_it_model_action ORDER BY modelItemID, actionIndex";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapAction))
    {
        ZGLOG_ERROR(QStringLiteral("获取巡检模型项动作信息失败"));
        return false;
    }
    for (auto &[id, action] : m_mapAction)
    {
        m_mapItemAction[action["modelItemID"] + "/" + action["tourModeID"]].push_back(id);
    }
    return true;
}

bool ZGOPTaskITMng::initMqttClient()
{
    if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
    {
        ZGLOG_ERROR("initMqttClient error.");
        return false;
    }
    m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
    if (m_pMqttClient == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage error.");
        return false;
    }
    m_pMqttClient->connectToHost();
    return true;
}

bool ZGOPTaskITMng::addTask(const std::string &taskID)
{
    std::string taskTypeID;
    if (!ZGProxyCommon::getDataByField("op_param_task", taskID, "taskTypeID", taskTypeID))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'类型失败").arg(taskID.c_str()));
        return false;
    }
    ZGOPTaskITTask *object;
    if (taskTypeID == "ZG_TT_IT")
        object = new ZGOPTaskITTask(taskID);
    else if (taskTypeID == "ZG_TT_IT_UAV")
        object = new ZGOPTaskITTaskUAV(taskID);
    else
    {
        ZGLOG_ERROR(QStringLiteral("任务'%1'类型'%2'不支持").arg(taskID.c_str()).arg(taskTypeID.c_str()));
        return false;
    }
    ZGLOG_TRACE(QString("intialize task: %1").arg(taskID.c_str()));
    if (!object->initialize())
    {
        ZGLOG_ERROR(QStringLiteral("任务'%1'初始化失败").arg(taskID.c_str()));
        delete object;
        return false;
    }
    QWriteLocker locker(&m_lock);
    if (m_mapTask.find(taskID) == m_mapTask.end())
        m_mapTask[std::move(taskID)] = object;
    else
        delete object;
    return true;
}

void ZGOPTaskITMng::removeTask(const std::string &taskID)
{
    QWriteLocker locker(&m_lock);
    auto pair = m_mapTask.find(std::move(taskID));
    if (pair != m_mapTask.end())
    {
        delete pair->second;
        m_mapTask.erase(pair);
    }
}

ZGOPTaskITTask *ZGOPTaskITMng::fetchTask(std::string taskID)
{
    ZGOPTaskITTask *task = nullptr;
    QReadLocker locker(&m_lock);
    auto pair = m_mapTask.find(std::move(taskID));
    if (pair != m_mapTask.end())
        task = pair->second;
    return task;
}

void ZGOPTaskITMng::expandTask(ZG6000::StringMap &task)
{
    const auto &appNode = ZGUtils::get(m_mapAppNode, task["appNodeID"], {});
    task["appNodeName"] = ZGUtils::get(appNode, "name", "");
    task["subsystemName"] = ZGUtils::get(m_mapSubsystem, task["subsystemID"], "");
    task["majorName"] = ZGUtils::get(m_mapMajor, task["majorID"], "");    
    std::string operUserName, monUserName;
    if (!task["rtOperUserID"].empty())
        ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtOperUserID"], "name", operUserName);
    task["rtOperUserName"] = operUserName;
    if (!task["rtMonUserID"].empty())
        ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtMonUserID"], "name", monUserName);
    task["rtMonUserName"] = monUserName;
    task["rtTaskStageName"] = ZGUtils::get(m_mapTaskStage, task["rtTaskStageID"], "");
    task["rtTaskStateName"] = ZGUtils::get(m_mapTaskState, task["rtTaskStateID"], "");
    ZG6000::StringMap it;
    ZGProxyCommon::getDataByFields("op_param_it_task", task["id"], {"typeID", "rtNumber", "pageID", "rtCurrentItemID", "rtCurrentItemIndex"}, it);
    task["itTypeID"] = ZGUtils::get(it, "typeID", "");
    if (!task["itTypeID"].empty())
        ZGProxyCommon::getDataByField("op_param_it_task_type", task["itTypeID"], "name", task["itTypeName"]);
    task["rtNumber"] = ZGUtils::get(it, "rtNumber", "");
    task["pageID"] = ZGUtils::get(it, "pageID", "");
    task["rtCurrentItemID"] = ZGUtils::get(it, "rtCurrentItemID", "");
    task["rtCurrentItemIndex"] = ZGUtils::get(it, "rtCurrentItemIndex", "");
}

void ZGOPTaskITMng::expandItem(ZG6000::StringMap &item)
{
    const auto &tourModeName = ZGUtils::get(m_mapTourMode, item["tourModeID"], "");
    const auto &itemTypeName = ZGUtils::get(m_mapItemType, item["itemTypeID"], "");
    const auto &categoryName = ZGUtils::get(m_mapCategory, item["categoryID"], "");
    const auto &execStateName = ZGUtils::get(m_mapExecState, item["rtExecStateID"], "");   
    item["tourModeName"] = tourModeName;
    item["itemTypeName"] = itemTypeName;
    item["categoryName"] = categoryName;
    item["rtExecStateName"] = execStateName;
}

void ZGOPTaskITMng::expandAction(ZG6000::StringMap &action)
{
    const auto& actionTypeName = ZGUtils::get(m_mapActionType, action["actionTypeID"], "");
    action["actionTypeName"] = actionTypeName;
    const auto& execStateName = ZGUtils::get(m_mapExecState, action["rtExecStateID"], "");
    action["rtExecStateName"] = execStateName;
    const auto& defectLevelName = ZGUtils::get(m_mapDefectLevel, action["rtDefectLevelID"], "");
    action["rtDefectLevelName"] = defectLevelName;
}

void ZGOPTaskITMng::processUpdateOperation(const std::string &tableName, const ListRecord &listRecord)
{
    for (const auto &record : listRecord)
    {
        try
        {
            std::string taskID;
            if (tableName == "op_param_task" || tableName == "op_param_it_task")
                taskID = ZGUtils::get(record, "id").newValue;
            if (tableName == "op_param_it_task_item")
            {
                const auto &itemID = ZGUtils::get(record, "id").newValue;
                if (!ZGProxyCommon::getDataByField("op_param_it_task_item", itemID, "taskID", taskID))
                    continue;
            }
            if (tableName == "op_param_it_task_action")
            {
                const auto &actionID = ZGUtils::get(record, "id").newValue;
                std::string itemID;
                if (!ZGProxyCommon::getDataByField("op_param_it_task_action", actionID, "itemID", itemID))
                    continue;
                if (!ZGProxyCommon::getDataByField("op_param_it_task_item", itemID, "taskID", taskID))
                    continue;
            }
            if (tableName == "sp_real_exam")
            {
                auto pair = record.find("examStateID");
                if (pair == record.end())
                    continue;
                const auto &examID = ZGUtils::get(record, "id").newValue;
                QString sql = QString("SELECT id FROM op_param_task WHERE rtExamID = '%1'").arg(examID.c_str());
                if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), taskID))
                    continue;
                if (taskID.empty())
                    continue;
            }
            auto task = fetchTask(taskID);
            if (task == nullptr)
            {
                ZGLOG_ERROR(QStringLiteral("任务'%1'不存在").arg(taskID.c_str()));
                continue;
            }
            task->dispatchData(tableName, record);
        }
        catch (const std::exception &e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
}

void ZGOPTaskITMng::processInsertOperation(const std::string &tableName, const ListRecord &listRecord)
{
    for (const auto &record : listRecord)
    {
        try
        {
            if (tableName == "op_param_it_task")
            {
                const auto &taskID = ZGUtils::get(record, "id").newValue;
                std::string taskType;
                QString sql = QString("SELECT taskTypeID FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
                if (ZGProxyCommon::execQuerySqlField(sql.toStdString(), taskType))
                {
                    m_pMqttClient->sendPublish(QString("op_param_task/%1/insert").arg(taskType.c_str()),
                                            QString("%1").arg(taskID.c_str()));
                }
            }           
        }
        catch (const std::exception &e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
}

void ZGOPTaskITMng::processDeleteOperation(const std::string &tableName, const ListRecord &listRecord)
{
    for (const auto &record : listRecord)
    {
        try
        {
            const auto &taskID = ZGUtils::get(record, "id").newValue;
            m_pMqttClient->sendPublish("op_param_task/delete", QString("%1").arg(taskID.c_str()));
        }
        catch (const std::exception &e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
}

bool ZGOPTaskITMng::saveTask(const std::string &taskID, std::string &errMsg)
{
    try
    {
        ZG6000::StringMap task;
        if (!ZGProxyCommon::getDataByID("op_param_task", taskID, task))
        {
            errMsg = QStringLiteral("获取任务'%1'信息失败").arg(taskID.c_str()).toStdString();
            return false;
        }
        ZG6000::StringMap itTask;
        if (!ZGProxyCommon::getDataByID("op_param_it_task", taskID, itTask))
        {
            errMsg = QStringLiteral("获取任务'%1'信息失败").arg(taskID.c_str()).toStdString();
            return false;
        }
        ZG6000::StringMap hisIt;
        for (const auto& [key, value]: task)
        {
            hisIt[key] = value;
        }
        hisIt["taskTypeName"] = ZGUtils::get(m_mapTaskType, hisIt["taskTypeID"], "");
        const auto& appNode = ZGUtils::get(m_mapAppNode, hisIt["appNodeID"]);
        hisIt["appNodeName"] = ZGUtils::get(appNode, "name", "");
        hisIt["subsystemName"] = ZGUtils::get(m_mapSubsystem, hisIt["subsystemID"], "");
        hisIt["majorName"] = ZGUtils::get(m_mapMajor, hisIt["majorID"], "");
        hisIt["rtTaskStageName"] = ZGUtils::get(m_mapTaskStage, hisIt["rtTaskStageID"], "");
        hisIt["rtTaskStateName"] = ZGUtils::get(m_mapTaskState, hisIt["rtTaskStateID"], "");
        std::string operName, monitorName, createrName;
        ZGProxyCommon::getDataByField("sp_param_hrm_user", hisIt["rtOperUserID"], "name", operName);
        ZGProxyCommon::getDataByField("sp_param_hrm_user", hisIt["rtMonUserID"], "name", monitorName);
        ZGProxyCommon::getDataByField("sp_param_hrm_user", hisIt["rtCreateUserID"], "name", createrName);
        hisIt["rtOperUserName"] = operName;
        hisIt["rtMonUserName"] = monitorName;
        hisIt["rtCreateUserName"] = createrName;
        std::string newTaskID;
        if (!ZGProxyCommon::createUUID(newTaskID))
        {
            errMsg = QStringLiteral("生成任务ID失败").toStdString();
            return false;
        }
        hisIt["id"] = newTaskID;
        hisIt["typeID"] = itTask["typeID"];
        hisIt["typeName"] = ZGUtils::get(m_mapITType, hisIt["typeID"], "");
        hisIt["rtNumber"] = itTask["rtNumber"];
        hisIt["pageID"] = itTask["pageID"];
        QDateTime dt;
        if (!ZGUtils::StringToDateTime(hisIt["rtStartTime"].c_str(), dt, true))
        {
            errMsg = QStringLiteral("无效的操作票开始时间'%1'").arg(hisIt["rtStartTime"].c_str()).toStdString();
            return false;
        }
        std::string hisItTable = "op_his_it_task_" + std::to_string(dt.date().year());
        std::string hisItemTable = "op_his_it_task_item_" + std::to_string(dt.date().year());
        std::string hisActionTable = "op_his_it_task_action_" + std::to_string(dt.date().year());
        ZG6000::StringList listSql;
        listSql.push_back(ZGUtils::generateInsertSql(hisItTable, hisIt));
        QString sql = QString("SELECT * FROM op_param_it_task_item WHERE taskID = '%1'").arg(taskID.c_str());
        ZG6000::ListStringMap listItem;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listItem))
        {
            errMsg = QStringLiteral("获取任务'%1'项失败").arg(taskID.c_str()).toStdString();
            return false;
        }
        ZG6000::StringList listItemID;
        if (!ZGProxyCommon::createUUID(static_cast<int>(listItem.size()), listItemID))
        {
            errMsg = QStringLiteral("生成任务项ID失败").toStdString();
            return false;
        }
        int itemIndex = 0;
        for (auto& item: listItem)
        {       
            const auto& itemID = ZGUtils::get(item, "id");
            sql = QString("SELECT * FROM op_param_it_task_action WHERE itemID = '%1'").arg(itemID.c_str());
            item["id"] = listItemID[itemIndex++];
            item["taskID"] = newTaskID;
            std::string deviceName;
            ZGProxyCommon::getDataByField("mp_param_device", item["deviceID"], "name", deviceName);
            item["deviceName"] = deviceName;
            item["tourModeName"] = ZGUtils::get(m_mapTourMode, item["tourModeID"], "");
            item["itemTypeName"] = ZGUtils::get(m_mapItemType, item["itemTypeID"], "");
            item["categoryName"] = ZGUtils::get(m_mapCategory, item["categoryID"], "");
            item["rtExecStateName"] = ZGUtils::get(m_mapExecState, item["rtExecStateID"], "");
            listSql.push_back(ZGUtils::generateInsertSql(hisItemTable, item));
            ZG6000::ListStringMap listAction;
            if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listAction))
            {
                errMsg = QStringLiteral("获取任务'%1'项动作失败").arg(taskID.c_str()).toStdString();
                return false;
            }
            ZG6000::StringList listActionID;
            if (!ZGProxyCommon::createUUID(static_cast<int>(listAction.size()), listActionID))
            {
                errMsg = QStringLiteral("生成任务项动作ID失败").toStdString();
                return false;
            }
            int actionIndex = 0;
            for (auto& action: listAction)
            {
                action["id"] = listActionID[actionIndex++];
                action["itemID"] = item["id"];
                action["actionTypeName"] = ZGUtils::get(m_mapActionType, action["actionTypeID"], "");
                action["rtExecStateName"] = ZGUtils::get(m_mapExecState, action["rtExecStateID"], "");
                action["rtDefectLevelName"] = ZGUtils::get(m_mapDefectLevel, action["rtDefectLevelID"], "");
                listSql.push_back(ZGUtils::generateInsertSql(hisActionTable, action));
            }
        }
        if (!ZGProxyCommon::execBatchSql(listSql, true))
        {
            errMsg = QStringLiteral("保存任务'%1'失败").arg(taskID.c_str()).toStdString();
            return false;
        }
        return true;
    }
    catch(const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        errMsg = e.what();
        return false;
    } 
}

ZGOPTaskITMng *ZGOPTaskITMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGOPTaskITMng;
    return g_pInstance;
}

void ZGOPTaskITMng::init()
{
    initEvents();
    initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    QThread::msleep(QRandomGenerator::global()->bounded(5, 10));
    while (!initParams())
    {
        ZGLOG_ERROR("initParams error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initMqttClient())
    {
        ZGLOG_ERROR("initMqttClient error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initTask())
    {
        ZGLOG_ERROR("initTask error.");
        QThread::msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    m_checkTimer.start(1000);
    ZGLOG_INFO("ZGOPTaskIT init finished.");
}

bool ZGOPTaskITMng::checkState(const Ice::Current &current)
{
    return m_initialized;
}

void ZGOPTaskITMng::dispatchData(const std::string &tableName, const std::string &oper, const std::string &reason,
                                 const std::string &time, const ListRecord &listRecord, const Ice::Current &current)
{
    if (!m_initialized)
        return;
    if (!ZGRuntime::instance()->isMaster())
        return;
    if (reason != "change")
        return;
    if (oper == "update")
        processUpdateOperation(tableName, listRecord);
    else if (oper == "insert")
        processInsertOperation(tableName, listRecord);
    else if (oper == "delete")
        processDeleteOperation(tableName, listRecord);
}

// bool ZGOPTaskITMng::deleteTask(const std::string &taskID, const StringMap &param, ErrorInfo &e,
//                                const Ice::Current &current)
// {
//     return execTaskAction(taskID, param, e, [&](ZGOPTaskITTask *task) {
//         std::string itType;
//         if (!ZGProxyCommon::getDataByField("op_param_it_task", taskID, "typeID", itType))
//         {
//             e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_RT);
//             e.errDetail = QStringLiteral("获取任务'%1'类型失败").arg(taskID.c_str()).toStdString();
//             ZGLOG_ERROR(e);
//             return false;
//         }
//         if (!task->remove(param, e))
//         {
//             ZGLOG_ERROR(e);
//             return false;
//         }
//         if (itType != "ZG_TT_TYPICAL")
//             removeTask(taskID);
//         return true;
//     });
// }

bool ZGOPTaskITMng::deleteTask(const std::string &taskID, const StringMap &param, ErrorInfo &e,
                               const Ice::Current &current)
{
    std::string itType;
    if (!ZGProxyCommon::getDataByField("op_param_it_task", taskID, "typeID", itType))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取任务'%1'类型失败").arg(taskID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    auto pTask = fetchTask(taskID);
    if (pTask != nullptr)
    {
        if (!pTask->remove(param, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
    }
    ZG6000::StringList listSql;
    if (itType == "ZG_TT_TYPICAL")
    {
        ZG6000::StringMap task{{"id", taskID}, {"rtTaskStageID", "ZG_TS_INIT"}, {"rtTaskStateID", "ZG_TS_FINISHED"}};
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    }
    else
    {
        QString sql = QString("SELECT id FROM op_param_it_task_item WHERE taskID = '%1'").arg(taskID.c_str());
        ZG6000::StringList listItemID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listItemID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务'%1'项ID失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        listSql.push_back(QString("DELETE FROM op_param_task WHERE id = '%1';").arg(taskID.c_str()).toStdString());
        listSql.push_back(QString("DELETE FROM op_param_it_task WHERE id = '%1';").arg(taskID.c_str()).toStdString());
        for (const auto &itemID : listItemID)
        {
            listSql.push_back(QString("DELETE FROM op_param_it_task_item WHERE id = '%1';").arg(itemID.c_str()).toStdString());
            listSql.push_back(QString("DELETE FROM op_param_it_task_action WHERE itemID = '%1';").arg(itemID.c_str()).toStdString());
        }
    }
        // listSql.push_back(QString("CALL op_param_it_task_delete('%1');").arg(taskID.c_str()).toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("删除任务'%1'失败").arg(taskID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (itType != "ZG_TT_TYPICAL")
        removeTask(taskID);
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITMng::getTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current)
{
    const auto &condition = ZGUtils::get(param, "condition", "1=1");
    const auto &orderType = ZGUtils::get(param, "order", "ASC");
    const auto &orderField = ZGUtils::get(param, "sort", "a.id");
    const auto &offset = ZGUtils::get(param, "offset", "0");
    const auto &limit = ZGUtils::get(param, "limit", "1000");
    QString addition = QString(" ORDER BY %1 %2 LIMIT %3, %4")
                           .arg(orderField.c_str())
                           .arg(orderType.c_str())
                           .arg(offset.c_str())
                           .arg(limit.c_str());
    QString sql = QString("SELECT * FROM op_param_task a WHERE %1").arg(condition.c_str()) + addition;
    auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
    if (dbProxy == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取数据库代理失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listTask, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        for (auto &task : listTask)
        {
            expandTask(task);
        }
        return true;
    }
    catch (const std::exception &ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_RT);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGOPTaskITMng::startTask(const std::string &taskID, const StringMap &param, ErrorInfo &e,
                              const Ice::Current &current)
{
    return execTaskAction(taskID, param, e, [&](ZGOPTaskITTask *task) {
        if (!task->start(param, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    });
}

bool ZGOPTaskITMng::pauseTask(const std::string &taskID, const StringMap &param, ErrorInfo &e,
                              const Ice::Current &current)
{
    return execTaskAction(taskID, param, e, [&](ZGOPTaskITTask *task) {
        if (!task->pause(param, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    });
}

bool ZGOPTaskITMng::resumeTask(const std::string &taskID, const StringMap &param, ErrorInfo &e,
                               const Ice::Current &current)
{
    return execTaskAction(taskID, param, e, [&](ZGOPTaskITTask *task) {
        if (!task->resume(param, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    });
}

bool ZGOPTaskITMng::retryTask(const std::string &taskID, const StringMap &param, ErrorInfo &e,
                              const Ice::Current &current)
{
    return execTaskAction(taskID, param, e, [&](ZGOPTaskITTask *task) {
        if (!task->retry(param, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    });
}

bool ZGOPTaskITMng::abolishTask(const std::string &taskID, const StringMap &param, ErrorInfo &e,
                                const Ice::Current &current)
{
    return execTaskAction(taskID, param, e, [&](ZGOPTaskITTask *task) {
        if (!task->abolish(param, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    });
}

bool ZGOPTaskITMng::confirmTask(const std::string &taskID, const StringMap &param, ErrorInfo &e,
                                const Ice::Current &current)
{
    return execTaskAction(taskID, param, e, [&](ZGOPTaskITTask *task) {
        if (!task->confirm(param, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    });
}

bool ZGOPTaskITMng::getTaskItems(const std::string &taskID, StringMap &task, ListStringMap &items, ErrorInfo &e,
                                 const Ice::Current &current)
{
    QString sql = QString("SELECT * FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
    ZG6000::ListStringMap listTask;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取任务信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listTask.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("任务不存在").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    task = listTask.front();   
    expandTask(task);
    sql = QString("SELECT a.*, b.name AS deviceName FROM op_param_it_task_item a "
                  "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
                  "WHERE a.taskID = '%1' ORDER BY itemIndex")
              .arg(taskID.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), items))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取任务项信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    for (auto &item : items)
    {
        expandItem(item);
    }
    return true;
}

bool ZGOPTaskITMng::getItemActions(const std::string &itemID, StringMap &item, ListStringMap &actions, ErrorInfo &e,
                                   const Ice::Current &current)
{
    QString sql = QString("SELECT a.*, b.name AS deviceName FROM op_param_it_task_item a "
                          "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
                          "WHERE a.id = '%1'")
                      .arg(itemID.c_str());
    ZG6000::ListStringMap listTask;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取任务项信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listTask.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("任务项不存在").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    item = listTask.front();
    expandItem(item);
    sql = QString("SELECT * FROM op_param_it_task_action WHERE itemID = '%1' ORDER BY actionIndex").arg(itemID.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), actions))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取任务项动作信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    for (auto& action: actions)
    {
        expandAction(action);
    }
    return true;
}

bool ZGOPTaskITMng::getTaskTypeObjects(const StringMap& params, ListStringMap& listObject, ErrorInfo& e, const Ice::Current& current)
{
    QString errMsg;
    if (!ZGUtils::checkRequiredParam(params, {"taskTypeID", "appNodeID"}, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg.toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        const auto& taskTypeID = ZGUtils::get(params, "taskTypeID");
        const auto& appNodeID = ZGUtils::get(params, "appNodeID");
        if (taskTypeID == "ZG_TT_TYPICAL")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("典型任务无可用设备").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QString sql = QString("SELECT DISTINCT modelID FROM op_param_it_task_model_item WHERE taskTypeID = '%1'")
            .arg(taskTypeID.c_str());
        ZG6000::StringList listModelID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listModelID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务类型关联模型失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& models = ZGUtils::join(listModelID, ",", "'", "'");
        sql = QString("SELECT a.id, a.name FROM op_param_it_object a "
            "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
            "LEFT JOIN op_param_it_model c ON a.modelID = c.id "
            "WHERE c.id IN (%1) AND (a.deviceID = '' OR a.deviceID IS NULL) "
            "OR (b.isEnable = 1 AND b.appNodeID = '%2')")
            .arg(models.c_str()).arg(appNodeID.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listObject))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务类型关联设备失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGOPTaskITMng::createTypicalTask(StringMap params, ErrorInfo &e, const Ice::Current &current)
{
    QString errMsg;
    if (!ZGUtils::checkRequiredParam(params, {"taskID", "rtCreateUserID"}, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg.toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    const auto &taskID = params["taskID"];
    QString sql = QString("SELECT id FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
    ZG6000::StringList listTaskID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listTaskID) || listTaskID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("查询任务'%1'失败").arg(taskID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringMap task{
        {"id", taskID},
        {"rtTaskStageID", "ZG_TS_CREATE"},
        {"rtTaskStateID", "ZG_TS_FINISHED"},
        {"rtExamID", ""},
        {"rtCreateUserID", params["rtCreateUserID"]},
        {"rtCreateTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString()},
        {"rtOperUserID", ""},
        {"rtMonUserID", ""},
        {"rtStartTime", ""},
        {"rtEndTime", ""},
        {"rtExecStartTime", ""},
        {"rtExecEndTime", ""},
        {"rtErrorDesc", ""}};
    ZG6000::StringList listSql;
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    ZG6000::StringMap it{{"id", taskID}, {"rtNumber", ""}, {"rtCurrentItemID", ""}, {"rtCurrentItemIndex", "0"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task", it));
    ZG6000::StringMap item{{"rtStartTime", ""},           {"rtEndTime", ""},
                           {"rtExecStateID", ""},         {"rtCurrentActionID", ""},
                           {"rtCurrentActionIndex", "0"}, {"rtErrorDesc", ""}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task_item", item, "taskID = '" + taskID + "'"));
    sql = QString("SELECT id FROM op_param_it_task_item WHERE taskID = '%1'").arg(taskID.c_str());
    ZG6000::StringList listTaskItemID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listTaskItemID))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = "查询任务项失败";
        ZGLOG_ERROR(e);
        return false;
    }
    for (const auto &taskItemID : listTaskItemID)
    {
        ZG6000::StringMap action{{"rtExecTime", ""},      {"rtExecStateID", ""}, {"rtPropertyValue", ""},
                                 {"rtDefectLevelID", ""}, {"rtCheckResult", ""}, {"rtIdentityStateID", ""}};
        listSql.push_back(
            ZGUtils::generateUpdateSql("op_param_it_task_action", action, "itemID = '" + taskItemID + "'"));
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = "创建典型任务失败";
        ZGLOG_ERROR(e);
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITMng::createSpecialTask(const std::string &taskTypeID, const StringList &listObjectID, StringMap params,
                                      std::string &taskID, ErrorInfo &e, const Ice::Current &current)
{
    QString errMsg;
    if (!ZGUtils::checkRequiredParam(params, {"appNodeID", "subsystemID", "majorID", "rtCreateUserID", "typeID"}, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg.toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listObjectID.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = "没有选择巡检对象";
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringMap itemMode;
    QString sql =
        QString("SELECT modelItemID AS id, tourModeID FROM op_param_it_task_model_item WHERE taskTypeID = '%1' ORDER BY modelItemID")
            .arg(params["typeID"].c_str());
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), itemMode))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = "查询模型项巡检模式失败";
        ZGLOG_ERROR(e);
        return false;
    }
    if (!ZGProxyCommon::createUUID(taskID))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = "生成任务ID失败";
        ZGLOG_ERROR(e);
        return false;
    }  
    ZG6000::StringList listSql;
    std::string examID;
    ZGProxyCommon::getDataByField("op_param_it_task_type", taskTypeID, "examID", examID);
    ZG6000::StringMap task{
        {"id", taskID},
        {"taskTypeID", taskTypeID},
        {"rtTaskStageID", "ZG_TS_CREATE"},
        {"rtTaskStateID", "ZG_TS_FINISHED"},
        {"rtCreateTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString()},
        {"rtExamID", examID}};
    for (const auto& [key, value]: params)
    {
        if ((key != "rtNumber") && (key != "pageID") && (key != "typeID"))
            task[key] = value;
    }
    listSql.push_back(ZGUtils::generateInsertSql("op_param_task", task));
    std::string pageID, rtNumber;
    if (params.find("pageID") != params.end())
    {
        pageID = params["pageID"];
    }
    if (params.find("rtNumber") != params.end())
    {
        rtNumber = params["rtNumber"];
    }
    ZG6000::StringMap taskIT{{"id", taskID}, {"typeID", params["typeID"]}, {"pageID", pageID}, {"rtNumber", rtNumber}};
    listSql.push_back(ZGUtils::generateInsertSql("op_param_it_task", taskIT));
    const auto &objectIDs = ZGUtils::join(listObjectID, ",", "'", "'");
    sql = QString("SELECT * FROM op_param_it_object WHERE id IN (%1) "
                  "ORDER BY FIELD(id, %1)")
              .arg(objectIDs.c_str());
    ZG6000::ListStringMap listObject;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listObject))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = "查询巡检对象失败";
        ZGLOG_ERROR(e);
        return false;
    }
    int itemIndex = 1;
    for (const auto &object : listObject)
    {
        try
        {
            const auto &modelID = ZGUtils::get(object, "modelID");
            const auto &objectID = ZGUtils::get(object, "id");
            const auto &deviceID = ZGUtils::get(object, "deviceID");
            ZG6000::StringMap deviceParam;
            ZGProxyCommon::getDataByFields("mp_param_device", deviceID, {"name", "voice"}, deviceParam);
            auto it = m_mapModelItem.find(modelID);
            if (it == m_mapModelItem.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("模型'%1'未找到巡检项").arg(modelID.c_str()).toStdString();
                ZGLOG_WARN(e);
                continue;
            }
            const auto &listModelItemID = it->second;
            ZG6000::StringList listTaskItemID;
            if (!ZGProxyCommon::createUUID(static_cast<int>(listModelItemID.size()), listTaskItemID))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("生成巡检项ID失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            int itemNo = 0;
            for (const auto &modelItemID : listModelItemID)
            {
                auto itModelItem = m_mapItem.find(modelItemID);
                if (itModelItem == m_mapItem.end())
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("巡检项'%1'未找到").arg(modelItemID.c_str()).toStdString();
                    ZGLOG_WARN(e);
                    return false;
                }
                const auto &tourModeID = ZGUtils::get(itemMode, modelItemID);
                const auto &modelItem = itModelItem->second;
                auto itemName = ZGUtils::get(modelItem, "name");
                const auto& listVarName = ZGUtils::extractVariables(itemName);
                if (!listVarName.empty())
                {
                    std::string variable = listVarName[0];
                    ZGUtils::replaceString(itemName, "[" + variable + "]", deviceParam["name"]);
                }
                auto itemVoice = ZGUtils::get(modelItem, "voice");
                const auto& listVarVoice = ZGUtils::extractVariables(itemVoice);
                if (!listVarVoice.empty())
                {
                    std::string variable = listVarVoice[0];
                    ZGUtils::replaceString(itemVoice, "[" + variable + "]", deviceParam["voice"]);
                }
                ZG6000::StringMap item{{"id", listTaskItemID[itemNo++]},
                                       {"name", itemName},
                                       {"voice", itemVoice},
                                       {"taskID", taskID},
                                       {"itemIndex", std::to_string(itemIndex++)},
                                       {"deviceID", deviceID},
                                       {"tourModeID", tourModeID},
                                       {"itemTypeID", ZGUtils::get(modelItem, "itemTypeID")},
                                       {"categoryID", ZGUtils::get(modelItem, "categoryID")},
                                       {"isAutoTour", ZGUtils::get(modelItem, "isAutoTour")},
                                       {"timeout", ZGUtils::get(modelItem, "timeout")},
                                       {"operGuide", ZGUtils::get(modelItem, "operGuide")}};
                listSql.push_back(ZGUtils::generateInsertSql("op_param_it_task_item", item));
                const auto &key = modelItemID + "/" + tourModeID;
                it = m_mapItemAction.find(key);
                if (it != m_mapItemAction.end())
                {
                    sql = QString("SELECT a.id, a.presetID, a.actionParam, a.enableIdentity, a.identityParam, "
                                  "b.actionIndex, b.actionTypeID, b.waitSeconds, b.propertyName, "
                                  "b.evalExpressionID, b.evalExpressionParam, b.isManualCheck FROM "
                                  "op_param_it_object_action a "
                                  "LEFT JOIN op_param_it_model_action b ON a.modelActionID = b.id "
                                  "LEFT JOIN op_param_it_model_item c ON b.modelItemID = c.id "
                                  "WHERE a.objectID = '%1' AND b.tourModeID = '%2' AND c.id = '%3' ORDER BY "
                                  "b.actionIndex")
                              .arg(objectID.c_str())
                              .arg(tourModeID.c_str())
                              .arg(modelItemID.c_str());
                    ZG6000::ListStringMap listActionParam;
                    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listActionParam))
                    {
                        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
                        e.errDetail = QStringLiteral("查询动作参数失败").toStdString();
                        ZGLOG_ERROR(e);
                        return false;
                    }
                    ZG6000::StringList listTaskActionID;
                    if (!ZGProxyCommon::createUUID(static_cast<int>(listActionParam.size()), listTaskActionID))
                    {
                        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
                        e.errDetail = QStringLiteral("生成动作参数ID失败").toStdString();
                        ZGLOG_ERROR(e);
                        return false;
                    }
                    int actionIndex = 0;
                    for (const auto &actionParam : listActionParam)
                    {
                        ZG6000::StringMap action{
                            {"id", listTaskActionID[actionIndex++]},
                            {"itemID", item["id"]},
                            {"actionIndex", ZGUtils::get(actionParam, "actionIndex")},
                            {"actionTypeID", ZGUtils::get(actionParam, "actionTypeID")},
                            {"presetID", ZGUtils::get(actionParam, "presetID")},
                            {"actionParam", ZGUtils::get(actionParam, "actionParam")},
                            {"waitSeconds", ZGUtils::get(actionParam, "waitSeconds")},
                            {"propertyName", ZGUtils::get(actionParam, "propertyName")},
                            {"evalExpressionID", ZGUtils::get(actionParam, "evalExpressionID")},
                            {"evalExpressionParam", ZGUtils::get(actionParam, "evalExpressionParam")},
                            {"isManualCheck", ZGUtils::get(actionParam, "isManualCheck")},
                            {"enableIdentity", ZGUtils::get(actionParam, "enableIdentity")},
                            {"identityParam", ZGUtils::get(actionParam, "identityParam")}};
                        listSql.push_back(ZGUtils::generateInsertSql("op_param_it_task_action", action));
                    }
                }
            }
        }
        catch (const std::exception &ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建任务失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    QThread::msleep(100);
    ZGProxyCommon::synchronize();
    addTask(taskID);
    return true;
}

bool ZGOPTaskITMng::createUAVTask(const StringMap& task, const ListStringMap& listPreset, const ListStringMap& listItem, const ListStringMap& listAction, ErrorInfo& e, const Ice::Current& current)
{
    ZG6000::StringMap totalTask, ipsTask;
    for (const auto& pair : task)
    {
        if (m_setTaskFields.find(pair.first) != m_setTaskFields.end())
            totalTask.insert({ pair.first, pair.second });
        if (m_setITFields.find(pair.first) != m_setITFields.end())
            ipsTask.insert({ pair.first, pair.second });
    }
    ZG6000::StringList listSql;
    if (!totalTask.empty())
        listSql.push_back(ZGUtils::generateInsertSql("op_param_task", totalTask));
    if (!ipsTask.empty())
        listSql.push_back(ZGUtils::generateInsertSql("op_param_it_task", ipsTask));
    for (const auto& devicePreset : listPreset)
    {
        listSql.push_back(ZGUtils::generateInsertSql("mp_param_preset", devicePreset));
    }
    for (const auto& taskItem : listItem)
    {
        listSql.push_back(ZGUtils::generateInsertSql("op_param_it_task_item", taskItem));
    }
    for (const auto& taskItemAction : listAction)
    {
        listSql.push_back(ZGUtils::generateInsertSql("op_param_it_task_action", taskItemAction));
    }
    if (!listSql.empty())
    {
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITMng::createCustomTask(const ListStringMap &listItem, StringMap params, std::string &taskID, ErrorInfo &e,
                                     const Ice::Current &current)
{
    QString errMsg;
    if (!ZGUtils::checkRequiredParam(params, {"appNodeID", "subsystemID", "majorID", "rtCreateUserID"}, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg.toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listItem.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = "没有选择巡检项";
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringList listSql;
    if (!ZGProxyCommon::createUUID(taskID))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("生成任务ID失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        const auto &appNodeID = ZGUtils::get(params, "appNodeID");
        const auto &subsystemID = ZGUtils::get(params, "subsystemID");
        const auto &majorID = ZGUtils::get(params, "majorID");
        const auto &createUserID = ZGUtils::get(params, "rtCreateUserID");
        ZG6000::StringMap task{{"id", taskID},
                               {"appNodeID", appNodeID},
                               {"subsystemID", subsystemID},
                               {"majorID", majorID},
                               {"rtCreateUserID", createUserID}};
        listSql.push_back(ZGUtils::generateInsertSql("op_param_task", task));
        ZG6000::StringMap it{{"id", taskID}, {"typeID", "ZG_TT_CUSTOM"}};
        listSql.push_back(ZGUtils::generateInsertSql("op_param_it_task", it));
    }
    catch (const std::exception &ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringList listItemID;
    if (!ZGProxyCommon::createUUID(static_cast<int>(listItem.size()), listItemID))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("生成巡检项ID失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    int itemIndex = 0;
    for (const auto &item : listItem)
    {
        if (!ZGUtils::checkRequiredParam(item, {"id", "deviceID", "modelItemID", "tourModeID"}, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
            e.errDetail = errMsg.toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            const auto &deviceID = ZGUtils::get(item, "deviceID");
            const auto &modelItemID = ZGUtils::get(item, "modelItemID");
            const auto &tourModeID = ZGUtils::get(item, "tourModeID");
            ZG6000::StringMap modelItem;
            if (!ZGProxyCommon::getDataByID("op_param_it_model_item", modelItemID, modelItem))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("获取巡检项数据失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            ZG6000::StringMap taskItem{{"id", listItemID[itemIndex++]},
                                       {"taskID", taskID},
                                       {"itemIndex", std::to_string(itemIndex)},
                                       {"tourModeID", tourModeID},
                                       {"deviceID", deviceID}};
            taskItem["name"] = modelItem["name"];
            taskItem["voice"] = modelItem["voice"];
            taskItem["itemTypeID"] = modelItem["itemTypeID"];
            taskItem["categoryID"] = modelItem["categoryID"];
            taskItem["isAutoTour"] = modelItem["isAutoTour"];
            taskItem["timeout"] = modelItem["timeout"];
            taskItem["operGuide"] = modelItem["operGuide"];
            listSql.push_back(ZGUtils::generateInsertSql("op_param_it_task_item", taskItem));
            QString sql = QString("SELECT a.presetID, a.actionParam, a.enableIdentity, a.identityParam, b.name, b.actionIndex, "
                        "b.actionTypeID, b.waitSeconds, b.propertyName, b.evalExpressionID, "
                        "b.evalExpressionParam, b.isManualCheck FROM op_param_it_object_action a "
                        "LEFT JOIN op_param_it_model_action b ON a.modelActionID = b.id "
                        "LEFT JOIN op_param_it_object c ON a.objectID = c.id "
                        "WHERE c.deviceID = '%1' AND b.modelItemID = '%2' AND b.tourModeID = '%3'")
                        .arg(deviceID.c_str()).arg(modelItemID.c_str()).arg(tourModeID.c_str());
            ZG6000::ListStringMap listAction;
            if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listAction))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("获取巡检项动作失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            ZG6000::StringList listActionID;
            if (!ZGProxyCommon::createUUID(static_cast<int>(listAction.size()), listActionID))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("生成巡检项动作ID失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            int actionIndex = 0;
            for (auto &action : listAction)
            {
                action["id"] = listActionID[actionIndex++];
                listSql.push_back(ZGUtils::generateInsertSql("op_param_it_task_action", action));
            }
        }
        catch (const std::exception &ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("创建自定义任务失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskITMng::editTask(const std::string &taskID, const StringMap &head, const ListStringMap &items, ErrorInfo &e,
                             const Ice::Current &current)
{
    return execTaskAction(taskID, head, e, [&](ZGOPTaskITTask *task) { return task->edit(head, items, e); });
}

bool ZGOPTaskITMng::convertTask(const std::string &taskID, const StringMap &params, ErrorInfo &e,
                                const Ice::Current &current)
{
    return execTaskAction(taskID, params, e, [&](ZGOPTaskITTask *task) { return task->convert(params, e); });
}

bool ZGOPTaskITMng::updateTask(const ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current)
{
    ZG6000::StringList listSql;
    for (const auto &task : listTask)
    {
        QString errMsg;
        if (!ZGUtils::checkRequiredParam(task, {"id"}, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
            e.errDetail = errMsg.toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZG6000::StringMap taskTotal, taskIT;
        for (const auto &pair : task)
        {
            if (m_setTaskFields.find(pair.first) != m_setTaskFields.end())
                taskTotal[pair.first] = pair.second;
            if (m_setITFields.find(pair.first) != m_setITFields.end())
                taskIT[pair.first] = pair.second;
        }
        if (!taskTotal.empty())
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskTotal));
        if (!taskIT.empty())
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task", taskIT));
    }
    if (!listSql.empty())
    {
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
    }
    return true;
}

bool ZGOPTaskITMng::updateItem(const ListStringMap &listItem, ErrorInfo &e, const Ice::Current &current)
{
    ZG6000::StringList listSql;
    for (const auto &item : listItem)
    {
        QString errMsg;
        if (!ZGUtils::checkRequiredParam(item, {"id"}, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
            e.errDetail = errMsg.toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task_item", item));
    }
    if (!listSql.empty())
    {
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新任务项失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
    }
    return true;
}

bool ZGOPTaskITMng::updateAction(const ListStringMap &listAction, ErrorInfo &e, const Ice::Current &current)
{
    ZG6000::StringList listSql;
    for (const auto &action : listAction)
    {
        QString errMsg;
        if (!ZGUtils::checkRequiredParam(action, {"id"}, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
            e.errDetail = errMsg.toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task_action", action));
    }
    if (!listSql.empty())
    {
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新任务动作失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
    }
    return true;
}

bool ZGOPTaskITMng::downloadTask(StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ListStringMap& listAction, ErrorInfo& e, const Ice::Current& current)
{
    std::string tasks = ZGUtils::join(listTaskID, ",", "'", "'");
    QString sql = QString("SELECT a.*, b.rtCurrentItemID, b.rtCurrentItemIndex, b.rtNumber, b.typeID FROM op_param_task a "
                    "LEFT JOIN op_param_it_task b ON a.id = b.id WHERE a.id IN (%1)").arg(tasks.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("查询任务失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    sql = QString("SELECT * FROM op_param_it_task_item WHERE taskID IN (%1) ORDER BY taskID, itemIndex").arg(tasks.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listItem))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("查询任务项失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    sql = QString("SELECT * FROM op_param_it_task_action WHERE itemID IN (SELECT id FROM op_param_it_task_item WHERE taskID IN (%1)) "
                  "ORDER BY itemID, actionIndex").arg(tasks.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listAction))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("查询任务动作失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

void ZGOPTaskITMng::publishMessage(const QString &topic, const QString &msg)
{
    if (m_pMqttClient)
        m_pMqttClient->sendPublish(topic, msg);
}

bool ZGOPTaskITMng::execTaskAction(const std::string &taskID, const StringMap &param, ErrorInfo &e,
                                   std::function<bool(ZGOPTaskITTask *)> func)
{
    auto task = fetchTask(taskID);
    if (task == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("找不到可用的任务'%1'").arg(taskID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return func(task);
}

void ZGOPTaskITMng::onTimer()
{
    if (!ZGRuntime::instance()->isMaster())
        return;
    std::vector<ZGOPTaskITTask *> listTask;
    {
        QReadLocker locker(&m_lock);
        for (const auto &task : m_mapTask)
        {
            listTask.push_back(task.second);
        }
    }
    for (const auto &task : listTask)
    {
        auto future = QtConcurrent::run([=](){ task->onTimer(); });
    }
}

} // namespace ZG6000

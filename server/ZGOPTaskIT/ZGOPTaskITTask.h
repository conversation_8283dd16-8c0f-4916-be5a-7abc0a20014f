#ifndef ZGOPTASKITTASK_H
#define ZGOPTASKITTASK_H

#include <QObject>
#include <QMutex>
#include "ZGServerCommon.h"
#include "ZGFSM.hpp"

class ZGOPTaskITItem;
class ZGOPTaskITTask : public QObject
{
    Q_OBJECT
public:
    explicit ZGOPTaskITTask(const std::string& id, QObject *parent = nullptr);
    bool initialize();
    void dispatchData(const std::string& tableName, const ZG6000::MapField& record);
    bool confirm(const ZG6000::StringMap& params, ZG6000::ErrorInfo& e);
    bool start(const ZG6000::StringMap& params, ZG6000::ErrorInfo& e);
    bool stop(const ZG6000::StringMap& params, ZG6000::ErrorInfo& e);
    bool pause(const ZG6000::StringMap& params, ZG6000::ErrorInfo& e);
    bool resume(const ZG6000::StringMap& params, ZG6000::ErrorInfo& e);
    bool retry(const ZG6000::StringMap& params, ZG6000::ErrorInfo& e);
    bool abolish(const ZG6000::StringMap& params, ZG6000::ErrorInfo& e);
    bool edit(const ZG6000::StringMap& params, const ZG6000::ListStringMap& items, ZG6000::ErrorInfo& e);
    bool remove(const ZG6000::StringMap& params, ZG6000::ErrorInfo& e);
    bool convert(const ZG6000::StringMap& params, ZG6000::ErrorInfo& e);
    bool currentState(std::string& state) const;
    bool setCurrentState(const std::string& state);
    bool currentStage(std::string& stage) const;
    bool setCurrentStage(const std::string& stage);
    bool notify(const std::string& action, ZG6000::StringMap args, std::string& errMsg);
    void updateFSMStage(const std::string& stage);
    void updateFSMState(const std::string& state);
    virtual void onTimer();
    bool nextItem();

protected:
    virtual void registerAction();
    bool initTaskContext();
    void processTaskChange(const ZG6000::MapField& record);
    void processITTaskChange(const ZG6000::MapField& record);
    void processItemChange(const ZG6000::MapField& record);
    void processActionChange(const ZG6000::MapField& record);
    void processExamChange(const ZG6000::MapField& record);
    bool execAction(std::string action, ZG6000::StringMap param, ZG6000::ErrorInfo& e);
    virtual bool onCreateConfirm(ZG6000::StringMap args, std::string& errMsg);
    virtual bool onExamConfirm(ZG6000::StringMap args, std::string& errMsg);
    virtual bool onExecuteStart(ZG6000::StringMap args, std::string& errMsg);
    virtual bool onExecuteStop(ZG6000::StringMap args, std::string& errMsg);
    virtual bool onExecutePause(ZG6000::StringMap args, std::string& errMsg);
    virtual bool onExecuteResume(ZG6000::StringMap args, std::string& errMsg);
    virtual bool onExecuteRetry(ZG6000::StringMap args, std::string& errMsg);
    virtual bool onExecuteConfirm(ZG6000::StringMap args, std::string& errMsg);
    virtual bool onExecuteItemTimeout(ZG6000::StringMap args, std::string& errMsg);
    virtual bool onExecuteItemError(ZG6000::StringMap args, std::string& errMsg);
    virtual bool onAbolish(ZG6000::StringMap args, std::string& errMsg);
    bool checkTaskValid(std::string& errMsg);
    bool updateExecuteReady(ZG6000::StringMap args, std::string& errMsg);
    bool createExam(ZG6000::StringMap args, std::string& errMsg);
    bool finishExam(ZG6000::StringMap args, std::string& errMsg);
    bool archiveTask(std::string& errMsg);
    bool deleteExam(ZG6000::ErrorInfo &e);

protected:
    std::string m_id;
    class State {
    public:
        State() = default;
        State(std::string stage, std::string state) : stage_(std::move(stage)), state_(std::move(state)) {}
        std::string to_string() const {
            return stage_ + ":" + state_;
        }
        bool operator<(const State& rhs) const {
            return stage_ < rhs.stage_ || (stage_ == rhs.stage_ && state_ < rhs.state_);
        }
        bool operator==(const State& rhs) const {
            return stage_ == rhs.stage_ && state_ == rhs.state_;
        }
        std::string stage_;
        std::string state_;
    };
    ZGFSM<State, std::string> fsm;
    ZGOPTaskITItem* m_currentItem{nullptr};
    std::vector<std::pair<std::string, std::string>> m_listPairState{ {"ZG_TS_READY", "ZG_ES_READY"}, {"ZG_TS_EXECUTING", "ZG_ES_EXAM"},
		{"ZG_TS_FINISHED", "ZG_ES_ACCEPT"}, {"ZG_TS_STOPPED", "ZG_ES_REJECT"} };
	ZG6000::StringMap m_mapExamState{ {"ZG_TS_READY", "ZG_ES_READY"}, {"ZG_TS_EXECUTING", "ZG_ES_EXAM"},
		{"ZG_TS_FINISHED", "ZG_ES_ACCEPT"}, {"ZG_TS_STOPPED", "ZG_ES_REJECT"}};
    QMutex m_mutex;
};

#endif // ZGOPTASKITTASK_H

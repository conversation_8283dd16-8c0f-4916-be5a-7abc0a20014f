#include "ZGOPTaskITItem.h"
#include "ZGOPTaskITAction.h"
#include "ZGOPTaskITDefine.h"
#include "ZGOPTaskITTask.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"

ZGOPTaskITItem::ZGOPTaskITItem(QObject *parent) : QObject{parent}
{
    m_currentAction = new ZGOPTaskITAction(this);
}

bool ZGOPTaskITItem::initialize()
{
    if (!m_id.empty())
    {
        ZG6000::StringMap item;
        if (!ZGProxyCommon::getDataByID("op_param_it_task_item", m_id, item))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务项'%1'信息失败").arg(m_id.c_str()));
            return false;
        }
        try
        {
            const auto &rtExecStateID = ZGUtils::get(item, "rtExecStateID");
            if (rtExecStateID == "ZG_IES_FINISH")
            {
                ZGOPTaskITTask *task = dynamic_cast<ZGOPTaskITTask *>(parent());
                ZGLOG_TRACE("initalize nextItem");
                return task->nextItem();
            }
            const auto &rtCurrentActionID = ZGUtils::get(item, "rtCurrentActionID");
            if (!rtCurrentActionID.empty())
            {
                m_currentAction->setId(rtCurrentActionID);
                if (!m_currentAction->initialize())
                {
                    ZGLOG_ERROR(QStringLiteral("初始化任务项'%1'当前动作失败").arg(m_id.c_str()));
                    return false;
                }
            }
            const auto &rtStartTime = ZGUtils::get(item, "rtStartTime");
            if (!rtStartTime.empty())
                ZGUtils::StringToDateTime(rtStartTime.c_str(), m_lastStartTime, true);
        }
        catch (const std::exception &e)
        {
            ZGLOG_ERROR(e.what());
            return false;
        }
    }
    return true;
}

void ZGOPTaskITItem::setId(const std::string &id)
{
    if (id != m_id)
        m_id = id;
}

bool ZGOPTaskITItem::currentState(std::string &state) const
{
    if (!ZGProxyCommon::getDataByField("op_param_it_task_item", m_id, "rtExecStateID", state))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务项'%1'状态失败").arg(m_id.c_str()));
        return false;
    }
    return true;
}

bool ZGOPTaskITItem::setCurrentState(const std::string &state)
{
    ZG6000::StringMap item{{"id", m_id}, {"rtExecStateID", state}};
    std::string sql = ZGUtils::generateUpdateSql("op_param_it_task_item", item);
    if (!ZGProxyCommon::execSql(sql))
    {
        ZGLOG_ERROR(QStringLiteral("更新任务项'%1'状态失败").arg(m_id.c_str()));
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

void ZGOPTaskITItem::updateCurrentActionID(const std::string &actionID)
{
    if (m_currentAction != nullptr)
        m_currentAction->setId(actionID);
}

bool ZGOPTaskITItem::setItemError(const std::string &errorDesc)
{
    ZG6000::StringMap item{{"id", m_id}, {"rtExecStateID", "ZG_IES_ERROR"}, {"rtErrorDesc", errorDesc}};
    std::string sql = ZGUtils::generateUpdateSql("op_param_it_task_item", item);
    if (!ZGProxyCommon::execSql(sql))
    {
        ZGLOG_ERROR(QStringLiteral("更新任务项'%1'状态失败").arg(m_id.c_str()));
        return false;
    }
    ZGOPTaskITTask *task = dynamic_cast<ZGOPTaskITTask *>(parent());
    std::string errMsg;
    std::string newErrorDesc =
        QStringLiteral("任务项%1执行失败，原因：%2").arg(m_id.c_str()).arg(errorDesc.c_str()).toStdString();
    ZG6000::StringMap errorItem{{"id", m_id}, {"rtErrorDesc", newErrorDesc}};
    task->notify(ZGOPIT_EXEC_ERROR, errorItem, errMsg);
    return true;
}

bool ZGOPTaskITItem::nextAction()
{
    std::string currentIndex;
    if (!ZGProxyCommon::getDataByField("op_param_it_task_item", m_id, "rtCurrentActionIndex", currentIndex))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务项'%1'当前动作失败").arg(m_id.c_str()));
        return false;
    }
    int nextIndex = currentIndex.empty() ? 1 : std::stoi(currentIndex) + 1;
    QString sql = QString("SELECT id FROM op_param_it_task_action WHERE itemID = '%1' AND actionIndex = %2")
                      .arg(m_id.c_str())
                      .arg(nextIndex);
    ZG6000::StringList listActionId;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listActionId))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务项'%1'下一动作失败").arg(m_id.c_str()));
        return false;
    }
    if (listActionId.empty())
    {
        ZGLOG_TRACE(QStringLiteral("任务项'%1'执行完毕，进入下一任务项").arg(m_id.c_str()));
        ZG6000::StringMap item{
            {"id", m_id},
            {"rtExecStateID", "ZG_IES_FINISH"},
            {"rtEndTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString()}};
        if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql("op_param_it_task_item", item)))
        {
            ZGLOG_ERROR(QStringLiteral("更新任务项'%1'状态失败").arg(m_id.c_str()));
            return false;
        }
    }
    else
    {
        const auto &nextActionId = listActionId.front();
        ZGLOG_TRACE(QString("nextActionId: '%1', nextIndex: %2").arg(nextActionId.c_str()).arg(nextIndex));
        ZG6000::StringMap item{
            {"id", m_id}, {"rtCurrentActionIndex", std::to_string(nextIndex)}, {"rtCurrentActionID", nextActionId}};
        if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql("op_param_it_task_item", item)))
        {
            ZGLOG_ERROR(QStringLiteral("更新任务项'%1'当前动作失败").arg(m_id.c_str()));
            return false;
        }
    }
    ZGProxyCommon::synchronize();
    return true;
}

void ZGOPTaskITItem::onTimer(const ZG6000::StringMap &task)
{
    if (!checkCondition(task))
        return;
    if (m_id.empty())
    {
        ZGLOG_TRACE("empty itemID");
        return;
    }
    ZG6000::StringMap item;
    if (!ZGProxyCommon::getDataByID("op_param_it_task_item", m_id, item))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务项'%1'信息失败").arg(m_id.c_str()));
        return;
    }
    QDateTime now = QDateTime::currentDateTime();
    try
    {
        const auto &timeout = ZGUtils::get(item, "timeout");
        if (!timeout.empty())
        {
            int timeoutSecs = std::atoi(timeout.c_str());
            if (m_lastStartTime.secsTo(now) > timeoutSecs)
            {
                if (setCurrentState("ZG_IES_TIMEOUT"))
                {
                    ZGOPTaskITTask *pTask = dynamic_cast<ZGOPTaskITTask *>(parent());
                    std::string errMsg;
                    pTask->notify(ZGOPIT_EXEC_TIMEOUT, {}, errMsg);
                    return;
                }
            }
        }
        const auto &rtCurrentActionID = ZGUtils::get(item, "rtCurrentActionID");
        if (rtCurrentActionID != m_currentAction->id())
            m_currentAction->setId(rtCurrentActionID);
        const auto &itemState = ZGUtils::get(item, "rtExecStateID");
        if (itemState.empty() || itemState == "ZG_IES_READY")
            processReadyState(item, now);
        else if (itemState == "ZG_IES_EXECUTE")
            m_currentAction->onTimer(item);
        else if (itemState == "ZG_IES_FINISH")
            processFinishState(item, now);
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGOPTaskITItem::processReadyState(const ZG6000::StringMap &item, QDateTime &time)
{
    try
    {
        ZG6000::StringMap newItem{{"id", m_id},
                                  {"rtExecStateID", "ZG_IES_EXECUTE"},
                                  {"rtStartTime", ZGUtils::DateTimeToString(time, true).toStdString()}};
        if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql("op_param_it_task_item", newItem)))
        {
            ZGLOG_ERROR(QStringLiteral("更新任务项'%1'状态失败").arg(m_id.c_str()));
            return;
        }
        updateLastStartTime();
        const auto &isAutoTour = ZGUtils::get(item, "isAutoTour");
        if (isAutoTour != "1")
        {
            ZGOPTaskITTask *task = dynamic_cast<ZGOPTaskITTask *>(parent());
            task->setCurrentState("ZG_TS_PAUSED");
        }
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}
void ZGOPTaskITItem::processFinishState(const ZG6000::StringMap &item, QDateTime &time)
{
    ZGOPTaskITTask *task = dynamic_cast<ZGOPTaskITTask *>(parent());
    ZGLOG_TRACE("finish nextItem");
    task->nextItem();
}

bool ZGOPTaskITItem::checkCondition(const ZG6000::StringMap &task)
{
    try
    {
        const auto &taskStageID = ZGUtils::get(task, "rtTaskStageID");
        const auto &taskStateID = ZGUtils::get(task, "rtTaskStateID");
        return (taskStateID == "ZG_TS_EXECUTING") && (taskStageID == "ZG_TS_EXECUTE");
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

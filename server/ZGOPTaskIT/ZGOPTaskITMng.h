#ifndef ZG6000_ZGOPTASKITMNG_H
#define ZG6000_ZGOPTASKITMNG_H

#include <QObject>
#include <Ice/Ice.h>
#include <QTimer>
#include <QReadWriteLock>

#include "ZGOPTaskITTask.h"
#include "ZGServerCommon.h"
#include "ZGUtils.h"

class ZGMqttClient;
class ZGOPTaskITTask;
namespace ZG6000 {

class ZGOPTaskITMng : public QObject
{
    Q_OBJECT
public:
    static ZGOPTaskITMng* instance();
    void init();
    bool checkState(const Ice::Current &current);
    void dispatchData(const std::string& tableName, const std::string& oper, const std::string& reason,
                      const std::string& time, const ListRecord& listRecord, const Ice::Current &current);

    bool deleteTask(const std::string& taskID, const StringMap& param, ErrorInfo &e, const Ice::Current &current);
    bool getTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current);
    bool startTask(const std::string& taskID, const StringMap& param, ErrorInfo &e, const Ice::Current &current);
    bool pauseTask(const std::string& taskID, const StringMap& param, ErrorInfo &e, const Ice::Current &current);
    bool resumeTask(const std::string& taskID, const StringMap& param, ErrorInfo &e, const Ice::Current &current);
    bool retryTask(const std::string& taskID, const StringMap& param, ErrorInfo &e, const Ice::Current &current);
    bool abolishTask(const std::string& taskID, const StringMap& param, ErrorInfo &e, const Ice::Current &current);
    bool confirmTask(const std::string& taskID, const StringMap& param, ErrorInfo &e, const Ice::Current &current);

    bool getTaskItems(const std::string& taskID, StringMap& task, ListStringMap& items, ErrorInfo &e, const Ice::Current &current);
    bool getItemActions(const std::string& itemID, StringMap& item, ListStringMap& actions, ErrorInfo &e, const Ice::Current &current);
    bool getTaskTypeDevices(const std::string& taskTypeID, ListStringMap& listDevice, ErrorInfo& e, const Ice::Current& current);
    bool getTaskTypeObjects(const StringMap& params, ListStringMap& listObject, ErrorInfo& e, const Ice::Current& current);
    bool createTypicalTask(StringMap params, ErrorInfo &e, const Ice::Current &current);
    bool createSpecialTask(const std::string& taskTypeID, const StringList& listObjectID, StringMap params, std::string& taskID, ErrorInfo &e, const Ice::Current &current);
    bool createUAVTask(const StringMap& task, const ListStringMap& listPreset, const ListStringMap& listItem, const ListStringMap& listAction, ErrorInfo& e, const Ice::Current& current);
    bool createCustomTask(const ListStringMap &listItem, StringMap params, std::string &taskID,
                          ErrorInfo &e, const Ice::Current &current);
    bool editTask(const std::string& taskID, const StringMap& head, const ListStringMap& items, ErrorInfo &e, const Ice::Current &current);
    bool convertTask(const std::string& taskID,const StringMap& params, ErrorInfo &e, const Ice::Current &current);
    bool updateTask(const ListStringMap& listTask, ErrorInfo& e, const Ice::Current& current);
    bool updateItem(const ListStringMap& listItem, ErrorInfo& e, const Ice::Current& current);
    bool saveTask(const std::string& taskID, std::string& errMsg);
    bool updateAction(const ListStringMap& listAction, ErrorInfo& e, const Ice::Current& current);
    bool downloadTask(StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ListStringMap& listAction, ErrorInfo& e, const Ice::Current& current);

    std::string getTaskState(const std::string& id) {
        return ZGUtils::get(m_mapTaskState, id, "");
    }

    std::string getTaskStage(const std::string& id) {
        return ZGUtils::get(m_mapTaskStage, id, "");
    }

    std::string getExecState(const std::string& id) {
        return ZGUtils::get(m_mapExecState, id, "");
    }

    void publishMessage(const QString& topic, const QString& msg);

    bool execTaskAction(const std::string& taskID, const StringMap& param,
                    ErrorInfo& e, std::function<bool(ZGOPTaskITTask*)> func);

private slots:
    void onTimer();

private:
    explicit ZGOPTaskITMng(QObject *parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initTask();
    bool initParams();
    bool initMqttClient();
    bool addTask(const std::string& taskID);
    void removeTask(const std::string& taskID);
    ZGOPTaskITTask* fetchTask(std::string taskID);
    void expandTask(ZG6000::StringMap& task);
    void expandItem(ZG6000::StringMap& item);
    void expandAction(ZG6000::StringMap& action);
    void processUpdateOperation(const std::string& tableName, const ListRecord& listRecord);
    void processInsertOperation(const std::string& tableName, const ListRecord& listRecord);
    void processDeleteOperation(const std::string& tableName, const ListRecord& listRecord);

private:
    bool m_initialized{ false };
    QString m_serverName{ "" };
    QString m_instName{ "" };
    bool m_masterInst{ false };
    int m_initInterval{ 10 };
    int m_checkInterval{ 10 };
    QTimer m_checkTimer;
    ZGMqttClient* m_pMqttClient{ nullptr };
    StringMap m_mapTaskType;
    StringMap m_mapTaskState;
    StringMap m_mapTaskStage;
    StringMap m_mapActionType;
    StringMap m_mapCategory;
    StringMap m_mapDefectLevel;
    StringMap m_mapExecState;
    StringMap m_mapItemType;
    StringMap m_mapTourMode;
    MapStringMap m_mapAppNode;
    StringMap m_mapSubsystem;
    StringMap m_mapMajor;
    StringMap m_mapITType;
    MapStringMap m_mapModel;
    std::map<std::string, StringList> m_mapModelItem;
    MapStringMap m_mapItem;
    std::map<std::string, StringList> m_mapItemAction;
    MapStringMap m_mapAction;
    std::unordered_map<std::string, ZGOPTaskITTask*> m_mapTask;
    QReadWriteLock m_lock;
    std::set<std::string> m_setTaskFields{"id", "name", "taskTypeID", "appNodeID", "subsystemID", "majorID", "rtCreateUserID", "rtCreateTime",
		"rtOperUserID", "rtMonUserID", "rtStartTime", "rtEndTime", "rtExecStartTime", "rtExecEndTime", "rtTaskStageID", "rtTaskStateID"};
    std::set<std::string> m_setITFields{"id", "typeID", "rtNumber", "rtCurrentItemID", "rtCurrentItemIndex"};
    std::string m_taskID;
};

inline static ZGOPTaskITMng* g_pInstance = nullptr;

} // namespace ZG6000

#endif // ZG6000_ZGOPTASKITMNG_H

#include "ZGOPTaskITAction.h"
#include "ZGOPTaskITItem.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"
#include "ZGJson.h"

ZGOPTaskITAction::ZGOPTaskITAction(QObject *parent) : QObject{parent}
{
}

bool ZGOPTaskITAction::initialize()
{
    QString sql = QString("SELECT rtExecStateID FROM op_param_it_task_action");
    std::string rtExecStateID;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), rtExecStateID))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务项动作状态失败"));
        return false;
    }
    if (rtExecStateID == "ZG_IES_FINISH")
    {
        ZGOPTaskITItem *item = dynamic_cast<ZGOPTaskITItem *>(parent());
        return item->nextAction();
    }
    return true;
}

bool ZGOPTaskITAction::setCurrentState(const std::string &state)
{
    ZG6000::StringMap action{{"id", m_id}, {"rtExecStateID", state}};
    std::string sql = ZGUtils::generateUpdateSql("op_param_it_task_action", action);
    if (!ZGProxyCommon::execSql(sql))
    {
        ZGLOG_ERROR(QStringLiteral("更新任务项动作'%1'状态失败").arg(m_id.c_str()));
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

void ZGOPTaskITAction::onTimer(const ZG6000::StringMap &item)
{
    if (!checkCondition(item))
        return;
    if (m_id.empty())
    {
        ZGOPTaskITItem *pItem = dynamic_cast<ZGOPTaskITItem *>(parent());
        pItem->nextAction();
        return;
    }
    ZG6000::StringMap action;
    QString sql = QString("SELECT * FROM op_param_it_task_action WHERE id = '%1'").arg(m_id.c_str());
    if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), action))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务项动作'%1'信息失败").arg(m_id.c_str()));
        return;
    }
    try
    {
        const auto& actionState = ZGUtils::get(action, "rtExecStateID");
        QDateTime now = QDateTime::currentDateTime();
        if (actionState.empty() || actionState == "ZG_IES_READY")
            processReadyState(action, now);
        else if (actionState == "ZG_IES_WAIT")
            processWaitState(action, now);
        else if (actionState == "ZG_IES_EXECUTE")
            processExecuteState(action, now);
        else if (actionState == "ZG_IES_CONFIRM")
            processConfirmState(action, now);
        else if (actionState == "ZG_IES_FINISH")
            processFinishState(action, now);
    }
    catch(const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }  
}

void ZGOPTaskITAction::processReadyState(const ZG6000::StringMap &action, QDateTime &time)
{
    ZGLOG_TRACE("processReadyState");
    ZG6000::StringMap newAction{{"id", m_id},
                             {"rtExecStateID", "ZG_IES_WAIT"},
                             {"rtExecTime", time.toString("yyyy-MM-dd hh:mm:ss").toStdString()}};
    std::string sql = ZGUtils::generateUpdateSql("op_param_it_task_action", newAction);
    if (!ZGProxyCommon::execSql(sql))
    {
        ZGLOG_ERROR(QStringLiteral("更新任务项动作'%1'状态失败").arg(m_id.c_str()));
        return;
    }
    ZGProxyCommon::synchronize();
}

void ZGOPTaskITAction::processWaitState(const ZG6000::StringMap &action, QDateTime &time)
{
    ZGLOG_TRACE("processWaitState");
    try
    {
        int waitSeconds = std::atoi(ZGUtils::get(action, "waitSeconds").c_str());
        QDateTime execTime = QDateTime::fromString(ZGUtils::get(action, "rtExecTime").c_str(), "yyyy-MM-dd hh:mm:ss");
        if (execTime.secsTo(time) >= waitSeconds)
        {
            if (!setCurrentState("ZG_IES_EXECUTE"))
                ZGLOG_ERROR(QStringLiteral("更新任务项动作'%1'执行状态失败").arg(m_id.c_str()));
        }
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGOPTaskITAction::processExecuteState(const ZG6000::StringMap &action, QDateTime &time)
{
    ZGLOG_TRACE("processExecuteState");
    try
    {
        const auto &actionTypeID = ZGUtils::get(action, "actionTypeID");
        const auto &presetID = ZGUtils::get(action, "presetID");
        ZGLOG_TRACE(QString("actionTypeID: %1, presetID: %2").arg(actionTypeID.c_str()).arg(presetID.c_str()));
        if (actionTypeID == "ZG_AT_ROBOT_MOVE")
        {
            processRobotMove(presetID);
        }
        if (actionTypeID == "ZG_AT_HK_PHOTO")
        {
            processHKPhoto(presetID);
        }
        if (actionTypeID == "ZG_AT_HK_VIDEO")
        {
            processHKVideo(action, presetID);
        }
        // if (!presetID.empty())
        // {
        //     ZG6000::StringMap preset;
        //     ZGProxyCommon::getDataByID("mp_param_preset", presetID, preset);
        //     const auto &deviceID = ZGUtils::get(preset, "deviceID", "");
        //     const auto &propertyName = ZGUtils::get(preset, "propertyName", "");
        //     const auto &presetNo = ZGUtils::get(preset, "presetNo", "");
        //     ZGLOG_TRACE(QString("deviceID: %1, propertyName: %2, presetNo: %3")
        //                     .arg(deviceID.c_str())
        //                     .arg(propertyName.c_str())
        //                     .arg(presetNo.c_str()));
        // }
        // if (!setCurrentState("ZG_IES_FINISH"))
        //     ZGLOG_ERROR(QStringLiteral("更新任务项动作'%1'完成状态失败").arg(m_id.c_str()));
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGOPTaskITAction::processConfirmState(const ZG6000::StringMap &action, QDateTime &time)
{
    ZGLOG_TRACE("processConfirmState");
    try
    {
        const auto &actionTypeID = ZGUtils::get(action, "actionTypeID");
        const auto &presetID = ZGUtils::get(action, "presetID");
        if (actionTypeID == "ZG_AT_ROBOT_MOVE")
        {
            waitRobotMoveFinished(presetID);
        }
    }
    catch(const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }   
}

void ZGOPTaskITAction::processFinishState(const ZG6000::StringMap &action, QDateTime &time)
{
    ZGLOG_TRACE("processFinishState");
    ZGOPTaskITItem *item = dynamic_cast<ZGOPTaskITItem *>(parent());
    if (!item->nextAction())
        ZGLOG_ERROR(QStringLiteral("任务项'%1'执行下一动作失败").arg(item->id().c_str()));
}

bool ZGOPTaskITAction::checkCondition(const ZG6000::StringMap &item)
{
    try
    {
        const auto &itemState = ZGUtils::get(item, "rtExecStateID");
        const auto &tourModeID = ZGUtils::get(item, "tourModeID");
        return ((tourModeID == "ZG_TM_ROBOT" || tourModeID == "ZG_TM_CAMERA") && (itemState == "ZG_IES_EXECUTE"));
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

void ZGOPTaskITAction::processRobotMove(const std::string &presetID)
{
    ZGLOG_TRACE("processRobotMove");
    if (presetID.empty())
    {
        ZGOPTaskITItem* pItem = dynamic_cast<ZGOPTaskITItem*>(parent());
        pItem->setItemError(QStringLiteral("动作%1未设置预置位").arg(m_id.c_str()).toStdString());
        return;
    }
    if (ctrlCall([&](std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx> ctrlPrx){
        ZG6000::ErrorInfo e;
        if (!ctrlPrx->presetPointCtrl(presetID, e))
        {
            ZGLOG_ERROR(QStringLiteral("执行动作%1失败").arg(m_id.c_str()));
            ZGOPTaskITItem* pItem = dynamic_cast<ZGOPTaskITItem*>(parent());
            pItem->setItemError(e.errDetail);
            return false;
        }
        return true;
    }))
    {
        if (!setCurrentState("ZG_IES_CONFIRM"))
        ZGLOG_ERROR(QStringLiteral("更新任务项动作%1确认状态失败").arg(m_id.c_str()));
    }
}

void ZGOPTaskITAction::processHKPhoto(const std::string &presetID)
{
    ZGLOG_TRACE("processHKPhoto");
    if (presetID.empty())
    {
        ZGOPTaskITItem* pItem = dynamic_cast<ZGOPTaskITItem*>(parent());
        pItem->setItemError(QStringLiteral("动作未设置预置位").toStdString());
        return;
    }
    if (ctrlCall([&](std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx> ctrlPrx){
        ZG6000::ErrorInfo e;
        ZGOPTaskITItem* pItem = dynamic_cast<ZGOPTaskITItem*>(parent());
        if (!ctrlPrx->presetPointCtrl(presetID, e))
        {
            ZGLOG_ERROR(QStringLiteral("执行动作失败"));           
            pItem->setItemError(e.errDetail);
            return false;
        }
        ZG6000::StringMap preset;
        if (!ZGProxyCommon::getDataByFields("mp_param_preset", presetID, {"deviceID", "propertyName", "presetNo"}, preset))
        {
            ZGLOG_ERROR(QStringLiteral("获取预置位信息失败"));
            pItem->setItemError(QStringLiteral("获取预置位信息失败").toStdString());
            return false;
        }
        std::string tableName, yvID;
        if (!ZGProxyCommon::getDataIDByProperty(preset["deviceID"], preset["propertyName"], tableName, yvID, e))
        {
            ZGLOG_ERROR(e);
            pItem->setItemError(QStringLiteral("获取预置位对应的视频信息失败").toStdString());
            return false;
        }
        std::string url;
        if (!ctrlPrx->captureImage(yvID, url, e))
        {
            ZGLOG_ERROR(QStringLiteral("抓图失败"));
            pItem->setItemError(e.errDetail);
            return false;
        }
        ZG6000::StringMap action{{"id", m_id}, {"rtPropertyValue", url}};
        std::string sql = ZGUtils::generateUpdateSql("op_param_it_task_action", action);
        if (!ZGProxyCommon::execSql(sql))
        {
            ZGLOG_ERROR(QStringLiteral("更新任务项动作'%1'抓图地址失败").arg(m_id.c_str()));
            pItem->setItemError(QStringLiteral("更新任务项动作抓图地址失败").toStdString());
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }))
    {
        if (!setCurrentState("ZG_IES_FINISH"))
            ZGLOG_ERROR(QStringLiteral("更新任务项动作'%1'完成状态失败").arg(m_id.c_str()));
    }
}

void ZGOPTaskITAction::processHKVideo(const ZG6000::StringMap& action, const std::string& presetID)
{
    ZGLOG_TRACE("processHKVideo");
    if (presetID.empty())
    {
        ZGOPTaskITItem* pItem = dynamic_cast<ZGOPTaskITItem*>(parent());
        pItem->setItemError(QStringLiteral("动作未设置预置位").toStdString());
        return;
    }
    if (ctrlCall([&](std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx> ctrlPrx){
        ZG6000::ErrorInfo e;
        ZGOPTaskITItem* pItem = dynamic_cast<ZGOPTaskITItem*>(parent());
        if (!ctrlPrx->presetPointCtrl(presetID, e))
        {
            ZGLOG_ERROR(QStringLiteral("执行动作失败"));           
            pItem->setItemError(e.errDetail);
            return false;
        }
        ZG6000::StringMap preset;
        if (!ZGProxyCommon::getDataByFields("mp_param_preset", presetID, {"deviceID", "propertyName", "presetNo"}, preset))
        {
            ZGLOG_ERROR(QStringLiteral("获取预置位信息失败"));
            pItem->setItemError(QStringLiteral("获取预置位信息失败").toStdString());
            return false;
        }
        std::string tableName, yvID;
        if (!ZGProxyCommon::getDataIDByProperty(preset["deviceID"], preset["propertyName"], tableName, yvID, e))
        {
            ZGLOG_ERROR(e);
            pItem->setItemError(QStringLiteral("获取预置位对应的视频信息失败").toStdString());
            return false;
        }       
        int duration = 5;
        const auto& actionParam = ZGUtils::get(action, "actionParam");
        if (!actionParam.empty())
        {
            ZG6000::StringMap param;
            std::string errMsg;
            if (ZGJson::convertFromJson(actionParam, param, errMsg))
            {
                duration = std::atoi(param["duration"].c_str());
            }
        }      
        std::string url;
        ZGLOG_TRACE(QString("yvID: '%1', duration: '%2'").arg(yvID.c_str()).arg(duration));
        if (!ctrlPrx->recordVideo(yvID, duration, url, e))
        {
            ZGLOG_ERROR(QStringLiteral("录像失败"));
            pItem->setItemError(e.errDetail);
            return false;
        }
        ZG6000::StringMap newAction{{"id", m_id}, {"rtPropertyValue", url}};
        std::string sql = ZGUtils::generateUpdateSql("op_param_it_task_action", newAction);
        if (!ZGProxyCommon::execSql(sql))
        {
            ZGLOG_ERROR(QStringLiteral("更新动作'%1'录像地址失败").arg(m_id.c_str()));
            pItem->setItemError(QStringLiteral("更新动作录像地址失败").toStdString());
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }))
    {
        if (!setCurrentState("ZG_IES_FINISH"))
            ZGLOG_ERROR(QStringLiteral("更新任务项动作%1完成状态失败").arg(m_id.c_str()));
    }
}

void ZGOPTaskITAction::waitRobotMoveFinished(const std::string &presetID)
{
    ZGLOG_TRACE("waitRobotMoveFinished");
    if (presetID.empty())
    {
        ZGOPTaskITItem* pItem = dynamic_cast<ZGOPTaskITItem*>(parent());
        pItem->setItemError(QStringLiteral("动作%1未设置预置位").arg(m_id.c_str()).toStdString());
        return;
    }
    ZG6000::StringMap presetParam;
    if (!ZGProxyCommon::getDataByFields("mp_param_preset", presetID, {"deviceID", "propertyName", "presetNo"}, presetParam))
    {
        ZGLOG_ERROR(QStringLiteral("获取预置位信息失败"));
        return;
    }
    std::string property = ZGUtils::get(presetParam, "propertyName");
    ZGUtils::replaceString(property, "CMD_", "");
    std::string actualValue;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(presetParam["deviceID"], property, actualValue, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    if (actualValue == presetParam["presetNo"])
    {
        if (!setCurrentState("ZG_IES_FINISH"))
            ZGLOG_ERROR(QStringLiteral("更新任务项动作'%1'完成状态失败").arg(m_id.c_str()));
    }
}

bool ZGOPTaskITAction::ctrlCall(const std::function<bool(std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx>)> &func)
{
    auto ctrlPrx = ZGProxyMng::instance()->getProxyOPPatrolDeviceCtrl();
    if (ctrlPrx == nullptr)
    {
        ZGLOG_ERROR(QStringLiteral("获取巡检设备控制代理失败"));
        return false;
    }
    try
    {
        return func(ctrlPrx);
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

#include "ZGOPTaskITTask.h"
#include "ZGOPTaskITDefine.h"
#include "ZGOPTaskITItem.h"
#include "ZGOPTaskITMng.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "zgerror/ZGOPTaskITError.h"

#include <QJsonDocument>
#include <QJsonObject>

ZGOPTaskITTask::ZGOPTaskITTask(const std::string &id, QObject *parent) : QObject{parent}, m_id(id)
{
    m_currentItem = new ZGOPTaskITItem(this);
}

bool ZGOPTaskITTask::initialize()
{
    registerAction();
    if (!initTaskContext())
    {
        ZGLOG_ERROR(QStringLiteral("初始化任务'%1'上下文失败").arg(m_id.c_str()));
        return false;
    }
    return true;
}

void ZGOPTaskITTask::dispatchData(const std::string &tableName, const ZG6000::MapField &record)
{
    if (tableName == "op_param_task")
        processTaskChange(record);
    else if (tableName == "op_param_it_task")
        processITTaskChange(record);
    else if (tableName == "op_param_it_task_item")
        processItemChange(record);
    else if (tableName == "op_param_it_task_action")
        processActionChange(record);
    else if (tableName == "sp_real_exam")
        processExamChange(record);
}

bool ZGOPTaskITTask::confirm(const ZG6000::StringMap &params, ZG6000::ErrorInfo &e)
{
    return execAction(ZGOPIT_CONFIRM, params, e);
}

bool ZGOPTaskITTask::start(const ZG6000::StringMap &params, ZG6000::ErrorInfo &e)
{
    return execAction(ZGOPIT_START, params, e);
}

bool ZGOPTaskITTask::stop(const ZG6000::StringMap &params, ZG6000::ErrorInfo &e)
{
    return execAction(ZGOPIT_STOP, params, e);
}

bool ZGOPTaskITTask::pause(const ZG6000::StringMap &params, ZG6000::ErrorInfo &e)
{
    return execAction(ZGOPIT_PAUSE, params, e);
}

bool ZGOPTaskITTask::resume(const ZG6000::StringMap &params, ZG6000::ErrorInfo &e)
{
    return execAction(ZGOPIT_RESUME, params, e);
}

bool ZGOPTaskITTask::retry(const ZG6000::StringMap &params, ZG6000::ErrorInfo &e)
{
    return execAction(ZGOPIT_RETRY, params, e);
}

bool ZGOPTaskITTask::abolish(const ZG6000::StringMap &params, ZG6000::ErrorInfo &e)
{
    return execAction(ZGOPIT_ABOLISH, params, e);
}

bool ZGOPTaskITTask::edit(const ZG6000::StringMap &params, const ZG6000::ListStringMap &items, ZG6000::ErrorInfo &e)
{
    ZG6000::StringMap head = params;
    ZG6000::StringList listSql;
    auto pair = head.find("rtNumber");
    if (pair != head.end())
    {
        ZG6000::StringMap it{{"id", m_id}, {"rtNumber", pair->second}};
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task", it));
        head.erase(pair);
    }
    head["id"] = m_id;
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", head));
    if (!items.empty())
    {
        for (const auto& item: items)
        {
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task_item", item));
        }
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("编辑任务'%1'失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITTask::remove(const ZG6000::StringMap &params, ZG6000::ErrorInfo &e)
{
    if (!deleteExam(e))
        return false;
    ZG6000::MapField record;
    record["id"].newValue = m_id;
    record["rtTaskStageID"].newValue = "ZG_TS_DELETE";
    record["rtTaskStateID"].newValue = "ZG_TS_FINISHED";
    processTaskChange(record);
    return true;
}

bool ZGOPTaskITTask::convert(const ZG6000::StringMap &params, ZG6000::ErrorInfo &e)
{
    ZG6000::StringMap it{{"id", m_id}, {"typeID", "ZG_TT_TYPICAL"}};
    std::string sql = ZGUtils::generateUpdateSql("op_param_it_task", it);
    if (!ZGProxyCommon::execSql(sql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskIT::ZG_ERR_DB);
        e.errDetail = QStringLiteral("任务'%1'转为典型票失败").arg(m_id.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITTask::currentState(std::string &state) const
{
    if (!ZGProxyCommon::getDataByField("op_param_task", m_id, "rtTaskStateID", state))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'当前状态失败").arg(m_id.c_str()));
        return false;
    }
    return true;
}

bool ZGOPTaskITTask::setCurrentState(const std::string &state)
{
    ZG6000::StringMap task{{"id", m_id}, {"rtTaskStateID", state}};
    std::string sql = ZGUtils::generateUpdateSql("op_param_task", task);
    if (!ZGProxyCommon::execSql(sql))
    {
        ZGLOG_ERROR(QStringLiteral("更新任务'%1'当前状态'%2'失败").arg(m_id.c_str()).arg(state.c_str()));
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITTask::currentStage(std::string& stage) const
{
    if (!ZGProxyCommon::getDataByField("op_param_task", m_id, "rtTaskStageID", stage))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'当前阶段失败").arg(m_id.c_str()));
        return false;
    }
    return true;
}

bool ZGOPTaskITTask::setCurrentStage(const std::string &stage)
{
    ZG6000::StringMap task{{"id", m_id}, {"rtTaskStageID", stage}};
    std::string sql = ZGUtils::generateUpdateSql("op_param_task", task);
    if (!ZGProxyCommon::execSql(sql))
    {
        ZGLOG_ERROR(QStringLiteral("更新任务'%1'当前阶段'%2'失败").arg(m_id.c_str()).arg(stage.c_str()));
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITTask::notify(const std::string &action, ZG6000::StringMap args, std::string &errMsg)
{
    return fsm.command(action, std::move(args), errMsg);
}

void ZGOPTaskITTask::updateFSMStage(const std::string &stage)
{
    auto s = fsm.state();
    s.stage_ = stage;
    fsm.set_state(s);
}

void ZGOPTaskITTask::updateFSMState(const std::string &state)
{
    auto s = fsm.state();
    s.state_ = state;
    fsm.set_state(s);
}

void ZGOPTaskITTask::onTimer()
{
    QMutexLocker lock(&m_mutex);
    ZG6000::StringMap task;
    if (!ZGProxyCommon::getDataByID("op_param_task", m_id, task))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'信息失败").arg(m_id.c_str()));
        return;
    }
    try
    {
        const auto &rtExecStage = ZGUtils::get(task, "rtTaskStageID");
        if (rtExecStage == "ZG_TS_EXECUTE")
        {
            const auto &rtEndTime = ZGUtils::get(task, "rtEndTime");
            QDateTime endTime;
            if (ZGUtils::StringToDateTime(rtEndTime.c_str(), endTime, true))
            {
                QDateTime currTime = QDateTime::currentDateTime();
                if (currTime > endTime)
                {
                    setCurrentState("ZG_TS_TASK_TIMEOUT");
                    return;
                }
            }
        }       
        m_currentItem->onTimer(task);
    }
    catch (const std::exception &e)
    {
        ZGLOG_DEBUG(e.what());
    }
}

bool ZGOPTaskITTask::nextItem()
{
    std::string currentIndex;
    if (!ZGProxyCommon::getDataByField("op_param_it_task", m_id, "rtCurrentItemIndex", currentIndex))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'当前项失败").arg(m_id.c_str()));
        return false;
    }
    ZGLOG_TRACE(QString("currentIndex: '%1'").arg(currentIndex.c_str()));
    int nextItemIndex = currentIndex.empty() ? 1 : std::atoi(currentIndex.c_str()) + 1;
    QString sql = QString("SELECT id FROM op_param_it_task_item WHERE taskID = '%1' AND itemIndex = %2")
                      .arg(m_id.c_str())
                      .arg(nextItemIndex);
    ZG6000::StringList listItemId;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listItemId))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'下一项失败").arg(m_id.c_str()));
        return false;
    }
    ZG6000::StringList listSql;
    try
    {
        if (listItemId.empty())
        {
            ZGLOG_TRACE(QStringLiteral("任务'%1'已完成").arg(m_id.c_str()));
            ZG6000::StringMap task{
                {"id", m_id},
                {"rtTaskStateID", "ZG_TS_FINISHED"},
                {"rtExecEndTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString()}};
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
            ZG6000::StringMap it{{"id", m_id}, {"rtCurrentItemID", ""}, {"rtCurrentItemIndex", "0"}};
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task", it));
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                ZGLOG_ERROR(QStringLiteral("更新任务'%1'完成状态失败").arg(m_id.c_str()));
                return false;
            }
        }
        else
        {
            std::string nextItemId = listItemId.front();
            ZG6000::StringMap it{
                {"id", m_id}, {"rtCurrentItemID", nextItemId}, {"rtCurrentItemIndex", std::to_string(nextItemIndex)}};
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task", it));
            std::string nextActionIndex = "1";
            sql = QString("SELECT id FROM op_param_it_task_action WHERE itemID = '%1' AND actionIndex = %2")
                      .arg(nextItemId.c_str())
                      .arg(nextActionIndex.c_str());
            ZG6000::StringList listActionId;
            if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listActionId))
            {
                ZGLOG_ERROR(QStringLiteral("获取任务'%1'下一项动作失败").arg(m_id.c_str()));
                return false;
            }
            std::string nextActionId;
            if (listActionId.empty())
            {
                nextActionId = "";
                nextActionIndex = "0";
            }
            else
            {
                nextActionId = listActionId.front();
                nextActionIndex = "1";
            }
            ZGLOG_TRACE(QString("nextItemID: '%1', nextItemIndex: '%2', nextActionID: '%3', nextActionIndex: '%4'")
                            .arg(nextItemId.c_str())
                            .arg(nextItemIndex)
                            .arg(nextActionId.c_str())
                            .arg(nextActionIndex.c_str()));
            // std::string isAutoTour;
            // if (!ZGProxyCommon::getDataByField("op_param_it_task_item", nextItemId, "isAutoTour", isAutoTour))
            // {
            //     ZGLOG_ERROR(QStringLiteral("获取任务项'%1'是否自动执行标志失败").arg(nextItemId.c_str()));
            //     return false;
            // }
            // if (isAutoTour != "1")
            // {
            //     ZG6000::StringMap task{{"id", m_id}, {"rtTaskStateID", "ZG_TS_PAUSED"}};
            //     listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
            // }
            ZG6000::StringMap item{
                {"id", nextItemId},
                {"rtStartTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString()},
                {"rtCurrentActionID", nextActionId},
                {"rtCurrentActionIndex", nextActionIndex}};
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task_item", item));
            if (!ZGProxyCommon::execBatchSql(listSql))
            {
                ZGLOG_ERROR(QStringLiteral("更新任务'%1'下一项失败").arg(m_id.c_str()));
                return false;
            }
            m_currentItem->setId(nextItemId);
        }
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

void ZGOPTaskITTask::registerAction()
{
    fsm.on({"ZG_TS_CREATE", "ZG_TS_FINISHED"}, ZGOPIT_CONFIRM) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onCreateConfirm(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXAM", "ZG_TS_FINISHED"}, ZGOPIT_CONFIRM) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExamConfirm(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_READY"}, ZGOPIT_START) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteStart(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_EXECUTING"}, ZGOPIT_PAUSE) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecutePause(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_EXECUTING"}, ZGOPIT_EXEC_TIMEOUT) =
        [&](ZG6000::StringMap args, std::string &errMsg) { return onExecuteItemTimeout(std::move(args), errMsg); };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_EXECUTING"}, ZGOPIT_EXEC_ERROR) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteItemError(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_PAUSED"}, ZGOPIT_RESUME) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteResume(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_ERROR"}, ZGOPIT_RETRY) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteRetry(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_ITEM_TIMEOUT"}, ZGOPIT_RETRY) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteRetry(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_FINISHED"}, ZGOPIT_CONFIRM) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onExecuteConfirm(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_READY"}, ZGOPIT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_PAUSED"}, ZGOPIT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_ERROR"}, ZGOPIT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_ITEM_TIMEOUT"}, ZGOPIT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
    fsm.on({"ZG_TS_EXECUTE", "ZG_TS_TASK_TIMEOUT"}, ZGOPIT_ABOLISH) = [&](ZG6000::StringMap args, std::string &errMsg) {
        return onAbolish(std::move(args), errMsg);
    };
}

bool ZGOPTaskITTask::initTaskContext()
{
    std::string stage, state;
    if (!currentStage(stage) || !currentState(state))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'当前状态或阶段失败").arg(m_id.c_str()));
        return false;
    }
    fsm.set_state({stage, state});
    std::string itemID;
    if (!ZGProxyCommon::getDataByField("op_param_it_task", m_id, "rtCurrentItemID", itemID))
    {
        ZGLOG_ERROR(QStringLiteral("获取任务'%1'当前项失败").arg(m_id.c_str()));
        return false;
    }
    m_currentItem->setId(itemID);
    if (!m_currentItem->initialize())
    {
        ZGLOG_ERROR(QStringLiteral("初始化任务'%1'当前项失败").arg(m_id.c_str()));
        return false;
    }
    return true;
}

void ZGOPTaskITTask::processTaskChange(const ZG6000::MapField &record)
{
    QJsonObject root;
    QJsonObject dataObj;
    try
    {
        for (const auto &field : record)
        {
            if (field.first == "rtTaskStageID")
            {
                updateFSMStage(field.second.newValue);
                dataObj.insert("rtTaskStageName",
                               ZG6000::ZGOPTaskITMng::instance()->getTaskStage(field.second.newValue).c_str());
            }
            if (field.first == "rtTaskStateID")
            {
                updateFSMState(field.second.newValue);
                dataObj.insert("rtTaskStateName",
                               ZG6000::ZGOPTaskITMng::instance()->getTaskState(field.second.newValue).c_str());
            }
            dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
        }
        root["head"] = dataObj;
        QJsonDocument doc(root);
        const auto &taskID = ZGUtils::get(record, "id").newValue;
        ZGLOG_DEBUG(doc.toJson());
        ZG6000::ZGOPTaskITMng::instance()->publishMessage(QString("op_param_it_task/%1").arg(taskID.c_str()), doc.toJson());
        std::string taskTypeID;
        if (ZGProxyCommon::getDataByField("op_param_task", taskID, "taskTypeID", taskTypeID))
        {
            QString msg = QString("{\"id\":\"%1\"}").arg(taskID.c_str());
            ZG6000::ZGOPTaskITMng::instance()->publishMessage(
                QString("op_param_task/%1/update").arg(taskTypeID.c_str()), msg);
        }
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGOPTaskITTask::processITTaskChange(const ZG6000::MapField &record)
{
    QJsonObject root;
    QJsonObject dataObj;
    try
    {
        for (const auto &field : record)
        {
            if (field.first == "rtCurrentItemID")
            {
                m_currentItem->setId(field.second.newValue);
            }                
            dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
        }
        root["head"] = dataObj;
        QJsonDocument doc(root);
        const auto &taskID = ZGUtils::get(record, "id").newValue;
        ZG6000::ZGOPTaskITMng::instance()->publishMessage(QString("op_param_it_task/%1").arg(taskID.c_str()),
                                                          doc.toJson());
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGOPTaskITTask::processItemChange(const ZG6000::MapField &record)
{
    QJsonObject root;
    QJsonObject dataObj;
    try
    {
        for (const auto &field : record)
        {
            if (field.first == "rtCurrentActionID")
                m_currentItem->updateCurrentActionID(field.second.newValue);
            if (field.first == "rtExecStateID")
            {
                if (field.second.newValue == "ZG_IES_EXECUTE")
                    m_currentItem->updateLastStartTime();
                dataObj.insert("rtExecStateName",
                                ZG6000::ZGOPTaskITMng::instance()->getExecState(field.second.newValue).c_str());
            }               
            dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
        }
        root["item"] = dataObj;
        QJsonDocument doc(root);
        ZG6000::ZGOPTaskITMng::instance()->publishMessage(QString("op_param_it_task/%1").arg(m_id.c_str()), doc.toJson());
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGOPTaskITTask::processActionChange(const ZG6000::MapField &record)
{
    QJsonObject root;
    QJsonObject dataObj;
    try
    {
        const auto &actionID = ZGUtils::get(record, "id").newValue;
        std::string itemID;
        if (!ZGProxyCommon::getDataByField("op_param_it_task_action", actionID, "itemID", itemID))
        {
            ZGLOG_ERROR(QStringLiteral("获取动作'%1'所属任务项失败").arg(actionID.c_str()));
            return;
        }
        for (const auto &field : record)
        {
            if (field.first == "rtExecStateID")
            {
                dataObj.insert("rtExecStateName",
                               ZG6000::ZGOPTaskITMng::instance()->getExecState(field.second.newValue).c_str());
            }               
            dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
        }
        root["action"] = dataObj;
        QJsonDocument doc(root);
        QString topic = QString("op_param_it_task/%1").arg(m_id.c_str());
        ZGLOG_TRACE(QString("%1:%2").arg(topic).arg(doc.toJson()));
        ZG6000::ZGOPTaskITMng::instance()->publishMessage(QString("op_param_it_task/%1").arg(m_id.c_str()), doc.toJson());
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGOPTaskITTask::processExamChange(const ZG6000::MapField & record)
{
    ZGLOG_DEBUG("processExamChange");
    try
    {
        const auto &examStateID = ZGUtils::get(record, "examStateID").newValue;
        std::string newTaskStateID;
        auto it =
            std::find_if(m_listPairState.begin(), m_listPairState.end(),
                         [&](const std::pair<std::string, std::string> &pair) { return pair.second == examStateID; });
        if (it != m_listPairState.end())
            newTaskStateID = it->first;
        ZG6000::StringMap updateTask{{"id", m_id}};
        updateTask["rtTaskStateID"] = newTaskStateID;
        std::string sql = ZGUtils::generateUpdateSql("op_param_task", updateTask);
        if (!ZGProxyCommon::execSql(sql))
        {
            ZGLOG_ERROR(QStringLiteral("更新任务'%1'审批状态失败").arg(m_id.c_str()));
            return;
        }
        ZGProxyCommon::synchronize();
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
    }
}

bool ZGOPTaskITTask::execAction(std::string action, ZG6000::StringMap param, ZG6000::ErrorInfo &e)
{
    std::string errMsg;
    if (!fsm.command(action, std::move(param), errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskIT::ZG_ERR_INTERNAL);
        e.errDetail = errMsg;
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskITTask::onCreateConfirm(ZG6000::StringMap args, std::string &errMsg)
{
    std::string rtExamID;
    QString sql = QString("SELECT b.examID FROM op_param_it_task a LEFT JOIN op_param_it_task_type b "
                        "ON a.typeID = b.id WHERE a.id = '%1' ORDER BY a.id").arg(m_id.c_str());
    ZG6000::StringList listExamID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listExamID))
    {
        errMsg = QStringLiteral("获取任务'%1'审批ID失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (listExamID.empty())
    {
        errMsg = QStringLiteral("获取任务'%1'审批ID失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    rtExamID = listExamID[0];
    if (rtExamID.empty())
        return updateExecuteReady(std::move(args), errMsg);
    ZGLOG_DEBUG(QString("createExam, id = '%1'").arg(rtExamID.c_str()));
    args["examID"] = rtExamID;
    return createExam(std::move(args), errMsg);
}

bool ZGOPTaskITTask::onExamConfirm(ZG6000::StringMap args, std::string &errMsg)
{
    std::string rtExamID;
    ZGProxyCommon::getDataByField("op_param_task", m_id, "rtExamID", rtExamID);
    ZG6000::StringMap params{{"examID", rtExamID}};
    if (!finishExam(params, errMsg))
        return false;
    return updateExecuteReady(std::move(args), errMsg);
}

bool ZGOPTaskITTask::onExecuteStart(ZG6000::StringMap args, std::string &errMsg)
{
    if (!checkTaskValid(errMsg))
        return false;
    ZG6000::StringList listSql;
    QDateTime now = QDateTime::currentDateTime();
    ZG6000::StringMap task{{"id", m_id},
                           {"rtTaskStateID", "ZG_TS_EXECUTING"},
                           {"rtExecStartTime", now.toString("yyyy-MM-dd hh:mm:ss").toStdString()},
                           {"rtErrorDesc", ""}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    QString sql =
        QString("SELECT id FROM op_param_it_task_item WHERE taskID = '%1' AND itemIndex = 1").arg(m_id.c_str());
    std::string itemID;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), itemID))
    {
        errMsg = QStringLiteral("获取任务'%1'的第一个任务项失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZG6000::StringMap it{{"id", m_id}, {"rtCurrentItemID", itemID}, {"rtCurrentItemIndex", "1"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task", it));
    ZG6000::StringMap item{{"id", itemID},
                           {"rtStartTime", now.toString("yyyy-MM-dd hh:mm:ss").toStdString()},
                           {"rtExecStateID", "ZG_IES_EXECUTE"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task_item", item));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新任务'%1'执行状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITTask::onExecuteStop(ZG6000::StringMap args, std::string& errMsg)
{
    return false;
}

bool ZGOPTaskITTask::onExecutePause(ZG6000::StringMap args, std::string &errMsg)
{
    if (!setCurrentState("ZG_TS_PAUSED"))
    {
        errMsg = QStringLiteral("更新任务'%1'状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    return true;
}

bool ZGOPTaskITTask::onExecuteResume(ZG6000::StringMap args, std::string &errMsg)
{
    std::string itemID = m_currentItem->id();
    if (itemID.empty())
    {
        errMsg = QStringLiteral("任务'%1'当前项为空").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZG6000::StringList listSql;
    ZG6000::StringMap task{{"id", m_id}, {"rtTaskStateID", "ZG_TS_EXECUTING"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    std::string timeout;
    ZGProxyCommon::getDataByField("op_param_it_task_item", itemID, "timeout", timeout);
    if (!timeout.empty())
        m_currentItem->updateLastStartTime();
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新任务'%1'执行状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITTask::onExecuteRetry(ZG6000::StringMap args, std::string &errMsg)
{
    std::string itemID = m_currentItem->id();
    if (itemID.empty())
    {
        errMsg = QStringLiteral("任务'%1'当前项为空").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZG6000::StringList listSql;
    ZG6000::StringMap task{{"id", m_id}, {"rtTaskStateID", "ZG_TS_EXECUTING"}, {"rtErrorDesc", ""}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    ZG6000::StringMap item{{"id", itemID}, {"rtExecStateID", "ZG_IES_EXECUTE"}, {"rtErrorDesc", ""}};
    std::string actionID = m_currentItem->getCurrentActionID();
    if (!actionID.empty())
    {
        ZG6000::StringMap action{{"id", actionID}, {"rtExecStateID", "ZG_IES_READY"}, {"rtExecTime", ""}};
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task_action", action));
    }
    std::string timeout;
    ZGProxyCommon::getDataByField("op_param_it_task_item", itemID, "timeout", timeout);
    if (!timeout.empty())
        m_currentItem->updateLastStartTime();
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task_item", item));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新任务'%1'执行状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    return true;
}

bool ZGOPTaskITTask::onExecuteConfirm(ZG6000::StringMap args, std::string &errMsg)
{
    if (!archiveTask(errMsg))
        return false;
    return true;
}

bool ZGOPTaskITTask::onExecuteItemTimeout(ZG6000::StringMap args, std::string &errMsg)
{
    if (!setCurrentState("ZG_TS_ITEM_TIMEOUT"))
    {
        errMsg = QStringLiteral("更新任务'%1'超时").arg(m_id.c_str()).toStdString();
        return false;
    }
    return true;
}

bool ZGOPTaskITTask::onExecuteItemError(ZG6000::StringMap args, std::string &errMsg)
{
    ZG6000::StringMap task{{"id", m_id}, {"rtTaskStateID", "ZG_TS_ERROR"}, {"rtErrorDesc", args["rtErrorDesc"]}};
    std::string sql = ZGUtils::generateUpdateSql("op_param_task", task);
    if (!ZGProxyCommon::execSql(sql))
    {
        errMsg = QStringLiteral("更新任务'%1'执行状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITTask::onAbolish(ZG6000::StringMap args, std::string &errMsg)
{
    if (!setCurrentStage("ZG_TS_ABOLISH"))
    {
        errMsg = QStringLiteral("更新任务'%1'作废阶段失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (!archiveTask(errMsg))
        return false;
    return true;
}

bool ZGOPTaskITTask::checkTaskValid(std::string &errMsg)
{
    QString sql = QString("SELECT rtStartTime, rtEndTime FROM op_param_task WHERE id = '%1'").arg(m_id.c_str());
    ZG6000::StringMap task;
    if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), task))
    {
        errMsg = QStringLiteral("获取任务'%1'信息失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    QDateTime now = QDateTime::currentDateTime();
    QDateTime startTime = QDateTime::fromString(QString::fromStdString(task["rtStartTime"]), "yyyy-MM-dd hh:mm:ss");
    if (!startTime.isValid())
    {
        errMsg = QStringLiteral("任务'%1'开始时间格式错误").arg(m_id.c_str()).toStdString();
        return false;
    }
    QDateTime endTime = QDateTime::fromString(QString::fromStdString(task["rtEndTime"]), "yyyy-MM-dd hh:mm:ss");
    if (!endTime.isValid())
    {
        errMsg = QStringLiteral("任务'%1'结束时间格式错误").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (now < startTime)
    {
        errMsg = QStringLiteral("任务'%1'未到开始时间").arg(m_id.c_str()).toStdString();
        return false;
    }
    if (now > endTime)
    {
        errMsg = QStringLiteral("任务'%1'已过结束时间").arg(m_id.c_str()).toStdString();
        return false;
    }
    return true;
}

bool ZGOPTaskITTask::updateExecuteReady(ZG6000::StringMap args, std::string &errMsg)
{
    ZG6000::StringList listItemID;
    if (!ZGProxyCommon::execQuerySqlCol("SELECT id FROM op_param_task WHERE id = '" + m_id + "'", listItemID))
    {
        errMsg = QStringLiteral("获取任务'%1'项失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZG6000::StringList listSql;
    for (const auto &itemID : listItemID)
    {
        ZG6000::StringMap action{{"rtExecTime", ""},      {"rtExecStateID", "ZG_IES_READY"},
                                 {"rtPropertyValue", ""}, {"rtDefectLevelID", ""},
                                 {"rtCheckResult", ""},   {"rtIdentityStateID", ""}};
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task_action", action, "itemID = '" + itemID + "'"));
    }
    ZG6000::StringMap item{{"rtStartTime", ""},
                           {"rtEndTime", ""},
                           {"rtExecStateID", "ZG_IES_READY"},
                           {"rtCurrentActionID", ""},
                           {"rtCurrentActionIndex", "0"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task_item", item, "taskID = '" + m_id + "'"));
    ZG6000::StringMap task{{"id", m_id}, {"rtTaskStageID", "ZG_TS_EXECUTE"}, {"rtTaskStateID", "ZG_TS_READY"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", task));
    ZG6000::StringMap it{{"id", m_id}, {"rtCurrentItemID", ""}, {"rtCurrentItemIndex", "0"}};
    listSql.push_back(ZGUtils::generateUpdateSql("op_param_it_task", it));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        errMsg = QStringLiteral("更新任务'%1'执行状态失败").arg(m_id.c_str()).toStdString();
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGOPTaskITTask::createExam(ZG6000::StringMap args, std::string &errMsg)
{
    auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
    if (examProxy == nullptr)
    {
        errMsg = QStringLiteral("获取审批管理服务代理对象失败").toStdString();
        return false;
    }
    try
    {
        std::string examID;
        ZG6000::ErrorInfo e;
        if (!examProxy->createExam(args["examID"], examID, e))
        {
            errMsg = e.errDetail;
            return false;
        }
        ZG6000::StringMap task;
        task["id"] = m_id;
        task["rtTaskStageID"] = "ZG_TS_EXAM";
        task["rtTaskStateID"] = "ZG_TS_READY";
        task["rtExamID"] = examID;
        if (!ZGProxyCommon::execSql(ZGUtils::generateUpdateSql("op_param_task", task)))
        {
            errMsg = QStringLiteral("更新任务审批阶段失败").toStdString();
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }
    catch (const Ice::Exception &e)
    {
        errMsg = e.what();
        return false;
    }
}

bool ZGOPTaskITTask::finishExam(ZG6000::StringMap args, std::string &errMsg)
{
    auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
    if (!examProxy)
    {
        errMsg = QStringLiteral("获取审批管理服务代理失败").toStdString();
        return false;
    }
    try
    {
        ZG6000::ErrorInfo e;
        if (!examProxy->finishExam(args["examID"], e))
        {
            errMsg = e.errDetail;
            return false;
        }
        return true;
    }
    catch (const Ice::Exception &e)
    {
        errMsg = e.what();
        return false;
    }
    catch (const std::exception &e)
    {
        errMsg = e.what();
        return false;
    }
    catch (...)
    {
        errMsg = QStringLiteral("审批失败").toStdString();
        return false;
    }
}

bool ZGOPTaskITTask::archiveTask(std::string &errMsg)
{
    if (!ZG6000::ZGOPTaskITMng::instance()->saveTask(m_id, errMsg))
        return false;
    ZG6000::ErrorInfo e;
    if (!ZG6000::ZGOPTaskITMng::instance()->deleteTask(m_id, {}, e, Ice::Current()))
    {
        ZGLOG_ERROR(e);
        errMsg = e.errDetail;
        return false;
    }
    return true;
}

bool ZGOPTaskITTask::deleteExam(ZG6000::ErrorInfo &e)
{
    std::string examID;
    if (!ZGProxyCommon::getDataByField("op_param_task", m_id, "rtExamID", examID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskIT::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取审批ID失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!examID.empty())
    {
        auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
        if (examProxy == nullptr)
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskIT::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取审批服务代理失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            if (!examProxy->deleteExam(examID, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }
        catch (const Ice::Exception &ie)
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskIT::ZG_ERR_INTERNAL);
            e.errDetail = ie.what();
            ZGLOG_ERROR(e);
            return false;
        }
        catch (const std::exception &ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGOPTaskIT::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }
    return true;
}

#ifndef ZGOPTASKITTASKUAV_H
#define ZGOPTASKITTASKUAV_H

#include "ZGOPTaskITTask.h"
#include "ZGProxyMng.h"

class ZGOPTaskITTaskUAV : public ZGOPTaskITTask
{
public:
    explicit ZGOPTaskITTaskUAV(const std::string& id, QObject *parent = nullptr);
    void onTimer() override;

protected:
    void registerAction() override;
    bool onExecuteStart(ZG6000::StringMap args, std::string& errMsg) override;
    bool onExecuteStop(ZG6000::StringMap args, std::string& errMsg) override;
    bool onExecutePause(ZG6000::StringMap args, std::string& errMsg) override;
    bool onExecuteResume(ZG6000::StringMap args, std::string& errMsg) override;
    bool onExecuteRetry(ZG6000::StringMap args, std::string& errMsg) override;

private:
    bool getUAVDevice(std::string& deviceID, std::string& errMsg);
    bool ctrlCall(const std::function<bool(std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx>)>& func);
};

#endif // ZGOPTASKITTASKUAV_H

#ifndef ZGOPTASKITACTION_H
#define ZGOPTASKITACTION_H

#include <QObject>
#include "ZGProxyMng.h"

class ZGOPTaskITAction : public QObject
{
    Q_OBJECT
public:
    explicit ZGOPTaskITAction(QObject *parent = nullptr);
    bool initialize();
    const std::string& id() const { return m_id; }
    void setId(const std::string& id) { m_id = id; }
    bool setCurrentState(const std::string& state);
    void onTimer(const ZG6000::StringMap& item);

protected:
    virtual void processReadyState(const ZG6000::StringMap& action, QDateTime& time);
    virtual void processWaitState(const ZG6000::StringMap& action, QDateTime& time);
    virtual void processExecuteState(const ZG6000::StringMap& action, QDateTime& time);
    virtual void processConfirmState(const ZG6000::StringMap& action, QDateTime& time);
    virtual void processFinishState(const ZG6000::StringMap& action, QDateTime& time);

private:
    bool checkCondition(const ZG6000::StringMap& item);
    void processRobotMove(const std::string& presetID);
    void processHKPhoto(const std::string& presetID);
    void processHKVideo(const ZG6000::StringMap& action, const std::string& presetID);
    void waitRobotMoveFinished(const std::string& presetID);
    bool ctrlCall(const std::function<bool(std::shared_ptr<ZG6000::ZGOPPatrolDeviceCtrlPrx>)>& func);

private:
    std::string m_id;
};

#endif // ZGOPTASKITACTION_H

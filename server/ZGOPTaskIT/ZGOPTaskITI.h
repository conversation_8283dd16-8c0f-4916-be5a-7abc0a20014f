#ifndef ZG6000_ZGOPTASKITI_H
#define ZG6000_ZGOPTASKITI_H

#include <ZGOPTaskIT.h>

namespace ZG6000 {

class ZGOPTaskITI : public ZG6000::ZGOPTaskIT
{
public:

    ZGOPTaskITI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current) override;

    // ZGOPTaskBase interface
public:
    bool deleteTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool getTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current) override;
    bool startTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool pauseTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool resumeTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool retryTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool abolishTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool confirmTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;

    // ZGOPTaskIT interface
public:
    bool getTaskItems(std::string taskID, StringMap &task, ListStringMap &items, ErrorInfo &e, const Ice::Current &current) override;
    bool getItemActions(std::string itemID, StringMap &item, ListStringMap &actions, ErrorInfo &e, const Ice::Current &current) override;
    bool getTaskTypeObjects(StringMap params, ListStringMap& listObject, ErrorInfo& e, const Ice::Current& current) override;
    bool createTypicalTask(StringMap params, ErrorInfo &e, const Ice::Current &current) override;
    bool createSpecialTask(std::string taskTypeID, StringList listObjectID, StringMap params, std::string &taskID, ErrorInfo &e, const Ice::Current &current) override;
    bool createUAVTask(StringMap task, ListStringMap listPreset, ListStringMap listItem, ListStringMap listAction, ErrorInfo& e, const Ice::Current& current) override;
    bool createCustomTask(ListStringMap listItem, StringMap params, std::string &taskID, ErrorInfo &e, const Ice::Current &current) override;
    bool editTask(std::string taskID, StringMap task, ListStringMap items, ErrorInfo &e, const Ice::Current &current) override;
    bool convertTask(std::string taskID, StringMap params, ErrorInfo &e, const Ice::Current &current) override;
    bool skipItem(std::string itemID, StringMap params, ErrorInfo &e, const Ice::Current &current) override;
    bool updateTask(ListStringMap listTask, ErrorInfo& e, const Ice::Current& current) override;
    bool updateItem(ListStringMap listItem, ErrorInfo& e, const Ice::Current& current) override;
    bool updateAction(ListStringMap listAction, ErrorInfo& e, const Ice::Current& current) override;
    bool downloadTask(StringList listTaskID, ListStringMap& listTask, ListStringMap& listItem, ListStringMap& listAction, ErrorInfo& e, const Ice::Current& current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGOPTASKITI_H

#ifndef ZGOPTASKITITEM_H
#define ZGOPTASKITITEM_H

#include <QObject>
#include <QDateTime>
#include "ZGServerCommon.h"
#include "ZGOPTaskITAction.h"

class ZGOPTaskITItem : public QObject
{
    Q_OBJECT
public:
    explicit ZGOPTaskITItem(QObject *parent = nullptr);
    bool initialize();
    const std::string& id() const { return m_id; }
    void setId(const std::string& id);
    bool currentState(std::string& state) const;
    bool setCurrentState(const std::string& state);
    void updateCurrentActionID(const std::string& actionID);
    std::string getCurrentActionID() {
        return m_currentAction->id();
    }
    bool setItemError(const std::string& errorDesc);
    bool nextAction();
    void updateLastStartTime() { m_lastStartTime = QDateTime::currentDateTime(); }
    virtual void onTimer(const ZG6000::StringMap& task);

protected:
    virtual void processReadyState(const ZG6000::StringMap& item, QDateTime& time);
    virtual void processFinishState(const ZG6000::StringMap& item, QDateTime& time);

private:
    bool checkCondition(const ZG6000::StringMap& task);

private:
    std::string m_id;
    ZGOPTaskITAction* m_currentAction{nullptr};
    QDateTime m_lastStartTime;
};

#endif // ZGOPTASKITITEM_H

#include "ZGSPStatistic.h"

#include <QThread>

#include "ZGRuleProcess.h"
#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"

ZGSPStatistic::ZGSPStatistic(QObject* parent) : QObject(parent)
{
}

bool ZGSPStatistic::initialize()
{
    initStatisticType();
    return true;
}

void ZGSPStatistic::statistic(const std::string& id, bool start)
{
    ZG6000::StringMap mapFieldValue;
    if (!ZGProxyCommon::getDataByID("sp_param_statistic", id, mapFieldValue))
    {
        ZGLOG_ERROR(QString("getDataByIDToMap error, id = %1").arg(id.c_str()));
        return;
    }
    StatisticParam statisticParam;
    if (!parseStatisticParam(mapFieldValue, statisticParam))
    {
        ZGLOG_ERROR("parseStatisticParam error.");
        return;
    }
    auto pair = m_mapTypeProcess.find(statisticParam.statisticTypeID);
    if (pair == m_mapTypeProcess.end())
    {
        ZGLOG_ERROR(QString("Can't find statisticTypeID %1").arg(statisticParam.statisticTypeID.c_str()));
        return;
    }
    if (start)
    {
        if (!statisticParam.rtUpdateTime.empty())
        {
            ZGLOG_FATAL(QString("updateTime:%1, newUpdateTime:%2").arg(statisticParam.rtUpdateTime.c_str()).arg(statisticParam.rtNewUpdateTime.c_str()));
            std::string rtUpdateTimeTemp = statisticParam.rtUpdateTime;
            std::string rtNewUpdateTimeTemp = statisticParam.rtNewUpdateTime;
            size_t dotPos = rtUpdateTimeTemp.find_last_of(":");
            if (dotPos != std::string::npos)
            {
                statisticParam.rtNewUpdateTime = rtUpdateTimeTemp.replace(dotPos + 1, 6, "59.999");
                ZGLOG_FATAL(QString("updateTime:%1, newUpdateTime:%2").arg(statisticParam.rtUpdateTime.c_str()).arg(statisticParam.rtNewUpdateTime.c_str()));
                pair->second(statisticParam);
            }
            else
            {
                ZGLOG_ERROR(QString("%1 is not a valid time format").arg(rtUpdateTimeTemp.c_str()));
                return;
            }
            QThread::msleep(100);
            statisticParam.rtNewUpdateTime = rtNewUpdateTimeTemp;
            statisticParam.rtNewUpdateTime.replace(dotPos + 1, 6, "00.000");
            ZGLOG_FATAL(QString("newUpdateTime:%1").arg(statisticParam.rtNewUpdateTime.c_str()));
            updateStartStasticValue(statisticParam, start);
        }
        else
            updateStartStasticValue(statisticParam, start);
    }
    else
        pair->second(statisticParam);
}

std::string ZGSPStatistic::dataTypeToString(DataType dataType)
{
    switch (dataType)
    {
    case DataType::dtText:
        return "string";
    case DataType::dtNumber:
        return "int";
    case DataType::dtReal:
        return "double";
    }
    return "";
}

void ZGSPStatistic::updateStartStasticValue(const StatisticParam& statisticParam, bool start)
{
    ZG6000::StringMap mapFieldValue;
    mapFieldValue.insert(std::make_pair("rtValue", ""));
    mapFieldValue.insert(std::make_pair("rtOccurTime", ""));
    mapFieldValue.insert(std::make_pair("rtSampleTotalValue", ""));
    mapFieldValue.insert(std::make_pair("rtSampleNum", ""));
    mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
    if (start)
        mapFieldValue.insert(std::make_pair("rtIsNewStatistic", "1"));
    if (!ZGProxyCommon::updateDataByID("sp_param_statistic", statisticParam.id, mapFieldValue))
    {
        ZGLOG_ERROR(QString("updateDataByIDFromMap error, id = %1").arg(statisticParam.id.c_str()));
    }
}

bool ZGSPStatistic::parseStatisticParam(ZG6000::StringMap& mapFieldValue, StatisticParam& statisticParam)
{
    try
    {
        statisticParam.id = ZGUtils::get(mapFieldValue, "id");
        statisticParam.statisticTypeID = ZGUtils::get(mapFieldValue, "statisticTypeID");
        statisticParam.statisticIntervalID = ZGUtils::get(mapFieldValue, "statisticIntervalID");
        statisticParam.statisticModeID = ZGUtils::get(mapFieldValue, "statisticModeID");
        statisticParam.tableName = ZGUtils::get(mapFieldValue, "tableName");
        statisticParam.dataID = ZGUtils::get(mapFieldValue, "dataID");
        statisticParam.fieldName = ZGUtils::get(mapFieldValue, "fieldName");
        statisticParam.key = statisticParam.tableName + "/" + statisticParam.dataID + "$" + statisticParam.fieldName;
        statisticParam.operatorID = ZGUtils::get(mapFieldValue, "operatorID");
        statisticParam.operatorValue = ZGUtils::get(mapFieldValue, "value");
        statisticParam.sampleRatio = ZGUtils::get(mapFieldValue, "sampleRatio");
        statisticParam.rtValue = ZGUtils::get(mapFieldValue, "rtValue");
        statisticParam.rtOccurTime = ZGUtils::get(mapFieldValue, "rtOccurTime");
        statisticParam.rtSampleTotalValue = ZGUtils::get(mapFieldValue, "rtSampleTotalValue");
        statisticParam.rtSampleNum = ZGUtils::get(mapFieldValue, "rtSampleNum");
        statisticParam.rtUpdateTime = ZGUtils::get(mapFieldValue, "rtUpdateTime");
        QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
        statisticParam.rtNewUpdateTime = currentTime.toStdString();
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

void ZGSPStatistic::initStatisticType()
{
    m_mapTypeProcess.insert({"ZG_ST_MAX", [this](auto&& placeHolder) 
        { calculateMax(std::forward<decltype(placeHolder)>(placeHolder)); }});
    m_mapTypeProcess.insert({"ZG_ST_MIN", [this](auto&& placeHolder) 
        { calculateMin(std::forward<decltype(placeHolder)>(placeHolder)); }});
    m_mapTypeProcess.insert({"ZG_ST_AVG", [this](auto&& placeHolder) 
        { calculateAvg(std::forward<decltype(placeHolder)>(placeHolder)); }});
    m_mapTypeProcess.insert({"ZG_ST_COUNT", [this](auto&& placeHolder) 
        { calculateCount(std::forward<decltype(placeHolder)>(placeHolder)); }});
    m_mapTypeProcess.insert({ "ZG_ST_SUM", [this](auto&& placeHolder)
        { calculateSum(std::forward<decltype(placeHolder)>(placeHolder)); } });
}

bool ZGSPStatistic::evaluateByMode(const StatisticParam& statisticParam, bool& result)
{
    try
    {
        if (statisticParam.statisticModeID == "ZG_SM_POS")
        {
            switch (statisticParam.dataType)
            {
            case DataType::dtText:
                return false;
            case DataType::dtNumber:
                result = std::stoi(statisticParam.dataValue) > 0;
                return true;
            case DataType::dtReal:
                result = std::stod(statisticParam.dataValue) > ZGUtils::eps;
                return true;
            }
        }
        if (statisticParam.statisticModeID == "ZG_SM_NEG")
        {
            switch (statisticParam.dataType)
            {
            case DataType::dtText:
                return false;
            case DataType::dtNumber:
                result = std::stoi(statisticParam.dataValue) < 0;
                return true;
            case DataType::dtReal:
                result = std::stod(statisticParam.dataValue) < -ZGUtils::eps;
                return true;
            }
        }
        if (statisticParam.statisticModeID == "ZG_SM_ABS")
        {
            return statisticParam.dataType != DataType::dtText;
        }
        return true;
    }
    catch (const std::exception&)
    {
        ZGLOG_ERROR(QString("Convert dataValue error, dataValue = %1").arg(statisticParam.dataValue.c_str()));
        return false;
    }
}

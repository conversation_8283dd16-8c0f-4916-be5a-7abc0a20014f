#include "ZGSPHisStatisticProcessMng.h"

#include "ZGDebugMng.h"
#include "ZGHeartMng.h"
#include "ZGRuntime.h"
#include "ZGSPHisStatistic.h"
#include "ZGUtils.h"

static ZGSPHisStatisticProcessMng* g_pInstance = nullptr;

ZGSPHisStatisticProcessMng* ZGSPHisStatisticProcessMng::instance()
{
	if (g_pInstance == nullptr)
		g_pInstance = new ZGSPHisStatisticProcessMng;
	return g_pInstance;
}

void ZGSPHisStatisticProcessMng::init()
{
	initEvents();
	initServerInstConfig();
	start();
	ZGLOG_INFO("ZGSPHisStatisticProcess init start...");
}

bool ZGSPHisStatisticProcessMng::checkState()
{
	return m_initialized;
}

void ZGSPHisStatisticProcessMng::statistic(const std::string& id)
{
	m_pStatistic->statistic(id);
}

void ZGSPHisStatisticProcessMng::statisticBatch(const ZG6000::StringList& listID)
{
	for (const auto& id : listID)
	{
		m_pStatistic->statistic(id);
	}
}

void ZGSPHisStatisticProcessMng::statisticStart(const std::string& id)
{
	m_pStatistic->statistic(id, true);
}

void ZGSPHisStatisticProcessMng::statisticStartBatch(const ZG6000::StringList& listID)
{
	for (const auto& id : listID)
	{
		m_pStatistic->statistic(id, true);
	}
}

void ZGSPHisStatisticProcessMng::run()
{
	while (!initServerInstInfo())
	{
		ZGLOG_ERROR("initServerInstInfo error.");
		msleep(m_initInterval * 1000);
	}
	while (!m_pStatistic->initialize())
	{
		ZGLOG_ERROR("Statistic initialize error.");
		msleep(m_initInterval * 1000);
	}
	m_masterInst = ZGRuntime::instance()->isMaster();
	m_initialized = true;
	emit initFinished();
	ZGLOG_INFO("ZGSPHisStatisticProcess init finished.");
}

ZGSPHisStatisticProcessMng::ZGSPHisStatisticProcessMng(QObject *parent) : QThread(parent)
{
	m_pStatistic = new ZGSPHisStatistic;
}

void ZGSPHisStatisticProcessMng::initEvents()
{
	connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPHisStatisticProcessMng::onCheckStatus);
	connect(this, &ZGSPHisStatisticProcessMng::initFinished, this, &ZGSPHisStatisticProcessMng::onInitFinished);
}

void ZGSPHisStatisticProcessMng::initServerInstConfig()
{
	const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
	QString errMsg;
	int value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_initInterval = value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_checkInterval = value;
}

bool ZGSPHisStatisticProcessMng::initServerInstInfo()
{
	m_serverName = ZGRuntime::instance()->getServerID();
	if (m_serverName.isEmpty())
	{
		ZGLOG_ERROR("Empty server id.");
		return false;
	}
	m_instName = ZGRuntime::instance()->getInstanceID();
	if (m_instName.isEmpty())
	{
		ZGLOG_ERROR("Empty server instance id.");
		return false;
	}
	return true;
}

void ZGSPHisStatisticProcessMng::onInitFinished()
{
	m_checkTimer.start(m_checkInterval * 1000);
}

void ZGSPHisStatisticProcessMng::onCheckStatus()
{
	m_masterInst = ZGRuntime::instance()->isMaster();
}

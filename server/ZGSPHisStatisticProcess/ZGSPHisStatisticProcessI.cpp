#include "ZGSPHisStatisticProcessI.h"
#include "ZGSPHisStatisticProcessMng.h"


ZG6000::ZGSPHisStatisticProcessI::ZGSPHisStatisticProcessI()
{
	ZGSPHisStatisticProcessMng::instance()->init();
}

bool ZG6000::ZGSPHisStatisticProcessI::checkState(const Ice::Current& current)
{
	return ZGSPHisStatisticProcessMng::instance()->checkState();
}

void ZG6000::ZGSPHisStatisticProcessI::statistic(std::string id, const Ice::Current& current)
{
	ZGSPHisStatisticProcessMng::instance()->statistic(id);
}

void ZG6000::ZGSPHisStatisticProcessI::statisticBatch(StringList listID, const Ice::Current& current)
{
	ZGSPHisStatisticProcessMng::instance()->statisticBatch(listID);
}

void ZG6000::ZGSPHisStatisticProcessI::statisticStart(std::string id, const Ice::Current& current)
{
	ZGSPHisStatisticProcessMng::instance()->statisticStart(id);
}

void ZG6000::ZGSPHisStatisticProcessI::statisticStartBatch(StringList listID, const Ice::Current& current)
{
	ZGSPHisStatisticProcessMng::instance()->statisticStartBatch(listID);
}

void ZG6000::ZGSPHisStatisticProcessI::statisticStartAndCalc(std::string id, const Ice::Current& current)
{
}

#ifndef ZGSPHISSTATISTICPROCESSI_H
#define ZGSPHISSTATISTICPROCESSI_H

#include "ZGSPHisStatisticProcess.h"

namespace ZG6000
{
class ZGSPHisStatisticProcessI : public ZGSPHisStatisticProcess
{
public:
    ZGSPHisStatisticProcessI();
    bool checkState(const Ice::Current& current) override;
    void statistic(std::string id, const Ice::Current& current) override;
    void statisticBatch(StringList listID, const Ice::Current& current) override;
    void statisticStart(std::string id, const Ice::Current& current) override;
    void statisticStartBatch(StringList listID, const Ice::Current& current) override;
    void statisticStartAndCalc(std::string id, const Ice::Current& current) override;
};
}

#endif // ZGSPHISSTATISTICPROCESSI_H

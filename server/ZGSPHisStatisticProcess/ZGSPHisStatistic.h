#ifndef ZGSPHISSTATISTIC_H
#define ZGSPHISSTATISTIC_H

#include "ZGSPStatistic.h"

class ZGSPHisStatistic : public ZGSPStatistic
{
	Q_OBJECT
public:
	explicit ZGSPHisStatistic(QObject* parent = nullptr);
protected:
	void calculateAvg(const StatisticParam& statisticParam) override;
	void calculateCount(const StatisticParam& statisticParam) override;
	void calculateMax(const StatisticParam& statisticParam) override;
	void calculateMin(const StatisticParam& statisticParam) override;
	void calculateSum(const StatisticParam& statisticParam) override;

private:
	using Calculate = std::function<void(const ZG6000::ListStringMap&, ZG6000::StringMap&)>;
	void calculateByChange(const StatisticParam& statisticParam, const Calculate& calculate);
	bool getChangeTableName(const std::string& realTableName, const std::string& updateTime, std::string& storeTableName);
	bool getDatasetFromChange(const StatisticParam& statisticParam, ZG6000::ListStringMap& listResult);
};

#endif // ZGSPHISSTATISTIC_H

#ifndef ZGSPHISSTATISTICPROCESSMNG_H
#define ZGSPHISSTATISTICPROCESSMNG_H

#include <QThread>
#include <QTimer>
#include <ZGServerCommon.h>

class ZGSPStatistic;
class ZGSPHisStatisticProcessMng : public QThread
{
    Q_OBJECT
public:
    static ZGSPHisStatisticProcessMng* instance();
    void init();
    bool checkState();
    void statistic(const std::string& id);
    void statisticBatch(const ZG6000::StringList& listID);
    void statisticStart(const std::string& id);
    void statisticStartBatch(const ZG6000::StringList& listID);

protected:
    void run() override;

private:
    explicit ZGSPHisStatisticProcessMng(QObject *parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();

signals:
    void initFinished();

private slots:
    void onInitFinished();
    void onCheckStatus();

private:
    bool m_initialized{ false };
    QString m_serverName;
    QString m_instName;
    bool m_masterInst{ false };
    int m_initInterval{ 10 };
    int m_checkInterval{ 60 };
    QTimer m_checkTimer;
    int m_currentStep{ 0 };
    ZGSPStatistic* m_pStatistic{ nullptr };
};

#endif // ZGSPHISSTATISTICPROCESSMNG_H

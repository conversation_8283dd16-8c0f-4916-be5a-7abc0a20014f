#include "ZGSPHisStatistic.h"
#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"

void ZGSPHisStatistic::calculateAvg(const StatisticParam& statisticParam)
{
    calculateByChange(statisticParam,
        [&statisticParam](const ZG6000::ListStringMap& listResult, ZG6000::StringMap& mapFieldValue)-> void
    {
        try
        {
            double totalValue = std::atof(statisticParam.rtSampleTotalValue.c_str());
            int totalNum = std::atoi(statisticParam.rtSampleNum.c_str());
            for (const auto& result : listResult)
            {
                try
                {
                    double value = std::stod(ZGUtils::get(result, "newValue"));
                    totalValue += value;
                    ++totalNum;
                }
                catch (const std::exception& e)
                {
                    ZGLOG_WARN(e.what());
                }
            }
            double newAvgValue = totalValue / totalNum;
            mapFieldValue.insert(std::make_pair("rtSampleTotalValue", std::to_string(totalValue)));
            mapFieldValue.insert(std::make_pair("rtSampleNum", std::to_string(totalNum)));
            mapFieldValue.insert(std::make_pair("rtValue", std::to_string(newAvgValue)));
            mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(QString("calculate error, %1, statisticID = %2")
                .arg(e.what()).arg(statisticParam.id.c_str()));
        }
    });
}

void ZGSPHisStatistic::calculateCount(const StatisticParam& statisticParam)
{
    calculateByChange(statisticParam,
        [&statisticParam](const ZG6000::ListStringMap& listResult, ZG6000::StringMap& mapFieldValue)->void
    {
        int count = std::atoi(statisticParam.rtValue.c_str());
        count += static_cast<int>(listResult.size());
        mapFieldValue.insert(std::make_pair("rtValue", std::to_string(count)));
        mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
    });
}

void ZGSPHisStatistic::calculateMax(const StatisticParam& statisticParam)
{
    calculateByChange(statisticParam,
        [&statisticParam](const ZG6000::ListStringMap& listResult, ZG6000::StringMap& mapFieldValue)->void
    {
	    try
	    {
            const auto& newValue = ZGUtils::get(listResult[0], "newValue");
            const auto& changeTime = ZGUtils::get(listResult[0], "changeTime");
            if (statisticParam.rtValue.empty())
            {
                mapFieldValue.insert(std::make_pair("rtValue", newValue));
                mapFieldValue.insert(std::make_pair("rtOccurTime", changeTime));
            }
            else
            {
                if (ZGUtils::isNumber(statisticParam.rtValue))
                {
                    int maxValue = std::stoi(statisticParam.rtValue);
                    std::string updateTime = changeTime;
                    for (const auto& result : listResult)
                    {
                        try
                        {
                            int tickValue = std::stoi(ZGUtils::get(result, "newValue"));
                            if (tickValue > maxValue)
                            {
                                maxValue = tickValue;
                                updateTime = ZGUtils::get(result, "changeTime");
                            }
                        }
                        catch (const std::exception& e)
                        {
                            ZGLOG_ERROR(e.what());
                        }
                    }
                    if (updateTime != changeTime)
                    {
                        mapFieldValue.insert(std::make_pair("rtValue", std::to_string(maxValue)));
                        mapFieldValue.insert(std::make_pair("rtOccurTime", updateTime));
                    }
                }
                else if (ZGUtils::isReal(statisticParam.rtValue))
                {
                    double maxValue = std::stod(statisticParam.rtValue);
                    std::string updateTime = changeTime;
                    for (const auto& result : listResult)
                    {
                        try
                        {
                            double tickValue = std::stod(ZGUtils::get(result, "newValue"));
                            if (tickValue - maxValue > ZGUtils::eps)
                            {
                                maxValue = tickValue;
                                updateTime = ZGUtils::get(result, "changeTime");
                            }
                        }
                        catch (const std::exception& e)
                        {
                            ZGLOG_ERROR(e.what());
                        }
                    }
                    if (updateTime != changeTime)
                    {
                        mapFieldValue.insert(std::make_pair("rtValue", std::to_string(maxValue)));
                        mapFieldValue.insert(std::make_pair("rtOccurTime", updateTime));
                    }
                }
                else
                {
                    if (newValue > statisticParam.rtValue)
                    {
                        mapFieldValue.insert(std::make_pair("rtValue", newValue));
                        mapFieldValue.insert(std::make_pair("rtOccurTime", changeTime));
                    }
                }
            }
	    }
	    catch (const std::exception& e)
	    {
            ZGLOG_ERROR(QString("calculate error, %1, statisticID = %2")
                .arg(e.what()).arg(statisticParam.id.c_str()));
	    }
    });
}

void ZGSPHisStatistic::calculateMin(const StatisticParam& statisticParam)
{
    calculateByChange(statisticParam,
        [&statisticParam](const ZG6000::ListStringMap& listResult, ZG6000::StringMap& mapFieldValue)->void
        {
            try
            {
                const auto& newValue = ZGUtils::get(listResult[0], "newValue");
                const auto& changeTime = ZGUtils::get(listResult[0], "changeTime");
                if (statisticParam.rtValue.empty())
                {
                    mapFieldValue.insert(std::make_pair("rtValue", newValue));
                    mapFieldValue.insert(std::make_pair("rtOccurTime", changeTime));
                }
                else
                {
                    if (ZGUtils::isNumber(statisticParam.rtValue))
                    {
                        int minValue = std::stoi(statisticParam.rtValue);
                        std::string updateTime = changeTime;
                        for (const auto& result : listResult)
                        {
                            try
                            {
                                int tickValue = std::stoi(ZGUtils::get(result, "newValue"));
                                if (minValue > tickValue)
                                {
                                    minValue = tickValue;
                                    updateTime = ZGUtils::get(result, "changeTime");
                                }
                            }
                            catch (const std::exception& e)
                            {
                                ZGLOG_ERROR(e.what());
                            }
                        }
                        if (updateTime != changeTime)
                        {
                            mapFieldValue.insert(std::make_pair("rtValue", std::to_string(minValue)));
                            mapFieldValue.insert(std::make_pair("rtOccurTime", updateTime));
                        }
                    }
                    else if (ZGUtils::isNumber(statisticParam.rtValue) || ZGUtils::isReal(statisticParam.rtValue))
                    {
                        double minValue = std::stod(statisticParam.rtValue);
                        std::string updateTime = changeTime;
                        for (const auto& result : listResult)
                        {
                            try
                            {
                                double tickValue = std::stod(ZGUtils::get(result, "newValue"));
                                if (minValue - tickValue > ZGUtils::eps)
                                {
                                    minValue = tickValue;
                                    updateTime = ZGUtils::get(result, "changeTime");
                                }
                            }
                            catch (const std::exception& e)
                            {
                                ZGLOG_ERROR(e.what());
                            }
                        }
                        if (updateTime != changeTime)
                        {
                            mapFieldValue.insert(std::make_pair("rtValue", std::to_string(minValue)));
                            mapFieldValue.insert(std::make_pair("rtOccurTime", updateTime));
                        }
                    }
                    else
                    {
                        if (newValue > statisticParam.rtValue)
                        {
                            mapFieldValue.insert(std::make_pair("rtValue", newValue));
                            mapFieldValue.insert(std::make_pair("rtOccurTime", changeTime));
                        }
                    }
                }
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(QString("calculate error, %1, statisticID = %2")
                    .arg(e.what()).arg(statisticParam.id.c_str()));
            }
        });
}

void ZGSPHisStatistic::calculateSum(const StatisticParam& statisticParam)
{
    calculateByChange(statisticParam,
        [&statisticParam](const ZG6000::ListStringMap& listResult, ZG6000::StringMap& mapFieldValue)->void
    {
        double totalValue = std::atof(statisticParam.rtValue.c_str());
        for (const auto& result : listResult)
        {
            try
            {
                const auto& newValue = ZGUtils::get(result, "newValue");
                double value = std::stod(newValue);
                totalValue += value;
            }
            catch (const std::exception& e)
            {
                ZGLOG_WARN(e.what());
            }
        }
        mapFieldValue.insert(std::make_pair("rtValue", std::to_string(totalValue)));
        mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
    });
}

void ZGSPHisStatistic::calculateByChange(const StatisticParam& statisticParam, const Calculate& calculate)
{
    if (statisticParam.rtUpdateTime.empty())
        updateStartStasticValue(statisticParam, false);
    else
    {
        ZG6000::ListStringMap listResult;
        if (!getDatasetFromChange(statisticParam, listResult))
        {
            ZGLOG_ERROR("getDatasetFromChange error.");
            return;
        }
        ZG6000::StringMap mapFieldValue;
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        if (!listResult.empty())
            calculate(listResult, mapFieldValue);
        if (!ZGProxyCommon::updateDataByID("sp_param_statistic", statisticParam.id, mapFieldValue))
            ZGLOG_ERROR(QString("updateDataByIDFromMap error, id = %1").arg(statisticParam.id.c_str()));
    }
}

bool ZGSPHisStatistic::getChangeTableName(const std::string& realTableName, const std::string& updateTime,
	std::string& storeTableName)
{
    storeTableName = realTableName;
    auto pos = storeTableName.find("param");
    if (pos == std::string::npos)
    {
        ZGLOG_ERROR(QString("Invalid table name %1.").arg(storeTableName.c_str()));
        return false;
    }
    storeTableName.replace(pos, std::strlen("param"), "his");
    return true;
}

bool ZGSPHisStatistic::getDatasetFromChange(const StatisticParam& statisticParam, ZG6000::ListStringMap& listResult)
{
    std::string changeTableName;
    if (!getChangeTableName(statisticParam.tableName, statisticParam.rtUpdateTime, changeTableName))
        return false;
    std::string fieldName = "newValue";
    if (statisticParam.statisticModeID == "ZG_SM_ABS")
        fieldName = "ABS(" + fieldName + ") AS newValue";
    fieldName += ", changeTime";
    std::string condition = "dataID = '" + statisticParam.dataID + "' AND fieldName = '" + statisticParam.fieldName + "'";
    condition += " AND changeTime > '" + statisticParam.rtUpdateTime + "' AND changeTime <= '" + statisticParam
        .rtNewUpdateTime + "'";
    if (statisticParam.statisticModeID == "ZG_SM_POS")
        condition += " AND newValue >= '0'";
    else if (statisticParam.statisticModeID == "ZG_SM_NEG")
        condition += " AND newValue <= '0'";
    if (!statisticParam.operatorID.empty() && !statisticParam.operatorValue.empty())
    {
        std::string operatorID = statisticParam.operatorID;
        if (operatorID == "==")
            operatorID = "=";
        condition += " AND newValue " + operatorID + " '" +
            statisticParam.operatorValue + "'";
    }
    condition += " ORDER BY newValue DESC";
    std::string sql = "SELECT " + fieldName + " FROM " + changeTableName + " WHERE " + condition;
    qDebug() << sql.c_str();
    return ZGProxyCommon::execQuerySql(sql, listResult, true);
}

ZGSPHisStatistic::ZGSPHisStatistic(QObject *parent) : ZGSPStatistic(parent)
{

}

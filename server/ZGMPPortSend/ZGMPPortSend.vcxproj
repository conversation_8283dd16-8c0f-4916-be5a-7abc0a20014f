<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{9AC0F612-11EF-3E7A-8BC3-64CC0EED81C7}</ProjectGuid>
    <RootNamespace>ZGMPPortSend</RootNamespace>
    <Keyword>QtVS_v304</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformMinVersion>10.0.19041.0</WindowsTargetPlatformMinVersion>
    <QtMsBuild Condition="'$(QtMsBuild)'=='' OR !Exists('$(QtMsBuild)\qt.targets')">$(MSBuildProjectDirectory)\QtMsBuild</QtMsBuild>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <PlatformToolset>v142</PlatformToolset>
    <OutputDirectory>..\..\..\bin\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>Application</ConfigurationType>
    <IntermediateDirectory>..\..\..\tmp\ZGMPPortSend\obj\</IntermediateDirectory>
    <PrimaryOutput>ZGMPPortSend</PrimaryOutput>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <PlatformToolset>v142</PlatformToolset>
    <OutputDirectory>..\..\..\bin\</OutputDirectory>
    <ATLMinimizesCRunTimeLibraryUsage>false</ATLMinimizesCRunTimeLibraryUsage>
    <CharacterSet>NotSet</CharacterSet>
    <ConfigurationType>Application</ConfigurationType>
    <IntermediateDirectory>..\..\..\tmp\ZGMPPortSendd\obj\</IntermediateDirectory>
    <PrimaryOutput>ZGMPPortSendd</PrimaryOutput>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <Import Project="$(QtMsBuild)\qt_defaults.props" Condition="Exists('$(QtMsBuild)\qt_defaults.props')" />
  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <QtInstall>6.5.3_msvc2019_64</QtInstall>
    <QtModules>core;network</QtModules>
  </PropertyGroup>
  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <QtInstall>6.5.3_msvc2019_64</QtInstall>
    <QtModules>core;network</QtModules>
  </PropertyGroup>
  <Target Name="QtMsBuildNotFound" BeforeTargets="CustomBuild;ClCompile" Condition="!Exists('$(QtMsBuild)\qt.targets') OR !Exists('$(QtMsBuild)\Qt.props')">
    <Message Importance="High" Text="QtMsBuild: could not locate qt.targets, qt.props; project may not build correctly." />
  </Target>
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
    <Import Project="$(QtMsBuild)\Qt.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
    <Import Project="$(QtMsBuild)\Qt.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">..\..\..\tmp\ZGMPPortSend\obj\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ZGMPPortSend</TargetName>
    <IgnoreImportLibrary Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</IgnoreImportLibrary>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">..\..\..\tmp\ZGMPPortSendd\obj\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ZGMPPortSendd</TargetName>
    <IgnoreImportLibrary Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</IgnoreImportLibrary>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;..\..\include;..\..\include\thirdparty;..\..\ice;..\..\..\tmp\ZGMPPortSend\moc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -utf-8 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>..\..\..\tmp\ZGMPPortSend\obj\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>None</DebugInformationFormat>
      <DisableSpecificWarnings>4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ObjectFileName>..\..\..\tmp\ZGMPPortSend\obj\</ObjectFileName>
      <Optimization>MaxSpeed</Optimization>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;NDEBUG;QT_NO_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <ProgramDataBaseFileName></ProgramDataBaseFileName>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <AdditionalDependencies>D:\ZG\ZG6000\lib\ZGRuntime.lib;D:\ZG\ZG6000\lib\ZGPubFun.lib;D:\ZG\ZG6000\lib\ZGProxyMng.lib;D:\ZG\ZG6000\lib\ZGServerApplication.lib;D:\ZG\ZG6000\lib\ZGDebugMng.lib;D:\ZG\ZG6000\lib\ZGServerBase.lib;D:\ZG\ZG6000\lib\ZGRedisClient.lib;D:\ZG\ZG6000\lib\ZGJson.lib;D:\ZG\ZG6000\lib\ZGProxyCommon.lib;E:\Library\ice-3.7.6\lib\x64\Release\ice37++11.lib;$(QTDIR)\lib\Qt6EntryPoint.lib;shell32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:\ZG\ZG6000\lib;E:\Library\ice-3.7.6\lib\x64\Release;E:\Library\ice-3.7.6\lib\x64\Debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'" %(AdditionalOptions)</AdditionalOptions>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreImportLibrary>true</IgnoreImportLibrary>
      <LinkIncremental>false</LinkIncremental>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)\ZGMPPortSend.exe</OutputFile>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <SubSystem>Windows</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Link>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;NDEBUG;QT_NO_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <QtMoc>
      <CompilerFlavor>msvc</CompilerFlavor>
      <Include>D:/ZG/ZG6000/tmp/ZGMPPortSend/moc/moc_predefs.h</Include>
      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>
      <DynamicSource>output</DynamicSource>
      <QtMocDir>D:\ZG\ZG6000\tmp\ZGMPPortSend\moc</QtMocDir>
      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>
    </QtMoc>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>.;E:\Library\ice-3.7.6\include;..\..\include;..\..\include\thirdparty;..\..\ice;..\..\..\tmp\ZGMPPortSendd\moc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -utf-8 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 %(AdditionalOptions)</AdditionalOptions>
      <AssemblerListingLocation>..\..\..\tmp\ZGMPPortSendd\obj\</AssemblerListingLocation>
      <BrowseInformation>false</BrowseInformation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4577;4467;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <ObjectFileName>..\..\..\tmp\ZGMPPortSendd\obj\</ObjectFileName>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PreprocessToFile>false</PreprocessToFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <AdditionalDependencies>ZGRuntimed.lib;ZGPubFund.lib;ZGProxyMngd.lib;ZGServerApplicationd.lib;ZGDebugMngd.lib;ZGServerBased.lib;D:\ZG\ZG6000\lib\ZGRedisClientd.lib;ZGJsond.lib;ZGProxyCommond.lib;E:\Library\ice-3.7.6\lib\x64\Debug\ice37++11d.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:\ZG\ZG6000\lib;E:\Library\ice-3.7.6\lib\x64\Release;E:\Library\ice-3.7.6\lib\x64\Debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'" %(AdditionalOptions)</AdditionalOptions>
      <DataExecutionPrevention>true</DataExecutionPrevention>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreImportLibrary>true</IgnoreImportLibrary>
      <OutputFile>$(OutDir)\ZGMPPortSendd.exe</OutputFile>
      <RandomizedBaseAddress>true</RandomizedBaseAddress>
      <SubSystem>Console</SubSystem>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Link>
    <Midl>
      <DefaultCharType>Unsigned</DefaultCharType>
      <EnableErrorChecks>None</EnableErrorChecks>
      <WarningLevel>0</WarningLevel>
    </Midl>
    <ResourceCompile>
      <PreprocessorDefinitions>_CONSOLE;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;ICE_CPP11_MAPPING;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <QtMoc>
      <CompilerFlavor>msvc</CompilerFlavor>
      <Include>D:/ZG/ZG6000/tmp/ZGMPPortSendd/moc/moc_predefs.h</Include>
      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>
      <DynamicSource>output</DynamicSource>
      <QtMocDir>D:\ZG\ZG6000\tmp\ZGMPPortSendd\moc</QtMocDir>
      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>
    </QtMoc>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\ice\ZGMPPortSend.cpp" />
    <ClCompile Include="ZGMPPortSendI.cpp" />
    <ClCompile Include="ZGMPPortSendMng.cpp" />
    <ClCompile Include="main.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\ice\ZGMPPortSend.h" />
    <ClInclude Include="ZGMPPortSendI.h" />
    <QtMoc Include="ZGMPPortSendMng.h" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\ZG\ZG6000\tmp\ZGMPPortSend\moc\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;D:\ZG\ZG6000\tmp\ZGMPPortSend\moc\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\ZG\ZG6000\tmp\ZGMPPortSend\moc\moc_predefs.h;%(Outputs)</Outputs>
    </CustomBuild>
    <CustomBuild Include="D:\ZG\ZG6000\tmp\ZGMPPortSendd\moc\moc_predefs.h.cbt">
      <FileType>Document</FileType>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\mkspecs\features\data\dummy.cpp;%(AdditionalInputs)</AdditionalInputs>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -Zi -MDd -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;D:\ZG\ZG6000\tmp\ZGMPPortSendd\moc\moc_predefs.h</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generate moc_predefs.h</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\ZG\ZG6000\tmp\ZGMPPortSendd\moc\moc_predefs.h;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <Import Project="$(QtMsBuild)\qt.targets" Condition="Exists('$(QtMsBuild)\qt.targets')" />
  <ImportGroup Label="ExtensionTargets" />
</Project>
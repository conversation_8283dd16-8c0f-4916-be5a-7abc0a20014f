#include "ZGMPPortSendMng.h"

#include "ZGDebugMng.h"
#include "ZGHeartMng.h"
#include "ZGJson.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include <QDebug>
#include <QRandomGenerator>

static ZGMPPortSendMng* g_pPortSendMng = nullptr;

ZGMPPortSendMng *ZGMPPortSendMng::instance()
{
    if (g_pPortSendMng == nullptr)
        g_pPortSendMng = new ZGMPPortSendMng;
    return g_pPortSendMng;
}

bool ZGMPPortSendMng::checkState()
{
    return m_initialized;
}

ZG6000::StringMap convertToStringMap(const ZG6000::MapField& mapField)
{
    ZG6000::StringMap stringMap;
    for (const auto& pair : mapField)
    {
        stringMap.insert(std::make_pair(pair.first, pair.second.newValue));
    }
    return stringMap;
}

void ZGMPPortSendMng::dispatchData(const std::string &tableName, const std::string &oper, const std::string &reason, const std::string &time, const ZG6000::ListRecord &listRecord)
{
    if (!m_initialized)
        return;
    if (oper != "update")
        return;
    ZG6000::ListStringMap listStringMap;
    if (tableName == "mp_param_device")
    {
        for (const auto& record : listRecord)
        {
            if (record.find("rtState") != record.end() || record.find("rtANetState") != record.end()
                || (record.find("rtBNetState") != record.end()) || (record.find("rtCNetState") != record.end())
                || (record.find("rtDNetState") != record.end()) || (record.find("rtMasterState") != record.end()))
                listStringMap.emplace_back(convertToStringMap(record));
        }
    }
    else
    {
        for (const auto& record : listRecord)
        {
            if (record.find("rtRawValue") != record.end() || record.find("rtQualityFlag") != record.end()
                || record.find("rtNewValue") != record.end())
                listStringMap.emplace_back(convertToStringMap(record));
        }
    }
    if (!listStringMap.empty())
    {
        std::pair<std::string, std::string> pair = m_mapCommand.at(tableName);
        transmitDataToPort(pair.second, listStringMap);
    }
}

void ZGMPPortSendMng::init()
{
    initEvents();
    initCache();
    initServerInstConfig();
    ZGLOG_INFO("ZGMPPortSend init start...");
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        msleep(m_initInterval * 1000);
    }
    while (!cacheDataPortMap())
    {
        ZGLOG_ERROR("cacheDataPortMap error.");
        msleep(m_initInterval * 1000);
    }
    while (!initRedisClient())
    {
        ZGLOG_ERROR("initRedisClient error.");
        msleep(m_initInterval * 1000);
    }
    while (!initRedisRtTopic())
    {
        ZGLOG_ERROR("initRedisRtTopic error.");
        msleep(m_initInterval * 1000);
    }
    onInitFinished();
    ZGLOG_INFO("ZGMPPortSend init finished...");
    start();
}

ZGMPPortSendMng::ZGMPPortSendMng(QObject *parent): QThread(parent)
{

}

void ZGMPPortSendMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGMPPortSendMng::onCheckInstStatus);
    connect(this, &ZGMPPortSendMng::initFinished, this, &ZGMPPortSendMng::onInitFinished);
}

void ZGMPPortSendMng::initCache()
{
    m_mapCommand["ZG_Q_PORT_YK_SEND"] = std::make_pair("mp_param_port_map_yk", "yk");
    m_mapCommand["ZG_Q_PORT_YS_SEND"] = std::make_pair("mp_param_port_map_ys", "ys");
    m_mapCommand["ZG_Q_PORT_YT_SEND"] = std::make_pair("mp_param_port_map_yt", "yt");
    m_mapCommand["mp_param_dataset_yc"] = std::make_pair("mp_param_port_map_yc", "yc");
    m_mapCommand["mp_param_dataset_yx"] = std::make_pair("mp_param_port_map_yx", "yx");
    m_mapCommand["mp_param_dataset_text"] = std::make_pair("mp_param_port_map_text", "text");
    m_mapCommand["mp_param_dataset_ym"] = std::make_pair("mp_param_port_map_ym", "ym");
    m_mapCommand["mp_param_dataset_param"] = std::make_pair("mp_param_port_map_param", "param");
    m_mapCommand["mp_param_device"] = std::make_pair("mp_param_device", "dev");
    m_mapCommand["ZG_T_SOE"] = std::make_pair("mp_param_port_map_yx", "soe");
    m_mapCommand["ZG_T_EVENT"] = std::make_pair("mp_param_port_map_event", "event");
    m_listQueue.push_back("ZG_Q_PORT_YK_SEND");
    m_listQueue.push_back("ZG_Q_PORT_YS_SEND");
    m_listQueue.push_back("ZG_Q_PORT_YT_SEND");
}

void ZGMPPortSendMng::initServerInstConfig()
{
    const auto & serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGMPPortSendMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGMPPortSendMng::initRedisClient()
{
    QList<ZGRuntime::REDIS_CLIENT_TYPE> listType;
    listType.append(ZGRuntime::REDIS_RT_QUEUE);
    listType.append(ZGRuntime::REDIS_RT_TOPIC);
    listType.append(ZGRuntime::REDIS_PORT_TOPIC);
    if (!ZGRuntime::instance()->initRedisClient(listType))
    {
        ZGLOG_ERROR("initRedisClient failed.");
        return false;
    }
    m_pRedisPortTopic = ZGRuntime::instance()->getRedisClientPortTopic();
    if (m_pRedisPortTopic == nullptr)
    {
        ZGLOG_ERROR("getRedisClientPortTopic failed.");
        return false;
    }
    m_pRedisRtQueue = ZGRuntime::instance()->getRedisClientRTQueue();
    if (m_pRedisRtQueue == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTQueue failed.");
        return false;
    }
    return true;
}

bool ZGMPPortSendMng::initRedisRtTopic()
{
    m_pRedisRtTopic = ZGRuntime::instance()->getRedisClientRTTopic();
    if (m_pRedisRtTopic == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTTopic failed.");
        return false;
    }
    if (!m_pRedisRtTopic->connected(true))
    {
        ZGLOG_ERROR("Redis RT topic is not connected.");
        return false;
    }
    connect(m_pRedisRtTopic, &ZGRedisClient::receivedMessage, this, &ZGMPPortSendMng::onReceivedMessage);
    ZG6000::StringList listTopic;
//    for (const auto& pair : m_mapMapPort)
//    {
//        listTopic.push_back(pair.first + "/yc");
//        listTopic.push_back(pair.first + "/yx");
//        listTopic.push_back(pair.first + "/text");
//        listTopic.push_back(pair.first + "/ym");
//    }
    listTopic.push_back("ZG_T_SOE");
    listTopic.push_back("ZG_T_EVENT");
    m_pRedisRtTopic->subscribe(listTopic);
    m_pRedisRtTopic->consume();
    return true;
}

bool ZGMPPortSendMng::getDataPortsByID(const std::string& id,
                                       ZG6000::StringList& listPort)
{
    const auto & pair = m_mapDataPort.find(id);
    if (pair == m_mapDataPort.end())
        return false;
    listPort = pair->second;
    return true;
}

void ZGMPPortSendMng::transmitDataToPort(const std::string& typeName, const ZG6000::ListStringMap& listStringMap)
{
    std::unordered_map<std::string, ZG6000::ListStringMap> mapPortData;
    for (const auto& stringMap : listStringMap)
    {        
        try
        {
            std::string id;
            id = stringMap.at("id");
            ZG6000::StringList listPort;
            if (!getDataPortsByID(id, listPort))
                continue;
            for (const auto& port : listPort)
            {
                auto pair = mapPortData.find(port);
                if (pair == mapPortData.end())
                {
                    ZG6000::ListStringMap listRecord;
                    listRecord.push_back(stringMap);
                    mapPortData.insert(std::make_pair(port, listRecord));
                }
                else
                {
                    pair->second.push_back(stringMap);
                }
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
    for (const auto& portData : mapPortData)
    {
        std::string json = ZGJson::convertToJson(portData.second);
        std::string topic = portData.first + "/send/" + typeName;
        if (typeName == "yk" || typeName == "ys" || typeName == "yt")
            ZGLOG_DEBUG_E(QString("Send to topic '%1'").arg(topic.c_str()), json.c_str());
        long long subscribeNum;
        std::string errMsg;
        if (!m_pRedisPortTopic->publish(topic, json, subscribeNum, errMsg))
            ZGLOG_ERROR(errMsg.c_str());
    }
}

void ZGMPPortSendMng::transmitRespToPort(const std::string& typeName, const ZG6000::StringMap& command)
{
	try
	{
        const auto& srcType = ZGUtils::get(command, "srcType");
        const auto& srcId = ZGUtils::get(command, "srcID");
        const auto& id = ZGUtils::get(command, "id");
        ZGLOG_DEBUG(QString("id: %1, srcType: %2, srcId: %3").arg(id.c_str()).arg(srcType.c_str()).arg(srcId.c_str()));
        if (srcType == "port")
        {
            std::string topic = srcId + "/send/" + typeName;
            long long subscribeNum;
            ZG6000::ListStringMap listCommand;
            listCommand.push_back(command);
            const auto& json = ZGJson::convertToJson(listCommand);
            ZGLOG_DEBUG_E(QString("Send to topic '%1'").arg(topic.c_str()), json.c_str());
            std::string errMsg;
            if (!m_pRedisPortTopic->publish(topic, ZGJson::convertToJson(listCommand), subscribeNum, errMsg))
                ZGLOG_ERROR(errMsg.c_str());
        }
	}
	catch (const std::exception& e)
	{
        ZGLOG_ERROR(e.what());
	}
}

bool ZGMPPortSendMng::cacheDataPortMap()
{
    m_mapDataPort.clear();
    m_mapMapPort.clear();
    if (!getMapPort(m_mapMapPort))
        return false;
    if (!cacheDataPortMapByTable("mp_param_port_map_yc", m_mapMapPort))
        return false;
    if (!cacheDataPortMapByTable("mp_param_port_map_yx", m_mapMapPort))
        return false;
    if (!cacheDataPortMapByTable("mp_param_port_map_text", m_mapMapPort))
        return false;
    if (!cacheDataPortMapByTable("mp_param_port_map_ym", m_mapMapPort))
        return false;
    if (!cacheDataPortMapByTable("mp_param_port_map_param", m_mapMapPort))
        return false;
    if (!cacheDataPortMapByTable("mp_param_port_map_event", m_mapMapPort))
        return false;
    if (!cacheDataPortMapByTable("mp_param_port_map_yk", m_mapMapPort))
        return false;
    if (!cacheDataPortMapByTable("mp_param_port_map_ys", m_mapMapPort))
        return false;
    if (!cacheDataPortMapByTable("mp_param_port_map_yt", m_mapMapPort))
        return false;
    if (!cacheDataPortMapByTable("mp_param_port_map_sdev", m_mapMapPort))
        return false;
    return true;
}

int splitString(const std::string& src, const std::string& comma, ZG6000::StringList& listString)
{
    int count = 0;
    size_t pos;
    std::string temp = src + comma;
    std::string_view view{temp.c_str()};
    while((pos = view.find(comma)) != std::string::npos) {
        if (pos) {
            std::string str{view.substr(0, pos)};
            listString.push_back(str);
            ++count;
        }
        view.remove_prefix(pos + 1);
    }
    return count;
}

bool ZGMPPortSendMng::getMapPort(std::unordered_map<std::string, ZG6000::StringList>& mapMapPort)
{
    QString sql;
    if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
        sql = "SELECT sendMapID, GROUP_CONCAT(id) as portID FROM mp_param_port WHERE sendMapID <> '' AND isEnabled = 1 GROUP BY sendMapID";
    else
        sql = "SELECT sendMapID, STRING_AGG(id, ',') as portID FROM mp_param_port WHERE sendMapID <> '' AND isEnabled = 1 GROUP BY sendMapID ORDER BY sendMapID";
    ZG6000::ListStringMap listMapResult;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listMapResult))
        return false;
    for (const auto& mapResult : listMapResult)
    {
        const std::string& sendMapID = mapResult.at("sendMapID");
        const std::string& portIDs = mapResult.at("portID");
        ZG6000::StringList listPort;
        splitString(portIDs, ",", listPort);
        mapMapPort.insert(std::make_pair(sendMapID, listPort));
    }
    return true;
}

bool ZGMPPortSendMng::cacheDataPortMapByTable(const std::string& tableName,
    const std::unordered_map<std::string, ZG6000::StringList>& mapMapPort)
{
    QString sql;
    if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
        sql = QString("SELECT dataID, GROUP_CONCAT(mapID) as mapID FROM %1 GROUP BY dataID").arg(tableName.c_str());
    else
        sql = QString("SELECT dataID, STRING_AGG(CAST(mapID AS NVARCHAR(MAX)), N',') as mapID FROM %1 GROUP BY dataID ORDER BY dataID").arg(tableName.c_str());
    ZG6000::ListStringMap listMapResult;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listMapResult))
        return false;
    for (const auto& mapResult : listMapResult)
    {
        const std::string& id = mapResult.at("dataID");
        const std::string& mapIDs = mapResult.at("mapID");
        ZG6000::StringList listMapID;
        int count = splitString(mapIDs, ",", listMapID);
        ZG6000::StringMap mapMapID;
        for (const auto& mapID: listMapID)
        {
            if (mapMapID.find(mapID) == mapMapID.end())
                mapMapID.insert(std::make_pair(mapID, "1"));
        }
        ZG6000::StringList listPort;
        if (count > 0)
        {
            for (const auto& mapPair: mapMapID)
            {
                const auto & pair = mapMapPort.find(mapPair.first);
                if (pair != mapMapPort.end())
                {
                    for (const auto& port : pair->second)
                    {
                        listPort.push_back(port);
                    }
                }
            }
        }
        m_mapDataPort.insert(std::make_pair(id, std::move(listPort)));
    }
    return true;
}

void ZGMPPortSendMng::processFixedTopicMessage(const QString &topic, const QString &message)
{
    ZG6000::ListStringMap listRecord;
    std::string errMsg;
    if (!ZGJson::convertFromJson(message.toStdString(), listRecord, errMsg))
    {
        ZGLOG_ERROR_E(QString("Convert json error: %1").arg(errMsg.c_str()), message);
        return;
    }
    if (listRecord.empty())
        return;
    std::pair<std::string, std::string> pair = m_mapCommand.at(topic.toStdString());
    transmitDataToPort(pair.second, listRecord);
}

void ZGMPPortSendMng::processPortMapTopicMessage(const QString &topic, const QString &message)
{
    ZG6000::StringList listString;
    int count = splitString(topic.toStdString(), "/", listString);
    if (count != 2)
    {
        ZGLOG_ERROR(QString("Invalid topic format: %1").arg(topic));
        return;
    }
    std::string tableName, oper, reason, time, errMsg;
    ZG6000::ListStringMap listRecord;
    if (!ZGJson::convertFromJson(message.toStdString(), tableName, oper, reason, time, listRecord, errMsg))
    {
        ZGLOG_ERROR_E(QString("Convert json error: %1").arg(errMsg.c_str()), message);
        return;
    }
    ZG6000::ListStringMap listNewRecord;
    listNewRecord.reserve(listRecord.size());
    for (auto& record : listRecord)
    {
        if ((record.find("rtRawValue") != record.end()) ||
            (record.find("rtQualityFlag") != record.end()) ||
            (record.find("rtUpdateTime") != record.end()))
            listNewRecord.emplace_back(std::move(record));
    }
    const auto & mapID = listString[0];
    const auto & dataType = listString[1];
    const auto & pair = m_mapMapPort.find(mapID);
    if (pair == m_mapMapPort.end())
    {
        ZGLOG_ERROR(QString("Can't find mapID: %1").arg(mapID.c_str()));
        return;
    }
    const auto & json = ZGJson::convertToJson(listNewRecord);
    for (const auto& portID : pair->second)
    {
        std::string portTopic = portID + "/send/" + dataType;
        long long subsriberNum;
        if (!m_pRedisPortTopic->publish(portTopic, json, subsriberNum, errMsg))
        {
            ZGLOG_ERROR_E(QString("publish json to port %1 error.").arg(portID.c_str()), json.c_str());
        }
    }
}

void ZGMPPortSendMng::onInitFinished()
{
    m_initialized = true;
    m_checkTimer.start(m_checkInterval * 1000);
}

void ZGMPPortSendMng::onCheckInstStatus()
{
    m_masterInst = ZGRuntime::instance()->isMaster();
}

void ZGMPPortSendMng::onReceivedMessage(const QString& topic, const QString& message)
{
    if (!m_masterInst)
        return;
    if (topic == "ZG_T_SOE" || topic == "ZG_T_EVENT")
        processFixedTopicMessage(topic, message);
    else
        processPortMapTopicMessage(topic, message);
}

void ZGMPPortSendMng::run()
{
    while (true)
    {
        if (m_masterInst)
        {
            std::string errMsg;
            std::pair<std::string, std::string> result;
            if (m_pRedisRtQueue->blpop(m_listQueue, m_commandTimeout, result, errMsg))
            {
                ZG6000::ListStringMap listCommand;
                if (!ZGJson::convertFromJson(result.second, listCommand, errMsg))
                {
                    ZGLOG_ERROR(errMsg.c_str());
                    continue;
                }
                if (listCommand.empty())
                    continue;
                std::pair<std::string, std::string> pair = m_mapCommand.at(result.first);
                std::string commandID;
                try
                {
                    commandID = ZGUtils::get(listCommand[0], "commandID");
                    const auto& id = ZGUtils::get(listCommand[0], "id");
                    ZGLOG_DEBUG(QString("command: %1, id: %2").arg(commandID.c_str()).arg(id.c_str()));
                }
                catch (const std::exception& e)
                {
                    ZGLOG_ERROR(e.what());
                    continue;
                }
                if (commandID == "ZG_DC_YK_SELECT_RESP" || commandID == "ZG_DC_YK_EXEC_RESP" || commandID == "ZG_DC_YK_CANCEL_RESP"
                    || commandID == "ZG_DC_YS_SELECT_RESP" || commandID == "ZG_DC_YS_EXEC_RESP" || commandID == "ZG_DC_YS_CANCEL_RESP")
                {
                    for (const auto & command : listCommand)
                    {
                        transmitRespToPort(pair.second, command);
                    }
                }
                else
					transmitDataToPort(pair.second, listCommand);
            }
        }
        else
            msleep(1000);
    }
}

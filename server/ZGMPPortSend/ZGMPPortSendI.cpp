
#include <ZGMPPortSendI.h>
#include "ZGMPPortSendMng.h"

ZG6000::ZGMPPortSendI::ZGMPPortSendI()
{
    ZGMPPortSendMng::instance()->init();
}

bool
ZG6000::ZGMPPortSendI::checkState(const Ice::Current& current)
{
    return ZGMPPortSendMng::instance()->checkState();
}

void
ZG6000::ZGMPPortSendI::dispatchData(::std::string tableName,
                                  ::std::string oper,
                                  ::std::string reason,
                                  ::std::string time,
                                  ZG6000::ListRecord listRecord,
                                  const Ice::Current& current)
{
    ZGMPPortSendMng::instance()->dispatchData(tableName, oper, reason, time, listRecord);
}

#ifndef __ZGMPPortSendI_h__
#define __ZGMPPortSendI_h__

#include <ZGMPPortSend.h>

namespace ZG6000
{

class ZGMPPortSendI : public virtual ZGMPPortSend
{
public:
    ZGMPPortSendI();

    bool checkState(const Ice::Current&) override;

    void dispatchData(::std::string,
                      ::std::string,
                      ::std::string,
                      ::std::string,
                      ListRecord,
                      const Ice::Current&) override;
};

}

#endif

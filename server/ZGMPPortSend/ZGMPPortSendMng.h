#ifndef ZGMPPORTSENDMNG_H
#define ZGMPPORTSENDMNG_H

#include <QThread>
#include <QTimer>
#include <unordered_map>
#include "ZGProxyMng.h"

class ZGRedisClient;
class ZGMPPortSendMng : public QThread
{
    Q_OBJECT
public:
    static ZGMPPortSendMng* instance();
    bool checkState();
    void dispatchData(const ::std::string& tableName,
                                      const ::std::string& oper,
                                      const ::std::string& reason,
                                      const ::std::string& time,
                                      const ZG6000::ListRecord& listRecord);
    void init();

signals:
    void initFinished();

private slots:
    void onInitFinished();
    void onCheckInstStatus();
    void onReceivedMessage(const QString& topic, const QString& message);

protected:
    void run() override;

private:
    explicit ZGMPPortSendMng(QObject *parent = nullptr);
    void initEvents();
    void initCache();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initRedisClient();
    bool initRedisRtTopic();
    bool getDataPortsByID(const std::string& id, ZG6000::StringList& listPort);
    void transmitDataToPort(const std::string& typeName, const ZG6000::ListStringMap& listStringMap);
    void transmitRespToPort(const std::string& typeName, const ZG6000::StringMap& command);
    bool cacheDataPortMap();
    bool getMapPort(std::unordered_map<std::string, ZG6000::StringList>& mapMapPort);
    bool cacheDataPortMapByTable(const std::string& tableName, const std::unordered_map<std::string, ZG6000::StringList>& mapMapPort);
    void processFixedTopicMessage(const QString& topic, const QString& message);
    void processPortMapTopicMessage(const QString& topic, const QString& message);
private:
    bool m_initialized{false};
    std::atomic_bool m_masterInst{true};
    int m_initInterval{10};
    int m_checkInterval{10};
    int m_commandTimeout{5};
    int m_cacheTimeout{60};
    ZG6000::StringList m_listQueue;
    ZGRedisClient* m_pRedisPortTopic{nullptr};
    ZGRedisClient* m_pRedisRtTopic{nullptr};
    ZGRedisClient* m_pRedisRtQueue{nullptr};
    std::map<std::string, std::pair<std::string, std::string>> m_mapCommand;
    std::unordered_map<std::string, ZG6000::StringList> m_mapDataPort;
    std::unordered_map<std::string, ZG6000::StringList> m_mapMapPort;
    int m_currentStep{0};
    QString m_instName{""};
    QString m_serverName{""};
    QTimer m_timer;
    QTimer m_checkTimer;
};

#endif // ZGMPPORTSENDMNG_H

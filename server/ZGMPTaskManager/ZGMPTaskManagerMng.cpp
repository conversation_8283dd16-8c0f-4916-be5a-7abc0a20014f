#include "ZGMPTaskManagerMng.h"
#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "zgerror/ZGMPTaskManagerError.h"

#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QRandomGenerator>

static ZGMPTaskManagerMng* g_pInstance = nullptr;

ZGMPTaskManagerMng* ZGMPTaskManagerMng::instance()
{
	if (g_pInstance == nullptr)
		g_pInstance = new ZGMPTaskManagerMng;
	return g_pInstance;
}

void ZGMPTaskManagerMng::init()
{
	initEvents();
	initServerInstConfig();
	start();
	ZGLOG_INFO("ZGMPTaskManager init start...");
}

bool ZGMPTaskManagerMng::checkState()
{
	return m_initialized;
}

void ZGMPTaskManagerMng::dispatchData(const std::string& tableName, const std::string& oper, const std::string& reason,
                                      const std::string& time, const ZG6000::ListRecord& listRecord)
{
	if (oper != "update")
		return;
	if (reason != "change")
		return;
	if ((tableName != "mp_param_task") && (tableName != "mp_param_task_item"))
		return;
	for (const auto& record: listRecord)
	{
		const auto& id = ZGUtils::get(record, "id");
		std::string appNodeID;
		std::string taskID = id.newValue;
		if (tableName == "mp_param_task_item")
		{
			if (!ZGProxyCommon::getDataByField(tableName, id.newValue, "taskID", taskID))
			{
				ZGLOG_ERROR(QStringLiteral("获取任务项'%1'所属任务失败").arg(id.newValue.c_str()));
				continue;
			}
		}
		if (!ZGProxyCommon::getDataByField("mp_param_task", taskID, "appNodeID", appNodeID))
		{
			ZGLOG_ERROR(QStringLiteral("获取任务'%1'应用节点ID失败").arg(id.newValue.c_str()));
			continue;
		}
		std::string topicName = appNodeID + (tableName == "mp_param_task_item" ? "/taskItem" : "task");
		ZG6000::StringMap event;
		for (const auto& pair: record)
		{
			event.insert(std::make_pair(pair.first, pair.second.newValue));
		}
		if (tableName == "mp_param_task_item")
			publishTaskItem(appNodeID, event);
		else
			publishTask(appNodeID, event);
	}
}

bool ZGMPTaskManagerMng::getTaskList(const std::string& appNodeID, const std::string& subsystemID, 
                                     const std::string& majorID, const std::string& taskTypeID,
                                     ZG6000::ListStringMap& taskList, ZG6000::ErrorInfo& e)
{
	std::string sql = "SELECT id FROM mp_param_task WHERE appNodeID = '" + appNodeID + "'";
	if (!subsystemID.empty())
		sql += "AND subsystemID = '" + subsystemID + "'";
	if (!majorID.empty())
		sql += " AND majorID = '" + majorID + "'";
	if (!taskTypeID.empty())
		sql += " AND taskTypeID='" + taskTypeID + "'";
	ZG6000::StringList listTaskID;
	if (!ZGProxyCommon::execQuerySqlCol(sql, listTaskID))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_DB);
		e.errDetail = u8"获取应用节点'" + appNodeID + u8"'专业'" + majorID + u8"'任务ID失败";
		ZGLOG_ERROR(e);
		return false;
	}
	if (!ZGProxyCommon::mgetDataByID("mp_param_task", listTaskID, taskList))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
		e.errDetail = u8"获取应用节点'" + appNodeID + u8"'专业'" + majorID + u8"'任务失败";
		ZGLOG_ERROR(e);
		return false;
	}
	for (auto& task : taskList)
	{
		bool pass;
		if (!checkTaskRuleCondition(task, "execConditionRuleID", pass))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
			e.errDetail = u8"验证执行调用失败";
			ZGLOG_ERROR(e);
			task["isAllow"] = "0";
		}
		task["isAllow"] = pass ? "1" : "0";
	}
	for (auto& task: taskList)
	{
		extendTask(task);
	}
	return true;
}

bool ZGMPTaskManagerMng::getTask(const std::string& taskID, ZG6000::StringMap& task, ZG6000::ErrorInfo& e)
{
	if (!ZGProxyCommon::getDataByID("mp_param_task", taskID, task))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
		e.errDetail = u8"获取任务'" + taskID + u8"'信息失败";
		ZGLOG_ERROR(e);
		return false;
	}
	extendTask(task);
	bool pass;
	if (!checkTaskRuleCondition(task, "execConditionRuleID", pass))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
		e.errDetail = u8"验证执行调用失败";
		ZGLOG_ERROR(e);
		task["isAllow"] = "0";
	}
	task["isAllow"] = pass ? "1" : "0";
	return true;
}

bool ZGMPTaskManagerMng::getTaskItems(const std::string& taskID, ZG6000::ListStringMap& itemList, ZG6000::ErrorInfo& e)
{
	std::string sql = "SELECT id FROM mp_param_task_item WHERE taskID = '" + taskID + "' ORDER BY itemIndex";
	ZG6000::StringList listItemID;
	if (!ZGProxyCommon::execQuerySqlCol(sql, listItemID))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_DB);
		e.errDetail = u8"获取任务'" + taskID + u8"'关联任务项ID失败";
		ZGLOG_ERROR(e);
		return false;
	}
	if (listItemID.empty())
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_DB);
		e.errDetail = u8"任务'" + taskID + u8"'无关联任务项";
		ZGLOG_ERROR(e);
		return false;
	}
	if (!ZGProxyCommon::mgetDataByID("mp_param_task_item", listItemID, itemList))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
		e.errDetail = u8"获取任务'" + taskID + u8"'关联任务项失败";
		ZGLOG_ERROR(e);
		return false;
	}
	for (auto& item: itemList)
	{
		extendTaskItem(item);
	}
	return true;
}

bool ZGMPTaskManagerMng::startTask(const std::string& clientID, const std::string& operUserID,
                                   const std::string& monUserID, const std::string& taskID, ZG6000::ErrorInfo& e)
{
	bool allow;
	if (!isClientAllowCtrl(clientID, allow, e))
		return false;
	if (!allow)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
		e.errDetail = u8"客户端禁止执行遥控";
		ZGLOG_ERROR(e);
		return false;
	}
	std::lock_guard<std::mutex> locker(m_mutex);
	ZG6000::StringMap task;
	if (!ZGProxyCommon::getDataByID("mp_param_task", taskID, task))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
		e.errDetail = u8"获取任务'" + taskID + u8"'信息失败";
		ZGLOG_ERROR(e);
		return false;
	}
	try
	{
		const auto& id = ZGUtils::get(task, "id");
		if ((id != taskID) || id.empty())
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
			e.errDetail = u8"找不到指定的任务'" + taskID + "'";
			ZGLOG_ERROR(e);
			return false;
		}
		bool executing;
		if (!isTaskExecuting(task, executing))
			return false;
		if (executing)
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
			e.errDetail = u8"任务正在执行，无法启动新任务";
			ZGLOG_ERROR(e);
			return false;
		}
		bool pass;
		if (!checkTaskRuleCondition(task, "execConditionRuleID", pass))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
			e.errDetail = u8"验证执行调用失败";
			ZGLOG_ERROR(e);
			return false;
		}
		if (!pass)
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
			e.errDetail = u8"任务'" + taskID + u8"'执行条件不满足";
			ZGLOG_ERROR(e);
			return false;
		}
		std::string sql = "SELECT id FROM mp_param_task_item WHERE taskID = '" + taskID + "'";
		ZG6000::StringList listItemID;
		if (!ZGProxyCommon::execQuerySqlCol(sql, listItemID))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_DB);
			e.errDetail = u8"获取任务'" + taskID + u8"'关联任务项ID失败";
			ZGLOG_ERROR(e);
			return false;
		}
		if (!ZGProxyCommon::mupdateDataByField("mp_param_task_item", listItemID,
			"rtTaskItemStateID", "ZG_TS_UNEXECUTE"))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
			e.errDetail = u8"更新任务'" + taskID + "'项实时状态失败";
			ZGLOG_ERROR(e);
			return false;
		}
		ZG6000::StringMap taskState;
		taskState["id"] = taskID;
		taskState["rtOperUserID"] = operUserID;
		taskState["rtMonUserID"] = monUserID;
		taskState["rtCurrentItemID"] = "-1";
		taskState["rtTaskStateID"] = "ZG_TS_EXECUTING";
		taskState["rtStartTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
		if (!updateTask(taskState))
			return false;
		const auto& appNodeID = ZGUtils::get(task, "appNodeID");
		const auto& subsystemID = ZGUtils::get(task, "subsystemID");
		const auto& majorID = ZGUtils::get(task, "majorID");
		const auto& name = ZGUtils::get(task, "name");
		const auto& operUserName = ZGUtils::get(m_mapUser, operUserID, "");
		const auto& clientName = ZGUtils::get(m_mapClient, clientID);
		QString event = QStringLiteral("客户端【%1】启动程控任务【%2】，操作员【%3】")
			.arg(clientName.c_str()).arg(name.c_str()).arg(operUserName.c_str());
		publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, event.toStdString());
		return true;
	}
	catch (const std::exception& ex)
	{
		ZGLOG_ERROR(ex.what());
		return false;
	}
}

bool ZGMPTaskManagerMng::resumeTask(const std::string& clientID, const std::string& operUserID, const std::string& monUserID, const std::string& taskID, ZG6000::ErrorInfo& e)
{
	bool allow;
	if (!isClientAllowCtrl(clientID, allow, e))
		return false;
	if (!allow)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
		e.errDetail = u8"客户端禁止执行遥控";
		ZGLOG_ERROR(e);
		return false;
	}
	std::lock_guard<std::mutex> locker(m_mutex);
	ZG6000::StringMap task;
	if (!ZGProxyCommon::getDataByID("mp_param_task", taskID, task))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
		e.errDetail = u8"获取任务'" + taskID + u8"'信息失败";
		ZGLOG_ERROR(e);
		return false;
	}
	ZG6000::StringMap taskState;
	taskState["id"] = taskID;
	taskState["rtStartTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
	taskState["rtTaskStateID"] = "ZG_TS_EXECUTING";
	if (!updateTask(taskState))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
		e.errDetail = u8"更新任务'" + taskID + u8"'实时信息失败";
		ZGLOG_ERROR(e);
		return false;
	}
	try
	{
		const auto& appNodeID = ZGUtils::get(task, "appNodeID");
		const auto& subsystemID = ZGUtils::get(task, "subsystemID");
		const auto& majorID = ZGUtils::get(task, "majorID");
		const auto& name = ZGUtils::get(task, "name");
		const auto& operUserName = ZGUtils::get(m_mapUser, operUserID, "");
		const auto& clientName = ZGUtils::get(m_mapClient, clientID);
		QString event = QStringLiteral("客户端【%1】继续程控任务【%2】，操作员【%3】")
			.arg(clientName.c_str()).arg(name.c_str()).arg(operUserName.c_str());
		publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, event.toStdString());
		return true;
	}
	catch (const std::exception& ex)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
		e.errDetail = ex.what();
		ZGLOG_ERROR(e);
		return false;
	}
}

bool ZGMPTaskManagerMng::getExecCondition(const std::string& taskID, ZG6000::ListStringMap& lstMapData, ZG6000::ErrorInfo& e)
{
	return getCondition(taskID, "execConditionRuleID", lstMapData, e);
}

bool ZGMPTaskManagerMng::getConfirmCondition(const std::string& taskID, ZG6000::ListStringMap& lstMapData, ZG6000::ErrorInfo& e)
{
	return getCondition(taskID, "confirmConditionRuleID", lstMapData, e);
}

bool ZGMPTaskManagerMng::getErrorCondition(const std::string& taskID, ZG6000::ListStringMap& lstMapData, ZG6000::ErrorInfo& e)
{
	return getCondition(taskID, "errorConditionRuleID", lstMapData, e);
}

bool ZGMPTaskManagerMng::stopTask(const std::string& clientID, const std::string& operUserID, const std::string& monUserID, const std::string& taskID, ZG6000::ErrorInfo& e)
{
	std::lock_guard<std::mutex> locker(m_mutex);
	ZG6000::StringMap task;
	if (!ZGProxyCommon::getDataByID("mp_param_task", taskID, task))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
		e.errDetail = u8"获取任务'" + taskID + u8"'实时信息失败";
		ZGLOG_ERROR(e);
		return false;
	}
	try
	{
		const auto& rtTaskStateID = ZGUtils::get(task, "rtTaskStateID");
		const auto& appNodeID = ZGUtils::get(task, "appNodeID");
		if (rtTaskStateID == "ZG_TS_EXECUTING")
		{
			ZG6000::StringMap record;
			record["id"] = taskID;
			record["rtEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
			record["rtTaskStateID"] = "ZG_TS_STOPPED";
			if (!updateTask(record))
				return false;
			publishTask(appNodeID, record);
			const auto& subsystemID = ZGUtils::get(task, "subsystemID");
			const auto& majorID = ZGUtils::get(task, "majorID");
			const auto& name = ZGUtils::get(task, "name");
			const auto& operUserName = ZGUtils::get(m_mapUser, operUserID, "");
			const auto& clientName = ZGUtils::get(m_mapClient, clientID);
			QString event = QStringLiteral("客户端【%1】停止程控任务【%2】，操作员【%3】")
				.arg(clientName.c_str()).arg(name.c_str()).arg(operUserName.c_str());
			publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, event.toStdString());
		}
		return true;
	}
	catch (const std::exception& ex)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
		e.errDetail = ex.what();
		ZGLOG_ERROR(e);
		return false;
	}
}

bool ZGMPTaskManagerMng::pauseTask(const std::string& clientID, const std::string& operUserID, const std::string& monUserID, const std::string& taskID, ZG6000::ErrorInfo& e)
{
	std::lock_guard<std::mutex> locker(m_mutex);
	ZG6000::StringMap task;
	if (!ZGProxyCommon::getDataByID("mp_param_task", taskID, task))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
		e.errDetail = u8"获取任务'" + taskID + u8"'实时信息失败";
		ZGLOG_ERROR(e);
		return false;
	}
	try
	{
		const auto& rtCurrentItemID = ZGUtils::get(task, "rtCurrentItemID");
		if ((!rtCurrentItemID.empty()) && (rtCurrentItemID != "-1"))
		{
			if (!ZGProxyCommon::updateDataByField("mp_param_task_item", rtCurrentItemID, "rtTaskItemStateID", "ZG_TS_UNEXECUTE"))
			{
				e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
				e.errDetail = u8"更新任务项'" + rtCurrentItemID + u8"'实时信息失败";
				ZGLOG_ERROR(e);
				return false;
			}
		}
		if (!ZGProxyCommon::updateDataByField("mp_param_task", taskID, "rtTaskStateID", "ZG_TS_PAUSED"))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
			e.errDetail = u8"更新任务'" + taskID + u8"'实时信息失败";
			ZGLOG_ERROR(e);
			return false;
		}
		const auto& appNodeID = ZGUtils::get(task, "appNodeID");
		const auto& subsystemID = ZGUtils::get(task, "subsystemID");
		const auto& majorID = ZGUtils::get(task, "majorID");
		const auto& name = ZGUtils::get(task, "name");
		const auto& operUserName = ZGUtils::get(m_mapUser, operUserID, "");
		const auto& clientName = ZGUtils::get(m_mapClient, clientID);
		QString event = QStringLiteral("客户端【%1】暂停程控任务【%2】，操作员【%3】")
			.arg(clientName.c_str()).arg(name.c_str()).arg(operUserName.c_str());
		publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, event.toStdString());
		return true;
	}
	catch (const std::exception& ex)
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
		e.errDetail = ex.what();
		ZGLOG_ERROR(e);
		return false;
	}
}

void ZGMPTaskManagerMng::run()
{
	while (!initServerInstInfo())
	{
		ZGLOG_ERROR("initServerInstInfo error.");
		msleep(m_initInterval * 1000);
	}
	sleep(QRandomGenerator::global()->bounded(5, 10));
	while (!initRedisQueue())
	{
		ZGLOG_ERROR("initRedisQueue error.");
		msleep(m_initInterval * 1000);
	}
	while (!initTaskType())
	{
		ZGLOG_ERROR("initTaskType error.");
		msleep(m_initInterval * 1000);
	}
	while (!initTaskState())
	{
		ZGLOG_ERROR("initTaskState error.");
		msleep(m_initInterval * 1000);
	}
	while (!initAppNode())
	{
		ZGLOG_ERROR("initAppNode error.");
		msleep(m_initInterval * 1000);
	}
	while (!initSubsystem())
	{
		ZGLOG_ERROR("initSubsystem error.");
		msleep(m_initInterval * 1000);
	}
	while (!initMajor())
	{
		ZGLOG_ERROR("initMajor error.");
		msleep(m_initInterval * 1000);
	}
	while (!initUser())
	{
		ZGLOG_ERROR("initUser error.");
		msleep(m_initInterval * 1000);
	}
	while (!initClient())
	{
		ZGLOG_ERROR("initClient error.");
		msleep(m_initInterval * 1000);
	}
	m_masterInst = ZGRuntime::instance()->isMaster();
	emit initFinished();
}

ZGMPTaskManagerMng::ZGMPTaskManagerMng(QObject* parent)
	: QThread{parent}
{
}

void ZGMPTaskManagerMng::initEvents()
{
	connect(&m_executeTimer, &QTimer::timeout, this, &ZGMPTaskManagerMng::onExecuteTask);
	connect(this, &ZGMPTaskManagerMng::initFinished, this, &ZGMPTaskManagerMng::onInitFinished);
}

void ZGMPTaskManagerMng::initServerInstConfig()
{
	const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
	QString errMsg;
	int value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_initInterval = value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_checkInterval = value;
}

bool ZGMPTaskManagerMng::initServerInstInfo()
{
	m_serverName = ZGRuntime::instance()->getServerID();
	if (m_serverName.isEmpty())
	{
		ZGLOG_ERROR("Empty server id.");
		return false;
	}
	m_instName = ZGRuntime::instance()->getInstanceID();
	if (m_instName.isEmpty())
	{
		ZGLOG_ERROR("Empty server instance id.");
		return false;
	}
	return true;
}

bool ZGMPTaskManagerMng::initRedisQueue()
{
	QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
	listClientType << ZGRuntime::REDIS_RT_QUEUE;
	if (!ZGRuntime::instance()->initRedisClient(listClientType))
	{
		ZGLOG_ERROR("initRedisClient error.");
		return false;
	}
	m_pRedisRtQueue = ZGRuntime::instance()->getRedisClientRTQueue();
	if (m_pRedisRtQueue == nullptr)
	{
		ZGLOG_ERROR("getRedisClientRTQueue error.");
		return false;
	}
	return true;
}

bool ZGMPTaskManagerMng::initMqttClient()
{
	if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
	{
		ZGLOG_ERROR("initMqttClient error.");
		return false;
	}
	m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
	if (m_pMqttClient == nullptr)
	{
		ZGLOG_ERROR("getMqttClientMessage error.");
		return false;
	}
	m_pMqttClient->connectToHost();
	return true;
}

bool ZGMPTaskManagerMng::initTaskType()
{
	std::string sql = "SELECT id, name FROM mp_dict_task_type";
	return ZGProxyCommon::execQuerySqlPair(sql, m_mapTaskType);
}

bool ZGMPTaskManagerMng::initTaskState()
{
	std::string sql = "SELECT id, name FROM mp_dict_task_state";
	return ZGProxyCommon::execQuerySqlPair(sql, m_mapTaskState);
}

bool ZGMPTaskManagerMng::initAppNode()
{
	std::string sql = "SELECT id, name FROM sp_param_appnode";
	return ZGProxyCommon::execQuerySqlPair(sql, m_mapAppNode);
}

bool ZGMPTaskManagerMng::initSubsystem()
{
    std::string sql = "SELECT id, name FROM sp_param_subsystem";
	return ZGProxyCommon::execQuerySqlPair(sql, m_mapSubsystem);
}

bool ZGMPTaskManagerMng::initMajor()
{
    std::string sql = "SELECT id, name FROM sp_param_major";
	return ZGProxyCommon::execQuerySqlPair(sql, m_mapMajor);
}

bool ZGMPTaskManagerMng::initUser()
{
	std::string sql = "SELECT id, name FROM sp_param_hrm_user";
	return ZGProxyCommon::execQuerySqlPair(sql, m_mapUser);
}

bool ZGMPTaskManagerMng::initClient()
{
	std::string sql = "SELECT id, name FROM sp_param_client";
	return ZGProxyCommon::execQuerySqlPair(sql, m_mapClient);
}

bool ZGMPTaskManagerMng::updateTaskTimeout(const std::string& taskID)
{
	ZG6000::StringMap task;
	QString currTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
	task["rtEndTime"] = currTime.toStdString();
    task["rtTaskStateID"] = "ZG_TS_ITEM_TIMEOUT";
	if (!ZGProxyCommon::updateDataByID("mp_param_task", taskID, task))
	{
		ZGLOG_ERROR(QStringLiteral("更新任务'%1'超时状态失败").arg(taskID.c_str()));
		return false;
	}
	std::string rtCurrentItemID;
	if (!ZGProxyCommon::getDataByField("mp_param_task", taskID, "rtCurrentItemID", rtCurrentItemID))
	{
		ZGLOG_ERROR(QStringLiteral("获取任务'%1'当前任务项失败").arg(taskID.c_str()));
		return false;
	}
	std::string sql = "SELECT id FROM mp_param_task_item WHERE taskID = '" + taskID + "'";
	ZG6000::StringList listItemID;
	if (!ZGProxyCommon::execQuerySqlCol(sql, listItemID))
	{
		ZGLOG_ERROR(QStringLiteral("获取任务'%1'任务项失败").arg(taskID.c_str()));
		return false;
	}
	ZG6000::StringList listItemState;
	if (!ZGProxyCommon::mgetDataByField("mp_param_task_item", listItemID, "rtTaskItemStateID", listItemState))
	{
		ZGLOG_ERROR(QStringLiteral("获取任务'%1'任务项状态失败").arg(taskID.c_str()));
		return false;
	}
	ZG6000::StringList listExecItemID;
	for (size_t i = 0; i < listItemID.size(); ++i)
	{
		if (listItemState[i] == "ZG_TS_EXECUTING")
		{
			listExecItemID.push_back(listItemID[i]);
		}
	}
	if (!listExecItemID.empty())
	{
        if (!ZGProxyCommon::mupdateDataByField("mp_param_task_item", listExecItemID, "rtTaskItemStateID", "ZG_TS_ITEM_TIMEOUT"))
		{
			ZGLOG_ERROR(QStringLiteral("更新任务'%1'任务项状态失败").arg(taskID.c_str()));
			return false;
		}
	}
	return true;
}

bool ZGMPTaskManagerMng::updateTaskCurrentItem(const std::string& taskID, const std::string& currentItem)
{
	ZG6000::StringMap task;
	task["id"] = taskID;
	task["rtCurrentItemID"] = currentItem;
	return updateTask(task);
}

bool ZGMPTaskManagerMng::updateTask(ZG6000::StringMap& task)
{
	try
	{
		const auto& id = ZGUtils::get(task, "id");
		if (!ZGProxyCommon::updateDataByID("mp_param_task", id, task))
		{
			ZGLOG_ERROR(QStringLiteral("更新任务'%1'失败").arg(id.c_str()));
		}
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
	return true;
}

bool ZGMPTaskManagerMng::updateTaskItem(ZG6000::StringMap& item)
{
	try
	{
		const auto& id = ZGUtils::get(item, "id");
		if (!ZGProxyCommon::updateDataByID("mp_param_task_item", id, item))
			ZGLOG_ERROR(QStringLiteral("更新任务项'%1'失败").arg(id.c_str()));
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
	return true;
}

void ZGMPTaskManagerMng::insertNameByID(ZG6000::StringMap& record, const ZG6000::StringMap& mapName,
                                        const std::string& fieldId, const std::string& fieldName)
{
	auto pair = record.find(fieldId);
	if (pair != record.end())
		record.insert(std::make_pair(fieldName, ZGUtils::get(mapName, pair->second, "")));
}

void ZGMPTaskManagerMng::extendTask(ZG6000::StringMap& task)
{
	insertNameByID(task, m_mapTaskType, "taskTypeID", "taskTypeName");
	insertNameByID(task, m_mapAppNode, "appNodeID", "appNodeName");
	insertNameByID(task, m_mapSubsystem, "subsystemID", "subsystemName");
	insertNameByID(task, m_mapMajor, "majorID", "majorName");
	insertNameByID(task, m_mapUser, "rtOperUserID", "rtOperUserName");
	insertNameByID(task, m_mapUser, "rtMonUserID", "rtMonUserName");
	insertNameByID(task, m_mapTaskState, "rtTaskStateID", "rtTaskStateName");
	try
	{
		const auto& currentItem = task.find("rtCurrentItemID");
		if (currentItem != task.end())
		{
			const auto& currentItemID = currentItem->second;
			if (!currentItemID.empty() && (currentItemID != "-1"))
			{
				ZG6000::StringMap record;
				if (!ZGProxyCommon::getDataByFields("mp_param_task_item", currentItemID, {"itemIndex", "tableName", "dataID"}, record))
					return;
				const auto& tableName = ZGUtils::get(record, "tableName");
				const auto& dataID = ZGUtils::get(record, "dataID");
				std::string dataName;
				if (!ZGProxyCommon::getDataByField(tableName, dataID, "name", dataName))
					return;
				task.insert({"rtCurrentItemName", dataName});
				task.insert({"rtCurrentItemIndex", ZGUtils::get(record, "itemIndex", "")});
			}
			else
			{
				task.insert({"rtCurrentItemName", ""});
				task.insert({"rtCurrentItemIndex", ""});
			}
		}
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
}

void ZGMPTaskManagerMng::extendTaskItem(ZG6000::StringMap& item)
{
	insertNameByID(item, m_mapTaskState, "rtTaskItemStateID", "rtTaskItemStateName");
	try
	{
		if ((item.find("tableName") != item.end()) && (item.find("dataID") != item.end()))
		{
			const auto& tableName = ZGUtils::get(item, "tableName");
			const auto& dataID = ZGUtils::get(item, "dataID");
			std::string dataName;
			if (!ZGProxyCommon::getDataByField(tableName, dataID, "name", dataName))
			{
				ZGLOG_ERROR(QStringLiteral("获取数据ID'%1'名称失败").arg(dataID.c_str()));
				return;
			}
			item.insert(std::make_pair("dataName", dataName));
		}
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
}

bool ZGMPTaskManagerMng::getTaskItems(const std::string& taskID, ZG6000::ListStringMap& listItems)
{
	std::string sql = "SELECT id FROM mp_param_task_item WHERE taskID = '" + taskID + "' ORDER BY itemIndex";
	ZG6000::StringList listItemID;
	if (!ZGProxyCommon::execQuerySqlCol(sql, listItemID))
	{
		ZGLOG_ERROR(QStringLiteral("获取任务'%1'关联任务项ID失败").arg(taskID.c_str()));
		return false;
	}
	if (listItemID.empty())
	{
		ZGLOG_ERROR(QStringLiteral("任务'%1'无关联任务项").arg(taskID.c_str()));
		return false;
	}
	if (!ZGProxyCommon::mgetDataByID("mp_param_task_item", listItemID, listItems))
	{
		ZGLOG_ERROR(QStringLiteral("获取任务'%1'关联任务项失败").arg(taskID.c_str()));
		return false;
	}
	return true;
}

bool ZGMPTaskManagerMng::getExecutingTaskItem(const std::string& taskID, ZG6000::ListStringMap& listItem)
{
	ZG6000::ListStringMap listAllItem;
	if (!getTaskItems(taskID, listAllItem))
		return false;
	try
	{
		for (const auto& item: listAllItem)
		{
			const auto& rtTaskItemStateID = ZGUtils::get(item, "rtTaskItemStateID");
			if (rtTaskItemStateID == "ZG_TS_EXECUTING")
				listItem.push_back(item);
		}
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

bool ZGMPTaskManagerMng::isTaskExecuting(const ZG6000::StringMap& task, bool& executing)
{
	try
	{
		const auto& rtTaskStateID = ZGUtils::get(task, "rtTaskStateID");
		executing = (rtTaskStateID == "ZG_TS_EXECUTING");
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

bool ZGMPTaskManagerMng::isTaskOvertime(const ZG6000::StringMap& task, bool& overTime)
{
	try
	{
		int overtime = std::atoi(ZGUtils::get(task, "overtime").c_str());
		const auto& rtStartTime = ZGUtils::get(task, "rtStartTime");
		QDateTime dateTime;
		if (!ZGUtils::StringToDateTime(rtStartTime.c_str(), dateTime))
		{
			ZGLOG_ERROR(QStringLiteral("无效的时间格式'%1'").arg(rtStartTime.c_str()));
			return false;
		}
		const auto& currentTime = QDateTime::currentDateTime();
		overTime = (std::abs(dateTime.secsTo(currentTime)) >= overtime);
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

bool ZGMPTaskManagerMng::checkTaskRuleCondition(const ZG6000::StringMap& task, const std::string& ruleID, bool& pass)
{
	return false;
	// const auto& ruleProxy = ZGProxyMng::instance()->getProxySPRuleEngine();
	// if (ruleProxy == nullptr)
	// {
	// 	ZGLOG_ERROR(QStringLiteral("获取规则引擎服务代理对象失败"));
	// 	return false;
	// }
	// try
	// {
	// 	const auto& rtRuleID = ZGUtils::get(task, ruleID);
	// 	ZGLOG_DEBUG(QString("ruleID: %1").arg(rtRuleID.c_str()));
	// 	bool result;
	// 	if (rtRuleID.empty())
	// 	{
	// 		if (ruleID == "errorConditionRuleID")
	// 			pass = false;
	// 		else
	// 			pass = true;
	// 		return true;
	// 	}
	// 	ZG6000::ErrorInfo e;
	// 	if (!ruleProxy->call(rtRuleID, result, e))
	// 	{
	// 		ZGLOG_ERROR(e);
	// 		return false;
	// 	}
	// 	pass = result;
	// 	return true;
	// }
	// catch (const Ice::Exception& e)
	// {
	// 	ZGLOG_ERROR(e.what());
	// 	return false;
	// }
	// catch (const std::exception& e)
	// {
	// 	ZGLOG_ERROR(e.what());
	// 	return false;
	// }
}

bool ZGMPTaskManagerMng::sendCommand(const QString& topicName, const QString& commandID, const QString& id,
                                     const QString& value)
{
	QJsonArray array;
	QJsonObject object;
	object["id"] = id;
	object["commandID"] = commandID;
	object["srcType"] = "auto";
	object["srcID"] = "-1";
	object["rtCode"] = QString::number(ZGUtils::genNumber(1, 100000));
	object["rtValue"] = value;
	object["rtCommandTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
	array.append(object);
	QJsonDocument doc(array);
	long long size;
	QString errMsg;
	if (!m_pRedisRtQueue->rpush(topicName, doc.toJson(QJsonDocument::Compact), size, errMsg))
	{
		ZGLOG_ERROR(QStringLiteral("提交到命令队列失败: '%1'").arg(errMsg));
		return false;
	}
	return true;
}

bool ZGMPTaskManagerMng::sendControl(const QString& tableName, const QString& id, const QString& value)
{
	if (tableName == "mp_param_dataset_yk")
		return sendYk(id, value);
	if (tableName == "mp_param_dataset_ys")
		return sendYs(id, value);
	if (tableName == "mp_param_dataset_yt")
		return sendYt(id, value);
	ZGLOG_ERROR(QStringLiteral("无效的表名称'%1'").arg(tableName));
	return false;
}

bool ZGMPTaskManagerMng::sendYk(const QString& id, const QString& value)
{
	std::string sql =
		"SELECT mp_param_model_yk.isSelectCtrl FROM mp_param_model_yk LEFT JOIN mp_param_dataset_yk ON mp_param_dataset_yk.dataModelID = mp_param_model_yk.id "
		"WHERE mp_param_dataset_yk.id = '" + id.toStdString() + "' ORDER BY mp_param_model_yk.id";
	std::string result;
	if (!ZGProxyCommon::execQuerySqlField(sql, result))
	{
		ZGLOG_ERROR("execQuerySqlField error.");
		return false;
	}
	QString commandID = "ZG_DC_YK_EXEC";
	if (result == "1")
		commandID = "ZG_DC_YK_SELECT";
	return sendCommand("ZG_Q_SYSTEM_YK", commandID, id, value);
}

bool ZGMPTaskManagerMng::sendYs(const QString& id, const QString& value)
{
	std::string sql =
		"SELECT mp_param_model_ys.isSelectCtrl FROM mp_param_model_ys LEFT JOIN mp_param_dataset_ys ON mp_param_dataset_ys.dataModelID = mp_param_model_ys.id "
		"WHERE mp_param_dataset_ys.id = '" + id.toStdString() + "' ORDER BY mp_param_model_ys.id";
	std::string result;
	if (!ZGProxyCommon::execQuerySqlField(sql, result))
	{
		ZGLOG_ERROR("execQuerySqlField error.");
		return false;
	}
	QString commandID = "ZG_DC_YS_EXEC";
	if (result == "1")
		commandID = "ZG_DC_YS_SELECT";
	return sendCommand("ZG_Q_SYSTEM_YS", commandID, id, value);
}

bool ZGMPTaskManagerMng::sendYt(const QString& id, const QString& value)
{
	return sendCommand("ZG_Q_SYSTEM_YT", "ZG_DC_YT_EXEC", id, value);
}

void ZGMPTaskManagerMng::processSequenceTask(const ZG6000::StringMap& task, const ZG6000::ListStringMap& listTaskItem)
{
    const auto& ruleProxy = ZGProxyMng::instance()->getProxyMPRuleEngine();
	if (ruleProxy == nullptr)
	{
		ZGLOG_ERROR(u8"获取命令规则服务代理对象失败");
		return;
	}
	const auto& commandProxy = ZGProxyMng::instance()->getProxyMPCommandProcess();
	if (commandProxy == nullptr)
	{
		ZGLOG_ERROR(u8"获取命令处理服务代理对象失败");
		return;
	}
    const auto& deviceProxy = ZGProxyMng::instance()->getProxyMPDeviceProperty();
    if (deviceProxy == nullptr)
	{
        ZGLOG_ERROR(u8"获取设备属性服务代理对象失败");
		return;
	}
	ZGLOG_DEBUG("processSequenceTask");
	try
	{
		const auto& taskID = ZGUtils::get(task, "id");
		const auto& rtCurrentItemID = ZGUtils::get(task, "rtCurrentItemID");
		const auto& destValue = ZGUtils::get(task, "destValue");
		for (size_t i = 0; i < listTaskItem.size(); ++i)
		{
			const auto& taskItem = listTaskItem[i];
			if (!rtCurrentItemID.empty() && (rtCurrentItemID != "-1"))
			{
				const auto& itemID = ZGUtils::get(taskItem, "id");
				if (itemID != rtCurrentItemID)
					continue;
			}
			const auto& rtTaskItemStateID = ZGUtils::get(taskItem, "rtTaskItemStateID");
			const auto& tableName = ZGUtils::get(taskItem, "tableName");
			const auto& dataID = ZGUtils::get(taskItem, "dataID");
			if (rtTaskItemStateID != "ZG_TS_FINISHED" && rtTaskItemStateID != "ZG_TS_EXECUTING")
			{
				try
				{
                    ZG6000::ErrorInfo e;
					std::string equipmentID;
					if (!ZGProxyCommon::getDataByField(tableName, dataID, "equipmentID", equipmentID))
					{
						ZGLOG_DEBUG(QStringLiteral("获取数据'%1'关联一次设备失败").arg(dataID.c_str()));
						return;
					}
					ZG6000::StringMap itemState;
					if (!equipmentID.empty())
					{
                        bool allow;
                        ZG6000::StringMap conditions;
                        if (!deviceProxy->isAllowCtrl(equipmentID, allow, conditions, e) || (!allow))
						{
							ZGLOG_DEBUG(QStringLiteral("设备'%1'不允许遥控").arg(equipmentID.c_str()));
							ZGLOG_DEBUG(e);
							itemState["id"] = ZGUtils::get(taskItem, "id");
							itemState["rtTaskItemStateID"] = "ZG_TS_CTRL_FORBID";
							if (!updateTaskItem(itemState))
								return;
							ZG6000::StringMap taskState;
							taskState["id"] = taskID;
							taskState["rtCurrentItemID"] = ZGUtils::get(taskItem, "id");
							taskState["rtEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
							taskState["rtTaskStateID"] = "ZG_TS_EXECUTE_ERROR";
							updateTask(taskState);
							return;
						}
					}
					ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'执行条件").arg(dataID.c_str()));
                    if (!ruleProxy->checkCommandExecCondition(dataID, destValue, e))
					{
                        ZGLOG_DEBUG(e);
						ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'执行条件失败").arg(dataID.c_str()));
						itemState["id"] = ZGUtils::get(taskItem, "id");
						itemState["rtTaskItemStateID"] = "ZG_TS_EXEC_COND_ERROR";
						if (!updateTaskItem(itemState))
							return;
						ZG6000::StringMap taskState;
						taskState["id"] = taskID;
						taskState["rtCurrentItemID"] = ZGUtils::get(taskItem, "id");
						taskState["rtEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
						taskState["rtTaskStateID"] = "ZG_TS_EXECUTE_ERROR";
						updateTask(taskState);
						return;
					}
					ZGLOG_DEBUG(QStringLiteral("发送命令，dataID = '%1'").arg(dataID.c_str()));
					if (!sendControl(tableName.c_str(), dataID.c_str(), destValue.c_str()))
					{
						ZGLOG_ERROR(QStringLiteral("发送命令失败，dataID = '%1'").arg(dataID.c_str()));
						return;
					}
					itemState["id"] = ZGUtils::get(taskItem, "id");
					itemState["rtStartTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
					itemState["rtTaskItemStateID"] = "ZG_TS_EXECUTING";
					ZGLOG_DEBUG(
						QStringLiteral("更新项'%1'状态'%2'").arg(itemState["id"].c_str()).arg(itemState["rtTaskItemStateID"].
							c_str()));
					updateTaskItem(itemState);
					updateTaskCurrentItem(taskID, itemState["id"]);
					return;
				}
				catch (const Ice::Exception& e)
				{
					ZGLOG_ERROR(e.what());
					return;
				}
			}
			if (rtTaskItemStateID == "ZG_TS_EXECUTING")
			{
				try
				{
                    ZG6000::ErrorInfo e;
					ZG6000::StringMap itemState;
					// ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'错误条件").arg(dataID.c_str()));
					//              if (ruleProxy->checkErrorCondition(dataID, destValue))
					//              {
					//                  ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'错误条件通过").arg(dataID.c_str()));
					//                  itemState["id"] = ZGUtils::get(taskItem, "id");
					//                  itemState["rtTaskItemStateID"] = "ZG_TS_ERROR_COND_ERROR";
					//                  if (!updateTaskItem(itemState))
					// return;
					//                  continue;
					//              }
					ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'确认条件").arg(dataID.c_str()));
                    if (!ruleProxy->checkCommandConfirmCondition(dataID, destValue, e))
					{
                        ZGLOG_DEBUG(e);
						ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'确认条件失败").arg(dataID.c_str()));
						return;
					}
					itemState["id"] = ZGUtils::get(taskItem, "id");
					itemState["rtEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
					itemState["rtTaskItemStateID"] = "ZG_TS_FINISHED";
					ZGLOG_DEBUG(
						QStringLiteral("更新项'%1'状态'%2'").arg(itemState["id"].c_str()).arg(itemState["rtTaskItemStateID"].
							c_str()));
					if (!updateTaskItem(itemState))
						return;
					if (i < listTaskItem.size() - 1)
					{
						const ZG6000::StringMap& nextItemState = listTaskItem[i + 1];
						const auto& nextItemID = ZGUtils::get(nextItemState, "id");
						ZG6000::StringMap taskState;
						updateTaskCurrentItem(taskID, nextItemID);
						return;
					}
				}
				catch (const Ice::Exception& e)
				{
					ZGLOG_ERROR(e.what());
					return;
				}
			}
		}
		ZG6000::StringMap taskState;
		taskState["id"] = taskID;
		taskState["rtEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
		bool pass;
		if (!checkTaskRuleCondition(task, "confirmConditionRuleID", pass))
		{
			ZGLOG_ERROR(QStringLiteral("验证任务'%1'确认规则失败").arg(taskID.c_str()));
			return;
		}
		const auto& appNodeID = ZGUtils::get(task, "appNodeID");
		const auto& subsystemID = ZGUtils::get(task, "subsystemID");
		const auto& majorID = ZGUtils::get(task, "majorID");
		const auto& name = ZGUtils::get(task, "name");
		if (!pass)
		{
			ZGLOG_ERROR(QStringLiteral("任务'%1'确认条件不满足").arg(taskID.c_str()));
			taskState["rtTaskStateID"] = "ZG_TS_CONF_COND_ERROR";
			if (!updateTask(taskState))
			{
				const std::string eventInfo = u8"任务'" + name + u8"'执行失败，确认条件不满足";
				publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, eventInfo);
			}
			return;
		}
		if (!checkTaskRuleCondition(task, "errorConditionRuleID", pass))
		{
			ZGLOG_ERROR(QStringLiteral("验证任务'%1'出错规则失败").arg(taskID.c_str()));
			return;
		}
		if (pass)
		{
			ZGLOG_ERROR(QStringLiteral("任务'%1'出错条件满足").arg(taskID.c_str()));
			taskState["rtTaskStateID"] = "ZG_TS_ERROR_COND_ERROR";
			if (updateTask(taskState))
			{
				const std::string eventInfo = u8"任务'" + name + u8"'执行出错";
				publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, eventInfo);
			}
			return;
		}
		taskState["rtCurrentItemID"] = "-1";
		taskState["rtTaskStateID"] = "ZG_TS_FINISHED";
		if (updateTask(taskState))
		{
			const std::string eventInfo = u8"任务'" + name + u8"'执行完成";
			publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, eventInfo);
		}
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
}

void ZGMPTaskManagerMng::processParallelTask(const ZG6000::StringMap& task, const ZG6000::ListStringMap& listTaskItem)
{
    const auto& ruleProxy = ZGProxyMng::instance()->getProxyMPRuleEngine();
	if (ruleProxy == nullptr)
	{
		ZGLOG_ERROR(u8"获取命令规则服务代理对象失败");
		return;
	}
	const auto& commandProxy = ZGProxyMng::instance()->getProxyMPCommandProcess();
	if (commandProxy == nullptr)
	{
		ZGLOG_ERROR(u8"获取命令处理服务代理对象失败");
		return;
	}
    const auto& deviceProxy = ZGProxyMng::instance()->getProxyMPDeviceProperty();
    if (deviceProxy == nullptr)
	{
        ZGLOG_ERROR(u8"获取设备属性服务代理对象失败");
		return;
	}
	ZGLOG_DEBUG("processParallelTask");
	try
	{
		const auto& taskID = ZGUtils::get(task, "id");
		const auto& destValue = ZGUtils::get(task, "destValue");
		for (const auto& taskItem: listTaskItem)
		{
			const auto& rtTaskItemStateID = ZGUtils::get(taskItem, "rtTaskItemStateID");
			const auto& tableName = ZGUtils::get(taskItem, "tableName");
			const auto& dataID = ZGUtils::get(taskItem, "dataID");
			if (rtTaskItemStateID == "ZG_TS_UNEXECUTE" || rtTaskItemStateID == "ZG_TS_EXEC_COND_ERROR" || rtTaskItemStateID.empty())
			{
				try
				{
                    ZG6000::ErrorInfo e;
					std::string equipmentID;
					if (!ZGProxyCommon::getDataByField(tableName, dataID, "equipmentID", equipmentID))
					{
						ZGLOG_DEBUG(QStringLiteral("获取数据'%1'关联一次设备失败").arg(dataID.c_str()));
						return;
					}
					ZG6000::StringMap itemState;
					if (!equipmentID.empty())
					{
                        bool allow;
                        ZG6000::StringMap conditions;
                        if (!deviceProxy->isAllowCtrl(equipmentID, allow, conditions, e) || (!allow))
						{
							ZGLOG_DEBUG(QStringLiteral("设备'%1'不允许遥控").arg(equipmentID.c_str()));
							ZGLOG_DEBUG(e);
							itemState["id"] = ZGUtils::get(taskItem, "id");
							itemState["rtTaskItemStateID"] = "ZG_TS_CTRL_FORBID";
							updateTaskItem(itemState);
							continue;
						}
					}
					ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'执行条件").arg(dataID.c_str()));
                    if (!ruleProxy->checkCommandExecCondition(dataID, destValue, e))
					{
                        ZGLOG_DEBUG(e);
						ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'执行条件失败").arg(dataID.c_str()));
						itemState["id"] = ZGUtils::get(taskItem, "id");
						itemState["rtTaskItemStateID"] = "ZG_TS_EXEC_COND_ERROR";
						updateTaskItem(itemState);
						continue;
					}
					ZGLOG_DEBUG(QStringLiteral("发送命令，dataID = '%1'").arg(dataID.c_str()));
					if (!sendControl(tableName.c_str(), dataID.c_str(), destValue.c_str()))
					{
						ZGLOG_ERROR(QStringLiteral("发送命令失败，dataID = '%1'").arg(dataID.c_str()));
						continue;
					}
					itemState["id"] = ZGUtils::get(taskItem, "id");
					itemState["rtStartTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
					itemState["rtTaskItemStateID"] = "ZG_TS_EXECUTING";
					ZGLOG_DEBUG(
						QStringLiteral("更新项'%1'状态'%2'").arg(itemState["id"].c_str()).arg(itemState["rtTaskItemStateID"].
							c_str()));
					updateTaskItem(itemState);
					continue;
				}
				catch (const Ice::Exception& e)
				{
					ZGLOG_ERROR(e.what());
					continue;
				}
			}
			if (rtTaskItemStateID == "ZG_TS_EXECUTING")
			{
				try
				{
                    ZG6000::ErrorInfo e;
					// ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'错误条件").arg(dataID.c_str()));
					ZG6000::StringMap itemState;
					// if (ruleProxy->checkErrorCondition(dataID, destValue))
					// {
					//     ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'错误条件通过").arg(dataID.c_str()));
					//     itemState["id"] = ZGUtils::get(taskItem, "id");
					//     itemState["rtEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
					//     itemState["rtTaskItemStateID"] = "ZG_TS_ERROR_COND_ERROR";
					//     if (updateTaskItem(itemState))
					//         --executeCount;
					//     continue;
					// }
					ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'确认条件").arg(dataID.c_str()));
                    if (!ruleProxy->checkCommandConfirmCondition(dataID, destValue, e))
					{
                        ZGLOG_DEBUG(e);
						ZGLOG_DEBUG(QStringLiteral("验证控制ID'%1'确认条件失败").arg(dataID.c_str()));
						continue;
					}
					itemState["id"] = ZGUtils::get(taskItem, "id");
					itemState["rtEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
					itemState["rtTaskItemStateID"] = "ZG_TS_FINISHED";
					ZGLOG_DEBUG(
						QStringLiteral("更新项'%1'状态'%2'").arg(itemState["id"].c_str()).arg(itemState["rtTaskItemStateID"].
							c_str()));
					updateTaskItem(itemState);
				}
				catch (const Ice::Exception& e)
				{
					ZGLOG_ERROR(e.what());
					return;
				}
			}
		}
		QString sql = QString(
				"SELECT COUNT(id) FROM mp_param_task_item WHERE taskID = '%1' AND rtTaskItemStateID = 'ZG_TS_EXECUTING' AND rtTaskItemStateID != 'ZG_TS_UNEXECUTE'")
			.arg(taskID.c_str());
		std::string exeCount;
		if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), exeCount))
		{
			return;
		}
		ZG6000::ListStringMap listExecTaskItem;
		if (!getExecutingTaskItem(taskID, listExecTaskItem))
			return;
		size_t executeCount = listExecTaskItem.size();
		if (executeCount == 0)
		{
			ZG6000::StringMap taskState;
			taskState["id"] = taskID;
			taskState["rtEndTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
			bool pass;
			if (!checkTaskRuleCondition(task, "confirmConditionRuleID", pass))
			{
				ZGLOG_ERROR(QStringLiteral("验证任务'%1'确认规则失败").arg(taskID.c_str()));
				return;
			}
			const auto& appNodeID = ZGUtils::get(task, "appNodeID");
			const auto& subsystemID = ZGUtils::get(task, "subsystemID");
			const auto& majorID = ZGUtils::get(task, "majorID");
			const auto& name = ZGUtils::get(task, "name");
			if (!pass)
			{
				ZGLOG_ERROR(QStringLiteral("任务'%1'确认条件不满足").arg(taskID.c_str()));
				taskState["rtTaskStateID"] = "ZG_TS_CONF_COND_ERROR";
				if (!updateTask(taskState))
				{
					const std::string eventInfo = u8"任务'" + name + u8"'执行失败，确认条件不满足";
					publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, eventInfo);
				}
				return;
			}
			if (!checkTaskRuleCondition(task, "errorConditionRuleID", pass))
			{
				ZGLOG_ERROR(QStringLiteral("验证任务'%1'出错规则失败").arg(taskID.c_str()));
				return;
			}
			if (pass)
			{
				ZGLOG_ERROR(QStringLiteral("任务'%1'出错条件满足").arg(taskID.c_str()));
				taskState["rtTaskStateID"] = "ZG_TS_ERROR_COND_ERROR";
				if (updateTask(taskState))
				{
					const std::string eventInfo = u8"任务'" + name + u8"'执行出错";
					publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, eventInfo);
				}
				return;
			}
			taskState["rtTaskStateID"] = "ZG_TS_FINISHED";
			if (updateTask(taskState))
			{
				const std::string eventInfo = u8"任务'" + name + u8"'执行完成";
				publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, eventInfo);
			}
		}
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
}

void ZGMPTaskManagerMng::publishTask(const std::string& appNodeID, const ZG6000::StringMap& task)
{
	try
	{
		ZG6000::StringMap taskEvent{task};
		extendTask(taskEvent);
		const auto& taskID = ZGUtils::get(taskEvent, "id");
		const auto& json = ZGJson::convertToJson(taskEvent);
		std::string topic = appNodeID + "/task";
		m_pMqttClient->sendPublish(topic.c_str(), json.c_str());
		topic = "mp_param_task/";
		topic += taskID;
		m_pMqttClient->sendPublish(topic.c_str(), json.c_str());
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
}

void ZGMPTaskManagerMng::publishTaskItem(const std::string& appNodeID, const ZG6000::StringMap& taskItem)
{
	try
	{
		ZG6000::StringMap taskItemEvent{taskItem};
		extendTaskItem(taskItemEvent);
		const auto& json = ZGJson::convertToJson(taskItemEvent);
		const auto& id = ZGUtils::get(taskItem, "id");
		std::string taskID;
		if (!ZGProxyCommon::getDataByField("mp_param_task_item", id, "taskID", taskID))
		{
			ZGLOG_ERROR(QStringLiteral("获取任务项'%1'关联任务ID失败").arg(id.c_str()));
			return;
		}
		std::string topic = "mp_param_task/";
		topic += taskID + "/item";
		m_pMqttClient->sendPublish(topic.c_str(), json.c_str());
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
	}
}

void ZGMPTaskManagerMng::publishEvent(const std::string& eventTypeID, const std::string& alarmLevelID,
                                      const std::string& appNodeID, const std::string& subsystemID, const std::string& majorID, const std::string& eventInfo)
{
	const auto& eventProxy = ZGProxyMng::instance()->getProxySPEventProcess();
	if (eventProxy)
	{
		ZG6000::StringMap event;
		event.insert(std::make_pair("eventTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString()));
		event.insert(std::make_pair("eventTypeID", eventTypeID));
		event.insert(std::make_pair("alarmLevelID", alarmLevelID));
		event.insert(std::make_pair("appNodeID", appNodeID));
		event.insert(std::make_pair("subsystemID", subsystemID));
		event.insert(std::make_pair("majorID", majorID));
		event.insert(std::make_pair("eventInfo", eventInfo));
		try
		{
			const auto& onewayProxy = eventProxy->ice_oneway();
			onewayProxy->processEvent(event);
		}
		catch (const Ice::Exception& e)
		{
			ZGLOG_ERROR(e.what());
		}
	}
}

bool ZGMPTaskManagerMng::getCondition(const std::string& taskID, const std::string& conditionField, ZG6000::ListStringMap& lstMapData, ZG6000::ErrorInfo& e)
{
	return false;
	// QString sql = QString("SELECT %1 FROM mp_param_task WHERE id = '%2'").arg(conditionField.c_str()).arg(taskID.c_str());
	// std::string ruleID;
	// if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), ruleID))
	// {
	// 	e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_DB);
	// 	e.errDetail = u8"获取任务'" + taskID + u8"'规则ID失败";
	// 	return false;
	// }
	// const auto& ruleProxy = ZGProxyMng::instance()->getProxySPRuleEngine();
	// if (ruleProxy == nullptr)
	// {
	// 	e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
	// 	e.errDetail = u8"获取规则引擎服务代理对象失败";
	// 	return false;
	// }
	// try
	// {
	// 	return ruleProxy->getRuleCondition(ruleID, lstMapData, e);
	// }
	// catch (const Ice::Exception& ie)
	// {
	// 	e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_INTERNAL);
	// 	e.errDetail = ie.what();
	// 	return false;
	// }
}

bool ZGMPTaskManagerMng::isClientAllowCtrl(const std::string& clientID, bool& allow, ZG6000::ErrorInfo& e)
{
	
	ZG6000::StringMap record;
	if (!ZGProxyCommon::getDataByFields("sp_param_client", clientID, {"rtIsEnableCtrl", "rtAppNodeID"}, record))
	{
		e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
		e.errDetail = u8"获取客户端'" + clientID + u8"'关联应用节点失败";
		ZGLOG_ERROR(e);
		return false;
	}
	try
	{
		std::string rtAppNodeID = ZGUtils::get(record, "rtAppNodeID");
		std::string rtIsEnableCtrl = ZGUtils::get(record, "rtIsEnableCtrl");
		if (rtIsEnableCtrl != "1")
		{
			allow = false;
			ZGLOG_INFO(QStringLiteral("client '%1' is not allow ctrl.").arg(clientID.c_str()));
			return true;
		}
		ZG6000::StringMap appNode;
		if (!ZGProxyCommon::getDataByFields("sp_param_appnode", rtAppNodeID, {"isEnableCtrl", "rtGroundLock", "rtYkBlock", "rtRepairBlock", "rtForbid", "rtAuthPosID"},
			appNode))
		{
			e = ZGRuntime::instance()->getErrorInfo(ZGMPTaskManager::ZG_ERR_RT);
			e.errDetail = u8"获取应用节点'" + rtAppNodeID + u8"'参数失败";
			ZGLOG_ERROR(e);
			return false;
		}
		allow = true;
		if ((ZGUtils::get(appNode, "isEnableCtrl") != "1") || (ZGUtils::get(appNode, "rtGroundLock") == "1")
			|| (ZGUtils::get(appNode, "rtYkBlock") == "1") || (ZGUtils::get(appNode, "rtRepairBlock") == "1")
			|| (ZGUtils::get(appNode, "rtForbid") == "1") || (ZGUtils::get(appNode, "rtAuthPosID") != "ZG_AP_LOCAL"))
		{
			allow = false;
			ZGLOG_INFO(QStringLiteral("appNode '%1' is not allow ctrl.").arg(rtAppNodeID.c_str()));
		}
		return true;
	}
	catch (const std::exception& ex)
	{
		ZGLOG_ERROR(ex.what());
		return false;
	}
}

void ZGMPTaskManagerMng::onInitFinished()
{
	while (!initMqttClient())
	{
		ZGLOG_ERROR("initMqttClient error.");
		msleep(m_initInterval * 1000);
	}
	m_initialized = true;
	ZGLOG_INFO("ZGMPTaskManager init finished.");
	m_executeTimer.start(1000);
}

void ZGMPTaskManagerMng::onExecuteTask()
{
	if (!ZGRuntime::instance()->isMaster())
		return;
	std::string sql = "SELECT id FROM mp_param_task";
	ZG6000::StringList listTaskID;
	if (!ZGProxyCommon::execQuerySqlCol(sql, listTaskID))
	{
		ZGLOG_ERROR(u8"获取任务ID参数失败");
		return;
	}
	ZG6000::ListStringMap listTask;
	if (!ZGProxyCommon::mgetDataByID("mp_param_task", listTaskID, listTask))
	{
		ZGLOG_ERROR(QStringLiteral("获取任务参数失败"));
		return;
	}
	for (const auto& task: listTask)
	{
		bool overtime;
		const auto& rtTaskStateID = ZGUtils::get(task, "rtTaskStateID");
		// if (rtTaskStateID == "ZG_TS_FINISHED" || rtTaskStateID == "ZG_TS_STOPPED")
		// {
		// 	ZG6000::StringMap taskState;
		// 	const auto& taskID = ZGUtils::get(task, "id");
		// 	taskState["id"] = taskID;
		// 	taskState["rtOperUserID"] = "";
		// 	taskState["rtMonUserID"] = "";
		// 	taskState["rtStartTime"] = "";
		// 	taskState["rtEndTime"] = "";
		// 	taskState["rtCurrentItemID"] = "-1";
		// 	taskState["rtTaskStateID"] = "ZG_TS_UNEXECUTE";
		// 	updateTask(taskState);
		// 	sql = "SELECT id FROM mp_param_task_item WHERE taskID = '" + taskID + "' ORDER BY itemIndex";
		// 	ZG6000::StringList listItemID;
		// 	if (!ZGProxyCommon::execQuerySqlCol(sql, listItemID))
		// 	{
		// 		ZGLOG_ERROR(QStringLiteral("获取任务'%1'关联任务项ID失败").arg(taskID.c_str()));
		// 	}
		// 	ZG6000::StringMap itemState;
		// 	itemState["rtStartTime"] = "";
		// 	itemState["rtEndTime"] = "";
		// 	itemState["rtTaskItemStateID"] = "ZG_TS_UNEXECUTE";
		// 	if (!ZGProxyCommon::mupdateDataByFields("mp_param_task_item", listItemID, itemState))
		// 		ZGLOG_ERROR(QStringLiteral("更新任务'%1'任务项状态失败").arg(taskID.c_str()));
		// }
		if (rtTaskStateID == "ZG_TS_EXECUTING")
		{
			if (!isTaskOvertime(task, overtime))
				continue;
			const auto& taskId = ZGUtils::get(task, "id");
			if (overtime)
			{
				ZGLOG_DEBUG("task overtime");
				if (!updateTaskTimeout(taskId))
					ZGLOG_ERROR(QStringLiteral("更新任务'%1'超时状态失败").arg(taskId.c_str()));
				const auto& appNodeID = ZGUtils::get(task, "appNodeID");
				const auto& subsystemID = ZGUtils::get(task, "subsystemID");
				const auto& majorID = ZGUtils::get(task, "majorID");
				const auto& name = ZGUtils::get(task, "name");
				std::string eventInfo = u8"任务【" + name + u8"】执行超时";
				publishEvent("ZG_ET_OPER", "ZG_AL_LEVEL0", appNodeID, subsystemID, majorID, eventInfo);
				continue;
			}
			ZG6000::ListStringMap listTaskItem;
			if (!getTaskItems(taskId, listTaskItem))
			{
				ZGLOG_ERROR(QStringLiteral("获取任务'%2'项列表失败").arg(taskId.c_str()));
				continue;
			}
			const auto& taskTypeID = ZGUtils::get(task, "taskTypeID");
			if (taskTypeID == "ZG_TT_SEQUENCE")
				processSequenceTask(task, listTaskItem);
			if (taskTypeID == "ZG_TT_PARALLEL")
				processParallelTask(task, listTaskItem);
		}
	}
}

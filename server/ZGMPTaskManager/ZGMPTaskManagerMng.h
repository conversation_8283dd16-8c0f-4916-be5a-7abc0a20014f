#ifndef ZGMPTASKMANAGERMNG_H
#define ZGMPTASKMANAGERMNG_H

#include <QThread>
#include <QTimer>
#include <mutex>
#include "ZGServerCommon.h"

class ZGRedisClient;
class ZGMqttClient;

class ZGMPTaskManagerMng : public QThread
{
	Q_OBJECT
public:
	static ZGMPTaskManagerMng* instance();
	void init();
	bool checkState();
	void dispatchData(const std::string& tableName, const std::string& oper, const std::string& reason,
	                  const std::string& time, const ZG6000::ListRecord& listRecord);
	bool getTaskList(const std::string& appNodeID, const std::string& subsystemID, const std::string& majorID,
	                 const std::string& taskTypeID, ZG6000::ListStringMap& taskList, ZG6000::ErrorInfo& e);
	bool getTask(const std::string& taskID, ZG6000::StringMap& task, ZG6000::ErrorInfo& e);
	bool getTaskItems(const std::string& taskID, ZG6000::ListStringMap& itemList, ZG6000::ErrorInfo& e);
	bool startTask(const std::string& clientID, const std::string& operUserID, const std::string& monUserID,
	               const std::string& taskID, ZG6000::ErrorInfo& e);
	bool stopTask(const std::string& clientID, const std::string& operUserID, const std::string& monUserID,
	              const std::string& taskID, ZG6000::ErrorInfo& e);
	bool pauseTask(const std::string& clientID, const std::string& operUserID, const std::string& monUserID,
	               const std::string& taskID, ZG6000::ErrorInfo& e);
	bool resumeTask(const std::string& clientID, const std::string& operUserID, const std::string& monUserID,
	                const std::string& taskID, ZG6000::ErrorInfo& e);
	bool getExecCondition(const std::string& taskID, ZG6000::ListStringMap& lstMapData, ZG6000::ErrorInfo& e);
	bool getConfirmCondition(const std::string& taskID, ZG6000::ListStringMap& lstMapData, ZG6000::ErrorInfo& e);
	bool getErrorCondition(const std::string& taskID, ZG6000::ListStringMap& lstMapData, ZG6000::ErrorInfo& e);

protected:
	void run() override;

private:
	explicit ZGMPTaskManagerMng(QObject* parent = nullptr);
	void initEvents();
	void initServerInstConfig();
	bool initServerInstInfo();
	bool initRedisQueue();
	bool initMqttClient();
	bool initTaskType();
	bool initTaskState();
	bool initAppNode();
	bool initSubsystem();
	bool initMajor();
	bool initUser();
	bool initClient();
	void insertNameByID(ZG6000::StringMap& record, const ZG6000::StringMap& mapName, const std::string& fieldId,
	                    const std::string& fieldName);
	bool updateTaskTimeout(const std::string& taskID);
	bool updateTaskCurrentItem(const std::string& taskID, const std::string& currentItem);
	bool updateTask(ZG6000::StringMap& task);
	bool updateTaskItem(ZG6000::StringMap& item);
	void extendTask(ZG6000::StringMap& task);
	void extendTaskItem(ZG6000::StringMap& item);
	bool getTaskItems(const std::string& taskID, ZG6000::ListStringMap& listItems);
	bool getExecutingTaskItem(const std::string& taskID, ZG6000::ListStringMap& listItem);
	bool isTaskExecuting(const ZG6000::StringMap& task, bool& executing);
	bool isTaskOvertime(const ZG6000::StringMap& task, bool& overTime);
	bool checkTaskRuleCondition(const ZG6000::StringMap& task, const std::string& ruleID, bool& pass);
	bool sendCommand(const QString& topicName, const QString& commandID, const QString& id, const QString& value);
	bool sendControl(const QString& tableName, const QString& id, const QString& value);
	bool sendYk(const QString& id, const QString& value);
	bool sendYs(const QString& id, const QString& value);
	bool sendYt(const QString& id, const QString& value);
	void processSequenceTask(const ZG6000::StringMap& task, const ZG6000::ListStringMap& listTaskItem);
	void processParallelTask(const ZG6000::StringMap& task, const ZG6000::ListStringMap& listTaskItem);
	void publishTask(const std::string& appNodeID, const ZG6000::StringMap& task);
	void publishTaskItem(const std::string& appNodeID, const ZG6000::StringMap& taskItem);
	void publishEvent(const std::string& eventTypeID, const std::string& alarmLevelID, const std::string& appNodeID,
	                  const std::string& subsystemID, const std::string& majorID, const std::string& eventInfo);
	bool getCondition(const std::string& taskID, const std::string& conditionField, ZG6000::ListStringMap& lstMapData, ZG6000::ErrorInfo& e);
	bool isClientAllowCtrl(const std::string& clientID, bool& allow, ZG6000::ErrorInfo& e);

signals:
	void initFinished();

private slots:
	void onInitFinished();
	void onExecuteTask();

private:
	bool m_initialized{false};
	QString m_serverName{""};
	QString m_instName{""};
	bool m_masterInst{false};
	int m_initInterval{10};
	int m_checkInterval{10};
	QTimer m_initTimer;
	QTimer m_executeTimer;
	ZGRedisClient* m_pRedisRtQueue{nullptr};
	ZGMqttClient* m_pMqttClient{nullptr};
	ZG6000::StringMap m_mapClient;
	ZG6000::StringMap m_mapTaskType;
	ZG6000::StringMap m_mapTaskState;
	ZG6000::StringMap m_mapAppNode;
	ZG6000::StringMap m_mapSubsystem;
	ZG6000::StringMap m_mapMajor;
	ZG6000::StringMap m_mapUser;
	std::mutex m_mutex;
};

#endif // ZGMPTASKMANAGERMNG_H

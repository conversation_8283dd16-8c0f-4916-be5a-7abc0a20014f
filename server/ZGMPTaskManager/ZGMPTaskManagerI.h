#ifndef ZGMPTASKMANAGERI_H
#define ZGMPTASKMANAGERI_H

#include "ZGMPTaskManager.h"

namespace ZG6000
{
	class ZGMPTaskManagerI : public ZGMPTaskManager
	{
	public:
		bool getTask(std::string taskID, StringMap& task, ErrorInfo& e, const Ice::Current& current) override;
		ZGMPTaskManagerI();

		// ZGServerBase interface
	public:
		bool checkState(const Ice::Current& current) override;
		void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time,
			ListRecord listRecord, const Ice::Current& current) override;

		// ZGMPTaskManager interface
	public:
		bool getTaskList(std::string appNodeID, std::string subsystemID, std::string majorID, std::string taskTypeID,
		                 ListStringMap& taskList, ErrorInfo& e, const Ice::Current& current) override;
		bool getTaskItems(std::string taskID, ListStringMap& itemList, ErrorInfo& e,
		                  const Ice::Current& current) override;
		bool startTask(std::string clientID, std::string operUserID, std::string monUserID, std::string taskID,
		               ErrorInfo& e, const Ice::Current& current) override;
		bool stopTask(std::string clientID, std::string _cpp_operator, std::string monitor, std::string taskID, ErrorInfo& e, const Ice::Current& current) override;
		bool pauseTask(std::string clientID, std::string _cpp_operator, std::string monitor, std::string taskID, ErrorInfo& e, const Ice::Current& current) override;
		bool resumeTask(std::string clientID, std::string _cpp_operator, std::string monitor, std::string taskID, ErrorInfo& e, const Ice::Current& current) override;
		bool getExecCondition(std::string taskID, ListStringMap& lstMapData, ErrorInfo& e, const Ice::Current& current) override;
		bool getConfirmCondition(std::string taskID, ListStringMap& lstMapData, ErrorInfo& e, const Ice::Current& current) override;
		bool getErrorCondition(std::string taskID, ListStringMap& lstMapData, ErrorInfo& e, const Ice::Current& current) override;
	};
}

#endif // ZGMPTASKMANAGERI_H

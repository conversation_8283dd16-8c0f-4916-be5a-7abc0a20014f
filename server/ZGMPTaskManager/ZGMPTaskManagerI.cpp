#include "ZGMPTaskManagerI.h"
#include "ZGMPTaskManagerMng.h"

ZG6000::ZGMPTaskManagerI::ZGMPTaskManagerI()
{
    ZGMPTaskManagerMng::instance()->init();
}

bool ZG6000::ZGMPTaskManagerI::checkState(const Ice::Current& current)
{
    return ZGMPTaskManagerMng::instance()->checkState();
}

void ZG6000::ZGMPTaskManagerI::dispatchData(std::string tableName, std::string oper, std::string reason,
	std::string time, ListRecord listRecord, const Ice::Current& current)
{
    ZGMPTaskManagerMng::instance()->dispatchData(tableName, oper, reason, time, listRecord);
}

bool ZG6000::ZGMPTaskManagerI::getTask(std::string taskID, StringMap& task, ErrorInfo& e, const Ice::Current& current)
{
	return ZGMPTaskManagerMng::instance()->getTask(taskID, task, e);
}

bool ZG6000::ZGMPTaskManagerI::getTaskList(std::string appNodeID, std::string subsystemID, std::string majorID, std::string taskTypeID, ListStringMap& taskList, ErrorInfo& e, const Ice::Current& current)
{
    return ZGMPTaskManagerMng::instance()->getTaskList(appNodeID, subsystemID, majorID, taskTypeID, taskList, e);
}

bool ZG6000::ZGMPTaskManagerI::getTaskItems(std::string taskID, ListStringMap& itemList, ErrorInfo& e, const Ice::Current& current)
{
    return ZGMPTaskManagerMng::instance()->getTaskItems(taskID, itemList, e);
}

bool ZG6000::ZGMPTaskManagerI::startTask(std::string clientID, std::string operUserID, std::string monUserID,
	std::string taskID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGMPTaskManagerMng::instance()->startTask(clientID, operUserID, monUserID, taskID, e);
}

bool ZG6000::ZGMPTaskManagerI::stopTask(std::string clientID, std::string _cpp_operator, std::string monitor, std::string taskID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGMPTaskManagerMng::instance()->stopTask(clientID, _cpp_operator, monitor, taskID, e);
}

bool ZG6000::ZGMPTaskManagerI::pauseTask(std::string clientID, std::string _cpp_operator, std::string monitor, std::string taskID, ErrorInfo& e, const Ice::Current& current)
{
	return ZGMPTaskManagerMng::instance()->pauseTask(clientID, _cpp_operator, monitor, taskID, e);
}

bool ZG6000::ZGMPTaskManagerI::resumeTask(std::string clientID, std::string _cpp_operator, std::string monitor, std::string taskID, ErrorInfo& e, const Ice::Current& current)
{
	return ZGMPTaskManagerMng::instance()->resumeTask(clientID, _cpp_operator, monitor, taskID, e);
}

bool ZG6000::ZGMPTaskManagerI::getExecCondition(std::string taskID, ListStringMap& lstMapData, ErrorInfo& e, const Ice::Current& current)
{
	return ZGMPTaskManagerMng::instance()->getExecCondition(taskID, lstMapData, e);
}

bool ZG6000::ZGMPTaskManagerI::getConfirmCondition(std::string taskID, ListStringMap& lstMapData, ErrorInfo& e, const Ice::Current& current)
{
	return ZGMPTaskManagerMng::instance()->getConfirmCondition(taskID, lstMapData, e);
}

bool ZG6000::ZGMPTaskManagerI::getErrorCondition(std::string taskID, ListStringMap& lstMapData, ErrorInfo& e, const Ice::Current& current)
{
	return ZGMPTaskManagerMng::instance()->getErrorCondition(taskID, lstMapData, e);
}

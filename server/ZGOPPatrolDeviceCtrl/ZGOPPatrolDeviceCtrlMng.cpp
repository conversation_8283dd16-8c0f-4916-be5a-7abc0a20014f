#include "ZGOPPatrolDeviceCtrlMng.h"

#include <QJsonDocument>
#include <QJsonObject>
#include <QRandomGenerator>

#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGJson.h"
#include "ZGUtils.h"
#include "redis/ZGRedisClient.h"
#include "zgerror/ZGOPPatrolDeviceCtrlError.h"

namespace ZG6000
{
	ZGOPPatrolDeviceCtrlMng* ZGOPPatrolDeviceCtrlMng::instance()
	{
		if (g_pInstance == nullptr)
			g_pInstance = new ZGOPPatrolDeviceCtrlMng;
		return g_pInstance;
	}

	void ZGOPPatrolDeviceCtrlMng::init()
	{
		initEvents();
		initServerInstConfig();
		start();
		ZGLOG_INFO("ZGOPPatrolDeviceCtrl init start...");
	}

	ZGOPPatrolDeviceCtrlMng::ZGOPPatrolDeviceCtrlMng(QObject* parent)
		: QThread{parent}
	{
	}

	bool ZGOPPatrolDeviceCtrlMng::checkState(const Ice::Current& current)
	{
		return m_initialized;
	}

	bool ZGOPPatrolDeviceCtrlMng::presetPointCtrl(std::string presetPointID, ErrorInfo& e, const Ice::Current& current)
	{
		ZG6000::StringMap record;
        if (!ZGProxyCommon::getDataByFields("mp_param_preset", presetPointID, {"deviceID", "presetNo", "propertyName"}, record))
		{
			e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_RT);
			e.errDetail = QStringLiteral("获取预置点'%1'设备预置位失败").arg(presetPointID.c_str()).toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
        try
        {
            const auto& deviceID = ZGUtils::get(record, "deviceID");
            const auto& presetNo = ZGUtils::get(record, "presetNo");
            const auto& propertyName = ZGUtils::get(record, "propertyName");
            if (deviceID.empty() || presetNo.empty())
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_RT);
                e.errDetail = QStringLiteral("设备ID或预置位为空").arg(presetPointID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            std::string deviceTypeID;
            if (!ZGProxyCommon::getDataByField("mp_param_device", deviceID, "typeID", deviceTypeID))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取设备'%1'类型失败").arg(record["deviceID"].c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            std::string tableName, dataID;
            if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            ZGLOG_TRACE(QString("dataID: %1, presetNo: %2, dataType: %3").arg(dataID.c_str()).arg(presetNo.c_str()).arg(deviceTypeID.c_str()));
            if (deviceTypeID == "ZG_DT_CAMERA")
            {
                auto hikPrx = ZGProxyMng::instance()->getProxyMPVideoHIK();
                if (hikPrx == nullptr)
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("获取海康服务代理对象失败").toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                if (!hikPrx->ptzControlGotoPreset(dataID, std::atoi(presetNo.c_str()), e))
                {
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
            else
            {
                if (!sendYs(dataID.c_str(), presetNo.c_str()))
                {
                    e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("发送遥设'%1'失败").arg(dataID.c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
        }
        catch (const Ice::Exception& ie)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
            e.errDetail = ie.what();
            ZGLOG_ERROR(e);
            return false;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
		return true;
	}

	bool ZGOPPatrolDeviceCtrlMng::devicePresetCtrl(std::string deviceID, std::string presetNo, ErrorInfo& e, const Ice::Current& current)
	{
        QString sql = QString("SELECT id FROM mp_param_preset WHERE deviceID = '%1' AND presetNo = '%2'")
                            .arg(deviceID.c_str()).arg(presetNo.c_str());
        std::string presetID;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), presetID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取设备'%1'设置点'%2'ID失败").arg(deviceID.c_str()).arg(presetNo.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return presetPointCtrl(presetID, e, current);
	}

	bool ZGOPPatrolDeviceCtrlMng::captureImage(std::string yvID, std::string& url, ErrorInfo& e, const Ice::Current& current)
	{
		auto hikPrx = ZGProxyMng::instance()->getProxyMPVideoHIK();
		if (hikPrx == nullptr)
		{
			e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
			e.errDetail = QStringLiteral("获取海康视频服务代理对象失败").toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
		try
		{
            ZGLOG_TRACE(QString("capturePicture, yvID: '%1'").arg(yvID.c_str()));
            return hikPrx->capturePicture(yvID, url, e);
		}
		catch (const Ice::Exception& ie)
		{
			e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
			e.errDetail = ie.what();
			ZGLOG_ERROR(e);
			return false;
		}
	}

	bool ZGOPPatrolDeviceCtrlMng::recordAudio(std::string yvID, int duration, std::string& url, ErrorInfo& e, const Ice::Current& current)
	{
		return true;
	}

	bool ZGOPPatrolDeviceCtrlMng::recordVideo(std::string yvID, int duration, std::string& url, ErrorInfo& e, const Ice::Current& current)
	{
		auto hikPrx = ZGProxyMng::instance()->getProxyMPVideoHIK();
		if (hikPrx == nullptr)
		{
			e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
			e.errDetail = QStringLiteral("获取海康视频服务代理对象失败").toStdString();
			ZGLOG_ERROR(e);
			return false;
		}
		try
		{
            ZGLOG_TRACE(QString("yvID: '%1', duration: %2").arg(yvID.c_str()).arg(duration));
			return hikPrx->startRealRecord(yvID, duration, url, e);
		}
		catch (const Ice::Exception& ie)
		{
			e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
			e.errDetail = ie.what();
			ZGLOG_ERROR(e);
			return false;
		}
	}

	bool ZGOPPatrolDeviceCtrlMng::deviceYk(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo& e, const Ice::Current& current)
	{
        std::string tableName, dataID;
        if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        if (!sendYk(dataID.c_str(), propertyValue.c_str()))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("发送遥控'%1'失败").arg(dataID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
	}

	bool ZGOPPatrolDeviceCtrlMng::deviceYs(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo& e, const Ice::Current& current)
	{
		ZGLOG_DEBUG(QString("deviceYS, id: %1, name: '%2', value: '%3'").arg(deviceID.c_str()).arg(propertyName.c_str()).arg(propertyValue.c_str()));
		try
		{
			std::string tableName, dataID;
            if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
			{
				ZGLOG_ERROR(e);
				return false;
			}
			if (!sendYs(dataID.c_str(), propertyValue.c_str()))
			{
				e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
				e.errDetail = QStringLiteral("发送遥控'%1'失败").arg(dataID.c_str()).toStdString();
				ZGLOG_ERROR(e);
				return false;
			}
			return true;
		}
		catch (const Ice::Exception& ie)
		{
			e = ZGRuntime::instance()->getErrorInfo(::ZGOPPatrolDeviceCtrl::ZG_ERR_INTERNAL);
			e.errDetail = ie.what();
			ZGLOG_ERROR(e);
			return false;
		}
	}

	void ZGOPPatrolDeviceCtrlMng::run()
	{
		while (!initServerInstInfo())
		{
			ZGLOG_ERROR("initServerInstInfo error.");
			msleep(m_initInterval * 1000);
		}
		sleep(QRandomGenerator::global()->bounded(5, 10));
		while (!initDevicePreset())
		{
			ZGLOG_ERROR("initDevicePreset error.");
			msleep(m_initInterval * 1000);
		}
		while (!initDevicePos())
		{
			ZGLOG_ERROR("initDevicePos error.");
			msleep(m_initInterval * 1000);
		}
		m_masterInst = ZGRuntime::instance()->isMaster();
		emit initFinished();
	}

	void ZGOPPatrolDeviceCtrlMng::onInitFinished()
	{
		while (!initRedis())
		{
			ZGLOG_ERROR("initRedis error.");
			msleep(m_initInterval * 1000);
		}
		m_initialized = true;
		ZGLOG_INFO("ZGOPPatrolDeviceCtrl init finished.");
		m_checkTimer.start(m_checkInterval * 1000);
	}

	void ZGOPPatrolDeviceCtrlMng::onCheckStatus()
	{
		m_masterInst = ZGRuntime::instance()->isMaster();
	}

	void ZGOPPatrolDeviceCtrlMng::onReceivedDeviceChange(const QString& channel, const QString& message)
	{
		ZGLOG_DEBUG(QString("Receive message: %1, %2").arg(channel).arg(message));
		qsizetype pos = channel.indexOf("/");
		if (pos == -1)
			return;
		QString deviceID = channel.mid(pos + 1);
		QJsonDocument doc = QJsonDocument::fromJson(message.toUtf8());
		const auto& devObj = doc.object();
        if ((devObj.find("PosX") == devObj.end()) && (devObj.find("PosY") == devObj.end()))
			return;
        if (devObj.find("PosX") != devObj.end())
            updatePosition(devObj, deviceID, "PosX");
        if (devObj.find("PosY") != devObj.end())
            updatePosition(devObj, deviceID, "PosY");
		updatePreset(deviceID);
	}

	void ZGOPPatrolDeviceCtrlMng::initEvents()
	{
		connect(&m_checkTimer, &QTimer::timeout, this, &ZGOPPatrolDeviceCtrlMng::onCheckStatus);
		connect(this, &ZGOPPatrolDeviceCtrlMng::initFinished, this, &ZGOPPatrolDeviceCtrlMng::onInitFinished);
	}

	void ZGOPPatrolDeviceCtrlMng::initServerInstConfig()
	{
		const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
		QString errMsg;
		int value;
		if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
			ZGLOG_WARN(errMsg);
		else
			m_initInterval = value;
		if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
			ZGLOG_WARN(errMsg);
		else
			m_checkInterval = value;
	}

	bool ZGOPPatrolDeviceCtrlMng::initServerInstInfo()
	{
		m_serverName = ZGRuntime::instance()->getServerID();
		if (m_serverName.isEmpty())
		{
			ZGLOG_ERROR("Empty server id.");
			return false;
		}
		m_instName = ZGRuntime::instance()->getInstanceID();
		if (m_instName.isEmpty())
		{
			ZGLOG_ERROR("Empty server instance id.");
			return false;
		}
		return true;
	}

	bool ZGOPPatrolDeviceCtrlMng::initDevicePreset()
	{
        QString sql = QString("SELECT deviceID, presetNo, presetParam FROM mp_param_preset a "
                      "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
                      "WHERE b.typeID = 'ZG_DT_ROBOT' AND b.isEnable = 1 ORDER BY a.id");
		ZG6000::ListStringMap listRecord;
		if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
		{
			ZGLOG_ERROR(QStringLiteral("获取机器人设备预置信息失败"));
			return false;
		}
		try
		{
			for (const auto& record : listRecord)
			{
				const auto& deviceID = ZGUtils::get(record, "deviceID");
				ZG6000::StringMap presetParam;
				std::string errMsg;
				if (!ZGJson::convertFromJson(ZGUtils::get(record, "presetParam"), presetParam, errMsg))
				{
					ZGLOG_ERROR(errMsg.c_str());
					return false;
				}
                const auto& posX = ZGUtils::get(presetParam, "xpos");
                const auto& posY = ZGUtils::get(presetParam, "ypos");
                const auto& presetNo = ZGUtils::get(record, "presetNo");
                m_mapDevicePreset[deviceID].push_back({{"PosX", posX}, {"PosY", posY}, {"PresetNo", presetNo}});
			}
		}
		catch (const std::exception& e)
		{
			ZGLOG_ERROR(e.what());
			return false;
		}
		return true;
	}

	bool ZGOPPatrolDeviceCtrlMng::initDevicePos()
	{
		QString sql = QString("SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_ROBOT'");
		ZG6000::StringList listDeviceID;
		if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
		{
			ZGLOG_ERROR(QStringLiteral("获取机器人ID列表失败"));
			return false;
		}
        for (const auto & deviceID : listDeviceID)
        {
            std::string posX, posY;
            ErrorInfo e;
            m_mapDevicePos.insert({ deviceID, {{"PosX", 0.0}, {"PosY", 0.0}} });
            if (!ZGProxyCommon::getPropertyValue(deviceID, "PosX", posX, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            m_mapDevicePos[deviceID]["PosX"] = std::atof(posX.c_str());
            if (!ZGProxyCommon::getPropertyValue(deviceID, "PosY", posY, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            m_mapDevicePos[deviceID]["PosY"] = std::atof(posY.c_str());
        }
        return true;
	}

	bool ZGOPPatrolDeviceCtrlMng::initRedis()
	{
		QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
		listClientType << ZGRuntime::REDIS_RT_QUEUE << ZGRuntime::REDIS_RT_TOPIC;
		if (!ZGRuntime::instance()->initRedisClient(listClientType))
		{
			ZGLOG_ERROR("initRedisClient error.");
			return false;
		}
		m_pRedisRtQueue = ZGRuntime::instance()->getRedisClientRTQueue();
		if (m_pRedisRtQueue == nullptr)
		{
			ZGLOG_ERROR("getRedisClientRTQueue error.");
			return false;
		}
		m_pRedisRtTopic = ZGRuntime::instance()->getRedisClientRTTopic();
		if (m_pRedisRtTopic == nullptr)
		{
			ZGLOG_ERROR("getRedisClientRTTopic error.");
			return false;
		}
		QString sql = QString("SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_ROBOT'");
		ZG6000::StringList listDeviceID;
		if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
		{
			ZGLOG_ERROR("Get robot device error.");
			return false;
		}
		ZG6000::StringList listTopic;
		for (const auto& deviceId : listDeviceID)
		{
			listTopic.push_back("mp_param_device/" + deviceId);
		}
		if (!listDeviceID.empty())
			m_pRedisRtTopic->subscribe(listTopic);
		connect(m_pRedisRtTopic, &ZGRedisClient::receivedMessage, this, &ZGOPPatrolDeviceCtrlMng::onReceivedDeviceChange);
		m_pRedisRtTopic->consume();
		return true;
	}

	bool ZGOPPatrolDeviceCtrlMng::sendYk(const QString& id, const QString& value)
	{
		std::string sql =
			"SELECT a.isSelectCtrl FROM mp_param_model_yk a LEFT JOIN mp_param_dataset_yk b ON b.dataModelID = a.id "
			"WHERE b.id = '" + id.toStdString() + "' ORDER BY a.id";
		std::string result;
		if (!ZGProxyCommon::execQuerySqlField(sql, result))
		{
			ZGLOG_ERROR("execQuerySqlField error.");
			return false;
		}
		QString commandID = "ZG_DC_YK_EXEC";
		if (result == "1")
			commandID = "ZG_DC_YK_SELECT";
		return sendCommand("ZG_Q_SYSTEM_YK", commandID, id, value);
	}

	bool ZGOPPatrolDeviceCtrlMng::sendYs(const QString& id, const QString& value)
	{
		std::string sql =
			"SELECT mp_param_model_ys.isSelectCtrl FROM mp_param_model_ys LEFT JOIN mp_param_dataset_ys ON mp_param_dataset_ys.dataModelID = mp_param_model_ys.id "
			"WHERE mp_param_dataset_ys.id = '" + id.toStdString() + "'";
		std::string result;
		if (!ZGProxyCommon::execQuerySqlField(sql, result))
		{
			ZGLOG_ERROR("execQuerySqlField error.");
			return false;
		}
		QString commandID = "ZG_DC_YS_EXEC";
		if (result == "1")
			commandID = "ZG_DC_YS_SELECT";
		return sendCommand("ZG_Q_SYSTEM_YS", commandID, id, value);
	}

	bool ZGOPPatrolDeviceCtrlMng::sendCommand(const QString& topicName, const QString& commandID, const QString& id, const QString& value)
	{
		QJsonArray array;
		QJsonObject object;
		object["id"] = id;
		object["commandID"] = commandID;
		object["srcType"] = "auto";
		object["srcID"] = "-1";
		object["rtCode"] = QString::number(ZGUtils::genNumber(1, 100000));
		object["rtValue"] = value;
		object["rtCommandTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
		array.append(object);
		QJsonDocument doc(array);
		long long size;
		QString errMsg;
		QByteArray ba = doc.toJson();
		ZGLOG_DEBUG(ba);
		if (!m_pRedisRtQueue->rpush(topicName, doc.toJson(QJsonDocument::Compact), size, errMsg))
		{
			ZGLOG_ERROR(QStringLiteral("提交到命令队列失败: '%1'").arg(errMsg));
			return false;
		}
		return true;
	}

	void ZGOPPatrolDeviceCtrlMng::updatePosition(const QJsonObject& devObj, const QString& deviceID, const QString& propPos)
	{
		const auto& posObj = devObj[propPos].toObject();
        if (posObj.find("rtNewValue") != posObj.end())
        {
            const auto& value = posObj["rtNewValue"].toString().toDouble();
            m_mapDevicePos[deviceID.toStdString()][propPos.toStdString()] = value;
        }
	}

	void ZGOPPatrolDeviceCtrlMng::updatePreset(const QString& deviceID)
	{
		auto pair = m_mapDevicePreset.find(deviceID.toStdString());
		if (pair == m_mapDevicePreset.end())
		{
			ZGLOG_DEBUG(QString("can't find deviceID %1").arg(deviceID));
			return;
		}
		try
		{
			for (const auto& record : pair->second)
			{
				auto it = m_mapDevicePos.find(deviceID.toStdString());
				if (it == m_mapDevicePos.end())
					return;
                double xPos = m_mapDevicePos[deviceID.toStdString()]["PosX"];
                double yPos = m_mapDevicePos[deviceID.toStdString()]["PosY"];
                double xParamPos = std::atof(ZGUtils::get(record, "PosX").c_str());
                double yParamPos = std::atof(ZGUtils::get(record, "PosY").c_str());
                if ((std::abs(xPos - xParamPos) >= 3.0) || (std::abs(yPos - yParamPos) >= 3.0))
					continue;
				ErrorInfo e;
                if (!ZGProxyCommon::updatePropertyValue(deviceID.toStdString(), "Preset", ZGUtils::get(record, "PresetNo"), e))
					ZGLOG_ERROR(e);
				break;
			}
		}
		catch (const Ice::Exception& e)
		{
			ZGLOG_ERROR(e.what());
		}
		catch (const std::exception& e)
		{
			ZGLOG_ERROR(e.what());
		}
	}
} // namespace ZG6000

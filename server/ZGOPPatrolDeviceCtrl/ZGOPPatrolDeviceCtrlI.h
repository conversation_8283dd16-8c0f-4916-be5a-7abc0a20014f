#ifndef ZG6000_ZGOPPATROLDEVICECTRLI_H
#define ZG6000_ZGOPPATROLDEVICECTRLI_H

#include "ZGOPPatrolDeviceCtrl.h"

namespace ZG6000 {

class ZGOPPatrolDeviceCtrlI : public ZG6000::ZGOPPatrolDeviceCtrl
{
public:
	ZGOPPatrolDeviceCtrlI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;

    // ZGOPPatrolDeviceCtrl interface
public:
    bool presetPointCtrl(std::string presetPointID, ErrorInfo &e, const Ice::Current &current) override;
    bool devicePresetCtrl(std::string deviceID, std::string presetNo, ErrorInfo &e, const Ice::Current &current) override;
    bool captureImage(std::string yvID, std::string& url, ErrorInfo& e, const Ice::Current& current) override;
    bool recordAudio(std::string yvID, int duration, std::string& url, ErrorInfo& e, const Ice::Current& current) override;
    bool recordVideo(std::string yvID, int duration, std::string& url, ErrorInfo& e, const Ice::Current& current) override;
    bool deviceYk(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo& e, const Ice::Current& current) override;
    bool deviceYs(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo& e, const Ice::Current& current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGOPPATROLDEVICECTRLI_H

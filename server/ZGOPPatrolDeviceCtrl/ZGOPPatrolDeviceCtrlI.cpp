#include "ZGOPPatrolDeviceCtrlI.h"
#include "ZGOPPatrolDeviceCtrlMng.h"

namespace ZG6000 {

ZGOPPatrolDeviceCtrlI::ZGOPPatrolDeviceCtrlI()
{
	ZGOPPatrolDeviceCtrlMng::instance()->init();
}

bool ZGOPPatrolDeviceCtrlI::checkState(const Ice::Current &current)
{
	return ZGOPPatrolDeviceCtrlMng::instance()->checkState(current);
}

bool ZGOPPatrolDeviceCtrlI::presetPointCtrl(std::string presetPointID, ErrorInfo &e, const Ice::Current &current)
{
	return ZGOPPatrolDeviceCtrlMng::instance()->presetPointCtrl(std::move(presetPointID), e, current);
}

bool ZGOPPatrolDeviceCtrlI::devicePresetCtrl(std::string deviceID, std::string presetNo, ErrorInfo &e, const Ice::Current &current)
{
	return ZGOPPatrolDeviceCtrlMng::instance()->devicePresetCtrl(std::move(deviceID), std::move(presetNo), e, current);
}

bool ZGOPPatrolDeviceCtrlI::captureImage(std::string yvID, std::string& url, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPPatrolDeviceCtrlMng::instance()->captureImage(std::move(yvID), url, e, current);
}

bool ZGOPPatrolDeviceCtrlI::recordAudio(std::string yvID, int duration, std::string& url, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPPatrolDeviceCtrlMng::instance()->recordAudio(std::move(yvID), duration, url, e, current);
}

bool ZGOPPatrolDeviceCtrlI::recordVideo(std::string yvID, int duration, std::string& url, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPPatrolDeviceCtrlMng::instance()->recordVideo(std::move(yvID), duration, url, e, current);
}

bool ZGOPPatrolDeviceCtrlI::deviceYk(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPPatrolDeviceCtrlMng::instance()->deviceYk(std::move(deviceID), std::move(propertyName), std::move(propertyValue), e, current);
}

bool ZGOPPatrolDeviceCtrlI::deviceYs(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo& e, const Ice::Current& current)
{
	return ZGOPPatrolDeviceCtrlMng::instance()->deviceYs(std::move(deviceID), std::move(propertyName), std::move(propertyValue), e, current);
}
} // namespace ZG6000

#ifndef ZG6000_ZGOPPATROLDEVICECTRLMNG_H
#define ZG6000_ZGOPPATROLDEVICECTRLMNG_H

#include <QThread>
#include <QTimer>
#include <QJsonObject>
#include <Ice/Ice.h>
#include "ZGServerCommon.h"

class ZGRedisClient;
namespace ZG6000 {
class ZGOPPatrolDeviceCtrlMng : public QThread
{
	Q_OBJECT
public:
    static ZGOPPatrolDeviceCtrlMng* instance();

public:
    void init();
    bool checkState(const Ice::Current& current);
    bool presetPointCtrl(std::string presetPointID, ErrorInfo& e, const Ice::Current& current);
    bool devicePresetCtrl(std::string deviceID, std::string presetNo, ErrorInfo& e, const Ice::Current& current);
    bool captureImage(std::string yvID, std::string& url, ErrorInfo& e, const Ice::Current& current);
    bool recordAudio(std::string yvID, int duration, std::string& url, ErrorInfo& e, const Ice::Current& current);
    bool recordVideo(std::string yvID, int duration, std::string& url, ErrorInfo& e, const Ice::Current& current);
    bool deviceYk(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo& e, const Ice::Current& current);
    bool deviceYs(std::string deviceID, std::string propertyName, std::string propertyValue, ErrorInfo& e, const Ice::Current& current);

protected:
    void run() override;

signals:
    void initFinished();

private slots:
    void onInitFinished();
    void onCheckStatus();
    void onReceivedDeviceChange(const QString& channel, const QString& message);

private:
    explicit ZGOPPatrolDeviceCtrlMng(QObject *parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initDevicePreset();
    bool initDevicePos();
    bool initRedis();
    bool sendYk(const QString& id, const QString& value);
    bool sendYs(const QString& id, const QString& value);
    bool sendCommand(const QString& topicName, const QString& commandID, const QString& id, const QString& value);
    void updatePosition(const QJsonObject& devObj, const QString& deviceID, const QString& propPos);
    void updatePreset(const QString& deviceID);

private:
    bool m_initialized{ false };
    QString m_serverName{ "" };
    QString m_instName{ "" };
    bool m_masterInst{ false };
    int m_initInterval{ 10 };
    int m_checkInterval{ 10 };
    QTimer m_checkTimer;
    ZGRedisClient* m_pRedisRtQueue{nullptr};
    ZGRedisClient* m_pRedisRtTopic{nullptr};
    std::unordered_map<std::string, std::vector<ZG6000::StringMap>> m_mapDevicePreset;
    std::unordered_map<std::string, std::map<std::string, double>> m_mapDevicePos;
};

inline static ZGOPPatrolDeviceCtrlMng* g_pInstance = nullptr;

} // namespace ZG6000

#endif // ZG6000_ZGOPPATROLDEVICECTRLMNG_H

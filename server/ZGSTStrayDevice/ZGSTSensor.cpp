#include "ZGSTSensor.h"

#include <csignal>

#include "ZGSTDevice.h"
#include "ZGSTStrayDefine.h"
#include "ZGSTStrayDeviceMng.h"
#include "ZGUtils.h"

ZGSTSensor::ZGSTSensor(const std::string& sensorID, QObject* parent):
	ZGSTEndPoint(sensorID, parent)
{
}

bool ZGSTSensor::initialize()
{
	initProcessor();
	return initialValue();
}

void ZGSTSensor::onNewMinute()
{
	ZG6000::StringMap values;
	ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::getPropertyValues(m_id, {CALC_YC_BTDW, ST_YC_BTDW, ST_FIELD_STATE}, values, e))
	{
	    ZGLOG_ERROR(e);
		return;
	}
    if (std::abs(std::atof(values[CALC_YC_BTDW].c_str()) - std::atof(values[ST_YC_BTDW].c_str())) > 1.0)
    {
        std::string ysProp = std::string("CMD_") + ST_YC_BTDW;
        if (!ZG6000::ZGSTStrayDeviceMng::instance()->sendYsCommand("-1", m_id, ysProp, values[CALC_YC_BTDW], true, e))
            ZGLOG_ERROR(e);
    }
    if (!ZGProxyCommon::updatePropertyValueEx(m_id, CALC_YX_COMM_STATE, values[ST_FIELD_STATE], e, true))
		ZGLOG_ERROR(e);
}

bool ZGSTSensor::initialValue()
{
	QDateTime currTime = QDateTime::currentDateTime();
	resetPropertyValues({{CALC_YC_ZXPY_SUM_D1, ttDay}, {CALC_YC_ZXPY_COUNT_D1, ttDay}, {CALC_YC_ZXPY_AVG_D1, ttDay},
	                                    {CALC_YC_ZXPY_MAX_D1, ttDay}, {CALC_YC_BTDW, ttBt}, {CALC_YC_FXPY_SUM_D1, ttDay},
	                                    {CALC_YC_FXPY_COUNT_D1, ttDay}, {CALC_YC_FXPY_AVG_D1, ttDay}, {CALC_YC_FXPY_MAX_D1, ttDay},
	                                    {CALC_YC_ZXGD_SUM_D1, ttDay}, {CALC_YC_ZXGD_COUNT_D1, ttDay}, {CALC_YC_ZXGD_AVG_D1, ttDay},
	                                    {CALC_YC_ZXGD_MAX_D1, ttDay}, {CALC_YC_FXGD_SUM_D1, ttDay}, {CALC_YC_FXGD_COUNT_D1, ttDay},
                                        {CALC_YC_FXGD_AVG_D1, ttDay}, {CALC_YC_FXGD_MAX_D1, ttDay}, {CALC_YC_ZXGJ_SUM_D1, ttDay},
	                                    {CALC_YC_ZXGJ_COUNT_D1, ttDay}, {CALC_YC_ZXGJ_AVG_D1, ttDay}, {CALC_YC_ZXGJ_MAX_D1, ttDay},
	                                    {CALC_YC_FXGJ_SUM_D1, ttDay}, {CALC_YC_FXGJ_COUNT_D1, ttDay}, {CALC_YC_FXGJ_AVG_D1, ttDay},
	                                    {CALC_YC_FXGJ_MAX_D1, ttDay}, {CALC_YC_ZXJD_SUM_D1, ttDay}, {CALC_YC_ZXJD_COUNT_D1, ttDay},
	                                    {CALC_YC_ZXJD_AVG_D1, ttDay}, {CALC_YC_ZXJD_MAX_D1, ttDay}, {CALC_YC_FXJD_SUM_D1, ttDay},
	                                    {CALC_YC_FXJD_COUNT_D1, ttDay}, {CALC_YC_FXJD_AVG_D1, ttDay}, {CALC_YC_FXJD_MAX_D1, ttDay},
	                                    {CALC_YC_ZXPY_AVG_H1_R, ttHour}, {CALC_YC_ZXPY_MAX_H1_R, ttHour}, {CALC_YC_FXPY_AVG_H1_R, ttHour},
	                                    {CALC_YC_FXPY_MAX_H1_R, ttHour}, {CALC_YC_ZXGD_AVG_H1_R, ttHour}, {CALC_YC_ZXGD_MAX_H1_R, ttHour},
                                        {CALC_YC_FXGD_AVG_H1_R, ttHour}, {CALC_YC_FXGD_MAX_H1_R, ttHour}}, currTime);
	return true;
}

void ZGSTSensor::initProcessor()
{
	m_mapProcessor.insert({
		ST_YC_ZXADY, [this](auto&& ph)
		{
			calcZXA(std::forward<decltype(ph)>(ph));
		}
	});
	m_mapProcessor.insert({
		ST_YC_ZXBDY, [this](auto&& ph)
		{
			calcZXB(std::forward<decltype(ph)>(ph));
		}
	});
	m_mapProcessor.insert({
		ST_YC_JHDW, [this](auto&& ph)
		{
			calcJHPY(std::forward<decltype(ph)>(ph));
		}
	});
	m_mapProcessor.insert({
		ST_YC_JHPY_COUNT_M1, [this](auto&& ph)
		{
			calcJHPYStat(std::forward<decltype(ph)>(ph));
		}
	});
	m_mapProcessor.insert({
		ST_YC_GDDY_COUNT_M1, [this](auto&& ph)
		{
			calcGDDY(std::forward<decltype(ph)>(ph));
		}
	});
	m_mapProcessor.insert({
		ST_YC_GJDY, [this](auto&& ph)
		{
			calcGJDY(std::forward<decltype(ph)>(ph));
		}
	});
	m_mapProcessor.insert({
		ST_YC_JDDY, [this](auto&& ph)
		{
			calcJDDY(std::forward<decltype(ph)>(ph));
		}
	});
	m_mapProcessor.insert({
		ST_FIELD_STATE, [this](auto&& ph)
		{
            calcCommState(std::forward<decltype(ph)>(ph));
		}
		});
    m_mapProcessor.insert({
        CALC_YC_ZXPY_AVG_H1, [this](auto&& ph)
        {
            calcZXPLWarn(std::forward<decltype(ph)>(ph));
        }
        });
    m_mapProcessor.insert({
        CALC_YC_FXPY_AVG_H1, [this](auto&& ph)
        {
            calcFXPLWarn(std::forward<decltype(ph)>(ph));
        }
        });
}

void ZGSTSensor::calcZXA(const ZG6000::StringMap& _property)
{
    auto pair = _property.find("rtNewValue");
	if (pair != _property.end())
        calcCurrent(pair->second, ST_RAIL_RESA, CALC_YC_ZXADL);
}

void ZGSTSensor::calcZXB(const ZG6000::StringMap& _property)
{
    auto pair = _property.find("rtNewValue");
	if (pair != _property.end())
        calcCurrent(pair->second, ST_RAIL_RESB, CALC_YC_ZXBDL);
}

void ZGSTSensor::calcJHPY(const ZG6000::StringMap& _property)
{
	auto pair = _property.find("rtNewValue");
	if (pair == _property.end())
		return;
	const auto& jhVol = pair->second;
	std::string btdwVal;
    ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YC_BTDW, btdwVal, e))
	{
	    ZGLOG_ERROR(e);
		return;
	}
	double calcJHOffset = std::atof(jhVol.c_str()) - std::atof(btdwVal.c_str());
	QString val = QString::number(calcJHOffset, 'f', 2);
    if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YC_JHPY, val.toStdString(), e, true))
        ZGLOG_ERROR(e);
}

void ZGSTSensor::calcJHPYStat(const ZG6000::StringMap& _property)
{
    QMutexLocker locker(&m_mutex);
	calcH1Avg(CALC_YC_ZXPY_SUM_H1_R, ST_YC_ZXPY_SUM_M1, CALC_YC_ZXPY_COUNT_H1_R, ST_YC_ZXPY_COUNT_M1, CALC_YC_ZXPY_AVG_H1_R);
	calcMax(CALC_YC_ZXPY_MAX_H1_R, ST_YC_ZXPY_MAX_M1);
	calcDayAvg(CALC_YC_ZXPY_SUM_D1, ST_YC_ZXPY_SUM_M1, CALC_YC_ZXPY_COUNT_D1, ST_YC_ZXPY_COUNT_M1, CALC_YC_ZXPY_AVG_D1);
	calcMax(CALC_YC_ZXPY_MAX_D1, ST_YC_ZXPY_MAX_M1);
	calcH1Avg(CALC_YC_FXPY_SUM_H1_R, ST_YC_FXPY_SUM_M1, CALC_YC_FXPY_COUNT_H1_R, ST_YC_FXPY_COUNT_M1, CALC_YC_FXPY_AVG_H1_R);
	calcMax(CALC_YC_FXPY_MAX_H1_R, ST_YC_FXPY_MAX_M1);
	calcDayAvg(CALC_YC_FXPY_SUM_D1, ST_YC_FXPY_SUM_M1, CALC_YC_FXPY_COUNT_D1, ST_YC_FXPY_COUNT_M1, CALC_YC_FXPY_AVG_D1);
	calcMax(CALC_YC_FXPY_MAX_D1, ST_YC_FXPY_MAX_M1);
}

void ZGSTSensor::calcGDDY(const ZG6000::StringMap& _property)
{
    QMutexLocker locker(&m_mutex);
	calcH1Avg(CALC_YC_ZXGD_SUM_H1_R, ST_YC_ZXGD_SUM_M1, CALC_YC_ZXGD_COUNT_H1_R, ST_YC_ZXGD_COUNT_M1, CALC_YC_ZXGD_AVG_H1_R);
	calcMax(CALC_YC_ZXGD_MAX_H1_R, ST_YC_ZXGD_MAX_M1);
	calcDayAvg(CALC_YC_ZXGD_SUM_D1, ST_YC_ZXGD_SUM_M1, CALC_YC_ZXGD_COUNT_D1, ST_YC_ZXGD_COUNT_M1, CALC_YC_ZXGD_AVG_D1);
	calcMax(CALC_YC_ZXGD_MAX_D1, ST_YC_ZXGD_MAX_M1);
	calcH1Avg(CALC_YC_FXGD_SUM_H1_R, ST_YC_FXGD_SUM_M1, CALC_YC_FXGD_COUNT_H1_R, ST_YC_FXGD_COUNT_M1, CALC_YC_FXGD_AVG_H1_R);
	calcMax(CALC_YC_FXGD_MAX_H1_R, ST_YC_FXGD_MAX_M1);
	calcDayAvg(CALC_YC_FXGD_SUM_D1, ST_YC_FXGD_SUM_M1, CALC_YC_FXGD_COUNT_D1, ST_YC_FXGD_COUNT_M1, CALC_YC_FXGD_AVG_D1);
	calcMax(CALC_YC_FXGD_MAX_D1, ST_YC_FXGD_MAX_M1);
}

void ZGSTSensor::calcGJDY(const ZG6000::StringMap& _property)
{
	auto pair = _property.find("rtNewValue");
	if (pair == _property.end())
		return;
	const auto& gjValue = pair->second;
	double value = std::atof(gjValue.c_str());
	if (value >= 0)
	{
		calcDayAvg(CALC_YC_ZXGJ_SUM_D1, gjValue, CALC_YC_ZXGJ_COUNT_D1, CALC_YC_ZXGJ_AVG_D1);
		calcMax(CALC_YC_ZXGJ_MAX_D1, value);
	}
	else
	{
		calcDayAvg(CALC_YC_FXGJ_SUM_D1, gjValue, CALC_YC_FXGJ_COUNT_D1, CALC_YC_FXGJ_AVG_D1);
		calcMax(CALC_YC_FXGJ_MAX_D1, value);
	}
}

void ZGSTSensor::calcJDDY(const ZG6000::StringMap& _property)
{
	auto pair = _property.find("rtNewValue");
	if (pair == _property.end())
		return;
	const auto& jdValue = pair->second;
	double value = std::atof(jdValue.c_str());
	if (value >= 0)
	{
		calcDayAvg(CALC_YC_ZXJD_SUM_D1, jdValue, CALC_YC_ZXJD_COUNT_D1, CALC_YC_ZXJD_AVG_D1);
		calcMax(CALC_YC_ZXJD_MAX_D1, value);
	}
	else
	{
		calcDayAvg(CALC_YC_FXJD_SUM_D1, jdValue, CALC_YC_FXJD_COUNT_D1, CALC_YC_FXJD_AVG_D1);
		calcMax(CALC_YC_FXJD_MAX_D1, value);
	}
	QWriteLocker locker(&m_lock);
	m_cacheValue.push_back(value);
}

void ZGSTSensor::calcCommState(const ZG6000::StringMap& _property)
{
	auto pair = _property.find("rtNewValue");
	if (pair == _property.end())
		return;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::updatePropertyValueEx(m_id, CALC_YX_COMM_STATE, pair->second, e, true))
        ZGLOG_ERROR(e);
}

void ZGSTSensor::calcZXPLWarn(const ZG6000::StringMap& _property)
{
	std::string propertyValue;
	ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YC_ZXPY_AVG_H1, propertyValue, e))
	{
		ZGLOG_ERROR(e);
		return;
	}
	auto strayDevice = dynamic_cast<ZGSTDevice*>(parent());
	std::string warnValue;
	if (!ZGProxyCommon::getPropertyValue(strayDevice->id(), CALC_PARAM_JHPY_P_WARN, warnValue, e))
	{
		ZGLOG_ERROR(e);
		return;
	}
	if (std::atof(propertyValue.c_str()) > std::atof(warnValue.c_str()))
	{
        if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YX_JHPY_P_WARN, "2", e, true))
			ZGLOG_ERROR(e);
	}
    else
    {
        if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YX_JHPY_P_WARN, "1", e, true))
			ZGLOG_ERROR(e);
    }
}

void ZGSTSensor::calcFXPLWarn(const ZG6000::StringMap &_property)
{
	std::string propertyValue;
	ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YC_FXPY_AVG_H1, propertyValue, e))
    {
        ZGLOG_ERROR(e);
		return;
	}
    auto strayDevice = dynamic_cast<ZGSTDevice*>(parent());
    std::string warnValue;
    if (!ZGProxyCommon::getPropertyValue(strayDevice->id(), CALC_PARAM_JHPY_N_WARN, warnValue, e))
    {
		ZGLOG_ERROR(e);
		return;
    }
    if (std::abs(std::atof(propertyValue.c_str())) > std::abs(std::atof(warnValue.c_str())))
    {
        if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YX_JHPY_N_WARN, "2", e, true))
			ZGLOG_ERROR(e);
    }
    else
    {
        if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YX_JHPY_N_WARN, "1", e, true))
			ZGLOG_ERROR(e);
    }
}

void ZGSTSensor::onNewMinute30()
{
    
}

void ZGSTSensor::onNewHour()
{
	ZG6000::StringMap values;
	ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::getPropertyValuesEx(m_id, { CALC_YC_ZXPY_MAX_H1_R, CALC_YC_ZXPY_AVG_H1_R, CALC_YC_FXPY_MAX_H1_R,
			CALC_YC_FXPY_AVG_H1_R, CALC_YC_ZXGD_MAX_H1_R, CALC_YC_ZXGD_AVG_H1_R, CALC_YC_FXGD_MAX_H1_R,
			CALC_YC_FXGD_AVG_H1_R }, values, e))
	{
		ZGLOG_ERROR(e);
		return;
	}
	ZG6000::StringMap updateValues{ {CALC_YC_ZXPY_MAX_H1, values[CALC_YC_ZXPY_MAX_H1_R]}, {CALC_YC_ZXPY_AVG_H1, values[CALC_YC_ZXPY_AVG_H1_R]},
					{CALC_YC_FXPY_MAX_H1, values[CALC_YC_FXPY_MAX_H1_R]}, {CALC_YC_FXPY_AVG_H1, values[CALC_YC_FXPY_AVG_H1_R]},
						{CALC_YC_ZXGD_MAX_H1, values[CALC_YC_ZXGD_MAX_H1_R]}, {CALC_YC_ZXGD_AVG_H1, values[CALC_YC_ZXGD_AVG_H1_R]},
						{CALC_YC_FXGD_MAX_H1, values[CALC_YC_FXGD_MAX_H1_R]}, {CALC_YC_FXGD_AVG_H1, values[CALC_YC_FXGD_AVG_H1_R]},
						{CALC_YC_ZXPY_MAX_H1_R, "0"}, {CALC_YC_ZXPY_AVG_H1_R, "0"}, {CALC_YC_FXPY_MAX_H1_R, "0"}, {CALC_YC_FXPY_AVG_H1_R, "0"},
					{CALC_YC_ZXGD_MAX_H1_R, "0"}, {CALC_YC_ZXGD_AVG_H1_R, "0"}, {CALC_YC_FXGD_MAX_H1_R, "0"}, {CALC_YC_FXGD_AVG_H1_R, "0"} };
	if (!ZGProxyCommon::updatePropertyValuesEx(m_id, updateValues, e, true))
	{
		ZGLOG_ERROR(e);
	}
	m_cacheData[CALC_YC_ZXPY_SUM_H1_R] = "0";
	m_cacheData[CALC_YC_ZXPY_COUNT_H1_R] = "0";
	m_cacheData[CALC_YC_FXPY_SUM_H1_R] = "0";
	m_cacheData[CALC_YC_FXPY_COUNT_H1_R] = "0";
	m_cacheData[CALC_YC_ZXGD_SUM_H1_R] = "0";
	m_cacheData[CALC_YC_ZXGD_COUNT_H1_R] = "0";
	m_cacheData[CALC_YC_FXGD_SUM_H1_R] = "0";
	m_cacheData[CALC_YC_FXGD_COUNT_H1_R] = "0";
	std::vector<double> listValue;
	{
		QReadLocker locker(&m_lock);
		listValue = m_cacheValue;
	}
	if (!listValue.empty())
	{
		std::sort(listValue.begin(), listValue.end(), std::greater<double>());
		// 获取前10%数据并计算平均值
		int topCount = static_cast<int>(listValue.size());
		if ((listValue.size() / 10) > 1)
			topCount = static_cast<int>(listValue.size()) / 10;
		double sumValue = 0;
		std::for_each_n(listValue.begin(), topCount, [&sumValue](double value)
		{
			sumValue += value;
		});
		if (topCount == 0)
			topCount = 1;
		double avgValue = sumValue / topCount;
		QString val = QString::number(avgValue, 'f', 2);
        if (!ZGProxyCommon::updatePropertyValueEx(m_id, CALC_YC_ZXJD_PEAK_H1, val.toStdString(), e, true))
            ZGLOG_ERROR(e);
		QWriteLocker locker(&m_lock);
		m_cacheValue.clear();
	}
}

void ZGSTSensor::onNewDay()
{
    ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::updatePropertyValuesEx(m_id, {
		{CALC_YC_ZXPY_SUM_D1, "0"}, {CALC_YC_ZXPY_COUNT_D1, "0"}, {CALC_YC_ZXPY_AVG_D1, "0"}, {CALC_YC_ZXPY_MAX_D1, "0"},
		{CALC_YC_FXPY_SUM_D1, "0"}, {CALC_YC_FXPY_COUNT_D1, "0"}, {CALC_YC_FXPY_AVG_D1, "0"}, {CALC_YC_FXPY_MAX_D1, "0"},
		{CALC_YC_ZXGD_SUM_D1, "0"}, {CALC_YC_ZXGD_COUNT_D1, "0"}, {CALC_YC_ZXGD_AVG_D1, "0"}, {CALC_YC_ZXGD_MAX_D1, "0"},
		{CALC_YC_FXGD_SUM_D1, "0"}, {CALC_YC_FXGD_COUNT_D1, "0"}, {CALC_YC_FXGD_AVG_D1, "0"}, {CALC_YC_FXGD_MAX_D1, "0"},
		{CALC_YC_ZXGJ_SUM_D1, "0"}, {CALC_YC_ZXGJ_COUNT_D1, "0"}, {CALC_YC_ZXGJ_AVG_D1, "0"}, {CALC_YC_ZXGJ_MAX_D1, "0"},
		{CALC_YC_FXGJ_SUM_D1, "0"}, {CALC_YC_FXGJ_COUNT_D1, "0"}, {CALC_YC_FXGJ_AVG_D1, "0"}, {CALC_YC_FXGJ_MAX_D1, "0"},
		{CALC_YC_ZXJD_SUM_D1, "0"}, {CALC_YC_ZXJD_COUNT_D1, "0"}, {CALC_YC_ZXJD_AVG_D1, "0"}, {CALC_YC_ZXJD_MAX_D1, "0"},
		{CALC_YC_FXJD_SUM_D1, "0"}, {CALC_YC_FXJD_COUNT_D1, "0"}, {CALC_YC_FXJD_AVG_D1, "0"}, {CALC_YC_FXJD_MAX_D1, "0"}
    }, e, true))
		ZGLOG_ERROR(e);
}

void ZGSTSensor::onNewBtTime()
{
	std::string lastUc;
    ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YC_BTDW, lastUc, e))
	{
	    ZGLOG_ERROR(e);
		return;
	}
	double oldUc = std::atof(lastUc.c_str());
	double sumP = std::atof(m_cacheData[CALC_YC_ZXPY_SUM_H1_R].c_str());
	int countP = std::atoi(m_cacheData[CALC_YC_ZXPY_COUNT_H1_R].c_str());
	double sumN = std::atof(m_cacheData[CALC_YC_FXPY_SUM_H1_R].c_str());
	int countN = std::atoi(m_cacheData[CALC_YC_FXPY_COUNT_H1_R].c_str());
    double newUc = 0;
    double newSum = sumP + sumN + oldUc * (countP + countN);
    int newCount = countP + countN;
    if (newCount != 0)
    {
    	newUc = newSum / newCount;
    }
	// 获取传感器的父类对象
	auto strayDevice = dynamic_cast<ZGSTDevice*>(parent());
	// 从父类设备ID中获取本体电位的告警参数
	ZG6000::StringMap values;
	if (!ZGProxyCommon::getPropertyValues(strayDevice->id(), {PARAM_UC_UP_VALUE, PARAM_UC_DOWN_VALUE}, values, e))
	{
		ZGLOG_ERROR(e);
		return;
	}
	QString val = QString::number(newUc, 'f', 2);
	if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YC_BTDW, val.toStdString(), e, true))
		ZGLOG_ERROR(e);
	double ucUp = std::atof(values[PARAM_UC_UP_VALUE].c_str());
	double ucDown = std::atof(values[PARAM_UC_DOWN_VALUE].c_str());
	if ((newUc - ucUp) > 0.01 || (ucDown - newUc) > 0.01)
	{
		if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YX_UC_WARN, "2", e, true))
			ZGLOG_ERROR(e);
	}
	else
	{
		if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YX_UC_WARN, "1", e, true))
			ZGLOG_ERROR(e);
	}
}

bool ZGSTSensor::plSignal() const
{
	std::string plSig;
    ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YX_PL_SIGNAL, plSig, e))
	{
	    ZGLOG_ERROR(e);
		return false;
	}
	return (plSig == "2");
}

void ZGSTSensor::resetCalculation()
{
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::updatePropertyValuesEx(m_id, {{CALC_YC_ZXPY_SUM_D1, "0"}, {CALC_YC_ZXPY_COUNT_D1, "0"}, {CALC_YC_ZXPY_AVG_D1, "0"},
                                               {CALC_YC_ZXPY_MAX_D1, "0"}, {CALC_YC_BTDW, "0"}, {CALC_YC_FXPY_SUM_D1, "0"},
                                               {CALC_YC_FXPY_COUNT_D1, "0"}, {CALC_YC_FXPY_AVG_D1, "0"}, {CALC_YC_FXPY_MAX_D1, "0"},
                                               {CALC_YC_ZXGD_SUM_D1, "0"}, {CALC_YC_ZXGD_COUNT_D1, "0"}, {CALC_YC_ZXGD_AVG_D1, "0"},
                                               {CALC_YC_ZXGD_MAX_D1, "0"}, {CALC_YC_FXGD_SUM_D1, "0"}, {CALC_YC_FXGD_COUNT_D1, "0"},
                                               {CALC_YC_FXGD_AVG_D1, "0"}, {CALC_YC_FXGD_MAX_D1, "0"}, {CALC_YC_ZXGJ_SUM_D1, "0"},
                                               {CALC_YC_ZXGJ_COUNT_D1, "0"}, {CALC_YC_ZXGJ_AVG_D1, "0"}, {CALC_YC_ZXGJ_MAX_D1, "0"},
                                               {CALC_YC_FXGJ_SUM_D1, "0"}, {CALC_YC_FXGJ_COUNT_D1, "0"}, {CALC_YC_FXGJ_AVG_D1, "0"},
                                               {CALC_YC_FXGJ_MAX_D1, "0"}, {CALC_YC_ZXJD_SUM_D1, "0"}, {CALC_YC_ZXJD_COUNT_D1, "0"},
                                               {CALC_YC_ZXJD_AVG_D1, "0"}, {CALC_YC_ZXJD_MAX_D1, "0"}, {CALC_YC_FXJD_SUM_D1, "0"},
                                               {CALC_YC_FXJD_COUNT_D1, "0"}, {CALC_YC_FXJD_AVG_D1, "0"}, {CALC_YC_FXJD_MAX_D1, "0"},
                                               {CALC_YC_ZXPY_AVG_H1_R, "0"}, {CALC_YC_ZXPY_MAX_H1_R, "0"}, {CALC_YC_FXPY_AVG_H1_R, "0"},
                                               {CALC_YC_FXPY_MAX_H1_R, "0"}, {CALC_YC_ZXGD_AVG_H1_R, "0"}, {CALC_YC_ZXGD_MAX_H1_R, "0"},
                                               {CALC_YC_FXGD_AVG_H1_R, "0"}, {CALC_YC_FXGD_MAX_H1_R, "0"}}, e, true))
        ZGLOG_ERROR(e);
}

QTime ZGSTSensor::operStartTime()
{
    auto strayDevice = dynamic_cast<ZGSTDevice*>(parent());
    return strayDevice->operStartTime();
}

QTime ZGSTSensor::operEndTime()
{
    auto strayDevice = dynamic_cast<ZGSTDevice*>(parent());
    return strayDevice->operEndTime();
}

void ZGSTSensor::calcOffset(const std::string& volProp, const std::string& btProp, const std::string& offsetProp)
{
    ZG6000::StringMap values;
    ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::getPropertyValues(m_id, {volProp, btProp}, values, e))
	{
	    ZGLOG_ERROR(e);
		return;
	}
	double offsetVal = std::atof(values[volProp].c_str()) - std::atof(values[btProp].c_str());
	QString val = QString::number(offsetVal, 'f', 2);
    if (!ZGProxyCommon::updatePropertyValue(m_id, offsetProp, val.toStdString(), e, true))
		ZGLOG_ERROR(e);
}

void ZGSTSensor::calcCurrent(const std::string& volVal, const std::string& resProp, const std::string& currentProp)
{
	std::string resValue;
    ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::getPropertyValue(m_id, resProp, resValue, e))
		return;
	double resistance = std::atof(resValue.c_str());
	double vol = std::atof(volVal.c_str());
	if (std::fabs(resistance - 0) < 0.000001)
	{
        ZGLOG_ERROR(QStringLiteral("传感器'%1'钢轨电阻为0，不能计算平均值").arg(m_id.c_str()));
		resistance = 1.0;
	}
	double calcCurrent = vol / resistance;
	QString val = QString::number(calcCurrent, 'f', 2);
    if (!ZGProxyCommon::updatePropertyValue(m_id, currentProp, val.toStdString(), e, true))
		ZGLOG_ERROR(e);
}

void ZGSTSensor::resetWarnCalc()
{
    ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::updatePropertyValuesEx(m_id, {
		{CALC_YC_PL_WARN_SUM_D1, ""}, {CALC_YC_PL_WARN_SUM_D2, ""}, {CALC_YC_PL_WARN_SUM_D3, ""}, {CALC_YC_PL_WARN_SUM_D4, ""},
		{CALC_YC_PL_WARN_SUM_D5, ""}, {CALC_YC_PL_WARN_SUM_D6, ""}, {CALC_YC_PL_WARN_SUM_D7, ""}, {CALC_YX_PL_SIGNAL, "1"}
        }, e, true))
	{
	    ZGLOG_ERROR(e);
		return;
	}
	m_warnIndex = 1;
}

bool ZGSTSensor::isSameM30DateTime(const QDateTime& dt1, const QDateTime& dt2)
{
	return (dt1.date() == dt2.date() && dt1.time().hour() == dt2.time().hour()
		&& (dt1.time().minute() / 30 == dt2.time().minute() / 30));
}

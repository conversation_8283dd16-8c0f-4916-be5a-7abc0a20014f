#ifndef ZGSTCDCBRANCH_H
#define ZGSTCDCBRANCH_H

#include "ZGSTEndPoint.h"

/** @brief	排流柜支路 */
class ZGSTSensor;
class ZGSTCDCBranch : public ZGSTEndPoint
{
	Q_OBJECT
public:
    explicit ZGSTCDCBranch(const std::string& id, QObject *parent = nullptr);
	void addSensor(ZGSTSensor* sensor) { m_lstSensor.push_back(sensor); }

public:
	bool initialize() override;
	void onNewDay();

private:
	void initDeviceProcessor();
	bool initPLGDevice();
    void initialValue();
	void calcPLSum(const ZG6000::StringMap& _property);
	void calcCurrentMax(const ZG6000::StringMap& _property);

private:
	std::vector<ZGSTSensor*> m_lstSensor;
	std::string m_plgDeviceID;
};

#endif // ZGSTCDCBRANCH_H

#ifndef ZG6000_ZGSTSTRAYDEVICEMNG_H
#define ZG6000_ZGSTSTRAYDEVICEMNG_H

#include <QObject>
#include <ZGServerCommon.h>

class ZGRedisClient;
class ZGSTDevice;
namespace ZG6000 {
class ZGSTStrayDeviceMng : public QObject
{
    Q_OBJECT
public:
    static ZGSTStrayDeviceMng* instance();
    void init();
    bool checkState();
    bool getAllStations(ListStringMap &listMapStation, ErrorInfo &e);
    bool getSensorByStation(std::string stationID, ListStringMap &listMapSensor, ErrorInfo &e);
    bool getDataBySensor(std::string sensorID, ListStringMap &listData, ErrorInfo &e);
    bool getDataBySensors(StringList listSensor, ListStringMap &listData, ErrorInfo &e);
    void resetCalculation();
    bool sendYs(std::string clientID, std::string deviceID, std::string propertyName, std::string propertyValue, bool automatic, ErrorInfo &e);
    bool sendYkCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName, const std::string& value,
        bool automatic, ZG6000::ErrorInfo& e);
    bool sendYsCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName,
        const std::string& value, bool automatic, ZG6000::ErrorInfo& e);
    bool sendCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName,
        const std::string& value, bool automatic, ZG6000::ErrorInfo& e);

private:
    explicit ZGSTStrayDeviceMng(QObject *parent = nullptr);
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initRedisClient();
    bool initStrayDevice();

private:
    bool m_initialized{ false };
    QString m_serverName{ "" };
    QString m_instName{ "" };
    int m_initInterval{ 10 };
    ZGRedisClient* m_pRedisClient{nullptr};
    ZGSTDevice* m_pDevice{ nullptr };
};

inline static ZGSTStrayDeviceMng* g_pInstance = nullptr;

} // namespace ZG6000

#endif // ZG6000_ZGSTSTRAYDEVICEMNG_H

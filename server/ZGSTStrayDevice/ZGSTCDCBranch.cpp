#include "ZGSTCDCBranch.h"

#include "ZGSTSensor.h"
#include "ZGSTStrayDefine.h"
#include "ZGUtils.h"

ZGSTCDCBranch::ZGSTCDCBranch(const std::string& id, QObject* parent)
	: ZGSTEndPoint(id, parent)
{
}

bool ZGSTCDCBranch::initialize()
{
    initDeviceProcessor();
	return true;
}

void ZGSTCDCBranch::onNewDay()
{
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::updatePropertyValues(m_id, {{CALC_YC_PL_SUM_D1, "0"}, {CALC_YC_PL_MAX_D1, "0"}, {CALC_TEXT_PL_MAXTIME_D1, "0"}}, e, true))
        ZGLOG_ERROR(e);
}

void ZGSTCDCBranch::initDeviceProcessor()
{
	m_mapProcessor.insert({
		PL_YX_QF, [this](auto&& ph)
		{
			calcPLSum(std::forward<decltype(ph)>(ph));
		}
	});
	m_mapProcessor.insert({
		PL_YC_DLZ, [this](auto&& ph)
		{
			calcCurrentMax(std::forward<decltype(ph)>(ph));
		}
                          });
}

bool ZGSTCDCBranch::initPLGDevice()
{
	std::string tableName;
	std::string dataID;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getDataIDByProperty(m_id, PL_YX_QF, tableName, dataID, e))
    {
        ZGLOG_ERROR(e);
		return false;
    }
	std::string datasetID;
	if (!ZGProxyCommon::getDataByField(tableName, dataID, "datasetID", datasetID))
		return false;
	QString sql = QString("SELECT id FROM mp_param_device WHERE datasetID = '%1'").arg(datasetID.c_str());
	if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), m_plgDeviceID))
		return false;
	return true;
}

void ZGSTCDCBranch::initialValue()
{
    QDateTime currTime = QDateTime::currentDateTime();
    resetPropertyValues({{CALC_YC_PL_SUM_D1, ttDay}, {CALC_YC_PL_MAX_D1, ttDay}}, currTime);
}

void ZGSTCDCBranch::calcPLSum(const ZG6000::StringMap& _property)
{
	auto pair = _property.find("rtNewValue");
	if (pair == _property.end())
		return;
	if (pair->second == "2")
	{
		std::string sumValue;
        ZG6000::ErrorInfo e;
        if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YC_PL_SUM_D1, sumValue, e))
        {
            ZGLOG_ERROR(e);
			return;
        }
		int value = std::atoi(sumValue.c_str());
        ++value;
		sumValue = std::to_string(value);
        if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YC_PL_SUM_D1, sumValue, e, true))
            ZGLOG_ERROR(e);
	}
}

void ZGSTCDCBranch::calcCurrentMax(const ZG6000::StringMap& _property)
{
	auto pair = _property.find("rtNewValue");
	if (pair == _property.end())
		return;
	double currentValue = std::atof(pair->second.c_str());
	std::string maxValue;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YC_PL_MAX_D1, maxValue, e))
    {
        ZGLOG_ERROR(e);
		return;
    }
	double lastMaxValue = std::atof(maxValue.c_str());
	if (currentValue > lastMaxValue)
	{
		maxValue = QString::number(currentValue, 'f', 2).toStdString();
        std::string currTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        if (!ZGProxyCommon::updatePropertyValues(m_id, {{CALC_YC_PL_MAX_D1, maxValue}, {CALC_TEXT_PL_MAXTIME_D1, currTime}}, e, true))
            ZGLOG_ERROR(e);
	}
}

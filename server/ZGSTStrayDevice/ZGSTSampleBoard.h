#ifndef ZGSTSAMPLEBOARD_H
#define ZGSTSAMPLEBOARD_H

#include "ZGSTEndPoint.h"

class ZGSTSampleBoard : public ZGSTEndPoint
{
    Q_OBJECT
public:
    explicit ZGSTSampleBoard(const std::string& deviceID, QObject* parent = nullptr);
    bool initialize() override;
    void onNewMinute();

private:
    void initProcessor();
    bool initialValue();
    bool updateCommState(const std::string& state);
    void calcCommState(const ZG6000::StringMap& _property);
};

#endif // ZGSTSAMPLEBOARD_H

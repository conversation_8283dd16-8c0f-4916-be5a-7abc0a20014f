#include "ZGSTEndPoint.h"
#include "ZGUtils.h"

ZGSTEndPoint::ZGSTEndPoint(const std::string& id, QObject *parent)
    : QObject{parent},
	m_id(id)
{

}

void ZGSTEndPoint::processPropertyChange(const ZG6000::MapStringMap& _properties)
{
	for (const auto & property : _properties)
	{
		auto pair = m_mapProcessor.find(property.first);
		if (pair != m_mapProcessor.end())
			pair->second(property.second);
    }
}

void ZGSTEndPoint::resetPropertyValues(const std::vector<std::pair<std::string, ZGSTEndPoint::TimeType>>& listNameType, const QDateTime& currTime)
{
    ZG6000::StringList listName;
    std::vector<TimeType> listTimeType;
    for (const auto & [name, timeType] : listNameType)
    {
        bool exist;
        ZG6000::ErrorInfo e;
        if (!ZGProxyCommon::isPropertyExists(m_id, name, exist, e))
            continue;
        if (!exist)
            continue;
        listName.push_back(name);
        listTimeType.push_back(timeType);
    }
    ZG6000::MapStringMap properties;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getProperties(m_id, listName, properties, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    ZG6000::StringMap updateValues;
    for (size_t i = 0; i < listName.size(); ++i)
    {
        const auto & property = properties[listName[i]];
        const auto & rtUpdateTime = ZGUtils::get(property, "rtUpdateTime");
        QDateTime lastTime;
        if (ZGUtils::StringToDateTime(rtUpdateTime.c_str(), lastTime))
        {
            switch (listTimeType[i])
            {
            case ttBt:
            case ttDay:
                if (currTime.date().day() == lastTime.date().day())
                    continue;
            case ttHour:
                if (currTime.date().day() == lastTime.date().day() &&
                    currTime.time().hour() == lastTime.time().hour())
                    continue;
            case ttM30:
                if (currTime.date().day() == lastTime.date().day() &&
                    currTime.time().hour() == lastTime.time().hour() &&
                    ((currTime.time().minute() / 30) == (lastTime.time().minute() / 30)))
                    continue;
            }
            updateValues[listName[i]] = "0";
        }
    }
    if (!ZGProxyCommon::updatePropertyValuesEx(m_id, updateValues, e, true))
        ZGLOG_ERROR(e);
    // if (!ZGProxyCommon::updatePropertyValues(m_id, updateValues, e, true))
    //     ZGLOG_ERROR(e);
}

void ZGSTEndPoint::calcH1Avg(const std::string& totalProp, const std::string& subTotalProp, const std::string& countProp, const std::string& subCountProp, const std::string& avgProp)
{
    ZG6000::StringMap values;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValues(m_id, {subTotalProp, subCountProp}, values, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    double newTotal = std::atof(m_cacheData[totalProp].c_str()) + std::atof(values[subTotalProp].c_str());
    int oldCount = std::atoi(values[subCountProp].c_str());
    if (oldCount == 0)
        return;
    int newCount = std::atoi(m_cacheData[countProp].c_str()) + oldCount;
    QString val = QString::number(newTotal, 'f', 2);
    m_cacheData[totalProp] = val.toStdString();
    m_cacheData[countProp] = std::to_string(newCount);
    if (newCount == 0)
    {
        ZGLOG_DEBUG(QStringLiteral("传感器'%1'属性'%2'的当前值为0, 不能计算平均值").arg(m_id.c_str()).arg(countProp.c_str()));
        return;
    }
    double avgVal = newTotal / newCount;
    val = QString::number(avgVal, 'f', 2);
    if (!ZGProxyCommon::updatePropertyValue(m_id, avgProp, val.toStdString(), e, true))
        ZGLOG_ERROR(e);
}

void ZGSTEndPoint::calcDayAvg(const std::string& totalProp, const std::string& newValue, const std::string& countProp, const std::string& avgProp)
{
    QTime currTime = QTime::currentTime();
    if (currTime >= operStartTime() && currTime <= operEndTime())
    {
        ZG6000::StringMap values;
        ZG6000::ErrorInfo e;
        if (!ZGProxyCommon::getPropertyValues(m_id, {totalProp, countProp}, values, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
        double newTotalValue = std::atof(values[totalProp].c_str()) + std::atof(newValue.c_str());
        QString val = QString::number(newTotalValue, 'f', 2);
        int newCount = std::atoi(values[countProp].c_str()) + 1;
        if (!ZGProxyCommon::updatePropertyValues(m_id, {{totalProp, val.toStdString()}, {countProp, std::to_string(newCount)}}, e, true))
            ZGLOG_ERROR(e);
        if (newCount == 0)
        {
            ZGLOG_DEBUG(QStringLiteral("设备'%1'属性'%2'的当前值为0, 不能计算平均值").arg(m_id.c_str()).arg(countProp.c_str()));
            return;
        }
        double avgVal = newTotalValue / newCount;
        val = QString::number(avgVal, 'f', 2);
        if (!ZGProxyCommon::updatePropertyValue(m_id, avgProp, val.toStdString(), e, true))
            ZGLOG_ERROR(e);
    }
}

void ZGSTEndPoint::calcMax(const std::string &maxProp, const std::string &subMaxProp)
{
    std::string newMaxVal;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(m_id, subMaxProp, newMaxVal, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    double newVal = std::atof(newMaxVal.c_str());
    calcMax(maxProp, newVal);
}

void ZGSTEndPoint::calcDayAvg(const std::string& totalProp, const std::string& subTotalProp, const std::string& countProp, const std::string& subCountProp, const std::string& avgProp)
{
    QTime currTime = QTime::currentTime();
    if (currTime >= operStartTime() && currTime <= operEndTime())
    {
        ZG6000::StringMap values;
        ZG6000::ErrorInfo e;
        if (!ZGProxyCommon::getPropertyValues(m_id, {subTotalProp, totalProp, countProp, subCountProp}, values, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
        double newTotal = std::atof(values[totalProp].c_str()) + std::atof(values[subTotalProp].c_str());
        int oldCount = std::atoi(values[subCountProp].c_str());
        if (oldCount == 0)
            return;
        int newCount = std::atoi(values[countProp].c_str()) + oldCount;
        if (newCount > 0 && newCount < 100)
        {
            ZGLOG_INFO(QString("oldCount: '%1', addCount: '%2'").arg(values[countProp].c_str()).arg(values[subCountProp].c_str()));
        }
        QString val = QString::number(newTotal, 'f', 2);
        if (!ZGProxyCommon::updatePropertyValues(m_id, {{totalProp, val.toStdString()}, {countProp, std::to_string(newCount)}}, e, true))
            ZGLOG_ERROR(e);
        if (newCount == 0)
        {
            ZGLOG_DEBUG(QStringLiteral("设备'%1'属性'%2'的当前值为0, 不能计算平均值").arg(m_id.c_str()).arg(countProp.c_str()));
            return;
        }
        double avgVal = newTotal / newCount;
        val = QString::number(avgVal, 'f', 2);
        if (!ZGProxyCommon::updatePropertyValue(m_id, avgProp, val.toStdString(), e, true))
            ZGLOG_ERROR(e);
    }
}

void ZGSTEndPoint::calcMax(const std::string& maxProp, double newVal)
{
    std::string oldMaxVal;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(m_id, maxProp, oldMaxVal, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    double oldVal = std::atof(oldMaxVal.c_str());
    if (std::abs(newVal) > std::abs(oldVal))
    {
        QString val = QString::number(newVal, 'f', 2);
        if (!ZGProxyCommon::updatePropertyValue(m_id, maxProp, val.toStdString(), e, true))
            ZGLOG_ERROR(e);
    }
}

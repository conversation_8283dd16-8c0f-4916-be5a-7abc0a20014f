#ifndef ZGSTDEVICE_H
#define ZGSTDEVICE_H

#include <QObject>
#include <QTimer>
#include <QMutex>
#include "ZGSTEndPoint.h"

class ZGRedisClient;
class ZGSTSensor;
class ZGSTCDCBranch;
class ZGSTSampleBoard;
class ZGSTDevice : public ZGSTEndPoint
{
    Q_OBJECT
public:
    explicit ZGSTDevice(const std::string& deviceID, QObject* parent = nullptr);
    bool initialize() override;
    bool getDataBySensor(const std::string& sensorID, ZG6000::ListStringMap& listData, ZG6000::ErrorInfo& e);
    bool getDataBySensors(const ZG6000::StringList& listSensor, ZG6000::ListStringMap& listData, ZG6000::ErrorInfo& e);
    const QTime& btCalcStartTime() const { return m_btCalcStartTime; }
    QTime operStartTime() override;
    QTime operEndTime() override;
    void resetCalculation();
    void onNewMinute30();
    void onNewHour();
    void onNewDay();

private:
    void initProcessor();
    bool initialValue();
    void initTimer();
    bool initSensor();
    bool initCDCBranch();
    bool initSampleBoard();
    bool initBtCalcTime();
    bool initOperTime();
    bool initRedisRtTopic();
    bool initGPIODataset();
    void calcGDDY(const ZG6000::StringMap& _property);
    void updateOperStartTime(const ZG6000::StringMap& _property);
    void updateOperEndTime(const ZG6000::StringMap& _property);
    void updateCalcStartTime(const ZG6000::StringMap& _property);
    void updateCalcEndTime(const ZG6000::StringMap& _property);
    inline bool isNewM1Stat(const QDateTime& currentTime);
    inline bool isNewM30Stat(const QDateTime& currentTime);
    inline bool isNewH1Stat(const QDateTime& currentTime);
    inline bool isNewD1Stat(const QDateTime& currentTime);
    inline bool isNewBtStat(const QDateTime& currentTime);

private slots:
    void onCheckTime();
    void onReceivedMessage(const QString& topic, const QString& message);

private:
    std::unordered_map<std::string, ZGSTSensor*> m_mapSensor{};
    std::unordered_map<std::string, ZGSTCDCBranch*> m_mapCDCBranch{};
    std::unordered_map<std::string, ZGSTSampleBoard*> m_mapSampleBoard{};
    std::vector<double> m_cacheValue;
    std::string m_gpioDatasetID;
    QTimer m_checkTimer;
    QTime m_btCalcStartTime{ 3, 30 };
    QTime m_btCalcEndTime{ 4, 0 };
    QTime m_operStartTime{ 5, 30 };
    QTime m_operEndTime{ 23, 0 };
    QDateTime m_lastDateTime;
    ZGRedisClient* m_pRedisClient{ nullptr };
    QMutex m_mutex;
};

#endif // ZGSTDEVICE_H

#ifndef ZGSTENDPOINT_H
#define ZGSTENDPOINT_H

#include <QObject>
#include "ZGProxyCommon.h"

class ZGSTEndPoint : public QObject
{
    Q_OBJECT
public:
    explicit ZGSTEndPoint(const std::string& id, QObject *parent = nullptr);
    void processPropertyChange(const ZG6000::MapStringMap& _properties);
    virtual bool initialize() { return true; }
    const std::string& id() { return m_id; }
    enum TimeType { ttDay, ttHour, ttM30, ttBt };
    void resetPropertyValues(const std::vector<std::pair<std::string, TimeType>>& listNameType, const QDateTime& currTime);
    virtual QTime operStartTime() {return QTime();}
    virtual QTime operEndTime() {return QTime();}
    void calcH1Avg(const std::string& totalProp, const std::string& subTotalProp, const std::string& countProp,
                           const std::string& subCountProp, const std::string& avgProp);
    void calcDayAvg(const std::string& totalProp, const std::string& subTotalProp, const std::string& countProp,
        const std::string& subCountProp, const std::string& avgProp);
    void calcDayAvg(const std::string& totalProp, const std::string& newValue, const std::string& countProp, const std::string& avgProp);
    void calcMax(const std::string& maxProp, const std::string& subMaxProp);
    void calcMax(const std::string& maxProp, double newVal);

protected:
    std::string m_id;
    std::unordered_map<std::string, std::string> m_cacheData;
    std::unordered_map<std::string, std::function<void(const ZG6000::StringMap&)>> m_mapProcessor;
};

#endif // ZGSTENDPOINT_H

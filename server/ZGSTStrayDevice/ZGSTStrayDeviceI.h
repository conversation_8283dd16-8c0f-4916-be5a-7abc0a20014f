#ifndef ZG6000_ZGSTSTRAYDEVICEI_H
#define ZG6000_ZGSTSTRAYDEVICEI_H

#include <ZGSTStrayDevice.h>

namespace ZG6000 {

class ZGSTStrayDeviceI : public ZG6000::ZGSTStrayDevice
{
public:
    ZGSTStrayDeviceI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;

    // ZGSTStrayDevice interface
public:
    bool getAllStations(ListStringMap &listMapStation, ErrorInfo &e, const Ice::Current &current) override;
    bool getSensorByStation(std::string stationID, ListStringMap &listMapSensor, ErrorInfo &e, const Ice::Current &current) override;
    bool getDataBySensor(std::string sensorID, ListStringMap &listData, ErrorInfo &e, const Ice::Current &current) override;
    bool getDataBySensors(StringList listSensor, ListStringMap &listData, ErrorInfo &e, const Ice::Current &current) override;
    bool sendYs(std::string clientID, std::string deviceID, std::string propertyName, std::string propertyValue, bool automatic, ErrorInfo &e, const Ice::Current &current) override;
    void resetCalculation(const Ice::Current& current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGSTSTRAYDEVICEI_H

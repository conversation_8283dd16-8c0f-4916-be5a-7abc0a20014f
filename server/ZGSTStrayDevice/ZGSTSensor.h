#ifndef ZGSTSENSOR_H
#define ZGSTSENSOR_H

#include <QObject>
#include <QReadWriteLock>
#include <QMutexLocker>
#include "ZGSTEndPoint.h"

class ZGSTSensor : public ZGSTEndPoint
{
	Q_OBJECT
public:
	explicit ZGSTSensor(const std::string& sensorID, QObject* parent = nullptr);
	bool initialize() override;
    void onNewMinute();
	void onNewMinute30();

	void onNewHour();
	void onNewDay();
	void onNewBtTime();
	bool plSignal() const;
	void resetCalculation();
    QTime operStartTime() override;
    QTime operEndTime() override;

private:
	void initProcessor();
	bool initialValue();
	void calcZXA(const ZG6000::StringMap& _property);
	void calcZXB(const ZG6000::StringMap& _property);
	void calcJHPY(const ZG6000::StringMap& _property);
	void calcJHPYStat(const ZG6000::StringMap& _property);
	void calcGDDY(const ZG6000::StringMap& _property);
	void calcGJDY(const ZG6000::StringMap& _property);
	void calcJDDY(const ZG6000::StringMap& _property);
	void calcCommState(const ZG6000::StringMap& _property);
    void calcZXPLWarn(const ZG6000::StringMap& _property);
    void calcFXPLWarn(const ZG6000::StringMap& _property);
	inline void calcOffset(const std::string& volProp, const std::string& btProp, const std::string& offsetProp);
	void calcCurrent(const std::string& volVal, const std::string& resProp, const std::string& currentProp);
	void resetWarnCalc();
	bool isSameM30DateTime(const QDateTime& dt1, const QDateTime& dt2);

private:
	std::vector<double> m_cacheValue;
	QReadWriteLock m_lock;
    QMutex m_mutex;
	int m_warnIndex{ -1 };
};

#endif // ZGSTSENSOR_H

#include "ZGSTStrayDeviceI.h"
#include "ZGSTStrayDeviceMng.h"

namespace ZG6000 {

ZGSTStrayDeviceI::ZGSTStrayDeviceI()
{
    ZGSTStrayDeviceMng::instance()->init();
}

bool ZGSTStrayDeviceI::checkState(const Ice::Current &current)
{
    return ZGSTStrayDeviceMng::instance()->checkState();
}

bool ZGSTStrayDeviceI::getAllStations(ListStringMap &listMapStation, ErrorInfo &e, const Ice::Current &current)
{
    return ZGSTStrayDeviceMng::instance()->getAllStations(listMapStation, e);
}

bool ZGSTStrayDeviceI::getSensorByStation(std::string stationID, ListStringMap &listMapSensor, ErrorInfo &e, const Ice::Current &current)
{
    return ZGSTStrayDeviceMng::instance()->getSensorByStation(std::move(stationID), listMapSensor, e);
}

bool ZGSTStrayDeviceI::getDataBySensor(std::string sensorID, ListStringMap &listData, ErrorInfo &e, const Ice::Current &current)
{
    return ZGSTStrayDeviceMng::instance()->getDataBySensor(std::move(sensorID), listData, e);
}

bool ZGSTStrayDeviceI::getDataBySensors(StringList listSensor, ListStringMap &listData, ErrorInfo &e, const Ice::Current &current)
{
    return ZGSTStrayDeviceMng::instance()->getDataBySensors(std::move(listSensor), listData, e);
}

bool ZGSTStrayDeviceI::sendYs(std::string clientID, std::string deviceID, std::string propertyName, std::string propertyValue, bool automatic, ErrorInfo &e, const Ice::Current &current)
{
    return ZGSTStrayDeviceMng::instance()->sendYs(std::move(clientID), std::move(deviceID), std::move(propertyName), std::move(propertyValue), automatic, e);
}

void ZGSTStrayDeviceI::resetCalculation(const Ice::Current& current)
{
    ZGSTStrayDeviceMng::instance()->resetCalculation();
}
} // namespace ZG6000

#include "ZGSTStrayDeviceMng.h"

#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QThread>

#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGSTDevice.h"
#include "ZGUtils.h"
#include "redis/ZGRedisClient.h"
#include "zgerror/ZGSTStrayDeviceError.h"

namespace ZG6000 {

ZGSTStrayDeviceMng *ZGSTStrayDeviceMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGSTStrayDeviceMng;
    return g_pInstance;
}

void ZGSTStrayDeviceMng::init()
{
	initServerInstConfig();
	while (!initRedisClient())
	{
		ZGLOG_ERROR("initRedisRtQueue error.");
		QThread::msleep(m_initInterval * 1000);
	}
	while (!initStrayDevice())
	{
		ZGLOG_ERROR("initStrayDevice error.");
		QThread::msleep(m_initInterval * 1000);
	}
	m_initialized = true;
}

bool ZGSTStrayDeviceMng::checkState()
{
    return m_initialized;
}

bool ZGSTStrayDeviceMng::getAllStations(ListStringMap& listMapStation, ErrorInfo& e)
{
	std::string sql = "SELECT id, name FROM sp_param_appnode";
	if (!ZGProxyCommon::execQuerySql(sql, listMapStation))
	{
		e = ZGRuntime::instance()->getErrorInfo(::ZGSTStrayDevice::ZG_ERR_DB);
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGSTStrayDeviceMng::getSensorByStation(std::string stationID, ListStringMap& listMapSensor, ErrorInfo& e)
{
	std::string sql =
        "SELECT id, name, typeID, subtypeID, datasetID, isEnable FROM mp_param_device WHERE (typeID = 'ZG_DT_ZS_SENSOR') AND appNodeID = '"
		+ stationID + "' ORDER BY id";
	if (!ZGProxyCommon::execQuerySql(sql, listMapSensor))
	{
		e = ZGRuntime::instance()->getErrorInfo(::ZGSTStrayDevice::ZG_ERR_DB);
		ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

bool ZGSTStrayDeviceMng::getDataBySensor(std::string sensorID, ListStringMap& listData, ErrorInfo& e)
{
	return m_pDevice->getDataBySensor(sensorID, listData, e);
}

bool ZGSTStrayDeviceMng::getDataBySensors(StringList listSensor, ListStringMap& listData, ErrorInfo& e)
{
	return m_pDevice->getDataBySensors(listSensor, listData, e);
}

void ZGSTStrayDeviceMng::resetCalculation()
{
    m_pDevice->resetCalculation();
}

bool ZGSTStrayDeviceMng::sendYs(std::string clientID, std::string deviceID, std::string propertyName, std::string propertyValue, bool automatic, ErrorInfo& e)
{
	return sendYsCommand(clientID, deviceID, propertyName, propertyValue, automatic, e);
}

ZGSTStrayDeviceMng::ZGSTStrayDeviceMng(QObject *parent)
    : QObject{parent}
{

}

void ZGSTStrayDeviceMng::initServerInstConfig()
{
	const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
	QString errMsg;
	int value;
	if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
		ZGLOG_WARN(errMsg);
	else
		m_initInterval = value;
}

bool ZGSTStrayDeviceMng::initServerInstInfo()
{
	m_serverName = ZGRuntime::instance()->getServerID();
	if (m_serverName.isEmpty())
	{
		ZGLOG_ERROR("Empty server id.");
		return false;
	}
	m_instName = ZGRuntime::instance()->getInstanceID();
	if (m_instName.isEmpty())
	{
		ZGLOG_ERROR("Empty server instance id.");
		return false;
	}
	return true;
}

bool ZGSTStrayDeviceMng::initRedisClient()
{
	QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
	listClientType << ZGRuntime::REDIS_RT_QUEUE;
	if (!ZGRuntime::instance()->initRedisClient(listClientType))
	{
		ZGLOG_ERROR("Init redis client error.");
		return false;
	}
	m_pRedisClient = ZGRuntime::instance()->getRedisClientRTQueue();
	if (m_pRedisClient == nullptr)
	{
		ZGLOG_ERROR("getRedisClientRTQueue error.");
		return false;
	}
	return true;
}

bool ZGSTStrayDeviceMng::initStrayDevice()
{
	if (m_pDevice == nullptr)
	{
		std::string sql = "SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_STRAY_DEV'";
		ZG6000::StringList listID;
		if (!ZGProxyCommon::execQuerySqlCol(sql, listID))
		{
			ZGLOG_ERROR(QStringLiteral("获取杂散装置本体设备失败"));
			return false;
		}
		if (listID.empty())
		{
			ZGLOG_ERROR(QStringLiteral("未配置杂散装置本体设备"));
			return false;
		}
		m_pDevice = new ZGSTDevice(listID[0], this);
	}
	return m_pDevice->initialize();
}

bool ZGSTStrayDeviceMng::sendYkCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName, const std::string& value, bool automatic, ZG6000::ErrorInfo& e)
{
	return sendCommand(clientID, deviceID, propertyName, value, automatic, e);
}

bool ZGSTStrayDeviceMng::sendYsCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName, const std::string& value, bool automatic, ZG6000::ErrorInfo& e)
{
	return sendCommand(clientID, deviceID, propertyName, value, automatic, e);
}

bool ZGSTStrayDeviceMng::sendCommand(const std::string& clientID, const std::string& deviceID, const std::string& propertyName, const std::string& value, bool automatic, ZG6000::ErrorInfo& e)
{
	std::string tableName;
	std::string dataID;
    if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
	{
		ZGLOG_ERROR(e);
		return false;
	}
	QJsonObject command;
	command["id"] = dataID.c_str();
	std::string modelTable;
	if (tableName == "mp_param_dataset_yk")
		modelTable = "mp_param_model_yk";
	if (tableName == "mp_param_dataset_ys")
		modelTable = "mp_param_model_ys";
	std::string sql =
		"SELECT a.isSelectCtrl FROM " + modelTable + " a LEFT JOIN " + tableName + " b ON b.dataModelID = a.id "
		"WHERE b.id = '" + dataID + "'";
	std::string result;
	if (!ZGProxyCommon::execQuerySqlField(sql, result))
	{
		ZGLOG_ERROR("execQuerySqlField error.");
		return false;
	}
	QString commandID;
	if (tableName == "mp_param_dataset_yk")
		commandID = "ZG_DC_YK_EXEC";
	if (tableName == "mp_param_dataset_ys")
		commandID = "ZG_DC_YS_EXEC";
	if (result == "1")
	{
		if (tableName == "mp_param_dataset_yk")
			commandID = "ZG_DC_YK_SELECT";
		if (tableName == "mp_param_dataset_ys")
			commandID = "ZG_DC_YS_SELECT";
	}
	command["commandID"] = commandID;
	command["isReturnValue"] = "0";
	if (automatic)
	{
		command["srcType"] = "auto";
		command["srcID"] = "-1";
	}
	else
	{
		command["srcType"] = "client";
		command["srcID"] = clientID.c_str();
	}
	command["rtCode"] = QString::number(ZGUtils::genNumber(0, 10000));
	command["rtValue"] = value.c_str();
	command["rtCommandTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
	QJsonArray array;
	array.append(command);
	QJsonDocument doc(array);
	long long size;
	QString errMsg;
	std::string topicName;
	if (tableName == "mp_param_dataset_yk")
		topicName = "ZG_Q_SYSTEM_YK";
	if (tableName == "mp_param_dataset_ys")
		topicName = "ZG_Q_SYSTEM_YS";
	ZGLOG_DEBUG(doc.toJson());
	if (!m_pRedisClient->rpush(topicName.c_str(), doc.toJson(), size, errMsg))
	{
		ZGLOG_ERROR("Send command to ys queue error.");
	}
	return true;
}
} // namespace ZG6000

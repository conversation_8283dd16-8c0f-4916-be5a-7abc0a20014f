#include "ZGSTDevice.h"

#include "ZGSTStrayDefine.h"
#include "ZGRuntime.h"
#include "ZGProxyCommon.h"
#include "ZGSTSensor.h"
#include "ZGSTCDCBranch.h"
#include "ZGSTSampleBoard.h"
#include "ZGUtils.h"
#include "zgerror/ZGSTStrayDeviceError.h"
#include <QtConcurrent>
#include <ZGJson.h>
#include <QMutexLocker>


ZGSTDevice::ZGSTDevice(const std::string& deviceID, QObject* parent)
    : ZGSTEndPoint(deviceID, parent)
{
    
}

bool ZGSTDevice::initialize()
{
    initProcessor();
    if (!initBtCalcTime())
        return false;
    if (!initOperTime())
        return false;
    if (!initSensor())
        return false;
    if (!initCDCBranch())
        return false;
    if (!initSampleBoard())
        return false;
    if (!initGPIODataset())
        return false;
    if (!initRedisRtTopic())
    {
        ZGLOG_ERROR(QStringLiteral("初始化Redis实时主题失败"));
        return false;
    }
    initTimer();
    return initialValue();
}

bool ZGSTDevice::getDataBySensor(const std::string& sensorID, ZG6000::ListStringMap& listData, ZG6000::ErrorInfo& e)
{
    ZG6000::StringList listSensorID{ sensorID };
    return getDataBySensors(listSensorID, listData, e);
}

bool ZGSTDevice::getDataBySensors(const ZG6000::StringList& listSensor, ZG6000::ListStringMap& listData, ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_CYB'");
    ZG6000::StringList listBoardID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listBoardID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSTStrayDevice::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取采样板信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    for (const auto& sensorID: listSensor)
    {
        if (m_mapSensor.find(sensorID) == m_mapSensor.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSTStrayDevice::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("'%1'不是一个有效的传感器").arg(sensorID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
    }
    ZG6000::MapStringMap mapValues;
    ZG6000::StringList listProperty;
    if (listBoardID.empty())
    {
        listProperty = ZG6000::StringList{ST_YC_GDDY, ST_YC_GJDY, ST_YC_JDDY, ST_YC_JHDW,
                    CALC_YC_BTDW, CALC_YC_ZXPY_AVG_H1, CALC_YX_COMM_STATE, CALC_YX_UC_WARN,
                    CALC_YX_JHPY_P_WARN, CALC_YX_JHPY_N_WARN};
    }
    else
    {
        listProperty = ZG6000::StringList{ST_YC_JHDW, CALC_YC_BTDW, CALC_YC_ZXPY_AVG_H1, CALC_YX_UC_WARN,
                CALC_YX_COMM_STATE, CALC_YX_JHPY_P_WARN, CALC_YX_JHPY_N_WARN};
    }
    if (!ZGProxyCommon::mgetPropertyValuesEx(listSensor, listProperty, mapValues, e))
        return false;
    for (const auto& sensorID: listSensor)
    {
        listData.push_back(std::move(mapValues[sensorID]));
    }
    return true;
}

QTime ZGSTDevice::operStartTime()
{
    return m_operStartTime;
}

QTime ZGSTDevice::operEndTime()
{
    return m_operEndTime;
}

void ZGSTDevice::resetCalculation()
{
    for (const auto& [_, sensor] : m_mapSensor)
    {
        sensor->resetCalculation();
    }
}

void ZGSTDevice::onNewMinute30()
{
}

void ZGSTDevice::onNewHour()
{
    ZG6000::StringMap values;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValuesEx(m_id, {CALC_YC_ZXGD_MAX_H1_R, CALC_YC_ZXGD_AVG_H1_R, CALC_YC_FXGD_MAX_H1_R,
            CALC_YC_FXGD_AVG_H1_R}, values, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    ZG6000::StringMap updateValues{
                        {CALC_YC_ZXGD_MAX_H1, values[CALC_YC_ZXGD_MAX_H1_R]}, {CALC_YC_ZXGD_AVG_H1, values[CALC_YC_ZXGD_AVG_H1_R]},
                        {CALC_YC_FXGD_MAX_H1, values[CALC_YC_FXGD_MAX_H1_R]}, {CALC_YC_FXGD_AVG_H1, values[CALC_YC_FXGD_AVG_H1_R]},
                    {CALC_YC_ZXGD_MAX_H1_R, "0"}, {CALC_YC_ZXGD_AVG_H1_R, "0"}, {CALC_YC_FXGD_MAX_H1_R, "0"}, {CALC_YC_FXGD_AVG_H1_R, "0"}};
    if (!ZGProxyCommon::updatePropertyValuesEx(m_id, updateValues, e, true))
    {
        ZGLOG_ERROR(e);
    }
    m_cacheData[CALC_YC_ZXGD_SUM_H1_R] = "0";
    m_cacheData[CALC_YC_ZXGD_COUNT_H1_R] = "0";
    m_cacheData[CALC_YC_FXGD_SUM_H1_R] = "0";
    m_cacheData[CALC_YC_FXGD_COUNT_H1_R] = "0";
}

void ZGSTDevice::onNewDay()
{
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::updatePropertyValuesEx(m_id, {
        {CALC_YC_ZXGD_SUM_D1, "0"}, {CALC_YC_ZXGD_COUNT_D1, "0"}, {CALC_YC_ZXGD_AVG_D1, "0"}, {CALC_YC_ZXGD_MAX_D1, "0"},
        {CALC_YC_FXGD_SUM_D1, "0"}, {CALC_YC_FXGD_COUNT_D1, "0"}, {CALC_YC_FXGD_AVG_D1, "0"}, {CALC_YC_FXGD_MAX_D1, "0"}
    }, e, true))
        ZGLOG_ERROR(e);
}

void ZGSTDevice::initProcessor()
{
    m_mapProcessor.insert({
        PARAM_OPER_START, [this](auto&& ph)
        {
            updateOperStartTime(std::forward<decltype(ph)>(ph));
        }
        });
    m_mapProcessor.insert({
        PARAM_OPER_END, [this](auto&& ph)
        {
            updateOperEndTime(std::forward<decltype(ph)>(ph));
        }
        });
    m_mapProcessor.insert({
        PARAM_OPER_END, [this](auto&& ph)
        {
            updateOperEndTime(std::forward<decltype(ph)>(ph));
        }
        });
    m_mapProcessor.insert({
        ST_YC_GDDY_COUNT_M1, [this](auto&& ph)
        {
            calcGDDY(std::forward<decltype(ph)>(ph));
        }
                          });
}

bool ZGSTDevice::initialValue()
{
    QDateTime currTime = QDateTime::currentDateTime();
    resetPropertyValues({{CALC_YC_ZXGD_SUM_D1, ttDay}, {CALC_YC_ZXGD_COUNT_D1, ttDay}, {CALC_YC_ZXGD_AVG_D1, ttDay},
                        {CALC_YC_ZXGD_MAX_D1, ttDay}, {CALC_YC_FXGD_SUM_D1, ttDay}, {CALC_YC_FXGD_COUNT_D1, ttDay},
                        {CALC_YC_FXGD_AVG_D1, ttDay}, {CALC_YC_FXGD_MAX_D1, ttDay}, {CALC_YC_ZXGD_AVG_H1_R, ttM30},
                        {CALC_YC_ZXGD_MAX_H1_R, ttM30}, {CALC_YC_FXGD_AVG_H1_R, ttM30}, {CALC_YC_FXGD_MAX_H1_R, ttM30}},
                        currTime);
    return true;
}

void ZGSTDevice::initTimer()
{
    m_checkTimer.setTimerType(Qt::PreciseTimer);
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSTDevice::onCheckTime);
    m_lastDateTime = QDateTime::currentDateTime();
    m_checkTimer.start(1000);
}

bool ZGSTDevice::initSensor()
{
    std::string sql = "SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_ZS_SENSOR' AND isEnable = '1'";
    ZG6000::StringList listSensorID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listSensorID))
    {
        ZGLOG_ERROR(QStringLiteral("获取传感器设备列表失败"));
        return false;
    }
    for (const auto& sensorId : listSensorID)
    {
        auto sensor = new ZGSTSensor(sensorId, this);
        if (!sensor->initialize())
        {
            delete sensor;
            return false;
        }
        m_mapSensor.insert({ sensorId, sensor });
    }
    return true;
}

bool ZGSTDevice::initCDCBranch()
{
    std::string sql = "SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_DEV_PLGZL' AND isEnable = '1'";
    ZG6000::StringList listCDCBranchID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listCDCBranchID))
    {
        ZGLOG_ERROR(QStringLiteral("获取排流柜支路列表失败"));
        return false;
    }
    for (const auto& cdcBranchId : listCDCBranchID)
    {
        auto cdcBranch = new ZGSTCDCBranch(cdcBranchId, this);
        if (!cdcBranch->initialize())
        {
            delete cdcBranch;
            return false;
        }
        m_mapCDCBranch.insert({ cdcBranchId, cdcBranch });
    }
    return true;
}

bool ZGSTDevice::initSampleBoard()
{
    std::string sql = "SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_CYB' AND isEnable = 1";
    ZG6000::StringList listBoardID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listBoardID))
    {
        ZGLOG_ERROR(QStringLiteral("获取采样板列表失败"));
        return false;
    }
    for (const auto& boardID: listBoardID)
    {
        auto board = new ZGSTSampleBoard(boardID, this);
        if (!board->initialize())
        {
            delete board;
            return false;
        }
        m_mapSampleBoard.insert({ boardID, board });
    }
    return true;
}

bool ZGSTDevice::initBtCalcTime()
{
    ZG6000::StringMap values;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValues(m_id, {PARAM_BTDW_CALC_START, PARAM_BTDW_CALC_END}, values, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    auto calcStart = QTime::fromString(values[PARAM_BTDW_CALC_START].c_str(), "hh:mm:ss");
    if (calcStart.isValid())
        m_btCalcStartTime = calcStart;
    else
        ZGLOG_WARN(QStringLiteral("无效的本体电位开始时间'%1'").arg(values[PARAM_BTDW_CALC_START].c_str()));
    auto calcEnd = QTime::fromString(values[PARAM_BTDW_CALC_END].c_str(), "hh:mm:ss");
    if (calcEnd.isValid())
        m_btCalcEndTime = calcEnd;
    else
        ZGLOG_WARN(QStringLiteral("无效的本体电位结束时间'%1'").arg(values[PARAM_BTDW_CALC_END].c_str()));
    ZGLOG_DEBUG(QString("btStartTime: '%1', endTime: '%2'").arg(m_btCalcStartTime.toString("hh:mm:ss")).arg(m_btCalcEndTime.toString("hh:mm:ss")));
    return true;
}

bool ZGSTDevice::initOperTime()
{
    ZG6000::StringMap values;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValues(m_id, {PARAM_OPER_START, PARAM_OPER_END}, values, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    auto startTime = QTime::fromString(values[PARAM_OPER_START].c_str(), "hh:mm:ss");
    if (startTime.isValid())
        m_operStartTime = startTime;
    else
        ZGLOG_WARN(QStringLiteral("无效的运营开始时间'%1'").arg(values[PARAM_OPER_START].c_str()));
    auto endTime = QTime::fromString(values[PARAM_OPER_END].c_str(), "hh:mm:ss");
    if (endTime.isValid())
        m_operEndTime = endTime;
    else
        ZGLOG_WARN(QStringLiteral("无效的运营结束时间'%1'").arg(values[PARAM_OPER_END].c_str()));
    return true;
}

bool ZGSTDevice::initRedisRtTopic()
{
    QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
    listClientType << ZGRuntime::REDIS_RT_TOPIC;
    if (!ZGRuntime::instance()->initRedisClient(listClientType))
    {
        ZGLOG_ERROR("initRedisClient error.");
        return false;
    }
    m_pRedisClient = ZGRuntime::instance()->getRedisClientRTTopic();
    if (m_pRedisClient == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTTopic failed.");
        return false;
    }
    if (!m_pRedisClient->connected(true))
    {
        ZGLOG_ERROR("Redis RT topic is not connected.");
        return false;
    }
    connect(m_pRedisClient, &ZGRedisClient::receivedMessage, this, &ZGSTDevice::onReceivedMessage);
    ZG6000::StringList listTopic;
    listTopic.push_back("mp_param_device/" + m_id);
    for (const auto& [sensorID, _] : m_mapSensor)
    {
        listTopic.push_back("mp_param_device/" + sensorID);
    }
    for (const auto& [cdcBranchID, _] : m_mapCDCBranch)
    {
        listTopic.push_back("mp_param_device/" + cdcBranchID);
    }
    for (const auto& [boardID, _]: m_mapSampleBoard)
    {
        listTopic.push_back("mp_param_device/" + boardID);
    }
    listTopic.push_back("mp_param_dataset/" + m_gpioDatasetID);
    m_pRedisClient->subscribe(listTopic);
    m_pRedisClient->consume();
    return true;
}

bool ZGSTDevice::initGPIODataset()
{
    QString sql = QString("SELECT id FROM mp_param_dataset WHERE serviceLogicalName = 'ZGSPGPIOServer'");
    ZG6000::StringList listDatasetID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDatasetID))
    {
        ZGLOG_ERROR(QStringLiteral("获取GPIO数据集失败"));
        return false;
    }
    if (!listDatasetID.empty())
    {
        m_gpioDatasetID = listDatasetID[0];
        auto datasetPrx = ZGProxyMng::instance()->getProxyMPDatasetProperty();
        if (datasetPrx != nullptr)
        {
            try
            {
                ZG6000::StringMap values;
                ZG6000::ErrorInfo e;
                if (!datasetPrx->getPropertyValues(m_gpioDatasetID, {"AlarmState", "CommState", "FaultState"}, values, e))
                {
                    ZGLOG_ERROR(e);
                    return false;
                }
                values["RunState"] = "2";
                if (!ZGProxyCommon::updatePropertyValues(m_id, values, e))
                {
                    ZGLOG_ERROR(e);
                    return false;
                }
            }
            catch (const Ice::Exception& e)
            {
                ZGLOG_ERROR(e.what());
                return false;
            }
        }
    }
    return true;
}

void ZGSTDevice::calcGDDY(const ZG6000::StringMap &_property)
{
    QMutexLocker locker(&m_mutex);
    calcH1Avg(CALC_YC_ZXGD_SUM_H1_R, ST_YC_ZXGD_SUM_M1, CALC_YC_ZXGD_COUNT_H1_R, ST_YC_ZXGD_COUNT_M1, CALC_YC_ZXGD_AVG_H1_R);
    calcMax(CALC_YC_ZXGD_MAX_H1_R, ST_YC_ZXGD_MAX_M1);
    calcDayAvg(CALC_YC_ZXGD_SUM_D1, ST_YC_ZXGD_SUM_M1, CALC_YC_ZXGD_COUNT_D1, ST_YC_ZXGD_COUNT_M1, CALC_YC_ZXGD_AVG_D1);
    calcMax(CALC_YC_ZXGD_MAX_D1, ST_YC_ZXGD_MAX_M1);
    calcH1Avg(CALC_YC_FXGD_SUM_H1_R, ST_YC_FXGD_SUM_M1, CALC_YC_FXGD_COUNT_H1_R, ST_YC_FXGD_COUNT_M1, CALC_YC_FXGD_AVG_H1_R);
    calcMax(CALC_YC_FXGD_MAX_H1_R, ST_YC_FXGD_MAX_M1);
    calcDayAvg(CALC_YC_FXGD_SUM_D1, ST_YC_FXGD_SUM_M1, CALC_YC_FXGD_COUNT_D1, ST_YC_FXGD_COUNT_M1, CALC_YC_FXGD_AVG_D1);
    calcMax(CALC_YC_FXGD_MAX_D1, ST_YC_FXGD_MAX_M1);
}

void ZGSTDevice::updateOperStartTime(const ZG6000::StringMap& _property)
{
    auto pair = _property.find("rtNewValue");
    if (pair == _property.end())
        return;
    auto startTime = QTime::fromString(pair->second.c_str(), "hh:mm:ss");
    if (startTime.isValid())
        m_operStartTime = startTime;
    else
        ZGLOG_WARN(QStringLiteral("无效的运营开始时间'%1'").arg(pair->second.c_str()));
}

void ZGSTDevice::updateOperEndTime(const ZG6000::StringMap& _property)
{
    auto pair = _property.find("rtNewValue");
    if (pair == _property.end())
        return;
    auto endTime = QTime::fromString(pair->second.c_str(), "hh:mm:ss");
    if (endTime.isValid())
        m_operEndTime = endTime;
    else
        ZGLOG_WARN(QStringLiteral("无效的运营结束时间'%1'").arg(pair->second.c_str()));
}

void ZGSTDevice::updateCalcStartTime(const ZG6000::StringMap& _property)
{
    auto pair = _property.find("rtNewValue");
    if (pair == _property.end())
        return;
    auto calcStart = QTime::fromString(pair->second.c_str(), "hh:mm:ss");
    if (calcStart.isValid())
        m_btCalcStartTime = calcStart;
    else
        ZGLOG_WARN(QStringLiteral("无效的本体电位计算开始时间'%1'").arg(pair->second.c_str()));
}

void ZGSTDevice::updateCalcEndTime(const ZG6000::StringMap& _property)
{
    auto pair = _property.find("rtNewValue");
    if (pair == _property.end())
        return;
    auto calcEnd = QTime::fromString(pair->second.c_str(), "hh:mm:ss");
    if (calcEnd.isValid())
        m_btCalcEndTime = calcEnd;
    else
        ZGLOG_WARN(QStringLiteral("无效的本体电位计算结束时间'%1'").arg(pair->second.c_str()));
}

bool ZGSTDevice::isNewM1Stat(const QDateTime& currentTime)
{
    return (currentTime.time().minute() != m_lastDateTime.time().minute());
}

bool ZGSTDevice::isNewM30Stat(const QDateTime& currentTime)
{
    return ((currentTime.time().minute() != m_lastDateTime.time().minute()) && (currentTime.time().minute() == 0 || currentTime.time().minute() == 30));
}

bool ZGSTDevice::isNewH1Stat(const QDateTime& currentTime)
{
    return ((currentTime.time().hour() != m_lastDateTime.time().hour()) &&
            (m_lastDateTime.time().minute() == 59) &&
            (currentTime.time().minute() == 0));
}

bool ZGSTDevice::isNewD1Stat(const QDateTime& currentTime)
{
    return ((currentTime.date().day() != m_lastDateTime.date().day()) &&
            (m_lastDateTime.daysTo(currentTime) == 1) &&
            (m_lastDateTime.time().hour() == 23) &&
            (m_lastDateTime.time().minute() == 59) &&
            (currentTime.time().hour() == 0) &&
            (currentTime.time().minute() == 0));
}

bool ZGSTDevice::isNewBtStat(const QDateTime& currentTime)
{
    return (currentTime.time().hour() == m_btCalcEndTime.hour() &&
            currentTime.time().minute() == m_btCalcEndTime.minute());
}

void ZGSTDevice::onCheckTime()
{
    QDateTime currentTime = QDateTime::currentDateTime();
    if (isNewH1Stat(currentTime))
    {
        auto future = QtConcurrent::run([&]()
            {
                if (isNewBtStat(currentTime))
                {
                    for (const auto& [_, sensor] : m_mapSensor)
                    {
                        sensor->onNewBtTime();
                    }
                }
                for (const auto& [_, sensor] : m_mapSensor)
                {
                    sensor->onNewHour();
                }
                onNewHour();
            });
    }
    if (isNewD1Stat(currentTime))
    {
        auto future = QtConcurrent::run([&]()
            {
                for (const auto& [_, sensor] : m_mapSensor)
                {
                    sensor->onNewDay();
                }
                for (const auto& [_, cdcBranch] : m_mapCDCBranch)
                {
                    cdcBranch->onNewDay();
                }
                onNewDay();
            });
    }
    if (isNewM1Stat(currentTime))
    {
        auto future = QtConcurrent::run([&]()
            {
                for (const auto& [_, sensor] : m_mapSensor)
                {
                    sensor->onNewMinute();
                }
                for (const auto& [_, board]: m_mapSampleBoard)
                {
                    board->onNewMinute();
                }
            });
    }
    m_lastDateTime = currentTime;
}

void ZGSTDevice::onReceivedMessage(const QString& topic, const QString& message)
{
    auto future = QtConcurrent::run([this](const QString& topic, const QString& message)
    {
	    auto pos = topic.indexOf("/");
	    if (pos == -1)
            return;
	    const auto& objectID = topic.mid(pos + 1).toStdString();
	    auto pairSensor = m_mapSensor.find(objectID);
	    if (pairSensor != m_mapSensor.end())
	    {
            ZG6000::MapStringMap properties;
            std::string errMsg;
            if (!ZGJson::convertFromJson(message.toStdString(), properties, errMsg))
            {
                ZGLOG_ERROR(errMsg.c_str());
                return;
            }
            pairSensor->second->processPropertyChange(properties);
            return;
	    }
	    auto pairBranch = m_mapCDCBranch.find(objectID);
	    if (pairBranch != m_mapCDCBranch.end())
	    {
            ZG6000::MapStringMap properties;
            std::string errMsg;
            if (!ZGJson::convertFromJson(message.toStdString(), properties, errMsg))
            {
                ZGLOG_ERROR(errMsg.c_str());
                return;
            }
            pairBranch->second->processPropertyChange(properties);
	    }
	    auto pairBoard = m_mapSampleBoard.find(objectID);
	    if (pairBoard != m_mapSampleBoard.end())
	    {
            ZG6000::MapStringMap properties;
            std::string errMsg;
            if (!ZGJson::convertFromJson(message.toStdString(), properties, errMsg))
            {
                ZGLOG_ERROR(errMsg.c_str());
                return;
            }
            pairBoard->second->processPropertyChange(properties);
	    }
	    if (objectID == m_id)
	    {
            ZG6000::MapStringMap properties;
            std::string errMsg;
            if (!ZGJson::convertFromJson(message.toStdString(), properties, errMsg))
            {
                ZGLOG_ERROR(errMsg.c_str());
                return;
            }
            processPropertyChange(properties);
	    }
	    if (objectID == m_gpioDatasetID)
	    {
            ZG6000::MapStringMap properties;
            std::string errMsg;
            if (!ZGJson::convertFromJson(message.toStdString(), properties, errMsg))
            {
                ZGLOG_ERROR(errMsg.c_str());
                return;
            }
            for (const auto & property : properties)
            {
                const auto& propName = property.first;
                const auto& prop = property.second;
                auto it = prop.find("rtNewValue");
                if (it != prop.end())
                {
                    auto propValue = it->second;
                    if (propName == "RunState")
                        propValue = "2";
                    ZGLOG_TRACE(QString("name: %1, value: %2").arg(propName.c_str()).arg(propValue.c_str()));
                    ZG6000::ErrorInfo e;
                    if (!ZGProxyCommon::updatePropertyValueEx(m_id, propName, propValue, e))
                        ZGLOG_ERROR(e);
                }
            }
	    }
	}, topic, message);
}

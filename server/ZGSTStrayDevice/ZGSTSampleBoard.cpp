#include "ZGSTSampleBoard.h"
#include "ZGSTStrayDefine.h"

ZGSTSampleBoard::ZGSTSampleBoard(const std::string& deviceID, QObject* parent)
    : ZGSTEndPoint{deviceID, parent}
{
}

bool ZGSTSampleBoard::initialize()
{
    initProcessor();
    return initialValue();
}

void ZGSTSampleBoard::onNewMinute()
{
    std::string rtState;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(m_id, "rtState", rtState, e))
    {
        ZGLOG_ERROR(e);
        ZGLOG_ERROR(QStringLiteral("获取采样板'%1'状态失败").arg(m_id.c_str()));
        return;
    }
    updateCommState(rtState);
}

void ZGSTSampleBoard::initProcessor()
{
    m_mapProcessor.insert({
        "rtState", [this](auto&& ph)
        {
            calcCommState(std::forward<decltype(ph)>(ph));
        }
    });
}

bool ZGSTSampleBoard::initialValue()
{
    std::string rtState;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(m_id, "rtState", rtState, e))
    {
        ZGLOG_ERROR(e);
        ZGLOG_ERROR(QStringLiteral("获取采样板'%1'状态失败").arg(m_id.c_str()));
        return false;
    }
    ZG6000::StringMap properties{
        {"CommState", rtState}, {"RunState", rtState},
        {"RecvState", rtState}, {"SendState", rtState},
        {"PowerState", rtState}
    };
    if (!ZGProxyCommon::updatePropertyValues(m_id, properties, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    return updateCommState(rtState);
}

bool ZGSTSampleBoard::updateCommState(const std::string& state)
{
    QString sql = QString("SELECT a.srcDeviceID FROM mp_param_device_relation a "
                          "LEFT JOIN mp_param_device b ON a.srcDeviceID = b.id "
                          "WHERE a.dstDeviceID = '%1' AND b.isEnable = 1").arg(m_id.c_str());
    ZG6000::StringList listSensorID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listSensorID))
    {
        ZGLOG_ERROR(QStringLiteral("获取采样板'%1'关联测量点失败").arg(m_id.c_str()));
        return false;
    }
    ZG6000::MapStringMap mapSensorState;
    for (const auto& sensorID : listSensorID)
    {
        mapSensorState[sensorID] = {{CALC_YX_COMM_STATE, state}};
    }
    if (!ZGProxyCommon::mupdateDataByField("mp_param_device", listSensorID, "rtState", state))
    {
        ZGLOG_ERROR(QStringLiteral("更新传感器通信状态失败"));
        return false;
    }
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::mupdatePropertyValues(mapSensorState, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

void ZGSTSampleBoard::calcCommState(const ZG6000::StringMap& _property)
{
    auto pair = _property.find("rtNewValue");
    if (pair == _property.end())
        return;
    const auto& commState = pair->second;
    ZG6000::StringMap properties{
        {"CommState", commState}, {"RunState", commState},
        {"RecvState", commState}, {"SendState", commState},
        {"PowerState", commState}
    };
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::updatePropertyValues(m_id, properties, e))
        ZGLOG_ERROR(e);
    updateCommState(commState);
}

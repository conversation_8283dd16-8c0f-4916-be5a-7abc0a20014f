#include "ZGOPTaskIUI.h"
#include "ZGOPTaskIUMng.h"

namespace ZG6000 {

ZGOPTaskIUI::ZGOPTaskIUI()
{
    ZGOPTaskIUMng::instance()->init();
}

bool ZGOPTaskIUI::checkState(const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->checkState(current);
}

void ZGOPTaskIUI::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current)
{
    ZGOPTaskIUMng::instance()->dispatchData(std::move(tableName), std::move(oper), std::move(reason), std::move(time), std::move(listRecord), current);
}

bool ZGOPTaskIUI::deleteTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->deleteTask(std::move(taskID), std::move(param), e, current);
}

bool ZGOPTaskIUI::getTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->getTaskList(std::move(param), listTask, e, current);
}

bool ZGOPTaskIUI::startTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->startTask(std::move(taskID), std::move(param), e, current);
}

bool ZGOPTaskIUI::pauseTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->pauseTask(std::move(taskID), std::move(param), e, current);
}

bool ZGOPTaskIUI::resumeTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->resumeTask(std::move(taskID), std::move(param), e, current);
}

bool ZGOPTaskIUI::retryTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->retryTask(std::move(taskID), std::move(param), e, current);
}

bool ZGOPTaskIUI::abolishTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->abolishTask(std::move(taskID), std::move(param), e, current);
}

bool ZGOPTaskIUI::confirmTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->confirmTask(std::move(taskID), std::move(param), e, current);
}

bool ZGOPTaskIUI::getTaskItems(std::string taskID, StringMap &task, ListStringMap &items, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->getTaskItems(std::move(taskID), task, items, e, current);
}

bool ZGOPTaskIUI::createTemplateTask(std::string templateID, StringList listAppNodeID, StringMap param, StringList& listTaskID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskIUMng::instance()->createTemplateTask(std::move(templateID), std::move(listAppNodeID), std::move(param), listTaskID, e, current);
}

bool ZGOPTaskIUI::createTemporaryTask(ListStringMap listUnlockTemplate, StringMap param, std::string& taskID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskIUMng::instance()->createTemporaryTask(std::move(listUnlockTemplate), std::move(param), taskID, e, current);
}

bool ZGOPTaskIUI::downloadTask(StringList listTaskID, ListStringMap &listTask, ListStringMap &listItem, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPTaskIUMng::instance()->downloadTask(std::move(listTaskID), listTask, listItem, e, current);
}

bool ZGOPTaskIUI::updateTask(ListStringMap listTask, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskIUMng::instance()->updateTask(std::move(listTask), e, current);
}

bool ZGOPTaskIUI::updateItem(ListStringMap listItem, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPTaskIUMng::instance()->updateItem(std::move(listItem), e, current);
}
} // namespace ZG6000

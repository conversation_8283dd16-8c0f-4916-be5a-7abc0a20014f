#ifndef ZG6000_ZGOPTASKIUI_H
#define ZG6000_ZGOPTASKIUI_H

#include <ZGOPTaskIU.h>

namespace ZG6000 {

class ZGOPTaskIUI : public ZG6000::ZGOPTaskIU
{
public:

    ZGOPTaskIUI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current) override;

    // ZGOPTaskBase interface
public:
    bool deleteTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool getTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current) override;
    bool startTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool pauseTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool resumeTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool retryTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool abolishTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool confirmTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;

    // ZGOPTaskIU interface
public:
    bool getTaskItems(std::string taskID, StringMap &task, ListStringMap &items, ErrorInfo &e, const Ice::Current &current) override;
    bool createTemplateTask(std::string templateID, StringList listAppNodeID, StringMap param, StringList& listTaskID, ErrorInfo& e, const Ice::Current& current) override;
    bool createTemporaryTask(ListStringMap listUnlockTemplate, StringMap param, std::string& taskID, ErrorInfo& e, const Ice::Current& current) override;
    bool downloadTask(StringList listTaskID, ListStringMap &listTask, ListStringMap &listItem, ErrorInfo &e, const Ice::Current &current) override;
    bool updateTask(ListStringMap listTask, ErrorInfo& e, const Ice::Current& current) override;
    bool updateItem(ListStringMap listItem, ErrorInfo& e, const Ice::Current& current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGOPTASKIUI_H

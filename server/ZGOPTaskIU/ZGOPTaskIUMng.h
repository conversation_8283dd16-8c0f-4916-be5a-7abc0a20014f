#ifndef ZG6000_ZGOPTASKIUMNG_H
#define ZG6000_ZGOPTASKIUMNG_H

#include <QObject>
#include <QTimer>
#include "ZGServerCommon.h"
#include <Ice/Ice.h>

class ZGMqttClient;
namespace ZG6000 {

class ZGOPTaskIUMng : public QObject
{
    Q_OBJECT
public:
    static ZGOPTaskIUMng* instance();
    void init();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current);
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current);

    // ZGOPTaskBase interface
public:
    bool deleteTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current);
    bool getTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current);
    bool startTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current);
    bool pauseTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current);
    bool resumeTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current);
    bool retryTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current);
    bool abolishTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current);
    bool confirmTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current);

    // ZGOPTaskIU interface
public:
    bool getTaskItems(std::string taskID, StringMap &task, ListStringMap &items, ErrorInfo &e, const Ice::Current &current);
    bool createTemplateTask(std::string templateID, StringList listAppNodeID, StringMap param, StringList& listTaskID, ErrorInfo& e, const Ice::Current& current);
    bool createTemporaryTask(ListStringMap listUnlockTemplate, StringMap param, std::string& taskID, ErrorInfo& e, const Ice::Current& current);
    bool generateTask(StringList& listSql, ListStringMap listUnlockTemplate, StringMap param, std::string& taskID, ErrorInfo& e);
    bool downloadTask(StringList listTaskID, ListStringMap &listTask, ListStringMap &listItem, ErrorInfo &e, const Ice::Current &current);
    bool updateTask(ListStringMap listTask, ErrorInfo& e, const Ice::Current& current);
    bool updateItem(ListStringMap listItem, ErrorInfo& e, const Ice::Current& current);

private:
    explicit ZGOPTaskIUMng(QObject *parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initMqttClient();
    bool initParams();
    void expandTask(ZG6000::StringMap& task);
    void expandItem(ZG6000::StringMap& item);

private slots:
    void onTimer();

private:
    bool m_initialized{ false };
    QString m_serverName{ "" };
    QString m_instName{ "" };
    int m_initInterval{ 10 };
    int m_checkInterval{ 10 };
    QTimer m_timer;
    ZGMqttClient* m_pMqttClient{ nullptr };
    StringMap m_mapAppNode;
    StringMap m_mapSubsystem;
    StringMap m_mapMajor;
    StringMap m_mapTaskStage;
    StringMap m_mapTaskState;
    StringMap m_mapItemType;
    StringMap m_mapItemState;
    std::set<std::string> m_setTaskFields{ "id", "name", "taskTypeID", "appNodeID", "subsystemID", "majorID", "rtCreateUserID", "rtCreateTime",
        "rtOperUserID", "rtMonUserID", "rtStartTime", "rtEndTime", "rtExecStartTime", "rtExecEndTime", "rtTaskStageID", "rtTaskStateID" };
    std::set<std::string> m_setITFields{ "id", "rtClientID", "rtNumber", "rtCurrentItemID", "rtCurrentIndex" };
};

inline static ZGOPTaskIUMng* g_pInstance = nullptr;

} // namespace ZG6000

#endif // ZG6000_ZGOPTASKIUMNG_H

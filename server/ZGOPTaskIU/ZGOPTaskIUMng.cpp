#include "ZGOPTaskIUMng.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGDebugMng.h"
#include "ZGMqttClient.h"
#include "ZGUtils.h"
#include "zgerror/ZGOPTaskIUError.h"
#include <QThread>
#include <QRandomGenerator>
#include <QJsonObject>
#include <QJsonDocument>

namespace ZG6000 {

ZGOPTaskIUMng *ZGOPTaskIUMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGOPTaskIUMng;
    return g_pInstance;
}

void ZGOPTaskIUMng::init()
{
    ZGLOG_TRACE("init");
    initEvents();
    initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    QThread::msleep(QRandomGenerator::global()->bounded(5, 10));
    while (!initParams())
    {
        ZGLOG_ERROR("initParams error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initMqttClient())
    {
        ZGLOG_ERROR("initMqttClient error.");
        QThread::msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    m_timer.start(1000);
    ZGLOG_INFO("ZGOPTaskIU init finished.");
}

bool ZGOPTaskIUMng::checkState(const Ice::Current &current)
{
    return m_initialized;
}

void ZGOPTaskIUMng::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time,
                                 ListRecord listRecord, const Ice::Current &current)
{
    if (reason != "change")
        return;
    QJsonObject root;
    QJsonObject dataObj;
    if (oper == "update")
    {
        try
        {
            for (const auto& record: listRecord)
            {
                for (const auto &field : record)
                {
                    dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
                }
                std::string taskID;
                if (tableName == "op_param_task")
                {
                    if (dataObj.find("rtTaskStageID") != dataObj.end())
                        dataObj.insert("rtTaskStageName", dataObj["rtTaskStageID"].toString());
                    if (dataObj.find("rtTaskStateID") != dataObj.end())
                        dataObj.insert("rtTaskStateName", dataObj["rtTaskStateID"].toString());
                    taskID = ZGUtils::get(record, "id").newValue;                   
                    root["head"] = dataObj;
                    QJsonDocument doc(root);
                    m_pMqttClient->sendPublish(QString("op_param_iu_task/%1").arg(taskID.c_str()), doc.toJson());
                    std::string taskTypeID;
                    if (ZGProxyCommon::getDataByField("op_param_task", taskID, "taskTypeID", taskTypeID))
                    {
                        m_pMqttClient->sendPublish(QString("op_param_task/%1/update").arg(taskTypeID.c_str()), QString("%1").arg(taskID.c_str()));
                    }
                }
                if (tableName == "op_param_iu_task")
                {
                    taskID = ZGUtils::get(record, "id").newValue;                   
                    root["head"] = dataObj;
                    QJsonDocument doc(root);
                    m_pMqttClient->sendPublish(QString("op_param_iu_task/%1").arg(taskID.c_str()), doc.toJson());
                }
                if (tableName == "op_param_iu_task_item")
                {
                    if (dataObj.find("rtExecStateID") != dataObj.end())
                        dataObj.insert("rtExecStateName", dataObj["rtExecStateID"].toString());
                    root["item"] = dataObj;
                    const auto& itemID = ZGUtils::get(record, "id").newValue;
                    ZGProxyCommon::getDataByField("op_param_iu_task_item", itemID, "taskID", taskID);
                    QJsonDocument doc(root);
                    ZGLOG_TRACE(doc.toJson());
                    m_pMqttClient->sendPublish(QString("op_param_iu_task/%1").arg(taskID.c_str()), doc.toJson());
                }
            }
        }
        catch(const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }       
    }
    if (oper == "insert")
    {
        try
        {
            for (const auto& record: listRecord)
            {
                std::string taskID = ZGUtils::get(record, "id").newValue;
                std::string taskTypeID;
                if (ZGProxyCommon::getDataByField("op_param_task", taskID, "taskTypeID", taskTypeID))
                    m_pMqttClient->sendPublish(QString("op_param_task/%1/%2").arg(taskTypeID.c_str()).arg(oper.c_str()), QString("%1").arg(taskID.c_str()));
            }
        }
        catch(const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }       
    }
    if (oper == "delete")
    {
        try
        {
            for (const auto& record: listRecord)
            {
                std::string taskID = ZGUtils::get(record, "id").newValue;
                m_pMqttClient->sendPublish(QString("op_param_task/%1").arg(oper.c_str()), QString("%1").arg(taskID.c_str()));
            }
        }
        catch(const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }       
    }
}

bool ZGOPTaskIUMng::initParams()
{
    QString sql = "SELECT id, name FROM sp_param_appnode";
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), m_mapAppNode))
    {
        ZGLOG_ERROR("获取应用节点信息失败");
        return false;
    }
    sql = "SELECT id, name FROM sp_param_subsystem";
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), m_mapSubsystem))
    {
        ZGLOG_ERROR("获取子系统信息失败");
        return false;
    }
    sql = "SELECT id, name FROM sp_param_major";
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), m_mapMajor))
    {
        ZGLOG_ERROR("获取专业信息失败");
        return false;
    }
    sql = "SELECT id, name FROM op_dict_task_stage";
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), m_mapTaskStage))
    {
        ZGLOG_ERROR("获取任务阶段信息失败");
        return false;
    }
    sql = "SELECT id, name FROM op_dict_task_state";
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), m_mapTaskState))
    {
        ZGLOG_ERROR("获取任务状态信息失败");
        return false;
    }
    sql = "SELECT id, name FROM op_dict_iu_item_type";
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), m_mapItemType))
    {
        ZGLOG_ERROR("获取任务项类型信息失败");
        return false;
    }
    sql = "SELECT id, name FROM op_dict_iu_item_state";
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), m_mapItemState))
    {
        ZGLOG_ERROR("获取任务项状态信息失败");
        return false;
    }
    return true;
}

bool ZGOPTaskIUMng::downloadTask(StringList listTaskID, ListStringMap &listTask, ListStringMap &listItem, ErrorInfo &e,
                                 const Ice::Current &current)
{
    const auto& ids = ZGUtils::join(listTaskID, ",", "'", "'");
    QString sql = QString("SELECT * FROM op_param_task WHERE id IN (%1)").arg(ids.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取任务信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    sql = QString("SELECT * FROM op_param_iu_task_item WHERE taskID IN (%1)").arg(ids.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listItem))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取任务项信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskIUMng::updateTask(ListStringMap listTask, ErrorInfo& e, const Ice::Current& current)
{
    ZG6000::StringList listSql;
    for (const auto & task : listTask)
    {
        QString errMsg;
        if (!ZGUtils::checkRequiredParam(task, { "id" }, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
            e.errDetail = errMsg.toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZG6000::StringMap taskTotal, taskIU;
        for (const auto& pair : task)
        {
            if (m_setTaskFields.find(pair.first) != m_setTaskFields.end())
                taskTotal[pair.first] = pair.second;
            if (m_setITFields.find(pair.first) != m_setITFields.end())
                taskIU[pair.first] = pair.second;
        }
        taskTotal["rtVersion"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
        if (!taskTotal.empty())
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_task", taskTotal));
        if (!taskIU.empty())
            listSql.push_back(ZGUtils::generateUpdateSql("op_param_iu_task", taskIU));
    }
    if (!listSql.empty())
    {
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
    }
    return true;
}

bool ZGOPTaskIUMng::updateItem(ListStringMap listItem, ErrorInfo& e, const Ice::Current& current)
{
    ZG6000::StringList listSql;
    for (const auto& item : listItem)
    {
        QString errMsg;
        if (!ZGUtils::checkRequiredParam(item, { "id" }, errMsg))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
            e.errDetail = errMsg.toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        listSql.push_back(ZGUtils::generateUpdateSql("op_param_iu_task_item", item));
    }
    if (!listSql.empty())
    {
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新任务项失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
    }
    return true;
}

ZGOPTaskIUMng::ZGOPTaskIUMng(QObject *parent) : QObject{parent}
{

}

void ZGOPTaskIUMng::initEvents()
{
    connect(&m_timer, &QTimer::timeout, this, &ZGOPTaskIUMng::onTimer);
}

void ZGOPTaskIUMng::initServerInstConfig()
{
    const auto &serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGOPTaskIUMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGOPTaskIUMng::initMqttClient()
{
    if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
    {
        ZGLOG_ERROR("initMqttClient error.");
        return false;
    }
    m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
    if (m_pMqttClient == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage error.");
        return false;
    }
    m_pMqttClient->connectToHost();
    return true;
}

void ZGOPTaskIUMng::expandTask(ZG6000::StringMap &task)
{
    task["appNodeName"] = ZGUtils::get(m_mapAppNode, task["appNodeID"], "");
    task["subSystemName"] = ZGUtils::get(m_mapSubsystem, task["subSystemID"], "");
    task["majorName"] = ZGUtils::get(m_mapMajor, task["majorID"], "");
    std::string operUserName, monUserName, createUserName;
    if (!task["rtOperUserID"].empty())
        ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtOperUserID"], "name", operUserName);
    task["rtOperUserName"] = operUserName;
    if (!task["rtMonUserID"].empty())
        ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtMonUserID"], "name", monUserName);
    if (!task["rtCreateUserID"].empty())
        ZGProxyCommon::getDataByField("sp_param_hrm_user", task["rtCreateUserID"], "name", createUserName);
    task["rtMonUserName"] = monUserName;
    task["rtCreateUserName"] = createUserName;
    task["rtTaskStageName"] = ZGUtils::get(m_mapTaskStage, task["rtTaskStageID"], "");
    task["rtTaskStateName"] = ZGUtils::get(m_mapTaskState, task["rtTaskStateID"], "");
    ZG6000::StringMap iuTask;
    ZGProxyCommon::getDataByFields("op_param_iu_task", task["id"], {"rtCurrentItemID", "rtCurrentItemIndex"}, iuTask);
    task["rtCurrentItemID"] = ZGUtils::get(iuTask, "rtCurrentItemID", "");
    task["rtCurrentItemIndex"] = ZGUtils::get(iuTask, "rtCurrentItemIndex", "");
}

void ZGOPTaskIUMng::expandItem(ZG6000::StringMap &item)
{
    item["itemTypeName"] = ZGUtils::get(m_mapItemType, item["itemTypeID"], "");
    item["rtExecStateName"] = ZGUtils::get(m_mapItemState, item["rtExecStateID"], ""); 
}

void ZGOPTaskIUMng::onTimer()
{

}

bool ZGOPTaskIUMng::deleteTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    QString sql = QString("DELETE FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
    ZG6000::StringList listSql;
    listSql.push_back(sql.toStdString());
    sql = QString("DELETE FROM op_param_iu_task WHERE id = '%1'").arg(taskID.c_str());
    listSql.push_back(sql.toStdString());
    sql = QString("DELETE FROM op_param_task_item WHERE taskID = '%1'").arg(taskID.c_str());
    listSql.push_back(sql.toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
        e.errDetail = QStringLiteral("删除任务失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskIUMng::getTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current)
{
    const auto &condition = ZGUtils::get(param, "condition", "1=1");
    const auto &orderType = ZGUtils::get(param, "order", "ASC");
    const auto &orderField = ZGUtils::get(param, "sort", "a.id");
    const auto &offset = ZGUtils::get(param, "offset", "0");
    const auto &limit = ZGUtils::get(param, "limit", "1000");
    QString addition = QString(" ORDER BY %1 %2 LIMIT %3, %4")
                           .arg(orderField.c_str())
                           .arg(orderType.c_str())
                           .arg(offset.c_str())
                           .arg(limit.c_str());
    QString sql = QString("SELECT * FROM op_param_task a WHERE %1").arg(condition.c_str()) + addition;
    auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
    if (dbProxy == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取数据库代理失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listTask, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        for (auto &task : listTask)
        {
            expandTask(task);
        }
        return true;
    }
    catch (const std::exception &ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_RT);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGOPTaskIUMng::startTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
    e.errDetail = QStringLiteral("不允许的操作").toStdString();
    ZGLOG_ERROR(e);
    return false;
}

bool ZGOPTaskIUMng::pauseTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
    e.errDetail = QStringLiteral("不允许的操作").toStdString();
    ZGLOG_ERROR(e);
    return false;
}

bool ZGOPTaskIUMng::resumeTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
    e.errDetail = QStringLiteral("不允许的操作").toStdString();
    ZGLOG_ERROR(e);
    return false;
}

bool ZGOPTaskIUMng::retryTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
    e.errDetail = QStringLiteral("不允许的操作").toStdString();
    ZGLOG_ERROR(e);
    return false;
}

bool ZGOPTaskIUMng::abolishTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
    e.errDetail = QStringLiteral("不允许的操作").toStdString();
    ZGLOG_ERROR(e);
    return false;
}

bool ZGOPTaskIUMng::confirmTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
    e.errDetail = QStringLiteral("不允许的操作").toStdString();
    ZGLOG_ERROR(e);
    return false;
}

bool ZGOPTaskIUMng::getTaskItems(std::string taskID, StringMap &task, ListStringMap &items, ErrorInfo &e, const Ice::Current &current)
{
    QString sql = QString("SELECT * FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
    ZG6000::ListStringMap listTask;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取任务信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (listTask.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("任务不存在").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    task = listTask.front();   
    expandTask(task);
    sql = QString("SELECT * FROM op_param_task_item WHERE taskID = '%1'").arg(taskID.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), items))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取任务项信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    for (auto& item: items)
    {
        expandItem(item);
    }
    return true;
}

bool ZGOPTaskIUMng::createTemplateTask(std::string templateID, StringList listAppNodeID, StringMap param, StringList& listTaskID, ErrorInfo& e, const Ice::Current& current)
{
    param["taskTemplateID"] = templateID;
    std::string templateName;
    if (!ZGProxyCommon::getDataByField("op_param_task", templateID, "name", templateName))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取模板名称失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    const auto& listVariable = ZGUtils::extractVariables(templateName);
    try
    {
        QString sql = QString("SELECT id, deviceTag FROM op_param_iu_task_template_item WHERE unlockTemplateID = '%1'").arg(templateID.c_str());
        ZG6000::ListStringMap listUnlockTemplate;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listUnlockTemplate))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取模板项失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZG6000::StringList listSql;
        for (size_t i = 0; i < listAppNodeID.size(); ++i)
        {
            param["appNodeID"] = listAppNodeID[i];
            std::string appNodeName;
            ZGProxyCommon::getDataByField("sp_param_appnode", listAppNodeID[i], "name", appNodeName);
            param["name"] = templateName;
            if (!listVariable.empty())
            {
                for (const auto& variable: listVariable)
                {
                    if (variable == "appNode")
                    {
                        ZGUtils::replaceString(param["name"], "[" + variable + "]", appNodeName);
                    }
                }
            }
            sql = QString("SELECT deviceTag AS id, id AS number FROM mp_param_device WHERE appNodeID = '%1' ORDER BY deviceTag").arg(listAppNodeID[i].c_str());
            ZG6000::StringMap device;
            if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), device))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
                e.errDetail = QStringLiteral("获取应用节点'%1'设备失败").arg(listAppNodeID[i].c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            for (auto& unlockTemplate : listUnlockTemplate)
            {
                unlockTemplate["deviceID"] = "";
                const auto& deviceTag = ZGUtils::get(unlockTemplate, "deviceTag");
                if (!deviceTag.empty())
                {
                    auto pairDevice = device.find(deviceTag);
                    if (pairDevice == device.end())
                    {
                        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
                        e.errDetail = QStringLiteral("应用节点'%1'下没有标识为'%2'的设备").arg(listAppNodeID[i].c_str()).arg(deviceTag.c_str()).toStdString();
                        ZGLOG_ERROR(e);
                    }
                    unlockTemplate["deviceID"] = pairDevice->second;
                }
            }
            std::string taskID;
            if (!generateTask(listSql, listUnlockTemplate, param, taskID, e))
                return false;
            listTaskID.push_back(taskID);
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建任务失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGOPTaskIUMng::createTemporaryTask(ListStringMap listUnlockTemplate, StringMap param, std::string& taskID, ErrorInfo& e, const Ice::Current& current)
{
    ZG6000::StringList listSql;
    if (!generateTask(listSql, std::move(listUnlockTemplate), std::move(param), taskID, e))
        return false;
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建任务失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGOPTaskIUMng::generateTask(StringList& listSql, ListStringMap listUnlockTemplate, StringMap param, std::string& taskID, ErrorInfo& e)
{
    try
    {
        if (!ZGProxyCommon::createUUID(taskID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建任务ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        auto pair = param.find("taskTemplateID");
        if (pair != param.end())
        {
            ZG6000::StringMap iuTask{ {"id", taskID}, {"taskTemplateID", ZGUtils::get(param, "taskTemplateID")} };
            listSql.push_back(ZGUtils::generateInsertSql("op_param_iu_task", iuTask));
            param.erase(pair);
        }
        auto pairAppNode = param.find("appNodeID");
        std::string appNodeName;
        if (pairAppNode != param.end())
        {
            ZGProxyCommon::getDataByField("sp_param_appnode", pairAppNode->second, "name", appNodeName);
        }
        param["id"] = taskID;
        listSql.push_back(ZGUtils::generateInsertSql("op_param_task", param));
        int offset = 0;
        for (const auto& unlockTemplate : listUnlockTemplate)
        {
            const auto& unlockTemplateID = ZGUtils::get(unlockTemplate, "templateID");
            const auto& deviceID = ZGUtils::get(unlockTemplate, "deviceID");
            std::string deviceName;
            ZGProxyCommon::getDataByField("mp_param_device", deviceID, "name", deviceName);
            QString sql = QString("SELECT * FROM op_param_iu_unlock_template_item WHERE templateID = '1' ORDER BY itemIndex");
            ZG6000::ListStringMap listUnlockTemplateItem;
            if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listUnlockTemplateItem))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
                e.errDetail = QStringLiteral("获取解锁模板'%1'项失败").arg(unlockTemplateID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            sql = QString("SELECT tag as id, id as number FROM mp_param_lock WHERE deviceID = '%1' ORDER BY tag").arg(deviceID.c_str());
            ZG6000::StringMap mapLock;
            if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), mapLock))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
                e.errDetail = QStringLiteral("获取设备'%1'关联锁具失败").arg(deviceID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            ZG6000::StringList listItemID;
            if (!ZGProxyCommon::createUUID(static_cast<int>(listUnlockTemplateItem.size()), listItemID))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_DB);
                e.errDetail = QStringLiteral("创建任务项ID失败").toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            int itemNo = 0;
            for (const auto& unlockTemplateItem : listUnlockTemplateItem)
            {
                ZG6000::StringMap taskItem;
                taskItem["id"] = listItemID[itemNo++];
                const auto& lockTag = ZGUtils::get(unlockTemplateItem, "lockTag");
                if (!lockTag.empty())
                {
                    auto pairLock = mapLock.find(lockTag);
                    if (pairLock == mapLock.end())
                    {
                        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
                        e.errDetail = QStringLiteral("设备'%1'下没有标识为'%2'的锁具").arg(deviceID.c_str()).arg(lockTag.c_str()).toStdString();
                        ZGLOG_ERROR(e);
                        return false;
                    }
                    taskItem["lockID"] = pairLock->second;
                }
                taskItem["unlockTemplateID"] = ZGUtils::get(unlockTemplateItem, "templateID");
                taskItem["unlockTemplateItemID"] = ZGUtils::get(unlockTemplateItem, "id");
                taskItem["name"] = ZGUtils::get(unlockTemplateItem, "name");
                const auto& listVariableName = ZGUtils::extractVariables(taskItem["name"]);
                if (!listVariableName.empty())
                {
                    for (const auto& variable: listVariableName)
                    {
                        if (variable == "device")
                        {
                            ZGUtils::replaceString(taskItem["name"], "[" + variable + "]", deviceName);
                        }
                        if (variable == "appNode")
                        {
                            ZGUtils::replaceString(taskItem["name"], "[" + variable + "]", appNodeName);
                        }
                    }
                }
                taskItem["voice"] = ZGUtils::get(unlockTemplateItem, "voice");
                const auto& listVariableVoice = ZGUtils::extractVariables(taskItem["voice"]);
                if (!listVariableVoice.empty())
                {
                    for (const auto& variable: listVariableVoice)
                    {
                        if (variable == "device")
                        {
                            ZGUtils::replaceString(taskItem["voice"], "[" + variable + "]", deviceName);
                        }
                        if (variable == "appNode")
                        {
                            ZGUtils::replaceString(taskItem["voice"], "[" + variable + "]", appNodeName);
                        }
                    }
                }
                taskItem["taskID"] = taskID;
                taskItem["itemIndex"] = std::to_string(std::atoi(ZGUtils::get(unlockTemplateItem, "itemIndex").c_str()) + offset);
                taskItem["itemTypeID"] = ZGUtils::get(unlockTemplateItem, "itemTypeID");
                taskItem["timeout"] = ZGUtils::get(unlockTemplateItem, "timeout");
                taskItem["confirmValue"] = ZGUtils::get(unlockTemplateItem, "confirmValue");
                taskItem["isAllowJump"] = ZGUtils::get(unlockTemplateItem, "isAllowJump");
                taskItem["jumpValue"] = ZGUtils::get(unlockTemplateItem, "jumpValue");
                const auto& jumpIndex = ZGUtils::get(unlockTemplateItem, "jumpIndex");
                taskItem["jumpIndex"] = std::to_string(std::atoi(jumpIndex.c_str()) + offset);
                taskItem["delaySecs"] = ZGUtils::get(unlockTemplateItem, "delaySecs");
                listSql.push_back(ZGUtils::generateInsertSql("op_param_iu_task_item", taskItem));
            }
            if (!listUnlockTemplateItem.empty())
            {
                const auto& itemIndex = ZGUtils::get(listUnlockTemplateItem[listUnlockTemplateItem.size() - 1], "itemIndex");
                offset = std::atoi(itemIndex.c_str()) + 1;
            }
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPTaskIU::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}
} // namespace ZG6000

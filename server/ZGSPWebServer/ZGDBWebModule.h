#ifndef ZGDBWEBMODULE_H
#define ZGDBWEBMODULE_H

#include <QObject>
#include "ZGWebModule.h"
#include "ZGProxyMng.h"

class ZGDBWebModule : public ZGWebModule
{
    Q_OBJECT
public:
    static Response getTableRecordsCount(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response getTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response addTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response addTableRecord(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response addTableRecordWithResult(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response updateTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response deleteTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response getTableParam(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response getFieldsParam(const QVariantMap& headers, const QJsonValue& param);
    static Response execQuery(const QVariantMap& headers, const QJsonValue& param);
    static Response execQueryToObject(const QVariantMap& headers, const QJsonValue& param);
    static Response execQueryWithCount(const QVariantMap& headers, const QJsonValue& param);
    static Response execInsertSql(const QVariantMap& headers, const QJsonValue& param);
    static Response execCommands(const QVariantMap& headers, const QJsonValue& param);
    static Response createUuid(const QVariantMap& headers, const QJsonValue& param);
    static Response syncData(const QVariantMap& headers, const QJsonValue& param);
    static Response syncHisData(const QVariantMap& headers, const QJsonValue& param);
    static Response getAppNodeLayer(const QVariantMap& headers, const QJsonValue& param);

private:
    static Response execSqlStatement(const QString& tableName, const QJsonValue& param,
        std::function<Response(const std::shared_ptr<ZG6000::ZGSPDBDataPrx>)> func);
    static bool getParamFromTable(const QString& tableName, QJsonObject& object, QString& errMsg);
    static bool getFieldsParamFromTable(const QString& tableName, QJsonArray& array, QString& errMsg);
    static bool getFieldsParams(const QString& tableName, QJsonArray& array, QString& errMsg);
    static void writeNodeLayer(const std::string& parentNodeID, QJsonArray& array);

private:
    static std::unordered_map<QString, QJsonObject> m_mapTableParam;
};

#endif // ZGDBWEBMODULE_H

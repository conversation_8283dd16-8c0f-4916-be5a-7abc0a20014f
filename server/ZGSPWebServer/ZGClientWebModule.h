#ifndef ZGCLIENTWEBMODULE_H
#define ZGCLIENTWEBMODULE_H

#include <ZGWebModule.h>
#include <QJsonObject>
#include "ZGServerCommon.h"

class ZGClientWebModule : public ZGWebModule
{
    Q_OBJECT
public:
    explicit ZGClientWebModule(QObject *parent = nullptr);
    static Response on_client_test(const QString& clientID, const QVariantMap& headers, const QJsonValue& param);
    static Response on_client_register(const QString& clientID, const QVariantMap &headers, const QJsonValue& param);
    static Response on_client_bind(const QString& clientID, const QVariantMap& headers, const QJsonValue& param);
    static Response on_client_verify(const QString& clientID, const QVariantMap& headers, const QJsonValue& param);
    static Response on_client_list(const QString& clientID, const QVariantMap &headers, const QJsonValue& param);
    static Response on_client_appnode_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param);
    static Response on_client_subsystem_get(const QString& clientID, const QVariantMap &headers, const QJsonValue& param);
    static Response on_client_state_check(const QString& clientID, const QVariantMap& headers, const QJsonValue& param);

private:
    static ZG6000::StringMap getSubsystem(const QJsonObject& object);
    static void writeNodeLayer(const std::string& nodeID, QJsonArray& array);
};

#endif // ZGCLIENTWEBMODULE_H

#include "ZGUserWebModule.h"
#include "ZGJson.h"
#include "ZGProxyCommon.h"

ZGUserWebModule::ZGUserWebModule(QObject *parent)
    : ZGWebModule{parent}
{

}

ZGWebModule::Response ZGUserWebModule::on_login(const QString& clientID, const QVariantMap &headers, const QJsonValue& param)
{
	std::string fieldValue;
	const auto& obj = param.toObject();
	QString errMsg;
	if (!ZGWebModule::checkRequiredFields(obj, { "type", "keepTime" }, errMsg))
		return ZGWebModule::errorObject(errMsg);
	const auto& type = obj["type"].toString();
	const auto& keepTime = obj["keepTime"].toInt();
	std::string id;
	if (clientID.isEmpty())
		return errorObject(u8"客户端未指定", rcErrNotRegister);
	std::string sql = "SELECT id FROM sp_param_client WHERE id = '" + clientID.toStdString() + "'";
	ZG6000::StringList listID;
	if (!ZGProxyCommon::execQuerySqlCol(sql, listID))
		return errorObject(QStringLiteral("获取客户端'%1'信息失败").arg(clientID));
	if (listID.empty())
	{
		errMsg = QStringLiteral("指定的客户端'%1'不存在").arg(clientID);
		ZGLOG_ERROR(errMsg);
		return errorObject(errMsg, rcErrNotRegister);
	}
	auto userProxy = ZGProxyMng::instance()->getProxySPUserManager();
	if (userProxy == nullptr)
		return ZGWebModule::errorObject(u8"获取用户管理服务代理失败");
	if (type == "password")
	{
		if (!ZGWebModule::checkRequiredFields(obj, { "userID", "password" }, errMsg))
			return ZGWebModule::errorObject(errMsg);
		const auto& userID = obj["userID"].toString().toStdString();
		const auto& password = obj["password"].toString().toStdString();
        std::string outClientID;
		try
		{
			ZG6000::ErrorInfo e;
			if (!userProxy->loginByPassword(clientID.toStdString(), userID, password, keepTime, outClientID, e))
				return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		}
		catch (const Ice::Exception& e)
		{
			return ZGWebModule::errorObject(e.what());
		}
		ZG6000::StringMap cookie;
		if (!ZGProxyCommon::getDataByFields("sp_param_client", clientID.toStdString(), { "rtCookieID", "rtKeepTime" }, cookie))
			return ZGWebModule::errorObject(u8"获取客户端cookie失败");
        return replyObject(clientID);
	}
	if (type == "card")
	{
        if (!ZGWebModule::checkRequiredFields(obj, { "cardID", "authModeID" }, errMsg))
			return ZGWebModule::errorObject(errMsg);
		const auto& cardID = obj["cardID"].toString().toStdString();
        const auto& authModeID = obj["authModeID"].toString().toStdString();
        std::string outClientID;
		try
		{
			ZG6000::ErrorInfo e;
            if (!userProxy->loginByCard(clientID.toStdString(), "-1", authModeID, cardID, keepTime, outClientID, e))
				return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
		}
		catch (const Ice::Exception& e)
		{
			return ZGWebModule::errorObject(e.what());
		}
        return replyObject(clientID);
	}
	return ZGWebModule::errorObject(QStringLiteral("无效的类型'%1'").arg(type));
}

ZGWebModule::Response ZGUserWebModule::on_logout(const QString& clientID, const QVariantMap& headers, const QJsonValue& param)
{
	std::string loginUserId;
	if (!ZGProxyCommon::getDataByField("sp_param_client", clientID.toStdString(), "rtLoginUserID", loginUserId))
		return ZGWebModule::errorObject(QString(QStringLiteral("无效的客户端%1")).arg(clientID), rcErrNotRegister);
	auto userProxy = ZGProxyMng::instance()->getProxySPUserManager();
	if (userProxy == nullptr)
		return ZGWebModule::errorObject(u8"获取用户管理服务代理失败");
	try
	{
		ZG6000::ErrorInfo e;
		if (!userProxy->logout(clientID.toStdString(), loginUserId, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
	}
	catch (const Ice::Exception& e)
	{
		return ZGWebModule::errorObject(e.what());
	}
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGUserWebModule::on_password_verify(const QString &clientID, const QVariantMap &headers, const QJsonValue &param)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, { "userID", "password", "powerID" }, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& userID = object["userID"].toString();
    const auto& password = object["password"].toString();
    const auto& powerID = object["powerID"].toString();
    QString appNodeID;
    if (object.find("appNodeID") != object.end())
        appNodeID = object["appNodeID"].toString();
    auto userProxy = ZGProxyMng::instance()->getProxySPUserManager();
    if (userProxy == nullptr)
        return ZGWebModule::errorObject(u8"获取用户管理服务代理失败");
    try
    {
        ZG6000::ErrorInfo e;
        if (!userProxy->verifyByPasswordNoClient(userID.toStdString(), password.toStdString(), appNodeID.toStdString(), powerID.toStdString(), e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGUserWebModule::on_user_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!checkRequiredFields(object, {"powerID"}, errMsg))
        return errorObject(errMsg);
    const auto& powerID = object["powerID"].toString().toStdString();
    std::string appNodeID;
    if (object.find("appNodeID") != object.end())
        appNodeID = object["appNodeID"].toString().toStdString();
	auto userProxy = ZGProxyMng::instance()->getProxySPUserManager();
	if (userProxy == nullptr)
		return ZGWebModule::errorObject(u8"获取用户管理服务代理失败");
	try
	{
		ZG6000::ErrorInfo e;
		ZG6000::ListStringMap lstUser;
        if (!userProxy->getAvaiableUser("", appNodeID, powerID, lstUser, e))
			return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        ZG6000::StringList listUserID;
        for (auto& user: lstUser)
        {
            listUserID.push_back(user["id"]);
        }
        ZG6000::ListStringMap listUser;
        if (!ZGProxyCommon::mgetDataByID("sp_param_hrm_user", listUserID, listUser))
            return ZGWebModule::errorObject(QStringLiteral("获取用户信息失败"));
        const auto& json = listStringMapToArray(listUser);
		return ZGWebModule::replyObject(json);
	}
	catch (const Ice::Exception& e)
	{
		return ZGWebModule::errorObject(e.what());
	}
}


#ifndef ZGSPWEBSERVERMNG_H
#define ZGSPWEBSERVERMNG_H

#include <QObject>
#include <QTimer>
#include <QJsonObject>
#include <QReadWriteLock>
#include <ZGServerCommon.h>
#include "ZGWebModule.h"

class QHttpServerRequest;
class QHttpServer;
class ZGWebModule;
class ZGRedisClient;

class ZGSPWebServerMng : public QObject
{
    Q_OBJECT

public:
    static ZGSPWebServerMng* instance();
    bool checkState();
    void init();
    bool initBaseService(QString& errMsg);

private:
    explicit ZGSPWebServerMng(QObject* parent = nullptr);
    void initApiHandle();
    void initWebModules();
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initCertification();
    bool createRtTopic();
    void initWebServer();
    bool startWebServer();
    bool isClientValid(const QString& clientId,
                       int& errCode,
                       QString& errMsg);
    bool doValidate(const QString& clientId,
                    const QString& cookie,
                    int& errCode,
                    QString& errMsg);
    bool fetchCookies(const QHttpServerRequest* req,
                      QMap<QString, QString>& cookies,
                      bool& secure,
                      bool& httpOnly);
    bool parseRequest(const QHttpServerRequest* req,
                      QString& clientID,
                      QJsonValue& param,
                      QString& errMsg);
    bool parseObject(const QJsonObject& object,
                     QString& clientID,
                     QJsonValue& param,
                     QString& errMsg);
    bool parseObjectWithKey(const QJsonObject& object,
                            QString& clientID,
                            QJsonValue& param,
                            QString& clientKey,
                            QString& errMsg);
    bool getUserPowers(const QString& userID,
                       ZG6000::StringList& listPower,
                       QString& errMsg);
    bool decryptWithClientKey(const QString& clientID,
                              const QByteArray& data,
                              QByteArray& clientKey,
                              QJsonDocument& document,
                              QString& errMsg);
    // 清除过期和无效的令牌
    void clearInvalidToken();

signals:
    void initFinished();

private slots:
    void onReceiveMessage(const QString& channel,
                          const QString& message);
    void onTimer();

private:
    bool m_initialized{false};
    QString m_serverName;
    QString m_instName;
    bool m_masterInst{false};
    int m_initInterval{10};
    int m_checkInterval{5};
    QTimer m_initTimer;
    QTimer m_checkTimer;
    quint16 m_port{8080};
    QHttpServer* m_httpServer;
    QString m_webRootPath;
    bool m_checkCookie{false};
    bool m_enableSsl{false};
    bool m_encrypt{false};
    QString m_registerPage{"register.html"};
    ZGRedisClient* m_pRtTopic{nullptr};
    using TableHandler = std::function<ZGWebModule::Response(const QString&,
                                                           const QVariantMap&,
                                                           const QJsonValue&)>;
    std::map<QString, TableHandler> m_mapTableApi;
    using Handle = std::function<ZGWebModule::Response(const QVariantMap&,
                                                     const QJsonValue&)>;
    std::map<QString, Handle> m_mapValidateApi;
    using ClientHandle = std::function<ZGWebModule::Response(const QString&,
                                                           const QVariantMap&,
                                                           const QJsonValue&)>;
    std::map<QString, ClientHandle> m_mapDirectApi;
    std::unordered_map<QString, ZGWebModule*> m_mapPathModule;
    std::unordered_map<QString, std::vector<ZGWebModule*>> m_mapMonitorTable;
    std::unordered_map<QString, QByteArray> m_mapClientKey;
};

#endif // ZGSPWEBSERVERMNG_H

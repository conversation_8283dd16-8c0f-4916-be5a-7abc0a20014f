#include "ZGRTWebModule.h"
#include <QJsonArray>
#include <QJsonDocument>
#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGUtils.h"
#include "ZGProxyCommon.h"

ZGWebModule::Response ZGRTWebModule::getTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param)
{
	return operateTable(tableName, param, [&](const std::shared_ptr<ZG6000::ZGSPRTDataPrx> rtProxy)-> ZGWebModule::Response
	{
		const auto& paramObj = param.toObject();
        auto it = paramObj.find("id");
		if (it == paramObj.end())
			return errorObject("Missing id.");
		const auto& idArray = it.value().toArray();
		if (idArray.isEmpty())
			return errorObject("Empty id.");
		ZG6000::StringList listID;
		for (const auto id : idArray)
		{
			QString strID = id.toString();
			listID.push_back(strID.toStdString());
		}
        std::string json;
        ZG6000::ErrorInfo e;
		it = paramObj.find("fields");
		if (it == paramObj.end())
        {
            if (!rtProxy->mgetDataByIDToJson(tableName.toStdString(), listID, json, e))
            {
                ZGLOG_ERROR(e);
                return errorObject(e.errDesc.c_str());
            }
        }
        else
        {
            const auto& fieldArray = it.value().toArray();
            if (fieldArray.isEmpty())
                return errorObject("Empty fields");
            ZG6000::StringList listField;
            for (const auto field : fieldArray)
            {
                QString strField = field.toString();
                listField.push_back(strField.toStdString());
            }
            if (!rtProxy->mgetDataByFieldsToJson(tableName.toStdString(), listID, listField, json, e))
            {
                ZGLOG_ERROR(e);
                return errorObject(e.errDesc.c_str());
            }
        }
		QJsonParseError parseError;
		QJsonDocument document = QJsonDocument::fromJson(json.c_str(), &parseError);
		if (parseError.error != QJsonParseError::NoError)
			return errorObject(parseError.errorString());
		return replyObject(document.array());
	});
}

ZGWebModule::Response ZGRTWebModule::addTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param)
{
	return operateTable(tableName, param, [&](const std::shared_ptr<ZG6000::ZGSPRTDataPrx> rtProxy)-> ZGWebModule::Response
	{
		ZG6000::StringList listID;
		ZG6000::ListStringMap listRecord;
		if (!getParamRecords(param, listID, listRecord))
			return errorObject("Invalid param.");
		ZG6000::ErrorInfo e;
		if (!rtProxy->minsertDataByFieldsFromListMap(tableName.toStdString(), listID, listRecord, e))
		{
			ZGLOG_ERROR(e);
			return errorObject(e.errDesc.c_str());
		}
		return replyObject("");
	});
}

ZGWebModule::Response ZGRTWebModule::updateTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param)
{
	return operateTable(tableName, param, [&](const std::shared_ptr<ZG6000::ZGSPRTDataPrx> rtProxy)-> ZGWebModule::Response
	{
		ZG6000::StringList listID;
		ZG6000::ListStringMap listRecord;
		if (!getParamRecords(param, listID, listRecord))
			return errorObject("Invalid param.");
		ZG6000::ErrorInfo e;
        if (listID.size() > 1)
        {
            if (!rtProxy->mupdateDataByFieldsFromListMap(tableName.toStdString(), listID, listRecord, e))
            {
                ZGLOG_ERROR(e);
                return errorObject(ZGJson::convertToJson(e).c_str());
            }
        }
        else if (listID.size() == 1)
        {
            if (!rtProxy->updateDataByIDFromMap(tableName.toStdString(), listID[0], listRecord[0], e))
            {
                ZGLOG_ERROR(e);
                return errorObject(ZGJson::convertToJson(e).c_str());
            }
        }
		return replyObject("");
	});
}

ZGWebModule::Response ZGRTWebModule::deleteTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param)
{
	return operateTable(tableName, param, [&](const std::shared_ptr<ZG6000::ZGSPRTDataPrx> rtProxy)-> ZGWebModule::Response
	{
		const auto& paramObj = param.toObject();
		auto it = paramObj.find("id");
		if (it == paramObj.end())
			return errorObject("Missing id.");
		const auto& idArray = it.value().toArray();
		if (idArray.isEmpty())
			return errorObject("Empty id.");
		ZG6000::StringList listID;
		QJsonArray::const_iterator idIt = idArray.begin();
		while (idIt != idArray.end())
		{
			const auto& strID = idIt->toString();
			listID.push_back(strID.toStdString());
			++idIt;
		}
		ZG6000::ErrorInfo e;
		if (!rtProxy->mdeleteDataByID(tableName.toStdString(), listID, e))
		{
			ZGLOG_ERROR(e);
			return errorObject(e.errDesc.c_str());
		}
		return replyObject("");
	});
}

ZGWebModule::Response ZGRTWebModule::getKeysValue(const QJsonValue& param)
{
	if (!param.isArray())
		return errorObject(u8"不正确的参数格式");
	const auto& keyArray = param.toArray();
	ZG6000::StringList listKey;
	foreach(const auto & key, keyArray)
	{
		listKey.push_back(key.toString().toStdString());
	}
	ZG6000::StringList listValue;
	ZG6000::ErrorInfo e;
	if (!ZGProxyCommon::mgetDataByKey(listKey, listValue, e))
		return errorObject(ZGJson::convertToJson(e).c_str());
	QJsonObject object;
	for (size_t i = 0; i < listKey.size(); ++i)
	{
		object.insert(listKey[i].c_str(), listValue[i].c_str());
	}
	return replyObject(object);
}

ZGWebModule::Response ZGRTWebModule::setKeysValue(const QJsonValue& param)
{
	if (!param.isArray())
		return errorObject(u8"不正确的参数格式");
	const auto& objArray = param.toArray();
	ZG6000::StringMap mapKey;
	foreach(const auto & obj, objArray)
	{
		const auto& jsonObject = obj.toObject();
		mapKey.insert(std::make_pair(jsonObject["key"].toString().toStdString(),
			jsonObject["value"].toString().toStdString()));
	}
	if (!ZGProxyCommon::msetDataByKey(mapKey))
		return errorObject(u8"更新数据出错");
	return replyObject("");
}

ZGWebModule::Response ZGRTWebModule::operateTable(const QString& tableName, const QJsonValue& param, const std::function<ZGWebModule::Response(const std::shared_ptr<ZG6000::ZGSPRTDataPrx>)> func)
{
	const auto& rtProxy = ZGProxyMng::instance()->getProxySPRTData();
	if (rtProxy == nullptr)
		return errorObject("Invalid proxy.");
	try
	{
		return func(rtProxy);
	}
	catch (const Ice::Exception& e)
	{
		return errorObject(e.what());
	}
}

bool ZGRTWebModule::getParamRecords(const QJsonValue& param, ZG6000::StringList& listID, ZG6000::ListStringMap& listRecord)
{
	if (!param.isArray())
		return false;
	const auto& paramArray = param.toArray();
	for (const auto value : paramArray)
	{
		const auto& recordObj = value.toObject();
		auto it = recordObj.find("id");
		if (it == recordObj.end())
			continue;
		ZG6000::StringMap record;
		it = recordObj.begin();
		while (it != recordObj.end())
		{
			QString key = it.key();
			QString val = it.value().toString();
			if (key == "id")
				listID.push_back(val.toStdString());
			else
				record.insert(std::make_pair(key.toStdString(), val.toStdString()));
			++it;
		}
		listRecord.push_back(record);
	}
	return true;
}


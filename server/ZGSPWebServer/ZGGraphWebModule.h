#ifndef ZGGRAPHWEBMODULE_H
#define ZGGRAPHWEBMODULE_H

#include "ZGWebModule.h"

class ZGGraphWebModule : public ZGWebModule
{
    Q_OBJECT
public:
    explicit ZGGraphWebModule(QObject *parent = nullptr);

public:
    static Response on_hmi_cellcontent_get(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_cell_create(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_cell_delete(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_cell_update(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_itemcontent_get(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_item_create(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_item_update(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_item_delete(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_page_get(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_page_index_update(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_pagecontent_get(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_page_logical(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_page_create(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_page_update(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_page_delete(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_page_major_get(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_page_major_update(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_jscontent_get(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_js_create(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_js_update(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_js_delete(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_csscontent_get(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_css_create(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_css_update(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_css_delete(const QVariantMap& headers, const QJsonValue& param);
    static Response on_hmi_file_delete(const QVariantMap& headers, const QJsonValue& param);

private:
    struct ForeignField
    {
        QString table;
        QString field;
        QString name;
    };
    static Response getFileContent(const QJsonValue& param, const QMap<QString, ForeignField>& mapField, const std::string tableName);
    static Response createFile(const QJsonValue& param, const std::string tableName);
    static Response updateFile(const QJsonValue& param, const std::string tableName);
    static Response removeFile(const QJsonValue& param, const std::string tableName);
    static bool fetchFileFromDatabase(const QString &fileContentID, QString& content);
};

#endif // ZGGRAPHWEBMODULE_H

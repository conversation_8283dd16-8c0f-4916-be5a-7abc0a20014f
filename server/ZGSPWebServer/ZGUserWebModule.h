#ifndef ZGUSERWEBMODULE_H
#define ZGUSERWEBMODULE_H

#include "ZGWebModule.h"

class ZGUserWebModule : public ZGWebModule
{
	Q_OBJECT
public:
	explicit ZGUserWebModule(QObject* parent = nullptr);
    static Response on_login(const QString& clientID, const QVariantMap& headers, const QJsonValue& param);
    static Response on_logout(const QString& clientID, const QVariantMap &headers, const QJsonValue& param);
    static Response on_password_verify(const QString& clientID, const QVariantMap& headers, const QJsonValue& param);
    static Response on_user_get(const QString& clientID, const QVariantMap &headers, const QJsonValue& param);
};

#endif // ZGUSERWEBMODULE_H

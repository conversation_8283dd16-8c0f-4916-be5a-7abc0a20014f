#ifndef ZGRTWEBMODULE_H
#define ZGRTWEBMODULE_H

#include "ZGWebModule.h"
#include "ZGProxyMng.h"

class ZGRTWebModule : public ZGWebModule
{
    Q_OBJECT
public:
    static Response getTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response addTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response updateTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response deleteTableRecords(const QString& tableName, const QVariantMap& headers, const QJsonValue& param);
    static Response getKeysValue(const QJsonValue& param);
    static Response setKeysValue(const QJsonValue& param);

private:
    static Response operateTable(const QString& tableName, const QJsonValue& param, 
        const std::function<Response(const std::shared_ptr<ZG6000::ZGSPRTDataPrx>)> func);
    static bool getParamRecords(const QJsonValue& param, ZG6000::StringList& listID, ZG6000::ListStringMap& listRecord);
};

#endif // ZGRTWEBMODULE_H

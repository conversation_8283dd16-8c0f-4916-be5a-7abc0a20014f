#include "ZGDBWebModule.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"
#include "ZGJson.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QMap>

std::unordered_map<QString, QJsonObject> ZGDBWebModule::m_mapTableParam;

ZGWebModule::Response ZGDBWebModule::getTableRecordsCount(const QString& tableName,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param)
{
    return execSqlStatement(tableName, param,
        [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
        {
            QString sql = "SELECT COUNT(*) FROM " + tableName;
            const auto& paramObj = param.toObject();
            const auto& it = paramObj.find("condition");
            if (it != paramObj.end() && !it.value().toString().isEmpty())
                sql += " WHERE " + it.value().toString();
            std::string strResult;
            ZG6000::ErrorInfo e;
            if (!dbProxy->execQuerySqlFieldToValue(sql.toStdString(), strResult, e))
            {
                ZGLOG_ERROR(e);
                return errorObject(e.errDesc.c_str());
            }
            return replyObject(strResult.c_str());
        });
}

ZGWebModule::Response ZGDBWebModule::getTableRecords(const QString& tableName,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param)
{
    return execSqlStatement(tableName, param,
        [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
        {
            const auto& paramObj = param.toObject();
            auto it = paramObj.find("fields");
            QString sql;
            if (it == paramObj.end())
                sql = "SELECT *";
            else
            {
                const auto& fields = it.value();
                const auto& fieldArray = fields.toArray();
                if (fieldArray.isEmpty())
                    return errorObject("Empty fields.");
                sql = "SELECT ";
                for (const auto field: fieldArray)
                {
                    QString fieldValue = field.toString();
                    sql += fieldValue + ",";
                }
                sql.chop(1);
            }
            sql += " FROM " + tableName;
            it = paramObj.find("condition");
            if (it != paramObj.end() && !it.value().toString().isEmpty())
                sql += " WHERE " + it.value().toString();
            std::string json;
            ZG6000::ErrorInfo e;
            if (!dbProxy->execQuerySqlToJson(sql.toStdString(), json, e))
            {
                ZGLOG_ERROR(e);
                return errorObject(e.errDesc.c_str());
            }
            QJsonParseError parseError;
            QJsonDocument document = QJsonDocument::fromJson(json.c_str(), &parseError);
            if (parseError.error != QJsonParseError::NoError)
                return errorObject(parseError.errorString());
            return replyObject(document.array());
        });
}

ZGWebModule::Response ZGDBWebModule::addTableRecords(const QString& tableName,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param)
{
    return execSqlStatement(tableName, param,
        [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
        {
            const auto& paramArray = param.toArray();
            ZG6000::ListStringMap listRecord;
            for (const auto value: paramArray)
            {
                const auto& recordObj = value.toObject();
                ZG6000::StringMap record;
                auto it = recordObj.begin();
                while (it != recordObj.end())
                {
                    QString key = it.key();
                    QString val = it.value().toString();
                    record.insert(std::make_pair(key.toStdString(), val.toStdString()));
                    ++it;
                }
                listRecord.push_back(record);
            }
            ZG6000::ErrorInfo e;
            if (!dbProxy->insertDataByTableFromListMapTwoWay(tableName.toStdString(), listRecord, e))
                return errorObject(ZGJson::convertToJson(e).c_str());
            return replyObject("");
        });
}

ZGWebModule::Response ZGDBWebModule::addTableRecord(const QString& tableName,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param)
{
    return execSqlStatement(tableName, param,
        [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            ZG6000::StringMap record;
            auto it = object.begin();
            while (it != object.end())
            {
                QString key = it.key();
                QString val = it.value().toString();
                record.insert(std::make_pair(key.toStdString(), val.toStdString()));
                ++it;
            }
            ZG6000::ListStringMap listRecord;
            listRecord.push_back(record);
            ZG6000::ErrorInfo e;
            if (!dbProxy->insertDataByTableFromListMapTwoWay(tableName.toStdString(), listRecord, e))
                return errorObject(ZGJson::convertToJson(e).c_str());
            return replyObject("");
        });
}

ZGWebModule::Response ZGDBWebModule::addTableRecordWithResult(const QString& tableName,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param)
{
    return execSqlStatement(tableName, param,
        [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
        {
            const auto& object = param.toObject();
            ZG6000::StringMap record;
            auto it = object.begin();
            while (it != object.end())
            {
                QString key = it.key();
                QString val = it.value().toString();
                record.insert(std::make_pair(key.toStdString(), val.toStdString()));
                ++it;
            }
            // TODO: 调用数据服务接口
            int id = -1;
            ZG6000::ErrorInfo e;
            if (!dbProxy->insertDataByTableFromMap(tableName.toStdString(), record, id, e))
                return errorObject(ZGJson::convertToJson(e).c_str());
            return replyObject(QString::number(id));
        });
}

ZGWebModule::Response ZGDBWebModule::updateTableRecords(const QString& tableName,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param)
{
    return execSqlStatement(tableName, param,
        [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
        {
            const auto& paramArray = param.toArray();
            if (paramArray.isEmpty())
                return errorObject("Empty param.");
            ZG6000::StringList listSql;
            for (auto it = paramArray.begin(); it != paramArray.end(); ++it)
            {
                const auto& paramObj = it->toObject();
                const auto& dataObj = paramObj["data"].toObject();
                QString fields;
                for (auto member = dataObj.begin(); member != dataObj.end(); ++member)
                {
                    const auto& fieldName = member.key();
                    const auto& fieldValue = member.value().toVariant().toString();
                    fields += QString("%1='%2',").arg(fieldName).arg(fieldValue);
                }
                fields.chop(1);
                QString condition;
                auto itCond = paramObj.find("condition");
                if (itCond == paramObj.end())
                    return errorObject("Missing condition.");
                condition += itCond.value().toString();
                QString sql = QString("UPDATE %1 SET %2 WHERE %3;").arg(tableName).arg(fields).arg(condition);
                listSql.push_back(sql.toStdString());
            }
            ZG6000::ErrorInfo e;
            if (!dbProxy->execBatchSql(listSql, e))
            {
                errorObject(ZGJson::convertToJson(e).c_str());
            }
            return replyObject("");
        });
}

ZGWebModule::Response ZGDBWebModule::deleteTableRecords(const QString& tableName,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param)
{
    return execSqlStatement(tableName, param,
        [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
        {
            const auto& paramObj = param.toObject();
            const auto& it = paramObj.find("condition");
            if (it == paramObj.end())
                return errorObject("Missing condition.");
            QString sql = "DELETE FROM " + tableName + " WHERE " + it.value().toString();
            ZG6000::ErrorInfo e;
            if (!dbProxy->execSql(sql.toStdString(), e))
            {
                ZGLOG_ERROR(e);
                return errorObject(e.errDesc.c_str());
            }
            return replyObject("");
        });
}

ZGWebModule::Response ZGDBWebModule::getTableParam(const QString& tableName,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param)
{
    // TODO: 暂时屏蔽缓存，便于调试
    // const auto& pair = m_mapTableParam.find(tableName);
    // if (pair != m_mapTableParam.end())
    //     return replyObject(pair->second);
    QJsonObject tableObj;
    QString errMsg;
    if (!getParamFromTable(tableName, tableObj, errMsg))
        return errorObject(errMsg);
    QJsonArray array;
    if (!getFieldsParamFromTable(tableName, array, errMsg))
        return errorObject(errMsg);
    QJsonObject object;
    object.insert("table", tableObj);
    object.insert("fields", array);
    // TODO: 暂时屏蔽缓存，便于调试
    // m_mapTableParam.insert(std::make_pair(tableName, object));
    return replyObject(object);
}

ZGWebModule::Response ZGDBWebModule::getFieldsParam(const QVariantMap& headers,
                                                  const QJsonValue& param)
{
    const auto& listTable = param.toArray();
    QJsonObject object;
    for (auto tableRef: listTable)
    {
        const auto& tableName = tableRef.toString();
        QJsonArray array;
        QString errMsg;
        if (!getFieldsParams(tableName, array, errMsg))
            return errorObject(errMsg);
        object[tableName] = array;
    }
    return replyObject(object);
}

ZGWebModule::Response ZGDBWebModule::execQuery(const QVariantMap& headers,
                                             const QJsonValue& param)
{
    return execSqlStatement("", param, [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
    {
        const auto& sql = param.toString();
        if (sql.isEmpty())
            return errorObject("Empty param.");
        ZG6000::ErrorInfo e;
        ZG6000::ListStringList listRecord;
        if (!dbProxy->execQuerySqlToListList(sql.toStdString(), listRecord, e))
        {
            ZGLOG_ERROR(e);
            return errorObject(ZGJson::convertToJson(e).c_str());
        }
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonParseError parseError;
        QJsonDocument document = QJsonDocument::fromJson(json.c_str(), &parseError);
        if (parseError.error != QJsonParseError::NoError)
            return errorObject(parseError.errorString());
        return replyObject(document.array());
    });
}

ZGWebModule::Response ZGDBWebModule::execQueryToObject(const QVariantMap& headers,
                                                     const QJsonValue& param)
{
    return execSqlStatement("", param, [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
    {
        const auto& sql = param.toString();
        if (sql.isEmpty())
            return errorObject("Empty param.");
        ZG6000::ErrorInfo e;
        ZG6000::ListStringMap listRecord;
        if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listRecord, e))
        {
            ZGLOG_ERROR(e);
            return errorObject(ZGJson::convertToJson(e).c_str());
        }
        const auto& recordArray = listStringMapToArray(listRecord);
        return replyObject(recordArray);
    });
}

ZGWebModule::Response ZGDBWebModule::execQueryWithCount(const QVariantMap& headers,
                                                      const QJsonValue& param)
{
    return execSqlStatement("", param, [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
    {
        if (!param.isArray())
            return errorObject(QStringLiteral("无效的输入参数"));
        const auto& jsonArray = param.toArray();
        if (jsonArray.size() < 2)
            return errorObject(QStringLiteral("参数个数不正确"));
        const auto& sqlCount = jsonArray[0].toString();
        if (sqlCount.isEmpty())
            return errorObject("Empty param.");
        ZG6000::ErrorInfo e;
        std::string result;
        if (!dbProxy->execQuerySqlFieldToValue(sqlCount.toStdString(), result, e))
        {
            ZGLOG_ERROR(e);
            return errorObject(ZGJson::convertToJson(e).c_str());
        }
        const auto& sqlQuery = jsonArray[1].toString();
        ZG6000::ListStringList listRecord;
        if (!dbProxy->execQuerySqlToListList(sqlQuery.toStdString(), listRecord, e))
        {
            ZGLOG_ERROR(e);
            return errorObject(ZGJson::convertToJson(e).c_str());
        }
        const auto& json = ZGJson::convertToJson(listRecord);
        QJsonParseError parseError;
        QJsonDocument document = QJsonDocument::fromJson(json.c_str(), &parseError);
        if (parseError.error != QJsonParseError::NoError)
            return errorObject(parseError.errorString());
        QJsonObject object;
        object.insert("count", result.c_str());
        object.insert("item", document.array());
        return replyObject(object);
    });
}

ZGWebModule::Response ZGDBWebModule::execInsertSql(const QVariantMap& headers,
                                                 const QJsonValue& param)
{
    return execSqlStatement("", param, [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
    {
        const auto& sql = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        int id;
        if (!dbProxy->execInsertSql(sql, id, e))
        {
            return errorObject(ZGJson::convertToJson(e).c_str());
        }
        return replyObject(id);
    });
}

ZGWebModule::Response ZGDBWebModule::execCommands(const QVariantMap& headers,
                                                const QJsonValue& param)
{
    return execSqlStatement("", param, [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
    {
        const auto paramArray = param.toArray();
        if (paramArray.isEmpty())
            return errorObject("Empty param.");
        ZG6000::StringList listSql;
        for (auto it = paramArray.begin(); it != paramArray.end(); ++it)
        {
            QString strSql = it->toString();
            listSql.push_back(strSql.toStdString());
        }
        ZG6000::ErrorInfo e;
        if (!dbProxy->execBatchSql(listSql, e))
        {
            ZGLOG_ERROR(e);
            return errorObject(ZGJson::convertToJson(e).c_str());
        }
        return replyObject("");
    });
}

ZGWebModule::Response ZGDBWebModule::createUuid(const QVariantMap& headers,
                                              const QJsonValue& param)
{
    return execSqlStatement("", param, [&](const std::shared_ptr<ZG6000::ZGSPDBDataPrx> dbProxy)-> ZGWebModule::Response
    {
        int count = param.toString().toInt();
        if (count <= 0)
            return errorObject(QStringLiteral("无效的参数'%1'").arg(param.toString()));
        ZG6000::ErrorInfo e;
        ZG6000::StringList uuids;
        if (!dbProxy->createUUIDs(count, uuids, e))
        {
            ZGLOG_ERROR(e);
            return errorObject(e.errDesc.c_str());
        }
        QJsonArray array;
        for (const auto& uuid: uuids)
        {
            array.append(uuid.c_str());
        }
        return replyObject(array);
    });
}

ZGWebModule::Response ZGDBWebModule::syncData(const QVariantMap& headers,
                                            const QJsonValue& param)
{
    const auto& array = param.toArray();
    ZG6000::StringList listSql;
    for (auto objRef: array)
    {
        const auto& object = objRef.toObject();
        QString errMsg;
        if (!checkRequiredFields(object, {"tableName", "recordID", "data", "oper"}, errMsg))
            return errorObject(errMsg);
        const auto& tableName = object["tableName"].toString().toStdString();
        const auto& oper = object["oper"].toVariant().toInt();
        const auto& id = object["recordID"].toString().toStdString();
        if (oper == 1 || oper == 2)
        {
            const auto& dataObj = object["data"].toObject();
            ZG6000::StringMap data;
            data["id"] = id;
            for (auto it = dataObj.begin(); it != dataObj.end(); ++it)
            {
                data[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            if (oper == 1)
                listSql.push_back(ZGUtils::generateInsertSql(tableName, data));
            else
                listSql.push_back(ZGUtils::generateUpdateSql(tableName, data));
        }
        else if (oper == 3)
        {
            QString sql = QString("DELETE FROM %1 WHERE id = '%2';").arg(tableName.c_str()).arg(id.c_str());
            listSql.push_back(sql.toStdString());
        }
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
        return errorObject(QString("同步数据失败"));
    return replyObject("");
}

ZGWebModule::Response ZGDBWebModule::syncHisData(const QVariantMap& headers,
                                               const QJsonValue& param)
{
    const auto& array = param.toArray();
    ZG6000::StringList listSql;
    for (auto objRef: array)
    {
        const auto& object = objRef.toObject();
        QString errMsg;
        if (!checkRequiredFields(object, {"tableName", "recordID", "data", "oper"}, errMsg))
            return errorObject(errMsg);
        const auto& tableName = object["tableName"].toString().toStdString() + "_" + std::to_string(
            QDateTime::currentDateTime().date().year());
        const auto& oper = object["oper"].toVariant().toInt();
        const auto& id = object["recordID"].toString().toStdString();
        if (oper == 1 || oper == 2)
        {
            const auto& dataObj = object["data"].toObject();
            ZG6000::StringMap data;
            data["id"] = id;
            for (auto it = dataObj.begin(); it != dataObj.end(); ++it)
            {
                data[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            if (oper == 1)
            {
                listSql.push_back(ZGUtils::generateInsertSql(tableName, data));
            }
            else
                listSql.push_back(ZGUtils::generateUpdateSql(tableName, data));
        }
        else if (oper == 3)
        {
            QString sql = QString("DELETE FROM %1 WHERE id = '%2';").arg(tableName.c_str()).arg(id.c_str());
            listSql.push_back(sql.toStdString());
        }
    }
    if (!ZGProxyCommon::execBatchSql(listSql, true))
        return errorObject(QString("同步数据失败"));
    return replyObject("");
}

ZGWebModule::Response ZGDBWebModule::getAppNodeLayer(const QVariantMap& headers,
                                                   const QJsonValue& param)
{
    QString strAppNodeID = param.toString();
    QJsonArray array;
    std::string appNodeID = strAppNodeID.toStdString();
    writeNodeLayer(appNodeID, array);
    return replyObject(array);
}

ZGWebModule::Response ZGDBWebModule::execSqlStatement(const QString& tableName,
                                                      const QJsonValue& param,
                                                      std::function<Response(
                                                          const std::shared_ptr<ZG6000::ZGSPDBDataPrx>)> func)
{
    auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
    if (dbProxy == nullptr)
        return errorObject("invalid db proxy.");
    try
    {
        return func(dbProxy);
    }
    catch (const Ice::Exception& e)
    {
        return errorObject(e.what());
    }
}

bool ZGDBWebModule::getParamFromTable(const QString& tableName,
                                      QJsonObject& object,
                                      QString& errMsg)
{
    std::string sql = "SELECT name, tableParam FROM sp_table_info WHERE id = '" + tableName.toStdString() + "'";
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql, listRecord))
    {
        errMsg = QStringLiteral("获取表记录信息失败");
        return false;
    }
    if (listRecord.empty())
    {
        errMsg = QStringLiteral("表信息表中无法找到表'%1'的信息").arg(tableName);
        return false;
    }
    try
    {
        const auto& name = ZGUtils::get(listRecord[0], "name");
        const auto& param = ZGUtils::get(listRecord[0], "tableParam");
        if (param.empty())
        {
            errMsg = QStringLiteral("未配置表'%1'表信息").arg(tableName);
            return false;
        }
        QJsonParseError parseError;
        const auto& document = QJsonDocument::fromJson(param.c_str(), &parseError);
        if (parseError.error != QJsonParseError::NoError)
        {
            errMsg = QStringLiteral("表'%1'表参数解析出错: '%2'").arg(tableName).arg(parseError.errorString());
            return false;
        }
        if (!document.isObject())
        {
            errMsg = QStringLiteral("表'%1'无效的表参数: '%2'").arg(tableName).arg(param.c_str());
            return false;
        }
        object = document.object();
        object.insert("name", tableName);
        object.insert("des", name.c_str());
    }
    catch (const std::exception& e)
    {
        errMsg = e.what();
        return false;
    }
    return true;
}

bool ZGDBWebModule::getFieldsParamFromTable(const QString& tableName,
                                            QJsonArray& array,
                                            QString& errMsg)
{
    std::string sql = "SHOW FULL COLUMNS FROM " + tableName.toStdString();
    const auto& dbProxy = ZGProxyMng::instance()->getProxySPDBData();
    if (dbProxy == nullptr)
    {
        errMsg = QString("获取数据库代理失败");
        return false;
    }
    ZG6000::ListStringMap listRecord;
    try
    {
        ZG6000::ErrorInfo e;
        if (!dbProxy->execQuerySqlToListMap(sql, listRecord, e))
        {
            errMsg = ZGJson::convertToJson(e).c_str();
            return false;
        }
    }
    catch (const Ice::Exception& e)
    {
        errMsg = e.what();
        return false;
    }
    sql = "SELECT fieldParam FROM sp_table_info WHERE id = '" + tableName.toStdString() + "'";
    std::string json;
    if (!ZGProxyCommon::execQuerySqlField(sql, json))
    {
        errMsg = QStringLiteral("获取表'%1'字段信息失败");
        return false;
    }
    if (json.empty())
    {
        errMsg = QStringLiteral("未配置表'%1'字段信息").arg(tableName);
        return false;
    }
    QJsonParseError parseError;
    const auto& document = QJsonDocument::fromJson(json.c_str(), &parseError);
    if (parseError.error != QJsonParseError::NoError)
    {
        errMsg = QStringLiteral("表'%1'字段配置格式不正确, '%2'").arg(tableName).arg(parseError.errorString());
        return false;
    }
    try
    {
        for (const auto& record: listRecord)
        {
            const auto& id = ZGUtils::get(record, "Field");
            QJsonObject field;
            if (document.isObject())
            {
                const auto& object = document.object();
                const auto& it = object.find(id.c_str());
                if (it == object.end())
                    continue;
                if (it.value().isObject())
                {
                    const auto& fieldObj = it.value().toObject();
                    for (auto itField = fieldObj.begin(); itField != fieldObj.end(); ++itField)
                    {
                        field.insert(itField.key(), itField.value());
                    }
                }
            }
            auto type = ZGUtils::get(record, "Type");
            size_t pos = type.find_first_of("(");
            if (pos != std::string::npos)
                type = type.substr(0, pos);
            const auto& null = ZGUtils::get(record, "Null");
            const auto& key = ZGUtils::get(record, "Key");
            const auto& _default = ZGUtils::get(record, "Default");
            const auto& extra = ZGUtils::get(record, "Extra");
            field.insert("type", type.c_str());
            field.insert("isNoEmpty", null == "NO");
            field.insert("isPrimary", key == "PRI");
            field.insert("isUnique", (key == "PRI" || key == "UNI"));
            field.insert("default", _default.c_str());
            field.insert("autoInc", (extra.find("auto_increment") != std::string::npos));
            QJsonObject fieldObj;
            fieldObj.insert(id.c_str(), field);
            array.append(fieldObj);
        }
    }
    catch (const std::exception& e)
    {
        errMsg = e.what();
        return false;
    }
    return true;
}

bool ZGDBWebModule::getFieldsParams(const QString& tableName,
                                    QJsonArray& array,
                                    QString& errMsg)
{
    std::string sql = "SHOW FULL COLUMNS FROM " + tableName.toStdString();
    const auto& dbProxy = ZGProxyMng::instance()->getProxySPDBData();
    if (dbProxy == nullptr)
    {
        errMsg = QString("获取数据库代理失败");
        return false;
    }
    ZG6000::ListStringMap listRecord;
    try
    {
        ZG6000::ErrorInfo e;
        if (!dbProxy->execQuerySqlToListMap(sql, listRecord, e))
        {
            errMsg = ZGJson::convertToJson(e).c_str();
            return false;
        }
    }
    catch (const Ice::Exception& e)
    {
        errMsg = e.what();
        return false;
    }
    try
    {
        for (const auto& record: listRecord)
        {
            const auto& id = ZGUtils::get(record, "Field");
            const auto& type = ZGUtils::get(record, "Type");
            const auto& null = ZGUtils::get(record, "Null");
            const auto& key = ZGUtils::get(record, "Key");
            const auto& _default = ZGUtils::get(record, "Default");
            const auto& extra = ZGUtils::get(record, "Extra");
            QJsonObject field;
            field.insert("type", type.c_str());
            field.insert("isNoEmpty", null == "NO");
            field.insert("isPrimary", key == "PRI");
            field.insert("isUnique", (key == "PRI" || key == "UNI"));
            field.insert("default", _default.c_str());
            field.insert("autoInc", (extra.find("auto_increment") != std::string::npos));
            QJsonObject fieldObj;
            fieldObj.insert(id.c_str(), field);
            array.append(fieldObj);
        }
    }
    catch (const std::exception& e)
    {
        errMsg = e.what();
        return false;
    }
    return true;
}

void ZGDBWebModule::writeNodeLayer(const std::string& parentNodeID,
                                   QJsonArray& array)
{
    std::string sql;
    if (parentNodeID.empty())
        sql =
            "SELECT appNodeID FROM sp_param_appnode_layer WHERE parentAppNodeID = '' OR parentAppNodeID IS NULL ORDER BY itemIndex";
    else
        sql = "SELECT appNodeID FROM sp_param_appnode_layer WHERE parentAppNodeID = '" + parentNodeID +
            "' ORDER BY itemIndex";
    ZG6000::StringList listAppNodeID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listAppNodeID))
    {
        ZGLOG_ERROR(QString("execQuerySqlCol error."));
        return;
    }
    for (const auto& appNodeId: listAppNodeID)
    {
        sql = "SELECT name FROM sp_param_appnode WHERE id = '" + appNodeId + "'";
        std::string appNodeName;
        if (!ZGProxyCommon::execQuerySqlField(sql, appNodeName))
        {
            ZGLOG_WARN(QString("Can't find appnode %1").arg(appNodeId.c_str()));
            continue;
        }
        QJsonObject obj{};
        obj.insert("id", appNodeId.c_str());
        obj.insert("text", appNodeName.c_str());
        QJsonArray childArray;
        writeNodeLayer(appNodeId, childArray);
        if (!childArray.isEmpty())
            obj.insert("nodes", childArray);
        array.append(obj);
    }
}

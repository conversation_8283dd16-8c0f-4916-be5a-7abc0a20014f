#include "ZGClientWebModule.h"
#include "ZGSPWebServerMng.h"
#include "ZGJson.h"
#include "ZGProxyCommon.h"
#include "ZGProxyMng.h"
#include "ZGServerCommon.h"

#include <QJsonDocument>
#include <QJsonArray>

ZGClientWebModule::ZGClientWebModule(QObject *parent)
    : ZGWebModule{parent}
{

}

ZGWebModule::Response ZGClientWebModule::on_client_test(const QString &clientID, const QVariantMap &headers, const QJsonValue &param)
{
    return replyObject("");
}

ZGWebModule::Response ZGClientWebModule::on_client_register(const QString& clientID, const QVariantMap& headers, const QJsonValue& param)
{
    QString errMsg;
    QJsonArray tokenArray;
    if (!verifyAuthorization("", headers, {"ZG_HP_REGISTER"}, tokenArray, errMsg))
        return errorObject(errMsg);
    if (tokenArray.isEmpty())
        return errorObject(QStringLiteral("缺少授权令牌"));
	if (!param.isObject())
		return errorObject(QStringLiteral("无效的参数"));
	const auto& object = param.toObject();
    if (!checkRequiredFields(object, {"appNodeID", "subsystemID", "name", "logicalName"}, errMsg))
		return errorObject(errMsg);
	const auto& appNodeID = object["appNodeID"].toString().toStdString();
    const auto& subsystemID = object["subsystemID"].toString().toStdString();
	const auto& name = object["name"].toString().toStdString();
	const auto& logicalName = object["logicalName"].toString().toStdString();
	const auto& it = object.find("clientTypeID");
	std::string clientTypeID;
	if (it != object.end())
		clientTypeID = it.value().toString().toStdString();
	else
		clientTypeID = "ZG_CT_WEB";
	auto clientPrx = ZGProxyMng::instance()->getProxySPClientManager();
	if (clientPrx == nullptr)
        return errorObject(QStringLiteral("获取客户端管理服务代理对象失败"));
	try
	{
        const auto& tokenObj = tokenArray.first().toObject();
        const auto& permissions = tokenObj.value("permissions").toArray();
        int rtActivated = 0;
        // 检查permissions中是否包含ZG_HP_MAINTAIN权限
        if (permissions.contains("ZG_HP_MAINTAIN"))
            rtActivated = 1;
		ZG6000::ErrorInfo e;
        if (!clientPrx->registerClient(clientID.toStdString(), name, clientTypeID, logicalName, rtActivated, appNodeID, subsystemID, e))
			return errorObject(ZGJson::convertToJson(e).c_str());
	}
	catch (const Ice::Exception& e)
	{
		return errorObject(e.what());
    }
    return replyObject("");
}

ZGWebModule::Response ZGClientWebModule::on_client_bind(const QString &clientID, const QVariantMap &headers, const QJsonValue &param)
{
    QString errMsg;
    QJsonArray tokenArray;
    if (!verifyAuthorization(clientID, headers, {"ZG_HP_REGISTER"}, tokenArray, errMsg))
        return errorObject(errMsg);
    const auto& newClientID = param.toString().toStdString();
	auto clientPrx = ZGProxyMng::instance()->getProxySPClientManager();
	if (clientPrx == nullptr)
		return errorObject(QStringLiteral("获取应用管理器代理失败"));
	try
	{
		ZG6000::ErrorInfo e;
        if (!clientPrx->bindClient(newClientID, e))
			return errorObject(ZGJson::convertToJson(e).c_str());
        return replyObject("");
	}
	catch (const Ice::Exception& e)
	{
		return errorObject(e.what());
    }
}

ZGWebModule::Response ZGClientWebModule::on_client_verify(const QString &clientID, const QVariantMap &headers, const QJsonValue &param)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!checkRequiredFields(object, {"user", "password", "power"}, errMsg))
        return errorObject(errMsg);
    const auto& user = object["user"].toString().toStdString();
    const auto& password = object["password"].toString().toStdString();
    const auto& power = object["power"].toString().toStdString();
    auto powerPrx = ZGProxyMng::instance()->getProxySPPowerVerify();
    if (powerPrx == nullptr)
        return errorObject(QStringLiteral("获取权限验证服务代理对象失败"));
    try
    {
        ZG6000::ErrorInfo e;
        if (!powerPrx->verifyByPassword("", user, password, "", power, e))
            return errorObject(ZGJson::convertToJson(e).c_str());
        return replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return errorObject(e.what());
    }
}

ZGWebModule::Response ZGClientWebModule::on_client_list(const QString &clientID, const QVariantMap& headers, const QJsonValue &param)
{
    const auto& clientTypeID = param.toString().toStdString();
    QString sql = QString("SELECT a.id, a.name AS clientName, b.name AS appNodeName, a.rtIsBound FROM sp_param_client a "
            "LEFT JOIN sp_param_appnode b ON a.rtAppNodeID = b.id "
            "WHERE a.clientTypeID = '%1' AND a.rtIsActivate = '1' ORDER BY a.id").arg(clientTypeID.c_str());
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord))
        return ZGWebModule::errorObject(QStringLiteral("获取客户端信息失败"));
    std::string json = ZGJson::convertToJson(listRecord);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGClientWebModule::on_client_appnode_get(const QString& clientID, const QVariantMap &headers, const QJsonValue& param)
{
    QJsonArray array;
    ZG6000::StringList listAppNodeID;
    // 获取顶层应用节点，即parentAppNodeID为空的应用节点
    QString sql = "SELECT appNodeID FROM sp_param_appnode_layer WHERE parentAppNodeID IS NULL OR parentAppNodeID = '' ORDER BY itemIndex";
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listAppNodeID))
        return ZGWebModule::errorObject(QStringLiteral("获取顶层应用节点失败"));
    for (const auto& appNodeID: listAppNodeID)
    {
        writeNodeLayer(appNodeID, array);
    }
    return ZGWebModule::replyObject(array);
}

ZGWebModule::Response ZGClientWebModule::on_client_subsystem_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param)
{
    std::string sql = "SELECT id, name FROM sp_param_subsystem";
	ZG6000::ListStringMap listRecord;
	if (!ZGProxyCommon::execQuerySql(sql, listRecord))
        return ZGWebModule::errorObject(QStringLiteral("获取子系统信息失败"));
	std::string json = ZGJson::convertToJson(listRecord);
	QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGClientWebModule::on_client_state_check(const QString &clientID, const QVariantMap &headers, const QJsonValue &param)
{
    QString errMsg;
    if (!ZGSPWebServerMng::instance()->initBaseService(errMsg))
        return ZGWebModule::errorObject(errMsg);
    return ZGWebModule::replyObject("");
}

ZG6000::StringMap ZGClientWebModule::getSubsystem(const QJsonObject &object)
{
    ZG6000::StringMap mapSubsytem;
    auto itSubsystem = object.begin();
    while (itSubsystem != object.end())
    {
        const auto& subsystemID = itSubsystem.key().toStdString();
        const auto& majorArray = itSubsystem.value().toArray();
        std::string majors;
        for (auto majorRef : majorArray)
        {
            majors += majorRef.toString().toStdString() + ",";
        }
        if (!majors.empty())
            majors.pop_back();
        mapSubsytem.insert(std::make_pair(subsystemID, majors));
        ++itSubsystem;
    }
    return mapSubsytem;
}

void ZGClientWebModule::writeNodeLayer(const std::string &nodeID, QJsonArray &array)
{
    ZG6000::StringMap node;
    std::string sql = "SELECT name, appNodeTypeID, position FROM sp_param_appnode WHERE id = '" + nodeID + "'";
    if (!ZGProxyCommon::execQuerySqlRow(sql, node))
    {
        ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'名称失败").arg(nodeID.c_str()));
        return;
    }
    QJsonObject obj{};
    obj.insert("id", nodeID.c_str());
    obj.insert("text", node["name"].c_str());
    obj.insert("appNodeTypeID", node["appNodeTypeID"].c_str());
    obj.insert("position", node["position"].c_str());
    sql = "SELECT appNodeID FROM sp_param_appnode_layer WHERE parentAppNodeID = '" + nodeID + "' ORDER BY itemIndex";
    ZG6000::StringList listAppNodeID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listAppNodeID))
    {
        ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'层次失败").arg(nodeID.c_str()));
        array.append(obj);
        return;
    }
    if (listAppNodeID.empty())
    {
        array.append(obj);
        return;
    }
    QJsonArray childArray;
    for (const auto& appNodeId: listAppNodeID)
    {
        writeNodeLayer(appNodeId, childArray);
    }
    obj.insert("nodes", childArray);
    array.append(obj);
}


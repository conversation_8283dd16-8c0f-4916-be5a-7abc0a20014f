#include "ZGSPWebServerMng.h"
#include <fstream>
#include <QtConcurrent>
#include <QtHttpServer/QHttpServer>
#include <QSslConfiguration>
#include <QSslCertificate>
#include <QSslKey>
#include <QtHttpServer/QHttpServerResponse>

#include "ZGClientWebModule.h"
#include "ZGDBWebModule.h"
#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"
#include "ZGPubFun.h"
#include "ZGRTWebModule.h"
#include "ZGRuntime.h"
#include "ZGUserWebModule.h"
#include "ZGUtils.h"
#include "ZGSecure.h"
#include "redis/ZGRedisClient.h"

QHttpServer g_httpserver;

static ZGSPWebServerMng* g_pInstance = nullptr;

ZGSPWebServerMng::ZGSPWebServerMng(QObject* parent) : QObject(parent),
                                                      m_httpServer(nullptr)
{
    m_httpServer = new QHttpServer;
}

void ZGSPWebServerMng::initApiHandle()
{
    m_mapTableApi.insert(std::make_pair("/api/db/<arg>/count", ZGDBWebModule::getTableRecordsCount));
    m_mapTableApi.insert(std::make_pair("/api/db/<arg>/get", ZGDBWebModule::getTableRecords));
    m_mapTableApi.insert(std::make_pair("/api/db/<arg>/add", ZGDBWebModule::addTableRecords));
    m_mapTableApi.insert(std::make_pair("/api/db/<arg>/addone", ZGDBWebModule::addTableRecord));
    m_mapTableApi.insert(std::make_pair("/api/db/<arg>/addresult", ZGDBWebModule::addTableRecordWithResult));
    m_mapTableApi.insert(std::make_pair("/api/db/<arg>/update", ZGDBWebModule::updateTableRecords));
    m_mapTableApi.insert(std::make_pair("/api/db/<arg>/delete", ZGDBWebModule::deleteTableRecords));
    m_mapTableApi.insert(std::make_pair("/api/db/<arg>/param", ZGDBWebModule::getTableParam));
    m_mapTableApi.insert(std::make_pair("/api/rt/<arg>/get", ZGRTWebModule::getTableRecords));
    m_mapTableApi.insert(std::make_pair("/api/rt/<arg>/add", ZGRTWebModule::addTableRecords));
    m_mapTableApi.insert(std::make_pair("/api/rt/<arg>/update", ZGRTWebModule::updateTableRecords));
    m_mapTableApi.insert(std::make_pair("/api/rt/<arg>/delete", ZGRTWebModule::deleteTableRecords));
    m_mapValidateApi.insert(std::make_pair("/api/db/query", ZGDBWebModule::execQuery));
    m_mapValidateApi.insert(std::make_pair("/api/db/obj/query", ZGDBWebModule::execQueryToObject));
    m_mapValidateApi.insert(std::make_pair("/api/db/query2", ZGDBWebModule::execQueryWithCount));
    m_mapValidateApi.insert(std::make_pair("/api/db/insert", ZGDBWebModule::execInsertSql));
    m_mapValidateApi.insert(std::make_pair("/api/db/command", ZGDBWebModule::execCommands));
    m_mapValidateApi.insert(std::make_pair("/api/db/uuid", ZGDBWebModule::createUuid));
    m_mapValidateApi.insert(std::make_pair("/api/db/fields/params", ZGDBWebModule::getFieldsParam));
    m_mapValidateApi.insert(std::make_pair("/api/db/data/sync", ZGDBWebModule::syncData));
    m_mapValidateApi.insert(std::make_pair("/api/db/hisdata/sync", ZGDBWebModule::syncHisData));
    m_mapValidateApi.insert(std::make_pair("/api/db/appnode/layer/get", ZGDBWebModule::getAppNodeLayer));
    m_mapDirectApi.insert(std::make_pair("/api/logout", ZGUserWebModule::on_logout));
    m_mapDirectApi.insert(std::make_pair("/api/password/verify", ZGUserWebModule::on_password_verify));
    m_mapDirectApi.insert(std::make_pair("/api/user/get", ZGUserWebModule::on_user_get));
    m_mapDirectApi.insert(std::make_pair("/api/client/test", ZGClientWebModule::on_client_test));
    m_mapDirectApi.insert(std::make_pair("/api/client/register", ZGClientWebModule::on_client_register));
    m_mapDirectApi.insert(std::make_pair("/api/client/bind", ZGClientWebModule::on_client_bind));
    m_mapDirectApi.insert(std::make_pair("/api/client/verify", ZGClientWebModule::on_client_verify));
    m_mapDirectApi.insert(std::make_pair("/api/client/list", ZGClientWebModule::on_client_list));
    m_mapDirectApi.insert(std::make_pair("/api/client/appnode/get", ZGClientWebModule::on_client_appnode_get));
    m_mapDirectApi.insert(std::make_pair("/api/client/subsystem/get", ZGClientWebModule::on_client_subsystem_get));
    m_mapDirectApi.insert(std::make_pair("/api/client/state/check", ZGClientWebModule::on_client_state_check));
}

void ZGSPWebServerMng::initWebModules()
{
    const auto& server = ZGRuntime::instance()->getServerConfig("server");
    auto itPort = server.find("port");
    if (itPort != server.end())
        m_port = itPort.value().toUShort();
    const auto& modules = ZGRuntime::instance()->getServerConfig("module");
    for (auto it = modules.begin(); it != modules.end(); ++it)
    {
        const auto& moduleName = it.key();
        const auto& isEnable = it.value();
        if (isEnable != "1")
            continue;
        QPluginLoader loader(moduleName);
        QObject* object = loader.instance();
        ZGLOG_INFO(QString("Load module '%1'...").arg(moduleName));
        if (object == nullptr)
        {
            ZGLOG_WARN(QString("Load module %1 error.").arg(moduleName));
            continue;
        }
        ZGWebModule* webModule = qobject_cast<ZGWebModule*>(object);
        if (webModule == nullptr)
        {
            ZGLOG_WARN(QString("%1 is not a valid web module.").arg(moduleName));
            continue;
        }
        if (!webModule->initialize())
        {
            ZGLOG_ERROR(QString("%1 initialize error.").arg(moduleName));
            continue;
        }
        const auto& prefix = webModule->prefix();
        m_mapPathModule[prefix] = webModule;
        const auto& monitorTables = webModule->monitorTables();
        if (!monitorTables.isEmpty())
            m_pRtTopic->subscribe(monitorTables);
        for (const auto& monitorTable : monitorTables)
        {
            auto pair = m_mapMonitorTable.find(monitorTable);
            if (pair == m_mapMonitorTable.end())
                m_mapMonitorTable.insert(std::make_pair(monitorTable, std::vector{webModule}));
            else
                pair->second.push_back(webModule);
        }
        connect(m_pRtTopic, &ZGRedisClient::receivedMessage, this, &ZGSPWebServerMng::onReceiveMessage);
        m_pRtTopic->consume();
    }
}

void ZGSPWebServerMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPWebServerMng::onTimer);
}

void ZGSPWebServerMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_cookie", value, 0, 1, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkCookie = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "enable_ssl", value, 0, 1, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_enableSsl = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "encrypt", value, 0, 1, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_encrypt = value;
}

bool ZGSPWebServerMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGSPWebServerMng::initCertification()
{
    if (m_enableSsl)
    {
        ZGLOG_INFO("initCertification...");
        const auto& keyPath = ZGPubFun::getCfgDir() + "/cert/server.key";
        QFile keyFile(keyPath);
        if (!keyFile.exists())
        {
            ZGLOG_ERROR(QString("Can't find key file %1").arg(keyPath));
            return false;
        }
        const auto& certPath = ZGPubFun::getCfgDir() + "/cert/server.crt";
        QFile certFile(certPath);
        if (!certFile.exists())
        {
            ZGLOG_ERROR(QString("Can't find cert file %1").arg(certPath));
            return false;
        }
        if (!keyFile.open(QIODevice::ReadOnly) || !certFile.open(QIODevice::ReadOnly))
        {
            ZGLOG_ERROR("Open key or cert file error.");
            return false;
        }
        QSslKey sslKey(keyFile.readAll(), QSsl::Rsa);
        QSslCertificate sslCert(certFile.readAll());
        QSslConfiguration sslConfig;
        sslConfig.setPrivateKey(sslKey);
        sslConfig.setLocalCertificate(sslCert);
        m_httpServer->sslSetup(sslCert, sslKey);
        keyFile.close();
        certFile.close();
        ZGLOG_INFO("initCertification finished.");
    }
    return true;
}

bool ZGSPWebServerMng::initBaseService(QString &errMsg)
{
    auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
    if (dbProxy == nullptr)
    {
        errMsg = QStringLiteral("获取数据服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    auto rtProxy = ZGProxyMng::instance()->getProxySPRTData();
    if (rtProxy == nullptr)
    {
        errMsg = QStringLiteral("获取实时数据服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    auto deviceProxy = ZGProxyMng::instance()->getProxyMPDeviceProperty();
    if (deviceProxy == nullptr)
    {
        errMsg = QStringLiteral("获取设备属性服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    auto historyProxy = ZGProxyMng::instance()->getProxySPHisDataManager();
    if (historyProxy == nullptr)
    {
        errMsg = QStringLiteral("获取历史数据服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    auto ruleProxy = ZGProxyMng::instance()->getProxyMPRuleEngine();
    if (ruleProxy == nullptr)
    {
        errMsg = QStringLiteral("获取规则引擎服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    auto powerProxy = ZGProxyMng::instance()->getProxySPPowerVerify();
    if (powerProxy == nullptr)
    {
        errMsg = QStringLiteral("获取权限验证服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    auto userProxy = ZGProxyMng::instance()->getProxySPUserManager();
    if (userProxy == nullptr)
    {
        errMsg = QStringLiteral("获取用户管理服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    auto taskProxy = ZGProxyMng::instance()->getProxyOPTaskOutage();
    if (taskProxy == nullptr)
    {
        errMsg = QStringLiteral("获取断电任务服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    auto eventProxy = ZGProxyMng::instance()->getProxySPEventProcess();
    if (eventProxy == nullptr)
    {
        errMsg = QStringLiteral("获取事件处理服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    auto clientProxy = ZGProxyMng::instance()->getProxySPClientManager();
    if (clientProxy == nullptr)
    {
        errMsg = QStringLiteral("获取客户端管理服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    auto messageGatewayProxy = ZGProxyMng::instance()->getProxySPMessageGateway();
    if (messageGatewayProxy == nullptr)
    {
        errMsg = QStringLiteral("获取消息网关服务代理失败");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    try
    {
        auto dbProxyF = dbProxy->checkStateAsync();
        auto rtProxyF = rtProxy->checkStateAsync();
        auto deviceProxyF = deviceProxy->checkStateAsync();
        auto historyProxyF = historyProxy->checkStateAsync();
        auto ruleProxyF = ruleProxy->checkStateAsync();
        auto powerProxyF = powerProxy->checkStateAsync();
        auto userProxyF = userProxy->checkStateAsync();
        auto taskProxyF = taskProxy->checkStateAsync();
        auto eventProxyF = eventProxy->checkStateAsync();
        auto clientProxyF = clientProxy->checkStateAsync();
        auto messageGatewayProxyF = messageGatewayProxy->checkStateAsync();
        try
        {
            if (!dbProxyF.get())
            {
                errMsg = QStringLiteral("数据服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("数据服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
        try
        {
            if (!rtProxyF.get())
            {
                errMsg = QStringLiteral("实时数据服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("实时数据服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
        try
        {
            if (!deviceProxyF.get())
            {
                errMsg = QStringLiteral("设备属性服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("设备属性服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
        try
        {
            if (!historyProxyF.get())
            {
                errMsg = QStringLiteral("历史数据服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("历史数据服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
        try
        {
            if (!ruleProxyF.get())
            {
                errMsg = QStringLiteral("规则引擎服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("规则引擎服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
        try
        {
            if (!powerProxyF.get())
            {
                errMsg = QStringLiteral("权限验证服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("权限验证服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
        try
        {
            if (!userProxyF.get())
            {
                errMsg = QStringLiteral("用户管理服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("用户管理服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
        try
        {
            if (!taskProxyF.get())
            {
                errMsg = QStringLiteral("断电任务服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("断电任务服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
        try
        {
            if (!eventProxyF.get())
            {
                errMsg = QStringLiteral("事件处理服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("事件处理服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
        try
        {
            if (!clientProxyF.get())
            {
                errMsg = QStringLiteral("客户端管理服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("客户端管理服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
        try
        {
            if (!messageGatewayProxyF.get())
            {
                errMsg = QStringLiteral("消息网关服务代理状态异常");
                ZGLOG_ERROR(errMsg);
                return false;
            }
        }
        catch (const Ice::Exception& e)
        {
            errMsg = QString("消息网关服务代理调用异常: %1").arg(e.what());
            ZGLOG_ERROR(errMsg);
            return false;
        }
    }
    catch (const Ice::Exception& e)
    {
        errMsg = QString("检查服务代理状态异常: %1").arg(e.what());
        ZGLOG_ERROR(errMsg);
        return false;
    }
    return true;
}

bool ZGSPWebServerMng::createRtTopic()
{
    QList<ZGRuntime::REDIS_CLIENT_TYPE> listClientType;
    listClientType << ZGRuntime::REDIS_RT_TOPIC << ZGRuntime::REDIS_RT_QUEUE;
    if (!ZGRuntime::instance()->initRedisClient(listClientType))
    {
        ZGLOG_ERROR("initRedisClient error.");
        return false;
    }
    m_pRtTopic = ZGRuntime::instance()->getRedisClientRTTopic();
    if (m_pRtTopic == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTTopic error.");
        return false;
    }
    return true;
}

void ZGSPWebServerMng::initWebServer()
{
    QThreadPool::globalInstance()->setMaxThreadCount(50);
    m_httpServer->route("/", QHttpServerRequest::Method::Options,
        [](const QHttpServerRequest& req)-> QHttpServerResponse
        {
            QHttpServerResponse resp(QHttpServerResponse::StatusCode::NoContent);
            resp.addHeader("Access-Control-Allow-Origin:", "*");
            resp.addHeader("Access-Control-Allow-Methods:", "POST, GET, OPTIONS");
            resp.addHeader("Access-Control-Allow-Headers:", "Content-Type");
            resp.addHeader("Access-Control-Max-Age", "86400");
            return resp;
        });
    // 请求公钥
    m_httpServer->route("/api/key/request", QHttpServerRequest::Method::Post,
        [this](const QHttpServerRequest& req)-> QFuture<QHttpServerResponse>
        {
            return QtConcurrent::run([this](const QHttpServerRequest& request)-> QHttpServerResponse
            {
                QJsonValue param;
                QString clientID, errMsg, cookieID;
                if (!parseRequest(&request, clientID, param, errMsg))
                {
                    const auto& errResp = ZGWebModule::errorObject(errMsg);
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                // 从cfg/cert读取公钥并返回
                QFile file(ZGPubFun::getCfgDir() + "/cert/public.pem");
                if (!file.open(QIODevice::ReadOnly))
                {
                    const auto& errResp = ZGWebModule::errorObject(QStringLiteral("读取公钥文件失败"));
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                QByteArray data = file.readAll();
                file.close();
                const auto& respObject = ZGWebModule::replyObject(QString(data));
                QHttpServerResponse resp(respObject);
                QByteArray encrypt = m_encrypt ? "1" : "0";
                resp.addHeader("encrypt", encrypt);
                return resp;
            }, std::ref(req));
        });
    m_httpServer->route("/api/key/update", QHttpServerRequest::Method::Post,
        [this](const QHttpServerRequest& req)-> QFuture<QHttpServerResponse>
        {
            return QtConcurrent::run([this](const QHttpServerRequest& request)-> QHttpServerResponse
            {
                QJsonDocument document;
                QString errMsg;
                if (m_encrypt)
                {
                    if (!ZGWebModule::decryptJsonWithServerKey(request.body(), document, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                }
                else
                {
                    document = QJsonDocument::fromJson(request.body());
                }
                QJsonObject object = document.object();
                QJsonValue param;
                QString clientID;
                if (!parseObject(object, clientID, param, errMsg))
                {
                    const auto& errResp = ZGWebModule::errorObject(errMsg);
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                int errCode;
                if (!isClientValid(clientID, errCode, errMsg))
                {
                    const auto& errResp = ZGWebModule::errorObject(errMsg, errCode);
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                const auto& encryptedClientKey = param.toString();
                // 使用服务器公钥解密
                QByteArray clientKey;
                if (!ZGWebModule::decryptWithServerKey(encryptedClientKey.toLatin1(), clientKey, errMsg))
                {
                    const auto& errResp = ZGWebModule::errorObject(errMsg);
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                ZG6000::StringMap client;
                client["rtKey"] = clientKey.toStdString();
                client["rtKeyTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString();
                if (!ZGProxyCommon::updateDataByID("sp_param_client", clientID.toStdString(), client))
                {
                    const auto& errResp = ZGWebModule::errorObject(QStringLiteral("更新客户端密钥失败"));
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                const auto& respObject = ZGWebModule::replyObject("");
                if (m_encrypt)
                {
                    QJsonDocument doc(respObject);
                    QByteArray respData = doc.toJson(QJsonDocument::Compact);
                    QByteArray encryptResp;
                    if (!ZGWebModule::encryptWithClientKey(clientKey, respData, encryptResp, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    return encryptResp.data();
                }
                return respObject;
            }, std::ref(req));
        });
    m_httpServer->route("/api/token/request", QHttpServerRequest::Method::Post,
        [this](const QHttpServerRequest& req)-> QFuture<QHttpServerResponse>
        {
            return QtConcurrent::run([this](const QHttpServerRequest& request)-> QHttpServerResponse
            {
                QByteArray clientID = request.value("Clientid");
                QJsonDocument document;
                QJsonObject object;
                QJsonValue param;
                QString errMsg;
                QString msgClientID;
                QByteArray clientKey;
                ZGLOG_TRACE(QString("clientID: %1").arg(clientID));
                if (clientID.isEmpty())
                {
                    if (m_encrypt)
                    {
                        if (!ZGWebModule::decryptJsonWithServerKey(request.body(), document, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                    }
                    else
                    {
                        document = QJsonDocument::fromJson(request.body());
                    }
                    object = document.object();
                    if (m_encrypt)
                    {
                        QString key;
                        if (!parseObjectWithKey(object, msgClientID, param, key, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                        clientKey = key.toLatin1();
                    }
                    else
                    {
                        if (!parseObject(object, msgClientID, param, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                    }
                }
                else
                {
                    // 使用客户端的对称密钥解密
                    if (m_encrypt)
                    {
                        if (!decryptWithClientKey(clientID, request.body(), clientKey, document, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                    }
                    else
                    {
                        document = QJsonDocument::fromJson(request.body());
                    }
                    object = document.object();
                    if (!parseObject(object, msgClientID, param, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    if (msgClientID != clientID)
                    {
                        const auto& errResp = ZGWebModule::errorObject(QStringLiteral("客户端ID不匹配"));
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    int errCode;
                    if (!isClientValid(clientID, errCode, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg, errCode);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                }
                const auto& paramObject = param.toObject();
                if (!ZGWebModule::checkRequiredFields(paramObject, {"type"}, errMsg))
                {
                    const auto& errResp = ZGWebModule::errorObject(errMsg);
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                const auto& type = paramObject["type"].toString();
                QString userID;
                auto powerProxy = ZGProxyMng::instance()->getProxySPPowerVerify();
                if (powerProxy == nullptr)
                {
                    const auto& errResp = ZGWebModule::errorObject(u8"获取用户管理服务代理失败");
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                ZG6000::ErrorInfo e;
                if (type == "password")
                {
                    if (!ZGWebModule::checkRequiredFields(paramObject, {"userID", "password"}, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    userID = paramObject["userID"].toString();
                    QByteArray password = paramObject["password"].toString().toLatin1();
                    QByteArray decryptPassword;
                    // 如果客户端ID为空，该password经过了服务器公钥加密，这里需要私钥解密
                    if (clientID.isEmpty())
                    {
                        ZGLOG_TRACE(QString("password: %1").arg(password));
                        if (!ZGWebModule::decryptWithServerKey(password, decryptPassword, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                        ZGLOG_TRACE(QString("password = %1, decryptPassword = %2").arg(password).arg(decryptPassword));
                    }
                    else
                    {
                        if (!ZGDBWebModule::getClientKey(clientID, clientKey, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                        ZGLOG_TRACE(QString("clientKey: %1, password: %2").arg(clientKey).arg(password));
                        if (!ZGWebModule::decryptWithClientKey(clientKey, password, decryptPassword, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                        ZGLOG_TRACE(QString("password = %1, decryptPassword = %2").arg(password).arg(decryptPassword));
                    }
                    try
                    {
                        if (!powerProxy->verifyByPassword(clientID.toStdString(), userID.toStdString(),
                            decryptPassword.toStdString(), "", "", e))
                        {
                            const auto& errResp = ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                    }
                    catch (const Ice::Exception& ie)
                    {
                        const auto& errResp = ZGWebModule::errorObject(ie.what());
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                }
                else if (type == "card")
                {
                    if (!ZGWebModule::checkRequiredFields(paramObject, {"cardID", "authModeID"}, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    const auto& cardID = paramObject["cardID"].toString();
                    const auto& authModeID = paramObject["authModeID"].toString();
                    try
                    {
                        std::string realUserID;
                        if (!powerProxy->verifyByCard(clientID.toStdString(), "", authModeID.toStdString(),
                            cardID.toStdString(), "", "", realUserID, e))
                        {
                            const auto& errResp = ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                        userID = realUserID.c_str();
                    }
                    catch (const Ice::Exception& ie)
                    {
                        const auto& errResp = ZGWebModule::errorObject(ie.what());
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                }
                else if (type == "auth")
                {
                    if (!ZGWebModule::checkRequiredFields(paramObject, {"userID"}, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    userID = paramObject["userID"].toString();
                    try
                    {
                        if (!powerProxy->verifyByAuthDev(clientID.toStdString(), userID.toStdString(), "ZG_AM_HIK_ALL",
                            "", "", e))
                        {
                            const auto& errResp = ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                    }
                    catch (const Ice::Exception& ie)
                    {
                        const auto& errResp = ZGWebModule::errorObject(ie.what());
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                }
                else
                {
                    const auto& errResp = ZGWebModule::errorObject(QStringLiteral("未知的验证类型"));
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                ZG6000::StringList listPower;
                if (!getUserPowers(userID, listPower, errMsg))
                {
                    const auto& errResp = ZGWebModule::errorObject(errMsg);
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                QJsonObject dataObject;
                QString tokenID, expireTime;
                if (!ZGWebModule::generateTokenObject(userID, 3600, true, dataObject, tokenID, expireTime, errMsg))
                {
                    const auto& errResp = ZGWebModule::errorObject(errMsg);
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                if (!ZGWebModule::addToken(tokenID, expireTime, errMsg))
                {
                    const auto& errResp = ZGWebModule::errorObject(errMsg);
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                // 使用客户端的对称密钥加密
                const auto& respObject = ZGWebModule::replyObject(dataObject);
                if (m_encrypt)
                {
                    QJsonDocument doc(respObject);
                    QByteArray respData = doc.toJson(QJsonDocument::Compact);
                    QByteArray encryptResp;
                    if (!ZGWebModule::encryptWithClientKey(clientKey, respData, encryptResp, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    return QHttpServerResponse{encryptResp};
                }
                return respObject;
            }, std::ref(req));
        });
    // m_httpServer->route("/api/login", QHttpServerRequest::Method::Post,
    //     [this](const QHttpServerRequest& req)-> QFuture<QHttpServerResponse>
    //     {
    //         return QtConcurrent::run([this](const QHttpServerRequest& request)-> QHttpServerResponse
    //         {
    //             QJsonDocument document;
    //             QJsonValue param;
    //             QString errMsg, cookieID;
    //             QByteArray clientID = request.value("Clientid");
    //             if (clientID.isEmpty())
    //             {
    //                 const auto& errResp = ZGWebModule::errorObject(QStringLiteral("未授权的请求"));
    //                 return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //             }
    //             QByteArray clientKey;
    //             if (m_encrypt)
    //             {
    //                 if (!decryptWithClientKey(clientID, request.body(), clientKey, document, errMsg))
    //                 {
    //                     const auto& errResp = ZGWebModule::errorObject(errMsg);
    //                     return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //                 }
    //             }
    //             else
    //             {
    //                 document = QJsonDocument::fromJson(request.body());
    //             }
    //             const auto& object = document.object();
    //             QString msgClientID;
    //             if (!parseObject(object, msgClientID, param, errMsg))
    //             {
    //                 const auto& errResp = ZGWebModule::errorObject(errMsg);
    //                 return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //             }
    //             int errCode;
    //             if (!isClientValid(clientID, errCode, errMsg))
    //             {
    //                 const auto& errResp = ZGWebModule::errorObject(errMsg, errCode);
    //                 return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //             }
    //             if (msgClientID != clientID)
    //             {
    //                 const auto& errResp = ZGWebModule::errorObject(QStringLiteral("客户端ID不匹配"));
    //                 return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //             }
    //             QVariantMap headers;
    //             for (const auto& pair : request.headers())
    //                 headers[pair.first] = pair.second;
    //             const auto& paramObject = param.toObject();
    //             if (!ZGWebModule::checkRequiredFields(paramObject, {"type", "keepTime"}, errMsg))
    //             {
    //                 const auto& errResp = ZGWebModule::errorObject(errMsg);
    //                 return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //             }
    //             const auto& type = paramObject["type"].toString();
    //             const auto& keepTime = paramObject["keepTime"].toInt();
    //             auto powerProxy = ZGProxyMng::instance()->getProxySPPowerVerify();
    //             if (powerProxy == nullptr)
    //             {
    //                 const auto& errResp = ZGWebModule::errorObject(u8"获取权限验证服务代理失败");
    //                 return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //             }
    //             ZG6000::ErrorInfo e;
    //             QString userID;
    //             if (type == "password")
    //             {
    //                 if (!ZGWebModule::checkRequiredFields(paramObject, {"userID", "password"}, errMsg))
    //                 {
    //                     const auto& errResp = ZGWebModule::errorObject(errMsg);
    //                     return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //                 }
    //                 userID = paramObject["userID"].toString();
    //                 const auto& password = paramObject["password"].toString();
    //                 try
    //                 {
    //                     std::string outClientID;
    //                     if (!powerProxy->loginByPassword(msgClientID.toStdString(), userID.toStdString(),
    //                         password.toStdString(), keepTime, e))
    //                     {
    //                         const auto& errResp = ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    //                         return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //                     }
    //                 }
    //                 catch (const Ice::Exception& ie)
    //                 {
    //                     const auto& errResp = ZGWebModule::errorObject(ie.what());
    //                     return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //                 }
    //             }
    //             else if (type == "card")
    //             {
    //                 if (!ZGWebModule::checkRequiredFields(paramObject, {"cardID", "authModeID"}, errMsg))
    //                 {
    //                     const auto& errResp = ZGWebModule::errorObject(errMsg);
    //                     return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //                 }
    //                 const auto& cardID = paramObject["cardID"].toString();
    //                 const auto& authModeID = paramObject["authModeID"].toString();
    //                 try
    //                 {
    //                     std::string realUserID;
    //                     if (!powerProxy->loginByCard(msgClientID.toStdString(), "", authModeID.toStdString(),
    //                         cardID.toStdString(), keepTime, realUserID, e))
    //                     {
    //                         const auto& errResp = ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    //                         return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //                     }
    //                     userID = realUserID.c_str();
    //                 }
    //                 catch (const Ice::Exception& ie)
    //                 {
    //                     const auto& errResp = ZGWebModule::errorObject(ie.what());
    //                     return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //                 }
    //             }
    //             else
    //             {
    //                 const auto& errResp = ZGWebModule::errorObject(QStringLiteral("未知的登录类型"));
    //                 return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //             }
    //             ZG6000::StringList listPower;
    //             if (!getUserPowers(userID, listPower, errMsg))
    //             {
    //                 const auto& errResp = ZGWebModule::errorObject(errMsg);
    //                 return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //             }
    //             QJsonObject dataObject;
    //             QString tokenID, expireTime;
    //             if (!ZGWebModule::generateTokenObject(userID, 86400, false, dataObject, tokenID, expireTime, errMsg))
    //             {
    //                 const auto& errResp = ZGWebModule::errorObject(errMsg);
    //                 return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //             }
    //             if (!ZGWebModule::addToken(tokenID, expireTime, errMsg))
    //             {
    //                 const auto& errResp = ZGWebModule::errorObject(errMsg);
    //                 return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //             }
    //             // 使用客户端的对称密钥加密
    //             const auto& respObject = ZGWebModule::replyObject(dataObject);
    //             if (m_encrypt)
    //             {
    //                 QJsonDocument doc(respObject);
    //                 QByteArray respData = doc.toJson(QJsonDocument::Compact);
    //                 QByteArray encryptResp;
    //                 if (!ZGWebModule::encryptWithClientKey(clientKey, respData, encryptResp, errMsg))
    //                 {
    //                     const auto& errResp = ZGWebModule::errorObject(errMsg);
    //                     return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
    //                 }
    //                 return QHttpServerResponse{encryptResp};
    //             }
    //             return respObject;
    //         }, std::ref(req));
    //     });
    for (const auto& pair : m_mapDirectApi)
    {
        m_httpServer->route(pair.first, QHttpServerRequest::Method::Post,
            [this, pair](const QHttpServerRequest& req)-> QFuture<QHttpServerResponse>
            {
                return QtConcurrent::run([this](const ClientHandle handle,
                                                const QHttpServerRequest& request)-> QHttpServerResponse
                {
                    QJsonDocument document;
                    QString errMsg;
                    if (m_encrypt)
                    {
                        if (!ZGWebModule::decryptJsonWithServerKey(request.body(), document, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                    }
                    else
                    {
                        document = QJsonDocument::fromJson(request.body());
                    }
                    QJsonObject object = document.object();
                    QJsonValue param;
                    QString clientID, clientKey;
                    if (!parseObjectWithKey(object, clientID, param, clientKey, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    QVariantMap headers;
                    for (const auto& pair : request.headers())
                        headers[pair.first] = pair.second;
                    auto respContent = handle(clientID, headers, param);
                    if (m_encrypt)
                    {
                        QJsonDocument doc(respContent);
                        QByteArray respData = doc.toJson(QJsonDocument::Compact);
                        QByteArray encryptResp;
                        if (!ZGWebModule::encryptWithClientKey(clientKey.toLatin1(), respData, encryptResp,
                            errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                        return QHttpServerResponse{encryptResp};
                    }
                    return respContent;
                }, pair.second, std::ref(req));
            });
    }
    for (const auto& pair : m_mapTableApi)
    {
        m_httpServer->route(pair.first, QHttpServerRequest::Method::Post,
            [this, pair](const QString& tableName,
                         const QHttpServerRequest& req)-> QFuture<QHttpServerResponse>
            {
                auto exetor = [this](TableHandler handle,
                                     QString table,
                                     const QHttpServerRequest& request)-> QHttpServerResponse
                {
                    QByteArray clientID = request.value("Clientid");
                    if (clientID.isEmpty())
                    {
                        const auto& errResp = ZGWebModule::errorObject(QStringLiteral("未授权的请求"));
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    QJsonDocument document;
                    QByteArray clientKey;
                    QString errMsg;
                    if (m_encrypt)
                    {
                        if (!decryptWithClientKey(clientID, request.body(), clientKey, document, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                    }
                    else
                    {
                        document = QJsonDocument::fromJson(request.body());
                    }
                    QJsonObject object = document.object();
                    QJsonValue param;
                    QString msgClientID;
                    int errorCode;
                    if (!parseObject(object, msgClientID, param, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    if (msgClientID != clientID)
                    {
                        const auto& errResp = ZGWebModule::errorObject(QStringLiteral("客户端ID不匹配"));
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    if (!isClientValid(msgClientID, errorCode, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg, errorCode);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    QVariantMap headers;
                    for (const auto& pair : request.headers())
                        headers[pair.first] = pair.second;
                    auto resp = handle(table, headers, param);
                    if (m_encrypt)
                    {
                        QJsonDocument doc(resp);
                        QByteArray respData = doc.toJson(QJsonDocument::Compact);
                        QByteArray encryptResp;
                        if (!ZGWebModule::encryptWithClientKey(clientKey, respData, encryptResp, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                        return QHttpServerResponse{encryptResp};
                    }
                    return resp;
                };
                return QtConcurrent::run(exetor, pair.second, tableName, std::ref(req));
            });
    }
    for (const auto& pair : m_mapValidateApi)
    {
        m_httpServer->route(pair.first, QHttpServerRequest::Method::Post,
            [this, pair](const QHttpServerRequest& req)-> QFuture<QHttpServerResponse>
            {
                auto exetor = [this](Handle handle,
                                     const QHttpServerRequest& request)-> QHttpServerResponse
                {
                    QByteArray clientID = request.value("Clientid");
                    if (clientID.isEmpty())
                    {
                        const auto& errResp = ZGWebModule::errorObject(QStringLiteral("未授权的请求"));
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    QJsonDocument document;
                    QByteArray clientKey;
                    QString errMsg;
                    if (m_encrypt)
                    {
                        if (!decryptWithClientKey(clientID, request.body(), clientKey, document, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                    }
                    else
                    {
                        document = QJsonDocument::fromJson(request.body());
                    }
                    QJsonObject object = document.object();
                    QJsonValue param;
                    QString msgClientID;
                    int errorCode;
                    if (!parseObject(object, msgClientID, param, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    if (msgClientID != clientID)
                    {
                        const auto& errResp = ZGWebModule::errorObject(QStringLiteral("客户端ID不匹配"));
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    if (!isClientValid(msgClientID, errorCode, errMsg))
                    {
                        const auto& errResp = ZGWebModule::errorObject(errMsg, errorCode);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    QVariantMap headers;
                    for (const auto& pair : request.headers())
                        headers[pair.first] = pair.second;
                    auto resp = handle(headers, param);
                    if (m_encrypt)
                    {
                        QJsonDocument doc(resp);
                        QByteArray respData = doc.toJson(QJsonDocument::Compact);
                        QByteArray encryptResp;
                        if (!ZGWebModule::encryptWithClientKey(clientKey, respData, encryptResp, errMsg))
                        {
                            const auto& errResp = ZGWebModule::errorObject(errMsg);
                            return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                        }
                        return QHttpServerResponse{encryptResp};
                    }
                    return resp;
                };
                return QtConcurrent::run(exetor, pair.second, std::ref(req));
            });
    }
    m_httpServer->route("/api/app/", QHttpServerRequest::Method::Post,
        [this](const QUrl& url,
               const QHttpServerRequest& req)-> QFuture<QHttpServerResponse>
        {
            const auto& path = url.path();
            return QtConcurrent::run([this](QString path,
                                            const QHttpServerRequest& request)-> QHttpServerResponse
            {
                QString newPath = path;
                qsizetype pos = newPath.indexOf("/");
                if (pos != -1)
                    newPath = newPath.left(pos);
                auto pairModule = m_mapPathModule.find(newPath);
                if (pairModule == m_mapPathModule.end())
                {
                    const auto& errResp = ZGWebModule::errorObject(QStringLiteral("unhandled request."));
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                const auto& module = pairModule->second;
                if (pos == -1)
                {
                    const auto& resp = module->supportedInterfaces();
                    return resp;
                }
                QByteArray clientID = request.value("Clientid");
                QString errMsg;
                if (clientID.isEmpty())
                {
                    const auto& errResp = ZGWebModule::errorObject(QStringLiteral("未授权的请求"));
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                QByteArray clientKey;
                QJsonDocument document;
                if (m_encrypt)
                {
                    if (!decryptWithClientKey(clientID, request.body(), clientKey, document, errMsg))
                    {
                        ZGLOG_ERROR(QStringLiteral("使用客户端'%1'密钥解密失败:%2").arg(clientID).arg(errMsg));
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                }
                else
                {
                    document = QJsonDocument::fromJson(request.body());
                }
                const auto& object = document.object();
                QJsonValue param;
                QString msgClientID;
                int errorCode;
                if (!parseObject(object, msgClientID, param, errMsg))
                {
                    const auto& errResp = ZGWebModule::errorObject(errMsg);
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                if (msgClientID != clientID)
                {
                    const auto& errResp = ZGWebModule::errorObject(QStringLiteral("客户端ID不匹配"));
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                if (!isClientValid(msgClientID, errorCode, errMsg))
                {
                    const auto& errResp = ZGWebModule::errorObject(errMsg, errorCode);
                    return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                }
                QVariantMap headers;
                for (const auto& pair : request.headers())
                    headers[pair.first] = pair.second;
                auto resp = module->dispatch(path, msgClientID, headers, param, request);
                if (m_encrypt)
                {
                    QJsonDocument doc(resp);
                    QByteArray respData = doc.toJson(QJsonDocument::Compact);
                    QByteArray encryptResp;
                    if (!ZGWebModule::encryptWithClientKey(clientKey, respData, encryptResp, errMsg))
                    {
                        ZGLOG_ERROR(QStringLiteral("使用客户端'%1'密钥加密失败:%2").arg(clientID).arg(errMsg));
                        const auto& errResp = ZGWebModule::errorObject(errMsg);
                        return QHttpServerResponse{errResp, QHttpServerResponse::StatusCode::InternalServerError};
                    }
                    return QHttpServerResponse{encryptResp};
                }
                return resp;
            }, path, std::ref(req));
        });
    //   m_httpServer->route("/upload/", QHttpServerRequest::Method::Post,
    //       [](const QUrl& url, const QHttpServerRequest& request)-> QFuture<QHttpServerResponse>
    // {
    // 	return QtConcurrent::run([&request](QUrl url)-> QHttpServerResponse
    // 	{
    // 		const auto& value = request.value("Content-Type");
    // 		if (!value.contains("multipart/form-data"))
    //                   return ZGWebModule::errorObject(QStringLiteral("错误的Content-Type"));
    // 		const auto& content = request.body();
    // 		qsizetype pos = content.indexOf("\r\n");
    // 		const auto& boundary = content.left(pos);
    // 		qsizetype oldPos = pos + 2;
    // 		pos = content.indexOf("\r\n", oldPos);
    // 		if (pos == -1)
    //                   return ZGWebModule::errorObject(QStringLiteral("错误的请求格式"));
    // 		auto disposition = content.mid(oldPos, pos - oldPos).trimmed();
    // 		oldPos = pos + 2;
    // 		pos = content.indexOf("\r\n", oldPos);
    // 		const auto& contentPair = content.mid(oldPos, pos - oldPos).trimmed();
    // 		oldPos = pos + 4;
    // 		pos = content.lastIndexOf(boundary);
    // 		const char* contentStart = content.constData() + oldPos;
    // 		long long length = pos - oldPos - 2;
    // 		pos = disposition.indexOf("filename");
    // 		oldPos = pos + 10;
    // 		pos = disposition.indexOf("\"", oldPos);
    // 		QString fileName = disposition.mid(oldPos, pos - oldPos);
    // 		oldPos = contentPair.indexOf(":");
    // 		const auto& contentType = contentPair.mid(oldPos + 1);
    // 		QString fileDir, filePath;
    // 		if (url.path().isEmpty())
    // 			fileDir = ZGPubFun::getRootDir() + "/";
    // 		else
    // 			fileDir = ZGPubFun::getRootDir() + "/" + url.path() + "/";
    // 		filePath = fileDir + fileName;
    // 		filePath.replace("\\", "/");
    // 		if (!QFile::exists(fileDir))
    //                   return ZGWebModule::errorObject(QStringLiteral("请求的路径不存在"));;
    // 		if (contentType.contains("text"))
    // 		{
    // 			QFile file(filePath);
    // 			file.open(QIODevice::WriteOnly);
    // 			QTextStream stream(&file);
    // 			stream << QByteArray::fromRawData(contentStart, length);
    // 			file.close();
    // 		}
    // 		else
    // 		{
    // 			std::ofstream outstream;
    // 			outstream.open(filePath.toLocal8Bit().data(), std::ios::binary);
    // 			outstream.write(contentStart, length);
    // 			outstream.close();
    // 		}
    //               return ZGWebModule::replyObject("");
    // 	}, url);
    // });
    m_httpServer->route("/page/", QHttpServerRequest::Method::Get,
        [this](const QUrl& url,
               const QHttpServerRequest& req)-> QFuture<QHttpServerResponse>
        {
            const auto& path = m_webRootPath + url.path();
            auto exetor = [&](QString filePath,
                              const QHttpServerRequest& request)-> QHttpServerResponse
            {
                if (!QFile::exists(filePath))
                    filePath = m_webRootPath + "react/index.html";
                QHttpServerResponse resp = QHttpServerResponse::fromFile(filePath);
                resp.addHeader("Cache-Control", "max-age=3600");
                return resp;
            };
            return QtConcurrent::run(exetor, path, std::ref(req));
        });
    m_httpServer->route("/", QHttpServerRequest::Method::Get,
        [this](const QUrl& url,
               const QHttpServerRequest& req)-> QFuture<QHttpServerResponse>
        {
            const auto& path = url.path();
            QString filePath;
            if (path.isEmpty())
                filePath = m_webRootPath + "hmi/index.html";
            else
            {
                filePath = m_webRootPath + "hmi/" + path;
                if (filePath.endsWith("/"))
                    filePath += "index.html";
            }
            auto exetor = [&](QString pagePath,
                              const QHttpServerRequest& request)-> QHttpServerResponse
            {
                if (!QFile::exists(pagePath))
                    pagePath = m_webRootPath + "hmi/index.html";
                QHttpServerResponse resp = QHttpServerResponse::fromFile(pagePath);
                resp.addHeader("Cache-Control", "max-age=3600");
                return resp;
            };
            return QtConcurrent::run(exetor, filePath, std::ref(req));
        });
}

bool ZGSPWebServerMng::startWebServer()
{
    if (!m_httpServer->listen(QHostAddress::Any, m_port))
    {
        ZGLOG_ERROR(QString("Web server failed to listen on port %1").arg(m_port));
        return false;
    }
    return true;
}

bool ZGSPWebServerMng::isClientValid(const QString& clientId,
                                     int& errCode,
                                     QString& errMsg)
{
    ZG6000::StringList listID;
    if (!ZGProxyCommon::mgetDataByField("sp_param_client", {clientId.toStdString()}, "id", listID))
    {
        errCode = ZGWebModule::rcErrNormal;
        errMsg = u8"获取客户端信息失败";
        return false;
    }
    if (listID.empty() || listID.front().empty())
    {
        errCode = ZGWebModule::rcErrNotRegister;
        errMsg = QStringLiteral("找不到客户端【%1】").arg(clientId);
        return false;
    }
    return true;
}

bool ZGSPWebServerMng::doValidate(const QString& clientId,
                                  const QString& cookie,
                                  int& errCode,
                                  QString& errMsg)
{
    ZG6000::StringList listCookieID;
    if (!ZGProxyCommon::mgetDataByField("sp_param_client", {clientId.toStdString()}, "rtCookieID", listCookieID))
    {
        errCode = ZGWebModule::rcErrNormal;
        errMsg = "mgetDataByField error";
        return false;
    }
    if (listCookieID.empty())
    {
        errCode = ZGWebModule::rcErrNotRegister;
        errMsg = QString("Can't find client %1").arg(clientId);
        return false;
    }
    std::string fieldValue = listCookieID.front();
    if (fieldValue.empty())
    {
        errCode = ZGWebModule::rcErrNotLogin;
        errMsg = QStringLiteral("客户端未登录");
        ZGLOG_ERROR(errMsg);
        return false;
    }
    if (cookie.toStdString() != fieldValue)
    {
        errCode = ZGWebModule::rcErrNotLogin;
        errMsg = QStringLiteral("无效的Cookie, 预期'%1'，当前'%2'").arg(fieldValue.c_str()).arg(cookie);
        ZGLOG_ERROR(errMsg);
        return false;
    }
    errCode = ZGWebModule::rcSuccess;
    return true;
}

bool ZGSPWebServerMng::fetchCookies(const QHttpServerRequest* req,
                                    QMap<QString, QString>& cookies,
                                    bool& secure,
                                    bool& httpOnly)
{
    // const auto& headers = req->headers();
    // auto pair = headers.find("Cookie");
    // if (pair == headers.end())
    // {
    // 	pair = headers.find("cookie");
    // 	if (pair == headers.end())
    // 	{
    // 		ZGLOG_ERROR("No cookies.");
    // 		return false;
    // 	}
    // }
    // QString cookieValue = pair.value().toString();
    // const auto& listCookie = cookieValue.split(";");
    // secure = false;
    // httpOnly = false;
    // for (const auto& cookie : listCookie)
    // {
    // 	qsizetype pos = cookie.indexOf("=");
    // 	if (pos == -1)
    // 	{
    // 		if (cookie == "Secure")
    // 			secure = true;
    // 		else if (cookie == "HttpOnly")
    // 			httpOnly = true;
    // 	}
    // 	else
    // 		cookies.insert(cookie.left(pos).trimmed(), cookie.mid(pos + 1).trimmed());
    // }
    return true;
}

bool ZGSPWebServerMng::parseRequest(const QHttpServerRequest* req,
                                    QString& clientID,
                                    QJsonValue& param,
                                    QString& errMsg)
{
    QJsonParseError parseError;
    QJsonDocument jsonDocument = QJsonDocument::fromJson(req->body(), &parseError);
    if (parseError.error != QJsonParseError::NoError)
    {
        errMsg = parseError.errorString();
        return false;
    }
    const auto& rootObject = jsonDocument.object();
    QStringList listKey{"clientID", "time", "params"};
    for (const auto& key : listKey)
    {
        auto it = rootObject.find(key);
        if (it == rootObject.end())
        {
            errMsg = u8"缺少 " + key;
            return false;
        }
        if (key == "clientID")
            clientID = it.value().toString();
        if (key == "params")
            param = it.value();
    }
    return true;
}

bool ZGSPWebServerMng::parseObject(const QJsonObject& object,
                                   QString& clientID,
                                   QJsonValue& param,
                                   QString& errMsg)
{
    QStringList listKey{"clientID", "time", "params"};
    for (const auto& key : listKey)
    {
        auto it = object.find(key);
        if (it == object.end())
        {
            errMsg = u8"缺少 " + key;
            return false;
        }
        if (key == "clientID")
            clientID = it.value().toString();
        if (key == "params")
            param = it.value();
    }
    return true;
}

bool ZGSPWebServerMng::parseObjectWithKey(const QJsonObject& object,
                                          QString& clientID,
                                          QJsonValue& param,
                                          QString& clientKey,
                                          QString& errMsg)
{
    QStringList listKey{"clientID", "time", "key", "params"};
    for (const auto& key : listKey)
    {
        auto it = object.find(key);
        if (it == object.end())
        {
            errMsg = u8"缺少 " + key;
            return false;
        }
        if (key == "clientID")
        {
            clientID = it.value().toString();
        }
        if (key == "key")
        {
            // 使用服务器的密钥解密
            QByteArray decryptClientKey = it.value().toString().toLatin1();
            QByteArray originClientKey;
            if (!ZGWebModule::decryptWithServerKey(decryptClientKey, originClientKey, errMsg))
                return false;
            clientKey = originClientKey;
        }
        if (key == "params")
            param = it.value();
    }
    return true;
}

bool ZGSPWebServerMng::getUserPowers(const QString& userID,
                                     ZG6000::StringList& listPower,
                                     QString& errMsg)
{
    QString sql = QString("SELECT DISTINCT powerID FROM sp_param_hrm_role_power a "
        "LEFT JOIN sp_param_hrm_user_role b ON a.roleID = b.roleID "
        "WHERE b.userID = '%1' ORDER BY powerID").arg(userID);
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listPower))
    {
        errMsg = QStringLiteral("获取用户'%1'权限失败").arg(userID);
        return false;
    }
    return true;
}

bool ZGSPWebServerMng::decryptWithClientKey(const QString& clientID,
                                            const QByteArray& data,
                                            QByteArray& clientKey,
                                            QJsonDocument& document,
                                            QString& errMsg)
{
    if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
        return false;
    if (!ZGWebModule::decryptJsonWithClientKey(clientKey, data, document, errMsg))
        return false;
    return true;
}

void ZGSPWebServerMng::clearInvalidToken()
{
    // 从sp_param_token表中删除过期的token
    QString currentTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true);
    // 删除expireTime为null、为空字符串或者小于当前时间的token
    QString sql = QString("DELETE FROM sp_param_token WHERE expireTime IS NULL OR expireTime = '' OR expireTime < '%1'")
        .arg(currentTime);
    if (!ZGProxyCommon::execSql(sql.toStdString()))
    {
        ZGLOG_ERROR(QStringLiteral("删除过期token失败"));
    }
}

void ZGSPWebServerMng::onReceiveMessage(const QString& channel,
                                        const QString& message)
{
    auto pair = m_mapMonitorTable.find(channel);
    if (pair != m_mapMonitorTable.end())
    {
        for (auto webModule : pair->second)
        {
            webModule->processMessage(channel, message);
        }
    }
}

void ZGSPWebServerMng::onTimer()
{
    auto future = QtConcurrent::run([this]()
    {
        clearInvalidToken();
    });
}

ZGSPWebServerMng* ZGSPWebServerMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGSPWebServerMng;
    return g_pInstance;
}

bool ZGSPWebServerMng::checkState()
{
    return m_initialized;
}

void ZGSPWebServerMng::init()
{
    m_webRootPath = ZGPubFun::getRootDir() + "/web/";
    initApiHandle();
    initEvents();
    initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::sleep(m_initInterval);
    }
    if (m_enableSsl)
    {
        while (!initCertification())
        {
            ZGLOG_ERROR("initCertification error.");
            QThread::sleep(m_initInterval);
        }
    }
    auto future = QtConcurrent::run([this](){
        while (true)
        {
            QString errMsg;
            initBaseService(errMsg);
            QThread::msleep(1000);
        }
    });
    m_checkTimer.start(10000);
    QTimer::singleShot(30000, this, [&]()
    {
        QString errMsg;
        while (!initBaseService(errMsg))
        {
            ZGLOG_ERROR(errMsg);
            QThread::sleep(m_initInterval);
        }
        while (!createRtTopic())
        {
            ZGLOG_ERROR("createRtTopic error.");
            QThread::sleep(m_initInterval);
        }
        initWebModules();
        initWebServer();
        if (!startWebServer())
        {
            ZGLOG_ERROR("startWebServer error.");
            return;
        }
        m_initialized = true;
        ZGLOG_INFO("ZGSPWebServer init finished.");
    });
}

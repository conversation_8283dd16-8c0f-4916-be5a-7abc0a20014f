#include "ZGRTWebModule.h"

#include "ZGDebugMng.h"
#include "ZGJson.h"

ZGRTWebModule::ZGRTWebModule(QObject* parent)
    : ZGWebModule(parent)
{
    registerHandle("rt/field/get", this, &ZGRTWebModule::on_rt_field_get);
    registerHandle("rt/fields/get", this, &ZGRTWebModule::on_rt_fields_get);
    registerHandle("rt/list/field/get", this, &ZGRTWebModule::on_rt_list_field_get);
    registerHandle("rt/list/fields/get", this, &ZGRTWebModule::on_rt_list_fields_get);
    registerHandle("rt/list/record/get", this, &ZGRTWebModule::on_rt_list_record_get);
    registerHandle("rt/list/record/update", this, &ZGRTWebModule::on_rt_list_record_update);
}

ZGWebModule::Response ZGRTWebModule::on_rt_field_get(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!checkRequiredFields(object, {"table", "id", "field"}, errMsg))
        return errorObject(errMsg);
    const auto& table = object["table"].toString().toStdString();
    const auto& id = object["id"].toString().toStdString();
    const auto& field = object["field"].toString().toStdString();
    return rtCall([&](const std::shared_ptr<ZG6000::ZGSPRTDataPrx>& rtProxy)-> ZGWebModule::Response
    {
        ZG6000::ErrorInfo e;
        std::string fieldValue;
        try
        {
            if (!rtProxy->getDataByFieldToValue(table, id, field, fieldValue, e))
                return errorObject(ZGJson::convertToJson(e).c_str());
            return replyObject(fieldValue.c_str());
        }
        catch (const Ice::Exception& ie)
        {
            return errorObject(ie.what());
        }
    });
}

ZGWebModule::Response ZGRTWebModule::on_rt_fields_get(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!checkRequiredFields(object, {"table", "id", "fields"}, errMsg))
        return errorObject(errMsg);
    const auto& table = object["table"].toString().toStdString();
    const auto& id = object["id"].toString().toStdString();
    const auto& fields = object["fields"].toArray();
    ZG6000::StringList fieldList;
    for (auto field : fields)
        fieldList.push_back(field.toString().toStdString());
    return rtCall([&](const std::shared_ptr<ZG6000::ZGSPRTDataPrx>& rtProxy)-> ZGWebModule::Response
    {
        ZG6000::ErrorInfo e;
        ZG6000::StringMap fieldValues;
        try
        {
            if (!rtProxy->getDataByFieldsToMap(table, id, fieldList, fieldValues, e))
                return errorObject(ZGJson::convertToJson(e).c_str());
            return replyObject(stringMapToObject(fieldValues));
        }
        catch (const Ice::Exception& ie)
        {
            return errorObject(ie.what());
        }
    });
}

ZGWebModule::Response ZGRTWebModule::on_rt_list_field_get(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!checkRequiredFields(object, {"table", "ids", "field"}, errMsg))
        return errorObject(errMsg);
    const auto& table = object["table"].toString().toStdString();
    const auto& ids = object["ids"].toArray();
    const auto& field = object["field"].toString().toStdString();
    ZG6000::StringList idList;
    for (auto id : ids)
        idList.push_back(id.toString().toStdString());
    return rtCall([&](const std::shared_ptr<ZG6000::ZGSPRTDataPrx>& rtProxy)-> ZGWebModule::Response
    {
        ZG6000::ErrorInfo e;
        ZG6000::StringList fieldValues;
        try
        {
            if (!rtProxy->mgetDataByFieldToList(table, idList, field, fieldValues, e))
                return errorObject(ZGJson::convertToJson(e).c_str());
            return replyObject(stringListToArray(fieldValues));
        }
        catch (const Ice::Exception& ie)
        {
            return errorObject(ie.what());
        }
    });
}

ZGWebModule::Response ZGRTWebModule::on_rt_list_fields_get(const QString& clientID,
                                                             const QVariantMap& headers,
                                                             const QJsonValue& param,
                                                             const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!checkRequiredFields(object, {"table", "ids", "fields"}, errMsg))
        return errorObject(errMsg);
    const auto& table = object["table"].toString().toStdString();
    const auto& ids = object["ids"].toArray();
    const auto& fields = object["fields"].toArray();
    ZG6000::StringList idList;
    for (auto id : ids)
        idList.push_back(id.toString().toStdString());
    ZG6000::StringList fieldList;
    for (auto field : fields)
        fieldList.push_back(field.toString().toStdString());
    return rtCall([&](const std::shared_ptr<ZG6000::ZGSPRTDataPrx>& rtProxy)-> ZGWebModule::Response
    {
        ZG6000::ErrorInfo e;
        ZG6000::ListStringMap listFieldValues;
        try
        {
            if (!rtProxy->mgetDataByFieldsToListMap(table, idList, fieldList, listFieldValues, e))
                return errorObject(ZGJson::convertToJson(e).c_str());
            return replyObject(listStringMapToArray(listFieldValues));
        }
        catch (const Ice::Exception& ie)
        {
            return errorObject(ie.what());
        }
    });
}

ZGWebModule::Response ZGRTWebModule::on_rt_list_record_get(const QString& clientID,
                                                             const QVariantMap& headers,
                                                             const QJsonValue& param,
                                                             const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!checkRequiredFields(object, {"table", "ids"}, errMsg))
        return errorObject(errMsg);
    const auto& table = object["table"].toString().toStdString();
    const auto& ids = object["ids"].toArray();
    ZG6000::StringList idList;
    for (auto id : ids)
        idList.push_back(id.toString().toStdString());
    return rtCall([&](const std::shared_ptr<ZG6000::ZGSPRTDataPrx>& rtProxy)-> ZGWebModule::Response
    {
        ZG6000::ErrorInfo e;
        ZG6000::ListStringMap listFieldValues;
        try
        {
            if (!rtProxy->mgetDataByIDToListMap(table, idList, listFieldValues, e))
                return errorObject(ZGJson::convertToJson(e).c_str());
            return replyObject(listStringMapToArray(listFieldValues));
        }
        catch (const Ice::Exception& ie)
        {
            return errorObject(ie.what());
        }
    });
}

ZGWebModule::Response ZGRTWebModule::on_rt_list_record_update(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!verifyAuthentication(clientID, headers, "ZG_HP_MAINTAIN", token, errMsg))
        return errorObject(errMsg);
    const auto& object = param.toObject();
    if (!checkRequiredFields(object, {"table", "data"}, errMsg))
        return errorObject(errMsg);
    const auto& table = object["table"].toString().toStdString();
    const auto& dataArray = object["data"].toArray();
    ZG6000::StringList listID;
    ZG6000::ListStringMap listRecord;
    if (!getUpdateData(dataArray, listID, listRecord))
        return errorObject("Invalid param.");
    if (listID.size() != listRecord.size())
        return errorObject("Invalid param.");
    return rtCall([&](const std::shared_ptr<ZG6000::ZGSPRTDataPrx>& rtProxy)-> ZGWebModule::Response
    {
        ZG6000::ErrorInfo e;
        if (listID.size() > 1)
        {
            if (!rtProxy->mupdateDataByFieldsFromListMap(table, listID, listRecord, e))
            {
                ZGLOG_ERROR(e);
                return errorObject(ZGJson::convertToJson(e).c_str());
            }
        }
        else if (listID.size() == 1)
        {
            if (!rtProxy->updateDataByIDFromMap(table, listID[0], listRecord[0], e))
            {
                ZGLOG_ERROR(e);
                return errorObject(ZGJson::convertToJson(e).c_str());
            }
        }
        return replyObject("");
    });
}

bool ZGRTWebModule::initialize()
{
    return true;
}


QString ZGRTWebModule::prefix()
{
    return "rt";
}

ZGWebModule::Response ZGRTWebModule::rtCall(
    std::function<ZGWebModule::Response(const std::shared_ptr<ZG6000::ZGSPRTDataPrx>)> func)
{
    auto rtProxy = ZGProxyMng::instance()->getProxySPRTData();
    if (rtProxy == nullptr)
        return errorObject("invalid rt proxy");
    try
    {
        return func(rtProxy);
    }
    catch (const Ice::Exception& e)
    {
        return errorObject(e.what());
    }
}

bool ZGRTWebModule::getUpdateData(const QJsonArray& dataArray,
                                  ZG6000::StringList& listID,
                                  ZG6000::ListStringMap& listRecord)
{
    for (const auto data : dataArray)
    {
        const auto& dataObject = data.toObject();
        auto it = dataObject.find("id");
        if (it == dataObject.end())
            continue;
        ZG6000::StringMap record;
        it = dataObject.begin();
        while (it != dataObject.end())
        {
            QString key = it.key();
            QString val = it.value().toString();
            if (key == "id")
                listID.push_back(val.toStdString());
            else
                record.insert(std::make_pair(key.toStdString(), val.toStdString()));
            ++it;
        }
        listRecord.push_back(record);
    }
    return true;
}

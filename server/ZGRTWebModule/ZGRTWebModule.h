#ifndef ZGRTWEBMODULE_H
#define ZGRTWEBMODULE_H

#include "ZGWebModule.h"
#include "ZGProxyMng.h"

class ZGRTWebModule : public ZGWebModule
{
    Q_OBJECT
    Q_PLUGIN_METADATA(IID ZGWebModule_iid FILE "ZGRTWebModule.json")
    Q_INTERFACES(ZGWebModule)

public:
    explicit ZGRTWebModule(QObject* parent = nullptr);

    Response on_rt_field_get(const QString& clientID,
                               const QVariantMap& headers,
                               const QJsonValue& param,
                               const QHttpServerRequest& req);

    Response on_rt_fields_get(const QString& clientID,
                                const QVariantMap& headers,
                                const QJsonValue& param,
                                const QHttpServerRequest& req);

    Response on_rt_list_field_get(const QString& clientID,
                                    const QVariantMap& headers,
                                    const QJsonValue& param,
                                    const QHttpServerRequest& req);

    Response on_rt_list_fields_get(const QString& clientID,
                                     const QVariantMap& headers,
                                     const QJsonValue& param,
                                     const QHttpServerRequest& req);

    Response on_rt_list_record_get(const QString& clientID,
                                     const QVariantMap& headers,
                                     const QJsonValue& param,
                                     const QHttpServerRequest& req);

    Response on_rt_list_record_update(const QString& clientID,
                                     const QVariantMap& headers,
                                     const QJsonValue& param,
                                     const QHttpServerRequest& req);

    // ZGWebModule interface
public:
    bool initialize() override;
    QString prefix() override;

private:
    ZGWebModule::Response rtCall(
        std::function<ZGWebModule::Response(const std::shared_ptr<ZG6000::ZGSPRTDataPrx>)> func);

    bool getUpdateData(const QJsonArray& dataArray,
                         ZG6000::StringList& listID,
                         ZG6000::ListStringMap& listRecord);
};

#endif // ZGRTWEBMODULE_H

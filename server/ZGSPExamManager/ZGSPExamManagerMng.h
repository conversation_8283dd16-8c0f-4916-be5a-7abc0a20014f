#ifndef ZGSPEXAMMANAGERMNG_H
#define ZGSPEXAMMANAGERMNG_H

#include <QObject>
#include <QTimer>
#include <ZGServerCommon.h>

class ZGMqttClient;
class ZGRedisClient;

class ZGSPExamManagerMng : public QObject
{
    Q_OBJECT

public:
    static ZGSPExamManagerMng* instance();
    void init();
    bool checkState();

public:
    /**
     * @brief   创建新的审批流程
     *
     * @param           examID  审批模板ID
     * @param [in,out]  realExamID          创建的审批流程ID
     * @param [in,out]  e               执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool createExam(const std::string& examID, std::string& realExamID, ZG6000::ErrorInfo& e);

    /**
     * @brief   获取审批信息
     *
     * @param           realExamID      审批流程ID
     * @param [in,out]  examInfo    该审批流程的相关信息(JSON格式，包括流程信息、流程节点信息和流程步骤信息)
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool getExamInfo(const std::string& realExamID, std::string& examInfo, ZG6000::ErrorInfo& e);

    /**
     * @brief   执行审批步骤
     *
     * @param           appNodeID   应用节点ID
     * @param           stepID      审批步骤ID
     * @param           params      相关参数
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool execStep(const std::string& appNodeID, const std::string& stepID, const ZG6000::StringMap& params, ZG6000::ErrorInfo& e);

    /**
     * @brief   执行审批步骤
     *
     * @param           examStepID  审批步骤ID
     * @param           stepInfo    该审批步骤的相关信息(JSON格式)
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool execExamStep(const std::string& examStepID, const std::string& stepInfo, ZG6000::ErrorInfo& e);

    /**
     * @brief   删除审批流程
     *
     * @param           examID  审批流程ID
     * @param [in,out]  e       执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool deleteExam(const std::string& examID, ZG6000::ErrorInfo& e);

    /**
     * @brief   终止审批流程
     *
     * @param           examID  审批流程ID
     * @param [in,out]  e       执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool cancelExam(const std::string& examID, ZG6000::ErrorInfo& e);

    /**
     * @brief   审批流程归档
     *
     * @param           examID  审批流程ID
     * @param [in,out]  e       执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool finishExam(const std::string& examID, ZG6000::ErrorInfo& e);

private:
    explicit ZGSPExamManagerMng(QObject* parent = nullptr);
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initDictParam();
    bool initExamParam();
    bool initMqttClient();
    bool initRedisClient();
    bool checkRequiredParam(const ZG6000::StringMap& params, const ZG6000::StringList& listParam, QString& errMsg);
    bool checkStepValid(const std::string& appNodeID, const ZG6000::StringMap& realStep, ZG6000::ErrorInfo& e);
    bool getRealExamParam(const std::string& realStepID, ZG6000::StringMap& realStep, ZG6000::StringMap& realNode, ZG6000::StringMap& realExam, ZG6000::ErrorInfo& e);
    bool processAcceptResult(ZG6000::StringList& listSql, const ZG6000::StringMap& realStep, const ZG6000::StringMap& realNode, const ZG6000::StringMap& realExam,
                             ZG6000::ErrorInfo& e);
    bool processRejectResult(ZG6000::StringList& listSql, const ZG6000::StringMap& realStep, const ZG6000::StringMap& realNode, const ZG6000::StringMap& realExam,
                             ZG6000::ErrorInfo& e);
    bool processBackResult(ZG6000::StringList& listSql, const ZG6000::StringMap& realStep, const ZG6000::StringMap& realNode, const ZG6000::StringMap& realExam,
                           ZG6000::ErrorInfo& e);
    std::string generateInitialStepSql(const std::string& stepID);
    std::string generateInitialNodeSql(const std::string& nodeID);
    bool saveExam(const std::string& examID, ZG6000::ErrorInfo& e);
    bool getExamInfo(const std::string& realExamID, QJsonObject& examObj, ZG6000::ErrorInfo& e);
    bool getExamNodeInfo(const ZG6000::ListStringMap& listNode, QJsonArray& nodeArray, ZG6000::ErrorInfo& e);
    bool getExamStepInfo(const ZG6000::ListStringMap& listStep, QJsonArray& stepArray, ZG6000::ErrorInfo& e);
    void dispatchTableData(const std::string& tableName, const std::string& oper, const std::string& reason, const std::string& time, ZG6000::ListRecord listRecord);
    void processExamChange(ZG6000::MapField record);
    void processNodeChange(ZG6000::MapField record);
    void processStepChange(ZG6000::MapField record);

private slots:
    void onReceivedMessage(const QString& channel, const QString& message);

private:
    bool m_initialized{false};
    QString m_serverName;
    QString m_instName;
    bool m_masterInst{false};
    int m_initInterval{10};
    int m_checkInterval{5};
    QTimer m_initTimer;
    QTimer m_checkTimer;
    int m_currentStep{0};
    ZG6000::StringMap m_mapAuthMode;
    ZG6000::StringMap m_mapAppNode;
    ZG6000::StringMap m_mapExamMode;
    ZG6000::StringMap m_mapExamState;
    ZG6000::StringMap m_mapExamResult;
    ZG6000::MapStringMap m_mapExamParam;
    ZG6000::MapStringMap m_mapNodeParam;
    ZG6000::MapStringMap m_mapStepParam;
    std::map<std::string, ZG6000::StringList> m_mapExamNode;
    std::map<std::string, ZG6000::StringList> m_mapNodeStep;
    std::map<std::string, ZG6000::StringList> m_mapNodeAuth;
    ZGMqttClient* m_pMqttClient{nullptr};
    ZGRedisClient* m_pRedisTopic{nullptr};
};

#endif // ZGSPEXAMMANAGERMNG_H

#ifndef ZGSPEXAMMANAGERI_H
#define ZGSPEXAMMANAGERI_H

#include <ZGSPExamManager.h>

namespace ZG6000
{
    class ZGSPExamManagerI : public virtual  ZGSPExamManager
    {
    public:
        ZGSPExamManagerI();
        bool checkState(const Ice::Current& current) override;
        bool createExam(std::string paramExamID, std::string& examID, ErrorInfo& e, const Ice::Current& current) override;
        bool getExamInfo(std::string examID, std::string& examInfo, ErrorInfo& e, const Ice::Current& current) override;
        bool execStep(std::string appNodeID, std::string stepID, StringMap params, ErrorInfo& e, const Ice::Current& current) override;
        bool deleteExam(std::string examID, ErrorInfo& e, const Ice::Current& current) override;
        bool finishExam(std::string examID, ErrorInfo& e, const Ice::Current& current) override;
    };
}


#endif // ZGSPEXAMMANAGERI_H

#include "ZGSPExamManagerI.h"
#include "ZGSPExamManagerMng.h"

ZG6000::ZGSPExamManagerI::ZGSPExamManagerI()
{
    ZGSPExamManagerMng::instance()->init();
}

bool ZG6000::ZGSPExamManagerI::checkState(const Ice::Current& current)
{
    return ZGSPExamManagerMng::instance()->checkState();
}

bool ZG6000::ZGSPExamManagerI::createExam(std::string paramExamID, std::string& examID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSPExamManagerMng::instance()->createExam(paramExamID, examID, e);
}

bool ZG6000::ZGSPExamManagerI::getExamInfo(std::string examID, std::string& examInfo, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSPExamManagerMng::instance()->getExamInfo(examID, examInfo, e);
}

bool ZG6000::ZGSPExamManagerI::execStep(std::string appNodeID, std::string stepID, StringMap params, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSPExamManagerMng::instance()->execStep(appNodeID, stepID, params, e);
}

bool ZG6000::ZGSPExamManagerI::deleteExam(std::string examID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSPExamManagerMng::instance()->deleteExam(examID, e);
}

bool ZG6000::ZGSPExamManagerI::finishExam(std::string examID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSPExamManagerMng::instance()->finishExam(examID, e);
}

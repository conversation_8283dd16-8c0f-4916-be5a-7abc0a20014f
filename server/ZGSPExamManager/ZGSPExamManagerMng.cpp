#include "ZGSPExamManagerMng.h"

#include "ZGHeartMng.h"
#include "ZGJson.h"
#include "ZGMqttClient.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include "rapidjson/stringbuffer.h"
#include "rapidjson/writer.h"
#include "redis/ZGRedisClient.h"
#include "zgerror/ZGSPExamManagerError.h"

using namespace rapidjson;

static ZGSPExamManagerMng* g_pInstance = nullptr;

const std::string templateTable = "sp_param_exam";
const std::string templateNodeTable = "sp_param_exam_node";
const std::string templateAuthTable = "sp_param_exam_node_auth";
const std::string templateStepTable = "sp_param_exam_node_step";

ZGSPExamManagerMng* ZGSPExamManagerMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGSPExamManagerMng;
    return g_pInstance;
}

void ZGSPExamManagerMng::init()
{
    initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::sleep(m_initInterval);
    }
    while (!initDictParam())
    {
        ZGLOG_ERROR("initDictParam error.");
        QThread::sleep(m_initInterval);
    }
    while (!initExamParam())
    {
        ZGLOG_ERROR("initDictParam error.");
        QThread::sleep(m_initInterval);
    }
    while (!initRedisClient())
    {
        ZGLOG_ERROR("initRedisClient error.");
        QThread::sleep(m_initInterval);
    }
    while (!initMqttClient())
    {
        ZGLOG_ERROR("initRedisClient error.");
        QThread::sleep(m_initInterval);
    }
    m_initialized = true;
    ZGLOG_INFO("ZGSPExamManager init finished.");
}

bool ZGSPExamManagerMng::checkState()
{
    return m_initialized;
}

bool ZGSPExamManagerMng::createExam(const std::string& examID, std::string& realExamID, ZG6000::ErrorInfo& e)
{
    auto pairExam = m_mapExamParam.find(examID);
    if (pairExam == m_mapExamParam.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("找不到审批ID'%1'").arg(examID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!ZGProxyCommon::createUUID(realExamID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建UUID失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZG6000::StringList listSql;
    ZG6000::StringMap realExam;
    realExam["id"] = realExamID;
    realExam["paramExamID"] = examID;
    realExam["examStateID"] = "ZG_ES_READY";
    QDateTime currTime = QDateTime::currentDateTime();
    const auto& startTime = ZGUtils::DateTimeToString(currTime).toStdString();
    realExam["startTime"] = startTime;   
    const auto& listNodeID = m_mapExamNode[examID];
    ZG6000::StringList listRealNodeID;
    if (!ZGProxyCommon::createUUID(static_cast<int>(listNodeID.size()), listRealNodeID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建节点UUID失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    for (size_t i = 0; i < listNodeID.size(); ++i)
    {
        ZG6000::StringMap realNode;
        realNode["id"] = listRealNodeID[i];
        if (i ==  0)
            realExam["currentExamNodeID"] = listRealNodeID[i];
        realNode["examID"] = realExamID;
        realNode["paramExamNodeID"] = listNodeID[i];
        realNode["nodeIndex"] = std::to_string(i + 1);
        realNode["currentExamStepID"] = "-1";
        realNode["examStateID"] = "ZG_ES_READY";
        const auto& listStepID = m_mapNodeStep[listNodeID[i]];
        ZG6000::StringList listRealStepID;
        if (!ZGProxyCommon::createUUID(static_cast<int>(listStepID.size()), listRealStepID))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建步骤UUID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (size_t j = 0; j < listStepID.size(); ++j)
        {
            ZG6000::StringMap realStep;
            realStep["id"] = listRealStepID[j];
            if (j == 0)
            {
                if (i == 0)
                {
                    realExam["currentExamStepID"] = listRealStepID[j];
                    listSql.push_back(ZGUtils::generateInsertSql("sp_real_exam", realExam));
                }
                realNode["currentExamStepID"] = listRealStepID[j];
                listSql.push_back(ZGUtils::generateInsertSql("sp_real_exam_node", realNode));
            }
            realStep["examNodeID"] = listRealNodeID[i];
            realStep["paramExamStepID"] = listStepID[j];
            realStep["stepIndex"] = std::to_string(j + 1);
            listSql.push_back(ZGUtils::generateInsertSql("sp_real_exam_step", realStep));
        }
    }
    for (const auto& sql: listSql)
    {
        ZGLOG_DEBUG(sql.c_str());
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("创建审批失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPExamManagerMng::getExamInfo(const std::string& realExamID, std::string& examInfo, ZG6000::ErrorInfo& e)
{
    QJsonObject examObj;
    if (!getExamInfo(realExamID, examObj, e))
        return false;
    QJsonDocument doc(examObj);
    examInfo = doc.toJson().data();
    return true;
}

bool ZGSPExamManagerMng::execStep(const std::string& appNodeID, const std::string& stepID, const ZG6000::StringMap& params, ZG6000::ErrorInfo& e)
{
    ZG6000::StringMap realStep, realNode, realExam;
    if (!getRealExamParam(stepID, realStep, realNode, realExam, e))
        return false;
    if (!checkStepValid(appNodeID, realStep, e))
        return false;
    QString errMsg;
    if (!checkRequiredParam(params, {"examUserID", "examResultID"}, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = errMsg.toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    auto updateParams = params;
    updateParams["id"] = stepID;
    updateParams["examTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString();
    ZG6000::StringList listSql;
    listSql.push_back(ZGUtils::generateUpdateSql("sp_real_exam_step", updateParams));
    const auto& examResultID = ZGUtils::get(params, "examResultID", "");
    if (examResultID == "ZG_ER_ACCEPT")
    {
        if (!processAcceptResult(listSql, realStep, realNode, realExam, e))
            return false;
    }
    else if (examResultID == "ZG_ER_BACK")
    {
        if (!processBackResult(listSql, realStep, realNode, realExam, e))
            return false;
    }
    else if (examResultID == "ZG_ER_REJECT")
    {
        if (!processRejectResult(listSql, realStep, realNode, realExam, e))
            return false;
    }
    else
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("无效的审批结果'%1'").arg(examResultID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("步骤'%1'审批失败").arg(stepID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

bool ZGSPExamManagerMng::execExamStep(const std::string& examStepID, const std::string& stepInfo, ZG6000::ErrorInfo& e)
{
    ZG6000::StringMap mapExamStep;
    std::string sql = "SELECT * FROM sp_real_exam_step WHERE id = '" + examStepID + "'";
    if (!ZGProxyCommon::execQuerySqlRow(sql, mapExamStep))
    {
        return false;
    }
    try
    {
        const auto& examNodeID = ZGUtils::get(mapExamStep, "examNodeID");
        const auto& paramExamStepID = ZGUtils::get(mapExamStep, "paramExamStepID");
        ZG6000::StringMap mapExamNode;
        sql = "SELECT * FROM sp_real_exam_node WHERE id = '" + examNodeID + "'";
        if (!ZGProxyCommon::execQuerySqlRow(sql, mapExamNode))
        {
            return false;
        }
        const auto& examID = ZGUtils::get(mapExamNode, "examID");
        const auto& paramExamNodeID = ZGUtils::get(mapExamNode, "paramExamNodeID");
        const auto& nodeParam = ZGUtils::get(m_mapNodeParam, paramExamNodeID);
        const auto& examModeID = ZGUtils::get(nodeParam, "examModeID");
        ZG6000::StringMap mapExam;
        sql = "SELECT * FROM sp_real_exam WHERE id = '" + examID + "'";
        if (!ZGProxyCommon::execQuerySqlRow(sql, mapExam))
        {
            return false;
        }
        const auto& examStateID = ZGUtils::get(mapExam, "examStateID");
        if (examStateID != "ZG_ES_NOT_EXAM" || examStateID != "ZG_ES_EXAMING")
        {
            return false;
        }
        const auto& currentExamNodeID = ZGUtils::get(mapExam, "currentExamNodeID");
        const auto& currentExamStepID = ZGUtils::get(mapExam, "currentExamStepID");
        if (currentExamStepID != examStepID || currentExamNodeID != examNodeID)
        {
            return false;
        }
        ZG6000::StringMap stringMap;
        std::string errMsg;
        if (!ZGJson::convertFromJson(stepInfo, stringMap, errMsg))
        {
            return false;
        }
        return true;
    }
    catch (const std::exception& e)
    {
        return false;
    }
}

bool ZGSPExamManagerMng::deleteExam(const std::string& examID, ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT id FROM sp_real_exam_node WHERE examID = '%1'").arg(examID.c_str());
    ZG6000::StringList listNodeID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listNodeID))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取审批'%1'审批节点失败").arg(examID.c_str()).toStdString();
        ZGLOG_ERROR(e.errDetail.c_str());
        return false;
    }
    const auto& nodes = ZGUtils::join(listNodeID, ",", "'", "'");
    ZG6000::StringList listSql;
    if (!listNodeID.empty())
    {
        sql = QString("DELETE FROM sp_real_exam_step WHERE examNodeID IN (%1);").arg(nodes.c_str());
        listSql.push_back(sql.toStdString());
        sql = QString("DELETE FROM sp_real_exam_node WHERE examID = '%1';").arg(examID.c_str());
        listSql.push_back(sql.toStdString());
    }
    sql = QString("DELETE FROM sp_real_exam WHERE id = '%1';").arg(examID.c_str());
    listSql.push_back(sql.toStdString());
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("删除审批'%1'失败").arg(examID.c_str()).toStdString();
        ZGLOG_ERROR(e.errDetail.c_str());
        return false;
    }
    return true;
}

bool ZGSPExamManagerMng::cancelExam(const std::string& examID, ZG6000::ErrorInfo& e)
{
    ZG6000::StringMap mapFieldValue;
    mapFieldValue.insert(std::make_pair("examStateID", "ZG_ES_EXAM_ABORT"));
    mapFieldValue.insert(std::make_pair("endTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString()));
    if (!ZGProxyCommon::updateDataByID("sp_real_exam", examID, mapFieldValue))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = "update sp_real_exam error, examID = " + examID;
        ZGLOG_ERROR(e.errDetail.c_str());
        return false;
    }
    return true;
}

bool ZGSPExamManagerMng::finishExam(const std::string& examID, ZG6000::ErrorInfo& e)
{
    if (!saveExam(examID, e))
        return false;
    return deleteExam(examID, e);
}

ZGSPExamManagerMng::ZGSPExamManagerMng(QObject* parent) : QObject(parent)
{
}

void ZGSPExamManagerMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
}

bool ZGSPExamManagerMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGSPExamManagerMng::initDictParam()
{
    std::string sql = "SELECT id, name FROM sp_dict_auth_mode";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapAuthMode))
        return false;
    sql = "SELECT id, name FROM sp_dict_exam_mode";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapExamMode))
        return false;
    sql = "SELECT id, name FROM sp_dict_exam_state";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapExamState))
        return false;
    sql = "SELECT id, name FROM sp_dict_exam_result";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapExamResult))
        return false;
    sql = "SELECT id, name FROM sp_param_appnode";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapAppNode))
        return false;
    return true;
}

bool ZGSPExamManagerMng::initExamParam()
{
    std::string sql = "SELECT * FROM sp_param_exam";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapExamParam))
    {
        ZGLOG_ERROR(QStringLiteral("获取审批参数失败"));
        return false;
    }
    sql = "SELECT * FROM sp_param_exam_node ORDER BY nodeIndex";
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql, m_mapNodeParam))
    {
        ZGLOG_ERROR(QStringLiteral("获取审批节点参数失败"));
        return false;
    }
    for (const auto& [nodeID, nodeParam] : m_mapNodeParam)
    {
        const auto& examID = ZGUtils::get(nodeParam, "examID", "");
        m_mapExamNode[examID].push_back(nodeID);
    }
    sql = "SELECT * FROM sp_param_exam_node_step ORDER BY stepIndex";
    ZG6000::ListStringMap listMapResult;
    if (!ZGProxyCommon::execQuerySql(sql, m_mapStepParam))
    {
        ZGLOG_ERROR(QString("获取审批步骤参数失败"));
        return false;
    }
    for (const auto& [stepID, stepParam] : m_mapStepParam)
    {
        const auto& examNodeID = ZGUtils::get(stepParam, "examNodeID", "");
        m_mapNodeStep[examNodeID].push_back(stepID);
    }
    return true;
}

bool ZGSPExamManagerMng::initMqttClient()
{
    if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
    {
        ZGLOG_ERROR("initMqttClient error.");
        return false;
    }
    m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
    if (m_pMqttClient == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage return null.");
        return false;
    }
    m_pMqttClient->connectToHost();
    return true;
}

bool ZGSPExamManagerMng::initRedisClient()
{
    QList<ZGRuntime::REDIS_CLIENT_TYPE> listType;
    listType << ZGRuntime::REDIS_RT_TOPIC;
    if (!ZGRuntime::instance()->initRedisClient(listType))
    {
        ZGLOG_ERROR("initRedisClient error.");
        return false;
    }
    m_pRedisTopic = ZGRuntime::instance()->getRedisClientRTTopic();
    if (m_pRedisTopic == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTTopic error.");
        return false;
    }
    QThread::msleep(1000);
    if (!m_pRedisTopic->connected())
        return false;
    const ZG6000::StringList listTopic{"sp_real_exam", "sp_real_exam_node", "sp_real_exam_step"};
    m_pRedisTopic->subscribe(listTopic);
    connect(m_pRedisTopic, &ZGRedisClient::receivedMessage, this, &ZGSPExamManagerMng::onReceivedMessage);
    m_pRedisTopic->consume();
    return true;
}

bool ZGSPExamManagerMng::checkRequiredParam(const ZG6000::StringMap& params, const ZG6000::StringList& listParam, QString& errMsg)
{
    for (const auto& param : listParam)
    {
        if (params.find(param) == params.end())
        {
            errMsg = QStringLiteral("找不到必要的参数'%1'").arg(param.c_str());
            return false;
        }
    }
    return true;
}

bool ZGSPExamManagerMng::checkStepValid(const std::string& appNodeID, const ZG6000::StringMap& realStep, ZG6000::ErrorInfo& e)
{
    try
    {
        const auto& stepID = ZGUtils::get(realStep, "id");
        if (stepID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到审批步骤'%1'").arg(stepID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& paramExamStepID = ZGUtils::get(realStep, "paramExamStepID");
        auto pairStep = m_mapStepParam.find(paramExamStepID);
        if (pairStep == m_mapStepParam.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到实时审批步骤'%1'关联的参数审批步骤").arg(stepID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& paramStep = pairStep->second;
        const auto& examNodeID = ZGUtils::get(paramStep, "examNodeID", "");
        auto pairNode = m_mapNodeParam.find(examNodeID);
        if (pairNode == m_mapNodeParam.end())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到审批节点'%1'关联的参数").arg(examNodeID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& nodeParam = pairNode->second;
        const auto& nodeAppNodeID = ZGUtils::get(nodeParam, "appNodeID", "");
        if (appNodeID != nodeAppNodeID)
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("当前步骤'%1'应执行应用节点为'%2'，当前为'%3'").arg(stepID.c_str()).arg(nodeAppNodeID.c_str()).arg(appNodeID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPExamManagerMng::getRealExamParam(const std::string& realStepID, ZG6000::StringMap& realStep, ZG6000::StringMap& realNode, ZG6000::StringMap& realExam, ZG6000::ErrorInfo& e)
{
    try
    {
        QString sql = QString("SELECT * FROM sp_real_exam_step WHERE id = '%1'").arg(realStepID.c_str());
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), realStep))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取审批步骤'%1'的信息失败").arg(realStepID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (realStep["id"].empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到审批步骤'%1'").arg(realStepID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& examNodeID = ZGUtils::get(realStep, "examNodeID");
        sql = QString("SELECT * FROM sp_real_exam_node WHERE id = '%1'").arg(examNodeID.c_str());
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), realNode))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取实时审批节点'%1'参数失败").arg(examNodeID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (realNode["id"].empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到实时审批节点'%1'").arg(examNodeID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& examID = ZGUtils::get(realNode, "examID");
        sql = QString("SELECT * FROM sp_real_exam WHERE id = '%1'").arg(examID.c_str());
        if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), realExam))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取实时审批'%1'参数失败").arg(examID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (realExam["id"].empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到实时审批节点'%1'").arg(examNodeID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPExamManagerMng::processAcceptResult(ZG6000::StringList& listSql, const ZG6000::StringMap& realStep, const ZG6000::StringMap& realNode,
                                             const ZG6000::StringMap& realExam, ZG6000::ErrorInfo& e)
{
    try
    {
        int stepIndex = std::atoi(ZGUtils::get(realStep, "stepIndex").c_str());
        ++stepIndex;
        ZGLOG_DEBUG(QStringLiteral("下一步骤序号: '%1'").arg(stepIndex));
        const auto& nodeID = ZGUtils::get(realNode, "id");
        const auto& examID = ZGUtils::get(realExam, "id");
        QString sql = QString("SELECT id, examNodeID FROM sp_real_exam_step WHERE examNodeID = '%1' AND stepIndex = %2").arg(nodeID.c_str()).arg(stepIndex);
        ZG6000::ListStringMap listStep;
        ZG6000::StringMap updateNode, updateExam;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listStep))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取步骤失败，节点ID = '%1', 步骤序号 = '%2'").arg(nodeID.c_str()).arg(stepIndex).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listStep.empty())
        {
            QDateTime currTime = QDateTime::currentDateTime();
            updateNode["id"] = nodeID;
            updateNode["examStateID"] = "ZG_ES_ACCEPT";
            updateNode["endTime"] = ZGUtils::DateTimeToString(currTime, true).toStdString();
            listSql.push_back(ZGUtils::generateUpdateSql("sp_real_exam_node", updateNode));
            int nodeIndex = std::atoi(ZGUtils::get(realNode, "nodeIndex").c_str());
            ++nodeIndex;
            sql = QString("SELECT id FROM sp_real_exam_node WHERE examID = '%1' AND nodeIndex = %2").arg(examID.c_str()).arg(nodeIndex);
            ZG6000::StringList listNodeID;
            if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listNodeID))
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
                e.errDetail = QStringLiteral("获取节点失败，审批ID: '%1', 步骤序号: %2").arg(examID.c_str()).arg(stepIndex).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            // 所有审批已完成
            if (listNodeID.empty())
            {
                updateExam["id"] = examID;
                updateExam["examStateID"] = "ZG_ES_ACCEPT";
                updateExam["endTime"] = ZGUtils::DateTimeToString(currTime, true).toStdString();
                listSql.push_back(ZGUtils::generateUpdateSql("sp_real_exam", updateExam));
            }
            else
            {
                if (listNodeID.size() > 1)
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("审批'%1'存在多于一个审批节点序号: '%2'").arg(examID.c_str()).arg(nodeIndex).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                stepIndex = 1;
                sql = QString("SELECT id FROM sp_real_exam_step WHERE examNodeID = '%1' AND stepIndex = %2").arg(nodeID.c_str()).arg(stepIndex);
                ZG6000::StringList listStepID;
                if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listStepID))
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
                    e.errDetail = QStringLiteral("获取步骤ID失败，节点ID = '%1', 步骤序号 = '%2'").arg(nodeID.c_str()).arg(stepIndex).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                if (listStepID.empty())
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
                    e.errDetail = QStringLiteral("审批节点'%1'下找不到审批步骤").arg(listNodeID[0].c_str()).toStdString();
                    ZGLOG_ERROR(e);
                    return false;
                }
                updateNode["id"] = listNodeID[0];
                updateNode["examStateID"] = "ZG_ES_EXAM";
                updateNode["currentExamStepID"] = listStepID[0];
                listSql.push_back(ZGUtils::generateUpdateSql("sp_real_exam_node", updateNode));
                updateExam["id"] = examID;
                updateExam["examStateID"] = "ZG_ES_EXAM";
                updateExam["currentExamNodeID"] = listNodeID[0];
                updateExam["currentExamStepID"] = listStepID[0];
                listSql.push_back(ZGUtils::generateUpdateSql("sp_real_exam", updateExam));
            }
        }
        else
        {
            updateNode["id"] = ZGUtils::get(listStep[0], "examNodeID");
            updateNode["examStateID"] = "ZG_ES_EXAM";
            updateNode["currentExamStepID"] = ZGUtils::get(listStep[0], "id");
            listSql.push_back(ZGUtils::generateUpdateSql("sp_real_exam_node", updateNode));
            updateExam["id"] = examID;
            updateExam["examStateID"] = "ZG_ES_EXAM";
            updateExam["currentExamNodeID"] = ZGUtils::get(listStep[0], "examNodeID");
            updateExam["currentExamStepID"] = ZGUtils::get(listStep[0], "id");
            listSql.push_back(ZGUtils::generateUpdateSql("sp_real_exam", updateExam));
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPExamManagerMng::processRejectResult(ZG6000::StringList& listSql, const ZG6000::StringMap& realStep, const ZG6000::StringMap& realNode, const ZG6000::StringMap& realExam, ZG6000::ErrorInfo& e)
{
    ZG6000::StringMap updateNode, updateExam;
    try
    {
        updateNode["id"] = ZGUtils::get(realNode, "id");
        updateNode["examStateID"] = "ZG_ES_REJECT";
        QDateTime currTime = QDateTime::currentDateTime();
        updateNode["endTime"] = ZGUtils::DateTimeToString(currTime, true).toStdString();
        listSql.push_back(ZGUtils::generateUpdateSql("sp_real_exam_node", updateNode));
        updateExam["id"] = ZGUtils::get(realExam, "id");
        updateExam["examStateID"] = "ZG_ES_REJECT";
        updateExam["endTime"] = ZGUtils::DateTimeToString(currTime, true).toStdString();
        listSql.push_back(ZGUtils::generateUpdateSql("sp_real_exam", updateExam));
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPExamManagerMng::processBackResult(ZG6000::StringList& listSql, const ZG6000::StringMap& realStep, const ZG6000::StringMap& realNode, const ZG6000::StringMap& realExam, ZG6000::ErrorInfo& e)
{
    return false;
}

std::string ZGSPExamManagerMng::generateInitialStepSql(const std::string& stepID)
{
    ZG6000::StringMap realStepParam;
    realStepParam["id"] = stepID;
    realStepParam["examUserID"] = "";
    realStepParam["examTime"] = "";
    realStepParam["examInfo"] = "";
    realStepParam["examResultID"] = "";
    realStepParam["paramAuthModeID"] = "";
    return ZGUtils::generateUpdateSql("sp_real_exam_step", realStepParam);
}

std::string ZGSPExamManagerMng::generateInitialNodeSql(const std::string& nodeID)
{
    try
    {
        const auto& listStepID = ZGUtils::get(m_mapNodeStep, nodeID);
        ZG6000::StringMap realNodeParam;
        realNodeParam["id"] = nodeID;
        if (!listStepID.empty())
            realNodeParam["currentExamStepID"] = listStepID[0];
        realNodeParam["examStateID"] = "ZG_ES_READY";
        realNodeParam["startTime"] = "";
        realNodeParam["endTime"] = "";
        return ZGUtils::generateUpdateSql("sp_real_exam_node", realNodeParam);
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

bool ZGSPExamManagerMng::saveExam(const std::string &examID, ZG6000::ErrorInfo &e)
{
    QString sql = QString("SELECT id, examStateID, startTime, endTime FROM sp_real_exam WHERE id = '%1'").arg(examID.c_str());
    ZG6000::StringMap exam;
    if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), exam))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取审批'%1'信息失败").arg(examID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    exam["examStateName"] = ZGUtils::get(m_mapExamState, exam["examStateID"], "");
    int year = QDate::currentDate().year();
    QString examTable = QString("sp_his_exam_%1").arg(year);
    QString examNodeTable = QString("sp_his_exam_node_%1").arg(year);
    QString examStepTable = QString("sp_his_exam_step_%1").arg(year);
    sql = QString("SELECT id FROM %1 WHERE id = '%2'").arg(examTable).arg(examID.c_str());
    ZG6000::StringList listExamID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listExamID, true))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取历史审批'%1'信息失败").arg(examID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!listExamID.empty())
    {
        ZG6000::StringList listDelSql;
        QString sql = QString("SELECT id FROM sp_real_exam_node WHERE examID = '%1'").arg(examID.c_str());
        ZG6000::StringList listNodeID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listNodeID))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取审批'%1'审批节点失败").arg(examID.c_str()).toStdString();
            ZGLOG_ERROR(e.errDetail.c_str());
            return false;
        }
        const auto& nodes = ZGUtils::join(listNodeID, ",", "'", "'");
        ZG6000::StringList listSql;
        if (!listNodeID.empty())
        {
            sql = QString("DELETE FROM %1 WHERE examNodeID IN (%2);").arg(examStepTable).arg(nodes.c_str());
            listDelSql.push_back(sql.toStdString());
            sql = QString("DELETE FROM %1 WHERE examID = '%2';").arg(examNodeTable).arg(examID.c_str());
            listDelSql.push_back(sql.toStdString());
        }
        sql = QString("DELETE FROM %1 WHERE id = '%2';").arg(examTable).arg(examID.c_str());
        listDelSql.push_back(sql.toStdString());
        if (!ZGProxyCommon::execBatchSql(listDelSql, true))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("删除历史审批'%1'信息失败").arg(examID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
    }
    ZG6000::StringList listSql;
    listSql.push_back(ZGUtils::generateInsertSql(examTable.toStdString(), exam));
    sql = QString("SELECT id, examID, examStateID, startTime, endTime FROM sp_real_exam_node WHERE examID = '%1'").arg(examID.c_str());
    ZG6000::ListStringMap listExamNode;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listExamNode))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取审批'%1'节点信息失败").arg(examID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    for (auto& examNode: listExamNode)
    {
        examNode["examStateName"] = ZGUtils::get(m_mapExamState, examNode["examStateID"], "");
        listSql.push_back(ZGUtils::generateInsertSql(examNodeTable.toStdString(), examNode));
        sql = QString("SELECT id, examNodeID, stepIndex, examUserID, examTime, examInfo, "
                      "examResultID FROM sp_real_exam_step WHERE examNodeID = '%1'").arg(examNode["id"].c_str());
        ZG6000::ListStringMap listExamStep;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listExamStep))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取审批节点'%1'步骤信息失败").arg(examNode["id"].c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (auto& examStep: listExamStep)
        {
            examStep["examResultName"] = ZGUtils::get(m_mapExamResult, examStep["examResultID"], "");
            std::string examUserName;
            ZGProxyCommon::getDataByField("sp_param_hrm_user", examStep["examUserID"], "name", examUserName);
            examStep["examUserName"] = examUserName;
            listSql.push_back(ZGUtils::generateInsertSql(examStepTable.toStdString(), examStep));
        }
    }
    if (!ZGProxyCommon::execBatchSql(listSql, true))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("保存审批'%1'信息失败").arg(examID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSPExamManagerMng::getExamInfo(const std::string& realExamID, QJsonObject& examObj, ZG6000::ErrorInfo& e)
{
    QString sql = QString("SELECT * FROM sp_real_exam WHERE id = '%1'").arg(realExamID.c_str());
    ZG6000::StringMap realExam;
    if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), realExam))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取审批'%1'信息失败").arg(realExamID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (realExam["id"].empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("找不到审批'%1'记录").arg(realExamID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        const auto& paramExamID = ZGUtils::get(realExam, "paramExamID");
        const auto& paramExam = ZGUtils::get(m_mapExamParam, paramExamID);
        examObj["id"] = realExamID.c_str();
        examObj["name"] = ZGUtils::get(paramExam, "name").c_str();
        examObj["currentExamNodeID"] = ZGUtils::get(realExam, "currentExamNodeID").c_str();
        examObj["currentExamStepID"] = ZGUtils::get(realExam, "currentExamStepID").c_str();
        const auto& stateID = ZGUtils::get(realExam, "examStateID");
        examObj["examStateID"] = stateID.c_str();
        examObj["examStateName"] = ZGUtils::get(m_mapExamState, stateID, "").c_str();;
        examObj["startTime"] = ZGUtils::get(realExam, "startTime").c_str();
        examObj["endTime"] = ZGUtils::get(realExam, "endTime").c_str();
        sql = QString("SELECT * FROM sp_real_exam_node WHERE examID = '%1'").arg(realExamID.c_str());
        ZG6000::ListStringMap listNode;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listNode))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取审批'%1'节点信息失败").arg(realExamID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        QJsonArray nodeArray;
        if (!getExamNodeInfo(listNode, nodeArray, e))
            return false;
        examObj["node"] = nodeArray;
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPExamManagerMng::getExamNodeInfo(const ZG6000::ListStringMap& listNode, QJsonArray& nodeArray, ZG6000::ErrorInfo& e)
{
    try
    {
        for (const auto & node : listNode)
        {
            QJsonObject nodeObj;
            const auto& paramNodeID = ZGUtils::get(node, "paramExamNodeID");
            const auto& nodeParam = ZGUtils::get(m_mapNodeParam, paramNodeID);
            const auto& nodeID = ZGUtils::get(node, "id");
            nodeObj["id"] = nodeID.c_str();
            nodeObj["name"] = ZGUtils::get(nodeParam, "name").c_str();
            const auto& appNodeID = ZGUtils::get(nodeParam, "appNodeID");
            nodeObj["appNodeID"] = appNodeID.c_str();
            nodeObj["appNodeName"] = ZGUtils::get(m_mapAppNode, appNodeID, "").c_str();
            const auto& examModeID = ZGUtils::get(nodeParam, "examModeID");
            nodeObj["examModeID"] = examModeID.c_str();
            nodeObj["examModeName"] = ZGUtils::get(m_mapExamMode, examModeID, "").c_str();
            nodeObj["currentExamStepID"] = ZGUtils::get(node, "currentExamStepID").c_str();
            const auto& stateID = ZGUtils::get(node, "examStateID");
            nodeObj["examStateID"] = stateID.c_str();
            nodeObj["examStateName"] = ZGUtils::get(m_mapExamState, stateID, "").c_str();
            nodeObj["startTime"] = ZGUtils::get(node, "startTime").c_str();
            nodeObj["endTime"] = ZGUtils::get(node, "endTime").c_str();
            QString sql = QString("SELECT * FROM sp_real_exam_step WHERE examNodeID = '%1'").arg(nodeID.c_str());
            ZG6000::ListStringMap listStep;
            if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listStep))
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_DB);
                e.errDetail = QStringLiteral("获取节点'%1'步骤信息失败").arg(nodeID.c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            QJsonArray stepArray;
            if (!getExamStepInfo(listStep, stepArray, e))
                return false;
            nodeObj["step"] = stepArray;
            nodeArray.append(nodeObj);
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGSPExamManagerMng::getExamStepInfo(const ZG6000::ListStringMap& listStep, QJsonArray& stepArray, ZG6000::ErrorInfo& e)
{
    try
    {
        for (const auto& step : listStep)
        {
            QJsonObject stepObj;
            const auto& paramStepID = ZGUtils::get(step, "paramExamStepID");
            const auto& stepParam = ZGUtils::get(m_mapStepParam, paramStepID);
            stepObj["id"] = ZGUtils::get(step, "id").c_str();
            stepObj["name"] = ZGUtils::get(stepParam, "name").c_str();
            stepObj["stepIndex"] = ZGUtils::get(stepParam, "stepIndex").c_str();
            stepObj["powerID"] = ZGUtils::get(stepParam, "powerID").c_str();
            stepObj["roleID"] = ZGUtils::get(stepParam, "roleID").c_str();
            stepObj["userID"] = ZGUtils::get(stepParam, "userID").c_str();
            const auto& examUserID = ZGUtils::get(step, "examUserID");
            stepObj["examUserID"] = examUserID.c_str();
            std::string examUserName;
            if (!examUserID.empty())
            {
                if (!ZGProxyCommon::getDataByField("sp_param_hrm_user", examUserID, "name", examUserName))
                    ZGLOG_ERROR(QStringLiteral("获取用户'%1'名字失败").arg(examUserID.c_str()));
            }
            stepObj["examUserName"] = examUserName.c_str();
            stepObj["examTime"] = ZGUtils::get(step, "examTime").c_str();
            stepObj["examInfo"] = ZGUtils::get(step, "examInfo").c_str();
            const auto& examResultID = ZGUtils::get(step, "examResultID");
            stepObj["examResultID"] = examResultID.c_str();
            stepObj["examResultName"] = ZGUtils::get(m_mapExamResult, examResultID, "").c_str();
            stepArray.append(stepObj);
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPExamManager::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

void ZGSPExamManagerMng::dispatchTableData(const std::string& tableName, const std::string& oper, const std::string& reason, const std::string& time, ZG6000::ListRecord listRecord)
{
    if (reason != "change")
        return;
    if (oper == "update")
    {
        for (auto& record : listRecord)
        {
            if (tableName == "sp_real_exam")
                processExamChange(std::move(record));
            else if (tableName == "sp_real_exam_node")
                processNodeChange(std::move(record));
            else if (tableName == "sp_real_exam_step")
                processStepChange(std::move(record));
        }
    }
}

void ZGSPExamManagerMng::processExamChange(ZG6000::MapField record)
{
    QJsonObject root;
    QJsonObject dataObj;
    try
    {
        std::string examID;
        for (const auto& field : record)
        {
            if (field.first == "id")
                examID = field.second.newValue;
            if (field.first == "examStateID" && (!field.second.newValue.empty()))
                dataObj.insert("examStateName", ZGUtils::get(m_mapExamState, field.second.newValue, "").c_str());
            dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
        }
        root["head"] = dataObj;
        QJsonDocument doc(root);
        const auto& json = doc.toJson();
        ZGLOG_DEBUG(json);
        m_pMqttClient->sendPublish(QString("sp_real_exam/%1/update").arg(examID.c_str()), json);
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGSPExamManagerMng::processNodeChange(ZG6000::MapField record)
{
    QJsonObject root;
    QJsonObject dataObj;
    try
    {
        std::string nodeID;
        for (const auto& field : record)
        {
            if (field.first == "id")
                nodeID = field.second.newValue;
            if (field.first == "examStateID" && (!field.second.newValue.empty()))
                dataObj.insert("examStateName", ZGUtils::get(m_mapExamState, field.second.newValue, "").c_str());
            dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
        }
        std::string examID;
        if (!ZGProxyCommon::getDataByField("sp_real_exam_node", nodeID, "examID", examID))
        {
            ZGLOG_ERROR(QStringLiteral("获取节点'%1'审批ID失败").arg(nodeID.c_str()));
            return;
        }
        root["node"] = dataObj;
        QJsonDocument doc(root);
        const auto& json = doc.toJson();
        ZGLOG_DEBUG(json);
        m_pMqttClient->sendPublish(QString("sp_real_exam/%1/update").arg(examID.c_str()), json);
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGSPExamManagerMng::processStepChange(ZG6000::MapField record)
{
    QJsonObject root;
    QJsonObject dataObj;
    try
    {
        std::string stepID;
        for (const auto& field : record)
        {
            if (field.first == "id")
                stepID = field.second.newValue;
            if (field.first == "examUserID" && (!field.second.newValue.empty()))
            {
                std::string userName;
                ZGProxyCommon::getDataByField("sp_param_hrm_user", field.second.newValue, "name", userName);
                dataObj.insert("examUserName", userName.c_str());
            }
            if (field.first == "examResultID" && (!field.second.newValue.empty()))
                dataObj.insert("examResultName", ZGUtils::get(m_mapExamResult, field.second.newValue, "").c_str());
            dataObj.insert(field.first.c_str(), field.second.newValue.c_str());
        }
        std::string nodeID;
        if (!ZGProxyCommon::getDataByField("sp_real_exam_step", stepID, "examNodeID", nodeID))
        {
            ZGLOG_ERROR(QStringLiteral("获取步骤'%1'节点ID失败").arg(stepID.c_str()));
            return;
        }
        std::string examID;
        if (!ZGProxyCommon::getDataByField("sp_real_exam_node", nodeID, "examID", examID))
        {
            ZGLOG_ERROR(QStringLiteral("获取节点'%1'审批ID失败").arg(nodeID.c_str()));
            return;
        }
        root["step"] = dataObj;
        QJsonDocument doc(root);
        const auto& json = doc.toJson();
        ZGLOG_DEBUG(json);
        m_pMqttClient->sendPublish(QString("sp_real_exam/%1/update").arg(examID.c_str()), json);
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGSPExamManagerMng::onReceivedMessage(const QString& channel, const QString& message)
{
    if (!ZGRuntime::instance()->isMaster())
        return;
    std::string tableName;
    std::string oper;
    std::string reason;
    std::string aTime;
    std::string errMsg;
    ZG6000::ListRecord listRecord;
    if (!ZGJson::convertFromJsonToListRecord(message.toStdString(), tableName, oper,
        reason, aTime, listRecord, errMsg))
    {
        ZGLOG_ERROR(errMsg.c_str());
        return;
    }
    dispatchTableData(tableName, oper, reason, aTime, listRecord);
}

#ifndef ZGMPREALWARNMNG_H
#define ZGMPREALWARNMNG_H

#include <QObject>
#include <QTimer>
#include <Ice/Ice.h>
#include "ZGServerCommon.h"

class ZGMqttClient;

namespace ZG6000
{
    class ZGMPRealWarnMng : public QObject
    {
        Q_OBJECT

    public:
        static ZGMPRealWarnMng* instance();
        bool checkState(const Ice::Current& current);
        void dispatchData(std::string tableName,
                          std::string oper,
                          std::string reason,
                          std::string time,
                          ListRecord listRecord,
                          const Ice::Current& current);
        bool getRealWarnByAppNode(std::string appNodeID,
                                  std::string subsystemID,
                                  ListStringMap& listRealWarn,
                                  ErrorInfo& e,
                                  const Ice::Current& current);

    public:
        void init();

    private slots:
        void onTimer();

    private:
        explicit ZGMPRealWarnMng(QObject* parent = nullptr);
        void initEvents();
        void initServerInstConfig();
        bool initServerInstInfo();
        bool initRealWarnParams();
        bool initMqttClient();

    private:
        bool m_initialized{false};
        int m_initInterval{10};
        int m_checkInterval{2};
        QString m_serverName{""};
        QString m_instName{""};
        std::string m_localNodeID;
        MapStringMap m_mapRealWarnPoints; // 实时告警点参数
        StringList m_listPointID; // 实时告警点ID
        StringList m_listAppNodeID; // 应用节点ID
        MapStringMap m_mapDataCategoryProperty; // 数据类别属性
        std::map<std::string, StringList> m_mapTopicRealWarn; // 每个主题包含的实时告警
        std::map<std::string, StringList> m_mapDatasetTopics; // 每个数据集关联的主题
        QString m_alarmLevel{"ZG_AL_LEVEL1"};
        ZGMqttClient* m_pMqttClient{nullptr};
        QTimer m_timer;
    };

    inline static ZGMPRealWarnMng* g_pInstance = nullptr;
} // namespace ZG6000

#endif // ZGMPREALWARNMNG_H

#include "ZGMPRealWarnMng.h"
#include "ZGRuntime.h"
#include "ZGProxyCommon.h"
#include "ZGDebugMng.h"
#include "ZGMqttClient.h"
#include "ZGUtils.h"
#include "zgerror/ZGMPRealWarnError.h"

#include <QJsonDocument>
#include <QThread>

namespace ZG6000
{
    ZGMPRealWarnMng* ZGMPRealWarnMng::instance()
    {
        if (!g_pInstance)
            g_pInstance = new ZGMPRealWarnMng;
        return g_pInstance;
    }

    bool ZGMPRealWarnMng::checkState(const Ice::Current& current)
    {
        return m_initialized;
    }

    void ZGMPRealWarnMng::dispatchData(std::string tableName,
                                       std::string oper,
                                       std::string reason,
                                       std::string time,
                                       ListRecord listRecord,
                                       const Ice::Current& current)
    {
        if (!m_initialized)
            return;
        if (oper != "update")
            return;
        if (reason != "change")
            return;
        for (const auto& record : listRecord)
        {
            try
            {
                const auto& id = ZGUtils::get(record, "id").newValue;
                auto pair = m_mapRealWarnPoints.find(id);
                if (pair == m_mapRealWarnPoints.end())
                    continue;
                auto it = record.find("rtNewValue");
                if (it == record.end())
                    continue;
                const auto& newValue = ZGUtils::get(record, "rtNewValue").newValue;
                const auto& updateTime = ZGUtils::get(record, "rtUpdateTime").newValue;
                const auto& realWarnParam = pair->second;
                const auto& realWarnDatasetID = ZGUtils::get(realWarnParam, "datasetID");
                const auto& dataCategoryID = ZGUtils::get(realWarnParam, "dataCategoryID");
                const auto& dataCategoryProperty = dataCategoryID + "/" + newValue;
                const auto& dataCategoryPropertyDesc = ZGUtils::getName(m_mapDataCategoryProperty, dataCategoryProperty,
                    "propName");
                const auto& dataCategoryPropertyDescL2 = ZGUtils::getName(m_mapDataCategoryProperty,
                    dataCategoryProperty, "propNameL2");
                QJsonObject object;
                for (const auto& [key, value] : realWarnParam)
                {
                    object[key.c_str()] = value.c_str();
                }
                object["rtNewValue"] = newValue.c_str();
                object["rtUpdateTime"] = updateTime.c_str();
                object["rtNewValueDesc"] = dataCategoryPropertyDesc.c_str();
                object["rtNewValueDescL2"] = dataCategoryPropertyDescL2.c_str();
                QJsonDocument doc(object);
                QByteArray ba = doc.toJson(QJsonDocument::Compact);
                // 根据数据集得到主题列表
                const auto& topics = ZGUtils::get(m_mapDatasetTopics, realWarnDatasetID);
                for (const auto& topic : topics)
                {
                    const auto topicName = QString("realWarn/%1").arg(topic.c_str());
                    m_pMqttClient->sendPublish(topicName, ba);
                }
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(e.what());
            }
        }
    }

    bool ZGMPRealWarnMng::getRealWarnByAppNode(std::string appNodeID,
                                               std::string subsystemID,
                                               ListStringMap& listRealWarn,
                                               ErrorInfo& e,
                                               const Ice::Current& current)
    {
        std::string topic = appNodeID + "/" + subsystemID;
        auto pair = m_mapTopicRealWarn.find(topic);
        if (pair == m_mapTopicRealWarn.end())
            return true;
        const auto& listRealWarnID = pair->second;
        ListStringMap listOriginRealWarn;
        if (!ZGProxyCommon::mgetDataByFields("mp_param_dataset_yx", listRealWarnID, {"rtNewValue", "rtUpdateTime"},
            listOriginRealWarn))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPRealWarn::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取实时告警失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            int index = 0;
            for (auto& realWarn : listOriginRealWarn)
            {
                const auto& id = listRealWarnID[index];
                ++index;
                const auto& realWarnParam = ZGUtils::get(m_mapRealWarnPoints, id);
                const auto& alarmValue = ZGUtils::get(realWarnParam, "alarmValue");
                StringList listAlarmValue;
                size_t size = ZGUtils::splitString(alarmValue, ",", listAlarmValue, true);
                if (size == 0)
                    continue;
                if (size == 2)
                    ZGLOG_TRACE(alarmValue.c_str());
                const auto& rtNewValue = realWarn["rtNewValue"];
                if (std::find(listAlarmValue.begin(), listAlarmValue.end(), rtNewValue) == listAlarmValue.end())
                    continue;
                for (const auto& [key, value] : realWarnParam)
                {
                    realWarn[key] = value;
                }
                const auto& dataCategoryProperty = realWarn["dataCategoryID"] + "/" + rtNewValue;
                const auto& rtNewValueDesc = ZGUtils::getName(m_mapDataCategoryProperty, dataCategoryProperty,
                    "propName");
                const auto& rtNewValueDescL2 = ZGUtils::getName(m_mapDataCategoryProperty, dataCategoryProperty,
                    "propNameL2");
                realWarn["rtNewValueDesc"] = rtNewValueDesc;
                realWarn["rtNewValueDescL2"] = rtNewValueDescL2;
                listRealWarn.push_back(std::move(realWarn));
            }
            return true;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGMPRealWarn::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    void ZGMPRealWarnMng::init()
    {
        initEvents();
        initServerInstConfig();
        while (!initServerInstInfo())
        {
            ZGLOG_ERROR("initServerInstInfo error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initRealWarnParams())
        {
            ZGLOG_ERROR("initRealWarnParams error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initMqttClient())
        {
            ZGLOG_ERROR("initMqttClient error.");
            QThread::msleep(m_initInterval * 1000);
        }
        m_initialized = true;
        m_timer.start(m_checkInterval * 1000);
        ZGLOG_INFO("ZGMPRealWarn init finished.");
    }

    void ZGMPRealWarnMng::onTimer()
    {
        // 根据m_listPointID从实时库中获取实时告警值
        StringMap mapRealWarn;
        if (!ZGProxyCommon::mgetDataByField("mp_param_dataset_yx", m_listPointID, "rtNewValue", mapRealWarn))
        {
            ZGLOG_ERROR(QStringLiteral("获取实时告警值失败"));
            return;
        }
        try
        {
            std::map<std::string, int> mapAppNodeWarns;
            // 先将所有应用节点的实时告警数量初始化为0
            for (const auto& appNodeID : m_listAppNodeID)
            {
                mapAppNodeWarns[appNodeID + "/up"] = 0;
                mapAppNodeWarns[appNodeID + "/down"] = 0;
            }
            for (const auto & [pointID, value] : mapRealWarn)
            {
                // 在实时告警参数中查找
                auto pair = m_mapRealWarnPoints.find(pointID);
                if (pair == m_mapRealWarnPoints.end())
                    continue;
                // 查看当前值是否在告警值范围内
                const auto& realWarnParam = pair->second;
                const auto& alarmValue = ZGUtils::get(realWarnParam, "alarmValue");
                StringList listAlarmValue;
                size_t size = ZGUtils::splitString(alarmValue, ",", listAlarmValue, true);
                if (size == 0)
                    continue;
                if (std::find(listAlarmValue.begin(), listAlarmValue.end(), value) == listAlarmValue.end())
                    continue;
                const auto& position = ZGUtils::get(realWarnParam, "position");
                if (position.empty())
                    continue;
                const auto& appNodeID = ZGUtils::get(realWarnParam, "appNodeID");
                mapAppNodeWarns[appNodeID + "/" + position]++;
            }
            // 将mapAppNodeWarns按照主题发布实时告警
            for (const auto& [appNodePosition, number] : mapAppNodeWarns)
            {
                const auto& topic = "ZG_REALWARN/" + appNodePosition;
                QString message = QString("{\"number\":%1}").arg(number);
                m_pMqttClient->sendPublish(topic.c_str(), message);
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    void ZGMPRealWarnMng::initEvents()
    {
        connect(&m_timer, &QTimer::timeout, this, &ZGMPRealWarnMng::onTimer);
    }

    void ZGMPRealWarnMng::initServerInstConfig()
    {
        const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
        QString errMsg;
        int value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
            ZGLOG_WARN(errMsg);
        else
            m_initInterval = value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
            ZGLOG_WARN(errMsg);
        else
            m_checkInterval = value;
        auto pair = serverGroup.find("alarmLevelID");
        if (pair != serverGroup.end())
            m_alarmLevel = pair.value();
    }

    bool ZGMPRealWarnMng::initServerInstInfo()
    {
        m_serverName = ZGRuntime::instance()->getServerID();
        if (m_serverName.isEmpty())
        {
            ZGLOG_ERROR("Empty server id.");
            return false;
        }
        m_instName = ZGRuntime::instance()->getInstanceID();
        if (m_instName.isEmpty())
        {
            ZGLOG_ERROR("Empty server instance id.");
            return false;
        }
        m_localNodeID = ZGPubFun::getLocalNodeID().toStdString();
        return true;
    }

    bool ZGMPRealWarnMng::initRealWarnParams()
    {
        QString sql = QString(
            "SELECT CONCAT(dataCategoryID,'/', propValue) AS id, propName, propNameL2 FROM mp_param_data_category_property");
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapDataCategoryProperty))
        {
            ZGLOG_ERROR(QStringLiteral("获取数据类别属性失败"));
            return false;
        }
        sql = QString(
            "SELECT a.id, a.name, a.nameL2, a.dataModelID, a.datasetID, b.dataCategoryID, b.alarmValue, f.name AS dataCategoryName, "
            "f.nameL2 AS dataCategoryNameL2, c.name AS datasetName, c.nameL2 AS datasetNameL2, a.deviceID, d.name AS deviceName, "
            "d.nameL2 AS deviceNameL2, d.position, c.appNodeID, e.name AS appNodeName, e.nameL2 AS appNodeNameL2 FROM mp_param_dataset_yx a "
            "LEFT JOIN mp_param_model_yx b ON a.dataModelID = b.id "
            "LEFT JOIN mp_param_dataset c ON a.datasetID = c.id "
            "LEFT JOIN mp_param_device d ON a.deviceID = d.id "
            "LEFT JOIN sp_param_appnode e ON c.appNodeID = e.id "
            "LEFT JOIN mp_dict_data_category f ON b.dataCategoryID = f.id "
            "WHERE b.isEnable = 1 AND c.isEnable = 1 AND d.isEnable = 1 AND (a.deviceID IS NOT NULL AND a.deviceID != '') "
            "AND b.alarmLevelID = '%1' ORDER BY a.id").arg(m_alarmLevel);
        ListStringMap listRealWarnPoints;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRealWarnPoints))
        {
            ZGLOG_ERROR(QStringLiteral("获取实时告警点失败"));
            return false;
        }
        for (auto& item : listRealWarnPoints)
        {
            m_listPointID.push_back(item["id"]);
            m_mapRealWarnPoints[item["id"]] = item;
        }
        ListStringMap listDataset;
        sql = QString("SELECT id, appNodeID, subsystemID FROM mp_param_dataset");
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDataset))
        {
            ZGLOG_ERROR(QStringLiteral("获取数据集应用节点失败"));
            return false;
        }
        for (auto& item : listDataset)
        {
            const auto& datasetID = item["id"];
            const auto& appNodeID = item["appNodeID"];
            const auto& subsystemID = item["subsystemID"];
            const auto& topics = appNodeID + "/" + subsystemID;
            m_mapDatasetTopics[datasetID].push_back(topics);
            m_mapDatasetTopics[datasetID].push_back("/");
        }
        ListStringMap listAppNodeDataset;
        sql = QString("SELECT appNodeID, subsystemID, datasetID FROM mp_param_appnode_dataset");
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listAppNodeDataset))
        {
            ZGLOG_ERROR(QStringLiteral("获取数据集应用节点失败"));
            return false;
        }
        // 加入到数据集应用节点中
        for (auto& item : listAppNodeDataset)
        {
            const auto& appNodeID = item["appNodeID"];
            const auto& subsystemID = item["subsystemID"];
            const auto& datasetID = item["datasetID"];
            const auto& topics = appNodeID + "/" + subsystemID;
            m_mapDatasetTopics[datasetID].push_back(topics);
        }
        // 将实时告警点的参数中的点归类到应用节点的实时告警中
        for (auto& [id, item] : m_mapRealWarnPoints)
        {
            const auto& datasetID = item["datasetID"];
            // 根据数据集得到主题列表
            const auto& topics = m_mapDatasetTopics[datasetID];
            for (const auto& topic : topics)
            {
                m_mapTopicRealWarn[topic].push_back(id);
            }
        }
        const auto& allWarns = m_mapTopicRealWarn["/"];
        ZGLOG_TRACE(QString("allWarns size: %1").arg(allWarns.size()));
        sql = QString("SELECT id FROM sp_param_appnode WHERE appNodeTypeID = 'ZG_AT_STATION' AND isEnable = 1");
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), m_listAppNodeID))
        {
            ZGLOG_ERROR(QStringLiteral("获取应用节点失败"));
            return false;
        }
        return true;
    }

    bool ZGMPRealWarnMng::initMqttClient()
    {
        if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
        {
            ZGLOG_ERROR("initMqttClient error.");
            return false;
        }
        m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
        if (m_pMqttClient == nullptr)
        {
            ZGLOG_ERROR("getMqttClientMessage error.");
            return false;
        }
        m_pMqttClient->connectToHost();
        return true;
    }

    ZGMPRealWarnMng::ZGMPRealWarnMng(QObject* parent)
        : QObject{parent}
    {
    }
} // namespace ZG6000

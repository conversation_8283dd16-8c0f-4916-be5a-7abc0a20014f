#ifndef ZGMPREALWARNI_H
#define ZGMPREALWARNI_H

#include <ZGMPRealWarn.h>

namespace ZG6000
{
    class ZGMPRealWarnI : public virtual ZGMPRealWarn
    {
    public:
        ZGMPRealWarnI();

    public:
        bool checkState(const Ice::Current& current) override;
        void dispatchData(std::string tableName,
                          std::string oper,
                          std::string reason,
                          std::string time,
                          ListRecord listRecord,
                          const Ice::Current& current) override;
        bool getRealWarnByAppNode(std::string appNodeID,
                                  std::string subsystemID,
                                  ListStringMap& listRealWarn,
                                  ErrorInfo& e,
                                  const Ice::Current& current) override;
    };
} // namespace ZG6000

#endif // ZGMPREALWARNI_H

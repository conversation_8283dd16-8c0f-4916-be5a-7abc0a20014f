#include "ZGMPRealWarnI.h"
#include "ZGMPRealWarnMng.h"

namespace ZG6000
{
    ZGMPRealWarnI::ZGMPRealWarnI()
    {
        ZGMPRealWarnMng::instance()->init();
    }

    bool ZG6000::ZGMPRealWarnI::checkState(const Ice::Current& current)
    {
        return ZGMPRealWarnMng::instance()->checkState(current);
    }

    void ZG6000::ZGMPRealWarnI::dispatchData(std::string tableName,
                                             std::string oper,
                                             std::string reason,
                                             std::string time,
                                             ListRecord listRecord,
                                             const Ice::Current& current)
    {
        return ZGMPRealWarnMng::instance()->dispatchData(std::move(tableName), std::move(oper), std::move(reason), std::move(time), std::move(listRecord), current);
    }

    bool ZG6000::ZGMPRealWarnI::getRealWarnByAppNode(std::string appNodeID,
                                                     std::string subsystemID,
                                                     ListStringMap& listRealWarn,
                                                     ErrorInfo& e,
                                                     const Ice::Current& current)
    {
        return ZGMPRealWarnMng::instance()->getRealWarnByAppNode(std::move(appNodeID), std::move(subsystemID), listRealWarn, e, current);
    }

} // namespace ZG6000

#include <ZGSPRTStatisticProcessI.h>
#include "ZGSPRTStatisticProcessMng.h"

ZG6000::ZGSPRTStatisticProcessI::ZGSPRTStatisticProcessI()
{
    ZGSPRTStatisticProcessMng::instance()->init();
}

bool
ZG6000::ZGSPRTStatisticProcessI::checkState(const Ice::Current&)
{
    return ZGSPRTStatisticProcessMng::instance()->checkState();
}

void
ZG6000::ZGSPRTStatisticProcessI::statistic(std::string id,
                                           const Ice::Current&)
{
    ZGSPRTStatisticProcessMng::instance()->statistic(id);
}

void
ZG6000::ZGSPRTStatisticProcessI::statisticBatch(StringList listID,
                                                const Ice::Current&)
{
    ZGSPRTStatisticProcessMng::instance()->statisticBatch(listID);
}

void ZG6000::ZGSPRTStatisticProcessI::statisticStart(std::string id, const Ice::Current&)
{
    ZGSPRTStatisticProcessMng::instance()->statisticStart(id);
}

void ZG6000::ZGSPRTStatisticProcessI::statisticStartBatch(StringList listID, const Ice::Current&)
{
    ZGSPRTStatisticProcessMng::instance()->statisticStartBatch(listID);
}

void ZG6000::ZGSPRTStatisticProcessI::statisticStartAndCalc(std::string id, const Ice::Current& current)
{
    ZGSPRTStatisticProcessMng::instance()->statisticStartAndCalc(id);
}

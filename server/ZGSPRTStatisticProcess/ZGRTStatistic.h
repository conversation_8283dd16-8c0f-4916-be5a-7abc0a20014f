#ifndef ZGRTSTATISTIC_H
#define ZGRTSTATISTIC_H

#include "ZGStatistic.h"

class ZGRTStatistic : public ZGStatistic
{
    Q_OBJECT
public:
    explicit ZGRTStatistic(QObject *parent = nullptr);

protected:
    void calculateMax(const StatisticParam& statisticParam, bool start) override;
    void calculateMin(const StatisticParam& statisticParam, bool start) override;
    void calculateAvg(const StatisticParam& statisticParam, bool start) override;
    void calculateCount(const StatisticParam& statisticParam, bool start) override;
    void calculateSum(const StatisticParam& statisticParam, bool start) override;
    bool parseStatisticParam(ZG6000::StringMap& mapFieldValue, StatisticParam& statisticParam) override;

private:
    using Compare = std::function<bool(const std::string_view& dataValue)>;
    void calculate(const StatisticParam& statisticParam, const Compare& compare, bool start);
};

#endif // ZGRTSTATISTIC_H

#ifndef __ZGSPRTStatisticProcessI_h__
#define __ZGSPRTStatisticProcessI_h__

#include <ZGSPRTStatisticProcess.h>

namespace ZG6000
{
    class ZGSPRTStatisticProcessI : public virtual ZGSPRTStatisticProcess
    {
    public:
        ZGSPRTStatisticProcessI();
        bool checkState(const Ice::Current&) override;

        void statistic(std::string,
                       const Ice::Current&) override;

        void statisticBatch(StringList,
                            const Ice::Current&) override;

        void statisticStart(std::string id, const Ice::Current& current) override;

        void statisticStartBatch(StringList listID, const Ice::Current& current) override;

        void statisticStartAndCalc(std::string id, const Ice::Current& current) override;
    };
}

#endif

#include "ZGSPRTStatisticProcessMng.h"

#include "ZGDebugMng.h"
#include "ZGHeartMng.h"
#include "ZGProxyMng.h"
#include "ZGRTStatistic.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"

static ZGSPRTStatisticProcessMng* g_pInstance = nullptr;

ZGSPRTStatisticProcessMng* ZGSPRTStatisticProcessMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGSPRTStatisticProcessMng;
    return g_pInstance;
}

void ZGSPRTStatisticProcessMng::init()
{
    initEvents();
    initServerInstConfig();
    start();
    ZGLOG_INFO("ZGSPRTStatisticProcess init start...");
}

bool ZGSPRTStatisticProcessMng::checkState()
{
    return m_initialized;
}

void ZGSPRTStatisticProcessMng::statistic(const std::string& id)
{
    m_pStatistic->statistic(id);
}

void ZGSPRTStatisticProcessMng::statisticBatch(const ZG6000::StringList& listID)
{
    for (const auto& id : listID)
    {
        m_pStatistic->statistic(id);
    }
}

void ZGSPRTStatisticProcessMng::statisticStart(std::string id)
{
    m_pStatistic->statistic(id, true, false);
}

void ZGSPRTStatisticProcessMng::statisticStartBatch(ZG6000::StringList listID)
{
    for (const auto& id : listID)
    {
        m_pStatistic->statistic(id, true, false);
    }
}

void ZGSPRTStatisticProcessMng::statisticStartAndCalc(std::string id)
{
    m_pStatistic->statistic(id, true, true);
}

void ZGSPRTStatisticProcessMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPRTStatisticProcessMng::onCheckStatus);
    connect(this, &ZGSPRTStatisticProcessMng::initFinished, this, &ZGSPRTStatisticProcessMng::onInitFinished);
}

void ZGSPRTStatisticProcessMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGSPRTStatisticProcessMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

void ZGSPRTStatisticProcessMng::onInitFinished()
{
    m_checkTimer.start(m_checkInterval * 1000);
}

void ZGSPRTStatisticProcessMng::onCheckStatus()
{
    m_masterInst = ZGRuntime::instance()->isMaster();
}

ZGSPRTStatisticProcessMng::ZGSPRTStatisticProcessMng(QObject* parent) : QThread(parent)
{
    m_pStatistic = new ZGRTStatistic;
}


void ZGSPRTStatisticProcessMng::run()
{
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        msleep(m_initInterval * 1000);
    }
    while (!m_pStatistic->initialize())
    {
        ZGLOG_ERROR("Statistic initialize error.");
        msleep(m_initInterval * 1000);
    }
    m_masterInst = ZGRuntime::instance()->isMaster();
    m_initialized = true;
    emit initFinished();
    ZGLOG_INFO("ZGSPRTStatisticProcess init finished.");
}

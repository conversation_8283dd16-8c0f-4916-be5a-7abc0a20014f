#ifndef ZGSPRTSTATISTICPROCESSMNG_H
#define ZGSPRTSTATISTICPROCESSMNG_H

#include <QThread>
#include <QTimer>
#include <ZGProxyMng.h>

class ZGStatistic;
class ZGSPRTStatisticProcessMng : public QThread
{
    Q_OBJECT
public:
    static ZGSPRTStatisticProcessMng* instance();
    void init();
    bool checkState();
    void statistic(const std::string& id);
    void statisticBatch(const ZG6000::StringList& listID);
    void statisticStart(std::string id);
    void statisticStartBatch(ZG6000::StringList listID);
    void statisticStartAndCalc(std::string id);

    // QThread interface
protected:
    virtual void run();

private:
    explicit ZGSPRTStatisticProcessMng(QObject* parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();

signals:
    void initFinished();

private slots:
    void onInitFinished();
    void onCheckStatus();

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    bool m_masterInst{false};
    int m_initInterval{10};
    int m_checkInterval{5};
    QTimer m_initTimer;
    QTimer m_checkTimer;
    int m_currentStep{0};
    ZGStatistic *m_pStatistic{nullptr};
};

#endif // ZGSPRTSTATISTICPROCESSMNG_H

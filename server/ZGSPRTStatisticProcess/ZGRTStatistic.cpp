#include "ZGRTStatistic.h"
#include "ZGUtils.h"
#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"

ZGRTStatistic::ZGRTStatistic(QObject* parent) : ZGStatistic(parent)
{
}

void ZGRTStatistic::calculateMax(const StatisticParam& statisticParam, bool start)
{
    calculate(statisticParam,
        [&statisticParam](const std::string_view& dataValue)-> bool
        {
            if (statisticParam.dataType == DataType::dtReal)
            {
                if (statisticParam.rtValue.empty())
                    return true;
                try
                {
                    double newValue = std::stod(dataValue.data());
                    double oldValue = std::stod(statisticParam.rtValue);
                    return (newValue - oldValue > ZGUtils::eps);
                }
                catch (const std::exception&)
                {
                    ZGLOG_ERROR(
                        QString("Convert value error, newValue = %1, oldValue = %2").arg(dataValue.data()).arg(
                            statisticParam.rtValue.c_str()));
                    return false;
                }
            }
            if (statisticParam.dataType == DataType::dtNumber)
            {
                if (statisticParam.rtValue.empty())
                    return true;
                try
                {
                    int newValue = std::stoi(dataValue.data());
                    int oldValue = std::stoi(statisticParam.rtValue);
                    return (newValue > oldValue);
                }
                catch (const std::exception&)
                {
                    ZGLOG_ERROR(
                        QString("Convert value error, newValue = %1, oldValue = %2").arg(dataValue.data()).arg(
                            statisticParam.rtValue.c_str()));
                    return false;
                }
            }
            return false;
        },
        start);
}

void ZGRTStatistic::calculateMin(const StatisticParam& statisticParam, bool start)
{
    calculate(statisticParam,
        [&statisticParam](const std::string_view& dataValue)-> bool
        {
            if (statisticParam.dataType == DataType::dtReal)
            {
                if (statisticParam.rtValue.empty())
                    return true;
                try
                {
                    double newValue = std::stod(dataValue.data());
                    double oldValue = std::stod(statisticParam.rtValue);
                    return (oldValue - newValue > ZGUtils::eps);
                }
                catch (const std::exception&)
                {
                    ZGLOG_ERROR(
                        QString("Convert value error, newValue = %1, oldValue = %2").arg(dataValue.data()).arg(
                            statisticParam.rtValue.c_str()));
                    return false;
                }
            }
            if (statisticParam.dataType == DataType::dtNumber)
            {
                if (statisticParam.rtValue.empty())
                    return true;
                try
                {
                    int newValue = std::stoi(dataValue.data());
                    int oldValue = std::stoi(statisticParam.rtValue);
                    return (oldValue > newValue);
                }
                catch (const std::exception&)
                {
                    ZGLOG_ERROR(
                        QString("Convert value error, newValue = %1, oldValue = %2").arg(dataValue.data()).arg(
                            statisticParam.rtValue.c_str()));
                    return false;
                }
            }
            return false;
        },
        start);
}

void ZGRTStatistic::calculateAvg(const StatisticParam& statisticParam, bool start)
{
    if (statisticParam.dataType == DataType::dtText)
    {
        ZGLOG_ERROR(
            QString("current dataValue can't average, id = %1, dataValue = %2").arg(statisticParam.id.c_str()).arg(
                statisticParam.dataValue.c_str()));
        return;
    }
    ZG6000::StringMap mapFieldValue;
    std::string calcDataValue = statisticParam.dataValue;
    if (statisticParam.statisticModeID == "ZG_SM_ABS" && calcDataValue[0] == '-')
        calcDataValue = calcDataValue.substr(1);
    if (start || statisticParam.rtValue.empty() || statisticParam.rtSampleTotalValue.empty() 
        || statisticParam.rtSampleNum.empty())
    {
        mapFieldValue.insert(std::make_pair("rtValue", calcDataValue));
        mapFieldValue.insert(std::make_pair("rtSampleTotalValue", calcDataValue));
        mapFieldValue.insert(std::make_pair("rtSampleNum", "1"));
        mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        if (start)
            mapFieldValue.insert(std::make_pair("rtIsNewStatistic", "1"));
    }
    else
    {
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        try
        {
            double totalValue = std::stod(statisticParam.rtSampleTotalValue);
            int totalNum = std::stoi(statisticParam.rtSampleNum);
            double newValue = std::stod(calcDataValue.data());
            totalValue += newValue;
            ++totalNum;
            double newAvgValue = totalValue / totalNum;
            mapFieldValue.insert(std::make_pair("rtValue", std::to_string(newAvgValue)));
            mapFieldValue.insert(std::make_pair("rtSampleTotalValue", std::to_string(totalValue)));
            mapFieldValue.insert(std::make_pair("rtSampleNum", std::to_string(totalNum)));
            mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
        }
        catch (const std::exception&)
        {
            ZGLOG_ERROR(
                QString("calculate average error, convert failed, dataValue = %1, totalValue = %2, totalNum = %3")
                .arg(calcDataValue.data()).arg(statisticParam.rtSampleTotalValue.c_str()).arg(statisticParam.rtSampleNum
                    .c_str()));
        }
    }
    if (!ZGProxyCommon::updateDataByID("sp_param_statistic", statisticParam.id, mapFieldValue))
    {
        ZGLOG_ERROR(QString("updateDataByIDFromMap error, id = %1").arg(statisticParam.id.c_str()));
    }
}

void ZGRTStatistic::calculateCount(const StatisticParam& statisticParam, bool start)
{
    ZG6000::StringMap mapFieldValue;
    if (start || statisticParam.rtValue.empty())
    {
        mapFieldValue.insert(std::make_pair("rtValue", "1"));
        mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        if (start)
            mapFieldValue.insert(std::make_pair("rtIsNewStatistic", "1"));
    }
    else
    {
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        try
        {
            int value = std::stoi(statisticParam.rtValue);
            ++value;
            mapFieldValue.insert(std::make_pair("rtValue", std::to_string(value)));
            mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
            mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        }
        catch (const std::exception&)
        {
            ZGLOG_ERROR(
                QString("calculate count error, convert failed, rtValue = %1").arg(statisticParam.rtValue.c_str()));
        }
    }
    if (!ZGProxyCommon::updateDataByID("sp_param_statistic", statisticParam.id, mapFieldValue))
    {
        ZGLOG_ERROR(QString("updateDataByIDFromMap error, id = %1").arg(statisticParam.id.c_str()));
    }
}

void ZGRTStatistic::calculateSum(const StatisticParam& statisticParam, bool start)
{
    ZG6000::StringMap mapFieldValue;
    if (start || statisticParam.rtValue.empty())
    {
        mapFieldValue.insert(std::make_pair("rtValue", statisticParam.dataValue));
        mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        if (start)
            mapFieldValue.insert(std::make_pair("rtIsNewStatistic", "1"));
    }
    else
    {
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        try
        {
            double oldValue = std::stod(statisticParam.rtValue);
            double newValue = std::stod(statisticParam.dataValue);
            newValue += oldValue;
            mapFieldValue.insert(std::make_pair("rtValue", std::to_string(newValue)));
            mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
            mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        }
        catch (const std::exception&)
        {
            ZGLOG_ERROR(
                QString("calculate count error, convert failed, rtValue = %1").arg(statisticParam.rtValue.c_str()));
        }
    }
    if (!ZGProxyCommon::updateDataByID("sp_param_statistic", statisticParam.id, mapFieldValue))
    {
        ZGLOG_ERROR(QString("updateDataByIDFromMap error, id = %1").arg(statisticParam.id.c_str()));
    }
}

bool ZGRTStatistic::parseStatisticParam(ZG6000::StringMap& mapFieldValue, StatisticParam& statisticParam)
{
    if (!ZGStatistic::parseStatisticParam(mapFieldValue, statisticParam))
        return false;
    if (!ZGProxyCommon::getDataByKey(statisticParam.key, statisticParam.dataValue))
    {
        ZGLOG_ERROR(QString("getDataByKeyToValue error, key = %1").arg(statisticParam.key.c_str()));
        return false;
    }
    if (ZGUtils::isNumber(statisticParam.dataValue))
        statisticParam.dataType = DataType::dtNumber;
    else if (ZGUtils::isReal(statisticParam.dataValue))
        statisticParam.dataType = DataType::dtReal;
    else
        statisticParam.dataType = DataType::dtText;
    return true;
}

void ZGRTStatistic::calculate(const StatisticParam& statisticParam, const Compare& compare, bool start)
{
    ZG6000::StringMap mapFieldValue;
    if (start || statisticParam.dataValue.empty())
    {
        mapFieldValue.insert(std::make_pair("rtValue", statisticParam.dataValue));
        mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        if (start)
            mapFieldValue.insert(std::make_pair("rtIsNewStatistic", "1"));
    }
    else
    {
        std::string_view calcDataValue{statisticParam.dataValue.c_str()};
        if (statisticParam.statisticModeID == "ZG_SM_ABS" && calcDataValue[0] == '-')
            calcDataValue.remove_prefix(1);
        if (compare(calcDataValue))
        {
            mapFieldValue.insert(std::make_pair("rtValue", calcDataValue));
            mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
        }
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
    }
    if (!ZGProxyCommon::updateDataByID("sp_param_statistic", statisticParam.id, mapFieldValue))
    {
        ZGLOG_ERROR(QString("updateDataByIDFromMap error, id = %1").arg(statisticParam.id.c_str()));
    }
}

#include "ZGStatistic.h"

#include "ZGRuleProcess.h"
#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"
#include "ZGUtils.h"

ZGStatistic::ZGStatistic(QObject* parent) : QObject(parent)
{
}

bool ZGStatistic::initialize()
{
    initStatisticType();
    return true;
}

void ZGStatistic::statistic(const std::string& id, bool start, bool calc)
{
    ZG6000::StringMap mapFieldValue;
    if (!ZGProxyCommon::getDataByID("sp_param_statistic", id, mapFieldValue))
    {
        ZGLOG_ERROR(QString("get statistic param error, id = %1").arg(id.c_str()));
        return;
    }
    StatisticParam statisticParam;
    if (!parseStatisticParam(mapFieldValue, statisticParam))
    {
        ZGLOG_ERROR("parseStatisticParam error.");
        return;
    }
    QDateTime currTime = QDateTime::currentDateTime();
    QString newUpdateTime = currTime.toString("yyyy-MM-dd hh:mm:ss.zzz");
    if (start && (!calc))
    {
        updateDefaultStatistic(statisticParam.id, newUpdateTime.toStdString());
        return;
    }
    bool result;
    if (!evaluateByMode(statisticParam, result))
    {
        ZGLOG_ERROR(QString("evaluateByMode error, current mode %1 can't apply to data value %2")
            .arg(statisticParam.statisticModeID.c_str()).arg(statisticParam.dataValue.c_str()));
        return;
    }
    ZG6000::StringMap mapUpdateValue;
    if (!result)
    {
        if (start)
            updateDefaultStatistic(statisticParam.id, newUpdateTime.toStdString());
        return;
    }
    std::string errMsg;
    if (!statisticParam.operatorID.empty() && !statisticParam.operatorValue.empty())
    {
        if (!ZGRuleProcess::instance()->compare(statisticParam.dataValue,
            statisticParam.operatorID,
            statisticParam.operatorValue,
            dataTypeToString(statisticParam.dataType),
            result,
            errMsg))
        {
            ZGLOG_ERROR(QString("compare value error, dataValue = %1, operatorID = %2, operValue = %3")
                .arg(statisticParam.dataValue.c_str()).arg(statisticParam.operatorID.c_str())
                .arg(statisticParam.operatorValue.c_str()));
            return;
        }
        if (!result)
        {
            if (start)
                updateDefaultStatistic(statisticParam.id, newUpdateTime.toStdString());
            return;
        }
    }
    auto pair = m_mapTypeProcess.find(statisticParam.statisticTypeID);
    if (pair != m_mapTypeProcess.end())
        pair->second(statisticParam, start);
    else
        ZGLOG_ERROR(QString("Can't find statistic type %1").arg(statisticParam.statisticTypeID.c_str()));
}

void ZGStatistic::updateDefaultStatistic(const std::string& statisticID, const std::string& updateTime)
{
    ZG6000::StringMap mapUpdateValue;
    mapUpdateValue.insert(std::make_pair("rtUpdateTime", updateTime));
    mapUpdateValue.insert(std::make_pair("rtValue", ""));
    mapUpdateValue.insert(std::make_pair("rtOccurTime", ""));
    mapUpdateValue.insert(std::make_pair("rtSampleTotalValue", ""));
    mapUpdateValue.insert(std::make_pair("rtSampleNum", ""));
    mapUpdateValue.insert(std::make_pair("rtIsNewStatistic", "1"));
    if (!ZGProxyCommon::updateDataByID("sp_param_statistic", statisticID, mapUpdateValue))
        ZGLOG_ERROR(QString("update statistic %1 default value error").arg(statisticID.c_str()));
}

std::string ZGStatistic::dataTypeToString(DataType dataType)
{
    switch (dataType)
    {
    case DataType::dtText:
        return "string";
    case DataType::dtNumber:
        return "int";
    case DataType::dtReal:
        return "double";
    }
    return "";
}

bool ZGStatistic::parseStatisticParam(ZG6000::StringMap& mapFieldValue, StatisticParam& statisticParam)
{
    try
    {
        statisticParam.id = ZGUtils::get(mapFieldValue, "id");
        statisticParam.statisticTypeID = ZGUtils::get(mapFieldValue, "statisticTypeID");
        statisticParam.statisticIntervalID = ZGUtils::get(mapFieldValue, "statisticIntervalID");
        statisticParam.statisticModeID = ZGUtils::get(mapFieldValue, "statisticModeID");
        statisticParam.tableName = ZGUtils::get(mapFieldValue, "tableName");
        statisticParam.dataID = ZGUtils::get(mapFieldValue, "dataID");
        statisticParam.fieldName = ZGUtils::get(mapFieldValue, "fieldName");
        statisticParam.key = statisticParam.tableName + "/" + statisticParam.dataID + "$" + statisticParam.fieldName;
        statisticParam.operatorID = ZGUtils::get(mapFieldValue, "operatorID");
        statisticParam.operatorValue = ZGUtils::get(mapFieldValue, "value");
        statisticParam.sampleRatio = ZGUtils::get(mapFieldValue, "sampleRatio");
        statisticParam.rtValue = ZGUtils::get(mapFieldValue, "rtValue");
        statisticParam.rtOccurTime = ZGUtils::get(mapFieldValue, "rtOccurTime");
        statisticParam.rtSampleTotalValue = ZGUtils::get(mapFieldValue, "rtSampleTotalValue");
        statisticParam.rtSampleNum = ZGUtils::get(mapFieldValue, "rtSampleNum");
        statisticParam.rtUpdateTime = ZGUtils::get(mapFieldValue, "rtUpdateTime");
        QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
        statisticParam.rtNewUpdateTime = currentTime.toStdString();
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

void ZGStatistic::initStatisticType()
{
    m_mapTypeProcess.insert({"ZG_ST_MAX", [this](auto&& placeHolder1, auto&& placeHolder2) 
        { calculateMax(std::forward<decltype(placeHolder1)>(placeHolder1), std::forward<decltype(placeHolder2)>(placeHolder2)); }});
    m_mapTypeProcess.insert({"ZG_ST_MIN", [this](auto&& placeHolder1, auto&& placeHolder2) 
        { calculateMin(std::forward<decltype(placeHolder1)>(placeHolder1), std::forward<decltype(placeHolder2)>(placeHolder2)); }});
    m_mapTypeProcess.insert({"ZG_ST_AVG", [this](auto&& placeHolder1, auto&& placeHolder2) 
        { calculateAvg(std::forward<decltype(placeHolder1)>(placeHolder1), std::forward<decltype(placeHolder2)>(placeHolder2)); }});
    m_mapTypeProcess.insert({"ZG_ST_COUNT", [this](auto&& placeHolder1, auto&& placeHolder2) 
        { calculateCount(std::forward<decltype(placeHolder1)>(placeHolder1), std::forward<decltype(placeHolder2)>(placeHolder2)); }});
    m_mapTypeProcess.insert({"ZG_ST_SUM", [this](auto&& placeHolder1, auto&& placeHolder2) 
        { calculateSum(std::forward<decltype(placeHolder1)>(placeHolder1), std::forward<decltype(placeHolder2)>(placeHolder2)); }});
}

bool ZGStatistic::evaluateByMode(const StatisticParam& statisticParam, bool& result)
{
    try
    {
        if (statisticParam.statisticModeID == "ZG_SM_POS")
        {
            switch (statisticParam.dataType)
            {
            case DataType::dtText:
                return false;
            case DataType::dtNumber:
                result = std::stoi(statisticParam.dataValue) > 0;
                return true;
            case DataType::dtReal:
                result = std::stod(statisticParam.dataValue) > ZGUtils::eps;
                return true;
            }
        }
        if (statisticParam.statisticModeID == "ZG_SM_NEG")
        {
            switch (statisticParam.dataType)
            {
            case DataType::dtText:
                return false;
            case DataType::dtNumber:
                result = std::stoi(statisticParam.dataValue) < 0;
                return true;
            case DataType::dtReal:
                result = std::stod(statisticParam.dataValue) < -ZGUtils::eps;
                return true;
            }
        }
        if (statisticParam.statisticModeID == "ZG_SM_ABS")
        {
            return statisticParam.dataType != DataType::dtText;
        }
        result = true;
        return true;
    }
    catch (const std::exception&)
    {
        ZGLOG_ERROR(QString("Convert dataValue error, dataValue = %1").arg(statisticParam.dataValue.c_str()));
        return false;
    }
}

#ifndef ZGSPEVENTPARSESERVICE_H
#define ZGSPEVENTPARSESERVICE_H

#include "ZGSPEventParseBase.h"

class ZGSPEventParseService : public ZGSPEventParseBase
{
	Q_OBJECT
public:
	explicit ZGSPEventParseService(QObject* parent = nullptr);
protected:
	std::string getAddition() override;
	std::string getAlarmLevelID() override;
	std::string getEventInfo() override;
    std::string getEventInfoL2() override;
	std::string getEventTypeID() override;
	std::string getIsPublishEvent() override;
	bool checkDataValid(const ZG6000::MapField& record) override;
	bool parseRecord(const ZG6000::MapField& record) override;
public:
	bool initParam() override;
private:
	bool initServiceParam();
private:
	std::unordered_map<std::string, HashParam> m_mapServiceParam;
	std::string m_serviceID;
	std::string m_serviceName;
    std::string m_serviceNameL2;
	std::string m_alarmLevelID;
	std::string m_rtState;
};

#endif // ZGSPEVENTPARSESERVICE_H

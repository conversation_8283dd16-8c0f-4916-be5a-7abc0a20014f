#ifndef ZGSPEVENTPARSESERVICEINST_H
#define ZGSPEVENTPARSESERVICEINST_H

#include "ZGSPEventParseBase.h"

class ZGSPEventParseServiceInst : public ZGSPEventParseBase
{
	Q_OBJECT
public:
	explicit ZGSPEventParseServiceInst(QObject* parent = nullptr);
public:
	bool initParam() override;
protected:
	bool checkDataValid(const ZG6000::MapField& record) override;
	bool parseRecord(const ZG6000::MapField& record) override;
	std::string getAddition() override;
	std::string getAlarmLevelID() override;
	std::string getEventInfo() override;
    std::string getEventInfoL2() override;
	std::string getEventTypeID() override;
	std::string getIsPublishEvent() override;

private:
	bool initServiceInstParam();

private:
	std::unordered_map<std::string, HashParam> m_mapServiceInstParam;
	std::string m_serviceInstID;
	std::string m_serviceInstName;
	std::string m_alarmLevelID;
	std::string m_rtState;
	std::string m_rtMasterState;
	std::string m_rtRunState;
};

#endif // ZGSPEVENTPARSESERVICEINST_H

#ifndef ZGSPEVENTPARSESERVER_H
#define ZGSPEVENTPARSESERVER_H

#include "ZGSPEventParseBase.h"

class ZGSPEventParseServer : public ZGSPEventParseBase
{
    Q_OBJECT
public:
    explicit ZGSPEventParseServer(QObject *parent = nullptr);
    bool initParam() override;
protected:
    bool parseRecord(const ZG6000::MapField& record) override;
    bool checkDataValid(const ZG6000::MapField& record) override;
    std::string getEventTypeID() override;
    std::string getAlarmLevelID() override;
    std::string getEventInfo() override;
    std::string getEventInfoL2() override;
    std::string getAddition() override;
    std::string getIsPublishEvent() override;

private:
    bool initServerParam();

private:
    std::unordered_map<std::string, HashParam> m_mapServerParam;
    std::string m_serverID;
    std::string m_serverName;
    std::string m_serverNameL2;
    std::string m_alarmLevelID;
    std::string m_netAState;
    std::string m_netBState;
    std::string m_masterState;
};

#endif // ZGSPEVENTPARSESERVER_H

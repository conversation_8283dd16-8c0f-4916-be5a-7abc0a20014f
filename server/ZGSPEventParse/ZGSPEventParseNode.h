#ifndef ZGSPEVENTPARSENODE_H
#define ZGSPEVENTPARSENODE_H

#include "ZGSPEventParseBase.h"

class ZGSPEventParseNode : public ZGSPEventParseBase
{
    Q_OBJECT
public:
    explicit ZGSPEventParseNode(QObject *parent = nullptr);
    bool initParam() override;
protected:
    bool parseRecord(const ZG6000::MapField& record) override;
    bool checkDataValid(const ZG6000::MapField& record) override;
    std::string getEventTypeID() override;
    std::string getAlarmLevelID() override;
    std::string getEventInfo() override;
    std::string getEventInfoL2() override;
    std::string getAddition() override;
    std::string getIsPublishEvent() override;

private:
    bool initNodeParam();

private:
    std::unordered_map<std::string, HashParam> m_mapNodeParam;
    std::string m_nodeID;
    std::string m_nodeName;
    std::string m_nodeNameL2;
    std::string m_alarmLevelID;
    std::string m_netAState;
    std::string m_netBState;
    std::string m_netState;
    std::string m_masterState;
};

#endif // ZGSPEVENTPARSENODE_H

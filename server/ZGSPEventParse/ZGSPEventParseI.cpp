
#include <ZGSPEventParseI.h>
#include "ZGSPEventParseMng.h"

ZG6000::ZGSPEventParseI::ZGSPEventParseI()
{
    ZGSPEventParseMng::instance()->init();
}

bool
ZG6000::ZGSPEventParseI::checkState(const Ice::Current&)
{
    return ZGSPEventParseMng::instance()->checkState();
}

void
ZG6000::ZGSPEventParseI::dispatchData(::std::string tableName,
                                      ::std::string oper,
                                      ::std::string reason,
                                      ::std::string time,
                                      ListRecord listRecord,
                                      const Ice::Current&)
{
    ZGSPEventParseMng::instance()->dispatchData(tableName, oper, reason, time, listRecord);
}

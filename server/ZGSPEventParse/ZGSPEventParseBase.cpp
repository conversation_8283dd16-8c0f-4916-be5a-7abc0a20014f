#include "ZGSPEventParseBase.h"
#include "ZGHeartMng.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "ZGJson.h"
#include "ZGProxyCommon.h"

std::unordered_map<std::string, HashParam> ZGSPEventParseBase::m_mapAlarmLevel{};
std::unordered_map<std::string, HashParam> ZGSPEventParseBase::m_mapEventType{};
std::unordered_map<std::string, HashParam> ZGSPEventParseBase::m_mapDataCategory{};

ZGSPEventParseBase::ZGSPEventParseBase(QObject *parent) : QObject(parent)
{

}

void ZGSPEventParseBase::dispatchEvent(const std::string& time,
	const ::ZG6000::ListRecord& listRecord)
{
    std::unique_lock locker(m_mutex);
    generateEvents(time, listRecord);
    processEvents();
}

void ZGSPEventParseBase::dispatchEvent(const ZG6000::ListStringMap& listRecord)
{
    std::unique_lock locker(m_mutex);
	generateEvents(listRecord);
    processEvents();
}

bool ZGSPEventParseBase::initFixedParam()
{
	try
	{
        std::string sql = "SELECT id, name, nameL2 from sp_dict_event_type";
        if (!initParamToMap(sql, m_mapEventType))
        {
            ZGLOG_ERROR("init event type error.");
            return false;
        }
        sql = "SELECT id, name, nameL2, colorName from mp_dict_data_category";
        if (!initParamToMap(sql, m_mapDataCategory))
        {
            ZGLOG_ERROR("init data category error.");
            return false;
        }
        sql = "SELECT id, name, nameL2, isPlayFile, isPlayTTS, playCount from sp_dict_alarm_level";
        if (!initParamToMap(sql, m_mapAlarmLevel))
        {
            ZGLOG_ERROR("init alarm level error.");
            return false;
        }
        
        return true;
	}
	catch (const std::exception& e)
	{
        ZGLOG_ERROR(e.what());
        return false;
	}
}

bool ZGSPEventParseBase::initialize()
{
    if (!initFixedParam()) 
        return false;
    return true;
}

std::string ZGSPEventParseBase::getNameByID(const std::unordered_map<std::string, HashParam>& mapParam,
    const std::string& id)
{
    if (id.empty())
        return "";
    try
    {
        const auto & param = ZGUtils::get(mapParam, id);
        return ZGUtils::get(param, "name");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

std::string ZGSPEventParseBase::getNameL2ByID(const std::unordered_map<std::string, HashParam> &mapParam, const std::string &id)
{
    if (id.empty())
        return "";
    try
    {
        const auto & param = ZGUtils::get(mapParam, id);
        return ZGUtils::get(param, "nameL2");
    }
    catch (const std::exception &e)
    {
        ZGLOG_ERROR(e.what());
        return "";
    }
}

inline void convertMapToUnorderedMap(ZG6000::StringMap& mapParam, HashParam& unorderedMapParam)
{
    for (auto& param : mapParam)
    {
        unorderedMapParam.insert(param);
    }
}

bool ZGSPEventParseBase::initParamToMap(const std::string& sql, std::unordered_map<std::string, HashParam>& mapParam)
{
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql, listRecord))
        return false;
    for (auto& record : listRecord)
    {
        try
        {
            const auto& id = ZGUtils::get(record, "id");
            HashParam param;
            convertMapToUnorderedMap(record, param);
            mapParam.insert(std::make_pair(id, param));
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
    return true;
}

void ZGSPEventParseBase::generateEvents(const std::string& time, const ::ZG6000::ListRecord& listRecord)
{
    m_eventTime = time;
    for (const auto& record : listRecord)
    {
        if (!checkDataValid(record))
            continue;
        if (!parseRecord(record))
            continue;
        generateEvent();
        saveEvent();
    }
    afterGenerateEvents();
}

void ZGSPEventParseBase::generateEvents(const ZG6000::ListStringMap& listRecords)
{
    for (const auto& record : listRecords)
    {
        if (!parseRecord(record))
            continue;
        if (!checkDataValid(record))
            continue;
        generateEvent();
        saveEvent();
    }
    afterGenerateEvents();
}

std::string ZGSPEventParseBase::getEventTime()
{
    return m_eventTime;
}

void ZGSPEventParseBase::generateEvent()
{
    m_currentEvent.insert(std::make_pair("eventTime", getEventTime()));
    const auto & eventTypeId = getEventTypeID();
    m_currentEvent.insert(std::make_pair("eventTypeID", eventTypeId));
    m_currentEvent.insert(std::make_pair("eventTypeName", getNameByID(m_mapEventType, eventTypeId)));
    m_currentEvent.insert(std::make_pair("eventTypeNameL2", getNameL2ByID(m_mapEventType, eventTypeId)));
    const auto & alarmLevelId = getAlarmLevelID();
    m_currentEvent.insert(std::make_pair("alarmLevelID", alarmLevelId));
    m_currentEvent.insert(std::make_pair("alarmLevelName", getNameByID(m_mapAlarmLevel, alarmLevelId)));
    m_currentEvent.insert(std::make_pair("alarmLevelNameL2", getNameL2ByID(m_mapAlarmLevel, alarmLevelId)));
    m_currentEvent.insert(std::make_pair("eventInfo", getEventInfo()));
    m_currentEvent.insert(std::make_pair("eventInfoL2", getEventInfoL2()));
    m_currentEvent.insert(std::make_pair("addition", getAddition()));
    m_isPublishEvent = getIsPublishEvent();
}

void ZGSPEventParseBase::debugEvent()
{
    for (const auto& pair : m_currentEvent)
    {
        qDebug() << pair.first.c_str() << ": " << pair.second.c_str();
    }
}

void ZGSPEventParseBase::processEvents()
{
    if (m_listEvent.empty())
        return;
    auto eventProcessPrx = ZGProxyMng::instance()->getProxySPEventProcess();
    if (eventProcessPrx == nullptr)
    {
        ZGLOG_ERROR("getProxySPEventProcess error.");
        return;
    }
    auto onewayEventProcessPrx = eventProcessPrx->ice_oneway();
    if (onewayEventProcessPrx == nullptr)
    {
        ZGLOG_ERROR("get one way eventProcessPrx error.");
        return;
    }
    try
    {
        onewayEventProcessPrx->processZGSPEvents(m_listEvent, m_listIsPublishEvent);
        m_listEvent.clear();
        m_listIsPublishEvent.clear();
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGSPEventParseBase::saveEvent()
{
    m_listEvent.push_back(m_currentEvent);
    m_listIsPublishEvent.push_back((m_isPublishEvent));
    m_currentEvent.clear();
}

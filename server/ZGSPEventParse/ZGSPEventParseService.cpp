#include "ZGSPEventParseService.h"
#include "ZGJson.h"
#include "ZGUtils.h"

ZGSPEventParseService::ZGSPEventParseService(QObject* parent) : ZGSPEventParseBase(parent)
{

}

std::string ZGSPEventParseService::getAddition()
{
	ZG6000::StringMap mapAddition;
	mapAddition.insert(std::make_pair("serviceID", m_serviceID));
	return ZGJson::convertToJson(mapAddition);
}

std::string ZGSPEventParseService::getAlarmLevelID()
{
	return m_alarmLevelID;
}

std::string ZGSPEventParseService::getEventInfo()
{
	std::string eventInfo = m_serviceName;
	if (!m_rtState.empty())
	{
		eventInfo += " ";
		eventInfo += u8"服务状态: ";
        if (m_rtState == "2")
			eventInfo += u8"正常";
		else
			eventInfo += u8"中断";
	}
	return eventInfo;
}

std::string ZGSPEventParseService::getEventInfoL2()
{
    std::string eventInfo = m_serviceName;
    if (!m_rtState.empty())
    {
        eventInfo += " ";
        eventInfo += u8"服务状态: ";
        if (m_rtState == "2")
            eventInfo += u8"正常";
        else
            eventInfo += u8"中断";
    }
    m_rtState.clear();
    return eventInfo;
}

std::string ZGSPEventParseService::getEventTypeID()
{
	return "ZG_ET_SERVICE";
}

std::string ZGSPEventParseService::getIsPublishEvent()
{
	return "0";
}

bool ZGSPEventParseService::checkDataValid(const ZG6000::MapField& record)
{
	if (record.find("rtState") == record.end())
		return false;
	return true;
}

bool ZGSPEventParseService::parseRecord(const ZG6000::MapField& record)
{
	try
	{
		const auto& rtState = record.find("rtState");
		if (rtState != record.end())
			m_rtState = rtState->second.newValue;
        m_serviceID = ZGUtils::get(record, "id").newValue;
        const auto& service = ZGUtils::get(m_mapServiceParam, m_serviceID);
        m_serviceName = ZGUtils::get(service, "name");
        m_alarmLevelID = ZGUtils::get(service, "alarmLevelID");
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

bool ZGSPEventParseService::initParam()
{
	return initServiceParam();
}

bool ZGSPEventParseService::initServiceParam()
{
	try
	{
        std::string sql = "SELECT id, name, nameL2, alarmLevelID from sp_param_node_service";
		if (!initParamToMap(sql, m_mapServiceParam))
		{
			ZGLOG_ERROR("init service error.");
			return false;
		}
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

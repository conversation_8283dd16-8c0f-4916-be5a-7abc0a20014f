#ifndef ZGSPEVENTPARSEMNG_H
#define ZGSPEVENTPARSEMNG_H

#include <QThread>
#include <QTimer>
#include <QDateTime>
#include <ZGServerCommon.h>

class ZGSPEventParseBase;
class ZGSPEventParseMng : public QThread
{
	Q_OBJECT
public:
    static ZGSPEventParseMng* instance();
    void init();
    bool checkState();
    void dispatchData(const std::string& tableName,
                      const std::string& oper,
                      const std::string& reason,
                      const std::string& time,
                      const ZG6000::ListRecord& listRecord);
    [[nodiscard]] std::string firstLanguage() const
    {
        return m_firstLanguage;
    }

    [[nodiscard]] std::string secondLanguage() const
    {
        return m_secondLanguage;
    }

protected:
    void run() override;

private:
    explicit ZGSPEventParseMng(QObject* parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    void initEventParseObject();
    bool initServerInstInfo();
    bool initParseParam();
    bool initLanguage();

signals:
    void initFinished();

private slots:
    void onInitFinished();
    void onCheckStatus();

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    bool m_masterInst{false};
    int m_initInterval{10};
    int m_checkInterval{5};
    QTimer m_initTimer;
    QTimer m_checkTimer;
    int m_currentStep{0};
    std::unordered_map<std::string, ZGSPEventParseBase*> m_mapEventParse;
    QDateTime m_dateTimeStart;
    std::string m_firstLanguage;
    std::string m_secondLanguage;
};

#endif // ZGSPEVENTPARSEMNG_H

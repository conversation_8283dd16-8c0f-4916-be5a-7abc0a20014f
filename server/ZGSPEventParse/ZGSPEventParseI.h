#ifndef __ZGSPEventParseI_h__
#define __ZGSPEventParseI_h__

#include <ZGSPEventParse.h>

namespace ZG6000
{

class ZGSPEventParseI : public virtual ZGSPEventParse
{
public:
    ZGSPEventParseI();

    bool checkState(const Ice::Current&) override;

    void dispatchData(::std::string,
                              ::std::string,
                              ::std::string,
                              ::std::string,
                              ListRecord,
                              const Ice::Current&) override;
};

}

#endif

#include "ZGSPEventParseMng.h"

#include <ZGJson.h>

#include "ZGHeartMng.h"
#include "ZGRuntime.h"
#include "ZGSPEventParseBase.h"
#include "ZGSPEventParseNode.h"
#include "ZGSPEventParseServer.h"
#include "ZGSPEventParseService.h"
#include "ZGSPEventParseServiceInst.h"
#include "ZGUtils.h"

static ZGSPEventParseMng* g_pEventParseMng = nullptr;

ZGSPEventParseMng* ZGSPEventParseMng::instance()
{
    if (g_pEventParseMng == nullptr)
        g_pEventParseMng = new ZGSPEventParseMng;
    return g_pEventParseMng;
}

void ZGSPEventParseMng::init()
{
    initEvents();
    initServerInstConfig();
    initEventParseObject();
    start();
    ZGLOG_INFO("ZGSPEventParse init start...");
}

bool ZGSPEventParseMng::checkState()
{
    return m_initialized;
}

void ZGSPEventParseMng::dispatchData(const std::string& tableName, const std::string& oper, const std::string& reason,
                                     const std::string& time, const ZG6000::ListRecord& listRecord)
{
    if (!m_initialized)
        return;
    // QDateTime dt = QDateTime::currentDateTime();
    // if (m_dateTimeStart.secsTo(dt) < 60)
    //     return;
    if (oper != "update")
        return;
    if (reason != "change")
        return;
    try
    {
        m_mapEventParse.at(tableName)->dispatchEvent(time, listRecord);
    }
    catch (const std::exception& e)
    {
        std::string errMsg = tableName + ": " + e.what();
        ZGLOG_ERROR(errMsg.c_str());
    }
}

void ZGSPEventParseMng::run()
{
	while (!initServerInstInfo())
	{
        ZGLOG_ERROR("initServerInstInfo error");
        msleep(m_initInterval * 1000);
	}
	while (!ZGSPEventParseBase::initialize())
	{
        ZGLOG_ERROR("ZGSPEventParseBase::initialize error.");
        msleep(m_initInterval * 1000);
	}
	while (!initParseParam())
	{
        ZGLOG_ERROR("initParseParam error.");
        msleep(m_initInterval * 1000);
    }
    while (!initLanguage())
    {
        ZGLOG_ERROR("initLanguage error.");
        msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    emit initFinished();
    ZGLOG_INFO("ZGSPEventParse init finished.");
}

ZGSPEventParseMng::ZGSPEventParseMng(QObject* parent) : QThread(parent)
{
}

void ZGSPEventParseMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPEventParseMng::onCheckStatus);
    connect(this, &ZGSPEventParseMng::initFinished, this, &ZGSPEventParseMng::onInitFinished);
}

void ZGSPEventParseMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGSPEventParseMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGSPEventParseMng::initParseParam()
{
    for (const auto& pairEventParse : m_mapEventParse)
    {
        if (!pairEventParse.second->initParam())
        {
            ZGLOG_ERROR(QString("%1 initParam error.").arg(pairEventParse.first.c_str()));
            return false;
        }
    }
    return true;
}

bool ZGSPEventParseMng::initLanguage()
{
    QString sql = QString("SELECT firstLanguageID, secondLanguageID FROM sp_param_system");
    ZG6000::StringMap mapLanguage;
    if (!ZGProxyCommon::execQuerySqlRow(sql.toStdString(), mapLanguage))
    {
        ZGLOG_ERROR("获取系统语言参数失败");
        return false;
    }
    m_firstLanguage = mapLanguage["firstLanguageID"];
    if (m_firstLanguage.empty())
        m_firstLanguage = "ZG_DL_CN";
    m_secondLanguage = mapLanguage["secondLanguageID"];
    if (!ZGUtils::initLanguage(m_serverName))
    {
        ZGLOG_ERROR(QStringLiteral("初始化语言配置失败"));
        return false;
    }
    ZGLOG_INFO(QString("first language: %1, second language: %2").arg(m_firstLanguage.c_str()).arg(m_secondLanguage.c_str()));
    return true;
}

void ZGSPEventParseMng::onInitFinished()
{
    m_dateTimeStart = QDateTime::currentDateTime();
    m_checkTimer.start(m_checkInterval * 1000);
}

void ZGSPEventParseMng::initEventParseObject()
{
    m_mapEventParse.insert(std::make_pair("sp_param_node", new ZGSPEventParseNode));
    m_mapEventParse.insert(std::make_pair("sp_param_node_server", new ZGSPEventParseServer));
    m_mapEventParse.insert(std::make_pair("sp_param_node_service", new ZGSPEventParseService));
    m_mapEventParse.insert(std::make_pair("sp_param_node_service_instance", new ZGSPEventParseServiceInst));
}

void ZGSPEventParseMng::onCheckStatus()
{
}

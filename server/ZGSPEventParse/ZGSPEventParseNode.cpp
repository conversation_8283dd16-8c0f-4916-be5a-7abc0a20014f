#include "ZGSPEventParseMng.h"
#include "ZGSPEventParseNode.h"
#include "ZGUtils.h"
#include "ZGJson.h"

ZGSPEventParseNode::ZGSPEventParseNode(QObject* parent) : ZGSPEventParseBase(parent)
{

}

bool ZGSPEventParseNode::initParam()
{
	return initNodeParam();
}

bool ZGSPEventParseNode::parseRecord(const ZG6000::MapField& record)
{
	try
	{
		const auto& rtANetState = record.find("rtANetState");
		if (rtANetState != record.end())
			m_netAState = rtANetState->second.newValue;
		const auto& rtBNetState = record.find("rtBNetState");
		if (rtBNetState != record.end())
			m_netBState = rtBNetState->second.newValue;
		const auto& rtNetwork = record.find("rtNetwork");
		if (rtNetwork != record.end())
			m_netState = rtNetwork->second.newValue;
        const auto& rtMasterState = record.find("rtMasterState");
        if (rtMasterState != record.end())
            m_masterState = rtMasterState->second.newValue;
        m_nodeID = ZGUtils::get(record, "id").newValue;
        const auto& node = ZGUtils::get(m_mapNodeParam, m_nodeID);
        m_nodeName = ZGUtils::get(node, "name");
        m_nodeNameL2 = ZGUtils::get(node, "nameL2");
        m_alarmLevelID = ZGUtils::get(node, "alarmLevelID");
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

bool ZGSPEventParseNode::checkDataValid(const ZG6000::MapField& record)
{
	if (record.find("rtANetState") == record.end() && record.find("rtBNetState") == record.end() &&
        record.find("rtNetwork") == record.end() && record.find("rtMasterState") == record.end())
		return false;
	return true;
}

std::string ZGSPEventParseNode::getEventTypeID()
{
	return "ZG_ET_SYSTEM";
}

std::string ZGSPEventParseNode::getAlarmLevelID()
{
	return m_alarmLevelID;
}

std::string ZGSPEventParseNode::getEventInfo()
{
	std::string eventInfo = m_nodeName;
	if (!m_netAState.empty())
	{
		eventInfo += " ";
        eventInfo += "A" + ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "netState") + ": ";
        if (m_netAState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "normal");
		else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "break");
	}
	if (!m_netBState.empty())
	{
		eventInfo += " ";
        eventInfo += "B" + ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "netState") + ": ";
        if (m_netBState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "normal");
		else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "break");
	}
	if (!m_netState.empty())
	{
		eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "networkState") + ": ";
        if (m_netState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "normal");
		else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "break");
	}
    if (!m_masterState.empty())
    {
        eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "masterState") + ": ";
        if (m_masterState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "master");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "slave");
    }
    ZGLOG_TRACE(eventInfo.c_str());
	return eventInfo;
}

std::string ZGSPEventParseNode::getEventInfoL2()
{
    std::string eventInfo = m_nodeNameL2;
    if (!m_netAState.empty())
    {
        eventInfo += " ";
        eventInfo += "A" + ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "netState") + ": ";
        if (m_netAState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "break");
    }
    if (!m_netBState.empty())
    {
        eventInfo += " ";
        eventInfo += "B" + ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "netState") + ": ";
        if (m_netBState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "break");
    }
    if (!m_netState.empty())
    {
        eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "networkState") + ": ";
        if (m_netState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "break");
    }
    if (!m_masterState.empty())
    {
        eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "masterState") + ": ";
        if (m_masterState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "master");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "slave");
    }
    m_netAState.clear();
    m_netBState.clear();
    m_netState.clear();
    m_masterState.clear();
    ZGLOG_TRACE(eventInfo.c_str());
    return eventInfo;
}

std::string ZGSPEventParseNode::getAddition()
{
	ZG6000::StringMap mapAddition;
	mapAddition.insert(std::make_pair("nodeID", m_nodeID));
	return ZGJson::convertToJson(mapAddition);
}

std::string ZGSPEventParseNode::getIsPublishEvent()
{
	return "1";
}

bool ZGSPEventParseNode::initNodeParam()
{
	try
	{
        std::string sql = "SELECT id, name, nameL2, alarmLevelID from sp_param_node";
		if (!initParamToMap(sql, m_mapNodeParam))
		{
			ZGLOG_ERROR("init node error.");
			return false;
		}
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

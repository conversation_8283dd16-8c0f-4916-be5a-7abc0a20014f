#include "ZGSPEventParseMng.h"
#include "ZGSPEventParseServer.h"
#include "ZGUtils.h"
#include "ZGJson.h"

ZGSPEventParseServer::ZGSPEventParseServer(QObject* parent) : ZGSPEventParseBase(parent)
{
}

bool ZGSPEventParseServer::initParam()
{
	return initServerParam();
}

bool ZGSPEventParseServer::parseRecord(const ZG6000::MapField& record)
{
	try
	{
		const auto& rtANetState = record.find("rtANetState");
		if (rtANetState != record.end())
			m_netAState = rtANetState->second.newValue;
		const auto& rtBNetState = record.find("rtBNetState");
		if (rtBNetState != record.end())
			m_netBState = rtBNetState->second.newValue;
		const auto& rtNetwork = record.find("rtMasterState");
		if (rtNetwork != record.end())
			m_masterState = rtNetwork->second.newValue;
        m_serverID = ZGUtils::get(record, "id").newValue;
        const auto& server = ZGUtils::get(m_mapServerParam, m_serverID);
        m_serverName = ZGUtils::get(server, "name");
        m_serverNameL2 = ZGUtils::get(server, "nameL2");
        m_alarmLevelID = ZGUtils::get(server, "alarmLevelID");
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

bool ZGSPEventParseServer::checkDataValid(const ZG6000::MapField& record)
{
	if (record.find("rtANetState") == record.end() && record.find("rtBNetState") == record.end() &&
		record.find("rtMasterState") == record.end())
		return false;
	return true;
}

std::string ZGSPEventParseServer::getEventTypeID()
{
	return "ZG_ET_SERVER";
}

std::string ZGSPEventParseServer::getAlarmLevelID()
{
	return m_alarmLevelID;
}

std::string ZGSPEventParseServer::getEventInfo()
{
	std::string eventInfo = m_serverName;
	if (!m_netAState.empty())
	{
		eventInfo += " ";
        eventInfo += "A" + ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "netState") + ": ";
        if (m_netAState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "normal");
		else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "break");
	}
	if (!m_netBState.empty())
	{
		eventInfo += " ";
        eventInfo += "B" + ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "netState") + ": ";
        if (m_netAState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "normal");
		else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "break");
	}
	if (!m_masterState.empty())
	{
		eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "masterState") + ": ";
        if (m_masterState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "master");
		else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "slave");
	}
	return eventInfo;
}

std::string ZGSPEventParseServer::getEventInfoL2()
{
    std::string eventInfo = m_serverNameL2;
    if (!m_netAState.empty())
    {
        eventInfo += " ";
        eventInfo += "A" + ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "netState") + ": ";
        if (m_netAState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "break");
    }
    if (!m_netBState.empty())
    {
        eventInfo += " ";
        eventInfo += "B" + ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "netState") + ": ";
        if (m_netBState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "break");
    }
    if (!m_masterState.empty())
    {
        eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "masterState") + ": ";
        if (m_masterState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "master");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "slave");
    }
    m_netAState.clear();
    m_netBState.clear();
    m_masterState.clear();
    return eventInfo;
}

std::string ZGSPEventParseServer::getAddition()
{
	ZG6000::StringMap mapAddition;
	mapAddition.insert(std::make_pair("serverID", m_serverID));
	return ZGJson::convertToJson(mapAddition);
}

std::string ZGSPEventParseServer::getIsPublishEvent()
{
	return "0";
}

bool ZGSPEventParseServer::initServerParam()
{
	try
	{
        std::string sql = "SELECT id, name, nameL2, alarmLevelID from sp_param_node_server";
		if (!initParamToMap(sql, m_mapServerParam))
		{
			ZGLOG_ERROR("init server error.");
			return false;
		}
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

#include "ZGSPEventParseMng.h"
#include "ZGSPEventParseServiceInst.h"
#include "ZGUtils.h"
#include "ZGJson.h"

ZGSPEventParseServiceInst::ZGSPEventParseServiceInst(QObject* parent) : ZGSPEventParseBase(parent)
{
}

bool ZGSPEventParseServiceInst::checkDataValid(const ZG6000::MapField& record)
{
	if (record.find("rtState") == record.end() && record.find("rtMasterState") == record.end()
		&& record.find("rtRunState") == record.end())
		return false;
	return true;
}

std::string ZGSPEventParseServiceInst::getAddition()
{
	ZG6000::StringMap mapAddition;
	mapAddition.insert(std::make_pair("serviceInstID", m_serviceInstID));
	return ZGJson::convertTo<PERSON>son(mapAddition);
}

std::string ZGSPEventParseServiceInst::getAlarmLevelID()
{
	return m_alarmLevelID;
}

std::string ZGSPEventParseServiceInst::getEventInfo()
{
	std::string eventInfo = m_serviceInstName;
	if (!m_rtState.empty())
	{
		eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "instState") + ": ";
        if (m_rtState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "normal");
		else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "abnormal");
	}
	if (!m_rtMasterState.empty())
	{
		eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "masterState") + ": ";
        if (m_rtMasterState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "masterInst");
		else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "slaveInst");
	}
	if (!m_rtRunState.empty())
	{
		eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "runState") + ": ";
        if (m_rtRunState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "run");
		else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->firstLanguage(), "stop");
	}
	return eventInfo;
}

std::string ZGSPEventParseServiceInst::getEventInfoL2()
{
    std::string eventInfo = m_serviceInstName;
    if (!m_rtState.empty())
    {
        eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "instState") + ": ";
        if (m_rtState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "normal");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "abnormal");
    }
    if (!m_rtMasterState.empty())
    {
        eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "masterState") + ": ";
        if (m_rtMasterState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "masterInst");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "slaveInst");
    }
    if (!m_rtRunState.empty())
    {
        eventInfo += " ";
        eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "runState") + ": ";
        if (m_rtRunState == "2")
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "run");
        else
            eventInfo += ZGUtils::languageString(ZGSPEventParseMng::instance()->secondLanguage(), "stop");
    }
    m_rtState.clear();
    m_rtMasterState.clear();
    m_rtRunState.clear();
    return eventInfo;
}

std::string ZGSPEventParseServiceInst::getEventTypeID()
{
	return "ZG_ET_SERVICE_INST";
}

std::string ZGSPEventParseServiceInst::getIsPublishEvent()
{
	return "0";
}

bool ZGSPEventParseServiceInst::initServiceInstParam()
{
	try
	{
		std::string sql = "SELECT id, serviceID, name from sp_param_node_service_instance";
		if (!initParamToMap(sql, m_mapServiceInstParam))
		{
			ZGLOG_ERROR("init service instance error.");
			return false;
		}
		sql = "SELECT id, alarmLevelID from sp_param_node_service";
		std::unordered_map<std::string, HashParam> mapServiceParam;
		if (!initParamToMap(sql, mapServiceParam))
		{
			ZGLOG_ERROR("init service error.");
			return false;
		}
		for (auto& serviceInstParam : m_mapServiceInstParam)
		{
            const auto& serviceID = ZGUtils::get(serviceInstParam.second, "serviceID");
            if (mapServiceParam.find(serviceID) == mapServiceParam.end())
            {
                ZGLOG_WARN(QString("Can't find service %1 in service table.").arg(serviceID.c_str()));
                continue;
            }
            const auto& service = ZGUtils::get(mapServiceParam, serviceID);
            serviceInstParam.second.insert(std::make_pair("alarmLevelID", ZGUtils::get(service, "alarmLevelID")));
		}
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

bool ZGSPEventParseServiceInst::initParam()
{
	return initServiceInstParam();
}

bool ZGSPEventParseServiceInst::parseRecord(const ZG6000::MapField& record)
{
	try
	{
		const auto& rtState = record.find("rtState");
		if (rtState != record.end())
			m_rtState = rtState->second.newValue;
		const auto& rtMasterState = record.find("rtMasterState");
		if (rtMasterState != record.end())
			m_rtMasterState = rtMasterState->second.newValue;
		const auto& rtRunState = record.find("rtRunState");
		if (rtRunState != record.end())
			m_rtRunState = rtRunState->second.newValue;
        m_serviceInstID = ZGUtils::get(record, "id").newValue;
        const auto& serviceInst = ZGUtils::get(m_mapServiceInstParam, m_serviceInstID);
        m_serviceInstName = ZGUtils::get(serviceInst, "name");
        m_alarmLevelID = ZGUtils::get(serviceInst, "alarmLevelID");
		return true;
	}
	catch (const std::exception& e)
	{
		ZGLOG_ERROR(e.what());
		return false;
	}
}

#ifndef ZGSPSECUREMANAGERI_H
#define ZGSPSECUREMANAGERI_H

#include "ZGSPSecureManager.h"

namespace ZG6000
{
class ZGSPSecureManagerI : public ZGSPSecureManager
{
public:
    ZGSPSecureManagerI();
    bool checkState(const Ice::Current& current) override;
    bool aesEncrypt(std::string in, std::string& output, ErrorInfo& e, const Ice::Current& current) override;
    bool aesDecrypt(std::string in, std::string& output, ErrorInfo& e, const Ice::Current& current) override;
    bool rsaEncrypt(std::string in, std::string& output, ErrorInfo& e, const Ice::Current& current) override;
    bool rsaDecrypt(std::string in, std::string& output, ErrorInfo& e, const Ice::Current& current) override;
    bool rsaSign(std::string in, std::string& sign, ErrorInfo& e, const Ice::Current& current) override;
    bool rsaVerify(std::string in, std::string sign, bool& result, ErrorInfo& e, const Ice::Current& current) override;
    bool rsaSeal(std::string in, std::string& output, std::string& envelope, ErrorInfo& e, const Ice::Current& current) override;
    bool rsaOpen(std::string in, std::string envelope, std::string& output, ErrorInfo& e, const Ice::Current& current) override;
};
}

#endif // ZGSPSECUREMANAGERI_H

#include "ZGSPSecureManagerMng.h"
#include "ZGDebugMng.h"
#include "ZGHeartMng.h"
#include "ZGPubFun.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "zgerror/ZGSPSecureManagerError.h"

#include <QCryptographicHash>


static ZGSPSecureManagerMng* g_pInstance = nullptr;

ZGSPSecureManagerMng* ZGSPSecureManagerMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGSPSecureManagerMng;
    return g_pInstance;
}

void ZGSPSecureManagerMng::init()
{
    initEvents();
    initServerInstConfig();
    initAESKey();
    initAESMode();
    start();
    ZGLOG_INFO("ZGSPSecureManager init start...");
}

bool ZGSPSecureManagerMng::checkState()
{
    return m_initialized;
}

bool ZGSPSecureManagerMng::aesEncrypt(const std::string& in, std::string& output, ZG6000::ErrorInfo& e)
{
    QString input = in.c_str();
    QByteArray out;
    std::string errMsg;
    if (!ZGSecure::aesEncrypt(input.toUtf8(), out, m_aesKey, errMsg, m_aesMode))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPSecureManager::ZG_ERR_EXEC);
        e.errDetail = errMsg;
        return false;
    }
    output = out.toBase64().data();
    return true;
}

bool ZGSPSecureManagerMng::aesDecrypt(const std::string& in, std::string& output, ZG6000::ErrorInfo& e)
{
    QByteArray input = QByteArray::fromBase64(in.c_str());
    QByteArray ba;
    std::string errMsg;
    if (!ZGSecure::aesDecrypt(input, ba, m_aesKey, errMsg, m_aesMode))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPSecureManager::ZG_ERR_EXEC);
        e.errDetail = errMsg;
        return false;
    }
    QString out = QString::fromUtf8(ba);
    output = out.toStdString();
    return true;
}

bool ZGSPSecureManagerMng::rsaEncrypt(const std::string& in, std::string& output, ZG6000::ErrorInfo& e)
{
    QString input = in.c_str();
    QByteArray out;
    std::string errMsg;
    if (!ZGSecure::rsaEncrypt(input.toUtf8(), out, m_encryptPubKey, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPSecureManager::ZG_ERR_EXEC);
        e.errDetail = errMsg;
        return false;
    }
    output = out.toBase64().data();
    return true;
}

bool ZGSPSecureManagerMng::rsaDecrypt(const std::string& in, std::string& output, ZG6000::ErrorInfo& e)
{
    QByteArray input = QByteArray::fromBase64(in.c_str());
    QByteArray ba;
    std::string errMsg;
    if (!ZGSecure::rsaDecrypt(input, ba, m_encryptPriKey, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPSecureManager::ZG_ERR_EXEC);
        e.errDetail = errMsg;
        return false;
    }
    QString out = QString::fromUtf8(ba);
    output = out.toStdString();
    return true;
}

bool ZGSPSecureManagerMng::rsaSign(const std::string& in, std::string& sign, ZG6000::ErrorInfo& e)
{
    QString input = in.c_str();
    QCryptographicHash hash(QCryptographicHash::Algorithm::Sha256);
    hash.addData(input.toUtf8());
    QByteArray digest = hash.result();
    std::string errMsg;
    QByteArray ba;
    if (!ZGSecure::rsaSign(digest, ba, m_signPriKey, errMsg))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPSecureManager::ZG_ERR_EXEC);
        e.errDetail = errMsg;
        return false;
    }
    sign = ba.toBase64().data();
    return true;
}

bool ZGSPSecureManagerMng::rsaVerify(const std::string& in, const std::string& sign, bool& result, ZG6000::ErrorInfo& e)
{
    QString input = in.c_str();
    QCryptographicHash hash(QCryptographicHash::Algorithm::Sha256);
    hash.addData(input.toUtf8());
    QByteArray digest = hash.result();
    QByteArray ba = QByteArray::fromBase64(sign.c_str());
    std::string errMsg;
    if (!ZGSecure::rsaVerify(digest, ba, m_signPubKey, errMsg))
    {
        if (!errMsg.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPSecureManager::ZG_ERR_EXEC);
            e.errDetail = errMsg;
            return false;
        }
        result = false;
    }
    else
        result = true;
    return true;
}

bool ZGSPSecureManagerMng::rsaSeal(const std::string& in, std::string& output, std::string& envelope, ZG6000::ErrorInfo& e)
{
    QString input = in.c_str();
    QByteArray ba, env;
    std::string errMsg;
    if (!ZGSecure::rsaSeal(input.toUtf8(), m_encryptPubKey, ba, env, errMsg, m_aesMode))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPSecureManager::ZG_ERR_EXEC);
        e.errDetail = errMsg;
        return false;
    }
    QString out = ba.toBase64();
    output = out.toStdString();
    QString enve = env.toBase64();
    envelope = enve.toStdString();
    return true;
}

bool ZGSPSecureManagerMng::rsaOpen(const std::string& in, const std::string& envelope, std::string& output, ZG6000::ErrorInfo& e)
{
    QByteArray input = QByteArray::fromBase64(in.c_str());
    QByteArray env = QByteArray::fromBase64(envelope.c_str());
    QByteArray ba;
    std::string errMsg;
    if (!ZGSecure::rsaOpen(input, env, m_encryptPriKey, ba, errMsg, m_aesMode))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPSecureManager::ZG_ERR_EXEC);
        e.errDetail = errMsg;
        return false;
    }
    QString out = QString::fromUtf8(ba);
    output = out.toStdString();
    return true;
}

void ZGSPSecureManagerMng::run()
{
	while (!initServerInstInfo())
	{
        ZGLOG_ERROR("initServerInstInfo error.");
        msleep(m_initInterval * 1000);
	}
    while (!initRSAKey())
    {
        ZGLOG_ERROR("initRSAKey error.");
        return;
    }
    m_masterInst = ZGRuntime::instance()->isMaster();
    m_initialized = true;
    emit initFinished();
    ZGLOG_INFO("ZGSPSecureManager init finished.");
}

ZGSPSecureManagerMng::ZGSPSecureManagerMng(QObject *parent) : QThread(parent)
{

}

void ZGSPSecureManagerMng::initAESMode()
{
    QMap<QString, QString> mapSection = ZGRuntime::instance()->getServerConfig("server");
    if (mapSection.contains("aesMode"))
    {
        QString value = mapSection.value("aesMode");
        if (value == "ecb")
            m_aesMode = ZGSecure::EncryptMode::ecb;
        else if (value == "cbc")
            m_aesMode = ZGSecure::EncryptMode::cbc;
        else if (value == "cfb1")
            m_aesMode = ZGSecure::EncryptMode::cfb1;
        else if (value == "cfb8")
            m_aesMode = ZGSecure::EncryptMode::cfb8;
        else if (value == "cfb128")
            m_aesMode = ZGSecure::EncryptMode::cfb128;
        else if (value == "ofb128")
            m_aesMode = ZGSecure::EncryptMode::ofb128;
    }
}

bool ZGSPSecureManagerMng::initRSAKey()
{
    QString cfgDir = ZGPubFun::getCfgDir();
    QString keyFile = cfgDir + "/encrypt_pri.pem";
    std::string errMsg;
    if (!ZGSecure::readRSAPrivateKey(keyFile, m_encryptPriKey, errMsg))
    {
        ZGLOG_ERROR(errMsg.c_str());
        return false;
    }
    keyFile = cfgDir + "/encrypt_pub.pem";
    if (!ZGSecure::readRSAPublicKey(keyFile, m_encryptPubKey, errMsg))
    {
        ZGLOG_ERROR(errMsg.c_str());
        return false;
    }
    keyFile = cfgDir + "/sign_pri.pem";
    if (!ZGSecure::readRSAPrivateKey(keyFile, m_signPriKey, errMsg))
    {
        ZGLOG_ERROR(errMsg.c_str());
        return false;
    }
    keyFile = cfgDir + "/sign_pub.pem";
    if (!ZGSecure::readRSAPublicKey(keyFile, m_signPubKey, errMsg))
    {
        ZGLOG_ERROR(errMsg.c_str());
        return false;
    }
    return true;
}

void ZGSPSecureManagerMng::initAESKey()
{
    m_aesKey = QByteArray::fromHex("28E891AA7345B61C70DF92E428A6510B");
}

void ZGSPSecureManagerMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPSecureManagerMng::onCheckStatus);
    connect(this, &ZGSPSecureManagerMng::initFinished, this, &ZGSPSecureManagerMng::onInitFinished);
}

void ZGSPSecureManagerMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGSPSecureManagerMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

void ZGSPSecureManagerMng::onInitFinished()
{
    m_checkTimer.start(m_checkInterval * 1000);
}

void ZGSPSecureManagerMng::onCheckStatus()
{
    m_masterInst = ZGRuntime::instance()->isMaster();
}

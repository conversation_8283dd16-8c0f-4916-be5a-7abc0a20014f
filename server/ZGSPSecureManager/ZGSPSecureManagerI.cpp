#include "ZGSPSecureManagerI.h"
#include "ZGSPSecureManagerMng.h"

ZG6000::ZGSPSecureManagerI::ZGSPSecureManagerI()
{
    ZGSPSecureManagerMng::instance()->init();
}

bool ZG6000::ZGSPSecureManagerI::checkState(const Ice::Current& )
{
    return ZGSPSecureManagerMng::instance()->checkState();
}

bool ZG6000::ZGSPSecureManagerI::aesEncrypt(std::string in, std::string& output, ErrorInfo& e, const Ice::Current& )
{
    return ZGSPSecureManagerMng::instance()->aesEncrypt(in, output, e);
}

bool ZG6000::ZGSPSecureManagerI::aesDecrypt(std::string in, std::string& output, ErrorInfo& e, const Ice::Current& )
{
    return ZGSPSecureManagerMng::instance()->aesDecrypt(in, output, e);
}

bool ZG6000::ZGSPSecureManagerI::rsaEncrypt(std::string in, std::string& output, ErrorInfo& e, const Ice::Current& )
{
    return ZGSPSecureManagerMng::instance()->rsaEncrypt(in, output, e);
}

bool ZG6000::ZGSPSecureManagerI::rsaDecrypt(std::string in, std::string& output, ErrorInfo& e, const Ice::Current& )
{
    return ZGSPSecureManagerMng::instance()->rsaDecrypt(in, output, e);
}

bool ZG6000::ZGSPSecureManagerI::rsaSign(std::string in, std::string& sign, ErrorInfo& e, const Ice::Current& )
{
    return ZGSPSecureManagerMng::instance()->rsaSign(in, sign, e);
}

bool ZG6000::ZGSPSecureManagerI::rsaVerify(std::string in, std::string sign, bool& result, ErrorInfo& e, const Ice::Current& )
{
    return ZGSPSecureManagerMng::instance()->rsaVerify(in, sign, result, e);
}

bool ZG6000::ZGSPSecureManagerI::rsaSeal(std::string in, std::string& output, std::string& envelope, ErrorInfo& e, const Ice::Current& )
{
    return ZGSPSecureManagerMng::instance()->rsaSeal(in, output, envelope, e);
}

bool ZG6000::ZGSPSecureManagerI::rsaOpen(std::string in, std::string envelope, std::string& output, ErrorInfo& e, const Ice::Current& )
{
    return ZGSPSecureManagerMng::instance()->rsaOpen(in, envelope, output, e);
}

#ifndef ZGSPSECUREMANAGERMNG_H
#define ZGSPSECUREMANAGERMNG_H

#include <QThread>
#include <QTimer>
#include "ZGServerCommon.h"
#include "ZGSecure.h"

class ZGSPSecureManagerMng : public QThread
{
	Q_OBJECT
public:
    static ZGSPSecureManagerMng* instance();
    void init();

public:
    bool checkState();

    /**
     * @brief   AES加密
     *
     * @param           in      原始数据
     * @param [in,out]  output  加密数据
     * @param [in,out]  e       执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool aesEncrypt(const std::string& in, std::string& output, ZG6000::ErrorInfo& e);

    /**
     * @brief   AES解密
     *
     * @param           in      加密数据
     * @param [in,out]  output  原始数据
     * @param [in,out]  e       执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool aesDecrypt(const std::string& in, std::string& output, ZG6000::ErrorInfo& e);

    /**
     * @brief   RSA加密
     *
     * @param           in      原始数据
     * @param [in,out]  output  加密数据
     * @param [in,out]  e       执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool rsaEncrypt(const std::string& in, std::string& output, ZG6000::ErrorInfo& e);

    /**
     * @brief   RSA解密
     *
     * @param           in      加密数据
     * @param [in,out]  output  原始数据
     * @param [in,out]  e       执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool rsaDecrypt(const std::string& in, std::string& output, ZG6000::ErrorInfo& e);

    /**
     * @brief   RSA签名
     *
     * @param           in      消息内容
     * @param [in,out]  sign    签名
     * @param [in,out]  e       执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool rsaSign(const std::string& in, std::string& sign, ZG6000::ErrorInfo& e);

    /**
     * @brief   RSA验签
     *
     * @param           in      消息内容
     * @param           sign    签名
     * @param [in,out]  result  验证结果
     * @param [in,out]  e       执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool rsaVerify(const std::string& in, const std::string& sign, bool& result, ZG6000::ErrorInfo& e);

    /**
     * @brief   RSA电子信封密封
     *
     * @param           in          原始数据
     * @param [in,out]  output      加密数据
     * @param [in,out]  envelope    信封
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool rsaSeal(const std::string& in, std::string& output, std::string& envelope, ZG6000::ErrorInfo& e);

    /**
     * @brief   RSA电子信封拆封
     *
     * @param           in          加密数据
     * @param           envelope    信封
     * @param [in,out]  output      原始数据
     * @param [in,out]  e           执行出错时的错误信息
     *
     * @return  执行成功返回true，失败返回false。
     */
    bool rsaOpen(const std::string& in, const std::string& envelope, std::string& output, ZG6000::ErrorInfo& e);

protected:
    void run() override;

private:
    explicit ZGSPSecureManagerMng(QObject *parent = nullptr);
    void initAESMode();
    bool initRSAKey();
    void initAESKey();
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();

signals:
    void initFinished();

private slots:
    void onInitFinished();
    void onCheckStatus();

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    bool m_masterInst{false};
    int m_initInterval{10};
    int m_checkInterval{5};
    QTimer m_initTimer;
    QTimer m_checkTimer;
    QByteArray m_encryptPubKey;
    QByteArray m_encryptPriKey;
    QByteArray m_signPubKey;
    QByteArray m_signPriKey;
    QByteArray m_aesKey;
    ZGSecure::EncryptMode m_aesMode{ZGSecure::EncryptMode::cbc};
};

#endif // ZGSPSECUREMANAGERMNG_H

#include "ZGMPRegionManagerI.h"
#include "ZGMPRegionManagerMng.h"

namespace ZG6000 {

ZGMPRegionManagerI::ZGMPRegionManagerI()
{
    ZGMPRegionManagerMng::instance()->init();
}

bool ZGMPRegionManagerI::checkState(const Ice::Current &current)
{
    return ZGMPRegionManagerMng::instance()->checkState();
}

bool ZGMPRegionManagerI::getRegionList(StringMap param, ListStringMap& listRegion, ErrorInfo& e, const Ice::Current& current)
{
    return ZGMPRegionManagerMng::instance()->getRegionList(std::move(param), listRegion, e);
}

bool ZGMPRegionManagerI::getRegionAccess(std::string regionID, ListStringMap& ListAccess, ErrorInfo& e, const Ice::Current& current)
{
    return ZGMPRegionManagerMng::instance()->getRegionAccess(std::move(regionID), ListAccess, e);
}

bool ZGMPRegionManagerI::clearRegionPeople(std::string regionID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGMPRegionManagerMng::instance()->clearRegionPeople(std::move(regionID), e);
}

bool ZGMPRegionManagerI::resetWarn(std::string regionID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGMPRegionManagerMng::instance()->resetWarn(std::move(regionID), e);
}

void ZGMPRegionManagerI::setupAlarm(std::string regionID, const Ice::Current& current)
{
    ZGMPRegionManagerMng::instance()->setupAlarm(std::move(regionID));
}

void ZGMPRegionManagerI::closeAlarm(std::string regionID, const Ice::Current& current)
{
    ZGMPRegionManagerMng::instance()->closeAlarm(std::move(regionID));
}

bool ZGMPRegionManagerI::getRegionPeople(std::string regionID, ListStringMap& listPeople, ErrorInfo& e, const Ice::Current& current)
{
    return ZGMPRegionManagerMng::instance()->getRegionPeople(std::move(regionID), listPeople, e);
}

bool ZGMPRegionManagerI::getRegionYv(std::string regionID, ListStringMap& listYv, ErrorInfo& e, const Ice::Current& current)
{
    return ZGMPRegionManagerMng::instance()->getRegionYv(std::move(regionID), listYv, e);
}
} // namespace ZG6000

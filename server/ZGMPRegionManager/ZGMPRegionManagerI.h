#ifndef __ZGMPRegionManagerI_h__
#define __ZGMPRegionManagerI_h__

#include <ZGMPRegionManager.h>

namespace ZG6000
{
    class ZGMPRegionManagerI : public virtual ZGMPRegionManager
    {
    public:
        ZGMPRegionManagerI();
        bool checkState(const Ice::Current&) override;
        bool getRegionList(StringMap param, ListStringMap& listRegion, ErrorInfo& e, const Ice::Current& current) override;
        bool getRegionAccess(std::string regionID, ListStringMap& ListAccess, ErrorInfo& e, const Ice::Current& current) override;
        bool getRegionPeople(::std::string regionID, ListStringMap& listPeople, ErrorInfo& e, const Ice::Current& current) override;
        bool getRegionYv(::std::string regionID, ListStringMap& listYv, ErrorInfo& e, const Ice::Current& current) override;
        bool clearRegionPeople(::std::string regionID, ErrorInfo& e, const Ice::Current& current) override;
        bool resetWarn(std::string regionID, ErrorInfo& e, const Ice::Current& current) override;
        void setupAlarm(std::string regionID, const Ice::Current& current) override;
        void closeAlarm(std::string regionID, const Ice::Current& current) override;
    };
}

#endif

#include "ZGMPRegionManagerMng.h"

#include <filesystem>
#include <QJsonDocument>
#include <QRandomGenerator>
#include <QThread>
#include <zgerror/ZGOPWPManagerError.h>

#include "ZGDebugMng.h"
#include "ZGRuntime.h"
#include "ZGProxyCommon.h"
#include "ZGJson.h"
#include "ZGUtils.h"
#include "zgerror/ZGMPRegionManagerError.h"

namespace ZG6000 {

ZGMPRegionManagerMng::ZGMPRegionManagerMng(QObject *parent)
    : QObject{parent}
{
}

ZGMPRegionManagerMng* ZGMPRegionManagerMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGMPRegionManagerMng;
    return g_pInstance;
}

void ZGMPRegionManagerMng::init()
{
    initEvents();
    initServerInstConfig();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::sleep(m_initInterval);
    }
    QThread::sleep(QRandomGenerator::global()->bounded(5, 10));
    while (!initRegionParam())
    {
        ZGLOG_ERROR("initRegionParam error.");
        QThread::sleep(m_initInterval);
    }
    while (!initTaskParam())
    {
        ZGLOG_ERROR("initTaskParam error.");
        QThread::sleep(m_initInterval);
    }
    while (!initParams())
    {
        ZGLOG_ERROR("initParams error.");
        QThread::sleep(m_initInterval);
    }
    while (!initRegionDefenceState())
    {
        ZGLOG_ERROR("initRegionDefenceState error.");
        QThread::sleep(m_initInterval);
    }
    while (!initRedisClient())
    {
        ZGLOG_ERROR("initRedisClient error.");
        QThread::sleep(m_initInterval);
    }
    while (!initMqttClient())
    {
        ZGLOG_ERROR("initMqttClient error.");
        QThread::sleep(m_initInterval);
    }
    m_initialized = true;
    ZGLOG_INFO("ZGMPRegionManager init finished.");
    m_timer.start(1000);
}

bool ZGMPRegionManagerMng::checkState()
{
    return m_initialized;
}
    
bool ZGMPRegionManagerMng::clearRegionPeople(std::string regionID, ErrorInfo& e)
{
    QString sql = QString("DELETE FROM mp_param_region_user WHERE regionID = '%1';").arg(regionID.c_str());
    if (!ZGProxyCommon::execSql(sql.toStdString()))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGMPRegionManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("清除区域'%1'人员失败").arg(regionID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    m_pMqttClient->sendPublish(QString("mp_param_region/%1/clear").arg(regionID.c_str()), "");
    auto eventProxy = ZGProxyMng::instance()->getProxySPEventProcess();
    if (eventProxy)
    {
        auto onewayPrx = eventProxy->ice_oneway();
        try
        {
            auto pair = m_mapRegionParam.find(regionID);
            if (pair != m_mapRegionParam.end())
            {
                const auto& regionParam = pair->second;
                const auto& appNodeID = ZGUtils::get(regionParam, "appNodeID");
                const auto& regionName = ZGUtils::get(regionParam, "name");
                ZG6000::StringMap event{{"appNodeID", appNodeID}, {"eventTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString()},
                                        {"eventTypeID", "ZG_ET_SYSTEM"}, {"alarmLevelID", "ZG_AL_LEVEL0"},
                                        {"eventInfo", QStringLiteral("清空【%1】人员").arg(regionName.c_str()).toStdString()}};
                onewayPrx->processEvent(event);
            }
        }
        catch (const Ice::Exception& ie)
        {
            ZGLOG_ERROR(ie.what());
        }
        catch (const std::exception& ex)
        {
            ZGLOG_ERROR(ex.what());
        }
    }
    StringMap property{ {"AlarmMotionDetection", "1"}, {"VCAAlarmPeopleNum1", "0"}, {"VCAAlarmPeopleNum2", "0"},
            {"VCAAlarmPeopleNum3", "0"}, {"VCAAlarmPeopleNum4", "0"}};
    auto pair = m_mapRegionVideoDevice.find(regionID);
    if (pair != m_mapRegionVideoDevice.end())
    {
        const auto& listDeviceID = pair->second;
        MapStringMap mapProperty;
        for (const auto& deviceID : listDeviceID)
        {
            mapProperty[deviceID] = property;
        }
        if (!ZGProxyCommon::mupdatePropertyValues(mapProperty, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
    }
    return true;
}

bool ZGMPRegionManagerMng::getRegionPeople(std::string regionID, ListStringMap& listPeople, ErrorInfo& e)
{
    QString sql = QString("SELECT a.userID, b.name FROM mp_param_region_user a LEFT JOIN sp_param_hrm_user b ON a.userID = b.id "
                          "WHERE a.regionID = '%1' ORDER BY a.id").arg(regionID.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listPeople))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGMPRegionManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取区域'%1'人员信息失败").arg(regionID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGMPRegionManagerMng::getRegionYv(std::string regionID, ListStringMap& listYv, ErrorInfo& e)
{
    QString sql = QString("SELECT a.* FROM mp_param_dataset_yv a LEFT JOIN mp_param_model_yv b ON a.dataModelID = b.id "
                          "WHERE a.regionID = '%1' AND b.isEnable = 1 AND (b.isPrivate <> 1 OR b.isPrivate IS NULL) ORDER BY a.id").arg(regionID.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listYv))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGMPRegionManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取区域'%1'视频信息失败").arg(regionID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGMPRegionManagerMng::getRegionList(StringMap param, ListStringMap& listRegion, ErrorInfo& e)
{
    std::string condition = param.find("condition") != param.end() ? param["condition"] : "1=1";
    std::string orderType = param.find("order") != param.end() ? param["order"] : "ASC";
    std::string orderField = param.find("sort") != param.end() ? param["sort"] : "id";
    std::string offset = param.find("offset") != param.end() ? param["offset"] : "0";
    std::string limit = param.find("limit") != param.end() ? param["limit"] : "1000";
    QString addition = QString(" ORDER BY %1 %2 LIMIT %3, %4").arg(orderField.c_str())
        .arg(orderType.c_str()).arg(offset.c_str()).arg(limit.c_str());
    QString sql = QString("SELECT * FROM mp_param_region WHERE %1").arg(condition.c_str()) + addition;
    auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
    if (dbProxy == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGMPRegionManager::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("获取数据服务代理对象失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listRegion, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        for (auto& region : listRegion)
        {
            region["appNodeName"] = ZGUtils::get(m_mapAppNode, region["appNodeID"], "");
            const auto& device = ZGUtils::get(m_mapDevice, region["deviceID"]);
            region["deviceName"] = ZGUtils::get(device, "name", "");
            region["identTypeName"] = ZGUtils::get(m_mapIdentType, region["identTypeID"], "");
        }
        return true;
    }
    catch (const Ice::Exception& ie)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
        e.errDetail = ie.what();
        ZGLOG_ERROR(e);
        return false;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGMPRegionManagerMng::getRegionAccess(std::string regionID, ListStringMap& ListAccess, ErrorInfo& e)
{
    QString sql = QString("SELECT doorDeviceID, inDeviceID, outDeviceID FROM mp_param_region_access WHERE regionID = '%1'").arg(regionID.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), ListAccess))
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGMPRegionManager::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取区域'%1'门禁信息失败").arg(regionID.c_str()).toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    try
    {
        for (auto& access: ListAccess)
        {
            const auto & doorDevice = ZGUtils::get(m_mapDevice, access["doorDeviceID"]);
            access["doorDeviceName"] = ZGUtils::get(doorDevice, "name", "");
            const auto & inDevice = ZGUtils::get(m_mapDevice, access["inDeviceID"]);
            access["inDeviceName"] = ZGUtils::get(inDevice, "name", "");
            const auto & outDevice = ZGUtils::get(m_mapDevice, access["outDeviceID"]);
            access["outDeviceName"] = ZGUtils::get(outDevice, "name", "");
        }
        return true;
    }
    catch (const std::exception& ex)
    {
        e = ZGRuntime::instance()->getErrorInfo(::ZGMPRegionManager::ZG_ERR_INTERNAL);
        e.errDetail = ex.what();
        ZGLOG_ERROR(e);
        return false;
    }
}

bool ZGMPRegionManagerMng::resetWarn(std::string regionID, ErrorInfo& e)
{
    // TODO: 复位告警
    return false;
}

void ZGMPRegionManagerMng::setupAlarm(std::string regionID)
{
    auto hikProxy = ZGProxyMng::instance()->getProxyMPVideoHIK();
    if (hikProxy == nullptr)
    {
        ZGLOG_ERROR(QStringLiteral("获取海康视频服务代理对象失败"));
        return;
    }
    try
    {
        StringList listYvID;
        QString sql = QString("SELECT a.id FROM mp_param_dataset_yv a LEFT JOIN mp_param_model_yv b ON a.dataModelID = b.id "
                              "WHERE a.regionID = '%1' AND b.isEnable = 1 AND a.defenceTypeID = 'ZG_DT_NORMAL' ORDER BY a.id")
            .arg(regionID.c_str());
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listYvID))
        {
            ZGLOG_ERROR(QStringLiteral("获取区域'%1'视频信息失败").arg(regionID.c_str()));
            return;
        }
        for (const auto& yvID: listYvID)
            ZGLOG_TRACE(yvID.c_str());
        ZG6000::ErrorInfo e;
        if (!hikProxy->setupAlarms(listYvID, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
        return;
    }
    updateAlarmState(regionID, "2");
}

void ZGMPRegionManagerMng::closeAlarm(std::string regionID)
{
    auto hikProxy = ZGProxyMng::instance()->getProxyMPVideoHIK();
    if (hikProxy == nullptr)
    {
        ZGLOG_ERROR(QStringLiteral("获取海康视频服务代理对象失败"));
        return;
    }
    try
    {
        StringList listYvID;
        QString sql = QString("SELECT a.id FROM mp_param_dataset_yv a LEFT JOIN mp_param_model_yv b ON a.dataModelID = b.id "
                              "WHERE a.regionID = '%1' AND b.isEnable = 1 AND a.defenceTypeID = 'ZG_DT_NORMAL' ORDER BY a.id")
            .arg(regionID.c_str());
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listYvID))
        {
            ZGLOG_ERROR(QStringLiteral("获取区域'%1'视频信息失败").arg(regionID.c_str()));
            return;
        }
        ZG6000::ErrorInfo e;
        if (!hikProxy->closeAlarms(listYvID, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
        return;
    }
    updateAlarmState(regionID, "1");
}

void ZGMPRegionManagerMng::onTimer()
{
    if (!ZGRuntime::instance()->isMaster())
        return;
    QString sql = QString("SELECT regionID, userID FROM mp_param_region_user");
    ZG6000::ListStringMap listRegionUser;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRegionUser))
    {
        ZGLOG_ERROR(QStringLiteral("获取区域用户失败"));
        return;
    }
    std::map<std::string, ZG6000::StringList> m_mapRegionUser;
    for (auto& regionUser: listRegionUser)
    {
        m_mapRegionUser[regionUser["regionID"]].push_back(regionUser["userID"]);
    }
    for (const auto & [regionID, regionParam]: m_mapRegionParam)
    {
        const auto& regionDeviceID = ZGUtils::get(regionParam, "deviceID", "");
        ZG6000::StringList listUserID;
        auto it = m_mapRegionUser.find(regionID);
        if (it != m_mapRegionUser.end())
            listUserID = it->second;
        updatePeopleUser(regionID, regionDeviceID, listUserID);
        calculateRegionProperties(regionDeviceID);
    }
    for (const auto& [regionID, listDeviceID]: m_mapRegionVideoDevice)
    {
        auto pair = m_mapRegionParam.find(regionID);
        if (pair == m_mapRegionParam.end())
            continue;
        const auto& regionParam = pair->second;
        const auto& regionDeviceID = ZGUtils::get(regionParam, "deviceID", "");
        ZG6000::ErrorInfo e;
        if (!listDeviceID.empty())
            updateVideoPropertiesToRegion(listDeviceID, regionDeviceID);
    }
    ++m_setupAlarmTickcount;
    if (m_setupAlarmTickcount == m_setupAlarmInterval)
    {
        m_setupAlarmTickcount = 0;
        try
        {
            for (const auto& [regionID, regionParam] : m_mapRegionParam)
            {
                const auto& isAutoDefence = ZGUtils::get(regionParam, "isAutoDefence");
                if (isAutoDefence != "1")
                    continue;
                if (m_mapAutoDefenceRegion.find(regionID) != m_mapAutoDefenceRegion.end())
                    continue;
                const auto& deviceID = ZGUtils::get(regionParam, "deviceID");
                if (deviceID.empty())
                    continue;
                std::string alarmState;
                ZG6000::ErrorInfo e;
                if (!ZGProxyCommon::getPropertyValue(deviceID, "AlarmState", alarmState, e))
                {
                    ZGLOG_ERROR(e);
                    continue;
                }
                if (alarmState == "2")
                {
                    if (m_mapAutoDefenceRegion.find(regionID) == m_mapAutoDefenceRegion.end())
                    {
                        m_mapAutoDefenceRegion.insert(regionID);
                    }
                    continue;
                }
                setupAlarm(regionID);
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
}

void ZGMPRegionManagerMng::onReceivedMessage(const QString& topic, const QString& message)
{
    if (!ZGRuntime::instance()->isMaster())
        return;
    qsizetype pos = topic.indexOf("/");
    if (pos == -1)
        return;
    const auto& deviceID = topic.mid(pos + 1).toStdString();
    const auto& json = message.toStdString();
    ZG6000::MapStringMap properties;
    std::string errMsg;
    if (!ZGJson::convertFromJson(json, properties, errMsg))
    {
        ZGLOG_ERROR(errMsg.c_str());
        return;
    }
    std::string subtypeID;
    if (!ZGProxyCommon::getDataByField("mp_param_device", deviceID, "subtypeID", subtypeID))
    {
        ZGLOG_ERROR(QStringLiteral("获取设备'%1'类型失败"));
    }
    if (subtypeID == "ZG_DS_ACCESS_CONTROL")
    {
        if (m_enableRayCount)
            processDoorDevice(deviceID, std::move(properties));
    }
    else if (subtypeID == "ZG_DS_IDENT_CARD")
        processCardIdentDevice(deviceID, std::move(properties));
    else if (subtypeID == "ZG_DS_IDENT_SMART")
        processSmartIndentDevice(deviceID, std::move(properties));
    else if (subtypeID == "ZG_DS_REGION")
        processRegionDevice(deviceID, std::move(properties));
    else
    {
        ZGLOG_ERROR(QStringLiteral("无效的设备'%1'子类型'%2'").arg(deviceID.c_str()).arg(subtypeID.c_str()));
    }
}

void ZGMPRegionManagerMng::initEvents()
{
    connect(&m_timer, &QTimer::timeout, this, &ZGMPRegionManagerMng::onTimer);
}

void ZGMPRegionManagerMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
}

bool ZGMPRegionManagerMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

bool ZGMPRegionManagerMng::initRedisClient()
{
    QList listClientType{ ZGRuntime::REDIS_RT_TOPIC, ZGRuntime::REDIS_RT_QUEUE };
    if (!ZGRuntime::instance()->initRedisClient(listClientType))
    {
        ZGLOG_ERROR("initRedisClient error.");
        return false;
    }
    m_pRedisQueue = ZGRuntime::instance()->getRedisClientRTQueue();
    if (m_pRedisQueue == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTQueue error.");
        return false;
    }
    m_pRedisTopic = ZGRuntime::instance()->getRedisClientRTTopic();
    if (m_pRedisTopic == nullptr)
    {
        ZGLOG_ERROR("getRedisClientRTTopic return null.");
        return false;
    }
    QThread::msleep(1000);
    if (!m_pRedisTopic->connected(true))
    {
        ZGLOG_ERROR("Redis RT topic is not connected.");
        return false;
    }
    connect(m_pRedisTopic, &ZGRedisClient::receivedMessage, this, &ZGMPRegionManagerMng::onReceivedMessage);
    std::set<std::string> topics;
    for (const auto& [deviceID, _]: m_mapDeviceRegion)
    {
        topics.insert("mp_param_device/" + deviceID);
    }
    for (const auto& regionAccess : m_listRegionAccess)
    {
        const auto& doorDeviceID = ZGUtils::get(regionAccess, "doorDeviceID");
        topics.insert("mp_param_device/" + doorDeviceID);
        const auto& inDeviceID = ZGUtils::get(regionAccess, "inDeviceID");
        topics.insert("mp_param_device/" + inDeviceID);
        const auto& outDeviceID = ZGUtils::get(regionAccess, "outDeviceID");
        topics.insert("mp_param_device/" + outDeviceID);
    }
    StringList listTopic;
    for (const auto & topic : topics)
    {
        ZGLOG_TRACE(topic.c_str());
        listTopic.push_back(topic);
    }
    if (!listTopic.empty())
        m_pRedisTopic->subscribe(listTopic);
    m_pRedisTopic->consume();
    return true;
}

bool ZGMPRegionManagerMng::initMqttClient()
{
    if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
    {
        ZGLOG_ERROR("initMqttClient error.");
        return false;
    }
    m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
    if (m_pMqttClient == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage return null.");
        return false;
    }
    m_pMqttClient->connectToHost();
    return true;
}

bool ZGMPRegionManagerMng::initRegionParam()
{
    QString sql = QString("SELECT * FROM mp_param_region");
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapRegionParam))
    {
        ZGLOG_ERROR(QStringLiteral("获取区域参数失败"));
        return false;
    }
    sql = QString("SELECT regionID, doorDeviceID, inDeviceID, outDeviceID FROM mp_param_region_access");
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_listRegionAccess))
    {
        ZGLOG_ERROR(QStringLiteral("获取区域访问参数失败"));
        return false;
    }
    try
    {
        for (const auto& [regionID, regionParam]: m_mapRegionParam)
        {
            const auto& deviceID = ZGUtils::get(regionParam, "deviceID");
            if (!deviceID.empty())
                m_mapDeviceRegion[deviceID] = regionID;
        }
        for (const auto& regionAccess : m_listRegionAccess)
        {
            const auto& regionID = ZGUtils::get(regionAccess, "regionID");
            const auto& doorDeviceID = ZGUtils::get(regionAccess, "doorDeviceID");
            const auto& inDeviceID = ZGUtils::get(regionAccess, "inDeviceID");
            const auto& outDeviceID = ZGUtils::get(regionAccess, "outDeviceID");
            m_mapIdentInfo[inDeviceID].push_back({ {"regionID", regionID}, {"doorDeviceID", doorDeviceID}, {"direction", "1"} });
            m_mapIdentInfo[outDeviceID].push_back({ {"regionID", regionID}, {"doorDeviceID", doorDeviceID}, {"direction", "2"} });
            m_mapDoorInfo[doorDeviceID].push_back(regionID);
            if (std::find(m_mapDoorIdent[doorDeviceID].begin(), m_mapDoorIdent[doorDeviceID].end(), inDeviceID) == m_mapDoorIdent[doorDeviceID].end())
                m_mapDoorIdent[doorDeviceID].push_back(inDeviceID);
            if (std::find(m_mapDoorIdent[doorDeviceID].begin(), m_mapDoorIdent[doorDeviceID].end(), outDeviceID) == m_mapDoorIdent[doorDeviceID].end())
                m_mapDoorIdent[doorDeviceID].push_back(outDeviceID);
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
    sql = QString("SELECT a.regionID, a.deviceID FROM mp_param_dataset_yv a LEFT JOIN mp_param_model_yv b ON a.dataModelID = b.id "
                  "WHERE a.regionID <> '' AND b.isEnable = 1 AND defenceTypeID = 'ZG_DT_NORMAL' ORDER BY a.id");
    ListStringMap listRegionDevice;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRegionDevice))
    {
        ZGLOG_ERROR(QStringLiteral("获取区域视频参数失败"));
        return false;
    }
    for (auto & regionDevice : listRegionDevice)
    {
        auto pair = m_mapRegionParam.find(regionDevice["regionID"]);
        if (pair == m_mapRegionParam.end())
            continue;
        if (regionDevice["deviceID"].empty())
            continue;
        if (std::find(m_mapRegionVideoDevice[regionDevice["regionID"]].begin(), m_mapRegionVideoDevice[regionDevice["regionID"]].end(), regionDevice["deviceID"])
            == m_mapRegionVideoDevice[regionDevice["regionID"]].end())
            m_mapRegionVideoDevice[regionDevice["regionID"]].push_back(regionDevice["deviceID"]);
    }
    ZGLOG_TRACE(QString("m_mapRegionVideoDevice size: %1").arg(m_mapRegionVideoDevice.size()));
    sql = QString("SELECT regionID, deviceID FROM mp_param_region_device");
    ListStringMap listRegionAlarmDevice;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRegionAlarmDevice))
    {
        ZGLOG_ERROR(QStringLiteral("获取区域告警设备失败"));
        return false; 
    }
    for (auto& regionAlarmDevice: listRegionAlarmDevice)
    {
        auto pair = m_mapRegionParam.find(regionAlarmDevice["regionID"]);
        if (pair == m_mapRegionParam.end())
            continue;
        if (regionAlarmDevice["deviceID"].empty())
            continue;
        m_mapRegionAlarmDevice[regionAlarmDevice["regionID"]].push_back(regionAlarmDevice["deviceID"]);
    }
    return true;
}

bool ZGMPRegionManagerMng::initTaskParam()
{
    QString sql = QString("SELECT isEnable FROM op_param_wp_system");
    StringList listEnable;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listEnable))
    {
        ZGLOG_ERROR(QStringLiteral("获取请销点系统参数失败"));
        return false;
    }
    if (listEnable.empty())
    {
        ZGLOG_ERROR(QStringLiteral("未配置请销点系统参数"));
        return false;
    }
    m_enableMP = (listEnable[0] == "1");
    sql = QString("select isEnableRay, isCheckUniqueUser FROM mp_param_system");
    ListStringMap listSystem;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listSystem))
    {
        ZGLOG_ERROR(QStringLiteral("获取监控平台系统参数失败"));
        return false;
    }
    if (listSystem.empty())
    {
        ZGLOG_ERROR(QStringLiteral("未配置监控平台系统参数"));
        return false;
    }
    m_isCheckUniqueUser = listSystem[0]["isCheckUniqueUser"] == "1";
    m_enableRayCount = listSystem[0]["isEnableRay"] == "1";
    return true;
}

bool ZGMPRegionManagerMng::initParams()
{
    std::string sql = "SELECT id, name FROM sp_param_appnode";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapAppNode))
    {
        ZGLOG_ERROR(QStringLiteral("获取应用节点信息失败"));
        return false;
    }
    sql = "SELECT id, name FROM mp_dict_ident_type";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapIdentType))
    {
        ZGLOG_ERROR(QStringLiteral("获取认证类型信息失败"));
        return false;
    }
    sql = "SELECT id, name, subtypeID FROM mp_param_device";
    if (!ZGProxyCommon::execQuerySql(sql, m_mapDevice))
    {
        ZGLOG_ERROR(QStringLiteral("获取设备信息失败"));
        return false;
    }
    sql = "SELECT isEnableAppNodeUser FROM sp_param_system";
    std::string isEnableAppNodeUser;
    if (!ZGProxyCommon::execQuerySqlField(sql, isEnableAppNodeUser))
    {
        ZGLOG_ERROR(QStringLiteral("获取系统参数失败"));
        return false;
    }
    m_enableAppNodeUser = std::atoi(isEnableAppNodeUser.c_str());
    ZGLOG_TRACE(QString("enableAppNodeUser = '%1'").arg(m_enableAppNodeUser));
    return true;
}

bool ZGMPRegionManagerMng::initRegionDefenceState()
{
    try
    {
        for (const auto& [regionID, regionParam]: m_mapRegionParam)
        {
            const auto& deviceID = ZGUtils::get(regionParam, "deviceID");
            if (deviceID.empty())
                continue;
            bool exist;
            ZG6000::ErrorInfo e;
            if (!ZGProxyCommon::isPropertyExists(deviceID, "AlarmState", exist, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            if (!exist)
                continue;
            if (!ZGProxyCommon::updatePropertyValue(deviceID, "AlarmState", "1", e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
        }
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

/**
 * @brief   处理智能识别设备识别信息
 *
 * @param   deviceID    设备ID
 * @param   properties  变化属性
 */
void ZGMPRegionManagerMng::processSmartIndentDevice(const std::string& deviceID, MapStringMap properties)
{
    ZGLOG_TRACE(QString("processSmartIndentDevice, identDevice: '%1'").arg(deviceID.c_str()));
    auto pairProp = properties.find("WorkNumber");
    if (pairProp == properties.end())
        return;
    const auto& param = pairProp->second;
    auto pairValue = param.find("rtNewValue");
    if (pairValue == param.end())
        return;
    const auto& userID = pairValue->second;
    if (userID.empty())
        return;
    processUserIdentity(deviceID, userID);
}

/**
 * @brief   处理刷卡器识别信息
 *         
 *
 * @param   deviceID    刷卡器设备
 * @param   properties  变化属性
 */
void ZGMPRegionManagerMng::processCardIdentDevice(const std::string& deviceID, MapStringMap properties)
{
    auto pairProp = properties.find("CardNumber");
    if (pairProp == properties.end())
        return;
    const auto& param = pairProp->second;
    auto pairValue = param.find("rtNewValue");
    if (pairValue == param.end())
        return;
    const auto& cardNumber = pairValue->second;
    if (cardNumber.empty())
        return;
    std::string userID;
    if (!getUserIDFromCardNumber(cardNumber, userID))
        return;
    processUserIdentity(deviceID, userID);
}

void ZGMPRegionManagerMng::processDoorDevice(const std::string& deviceID, MapStringMap properties)
{
    auto pairProp = properties.find("RayCount");
    if (pairProp == properties.end())
        return;
    const auto& param = pairProp->second;
    auto pairValue = param.find("rtNewValue");
    if (pairValue == param.end())
        return;
    // 红外计数归0
    if (std::atoi(pairValue->second.c_str()) == 0)
        return;
    auto pairDoor = m_mapDoorIdent.find(deviceID);
    if (pairDoor == m_mapDoorIdent.end())
    {
        ZGLOG_ERROR(QStringLiteral("找不到门禁'%1'关联的识别设备").arg(deviceID.c_str()));
        return;
    }
    ZGLOG_TRACE(QString("processDoorDevice, doorDevice: '%1'").arg(deviceID.c_str()));
    std::string propertyValue;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(deviceID, "Pos", propertyValue, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    ZGLOG_TRACE(QString("Door enable state: '%1'").arg(propertyValue.c_str()));
    // 当前门禁已禁用，不处理
    if (propertyValue == "1")
    {
        ZGLOG_ERROR(QStringLiteral("设备'%1'门禁已禁用").arg(deviceID.c_str()));
        return;
    }
    const auto & listIdentDeviceID = pairDoor->second;
    for (const auto& identDeviceID : listIdentDeviceID)
    {
        // 在m_mapDevice表中找不到设备信息，不处理
        auto pairDevice = m_mapDevice.find(identDeviceID);
        if (pairDevice == m_mapDevice.end())
        {
            ZGLOG_ERROR(QStringLiteral("找不到识别设备'%1'").arg(identDeviceID.c_str()));
            continue;
        }
        const auto& device = pairDevice->second;
        const auto& subtypeID = ZGUtils::get(device, "subtypeID");
        std::string propertyName;
        if (subtypeID == "ZG_DS_IDENT_CARD")
            propertyName = "CardNumber";
        else if (subtypeID == "ZG_DS_IDENT_SMART")
            propertyName = "WorkNumber";
        else
        {
            ZGLOG_ERROR(QStringLiteral("无效的识别设备'%1'子类型'%2'").arg(identDeviceID.c_str()).arg(subtypeID.c_str()));
            continue;
        }
        // 获取设备的属性
        std::string identValue;
        if (!ZGProxyCommon::getPropertyValue(identDeviceID, propertyName, identValue, e))
        {
            ZGLOG_ERROR(e);
            continue;
        }
        ZGLOG_TRACE(QString("Ident device: '%1', identValue: '%2'").arg(identDeviceID.c_str()).arg(identValue.c_str()));
        // 如果属性值为空，不处理
        if (identValue.empty())
            continue;
        std::string userID;
        // 如果获取的是卡号，则通过卡号找到用户ID
        if (subtypeID == "ZG_DS_IDENT_CARD")
        {
            if (!getUserIDFromCardNumber(identValue, userID))
            {
                ZGLOG_ERROR(QStringLiteral("根据卡号获取用户ID失败"));
                continue;
            }
        }
        else
        {
            if (!getUserIDFromUserID(identValue, userID))
            {
                ZGLOG_ERROR(QStringLiteral("获取用户失败"));
                continue;
            }
        }
        auto pairIdent = m_mapIdentInfo.find(identDeviceID);
        if (pairIdent == m_mapIdentInfo.end())
        {
            ZGLOG_ERROR(QStringLiteral("找不到识别设备'%1'信息").arg(identDeviceID.c_str()));
            continue;
        }
        const auto& identDeviceInfo = pairIdent->second;
        ZGLOG_TRACE(QString("identDeviceInfo size: %1").arg(identDeviceInfo.size()));
        // 针对识别设备关联的区域信息列表，增加或删除人员
        for (const auto& identInfo : identDeviceInfo)
        {
            const auto& regionID = ZGUtils::get(identInfo, "regionID");
            const auto& direction = ZGUtils::get(identInfo, "direction");
            ZGLOG_TRACE(QString("regionID: '%1', direction: '%2'").arg(regionID.c_str()).arg(direction.c_str()));
            if (direction == "1")
                addRegionPeople(regionID, deviceID, userID);
            else
                removeRegionPeople(regionID, deviceID, userID);
        }
        // 清除该识别设备的识别信息
        if (!ZGProxyCommon::updatePropertyValue(identDeviceID, propertyName, "", e))
            ZGLOG_ERROR(e);
    }
}

void ZGMPRegionManagerMng::processRegionDevice(const std::string &deviceID, MapStringMap properties)
{
    auto pairRule = properties.find("AlarmRuleIntrustion");
    auto pairMove = properties.find("AlarmMotionDetection");
    if (pairRule == properties.end() && pairMove == properties.end())
        return;
    std::string ruleValue, moveValue;
    if (pairRule != properties.end())
    {
        auto pairRuleValue = pairRule->second.find("rtNewValue");
        if (pairRuleValue != pairRule->second.end())
            ruleValue = pairRuleValue->second;
    }
    if (pairMove != properties.end())
    {
        auto pairMoveValue = pairMove->second.find("rtNewValue");
        if (pairMoveValue != pairMove->second.end())
            moveValue = pairMoveValue->second;
    }
    std::string newAlarmState;
    if (ruleValue == "2" || moveValue == "2")
        newAlarmState = "2";
    else
        newAlarmState = "1";
    std::string oldAlarmState;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(deviceID, "AlarmState", oldAlarmState, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    if (newAlarmState == oldAlarmState)
        return;
    try
    {
        const auto& regionID = ZGUtils::get(m_mapDeviceRegion, deviceID);
        if (newAlarmState == "2")
        {
            sendAlarmCtrl(regionID, "CMD_AlarmOn", "2");
            const auto& regionParam = ZGUtils::get(m_mapRegionParam, regionID);
            const auto& regionName = ZGUtils::get(regionParam, "name");
            const auto& groupID = ZGUtils::get(regionParam, "bcGroupID");
            broadcast(groupID, QStringLiteral("'%1'有人员闯入").arg(regionName.c_str()).toStdString());
        }
        else
            sendAlarmCtrl(regionID, "CMD_AlarmOff", "2");
    }
    catch (const std::exception& ex)
    {
        ZGLOG_ERROR(ex.what());
    }
}

void ZGMPRegionManagerMng::processUserIdentity(const std::string& deviceID, const std::string& userID)
{
    auto pairIdent = m_mapIdentInfo.find(deviceID);
    if (pairIdent == m_mapIdentInfo.end())
    {
        ZGLOG_ERROR(QStringLiteral("找不到识别设备'%1'关联的区域参数").arg(deviceID.c_str()));
        return;
    }
    const auto& listRegionInfo = pairIdent->second;
    const auto& doorDeviceID = ZGUtils::get(listRegionInfo[0], "doorDeviceID");
    const auto& identDirection = ZGUtils::get(listRegionInfo[0], "direction");
    bool pass;
    if (!isUserCanPass(userID, doorDeviceID, identDirection, pass))
        return;
    ZGLOG_INFO(QStringLiteral("用户'%1'于设备'%2'认证，允许通过=%3").arg(userID.c_str())
        .arg(deviceID.c_str()).arg(pass));
    if (!pass)
        return;
    openTheDoor(doorDeviceID);
    for (const auto & regionInfo : listRegionInfo)
    {
        const auto& regionID = ZGUtils::get(regionInfo, "regionID");
        const auto& doorID = ZGUtils::get(regionInfo, "doorDeviceID");
        const auto& direction = ZGUtils::get(regionInfo, "direction");
        auto pairRegion = m_mapRegionParam.find(regionID);
        if (pairRegion == m_mapRegionParam.end())
        {
            ZGLOG_ERROR(QStringLiteral("找不到区域ID'%1'关联的区域"));
            return;
        }
        if (!m_enableRayCount)
        {
            if (direction == "1")
                addRegionPeople(regionID, doorDeviceID, userID);
            else
                removeRegionPeople(regionID, doorDeviceID, userID);
        }
    }
}

void ZGMPRegionManagerMng::sendUserEvent(const std::string& userName, const std::string& appNodeID, const std::string& regionName, const std::string& deviceName, const std::string& direction)
{
    auto eventProxy = ZGProxyMng::instance()->getProxySPEventProcess();
    if (eventProxy == nullptr)
    {
        ZGLOG_ERROR(QStringLiteral("获取事件处理服务代理对象失败"));
        return;
    }
    auto oneWayProxy = eventProxy->ice_oneway();
    std::string directDesc = direction == "1" ? u8"进入" : u8"离开";
    StringMap event
    {
        {"eventTypeID", "ZG_ET_PEOPLE"}, {"alarmLevelID", "ZG_AL_LEVEL0"}, {"appNodeID", appNodeID}, {"dataCategoryID", ""},
        {"eventTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString()},
        {"eventInfo", QStringLiteral("【%1】从【%2】%3【%4】").arg(userName.c_str()).arg(deviceName.c_str()).arg(directDesc.c_str()).arg(regionName.c_str()).toStdString()}
    };
    try
    {
        oneWayProxy->processEvent(event);
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGMPRegionManagerMng::updateVideoPropertiesToRegion(const StringList& listVideoDeviceID, const std::string& regionDeviceID)
{
    ZG6000::ErrorInfo e;
    MapStringMap yxPropertyValues;
    ZG6000::StringList listRealYxProperty;
    for (const auto& yxProperty: m_listVideoYxProperty)
    {
        bool exist;
        if (!ZGProxyCommon::isPropertyExists(regionDeviceID, yxProperty, exist, e))
        {
            ZGLOG_ERROR(e);
            continue;
        }
        if (!exist)
            continue;
        listRealYxProperty.push_back(yxProperty);
    }
    if (!ZGProxyCommon::mgetPropertyValues(listVideoDeviceID, listRealYxProperty, yxPropertyValues, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    StringMap yxValues;
    for (const auto& yxProperty : listRealYxProperty)
    {
        yxValues[yxProperty] = "1";
    }
    for (const auto& [deviceID, values] : yxPropertyValues)
    {
        for (const auto& [key, value] : values)
        {
            if (value != "2")
                continue;
            yxValues[key] = value;
        }
    }
    bool exist;
    if (!ZGProxyCommon::isPropertyExists(regionDeviceID, "AlarmPeopleExist", exist, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    if (exist)
    {
        MapStringMap ycPropertyValues;
        if (!ZGProxyCommon::mgetPropertyValues(listVideoDeviceID, m_listVideoYcProperty, ycPropertyValues, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
        std::string peopleExist = "1";
        try
        {
            for (auto& [deviceID, values] : ycPropertyValues)
            {
                for (const auto& ycProperty : m_listVideoYcProperty)
                {
                    if (std::atoi(values[ycProperty].c_str()) > 0)
                    {
                        peopleExist = "2";
                        break;
                    }
                }
                if (peopleExist == "2")
                    break;
            }
        }
        catch (const std::exception& ex)
        {
            ZGLOG_ERROR(ex.what());
        }
        yxValues["AlarmPeopleExist"] = peopleExist;
        listRealYxProperty.push_back("AlarmPeopleExist");
        ZG6000::StringMap oldYxValues, changeYxValues;
        if (!ZGProxyCommon::getPropertyValues(regionDeviceID, listRealYxProperty, oldYxValues, e))
        {
            ZGLOG_ERROR(e);
            return;
        }
        for (auto it = yxValues.begin(); it != yxValues.end(); ++it)
        {
            if (it->second != oldYxValues[it->first])
                changeYxValues[it->first] = it->second;
        }
        if (!ZGProxyCommon::updatePropertyValues(regionDeviceID, changeYxValues, e))
            ZGLOG_ERROR(e);
    }
}

void ZGMPRegionManagerMng::calculateRegionProperties(const std::string& regionDeviceID)
{
    ZG6000::StringMap alarmValues;
    ZG6000::ErrorInfo e;
    ZG6000::StringList listOriginProperty{ "AlarmMotionDetection", "AccessPeopleExist", "AlarmPeopleExist" };
    ZG6000::StringList listProperty;
    for (const auto & originProperty : listOriginProperty)
    {
        bool exists;
        if (!ZGProxyCommon::isPropertyExists(regionDeviceID, originProperty, exists, e))
        {
            ZGLOG_ERROR(e);
            continue;
        }
        if (exists)
            listProperty.push_back(originProperty);
    }
    if (!ZGProxyCommon::getPropertyValues(regionDeviceID, listProperty, alarmValues, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    std::string peopleExist = "1";
    for (const auto & [_, value] : alarmValues)
    {
        if (value == "2")
        {
            peopleExist = "2";
            break;
        }
    }
    std::string oldPeopleExist;
    if (!ZGProxyCommon::getPropertyValue(regionDeviceID, "PeopleExist", oldPeopleExist, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    if (oldPeopleExist == peopleExist)
        return;
    if (!ZGProxyCommon::updatePropertyValue(regionDeviceID, "PeopleExist", peopleExist, e))
        ZGLOG_ERROR(e);
}

void ZGMPRegionManagerMng::broadcast(const std::string& groupID, const std::string& text)
{
    auto broadcastProxy = ZGProxyMng::instance()->getProxyMPBroadcastServer();
    if (broadcastProxy == nullptr)
    {
        ZGLOG_ERROR(QStringLiteral("获取广播服务代理对象失败"));
        return;
    }
    try
    {
        ErrorInfo e;
        if (!broadcastProxy->playGroupTTS(groupID, text, e))
            ZGLOG_ERROR(e);
    }
    catch (const Ice::Exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGMPRegionManagerMng::sendAlarmCtrl(const std::string &regionID, const std::string& name, const std::string &value)
{
    auto pair = m_mapRegionAlarmDevice.find(regionID);
    if (pair == m_mapRegionAlarmDevice.end())
        return;
    const auto& listDeviceID = pair->second;
    for (const auto& deviceID: listDeviceID)
    {
        sendCtrlCommand(deviceID, name, value);
    }
}

bool ZGMPRegionManagerMng::updateAlarmState(const std::string& regionID, const std::string& alarmState)
{
    const auto& pair = m_mapRegionParam.find(regionID);
    if (pair == m_mapRegionParam.end())
    {
        ZGLOG_ERROR(QStringLiteral("找不到区域'%1'").arg(regionID.c_str()));
        return false;
    }
    try
    {
        const auto& regionParam = pair->second;
        const auto& deviceID = ZGUtils::get(regionParam, "deviceID");
        if (deviceID.empty())
        {
            ZGLOG_ERROR(QStringLiteral("区域'%1'无关联设备").arg(regionID.c_str()));
            return false;
        }
        ZG6000::ErrorInfo e;
        if (!ZGProxyCommon::updatePropertyValue(deviceID, "AlarmState", alarmState, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

void ZGMPRegionManagerMng::updateYvAlarmState(const std::string& regionID, const std::string& alarmState)
{
    ListStringMap listYv;
    QString sql = QString("SELECT a.* FROM mp_param_dataset_yv a LEFT JOIN mp_param_model_yv b ON a.dataModelID = b.id "
                          "WHERE a.regionID = '%1' AND b.isEnable = 1 AND a.defenceTypeID = 'ZG_DT_NORMAL' ORDER BY a.id")
                        .arg(regionID.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listYv))
    {
        ZGLOG_ERROR(QStringLiteral("获取区域'%1'视频信息失败").arg(regionID.c_str()));
        return;
    }
    auto hikProxy = ZGProxyMng::instance()->getProxyMPVideoHIK();
    if (hikProxy == nullptr)
    {
        ZGLOG_ERROR(QStringLiteral("获取海康视频服务代理对象失败"));
        return;
    }
    try
    {
        ZG6000::ErrorInfo e;
        for (auto& yv : listYv)
        {
            if (alarmState == "2")
            {
                if (!hikProxy->setupAlarm(yv["id"], e))
                    ZGLOG_ERROR(e);
            }
            if (alarmState == "1")
            {
                ZGLOG_TRACE(QString("closeAlarm, yv = '%1'").arg(yv["id"].c_str()));
                if (!hikProxy->closeAlarm(yv["id"], e))
                    ZGLOG_ERROR(e);
            }
        }
    }
    catch (const Ice::Exception& ie)
    {
        ZGLOG_ERROR(ie.what());
    }
}

bool ZGMPRegionManagerMng::addRegionPeople(const std::string& regionID, const std::string& deviceID, const std::string& userID)
{
    ZGLOG_INFO(QStringLiteral("addRegionPeople, regionID: '%1', userID: '%2'").arg(regionID.c_str()).arg(userID.c_str()));
    ZG6000::StringList listSql;
    QString sql = QString("DELETE FROM mp_param_region_user WHERE userID = '%1';").arg(userID.c_str());
    listSql.push_back(sql.toStdString());
    std::string uuid;
    if (!ZGProxyCommon::createUUID(uuid))
    {
        ZGLOG_ERROR(QStringLiteral("创建记录ID失败"));
        return false;
    }
    StringMap peopleUser{{"id", uuid}, {"regionID", regionID}, {"deviceID", deviceID}, {"userID", userID},
        {"rtUpdateTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime(), true).toStdString()} };
    listSql.push_back(ZGUtils::generateInsertSql("mp_param_region_user", peopleUser));
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        ZGLOG_ERROR(QStringLiteral("区域'%1'添加人员'%2'失败"));
        return false;
    }
    QDateTime dt = QDateTime::currentDateTime();
    QString hisTable = QString("mp_his_region_user_%1").arg(dt.date().year());
    std::string regionName, appNodeID;
    auto pair = m_mapRegionParam.find(regionID);
    if (pair != m_mapRegionParam.end())
    {
        regionName = ZGUtils::get(pair->second, "name", "");
        appNodeID = ZGUtils::get(pair->second, "appNodeID", "");
    }
    peopleUser["regionName"] = regionName;
    std::string deviceName;
    ZGProxyCommon::getDataByField("mp_param_device", deviceID, "name", deviceName);
    peopleUser["deviceName"] = deviceName;
    std::string userName;
    ZGProxyCommon::getDataByField("sp_param_hrm_user", userID, "name", userName);
    peopleUser["userName"] = userName;
    MapStringMap mapPeopleUser{ {"user", peopleUser} };
    const auto& json = ZGJson::convertToJson(mapPeopleUser);
    m_pMqttClient->sendPublish(QString("mp_param_region/%1/insert").arg(regionID.c_str()), json.c_str());
    peopleUser["direction"] = QStringLiteral("进入").toStdString();
    if (!ZGProxyCommon::execSql(ZGUtils::generateInsertSql(hisTable.toStdString(), peopleUser), true))
    {
        ZGLOG_ERROR(QStringLiteral("创建人员'%1'历史进出记录失败").arg(userID.c_str()));
        return false;
    }
    sendUserEvent(userName, appNodeID, regionName, deviceName, "1");
    return true;
}

bool ZGMPRegionManagerMng::removeRegionPeople(const std::string& regionID, const std::string& deviceID, const std::string& userID)
{
    ZGLOG_INFO(QStringLiteral("removeRegionPeople, regionID: '%1', userID: '%2'").arg(regionID.c_str()).arg(userID.c_str()));
    QString sql = QString("DELETE FROM mp_param_region_user WHERE regionID = '%1' AND userID = '%2'").arg(regionID.c_str()).arg(userID.c_str());
    if (!ZGProxyCommon::execSql(sql.toStdString()))
    {
        ZGLOG_ERROR(QStringLiteral("清除区域'%1'人员'%2'失败").arg(regionID.c_str()).arg(userID.c_str()));
        return false;
    }
    StringMap value{ {"userID", userID} };
    MapStringMap mapPeopleUser{ {"user", value} };
    const auto& json = ZGJson::convertToJson(mapPeopleUser);
    m_pMqttClient->sendPublish(QString("mp_param_region/%1/delete").arg(regionID.c_str()), json.c_str());
    std::string uuid;
    if (!ZGProxyCommon::createUUID(uuid))
    {
        ZGLOG_ERROR(QStringLiteral("创建记录ID失败"));
        return false;
    }
    StringMap peopleUser{ {"id", uuid}, {"regionID", regionID}, {"userID", userID}, {"deviceID", deviceID} };
    QDateTime dt = QDateTime::currentDateTime();
    QString hisTable = QString("mp_his_region_user_%1").arg(dt.date().year());
    std::string regionName, appNodeID;
    auto pair = m_mapRegionParam.find(regionID);
    if (pair != m_mapRegionParam.end())
    {
        regionName = ZGUtils::get(pair->second, "name", "");
        appNodeID = ZGUtils::get(pair->second, "appNodeID", "");
    }
    peopleUser["regionName"] = regionName;
    std::string deviceName;
    ZGProxyCommon::getDataByField("mp_param_device", deviceID, "name", deviceName);
    peopleUser["deviceName"] = deviceName;
    std::string userName;
    ZGProxyCommon::getDataByField("sp_param_hrm_user", userID, "name", userName);
    peopleUser["userName"] = userName;
    peopleUser["direction"] = QStringLiteral("离开").toStdString();
    peopleUser["rtUpdateTime"] = ZGUtils::DateTimeToString(dt, true).toStdString();
    if (!ZGProxyCommon::execSql(ZGUtils::generateInsertSql(hisTable.toStdString(), peopleUser), true))
    {
        ZGLOG_ERROR(QStringLiteral("创建人员'%1'历史进出记录失败").arg(userID.c_str()));
        return false;
    }
    sendUserEvent(userName, appNodeID, regionName, deviceName, "2");
    return true;
}

void ZGMPRegionManagerMng::cacheIdentUser(const std::string& regionID, const std::string& doorID, const std::string& userID, const std::string& direction)
{
    m_mapPassUser[{regionID, doorID}] = { userID, direction, ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString() };
}

bool ZGMPRegionManagerMng::isUserCanPass(const std::string& userID, const std::string& deviceID, const std::string& direction, bool& pass)
{
    QString sql = QString("SELECT DISTINCT a.powerID FROM sp_param_hrm_role_power a "
                          "LEFT JOIN sp_param_hrm_user_role b ON a.roleID = b.roleID "
                          "WHERE b.userID = '%1' ORDER BY a.id").arg(userID.c_str());
    StringList listPowerID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listPowerID))
    {
        ZGLOG_ERROR(QStringLiteral("获取用户'%1'权限失败").arg(userID.c_str()));
        return false;
    }
    if (std::find(listPowerID.begin(), listPowerID.end(), "ZG_HP_REGION_WORK") == listPowerID.end())
    {
        ZGLOG_ERROR(QString("用户'%1'没有区域作业权限").arg(userID.c_str()));
        sendCtrlCommand(deviceID, "CMD_AccessPlay", std::to_string(dcvInvalidCard));
        pass = false;
        return true;
    }
    std::string appNodeID;
    if (!ZGProxyCommon::getDataByField("mp_param_device", deviceID, "appNodeID", appNodeID))
    {
        ZGLOG_ERROR(QStringLiteral("获取设备'%1'应用节点失败").arg(deviceID.c_str()));
        return false;
    }
    sql = QString("SELECT id FROM sp_param_hrm_user_appnode WHERE userID = '%1' "
                  "AND appnodeID = '%2'").arg(userID.c_str()).arg(appNodeID.c_str());
    StringList listUserAppNodeID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserAppNodeID))
    {
        ZGLOG_ERROR(QStringLiteral("获取用户'%1'应用节点'%2'失败").arg(userID.c_str()).arg(appNodeID.c_str()));
        return false;
    }
    if (listUserAppNodeID.empty())
    {
        ZGLOG_ERROR(QString("用户'%1'未管理应用节点'%2'").arg(userID.c_str()).arg(appNodeID.c_str()));
        sendCtrlCommand(deviceID, "CMD_AccessPlay", std::to_string(dcvInvalidCard));
        pass = false;
        return true;
    }
    std::string posValue;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(deviceID, "Pos", posValue, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    if (posValue == "1")
    {
        sendCtrlCommand(deviceID, "CMD_AccessPlay", std::to_string(dcvDisable));
        pass = false;
        return true;
    }
    if (m_isCheckUniqueUser && direction == "1")
    {
        sql = QString("SELECT id FROM mp_param_region_user WHERE userID = '%1'").arg(userID.c_str());
        ZG6000::StringList listRegionUserID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listRegionUserID))
        {
            ZGLOG_ERROR(QStringLiteral("获取区域用户'%1'失败").arg(userID.c_str()));
            return false;
        }
        if (!listRegionUserID.empty())
        {
            sendCtrlCommand(deviceID, "CMD_AccessPlay", std::to_string(dcvRepeatCard));
            pass = false;
            return true;
        }
    }
    if (m_enableAppNodeUser)
    {
        if (!ZGProxyCommon::getDataByField("mp_param_device", deviceID, "appNodeID", appNodeID))
        {
            ZGLOG_ERROR(QStringLiteral("获取门禁设备'%1'所属应用节点失败").arg(deviceID.c_str()));
            return false;
        }
        sql = QString("SELECT userID FROM sp_param_appnode_user WHERE appNodeID = '%1'").arg(appNodeID.c_str());
        StringList listUserID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
        {
            ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'人员失败").arg(appNodeID.c_str()));
            return false;
        }
        if (std::find(listUserID.begin(), listUserID.end(), userID) == listUserID.end())
        {
            ZGLOG_ERROR(QStringLiteral("应用节点'%1'未包含用户'%2'").arg(appNodeID.c_str()).arg(userID.c_str()));
            sendCtrlCommand(deviceID, "CMD_AccessPlay", std::to_string(dcvInvalidCard));
            pass = false;
            return true;
        }
    }
    if (m_enableMP)
    {
        if (!ZGProxyCommon::getDataByField("mp_param_device", deviceID, "appNodeID", appNodeID))
        {
            ZGLOG_ERROR(QStringLiteral("获取门禁设备'%1'所属应用节点失败").arg(deviceID.c_str()));
            return false;
        }
        sql = QString("SELECT a.userID FROM op_param_wp_user a LEFT JOIN op_param_wp b ON a.workPointID = b.id "
            "WHERE b.appNodeID = '%1' AND b.stageID = 'ZG_WS_EXECUTE' ORDER BY a.id").arg(appNodeID.c_str());
        StringList listUserID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
        {
            ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'请销点人员失败").arg(appNodeID.c_str()));
            return false;
        }
        sql = QString("SELECT workLeaderID FROM op_param_wp WHERE appNodeID = '%1' AND stageID = 'ZG_WS_EXECUTE'")
            .arg(appNodeID.c_str());
        StringList listUserID2;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID2))
        {
            ZGLOG_ERROR(QStringLiteral("获取应用节点'%1'请销点负责人失败").arg(appNodeID.c_str()));
            return false;
        }
        std::move(listUserID2.begin(), listUserID2.end(), std::back_inserter(listUserID));
        if (std::find(listUserID.begin(), listUserID.end(), userID) == listUserID.end())
        {
            ZGLOG_ERROR(QStringLiteral("请销点作业未包含用户'%1'").arg(userID.c_str()));
            sendCtrlCommand(deviceID, "CMD_AccessPlay", std::to_string(dcvInvalidCard));
            pass = false;
            return true;
        }
    }
    pass = true;
    return true;
}

void ZGMPRegionManagerMng::openTheDoor(const std::string& deviceID)
{
    sendCtrlCommand(deviceID, "CMD_PosDoor", "2");
    sendCtrlCommand(deviceID, "CMD_AccessPlay", std::to_string(dcvPass));
}

bool ZGMPRegionManagerMng::sendCtrlCommand(const std::string& deviceID, const std::string& propertyName, const std::string& value)
{
    std::string tableName, dataID;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    QJsonObject command;
    command["id"] = dataID.c_str();
    std::string modelTable = tableName;
    ZGUtils::replaceString(modelTable, "dataset", "model");
    QString sql = QString("SELECT a.isSelectCtrl FROM %1 a LEFT JOIN %2 b ON b.dataModelID = a.id WHERE b.id = '%3' ORDER BY a.id")
                      .arg(modelTable.c_str()).arg(tableName.c_str()).arg(dataID.c_str());
    std::string result;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), result))
    {
        ZGLOG_ERROR("execQuerySqlField error.");
        return false;
    }
    QString commandID;
    if (tableName == "mp_param_dataset_yk")
        commandID = "ZG_DC_YK_EXEC";
    if (tableName == "mp_param_dataset_ys")
        commandID = "ZG_DC_YS_EXEC";
    if (result == "1")
    {
        if (tableName == "mp_param_dataset_yk")
            commandID = "ZG_DC_YK_SELECT";
        if (tableName == "mp_param_dataset_ys")
            commandID = "ZG_DC_YS_SELECT";
    }
    command["commandID"] = commandID;
    command["isReturnValue"] = "0";
    command["srcType"] = "auto";
    command["srcID"] = "-1";
    command["rtCode"] = QString::number(ZGUtils::genNumber(0, 10000));
    command["rtValue"] = value.c_str();
    command["rtCommandTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
    QJsonArray array;
    array.append(command);
    QJsonDocument doc(array);
    long long size;
    QString errMsg;
    std::string topicName;
    if (tableName == "mp_param_dataset_yk")
        topicName = "ZG_Q_SYSTEM_YK";
    if (tableName == "mp_param_dataset_ys")
        topicName = "ZG_Q_SYSTEM_YS";
    ZGLOG_DEBUG(doc.toJson());
    if (!m_pRedisQueue->rpush(topicName.c_str(), doc.toJson(), size, errMsg))
    {
        ZGLOG_ERROR("Send command to ys queue error.");
    }
    return true;
}

bool ZGMPRegionManagerMng::updatePeopleUser(const std::string& regionID, const std::string& regionDeviceID, const ZG6000::StringList& listUserID)
{
    try
    {
        ZG6000::ErrorInfo e;
        std::string peopleExist = listUserID.size() > 0 ? "2" : "1";
        std::string peopleNum = std::to_string(listUserID.size());
        ZG6000::StringMap mapValues;
        if (!ZGProxyCommon::getPropertyValues(regionDeviceID, {"PeopleNum", "AccessPeopleExist"}, mapValues, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        if (mapValues["PeopleNum"] != peopleNum)
        {
            if (!ZGProxyCommon::updatePropertyValue(regionDeviceID, "PeopleNum", peopleNum, e))
                ZGLOG_ERROR(e);
        }
        if (mapValues["AccessPeopleExist"] != peopleExist)
        {
            if (!ZGProxyCommon::updatePropertyValue(regionDeviceID, "AccessPeopleExist", peopleExist, e))
                ZGLOG_ERROR(e);
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
    return true;
}

bool ZGMPRegionManagerMng::getUserIDFromCardNumber(const std::string& cardNumber, std::string& userID)
{
    QString sql = QString("SELECT userID FROM sp_param_hrm_user_card WHERE cardID = '%1'").arg(cardNumber.c_str());
    ZG6000::StringList listUserID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
    {
        ZGLOG_ERROR(QStringLiteral("获取卡'%1'关联人员失败").arg(cardNumber.c_str()));
        return false;
    }
    if (listUserID.empty())
    {
        ZGLOG_ERROR(QStringLiteral("找不到卡'%1'关联的人员").arg(cardNumber.c_str()));
        return false;
    }
    if (listUserID.size() > 1)
    {
        ZGLOG_ERROR(QStringLiteral("卡'%1'关联的人员不唯一").arg(cardNumber.c_str()));
        return false;
    }
    userID = listUserID[0];
    return true;
}

bool ZGMPRegionManagerMng::getUserIDFromUserID(const std::string& workNumber, std::string& userID)
{
    QString sql = QString("SELECT id FROM sp_param_hrm_user WHERE id = '%1'").arg(workNumber.c_str());
    ZG6000::StringList listID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listID))
    {
        ZGLOG_ERROR(QStringLiteral("获取人员'%1'ID失败").arg(workNumber.c_str()));
        return false;
    }
    if (listID.empty())
    {
        ZGLOG_ERROR(QStringLiteral("找不到用户'%1'").arg(workNumber.c_str()));
        return false;
    }
    userID = listID[0];
    return false;
}
} // namespace ZG6000

#ifndef ZG6000_ZGMPREGIONMANAGERMNG_H
#define ZG6000_ZGMPREGIONMANAGERMNG_H

#include <QObject>
#include <QTimer>
#include <set>
#include <tuple>
#include "ZGServerCommon.h"

class ZGRedisClient;
class ZGMqttClient;
namespace ZG6000 {

class ZGMPRegionManagerMng : public QObject
{
    Q_OBJECT

public:
    static ZGMPRegionManagerMng* instance();
    void init();
    bool checkState();
    /**
     * @brief 清空区域人员
     * @param regionID 区域ID
     * @param e 执行失败时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    bool clearRegionPeople(std::string regionID, ErrorInfo& e);

    /**
     * @brief 获取平台人员列表
     * @param regionID 区域ID
     * @param listPeople 平台人员列表
     * @param e 执行失败时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    bool getRegionPeople(std::string regionID, ListStringMap& listPeople, ErrorInfo& e);

    /**
     * @brief 获取区域视频列表
     * @param regionID 区域ID
     * @param listYv 视频列表ID
     * @param e 执行失败时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    bool getRegionYv(std::string regionID, ListStringMap& listYv, ErrorInfo& e);

    /**
     * @brief   获取区域列表
     *
     * @param           param       查询参数
     * @param [in,out]  listRegion  区域列表
     * @param [in,out]  e           执行失败时的错误描述
     *
     * @return  执行成功返回true，失败返回false
     */
    bool getRegionList(StringMap param, ListStringMap& listRegion, ErrorInfo& e);

    /**
     * @brief   获取区域门禁参数
     *
     * @param           regionID    区域ID
     * @param [in,out]  ListAccess  门禁列表
     * @param [in,out]  e           执行失败时的错误描述
     *
     * @return  执行成功返回true，失败返回false
     */
    bool getRegionAccess(std::string regionID, ListStringMap& ListAccess, ErrorInfo& e);

    bool resetWarn(std::string regionID, ErrorInfo& e);

    void setupAlarm(std::string regionID);

    void closeAlarm(std::string regionID);


private slots:
    void onTimer();
    void onReceivedMessage(const QString& topic, const QString& message);

private:
    explicit ZGMPRegionManagerMng(QObject* parent = nullptr);
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initRedisClient();
    bool initMqttClient();
    bool initRegionParam();
    bool initTaskParam();
    bool initParams();
    bool initRegionDefenceState();
    void processSmartIndentDevice(const std::string& deviceID, MapStringMap properties);
    void processCardIdentDevice(const std::string& deviceID, MapStringMap properties);
    void processDoorDevice(const std::string& deviceID, MapStringMap properties);
    void processRegionDevice(const std::string& deviceID, MapStringMap properties);
    void processUserIdentity(const std::string& deviceID, const std::string& userID);
    bool addRegionPeople(const std::string& regionID, const std::string& deviceID, const std::string& userID);
    bool removeRegionPeople(const std::string& regionID, const std::string& deviceID, const std::string& userID);
    bool getUserIDFromCardNumber(const std::string& cardNumber, std::string& userID);
    bool getUserIDFromUserID(const std::string& workNumber, std::string& userID);
    void cacheIdentUser(const std::string& regionID, const std::string& doorID, const std::string& userID, const std::string& direction);
    bool isUserCanPass(const std::string& userID, const std::string& deviceID, const std::string& direction, bool& pass);
    void openTheDoor(const std::string& deviceID);
    bool sendCtrlCommand(const std::string& deviceID, const std::string& propertyName, const std::string& value);
    bool updatePeopleUser(const std::string& regionID, const std::string& regionDeviceID, const ZG6000::StringList& listUserID);
    void sendUserEvent(const std::string& userName, const std::string& appNodeID, const std::string& regionName, const std::string& deviceName, const std::string& direction);
    void updateVideoPropertiesToRegion(const StringList& listVideoDeviceID, const std::string& regionDeviceID);
    void calculateRegionProperties(const std::string& regionDeviceID);
    void broadcast(const std::string& groupID, const std::string& text);
    void sendAlarmCtrl(const std::string& regionID, const std::string &name, const std::string& value);
    bool updateAlarmState(const std::string& regionID, const std::string& alarmState);
    void updateYvAlarmState(const std::string& regionID, const std::string& alarmState);

private:
    enum DoorCtrlVoice
    {
        dcvPass = 1, dcvInvalidCard, dcvRepeatCard, dcvToured, dcvOperTour, dcvMonTour, dcvErrLine, dcvVoilate, dcvDisable
    };
    bool m_initialized{ false };
    int m_initInterval{ 10 };
    QString m_serverName{ "" };
    QString m_instName{ "" };
    QTimer m_timer;
    int m_setupAlarmInterval{ 10 };
    int m_setupAlarmTickcount{ 0 };
    ZGRedisClient* m_pRedisTopic{ nullptr };
    ZGRedisClient* m_pRedisQueue{ nullptr };
    ZGMqttClient* m_pMqttClient{ nullptr };
    MapStringMap m_mapRegionParam;
    ListStringMap m_listRegionAccess;
    std::map<std::string, StringList> m_mapRegionVideoDevice;
    std::map<std::string, StringList> m_mapRegionAlarmDevice;
    std::map<std::string, ListStringMap> m_mapIdentInfo;
    std::map<std::string, StringList> m_mapDoorInfo;
    std::map<std::string, StringList> m_mapDoorIdent;
    std::set<std::string> m_mapAutoDefenceRegion;
    StringMap m_mapDeviceRegion;
    std::map <std::pair<std::string, std::string>, std::tuple<std::string, std::string, std::string>> m_mapPassUser;
    StringList m_listVideoYxProperty{"AlarmMotionDetection", "AlarmRuleTraversePlane", "AlarmRuleEnterArea",
                                    "AlarmRuleExitArea", "AlarmRuleIntrustion", "AlarmRuleLoiter", "AlarmRuleLeftTake",
                                        "AlarmRuleParking", "AlarmRuleRun", "AlarmRuleHighDensity", "AlarmRuleViolentMotion", "AlarmRuleReachHight",
                                    "AlarmRuleGetup", "AlarmRuleLeft", "AlarmRuleTake", "AlarmRuleLeavePosition", "AlarmRuleTrail"};
    StringList m_listVideoYcProperty{ "VCAAlarmPeopleNum1", "VCAAlarmPeopleNum2", "VCAAlarmPeopleNum3", "VCAAlarmPeopleNum4" };
    bool m_enableMP{ false };
    bool m_enableRayCount{ false };
    bool m_isCheckUniqueUser{ false };
    bool m_enableAppNodeUser{ false };
    StringMap m_mapAppNode;
    MapStringMap m_mapDevice;
    StringMap m_mapIdentType;
};

inline static ZGMPRegionManagerMng* g_pInstance = nullptr;

} // namespace ZG6000

#endif // ZG6000_ZGMPREGIONMANAGERMNG_H

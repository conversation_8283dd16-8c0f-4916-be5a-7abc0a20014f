#include "ZGMPHisStatisticProcessMng.h"
#include "ZGStatistic.h"
#include "ZGHeartMng.h"
#include "ZGRuntime.h"
#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"
#include "ZGMPHistStatistic.h"
#include "ZGUtils.h"

static ZGMPHisStatisticProcessMng* g_pInstance = nullptr;

ZGMPHisStatisticProcessMng* ZGMPHisStatisticProcessMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGMPHisStatisticProcessMng;
    return g_pInstance;
}

void ZGMPHisStatisticProcessMng::init()
{
    initServerInstConfig();
    initEvents();
    start();
    ZGLOG_INFO("ZGMPHisStatisticProcess init start...");
}

bool ZGMPHisStatisticProcessMng::checkState()
{
    return m_initialized;
}

void ZGMPHisStatisticProcessMng::statistic(const std::string& id)
{
    m_pStatistic->statistic(id);
}

void ZGMPHisStatisticProcessMng::statisticBatch(const ZG6000::StringList& listID)
{
    for (const auto& id : listID)
    {
        m_pStatistic->statistic(id);
    }
}

void ZGMPHisStatisticProcessMng::statisticStart(const std::string& id)
{
    m_pStatistic->statistic(id, true);
}

void ZGMPHisStatisticProcessMng::statisticStartBatch(const ZG6000::StringList& listID)
{
    for (const auto& id : listID)
    {
        m_pStatistic->statistic(id, true);
    }
}

void ZGMPHisStatisticProcessMng::run()
{
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        msleep(m_initInterval * 1000);
    }
    while (!m_pStatistic->initialize())
    {
        ZGLOG_ERROR("statistic initialize error.");
        msleep(m_initInterval * 1000);
    }
    m_initialized = true;
    emit initFinished();
    ZGLOG_INFO("ZGMPHisStatisticProcess init finished.");
}

void ZGMPHisStatisticProcessMng::initServerInstConfig()
{
    const auto & serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

void ZGMPHisStatisticProcessMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGMPHisStatisticProcessMng::onCheckStatus);
    connect(this, &ZGMPHisStatisticProcessMng::initFinished, this, &ZGMPHisStatisticProcessMng::onInitFinished);
}

bool ZGMPHisStatisticProcessMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

void ZGMPHisStatisticProcessMng::onInitFinished()
{
    m_checkTimer.start(m_checkInterval * 1000);
}

void ZGMPHisStatisticProcessMng::onCheckStatus()
{
    m_masterInst = ZGRuntime::instance()->isMaster();
}

ZGMPHisStatisticProcessMng::ZGMPHisStatisticProcessMng(QObject* parent) : QThread(parent)
{
    m_pStatistic = new ZGMPHistStatistic;
}

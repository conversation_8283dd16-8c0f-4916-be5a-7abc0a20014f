#ifndef ZGMPHISTSTATISTIC_H
#define ZGMPHISTSTATISTIC_H

#include <unordered_set>
#include "ZGStatistic.h"

class ZGMPHistStatistic : public ZGStatistic
{
    Q_OBJECT
public:
    explicit ZGMPHistStatistic(QObject *parent = nullptr);
    bool initialize() override;

protected:
    void calculateMax(const StatisticParam& statisticParam) override;
    void calculateMin(const StatisticParam& statisticParam) override;
    void calculateAvg(const StatisticParam& statisticParam) override;
    void calculateCount(const StatisticParam& statisticParam) override;
    void calculateSum(const StatisticParam& statisticParam) override;

private:
    using Calculate = std::function<void(const ZG6000::ListStringMap&, ZG6000::StringMap&)>;
    bool initStoreParam();
    bool initChangeParam();
    bool initTableStoreParam(const std::string& paramTable, std::unordered_set<std::string>& changeSet);
    bool initTableChangeParam(const std::string& paramTable, const std::string& modelTable, std::unordered_set<std::string>& changeSet);
    void calculateByStore(const StatisticParam& statisticParam, const Calculate& calculate);
    void calculateByChange(const StatisticParam& statisticParam, const Calculate& calculate);
    inline bool isDataInStore(const std::string& tableName, const std::string& dataID);
    inline bool isDataInChange(const std::string& tableName, const std::string& dataID);
    bool getStoreTableName(const std::string& realTableName, const std::string& updateTime, std::string& storeTableName);
    bool getChangeTableName(const std::string& realTableName, const std::string& updateTime, std::string& storeTableName);
    std::string generateQueryCondition(const StatisticParam& statisticParam);
    bool getDatasetFromStore(const StatisticParam& statisticParam, ZG6000::ListStringMap& listResult);
    bool getDatasetFromChange(const StatisticParam& statisticParam, ZG6000::ListStringMap& listResult);
    bool filterDataset(ZG6000::ListStringMap& srcListResult, ZG6000::ListStringMap& destListResult, const std::string& sampleRatio,
        const std::string& sampleRatioType);

private:
    std::unordered_set<std::string> m_setStoreYc;
    std::unordered_set<std::string> m_setChangeYc;
    std::unordered_set<std::string> m_setChangeYx;
    std::unordered_set<std::string> m_setChangeText;
};

#endif // ZGMPHISTSTATISTIC_H

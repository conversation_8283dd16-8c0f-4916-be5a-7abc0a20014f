#ifndef __ZGMPHisStatisticProcessI_h__
#define __ZGMPHisStatisticProcessI_h__

#include <ZGMPHisStatisticProcess.h>

namespace ZG6000
{
    class ZGMPHisStatisticProcessI : public virtual ZGMPHisStatisticProcess
    {
    public:
        ZGMPHisStatisticProcessI();

        bool checkState(const Ice::Current&) override;

        void statistic(::std::string, const Ice::Current&) override;

        void statisticBatch(StringList, const Ice::Current&) override;

        void statisticStart(std::string id, const Ice::Current& current) override;

        void statisticStartBatch(StringList listID, const Ice::Current& current) override;

        void statisticStartAndCalc(std::string id, const Ice::Current& current) override;
    };
}

#endif

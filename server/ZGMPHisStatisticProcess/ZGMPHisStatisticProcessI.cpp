#include <ZGMPHisStatisticProcessI.h>
#include "ZGMPHisStatisticProcessMng.h"

ZG6000::ZGMPHisStatisticProcessI::ZGMPHisStatisticProcessI()
{
    ZGMPHisStatisticProcessMng::instance()->init();
}

bool ZG6000::ZGMPHisStatisticProcessI::checkState(const Ice::Current& current)
{
    return ZGMPHisStatisticProcessMng::instance()->checkState();
}

void ZG6000::ZGMPHisStatisticProcessI::statistic(::std::string id,
                                                 const Ice::Current&)
{
    ZGMPHisStatisticProcessMng::instance()->statistic(id);
}

void ZG6000::ZGMPHisStatisticProcessI::statisticBatch(StringList listID,
                                                      const Ice::Current&)
{
    ZGMPHisStatisticProcessMng::instance()->statisticBatch(listID);
}

void ZG6000::ZGMPHisStatisticProcessI::statisticStart(std::string id, const Ice::Current&)
{
    ZGMPHisStatisticProcessMng::instance()->statisticStart(id);
}

void ZG6000::ZGMPHisStatisticProcessI::statisticStartBatch(StringList listID, const Ice::Current&)
{
    ZGMPHisStatisticProcessMng::instance()->statisticStartBatch(listID);
}

void ZG6000::ZGMPHisStatisticProcessI::statisticStartAndCalc(std::string id, const Ice::Current& current)
{
}
